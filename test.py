import unittest
import os
import json
from collections import OrderedDict

#from package import api_falke as api
from package import api_testodbc as api

from package.shared import odbc
from package.shared import utils

# https://docs.python.org/3/library/unittest.html

# Individual tests can be run like this:
# python test.py Tests.testGetProducts

packageDir = os.path.join(os.path.realpath(
    os.path.join(os.getcwd(), os.path.dirname(__file__))), "package")

debug = True


def debugPrint(*args):
    # When writing the tests we might want to print extra info
    if debug:
        print(*args)


class Tests(unittest.TestCase):
    def setUp(self):
        self.instance = api.Api()
        self.instance._indent = 2

    def testGetOrders(self):
        params = {
            "source_customer_code": "COD CM",
            "sync_token": "2017-05-05"
        }
        def getParams():
            return params

        self.instance._getParams = getParams

        response = self.instance.getOrders()
        debugPrint(response)

    def testRunCustom(self):
        params = {
            "fileName": "custom.sql"
        }
        def getParams():
            return params

        self.instance._getParams = getParams

        response = self.instance.runCustom()
        debugPrint(response)

    # SQL Server etc
    def testGetProductsFromChinook(self):
        params = {
            "test": "true"
        }
        def getParams():
            return params
        self.instance._getParams = getParams

        self.instance._setConfig()

        with odbc.openConn(self.instance._config["dsn"]) as cursor:
            sql = "select top 10 TrackId from Track"
            cursor.execute(sql)
            rows = utils.getRowsODBC(cursor, 10)
            debugPrint(json.dumps(rows, indent=self.instance._indent))

    # SQL Server etc
    def testGetProductsFromEvo(self):
        params = {
            "test": "true"
        }
        def getParams():
            return params
        self.instance._getParams = getParams

        self.instance._setConfig()

        with odbc.openConn(self.instance._config["dsn"]) as cursor:
            sql = "select * from StkItem"
            cursor.execute(sql)
            rows = utils.getRowsODBC(cursor, 10)
            debugPrint(json.dumps(rows, indent=self.instance._indent, default=self.instance._json_dump_default))

    # Firebird (employee sample database)
    def testGetProductsFromFirebird(self):
        params = {
            "test": "true"
        }
        def getParams():
            return params
        self.instance._getParams = getParams

        self.instance._setConfig()

        with odbc.openConn(self.instance._config["dsn"]) as cursor:
            sql = "select * from country"
            cursor.execute(sql)
            rows = utils.getRowsODBC(cursor, 10)
            debugPrint(json.dumps(rows, indent=self.instance._indent))

    def testCountProducts(self):
        params = {
            "test": "true",
        }
        def getParams():
            return params
        self.instance._getParams = getParams

        self.instance._setConfig()

        debugPrint(self.instance.countProducts())


    def testGetImages(self):
        params = {
            "test": "true",
        }

        def getParams():
            return params

        self.instance._getParams = getParams

        response = self.instance.getImages()
        debugPrint(response)

    def testAuditProducts(self):
        params = {
            "test": "true",
        }

        def getParams():
            return params

        self.instance._getParams = getParams

        response = self.instance.auditProducts()
        debugPrint(response)

    def testGetOrder(self):
        params = {
            "test": "true",
            "source_order_code": '1',
            "source_id": "123"
        }

        def getParams():
            return params

        self.instance._getParams = getParams

        response = self.instance.getOrder()
        # respJson = json.loads(response)
        # debugPrint(len(respJson["data"]["source_products"]))
        debugPrint(response)


    def testGetProductBySKU(self):
        params = {
            "test": "true",
            "sku": "6",
            "Xwarehouse_code": "JHB",
            "Xsource_id": "123"
        }

        def getParams():
            return params

        self.instance._getParams = getParams

        response = self.instance.getProductBySKU()
        debugPrint(response)


    def testGetProduct(self):
        params = {
            "test": "true",
            "source_variant_code": "6",
            "source_id": "123"
        }

        def getParams():
            return params

        self.instance._getParams = getParams

        response = self.instance.getProduct()
        debugPrint(response)


    def testGetProducts(self):
        params = {
            "test": "true",
            "source_id": "123",
            "limit": "20",
            "sync_token": "0",
            "current_iteration": "2",
        }

        def getParams():
            return params

        self.instance._getParams = getParams

        response = self.instance.getProducts()
        # respJson = json.loads(response)
        # debugPrint(len(respJson["data"]["source_products"]))
        debugPrint(response)

    def testCreateOrder(self):
        path = os.path.join(packageDir, self.instance._configDir, "test", "addOrder.json")
        f = open(path, "r")
        payloadData = bytes(f.read(), "utf-8")
        f.close()

        def payload():
            return payloadData

        self.instance._payload = payload

        params = {
            "test": "true",
        }

        def getParams():
            return params

        self.instance._getParams = getParams

        response = self.instance.createOrder()
        debugPrint(response)

    def testGetHostname(self):
        params = {
            "test": "true",
        }
        def getParams():
            return params
        self.instance._getParams = getParams
        response = self.instance.getHostname()
        debugPrint(response)

    def testIpConfig(self):
        params = {
            "test": "true",
        }

        def getParams():
            return params

        self.instance._getParams = getParams
        response = self.instance.getIpConfig()
        debugPrint(response)

    def testAuditProductsBatch(self):
        params = {
            "test": "true",
            "audit_lower_limit": "0",
            "audit_upper_limit": "100",
        }

        def getParams():
            return params

        self.instance._getParams = getParams

        response = self.instance.auditProductsBatch()
        debugPrint(response)


    def testAuditCustomersBatch(self):
        params = {
            "test": "true",
            "audit_lower_limit": "0",
            "audit_upper_limit": "100",
        }

        def getParams():
            return params

        self.instance._getParams = getParams

        response = self.instance.auditCustomersBatch()
        debugPrint(response)


    def testGetProductsBatch(self):
        params = {
            "test": "true",
            "source_id": "123",
            "limit": "1",
            "sync_token": "0",
            "current_iteration": "1",
            "audit_lower_limit": "0",
            "audit_upper_limit": "100",
        }

        def getParams():
            return params

        self.instance._getParams = getParams

        response = self.instance.getProductsBatch()
        # respJson = json.loads(response)
        # debugPrint(len(respJson["data"]["source_products"]))
        debugPrint(response)


    def testGetCustomersBatch(self):
        params = {
            "test": "true",
            "source_id": "123",
            "limit": "30",
            "sync_token": "0",
            "current_iteration": "1",
            "audit_lower_limit": "0",
            "audit_upper_limit": "100",
        }

        def getParams():
            return params

        self.instance._getParams = getParams

        response = self.instance.getCustomersBatch()
        # respJson = json.loads(response)
        # debugPrint(len(respJson["data"]["source_products"]))
        debugPrint(response)


    def testAuditResetCustomers(self):
        params = {
            "test": "true",
        }

        def getParams():
            return params
        self.instance._getParams = getParams
        response = self.instance.auditResetCustomers()
        debugPrint(response)


    def testGetAuditProducts(self):
        params = {
            "test": "true",
            "sync_token": "0",
            "limit": "30"
        }

        def getParams():
            return params

        self.instance._getParams = getParams
        response = self.instance._getAuditProducts()
        debugPrint(response)


    def testTransformProduct(self):
        params = {
            "test": "true",
            "sync_token": "0",
            "audit_lower_limit": "0",
            "audit_upper_limit": "1000",
            "limit": "1",
            "current_iteration": "1",
            "source_id": "9999"
        }

        def getParams():
            return params

        input_row = OrderedDict([('n', 1), ('source_product_code', '3314'), ('source_variant_code', '3314-0200-12.-10'),
                     ('title', 'MNS. PURE COTTON ANKLET'), ('collection', ''), ('product_type', ''),
                     ('vendor', 'Falke - Open'), ('variants.option1_name', 'Size'), ('variants.option1_value', '8-12'),
                     ('variants.option2_name', 'Colour'), ('variants.option2_value', 'WHITE'), ('variants.option3', ''),
                     ('variants.sku', '3314-0200-12.-10'), ('body_html', ''), ('variants.weight', '0.000550'),
                     ('variants.barcode', ' '), ('variants.qty', 0), ('variants.retail_price', 0.0),
                     ('variants.inventory_management', 'true'), ('product_active', 'true'), ('tags', ''),
                     ('meta_department', 'Fashion - Mens'), ('meta_range', 'Seasonal'),
                     ('meta_activity', 'Smart Casual')])

        self.instance._getParams = getParams
        response = self.instance._transformProduct(params, input_row, params["sync_token"])

        expected_output = OrderedDict([('source', OrderedDict(
            [('source_id', '9999'), ('product_active', 'true'), ('source_product_code', '3314'), ('sync_token', '0')])),
                     ('product', OrderedDict([('options', [
                         OrderedDict([('name', 'Size'), ('position', '1'), ('value', 'undefined')]),
                         OrderedDict([('name', 'Colour'), ('position', '2'), ('value', 'undefined')])]),
                                              ('body_html', ''), ('collection', ''), ('product_type', ''), ('tags', ''),
                                              ('title', 'MNS. PURE COTTON ANKLET'), ('vendor', 'Falke - Open'), (
                                              'variants', OrderedDict([('source_variant_code', '3314-0200-12.-10'),
                                                                       ('sku', '3314-0200-12.-10'), ('barcode', ' '),
                                                                       ('price', 0.0), ('qty', 0), ('price_tiers', [
                                                      OrderedDict([('tier', 'retail'), ('price', 0.0)])]),
                                                                       ('inventory_management', 'true'),
                                                                       ('option1', '8-12'), ('option2', 'WHITE')])), (
                                              'meta',
                                              [OrderedDict([('key', 'department'), ('value', 'Fashion - Mens')]),
                                               OrderedDict([('key', 'range'), ('value', 'Seasonal')]),
                                               OrderedDict([('key', 'activity'), ('value', 'Smart Casual')])])]))])

        self.assertEqual(response, expected_output)


    def testParseOptions(self):
        params = {
            "test": "true"
        }

        def getParams():
            return params

        self.instance._getParams = getParams

        # Test old version
        queryResult = OrderedDict(
            [('n', 1), ('source_product_code', '3314'), ('source_variant_code', '3314-0200-12.-10'),
             ('title', 'MNS. PURE COTTON ANKLET'), ('collection', ''), ('product_type', ''),
             ('vendor', 'Falke - Open'),
             ('variants.option1', 'foo'),
             ('variants.option2', 'bar'),
             ('variants.option3', 'baz'),
             ('variants.sku', '3314-0200-12.-10'), ('body_html', ''),
             ('variants.weight', '0.000550'),
             ('variants.barcode', ' '), ('variants.qty', 0), ('variants.retail_price', 0.0),
             ('variants.inventory_management', 'true'), ('product_active', 'true'), ('tags', ''),
             ('meta_department', 'Fashion - Mens'), ('meta_range', 'Seasonal'),
             ('meta_activity', 'Smart Casual')])

        source_product = OrderedDict()
        source_product['product'] = OrderedDict()
        source_product['product']['variants'] = OrderedDict()

        response = self.instance._parseOptions(queryResult, source_product)

        expected_output = OrderedDict([('product', OrderedDict(
            [('variants', OrderedDict([('option1', 'foo'), ('option2', 'bar'), ('option3', 'baz')])),
             ('options', [])]))])

        self.assertEqual(response, expected_output)

        # Test new version
        queryResult = OrderedDict([('n', 1), ('source_product_code', '3314'), ('source_variant_code', '3314-0200-12.-10'),
                                 ('title', 'MNS. PURE COTTON ANKLET'), ('collection', ''), ('product_type', ''),
                                 ('vendor', 'Falke - Open'), ('variants.option1_name', 'Size'),
                                 ('variants.option1_value', '8-12'),
                                 ('variants.option2_name', 'Colour'), ('variants.option2_value', 'WHITE'),
                                 ('variants.option3', ''),
                                 ('variants.sku', '3314-0200-12.-10'), ('body_html', ''),
                                 ('variants.weight', '0.000550'),
                                 ('variants.barcode', ' '), ('variants.qty', 0), ('variants.retail_price', 0.0),
                                 ('variants.inventory_management', 'true'), ('product_active', 'true'), ('tags', ''),
                                 ('meta_department', 'Fashion - Mens'), ('meta_range', 'Seasonal'),
                                 ('meta_activity', 'Smart Casual')])

        source_product = OrderedDict()
        source_product['product'] = OrderedDict()
        source_product['product']['variants'] = OrderedDict()

        response = self.instance._parseOptions(queryResult, source_product)

        expected_output = OrderedDict([('product', OrderedDict([('variants', OrderedDict([('option1', '8-12'), ('option2', 'WHITE')])), (
        'options', [OrderedDict([('name', 'Size'), ('position', '1'), ('value', 'undefined')]),
                    OrderedDict([('name', 'Colour'), ('position', '2'), ('value', 'undefined')])])]))])

        self.assertEqual(response, expected_output)


    # TODO: Move this to something that tests utils(?)
    def testWriteToLog(self):
        params = {
            "test": "true"
        }

        def getParams():
            return params

        self.instance._getParams = getParams

        lines = [
            "This is the first line",
            "This is the seccond line"
        ]

        response = utils.writeToLog(lines)
        debugPrint(response)

    def testUtilsBindParams(self):
        params = {
            "test": "true"
        }

        def getParams():
            return params

        self.instance._getParams = getParams
        txt = "'%(source_variant_code)n'"

        try:
            result = utils.bindSqlParams(txt, {
                "source_variant_code": "123",
            })
        except Exception:
                result = utils.bindSqlParams(txt, {
                    "source_variant_code": int("123"),
                })

        debugPrint(result)

    def testParseSegments(self):
        row = {
            "segment|source|products|meta_rep_id|equal": "REP001",
            "segment|source|products|meta_pricelist|equal": "A",
            "source_customer_code": "CUST1"
        }

        params = {
            "test": "true",
        }

        def getParams():
            return params

        self.instance._getParams = getParams

        response = self.instance._parseSegments(row)
        # debugPrint(response)

        self.assertEqual(len(response), 2) # Number of segments
        self.assertEqual(len(response[0]), 5) # Number of segment properties
        self.assertEqual(response[0]["key"], "meta_rep_id")
        self.assertEqual(response[0]["value"], "REP001")
        self.assertEqual(response[1]["key"], "meta_pricelist")
        self.assertEqual(response[1]["value"], "A")


    def testParseMetaFields(self):
        row = {

            "csv_contract_group_discounts": "order_0|entity_customer|key_item_group_code|value_123|type_discount~10,order_0|entity_product|key_item_group_code|value_456|type_discount~20",
             "meta_maximum_qty": 0,
             "meta_minimum_qty": 5,
             "csv_test_meta": "A|100,B|101.1,C|1853,D|235"

        }
        response = self.instance._parseMetaFields(row)

        debugPrint(response)

        # csv_meta_ assertions
        assert response[0]['key'] == "test_meta_A"
        assert response[0]['value'] == "100"
        assert response[1]['key'] == "test_meta_B"
        assert response[1]['value'] == "101.1"
        assert response[2]['key'] == "test_meta_C"
        assert response[2]['value'] == "1853"
        assert response[3]['key'] == "test_meta_D"
        assert response[3]['value'] == "235"

        # Contract pricing assertions
        assert response[4]['key'] == "order_0|entity_customer|key_item_group_code|value_123|type_discount"
        assert response[4]['value'] == "10"
        assert response[5]['key'] == "order_0|entity_product|key_item_group_code|value_456|type_discount"
        assert response[5]['value'] == "20"

        # meta_ assertions
        assert response[6]['key'] == "maximum_qty"
        assert response[6]['value'] == "0"
        assert response[7]['key'] == "minimum_qty"
        assert response[7]['value'] == "5"



if __name__ == "__main__":
    unittest.main()
