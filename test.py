import unittest
import os
import json
# from ctypes import *
from collections import OrderedDict

from package import api_testiqretail as api

from package.shared import odbc
from package.shared import iqretail
from package.shared import utils

# https://docs.python.org/3/library/unittest.html

# Individual tests can be run like this:
# python test.py Tests.testGetProducts

packageDir = os.path.join(os.path.realpath(
    os.path.join(os.getcwd(), os.path.dirname(__file__))), "package")

debug = True


def debugPrint(*args):
    # When writing the tests we might want to print extra info
    if debug:
        print(*args)


class Tests(unittest.TestCase):
    def setUp(self):
        self.instance = api.Api()
        self.instance._indent = 2

    def testDLLExport(self):
        import ctypes
        import xml.etree.ElementTree as ET
        params = {
            "test": "true",
            "dll_path": "C:\\IQRetail\\IQEnterprise\\IQEntAPI.dll"
        }

        def getParams():
            return params

        self.instance._getParams = getParams
        iqdll=ctypes.WinDLL(params["dll_path"])

        # export methods
        test_export = iqdll.IQ_API_Query_Exports
        outResults = ctypes.c_char_p()
        OutResultsLen = ctypes.c_int()
        test_export(ctypes.byref(outResults), ctypes.byref(OutResultsLen))
        # print("results")
        # print(outResults.value)

        # parse xml
        root = ET.fromstring(outResults.value)
        for child in root:
            print(child.tag, child.text)

        # memory cleanup
        free_mem = iqdll.IQ_API_Free_PChar
        free_mem(outResults)
        print("results after memory clean")
        print(outResults.value)

    def testDLL(self):
        import ctypes
        params = {
            "test": "true",
            "dll_path": "C:\\IQRetail\\IQEnterprise\\IQEntAPI.dll"
        }

        def getParams():
            return params

        self.instance._getParams = getParams
        # iqdll=windll.LoadLibrary(params["dll_path"])
        iqdll=ctypes.WinDLL(params["dll_path"])
        test_pchar = iqdll.IQ_API_Test_PChar
        # test_pchar.argtypes = [ctypes.c_char_p, ctypes.c_int, ctypes.c_char_p, ctypes.c_int]
        # test_pchar.argtypes = [ctypes.c_char_p, ctypes.c_int]
        # test_pchar.restype = ctypes.c_char_p
        # phrase = b"Hi"
        # mystr = "Hello, world!"
        # phrase = ctypes.c_char_p(mystr.encode('utf-8'))
        result = ctypes.c_char_p()
        a=ctypes.c_int()
        test_pchar(b"Hello, world!", len("Hello, world!"), ctypes.byref(result), ctypes.byref(a))
        # test_pchar(b"Hello, world!", len("Hello, world!"))
        print(result.value)
        # print(result.value)

    def testCreateOrder(self):
        path = os.path.join(packageDir, self.instance._configDir, "test", "addOrder.json")
        f = open(path, "r")
        payloadData = bytes(f.read(), "utf-8")
        f.close()

        def payload():
            return payloadData

        self.instance._payload = payload

        params = {
            "test": "true",
            "dll_path": "C:\\IQRetail\\IQEnterprise\\IQEntAPI.dll",
            "iq_terminal_number": "1",
            "iq_company_number": "001",
            "iq_user_number": "1",
            "iq_user_password": "011C945F30CE2CBAFC452F39840F025693339C42",
            "sales_representative_number": "3",
            "default_customer_code": "CASH001",
            "external_order_no": "abc"
        }

        def getParams():
            return params

        self.instance._getParams = getParams

        response = self.instance.createOrder()
        debugPrint(response)

if __name__ == "__main__":
    unittest.main()
