'use strict';

angular.module('myApp').controller('GraphOrderBestCustomersCtrl', ['$scope', 'stock2shop', 'utils', 'elasticsearch', '$q',
    function($scope, stock2shop, utils, elasticsearch, $q) {
        $scope.graph = {
            has_results: true,
            rendered: false,
            selected_channel_id : 0,
            selected_status : "",
            channels: {},
            label_text: "",
            statuses: {
                "": "All Statuses",
                "ordered": "Raised in ERP",
                "processing": "Processing",
                "back_order": "Back Order",
                "cancelled": "Cancelled",
                "completed": "Completed"
            },
            data: []
        };

        $q.all([
            stock2shop.channels.getChannels()
        ]).then(function(data) {
            $scope.graph.channels = utils.createMap(data[0].system_channels, "id");
            $scope.graph.channels[0] = {
              "description": "All Sales Channels"
            };
            $scope.renderGraph();
        });

        $scope.renderGraph = function() {
            $scope.graph.has_results = false;
            $scope.graph.rendered = false;
            $scope.graph.label_text = "Best Customers";
            var query = {
                "best_customers": {
                    "terms" : {
                        "field" : "customer.channel_customer_code.keyword",
                        "order" : { "sum_sales" : "desc" }
                    },
                    "aggs": {
                        "sum_sales": {
                            "sum": {
                                "field": "total"
                            }
                        }
                    }
                }
            };
            query = elasticsearch.setAggs({}, query);
            if($scope.graph.selected_channel_id > 0) {
                query = elasticsearch.setTerm(query, "channel_id", $scope.graph.selected_channel_id);
            }
            if($scope.graph.selected_status !== "") {
                query = elasticsearch.setTerm(query, "status", $scope.graph.selected_status);
            }
            query = elasticsearch.setSize(query, 0);
            $scope.graph.has_results = false;
            stock2shop.orders.elasticSearchOrders({body: query}).then(function(data) {
                $scope.graph.rendered = true;
                if(data.hits.total > 0) {
                    $scope.graph.has_results = true;
                }
                $scope.graph.label_text += " (" + data.hits.total + " Orders)";
                $scope.graph.data = data.aggregations.best_customers.buckets;
                angular.forEach(data.aggregations.best_customers.buckets, function(bucket) {
                    bucket.sum_sales.currency_value = utils.currency(bucket.sum_sales.value, ",", 0);
                });

            });
        };

    }
]);