'use strict';

angular.module('myApp').controller(
    'ProductImagesCtrl',
    [
        '$scope',
        '$q',
        '$routeParams',
        '$rootScope',
        'growl',
        'stock2shop',
        'utils',
        'validation',
        '$route',
        '$modal',
        function ($scope, $q, $routeParams, $rootScope, growl, stock2shop, utils, validation, $route, $modal) {

            $scope.data = {
                variant_id: 0,
                selected_image: false
            };
            $scope.pageReady = false;
            stock2shop.products.getProduct(
                {product_id: $routeParams.product_id},
                function (result) {
                    $scope.product = result.data.system_product;
                    if($scope.product.images.length>0) {
                        $scope.data.selected_image = $scope.product.images[0];
                    }
                    $scope.pageReady = true;
                }
            );

            $scope.setFile = function (element) {
                $scope.uploadedFile = element.files[0];
            };

            $scope.uploadFile = function () {
                if (!$scope.uploadedFile) {
                    return;
                }
                var fd = new FormData();
                fd.append("file", $scope.uploadedFile);
                var params = {
                    product_id: $scope.product.id,
                    body: fd,
                    _headers: {
                        'Content-Type': undefined
                    },
                    _transformRequest: angular.identity
                };
                if ($scope.data.variant_id !== 0) {
                    params.variant_id = $scope.data.variant_id;
                }
                stock2shop.images.addImage(params).then(function () {
                    $route.reload();
                });
            };

            $scope.deleteImage = function (image) {
                if (window.confirm("Are you sure you want to delete this image, this will be removed from all channels!")) {
                    stock2shop.images.deleteImage({image_id: image.id})
                        .then(function () {
                                growl.addSuccessMessage("Image Deleted");
                                $route.reload();
                            }
                        );
                }
            };

            $scope.selectImage = function(image) {
                $scope.data.selected_image = image;
            };

        }
    ]
);