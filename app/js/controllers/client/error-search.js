'use strict';

angular.module('myApp').controller('ErrorSearchCtrl', ['$scope', '$routeParams', '$q', '$filter', 'utils', 'config', 'stock2shop', 'growl', 'elasticsearch',
    function($scope, $routeParams, $q, $filter, utils, config, stock2shop, growl, elasticsearch) {
        $scope.showErrors = false;
        $scope.pagination = config.pagination;
        $scope.pagination.page = $routeParams.page || 1;
        $scope.pagination.limit = $routeParams.limit ||  50;
        $scope.search_string = '';

        // list of aggregations for drop downs
        $scope.aggs = {};

        // list of meta values for menu
        $scope.meta_values = {};

        // Search filters
        // -----------------------------------------------
        $scope.hasFilter = function() {
            return ($scope.filters.sources ||
            $scope.filters.channels ||
            $scope.filters.fulfillment_services ||
            $scope.filters.connector_id ||
            $scope.filters.connector_kind ||
            $scope.filters.direction ||
            $scope.filters.entity);
        };
        $scope.filters = {
            "sources": getFilteredConnectorId('source', $routeParams.connector_id),
            "channels": getFilteredConnectorId('channel', $routeParams.connector_id),
            "fulfillment_services": getFilteredConnectorId('fulfillmentservice', $routeParams.connector_id),
            "limit": $scope.pagination.limit,
            "q": $routeParams.q || undefined,
            "connector_id": $routeParams.connector_id || undefined,
            "connector_kind": $routeParams.connector_kind || undefined,
            "entity_id": $routeParams.entity_id || undefined,
            "entity": $routeParams.entity || undefined,
            "direction": $routeParams.direction || undefined
        };
        $scope.labels = {
            "connector_id": $routeParams.connector_id || "Connector",
            "connector_kind": $routeParams.connector_kind || "Connector Type",
            "sources": $routeParams.sources || "Sources",
            "channels": $routeParams.channels || "Channels",
            "fulfillment_services": $routeParams.fulfillment_services || "Fulfillment Services",
            "direction": $routeParams.direction || "Direction",
            "entity": $routeParams.entity || "Entity",
        };

        $scope.filtersVisible = $scope.hasFilter() || ($routeParams.show === 'Y');

        // Load Initial Data & Search
        // -----------------------------------------------
        $q.all([
            stock2shop.sources.getSources(),
            stock2shop.channels.getChannels(),
            stock2shop.fulfillment_services.getFulfillmentServices()
        ])
        .then(function(data) {

            // Setup Sources
            var sources = [];
            angular.forEach(data[0].system_sources, function(value) {
                // Ignore linked sources
                if (!value.source_id) {
                    sources.push(value);
                }
            });
            $scope.system_sources = sources;
            $scope.source_map = utils.createMap(sources, 'id');
            if($scope.filters.sources) {
                $scope.labels.sources = $scope.source_map[$scope.filters.sources].description;
            }

            // Setup Channels
            $scope.system_channels = data[1].system_channels;
            $scope.channel_map = utils.createMap($scope.system_channels, 'id');
            if($scope.filters.channels) {
                $scope.labels.channels = $scope.channel_map[$scope.filters.channels].description;
            }

            // Setup Fulfillment Services
            $scope.system_fulfillment_services = data[2].system_fulfillment_services;
            $scope.fulfillment_service_map = utils.createMap($scope.system_fulfillment_services, 'id');
            if($scope.filters.fulfillment_services) {
                $scope.labels.fulfillment_services =
                    $scope.fulfillment_service_map[$scope.filters.fulfillment_services].description;
            }

            // go
            search();
        });

        /**
         * Toggles display on filter
         * @param filter
         */
        $scope.toggleFilter = function(filter) {
            $scope.filter_display[filter] = !$scope.filter_display[filter];
        };

        /**
         * Page results
         * @param page
         */
        $scope.setPage = function(page) {
            $scope.filters.page = page;
            $scope.changeRoute();
        };

        /**
         * Free text search
         */
        $scope.freeTextSearch = function() {
            var limit = $scope.filters.limit;
            var q = $scope.search_string;
            $scope.resetAllItems();
            $scope.filters.q = q;
            if(limit) {
                $scope.filters.limit = limit;
            }
            // $scope.filters.sort_field = '_score-desc';
            $scope.changeRoute();
        };

        $scope.resetFilter = function(items) {
            angular.forEach(items, function(item) {
                $scope.filters[item] = undefined;
            });
            $scope.changeRoute();
        };

        $scope.resetAllItems = function() {
            angular.forEach($scope.filters, function(value, key) {
                $scope.filters[key] = undefined;
            });
            angular.forEach($scope.channel_map, function(item) {
                item.selected = false;
            });
            angular.forEach($scope.source_map, function(item) {
                item.selected = false;
            });
            angular.forEach($scope.fulfillment_service_map, function(item) {
                item.selected = false;
            });
        };

        /**
         * Sets the value of a filter and re-routes
         * @param key
         * @param value
         */
        $scope.setFilter = function(key, value){
            $scope.filters[key] = value;
            $scope.changeRoute();
        };

        $scope.filterIsActive = function(filter, value) {
            return String($scope.filters[filter]) === String(value);
        };

        /**
         * Determines if the reset filter option is displayed
         * @returns {boolean}
         */
        $scope.showResetFilters = function() {
            for (var key in $routeParams) {
                if ($scope.filters[key]) {
                    return true;
                }
            }
            return false;
        };

        /**
         * Fetch a well formatted label for the filters
         * @param key
         * @param value
         * @returns {*}
         */
        $scope.filterLabel = function(key, value) {
            if(!$scope.filters[key]) {
                return value;
            }

            if(key === 'channels') {
                return $scope.channel_map[value].description;
            }
        };

        $scope.toggleFilters = function() {
            $scope.filtersVisible = !$scope.filtersVisible;
            $scope.filters.show = false;
            if($scope.filtersVisible) {
                $scope.filters.show = "Y";
            }
        };

        /**
         * Cleanup filters before changing route
         */
        $scope.changeRoute = function() {

            // cleanup, make sure all blank values are undefined
            for(var key in $scope.filters) {
                if($scope.filters[key] === "") {
                    $scope.filters[key] = undefined;
                }
            }

            if($scope.filtersVisible) {
                $scope.filters.show = "Y";
            }

            // timestamp
            $scope.filters.ts = new Date().getTime();

            utils.changeRoute(
                "/errors",
                $scope.filters
            );
        };

        $scope.viewEntity = function(error) {
            var route = '/';
            if (error.entity === 'product' || error.entity === 'customer') {

                // Products and Customer routes
                route += error.entity += '/general/' + error.entity_id;
                utils.changeRoute(route);
            } else if (error.entity === 'variant') {

                // Variant route
                stock2shop.products.getVariant({variant_id: error.entity_id}, function (data) {
                    data = data.data;
                    if (data.system_variant.product_id) {
                        route += 'product/general/' + data.system_variant.product_id;
                        utils.changeRoute(route);
                    } else {
                        growl.addErrorMessage('No product_id found for variant ' + error.entity_id);
                    }
                });
            } else if (error.entity === 'fulfillment') {

                // Fulfillment route
                stock2shop.fulfillments.getFulfillment({fulfillment_id: error.entity_id}, function (data) {
                    data = data.data;
                    if (data.errors.length > 0) {
                        var errors = data.errors.join(', ');
                        console.error('Error getting fulfillment ' + error.entity_id + ': ' + errors);
                        return false;
                    }
                    if (data.errors.length === 0 && data.results.order_id) {
                        route += 'order/' + data.results.order_id;
                        utils.changeRoute(route);
                    } else {
                        growl.addErrorMessage('No order_id found for fulfillment ' + error.entity_id);
                    }
                });
            } else if (error.entity === 'refund') {
                // We assume the first identifier is the order_id
                var order_id = error.identifiers[0] || '';
                route += order_id ? 'order/' + order_id : 'orders';
                utils.changeRoute(route);
            } else {

                // All other routes
                route += error.entity + '/' + error.entity_id;
                utils.changeRoute(route);
            }
        }

        /**
         * Build elastic search query & execute search
         */
        function search() {
            var query = {};

            // set free text search
            if ($scope.filters.q) {
                query.search = $scope.filters.q;
            }

            // set connector kind
            if ($scope.filters.connector_kind) {
                query.connector_kind = $scope.filters.connector_kind;
            }

            // set connector id
            if ($scope.filters.connector_id) {
                query.connector_id = $scope.filters.connector_id;
            }

            // set entity
            if ($scope.filters.entity) {
                query.entity = $scope.filters.entity;
            }

            // set entity id
            if ($scope.filters.entity_id) {
                query.entity_id = $scope.filters.entity_id;
            }

            // set direction
            if ($scope.filters.direction) {
                query.direction = $scope.filters.direction;
            }

            // set is delete
            if ($scope.filters.is_delete) {
                query.is_delete = $scope.filters.is_delete;
            }

            // paging
            query.limit = $scope.pagination.limit;
            query.offset = $scope.pagination.limit * ($scope.pagination.page - 1);

            stock2shop.sync_errors.getSyncErrors(query, function (data) {
                data = data.data;
                $scope.data = data;

                // Format modified date YYYY-MM-DD HH:MM:SS.000000 to YYYY-MM-DD HH:MM:SS
                angular.forEach(data.results, function (error) {
                    error.modified = error.modified.split('.')[0];
                });

                // HACK:
                // Our current API response does not include the total number of records in the response.
                // So we have to calculate it based on the number of records returned and the current page.
                var total_records = 0;
                if (parseInt($scope.pagination.page) > 1 && data.results.length === 0) {
                    total_records = 1 + parseInt($scope.pagination.limit) * (parseInt($scope.pagination.page) - 1);
                    data.results.push({});
                } else {
                    total_records = data.results.length + parseInt($scope.pagination.limit) * (parseInt($scope.pagination.page) - 1);
                    if (data.results.length === parseInt($scope.pagination.limit)) {
                        total_records += 1;
                    }
                }

                // pagination
                $scope.pagination.total = total_records;
                var records = (data.results)? data.results.length: 0;
                records = data.results.length === 0 ? 1 : records;
                utils.setPaginationDisplay($scope.pagination, records);

                // Remove everything after "of" in the pagination display i.e. "1-10 of 100" -> "1-10"
                $scope.pagination.display = $scope.pagination.display.split(' of')[0];

                // set page visible
                $scope.showErrors = true;
            });
        }

        function getFilteredConnectorId(connector_kind, connector_id) {
            if (connector_kind && connector_id) {
                if ($routeParams.connector_kind === connector_kind) {
                    return connector_id;
                }
            }
            return undefined;
        }
    }
]);
