'use strict';

angular.module('myApp').controller(
    'FulfillmentserviceAddCtrl',
    [
        '$scope',
        '$route',
        '$routeParams',
        '$q',
        'growl',
        'stock2shopAdmin',
        'utils',
        function ($scope, $route, $routeParams, $q, growl, stock2shopAdmin, utils) {

            $scope.data = {
                client_id: $routeParams.client_id,
                fulfillment_service: {}
            };

            // add fulfillment_service
            $scope.addFulfillmentservice = function() {
                stock2shopAdmin.admin_fulfillment_services.addFulfillmentService(
                    {
                        client_id: $scope.data.client_id,
                        body: {system_fulfillment_service: $scope.data.fulfillment_service},
                    }
                ).then(function(data) {
                        utils.changeRoute('/clients/edit/' + $scope.data.client_id);
                    }
                );
            };
        }
    ]);