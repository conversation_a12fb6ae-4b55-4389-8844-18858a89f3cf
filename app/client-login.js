angular.module('myApp').controller('LoginCtrl', ['$scope', 'clientAuth', 'stock2shop', 'growl',
    function($scope, auth, stock2shop, growl) {
        $scope.username = "";
        $scope.password = "";
        $scope.showChangePassword = false;
        $scope.showAccountLocked = false;
        $scope.change = {
            old_password: '',
            new_password: '',
            new_password_confirmation: ''
        };

        $scope.showForgotPassword = false;
        $scope.showMFA = false;
        if (window.gm.getParameterByName("reset") == "true") {
            $scope.showForgotPassword = true;
        }

        $scope.autoLogin = function() {
            // get token from url
            var search = window.location.search;
            search = search.substring(1); // Remove leading '?'
            var params = new URLSearchParams(search);
            var token  =params.get("token");
            if(token) {
                stock2shop.users.validToken(
                    {
                        token: token
                    },
                    function(result) {
                        if(result.data.system_user.client_name) {
                            setCredentialsAndRedirect(result);
                        } else {
                            stock2shop.config.getConfig({token: token}, function(conf_results) {
                                result.data.system_user.client_name = conf_results.data.config.client_name;
                                result.data.system_user.role_description = null;
                                setCredentialsAndRedirect(result);
                            });
                        }
                    }
                );
            }
        };

        function setCredentialsAndRedirect(result) {
            var headers = result.headers();
            var credentials = auth.createCredentials(result.data.system_user, headers["x-console-client-version"]);
            auth.setCookie(credentials);
            auth.setCredentials(credentials);

            // Do not redirect if user role is trade
            // TODO Rather check for presence of "console" role?
            var tradeUser = false;
            angular.forEach(result.data.system_user.roles, function(role) {
                if (role.id === 1) {
                    tradeUser = true;
                }
            });
            if (tradeUser) {
                growl.addErrorMessage(
                    'Login with trade user not allowed');

            } else {
                auth.redirectAfterLogin();
            }
        }

        $scope.submit = function() {
            $scope.showAccountLocked = false;
            stock2shop.users.authenticateUser(
                {
                    body: {
                        system_user_auth: {
                            username: $scope.user.username,
                            password: $scope.user.password
                        }
                    }
                },
                function(result) {
                    setCredentialsAndRedirect(result);
                },
                function(result) {
                    // check string contains error message "password expired"
                    if (result.status === 403 && result.data.error) {
                        if (result.data.error.indexOf('Password expired') > -1) {
                            $scope.showChangePassword = true;
                        }
                        if (result.data.error.indexOf('MFA required') > -1) {
                            $scope.showMFA = true;
                        }
                    } else if (result.status === 503) {
                        auth.redirectToMaintenance();

                    } else if (
                        result.status === 401 && result.data.errors &&
                        result.data.errors.indexOf('User locked out') > -1
                    ) {
                        $scope.showAccountLocked = true;

                    } else {
                        growl.addErrorMessage('Invalid credentials');
                    }
                }
            );
        };

        $scope.submitForgotPassword = function() {
            stock2shop.users.requestPasswordReset(
                {
                    username: $scope.user.username
                },
                function(result) {
                    $scope.showEmailSent = true;
                    $scope.emailSent = result.data.system_user.message;
                },
                function(result) {
                    growl.addErrorMessage(result.data.errors);
                }
            );
        };

        $scope.submitChangePassword = function() {
            if($scope.change.new_password !== $scope.change.new_password_confirmation) {
                growl.addErrorMessage('Passwords do not match');
                return;
            }
            stock2shop.users.changePassword(
                {
                    username: $scope.user.username,
                    body: {
                        new_password: $scope.change.new_password,
                        old_password: $scope.change.old_password
                    }
                },
                function() {
                    $scope.user.password = $scope.change.new_password;
                    $scope.submit();
                },
                function(result) {
                    growl.addErrorMessage(result.data.errors);
                }
            );
        };

        // run autologin
        $scope.autoLogin();
    }
]);

