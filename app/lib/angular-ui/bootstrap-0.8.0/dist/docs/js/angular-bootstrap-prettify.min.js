/*
 AngularJS v1.1.5
 (c) 2010-2012 Google, Inc. http://angularjs.org
 License: MIT
*/
(function(v,t,H){'use strict';function F(d){return d.replace(/\&/g,"&amp;").replace(/\</g,"&lt;").replace(/\>/g,"&gt;").replace(/"/g,"&quot;")}function A(d,f){var a=t.element("<pre>"+f+"</pre>");d.html("");d.append(a.contents());return d}var s={},w={value:{}},K={"angular.js":"http://code.angularjs.org/"+t.version.full+"/angular.min.js","angular-resource.js":"http://code.angularjs.org/"+t.version.full+"/angular-resource.min.js","angular-sanitize.js":"http://code.angularjs.org/"+t.version.full+"/angular-sanitize.min.js",
"angular-cookies.js":"http://code.angularjs.org/"+t.version.full+"/angular-cookies.min.js"};s.jsFiddle=function(d,f,a){return{terminal:!0,link:function(o,c,l){function b(a,c){return'<input type="hidden" name="'+a+'" value="'+f(c)+'">'}var r={html:"",css:"",js:""};t.forEach(l.jsFiddle.split(" "),function(a,c){var b=a.split(".")[1];r[b]+=b=="html"?c==0?"<div ng-app"+(l.module?'="'+l.module+'"':"")+">\n"+d(a,2):"\n\n\n  <\!-- CACHE FILE: "+a+' --\>\n  <script type="text/ng-template" id="'+a+'">\n'+d(a,
4)+"  <\/script>\n":d(a)+"\n"});r.html+="</div>\n";A(c,'<form class="jsfiddle" method="post" action="http://jsfiddle.net/api/post/library/pure/" target="_blank">'+b("title","AngularJS Example: ")+b("css",'</style> <\!-- Ugly Hack due to jsFiddle issue: http://goo.gl/BUfGZ --\> \n<link rel="stylesheet" href="http://twitter.github.com/bootstrap/assets/css/bootstrap.css">\n'+a.angular+(l.resource?a.resource:"")+"<style>\n"+r.css)+b("html",r.html)+b("js",r.js)+'<button class="btn btn-primary"><i class="icon-white icon-pencil"></i> Edit Me</button></form>')}}};
s.code=function(){return{restrict:"E",terminal:!0}};s.prettyprint=["reindentCode",function(d){return{restrict:"C",terminal:!0,compile:function(f){f.html(v.prettyPrintOne(d(f.html()),H,!0))}}}];s.ngSetText=["getEmbeddedTemplate",function(d){return{restrict:"CA",priority:10,compile:function(f,a){A(f,F(d(a.ngSetText)))}}}];s.ngHtmlWrap=["reindentCode","templateMerge",function(d,f){return{compile:function(a,d){var c={head:"",module:"",body:a.text()};t.forEach((d.ngHtmlWrap||"").split(" "),function(a){if(a){var a=
K[a]||a,b=a.split(/\./).pop();b=="css"?c.head+='<link rel="stylesheet" href="'+a+'" type="text/css">\n':b=="js"?c.head+='<script src="'+a+'"><\/script>\n':c.module='="'+a+'"'}});A(a,F(f("<!doctype html>\n<html ng-app{{module}}>\n  <head>\n{{head:4}}  </head>\n  <body>\n{{body:4}}  </body>\n</html>",c)))}}}];s.ngSetHtml=["getEmbeddedTemplate",function(d){return{restrict:"CA",priority:10,compile:function(f,a){A(f,d(a.ngSetHtml))}}}];s.ngEvalJavascript=["getEmbeddedTemplate",function(d){return{compile:function(f,
a){var o=d(a.ngEvalJavascript);try{v.execScript?v.execScript(o||'""'):v.eval(o)}catch(c){v.console?v.console.log(o,"\n",c):v.alert(c)}}}}];s.ngEmbedApp=["$templateCache","$browser","$rootScope","$location","$sniffer",function(d,f,a,o,c){return{terminal:!0,link:function(l,b,r){l=[];l.push(["$provide",function(b){b.value("$templateCache",d);b.value("$anchorScroll",t.noop);b.value("$browser",f);b.value("$sniffer",c);b.provider("$location",function(){this.$get=["$rootScope",function(b){a.$on("$locationChangeSuccess",
function(a,c,d){b.$broadcast("$locationChangeSuccess",c,d)});return o}];this.html5Mode=t.noop});b.decorator("$timeout",["$rootScope","$delegate",function(a,b){return t.extend(function(c,d){return d&&d>50?setTimeout(function(){a.$apply(c)},d):b.apply(this,arguments)},b)}]);b.decorator("$rootScope",["$delegate",function(b){a.$watch(function(){b.$digest()});return b}])}]);r.ngEmbedApp&&l.push(r.ngEmbedApp);b.bind("click",function(a){a.target.attributes.getNamedItem("ng-click")&&a.preventDefault()});
t.bootstrap(b,l)}}}];w.reindentCode=function(){return function(d,f){if(!d)return d;for(var a=d.split(/\r?\n/),o="      ".substr(0,f||0),c;a.length&&a[0].match(/^\s*$/);)a.shift();for(;a.length&&a[a.length-1].match(/^\s*$/);)a.pop();var l=999;for(c=0;c<a.length;c++){var b=a[0],r=b.match(/^\s*/)[0];if(r!==b&&r.length<l)l=r.length}for(c=0;c<a.length;c++)a[c]=o+a[c].substring(l);a.push("");return a.join("\n")}};w.templateMerge=["reindentCode",function(d){return function(f,a){return f.replace(/\{\{(\w+)(?:\:(\d+))?\}\}/g,
function(f,c,l){f=a[c];l&&(f=d(f,l));return f==H?"":f})}}];w.getEmbeddedTemplate=["reindentCode",function(d){return function(f){f=document.getElementById(f);return!f?null:d(t.element(f).html(),0)}}];t.module("bootstrapPrettify",[]).directive(s).factory(w);v.PR_SHOULD_USE_CONTINUATION=!0;(function(){function d(a){function b(i){var a=i.charCodeAt(0);if(a!==92)return a;var k=i.charAt(1);return(a=O[k])?a:"0"<=k&&k<="7"?parseInt(i.substring(1),8):k==="u"||k==="x"?parseInt(i.substring(2),16):i.charCodeAt(1)}
function E(i){if(i<32)return(i<16?"\\x0":"\\x")+i.toString(16);i=String.fromCharCode(i);return i==="\\"||i==="-"||i==="]"||i==="^"?"\\"+i:i}function c(i){var a=i.substring(1,i.length-1).match(/\\u[0-9A-Fa-f]{4}|\\x[0-9A-Fa-f]{2}|\\[0-3][0-7]{0,2}|\\[0-7]{1,2}|\\[\s\S]|-|[^-\\]/g),i=[],k=a[0]==="^",e=["["];k&&e.push("^");for(var k=k?1:0,g=a.length;k<g;++k){var j=a[k];if(/\\[bdsw]/i.test(j))e.push(j);else{var j=b(j),h;k+2<g&&"-"===a[k+1]?(h=b(a[k+2]),k+=2):h=j;i.push([j,h]);h<65||j>122||(h<65||j>90||
i.push([Math.max(65,j)|32,Math.min(h,90)|32]),h<97||j>122||i.push([Math.max(97,j)&-33,Math.min(h,122)&-33]))}}i.sort(function(i,a){return i[0]-a[0]||a[1]-i[1]});a=[];g=[];for(k=0;k<i.length;++k)j=i[k],j[0]<=g[1]+1?g[1]=Math.max(g[1],j[1]):a.push(g=j);for(k=0;k<a.length;++k)j=a[k],e.push(E(j[0])),j[1]>j[0]&&(j[1]+1>j[0]&&e.push("-"),e.push(E(j[1])));e.push("]");return e.join("")}function d(i){for(var a=i.source.match(RegExp("(?:\\[(?:[^\\x5C\\x5D]|\\\\[\\s\\S])*\\]|\\\\u[A-Fa-f0-9]{4}|\\\\x[A-Fa-f0-9]{2}|\\\\[0-9]+|\\\\[^ux0-9]|\\(\\?[:!=]|[\\(\\)\\^]|[^\\x5B\\x5C\\(\\)\\^]+)",
"g")),e=a.length,b=[],g=0,j=0;g<e;++g){var h=a[g];h==="("?++j:"\\"===h.charAt(0)&&(h=+h.substring(1))&&(h<=j?b[h]=-1:a[g]=E(h))}for(g=1;g<b.length;++g)-1===b[g]&&(b[g]=++f);for(j=g=0;g<e;++g)h=a[g],h==="("?(++j,b[j]||(a[g]="(?:")):"\\"===h.charAt(0)&&(h=+h.substring(1))&&h<=j&&(a[g]="\\"+b[h]);for(g=0;g<e;++g)"^"===a[g]&&"^"!==a[g+1]&&(a[g]="");if(i.ignoreCase&&y)for(g=0;g<e;++g)h=a[g],i=h.charAt(0),h.length>=2&&i==="["?a[g]=c(h):i!=="\\"&&(a[g]=h.replace(/[a-zA-Z]/g,function(a){a=a.charCodeAt(0);
return"["+String.fromCharCode(a&-33,a|32)+"]"}));return a.join("")}for(var f=0,y=!1,n=!1,q=0,e=a.length;q<e;++q){var m=a[q];if(m.ignoreCase)n=!0;else if(/[a-z]/i.test(m.source.replace(/\\u[0-9a-f]{4}|\\x[0-9a-f]{2}|\\[^ux]/gi,""))){y=!0;n=!1;break}}for(var O={b:8,t:9,n:10,v:11,f:12,r:13},p=[],q=0,e=a.length;q<e;++q){m=a[q];if(m.global||m.multiline)throw Error(""+m);p.push("(?:"+d(m)+")")}return RegExp(p.join("|"),n?"gi":"g")}function f(a,b){function c(a){switch(a.nodeType){case 1:if(d.test(a.className))break;
for(var e=a.firstChild;e;e=e.nextSibling)c(e);e=a.nodeName.toLowerCase();if("br"===e||"li"===e)f[n]="\n",y[n<<1]=l++,y[n++<<1|1]=a;break;case 3:case 4:e=a.nodeValue,e.length&&(e=b?e.replace(/\r\n?/g,"\n"):e.replace(/[ \t\r\n]+/g," "),f[n]=e,y[n<<1]=l,l+=e.length,y[n++<<1|1]=a)}}var d=/(?:^|\s)nocode(?:\s|$)/,f=[],l=0,y=[],n=0;c(a);return{sourceCode:f.join("").replace(/\n$/,""),spans:y}}function a(a,b,c,d){b&&(a={sourceCode:b,basePos:a},c(a),d.push.apply(d,a.decorations))}function o(b,c){var E={},
f;(function(){for(var a=b.concat(c),n=[],q={},e=0,m=a.length;e<m;++e){var l=a[e],p=l[3];if(p)for(var i=p.length;--i>=0;)E[p.charAt(i)]=l;l=l[1];p=""+l;q.hasOwnProperty(p)||(n.push(l),q[p]=null)}n.push(/[\0-\uffff]/);f=d(n)})();var l=c.length,t=function(b){for(var d=b.basePos,G=[d,"pln"],e=0,m=b.sourceCode.match(f)||[],s={},p=0,i=m.length;p<i;++p){var x=m[p],k=s[x],u=void 0,g;if(typeof k==="string")g=!1;else{var j=E[x.charAt(0)];if(j)u=x.match(j[1]),k=j[0];else{for(g=0;g<l;++g)if(j=c[g],u=x.match(j[1])){k=
j[0];break}u||(k="pln")}if((g=k.length>=5&&"lang-"===k.substring(0,5))&&!(u&&typeof u[1]==="string"))g=!1,k="src";g||(s[x]=k)}j=e;e+=x.length;if(g){g=u[1];var h=x.indexOf(g),D=h+g.length;u[2]&&(D=x.length-u[2].length,h=D-g.length);k=k.substring(5);a(d+j,x.substring(0,h),t,G);a(d+j+h,g,r(k,g),G);a(d+j+D,x.substring(D),t,G)}else G.push(d+j,k)}b.decorations=G};return t}function c(a){var b=[],c=[];a.tripleQuotedStrings?b.push(["str",/^(?:\'\'\'(?:[^\'\\]|\\[\s\S]|\'{1,2}(?=[^\']))*(?:\'\'\'|$)|\"\"\"(?:[^\"\\]|\\[\s\S]|\"{1,2}(?=[^\"]))*(?:\"\"\"|$)|\'(?:[^\\\']|\\[\s\S])*(?:\'|$)|\"(?:[^\\\"]|\\[\s\S])*(?:\"|$))/,
null,"'\""]):a.multiLineStrings?b.push(["str",/^(?:\'(?:[^\\\']|\\[\s\S])*(?:\'|$)|\"(?:[^\\\"]|\\[\s\S])*(?:\"|$)|\`(?:[^\\\`]|\\[\s\S])*(?:\`|$))/,null,"'\"`"]):b.push(["str",/^(?:\'(?:[^\\\'\r\n]|\\.)*(?:\'|$)|\"(?:[^\\\"\r\n]|\\.)*(?:\"|$))/,null,"\"'"]);a.verbatimStrings&&c.push(["str",/^@\"(?:[^\"]|\"\")*(?:\"|$)/,null]);var d=a.hashComments;d&&(a.cStyleComments?(d>1?b.push(["com",/^#(?:##(?:[^#]|#(?!##))*(?:###|$)|.*)/,null,"#"]):b.push(["com",/^#(?:(?:define|elif|else|endif|error|ifdef|include|ifndef|line|pragma|undef|warning)\b|[^\r\n]*)/,
null,"#"]),c.push(["str",/^<(?:(?:(?:\.\.\/)*|\/?)(?:[\w-]+(?:\/[\w-]+)+)?[\w-]+\.h(?:h|pp|\+\+)?|[a-z]\w*)>/,null])):b.push(["com",/^#[^\r\n]*/,null,"#"]));a.cStyleComments&&(c.push(["com",/^\/\/[^\r\n]*/,null]),c.push(["com",/^\/\*[\s\S]*?(?:\*\/|$)/,null]));a.regexLiterals&&c.push(["lang-regex",RegExp("^(?:^^\\.?|[+-]|[!=]=?=?|\\#|%=?|&&?=?|\\(|\\*=?|[+\\-]=|->|\\/=?|::?|<<?=?|>>?>?=?|,|;|\\?|@|\\[|~|{|\\^\\^?=?|\\|\\|?=?|break|case|continue|delete|do|else|finally|instanceof|return|throw|try|typeof)\\s*(/(?=[^/*])(?:[^/\\x5B\\x5C]|\\x5C[\\s\\S]|\\x5B(?:[^\\x5C\\x5D]|\\x5C[\\s\\S])*(?:\\x5D|$))+/)")]);
(d=a.types)&&c.push(["typ",d]);a=(""+a.keywords).replace(/^ | $/g,"");a.length&&c.push(["kwd",RegExp("^(?:"+a.replace(/[\s,]+/g,"|")+")\\b"),null]);b.push(["pln",/^\s+/,null," \r\n\t\u00a0"]);c.push(["lit",/^@[a-z_$][a-z_$@0-9]*/i,null],["typ",/^(?:[@_]?[A-Z]+[a-z][A-Za-z_$@0-9]*|\w+_t\b)/,null],["pln",/^[a-z_$][a-z_$@0-9]*/i,null],["lit",/^(?:0x[a-f0-9]+|(?:\d(?:_\d+)*\d*(?:\.\d*)?|\.\d\+)(?:e[+\-]?\d+)?)[a-z]*/i,null,"0123456789"],["pln",/^\\[\s\S]?/,null],["pun",/^.[^\s\w\.$@\'\"\`\/\#\\]*/,null]);
return o(b,c)}function l(a,b,c){function d(a){switch(a.nodeType){case 1:if(l.test(a.className))break;if("br"===a.nodeName)f(a),a.parentNode&&a.parentNode.removeChild(a);else for(a=a.firstChild;a;a=a.nextSibling)d(a);break;case 3:case 4:if(c){var b=a.nodeValue,e=b.match(t);if(e){var m=b.substring(0,e.index);a.nodeValue=m;(b=b.substring(e.index+e[0].length))&&a.parentNode.insertBefore(n.createTextNode(b),a.nextSibling);f(a);m||a.parentNode.removeChild(a)}}}}function f(a){function b(a,c){var e=c?a.cloneNode(!1):
a,h=a.parentNode;if(h){var h=b(h,1),d=a.nextSibling;h.appendChild(e);for(var i=d;i;i=d)d=i.nextSibling,h.appendChild(i)}return e}for(;!a.nextSibling;)if(a=a.parentNode,!a)return;for(var a=b(a.nextSibling,0),c;(c=a.parentNode)&&c.nodeType===1;)a=c;e.push(a)}for(var l=/(?:^|\s)nocode(?:\s|$)/,t=/\r\n?|\n/,n=a.ownerDocument,q=n.createElement("li");a.firstChild;)q.appendChild(a.firstChild);for(var e=[q],m=0;m<e.length;++m)d(e[m]);b===(b|0)&&e[0].setAttribute("value",b);var s=n.createElement("ol");s.className=
"linenums";for(var b=Math.max(0,b-1|0)||0,m=0,p=e.length;m<p;++m)q=e[m],q.className="L"+(m+b)%10,q.firstChild||q.appendChild(n.createTextNode("\u00a0")),s.appendChild(q);a.appendChild(s)}function b(a,b){for(var c=b.length;--c>=0;){var d=b[c];I.hasOwnProperty(d)?s.console&&console.warn("cannot override language handler %s",d):I[d]=a}}function r(a,b){if(!a||!I.hasOwnProperty(a))a=/^\s*</.test(b)?"default-markup":"default-code";return I[a]}function t(a){var b=a.langExtension;try{var c=f(a.sourceNode,
a.pre),d=c.sourceCode;a.sourceCode=d;a.spans=c.spans;a.basePos=0;r(b,d)(a);var l=/\bMSIE\s(\d+)/.exec(navigator.userAgent),l=l&&+l[1]<=8,b=/\n/g,o=a.sourceCode,y=o.length,c=0,n=a.spans,q=n.length,d=0,e=a.decorations,m=e.length,v=0;e[m]=y;var p,i;for(i=p=0;i<m;)e[i]!==e[i+2]?(e[p++]=e[i++],e[p++]=e[i++]):i+=2;m=p;for(i=p=0;i<m;){for(var x=e[i],k=e[i+1],u=i+2;u+2<=m&&e[u+1]===k;)u+=2;e[p++]=x;e[p++]=k;i=u}e.length=p;var g=a.sourceNode,j;if(g)j=g.style.display,g.style.display="none";try{for(;d<q;){var h=
n[d+2]||y,D=e[v+2]||y,u=Math.min(h,D),C=n[d+1],J;if(C.nodeType!==1&&(J=o.substring(c,u))){l&&(J=J.replace(b,"\r"));C.nodeValue=J;var A=C.ownerDocument,w=A.createElement("span");w.className=e[v+1];var B=C.parentNode;B.replaceChild(w,C);w.appendChild(C);c<h&&(n[d+1]=C=A.createTextNode(o.substring(u,h)),B.insertBefore(C,w.nextSibling))}c=u;c>=h&&(d+=2);c>=D&&(v+=2)}}finally{if(g)g.style.display=j}}catch(z){s.console&&console.log(z&&z.stack?z.stack:z)}}var s=v,B=["break,continue,do,else,for,if,return,while"],
z=[[B,"auto,case,char,const,default,double,enum,extern,float,goto,int,long,register,short,signed,sizeof,static,struct,switch,typedef,union,unsigned,void,volatile"],"catch,class,delete,false,import,new,operator,private,protected,public,this,throw,true,try,typeof"],A=[z,"alignof,align_union,asm,axiom,bool,concept,concept_map,const_cast,constexpr,decltype,dynamic_cast,explicit,export,friend,inline,late_check,mutable,namespace,nullptr,reinterpret_cast,static_assert,static_cast,template,typeid,typename,using,virtual,where"],
w=[z,"abstract,boolean,byte,extends,final,finally,implements,import,instanceof,null,native,package,strictfp,super,synchronized,throws,transient"],F=[w,"as,base,by,checked,decimal,delegate,descending,dynamic,event,fixed,foreach,from,group,implicit,in,interface,internal,into,is,let,lock,object,out,override,orderby,params,partial,readonly,ref,sbyte,sealed,stackalloc,string,select,uint,ulong,unchecked,unsafe,ushort,var,virtual,where"],z=[z,"debugger,eval,export,function,get,null,set,undefined,var,with,Infinity,NaN"],
L=[B,"and,as,assert,class,def,del,elif,except,exec,finally,from,global,import,in,is,lambda,nonlocal,not,or,pass,print,raise,try,with,yield,False,True,None"],M=[B,"alias,and,begin,case,class,def,defined,elsif,end,ensure,false,in,module,next,nil,not,or,redo,rescue,retry,self,super,then,true,undef,unless,until,when,yield,BEGIN,END"],B=[B,"case,done,elif,esac,eval,fi,function,in,local,set,then,until"],N=/^(DIR|FILE|vector|(de|priority_)?queue|list|stack|(const_)?iterator|(multi)?(set|map)|bitset|u?(int|float)\d*)\b/,
K=/\S/,P=c({keywords:[A,F,z,"caller,delete,die,do,dump,elsif,eval,exit,foreach,for,goto,if,import,last,local,my,next,no,our,print,package,redo,require,sub,undef,unless,until,use,wantarray,while,BEGIN,END"+L,M,B],hashComments:!0,cStyleComments:!0,multiLineStrings:!0,regexLiterals:!0}),I={};b(P,["default-code"]);b(o([],[["pln",/^[^<?]+/],["dec",/^<!\w[^>]*(?:>|$)/],["com",/^<\!--[\s\S]*?(?:-\->|$)/],["lang-",/^<\?([\s\S]+?)(?:\?>|$)/],["lang-",/^<%([\s\S]+?)(?:%>|$)/],["pun",/^(?:<[%?]|[%?]>)/],["lang-",
/^<xmp\b[^>]*>([\s\S]+?)<\/xmp\b[^>]*>/i],["lang-js",/^<script\b[^>]*>([\s\S]*?)(<\/script\b[^>]*>)/i],["lang-css",/^<style\b[^>]*>([\s\S]*?)(<\/style\b[^>]*>)/i],["lang-in.tag",/^(<\/?[a-z][^<>]*>)/i]]),["default-markup","htm","html","mxml","xhtml","xml","xsl"]);b(o([["pln",/^[\s]+/,null," \t\r\n"],["atv",/^(?:\"[^\"]*\"?|\'[^\']*\'?)/,null,"\"'"]],[["tag",/^^<\/?[a-z](?:[\w.:-]*\w)?|\/?>$/i],["atn",/^(?!style[\s=]|on)[a-z](?:[\w:-]*\w)?/i],["lang-uq.val",/^=\s*([^>\'\"\s]*(?:[^>\'\"\s\/]|\/(?=\s)))/],
["pun",/^[=<>\/]+/],["lang-js",/^on\w+\s*=\s*\"([^\"]+)\"/i],["lang-js",/^on\w+\s*=\s*\'([^\']+)\'/i],["lang-js",/^on\w+\s*=\s*([^\"\'>\s]+)/i],["lang-css",/^style\s*=\s*\"([^\"]+)\"/i],["lang-css",/^style\s*=\s*\'([^\']+)\'/i],["lang-css",/^style\s*=\s*([^\"\'>\s]+)/i]]),["in.tag"]);b(o([],[["atv",/^[\s\S]+/]]),["uq.val"]);b(c({keywords:A,hashComments:!0,cStyleComments:!0,types:N}),["c","cc","cpp","cxx","cyc","m"]);b(c({keywords:"null,true,false"}),["json"]);b(c({keywords:F,hashComments:!0,cStyleComments:!0,
verbatimStrings:!0,types:N}),["cs"]);b(c({keywords:w,cStyleComments:!0}),["java"]);b(c({keywords:B,hashComments:!0,multiLineStrings:!0}),["bsh","csh","sh"]);b(c({keywords:L,hashComments:!0,multiLineStrings:!0,tripleQuotedStrings:!0}),["cv","py"]);b(c({keywords:"caller,delete,die,do,dump,elsif,eval,exit,foreach,for,goto,if,import,last,local,my,next,no,our,print,package,redo,require,sub,undef,unless,until,use,wantarray,while,BEGIN,END",hashComments:!0,multiLineStrings:!0,regexLiterals:!0}),["perl",
"pl","pm"]);b(c({keywords:M,hashComments:!0,multiLineStrings:!0,regexLiterals:!0}),["rb"]);b(c({keywords:z,cStyleComments:!0,regexLiterals:!0}),["js"]);b(c({keywords:"all,and,by,catch,class,else,extends,false,finally,for,if,in,is,isnt,loop,new,no,not,null,of,off,on,or,return,super,then,throw,true,try,unless,until,when,while,yes",hashComments:3,cStyleComments:!0,multilineStrings:!0,tripleQuotedStrings:!0,regexLiterals:!0}),["coffee"]);b(o([],[["str",/^[\s\S]+/]]),["regex"]);var Q=s.PR={createSimpleLexer:o,
registerLangHandler:b,sourceDecorator:c,PR_ATTRIB_NAME:"atn",PR_ATTRIB_VALUE:"atv",PR_COMMENT:"com",PR_DECLARATION:"dec",PR_KEYWORD:"kwd",PR_LITERAL:"lit",PR_NOCODE:"nocode",PR_PLAIN:"pln",PR_PUNCTUATION:"pun",PR_SOURCE:"src",PR_STRING:"str",PR_TAG:"tag",PR_TYPE:"typ",prettyPrintOne:s.prettyPrintOne=function(a,b,c){var d=document.createElement("div");d.innerHTML="<pre>"+a+"</pre>";d=d.firstChild;c&&l(d,c,!0);t({langExtension:b,numberLines:c,sourceNode:d,pre:1});return d.innerHTML},prettyPrint:s.prettyPrint=
function(a){function b(){var u;for(var c=s.PR_SHOULD_USE_CONTINUATION?n.now()+250:Infinity;q<d.length&&n.now()<c;q++){var g=d[q],j=g.className;if(v.test(j)&&!p.test(j)){for(var h=!1,f=g.parentNode;f;f=f.parentNode)if(k.test(f.tagName)&&f.className&&v.test(f.className)){h=!0;break}if(!h){g.className+=" prettyprinted";var j=j.match(m),o;if(h=!j){for(var h=g,f=H,r=h.firstChild;r;r=r.nextSibling)var w=r.nodeType,f=w===1?f?h:r:w===3?K.test(r.nodeValue)?h:f:f;h=(o=f===h?H:f)&&x.test(o.tagName)}h&&(j=o.className.match(m));
j&&(j=j[1]);u=i.test(g.tagName)?1:(h=(h=g.currentStyle)?h.whiteSpace:document.defaultView&&document.defaultView.getComputedStyle?document.defaultView.getComputedStyle(g,null).getPropertyValue("white-space"):0)&&"pre"===h.substring(0,3),h=u;(f=(f=g.className.match(/\blinenums\b(?::(\d+))?/))?f[1]&&f[1].length?+f[1]:!0:!1)&&l(g,f,h);e={langExtension:j,sourceNode:g,numberLines:f,pre:h};t(e)}}}q<d.length?setTimeout(b,250):a&&a()}for(var c=[document.getElementsByTagName("pre"),document.getElementsByTagName("code"),
document.getElementsByTagName("xmp")],d=[],f=0;f<c.length;++f)for(var o=0,r=c[f].length;o<r;++o)d.push(c[f][o]);var c=null,n=Date;n.now||(n={now:function(){return+new Date}});var q=0,e,m=/\blang(?:uage)?-([\w.]+)(?!\S)/,v=/\bprettyprint\b/,p=/\bprettyprinted\b/,i=/pre|xmp/i,x=/^code$/i,k=/^(?:pre|code|xmp)$/i;b()}};typeof define==="function"&&define.amd&&define("google-code-prettify",[],function(){return Q})})()})(window,window.angular);angular.element(document).find("head").append('<style type="text/css">.com{color:#93a1a1;}.lit{color:#195f91;}.pun,.opn,.clo{color:#93a1a1;}.fun{color:#dc322f;}.str,.atv{color:#D14;}.kwd,.linenums .tag{color:#1e347b;}.typ,.atn,.dec,.var{color:teal;}.pln{color:#48484c;}.prettyprint{padding:8px;background-color:#f7f7f9;border:1px solid #e1e1e8;}.prettyprint.linenums{-webkit-box-shadow:inset 40px 0 0 #fbfbfc,inset 41px 0 0 #ececf0;-moz-box-shadow:inset 40px 0 0 #fbfbfc,inset 41px 0 0 #ececf0;box-shadow:inset 40px 0 0 #fbfbfc,inset 41px 0 0 #ececf0;}ol.linenums{margin:0 0 0 33px;}ol.linenums li{padding-left:12px;color:#bebec5;line-height:18px;text-shadow:0 1px 0 #fff;}</style>');
