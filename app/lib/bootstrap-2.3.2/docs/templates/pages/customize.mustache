<!-- Masthead
================================================== -->
<header class="jumbotron subhead" id="overview">
  <div class="container">
    <h1>{{_i}}Customize and download{{/i}}</h1>
    <p class="lead">{{_i}}<a href="https://github.com/twbs/bootstrap/archive/v2.3.2.zip">Download Bootstrap</a> or customize variables, components, JavaScript plugins, and more.{{/i}}</p>
  </div>
</header>


  <div class="container">

    <!-- Docs nav
    ================================================== -->
    <div class="row">
      <div class="span3 bs-docs-sidebar">
        <ul class="nav nav-list bs-docs-sidenav">
          <li><a href="#components"><i class="icon-chevron-right"></i> {{_i}}1. Choose components{{/i}}</a></li>
          <li><a href="#plugins"><i class="icon-chevron-right"></i> {{_i}}2. Select jQuery plugins{{/i}}</a></li>
          <li><a href="#variables"><i class="icon-chevron-right"></i> {{_i}}3. Customize variables{{/i}}</a></li>
          <li><a href="#download"><i class="icon-chevron-right"></i> {{_i}}4. Download{{/i}}</a></li>
        </ul>
      </div>
      <div class="span9">


        <!-- Customize form
        ================================================== -->
        <form>
          <section class="download" id="components">
            <div class="page-header">
              <a class="btn btn-small pull-right toggle-all" href="#">{{_i}}Toggle all{{/i}}</a>
              <h1>
                {{_i}}1. Choose components{{/i}}
              </h1>
            </div>
            <div class="row download-builder">
              <div class="span3">
                <h3>{{_i}}Scaffolding{{/i}}</h3>
                <label class="checkbox"><input checked="checked" type="checkbox" value="reset.less"> {{_i}}Normalize and reset{{/i}}</label>
                <label class="checkbox"><input checked="checked" type="checkbox" value="scaffolding.less"> {{_i}}Body type and links{{/i}}</label>
                <label class="checkbox"><input checked="checked" type="checkbox" value="grid.less"> {{_i}}Grid system{{/i}}</label>
                <label class="checkbox"><input checked="checked" type="checkbox" value="layouts.less"> {{_i}}Layouts{{/i}}</label>
                <h3>{{_i}}Base CSS{{/i}}</h3>
                <label class="checkbox"><input checked="checked" type="checkbox" value="type.less"> {{_i}}Headings, body, etc{{/i}}</label>
                <label class="checkbox"><input checked="checked" type="checkbox" value="code.less"> {{_i}}Code and pre{{/i}}</label>
                <label class="checkbox"><input checked="checked" type="checkbox" value="labels-badges.less"> {{_i}}Labels and badges{{/i}}</label>
                <label class="checkbox"><input checked="checked" type="checkbox" value="tables.less"> {{_i}}Tables{{/i}}</label>
                <label class="checkbox"><input checked="checked" type="checkbox" value="forms.less"> {{_i}}Forms{{/i}}</label>
                <label class="checkbox"><input checked="checked" type="checkbox" value="buttons.less"> {{_i}}Buttons{{/i}}</label>
                <label class="checkbox"><input checked="checked" type="checkbox" value="sprites.less"> {{_i}}Icons{{/i}}</label>
              </div><!-- /span -->
              <div class="span3">
                <h3>{{_i}}Components{{/i}}</h3>
                <label class="checkbox"><input checked="checked" type="checkbox" value="button-groups.less"> {{_i}}Button groups and dropdowns{{/i}}</label>
                <label class="checkbox"><input checked="checked" type="checkbox" value="navs.less"> {{_i}}Navs, tabs, and pills{{/i}}</label>
                <label class="checkbox"><input checked="checked" type="checkbox" value="navbar.less"> {{_i}}Navbar{{/i}}</label>
                <label class="checkbox"><input checked="checked" type="checkbox" value="breadcrumbs.less"> {{_i}}Breadcrumbs{{/i}}</label>
                <label class="checkbox"><input checked="checked" type="checkbox" value="pagination.less"> {{_i}}Pagination{{/i}}</label>
                <label class="checkbox"><input checked="checked" type="checkbox" value="pager.less"> {{_i}}Pager{{/i}}</label>
                <label class="checkbox"><input checked="checked" type="checkbox" value="thumbnails.less"> {{_i}}Thumbnails{{/i}}</label>
                <label class="checkbox"><input checked="checked" type="checkbox" value="alerts.less"> {{_i}}Alerts{{/i}}</label>
                <label class="checkbox"><input checked="checked" type="checkbox" value="progress-bars.less"> {{_i}}Progress bars{{/i}}</label>
                <label class="checkbox"><input checked="checked" type="checkbox" value="hero-unit.less"> {{_i}}Hero unit{{/i}}</label>
                <label class="checkbox"><input checked="checked" type="checkbox" value="media.less"> {{_i}}Media component{{/i}}</label>
                <h3>{{_i}}JS Components{{/i}}</h3>
                <label class="checkbox"><input checked="checked" type="checkbox" value="tooltip.less"> {{_i}}Tooltips{{/i}}</label>
                <label class="checkbox"><input checked="checked" type="checkbox" value="popovers.less"> {{_i}}Popovers{{/i}}</label>
                <label class="checkbox"><input checked="checked" type="checkbox" value="modals.less"> {{_i}}Modals{{/i}}</label>
                <label class="checkbox"><input checked="checked" type="checkbox" value="dropdowns.less"> {{_i}}Dropdowns{{/i}}</label>
                <label class="checkbox"><input checked="checked" type="checkbox" value="accordion.less"> {{_i}}Collapse{{/i}}</label>
                <label class="checkbox"><input checked="checked" type="checkbox" value="carousel.less"> {{_i}}Carousel{{/i}}</label>
              </div><!-- /span -->
              <div class="span3">
                <h3>{{_i}}Miscellaneous{{/i}}</h3>
                <label class="checkbox"><input checked="checked" type="checkbox" value="wells.less"> {{_i}}Wells{{/i}}</label>
                <label class="checkbox"><input checked="checked" type="checkbox" value="close.less"> {{_i}}Close icon{{/i}}</label>
                <label class="checkbox"><input checked="checked" type="checkbox" value="utilities.less"> {{_i}}Utilities{{/i}}</label>
                <label class="checkbox"><input checked="checked" type="checkbox" value="component-animations.less"> {{_i}}Component animations{{/i}}</label>
                <h3>{{_i}}Responsive{{/i}}</h3>
                <label class="checkbox"><input checked="checked" type="checkbox" value="responsive-utilities.less"> {{_i}}Visible/hidden classes{{/i}}</label>
                <label class="checkbox"><input checked="checked" type="checkbox" value="responsive-767px-max.less"> {{_i}}Narrow tablets and below (<767px){{/i}}</label>
                <label class="checkbox"><input checked="checked" type="checkbox" value="responsive-768px-979px.less"> {{_i}}Tablets to desktops (767-979px){{/i}}</label>
                <label class="checkbox"><input checked="checked" type="checkbox" value="responsive-1200px-min.less"> {{_i}}Large desktops (>1200px){{/i}}</label>
                <label class="checkbox"><input checked="checked" type="checkbox" value="responsive-navbar.less"> {{_i}}Responsive navbar{{/i}}</label>
              </div><!-- /span -->
            </div><!-- /row -->
          </section>

          <section class="download" id="plugins">
            <div class="page-header">
              <a class="btn btn-small pull-right toggle-all" href="#">{{_i}}Toggle all{{/i}}</a>
              <h1>
                {{_i}}2. Select jQuery plugins{{/i}}
              </h1>
            </div>
            <div class="row download-builder">
              <div class="span3">
                <label class="checkbox">
                  <input type="checkbox" checked="true" value="bootstrap-transition.js">
                  {{_i}}Transitions <small>(required for any animation)</small>{{/i}}
                </label>
                <label class="checkbox">
                  <input type="checkbox" checked="true" value="bootstrap-modal.js">
                  {{_i}}Modals{{/i}}
                </label>
                <label class="checkbox">
                  <input type="checkbox" checked="true" value="bootstrap-dropdown.js">
                  {{_i}}Dropdowns{{/i}}
                </label>
                <label class="checkbox">
                  <input type="checkbox" checked="true" value="bootstrap-scrollspy.js">
                  {{_i}}Scrollspy{{/i}}
                </label>
                <label class="checkbox">
                  <input type="checkbox" checked="true" value="bootstrap-tab.js">
                  {{_i}}Togglable tabs{{/i}}
                </label>
                <label class="checkbox">
                  <input type="checkbox" checked="true" value="bootstrap-tooltip.js">
                  {{_i}}Tooltips{{/i}}
                </label>
                <label class="checkbox">
                  <input type="checkbox" checked="true" value="bootstrap-popover.js">
                  {{_i}}Popovers <small>(requires Tooltips)</small>{{/i}}
                </label>
              </div><!-- /span -->
              <div class="span3">
                <label class="checkbox">
                  <input type="checkbox" checked="true" value="bootstrap-affix.js">
                  {{_i}}Affix{{/i}}
                </label>
                <label class="checkbox">
                  <input type="checkbox" checked="true" value="bootstrap-alert.js">
                  {{_i}}Alert messages{{/i}}
                </label>
                <label class="checkbox">
                  <input type="checkbox" checked="true" value="bootstrap-button.js">
                  {{_i}}Buttons{{/i}}
                </label>
                <label class="checkbox">
                  <input type="checkbox" checked="true" value="bootstrap-collapse.js">
                  {{_i}}Collapse{{/i}}
                </label>
                <label class="checkbox">
                  <input type="checkbox" checked="true" value="bootstrap-carousel.js">
                  {{_i}}Carousel{{/i}}
                </label>
                <label class="checkbox">
                  <input type="checkbox" checked="true" value="bootstrap-typeahead.js">
                  {{_i}}Typeahead{{/i}}
                </label>
              </div><!-- /span -->
              <div class="span3">
                <h4 class="muted">{{_i}}Heads up!{{/i}}</h4>
                <p class="muted">{{_i}}All checked plugins will be compiled into a single file, bootstrap.js. All plugins require the latest version of <a href="http://jquery.com/" target="_blank">jQuery</a> to be included.{{/i}}</p>
              </div><!-- /span -->
            </div><!-- /row -->
          </section>


          <section class="download" id="variables">
            <div class="page-header">
              <a class="btn btn-small pull-right toggle-all" href="#">{{_i}}Reset to defaults{{/i}}</a>
              <h1>
                {{_i}}3. Customize variables{{/i}}
              </h1>
            </div>
            <div class="row download-builder">
              <div class="span3">
                <h3>{{_i}}Scaffolding{{/i}}</h3>
                <label>@bodyBackground</label>
                <input type="text" class="span3" placeholder="@white">
                <label>@textColor</label>
                <input type="text" class="span3" placeholder="@grayDark">

                <h3>{{_i}}Links{{/i}}</h3>
                <label>@linkColor</label>
                <input type="text" class="span3" placeholder="#08c">
                <label>@linkColorHover</label>
                <input type="text" class="span3" placeholder="darken(@linkColor, 15%)">
                <h3>{{_i}}Colors{{/i}}</h3>
                <label>@blue</label>
                <input type="text" class="span3" placeholder="#049cdb">
                <label>@green</label>
                <input type="text" class="span3" placeholder="#46a546">
                <label>@red</label>
                <input type="text" class="span3" placeholder="#9d261d">
                <label>@yellow</label>
                <input type="text" class="span3" placeholder="#ffc40d">
                <label>@orange</label>
                <input type="text" class="span3" placeholder="#f89406">
                <label>@pink</label>
                <input type="text" class="span3" placeholder="#c3325f">
                <label>@purple</label>
                <input type="text" class="span3" placeholder="#7a43b6">

                <h3>{{_i}}Sprites{{/i}}</h3>
                <label>@iconSpritePath</label>
                <input type="text" class="span3" placeholder="'../img/glyphicons-halflings.png'">
                <label>@iconWhiteSpritePath</label>
                <input type="text" class="span3" placeholder="'../img/glyphicons-halflings-white.png'">

                <h3>{{_i}}Grid system{{/i}}</h3>
                <label>@gridColumns</label>
                <input type="text" class="span3" placeholder="12">
                <label>@gridColumnWidth</label>
                <input type="text" class="span3" placeholder="60px">
                <label>@gridGutterWidth</label>
                <input type="text" class="span3" placeholder="20px">
                <label>@gridColumnWidth1200</label>
                <input type="text" class="span3" placeholder="70px">
                <label>@gridGutterWidth1200</label>
                <input type="text" class="span3" placeholder="30px">
                <label>@gridColumnWidth768</label>
                <input type="text" class="span3" placeholder="42px">
                <label>@gridGutterWidth768</label>
                <input type="text" class="span3" placeholder="20px">

              </div><!-- /span -->
              <div class="span3">

                <h3>{{_i}}Typography{{/i}}</h3>
                <label>@sansFontFamily</label>
                <input type="text" class="span3" placeholder="'Helvetica Neue', Helvetica, Arial, sans-serif">
                <label>@serifFontFamily</label>
                <input type="text" class="span3" placeholder="Georgia, 'Times New Roman', Times, serif">
                <label>@monoFontFamily</label>
                <input type="text" class="span3" placeholder="Menlo, Monaco, 'Courier New', monospace">

                <label>@baseFontSize</label>
                <input type="text" class="span3" placeholder="14px">
                <label>@baseFontFamily</label>
                <input type="text" class="span3" placeholder="@sansFontFamily">
                <label>@baseLineHeight</label>
                <input type="text" class="span3" placeholder="20px">

                <label>@altFontFamily</label>
                <input type="text" class="span3" placeholder="@serifFontFamily">
                <label>@headingsFontFamily</label>
                <input type="text" class="span3" placeholder="inherit">
                <label>@headingsFontWeight</label>
                <input type="text" class="span3" placeholder="bold">
                <label>@headingsColor</label>
                <input type="text" class="span3" placeholder="inherit">

                <label>@fontSizeLarge</label>
                <input type="text" class="span3" placeholder="@baseFontSize * 1.25">
                <label>@fontSizeSmall</label>
                <input type="text" class="span3" placeholder="@baseFontSize * 0.85">
                <label>@fontSizeMini</label>
                <input type="text" class="span3" placeholder="@baseFontSize * 0.75">

                <label>@paddingLarge</label>
                <input type="text" class="span3" placeholder="11px 19px">
                <label>@paddingSmall</label>
                <input type="text" class="span3" placeholder="2px 10px">
                <label>@paddingMini</label>
                <input type="text" class="span3" placeholder="1px 6px">

                <label>@baseBorderRadius</label>
                <input type="text" class="span3" placeholder="4px">
                <label>@borderRadiusLarge</label>
                <input type="text" class="span3" placeholder="6px">
                <label>@borderRadiusSmall</label>
                <input type="text" class="span3" placeholder="3px">

                <label>@heroUnitBackground</label>
                <input type="text" class="span3" placeholder="@grayLighter">
                <label>@heroUnitHeadingColor</label>
                <input type="text" class="span3" placeholder="inherit">
                <label>@heroUnitLeadColor</label>
                <input type="text" class="span3" placeholder="inherit">

                <h3>{{_i}}Tables{{/i}}</h3>
                <label>@tableBackground</label>
                <input type="text" class="span3" placeholder="transparent">
                <label>@tableBackgroundAccent</label>
                <input type="text" class="span3" placeholder="#f9f9f9">
                <label>@tableBackgroundHover</label>
                <input type="text" class="span3" placeholder="#f5f5f5">
                <label>@tableBorder</label>
                <input type="text" class="span3" placeholder="#ddd">

                <h3>{{_i}}Forms{{/i}}</h3>
                <label>@placeholderText</label>
                <input type="text" class="span3" placeholder="@grayLight">
                <label>@inputBackground</label>
                <input type="text" class="span3" placeholder="@white">
                <label>@inputBorder</label>
                <input type="text" class="span3" placeholder="#ccc">
                <label>@inputBorderRadius</label>
                <input type="text" class="span3" placeholder="3px">
                <label>@inputDisabledBackground</label>
                <input type="text" class="span3" placeholder="@grayLighter">
                <label>@formActionsBackground</label>
                <input type="text" class="span3" placeholder="#f5f5f5">
                <label>@btnPrimaryBackground</label>
                <input type="text" class="span3" placeholder="@linkColor">
                <label>@btnPrimaryBackgroundHighlight</label>
                <input type="text" class="span3" placeholder="darken(@white, 10%)">

              </div><!-- /span -->
              <div class="span3">

                <h3>{{_i}}Form states &amp; alerts{{/i}}</h3>
                <label>@warningText</label>
                <input type="text" class="span3" placeholder="#c09853">
                <label>@warningBackground</label>
                <input type="text" class="span3" placeholder="#fcf8e3">
                <label>@errorText</label>
                <input type="text" class="span3" placeholder="#b94a48">
                <label>@errorBackground</label>
                <input type="text" class="span3" placeholder="#f2dede">
                <label>@successText</label>
                <input type="text" class="span3" placeholder="#468847">
                <label>@successBackground</label>
                <input type="text" class="span3" placeholder="#dff0d8">
                <label>@infoText</label>
                <input type="text" class="span3" placeholder="#3a87ad">
                <label>@infoBackground</label>
                <input type="text" class="span3" placeholder="#d9edf7">

                <h3>{{_i}}Navbar{{/i}}</h3>
                <label>@navbarHeight</label>
                <input type="text" class="span3" placeholder="40px">
                <label>@navbarBackground</label>
                <input type="text" class="span3" placeholder="@grayDarker">
                <label>@navbarBackgroundHighlight</label>
                <input type="text" class="span3" placeholder="@grayDark">
                <label>@navbarText</label>
                <input type="text" class="span3" placeholder="@grayLight">
                <label>@navbarBrandColor</label>
                <input type="text" class="span3" placeholder="@navbarLinkColor">
                <label>@navbarLinkColor</label>
                <input type="text" class="span3" placeholder="@grayLight">
                <label>@navbarLinkColorHover</label>
                <input type="text" class="span3" placeholder="@white">
                <label>@navbarLinkColorActive</label>
                <input type="text" class="span3" placeholder="@navbarLinkColorHover">
                <label>@navbarLinkBackgroundHover</label>
                <input type="text" class="span3" placeholder="transparent">
                <label>@navbarLinkBackgroundActive</label>
                <input type="text" class="span3" placeholder="@navbarBackground">
                <label>@navbarSearchBackground</label>
                <input type="text" class="span3" placeholder="lighten(@navbarBackground, 25%)">
                <label>@navbarSearchBackgroundFocus</label>
                <input type="text" class="span3" placeholder="@white">
                <label>@navbarSearchBorder</label>
                <input type="text" class="span3" placeholder="darken(@navbarSearchBackground, 30%)">
                <label>@navbarSearchPlaceholderColor</label>
                <input type="text" class="span3" placeholder="#ccc">

                <label>@navbarCollapseWidth</label>
                <input type="text" class="span3" placeholder="979px">
                <label>@navbarCollapseDesktopWidth</label>
                <input type="text" class="span3" placeholder="@navbarCollapseWidth + 1">

                <h3>{{_i}}Dropdowns{{/i}}</h3>
                <label>@dropdownBackground</label>
                <input type="text" class="span3" placeholder="@white">
                <label>@dropdownBorder</label>
                <input type="text" class="span3" placeholder="rgba(0,0,0,.2)">
                <label>@dropdownLinkColor</label>
                <input type="text" class="span3" placeholder="@grayDark">
                <label>@dropdownLinkColorHover</label>
                <input type="text" class="span3" placeholder="@white">
                <label>@dropdownLinkBackgroundHover</label>
                <input type="text" class="span3" placeholder="@linkColor">
              </div><!-- /span -->
            </div><!-- /row -->
          </section>

          <section class="download" id="download">
            <div class="page-header">
              <h1>
                {{_i}}4. Download{{/i}}
              </h1>
            </div>
            <div class="download-btn">
              <a class="btn btn-primary" href="#" {{#production}}onclick="_gaq.push(['_trackEvent', 'Customize', 'Download', 'Customize and Download']);"{{/production}}>{{_i}}Customize and Download{{/i}}</a>
              <h4>{{_i}}What's included?{{/i}}</h4>
              <p>{{_i}}Downloads include compiled CSS, compiled and minified CSS, and compiled jQuery plugins, all nicely packed up into a zipball for your convenience.{{/i}}</p>
            </div>
          </section><!-- /download -->
        </form>



      </div>{{! /span9 }}
    </div>{{! row}}

  </div>{{! /.container }}
