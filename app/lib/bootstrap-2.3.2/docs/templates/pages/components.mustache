<!-- Subhead
================================================== -->
<header class="jumbotron subhead" id="overview">
  <div class="container">
    <h1>{{_i}}Components{{/i}}</h1>
    <p class="lead">{{_i}}Dozens of reusable components built to provide navigation, alerts, popovers, and more.{{/i}}</p>
  </div>
</header>


  <div class="container">

    <!-- Docs nav
    ================================================== -->
    <div class="row">
      <div class="span3 bs-docs-sidebar">
        <ul class="nav nav-list bs-docs-sidenav">
          <li><a href="#dropdowns"><i class="icon-chevron-right"></i> {{_i}}Dropdowns{{/i}}</a></li>
          <li><a href="#buttonGroups"><i class="icon-chevron-right"></i> {{_i}}Button groups{{/i}}</a></li>
          <li><a href="#buttonDropdowns"><i class="icon-chevron-right"></i> {{_i}}Button dropdowns{{/i}}</a></li>
          <li><a href="#navs"><i class="icon-chevron-right"></i> {{_i}}Navs{{/i}}</a></li>
          <li><a href="#navbar"><i class="icon-chevron-right"></i> {{_i}}Navbar{{/i}}</a></li>
          <li><a href="#breadcrumbs"><i class="icon-chevron-right"></i> {{_i}}Breadcrumbs{{/i}}</a></li>
          <li><a href="#pagination"><i class="icon-chevron-right"></i> {{_i}}Pagination{{/i}}</a></li>
          <li><a href="#labels-badges"><i class="icon-chevron-right"></i> {{_i}}Labels and badges{{/i}}</a></li>
          <li><a href="#typography"><i class="icon-chevron-right"></i> {{_i}}Typography{{/i}}</a></li>
          <li><a href="#thumbnails"><i class="icon-chevron-right"></i> {{_i}}Thumbnails{{/i}}</a></li>
          <li><a href="#alerts"><i class="icon-chevron-right"></i> {{_i}}Alerts{{/i}}</a></li>
          <li><a href="#progress"><i class="icon-chevron-right"></i> {{_i}}Progress bars{{/i}}</a></li>
          <li><a href="#media"><i class="icon-chevron-right"></i> {{_i}}Media object{{/i}}</a></li>
          <li><a href="#misc"><i class="icon-chevron-right"></i> {{_i}}Misc{{/i}}</a></li>
        </ul>
      </div>
      <div class="span9">



        <!-- Dropdowns
        ================================================== -->
        <section id="dropdowns">
          <div class="page-header">
            <h1>{{_i}}Dropdown menus{{/i}}</h1>
          </div>

          <h2>{{_i}}Example{{/i}}</h2>
          <p>{{_i}}Toggleable, contextual menu for displaying lists of links. Made interactive with the <a href="./javascript.html#dropdowns">dropdown JavaScript plugin</a>.{{/i}}</p>
          <div class="bs-docs-example">
            <div class="dropdown clearfix">
              <ul class="dropdown-menu" role="menu" aria-labelledby="dropdownMenu" style="display: block; position: static; margin-bottom: 5px; *width: 180px;">
                <li><a tabindex="-1" href="#">{{_i}}Action{{/i}}</a></li>
                <li><a tabindex="-1" href="#">{{_i}}Another action{{/i}}</a></li>
                <li><a tabindex="-1" href="#">{{_i}}Something else here{{/i}}</a></li>
                <li class="divider"></li>
                <li><a tabindex="-1" href="#">{{_i}}Separated link{{/i}}</a></li>
              </ul>
            </div>
          </div>{{! /example }}
<pre class="prettyprint linenums">
&lt;ul class="dropdown-menu" role="menu" aria-labelledby="dropdownMenu"&gt;
  &lt;li&gt;&lt;a tabindex="-1" href="#"&gt;{{_i}}Action{{/i}}&lt;/a&gt;&lt;/li&gt;
  &lt;li&gt;&lt;a tabindex="-1" href="#"&gt;{{_i}}Another action{{/i}}&lt;/a&gt;&lt;/li&gt;
  &lt;li&gt;&lt;a tabindex="-1" href="#"&gt;{{_i}}Something else here{{/i}}&lt;/a&gt;&lt;/li&gt;
  &lt;li class="divider"&gt;&lt;/li&gt;
  &lt;li&gt;&lt;a tabindex="-1" href="#"&gt;{{_i}}Separated link{{/i}}&lt;/a&gt;&lt;/li&gt;
&lt;/ul&gt;
</pre>

          <h2>{{_i}}Markup{{/i}}</h2>
          <p>{{_i}}Looking at just the dropdown menu, here's the required HTML. You need to wrap the dropdown's trigger and the dropdown menu within <code>.dropdown</code>, or another element that declares <code>position: relative;</code>. Then just create the menu.{{/i}}</p>

<pre class="prettyprint linenums">
&lt;div class="dropdown"&gt;
  &lt;!-- Link or button to toggle dropdown --&gt;
  &lt;ul class="dropdown-menu" role="menu" aria-labelledby="dLabel"&gt;
    &lt;li&gt;&lt;a tabindex="-1" href="#"&gt;{{_i}}Action{{/i}}&lt;/a&gt;&lt;/li&gt;
    &lt;li&gt;&lt;a tabindex="-1" href="#"&gt;{{_i}}Another action{{/i}}&lt;/a&gt;&lt;/li&gt;
    &lt;li&gt;&lt;a tabindex="-1" href="#"&gt;{{_i}}Something else here{{/i}}&lt;/a&gt;&lt;/li&gt;
    &lt;li class="divider"&gt;&lt;/li&gt;
    &lt;li&gt;&lt;a tabindex="-1" href="#"&gt;{{_i}}Separated link{{/i}}&lt;/a&gt;&lt;/li&gt;
  &lt;/ul&gt;
&lt;/div&gt;
</pre>

          <h2>{{_i}}Options{{/i}}</h2>
          <p>{{_i}}Align menus to the right and add include additional levels of dropdowns.{{/i}}</p>

          <h3>{{_i}}Aligning the menus{{/i}}</h3>
          <p>{{_i}}Add <code>.pull-right</code> to a <code>.dropdown-menu</code> to right align the dropdown menu.{{/i}}</p>
<pre class="prettyprint linenums">
&lt;ul class="dropdown-menu pull-right" role="menu" aria-labelledby="dLabel"&gt;
  ...
&lt;/ul&gt;
</pre>

          <h3>{{_i}}Disabled menu options{{/i}}</h3>
          <p>{{_i}}Add <code>.disabled</code> to a <code>&lt;li&gt;</code> in the dropdown to disable the link.{{/i}}</p>
          <div class="bs-docs-example">
            <div class="dropdown clearfix">
              <ul class="dropdown-menu" role="menu" aria-labelledby="dropdownMenu" style="display: block; position: static; margin-bottom: 5px; *width: 180px;">
                <li><a tabindex="-1" href="#">{{_i}}Regular link{{/i}}</a></li>
                <li class="disabled"><a tabindex="-1" href="#">{{_i}}Disabled link{{/i}}</a></li>
                <li><a tabindex="-1" href="#">{{_i}}Another link{{/i}}</a></li>
              </ul>
            </div>
          </div>{{! /example }}
<pre class="prettyprint linenums">
&lt;ul class="dropdown-menu" role="menu" aria-labelledby="dropdownMenu"&gt;
  &lt;li&gt;&lt;a tabindex="-1" href="#"&gt;{{_i}}Regular link{{/i}}&lt;/a&gt;&lt;/li&gt;
  &lt;li class="disabled"&gt;&lt;a tabindex="-1" href="#"&gt;{{_i}}Disabled link{{/i}}&lt;/a&gt;&lt;/li&gt;
  &lt;li&gt;&lt;a tabindex="-1" href="#"&gt;{{_i}}Another link{{/i}}&lt;/a&gt;&lt;/li&gt;
&lt;/ul&gt;
</pre>

          <h3>{{_i}}Sub menus on dropdowns{{/i}}</h3>
          <p>{{_i}}Add an extra level of dropdown menus, appearing on hover like those of OS X, with some simple markup additions. Add <code>.dropdown-submenu</code> to any <code>li</code> in an existing dropdown menu for automatic styling.{{/i}}</p>
          <div class="bs-docs-example bs-docs-example-submenus">

            <div class="pull-left">
              <p class="muted">Default</p>
              <div class="dropdown clearfix">
                <ul class="dropdown-menu" role="menu" aria-labelledby="dropdownMenu">
                  <li><a tabindex="-1" href="#">{{_i}}Action{{/i}}</a></li>
                  <li><a tabindex="-1" href="#">{{_i}}Another action{{/i}}</a></li>
                  <li><a tabindex="-1" href="#">{{_i}}Something else here{{/i}}</a></li>
                  <li class="divider"></li>
                  <li class="dropdown-submenu">
                    <a tabindex="-1" href="#">{{_i}}More options{{/i}}</a>
                    <ul class="dropdown-menu">
                      <li><a tabindex="-1" href="#">{{_i}}Second level link{{/i}}</a></li>
                      <li><a tabindex="-1" href="#">{{_i}}Second level link{{/i}}</a></li>
                      <li><a tabindex="-1" href="#">{{_i}}Second level link{{/i}}</a></li>
                      <li><a tabindex="-1" href="#">{{_i}}Second level link{{/i}}</a></li>
                      <li><a tabindex="-1" href="#">{{_i}}Second level link{{/i}}</a></li>
                    </ul>
                  </li>
                </ul>
              </div>
            </div>{{! /.pull-left }}

            <div class="pull-left">
              <p class="muted">Dropup</p>
              <div class="dropup">
                <ul class="dropdown-menu" role="menu" aria-labelledby="dropdownMenu">
                  <li><a tabindex="-1" href="#">{{_i}}Action{{/i}}</a></li>
                  <li><a tabindex="-1" href="#">{{_i}}Another action{{/i}}</a></li>
                  <li><a tabindex="-1" href="#">{{_i}}Something else here{{/i}}</a></li>
                  <li class="divider"></li>
                  <li class="dropdown-submenu">
                    <a tabindex="-1" href="#">{{_i}}More options{{/i}}</a>
                    <ul class="dropdown-menu">
                      <li><a tabindex="-1" href="#">{{_i}}Second level link{{/i}}</a></li>
                      <li><a tabindex="-1" href="#">{{_i}}Second level link{{/i}}</a></li>
                      <li><a tabindex="-1" href="#">{{_i}}Second level link{{/i}}</a></li>
                      <li><a tabindex="-1" href="#">{{_i}}Second level link{{/i}}</a></li>
                      <li><a tabindex="-1" href="#">{{_i}}Second level link{{/i}}</a></li>
                    </ul>
                  </li>
                </ul>
              </div>
            </div>{{! /.pull-left }}

            <div class="pull-left">
              <p class="muted">Left submenu</p>
              <div class="dropdown">
                <ul class="dropdown-menu" role="menu" aria-labelledby="dropdownMenu">
                  <li><a tabindex="-1" href="#">{{_i}}Action{{/i}}</a></li>
                  <li><a tabindex="-1" href="#">{{_i}}Another action{{/i}}</a></li>
                  <li><a tabindex="-1" href="#">{{_i}}Something else here{{/i}}</a></li>
                  <li class="divider"></li>
                  <li class="dropdown-submenu pull-left">
                    <a tabindex="-1" href="#">{{_i}}More options{{/i}}</a>
                    <ul class="dropdown-menu">
                      <li><a tabindex="-1" href="#">{{_i}}Second level link{{/i}}</a></li>
                      <li><a tabindex="-1" href="#">{{_i}}Second level link{{/i}}</a></li>
                      <li><a tabindex="-1" href="#">{{_i}}Second level link{{/i}}</a></li>
                      <li><a tabindex="-1" href="#">{{_i}}Second level link{{/i}}</a></li>
                      <li><a tabindex="-1" href="#">{{_i}}Second level link{{/i}}</a></li>
                    </ul>
                  </li>
                </ul>
              </div>
            </div>{{! /.pull-left }}

          </div>{{! /example }}
<pre class="prettyprint linenums">
&lt;ul class="dropdown-menu" role="menu" aria-labelledby="dLabel"&gt;
  ...
  &lt;li class="dropdown-submenu"&gt;
    &lt;a tabindex="-1" href="#"&gt;{{_i}}More options{{/i}}&lt;/a&gt;
    &lt;ul class="dropdown-menu"&gt;
      ...
    &lt;/ul&gt;
  &lt;/li&gt;
&lt;/ul&gt;
</pre>

        </section>




        <!-- Button Groups
        ================================================== -->
        <section id="buttonGroups">
          <div class="page-header">
            <h1>{{_i}}Button groups{{/i}}</h1>
          </div>

          <h2>{{_i}}Examples{{/i}}</h2>
          <p>{{_i}}Two basic options, along with two more specific variations.{{/i}}</p>

          <h3>{{_i}}Single button group{{/i}}</h3>
          <p>{{_i}}Wrap a series of buttons with <code>.btn</code> in <code>.btn-group</code>.{{/i}}</p>
          <div class="bs-docs-example">
            <div class="btn-group" style="margin: 9px 0 5px;">
              <button class="btn">{{_i}}Left{{/i}}</button>
              <button class="btn">{{_i}}Middle{{/i}}</button>
              <button class="btn">{{_i}}Right{{/i}}</button>
            </div>
          </div>
<pre class="prettyprint linenums">
&lt;div class="btn-group"&gt;
  &lt;button class="btn"&gt;Left&lt;/button&gt;
  &lt;button class="btn"&gt;Middle&lt;/button&gt;
  &lt;button class="btn"&gt;Right&lt;/button&gt;
&lt;/div&gt;
</pre>

          <h3>{{_i}}Multiple button groups{{/i}}</h3>
          <p>{{_i}}Combine sets of <code>&lt;div class="btn-group"&gt;</code> into a <code>&lt;div class="btn-toolbar"&gt;</code> for more complex components.{{/i}}</p>
          <div class="bs-docs-example">
            <div class="btn-toolbar" style="margin: 0;">
              <div class="btn-group">
                <button class="btn">1</button>
                <button class="btn">2</button>
                <button class="btn">3</button>
                <button class="btn">4</button>
              </div>
              <div class="btn-group">
                <button class="btn">5</button>
                <button class="btn">6</button>
                <button class="btn">7</button>
              </div>
              <div class="btn-group">
                <button class="btn">8</button>
              </div>
            </div>
          </div>
<pre class="prettyprint linenums">
&lt;div class="btn-toolbar"&gt;
  &lt;div class="btn-group"&gt;
    ...
  &lt;/div&gt;
&lt;/div&gt;
</pre>

          <h3>{{_i}}Vertical button groups{{/i}}</h3>
          <p>{{_i}}Make a set of buttons appear vertically stacked rather than horizontally.{{/i}}</p>
          <div class="bs-docs-example">
            <div class="btn-group btn-group-vertical">
              <button type="button" class="btn"><i class="icon-align-left"></i></button>
              <button type="button" class="btn"><i class="icon-align-center"></i></button>
              <button type="button" class="btn"><i class="icon-align-right"></i></button>
              <button type="button" class="btn"><i class="icon-align-justify"></i></button>
            </div>
          </div>
<pre class="prettyprint linenums">
&lt;div class="btn-group btn-group-vertical"&gt;
  ...
&lt;/div&gt;
</pre>


          <hr class="bs-docs-separator">


          <h4>{{_i}}Checkbox and radio flavors{{/i}}</h4>
          <p>{{_i}}Button groups can also function as radios, where only one button may be active, or checkboxes, where any number of buttons may be active. View <a href="./javascript.html#buttons">the JavaScript docs</a> for that.{{/i}}</p>

          <h4>{{_i}}Dropdowns in button groups{{/i}}</h4>
          <p><span class="label label-info">{{_i}}Heads up!{{/i}}</span> {{_i}}Buttons with dropdowns must be individually wrapped in their own <code>.btn-group</code> within a <code>.btn-toolbar</code> for proper rendering.{{/i}}</p>
        </section>



        <!-- Split button dropdowns
        ================================================== -->
        <section id="buttonDropdowns">
          <div class="page-header">
            <h1>{{_i}}Button dropdown menus{{/i}}</h1>
          </div>


          <h2>{{_i}}Overview and examples{{/i}}</h2>
          <p>{{_i}}Use any button to trigger a dropdown menu by placing it within a <code>.btn-group</code> and providing the proper menu markup.{{/i}}</p>
          <div class="bs-docs-example">
            <div class="btn-toolbar" style="margin: 0;">
              <div class="btn-group">
                <button class="btn dropdown-toggle" data-toggle="dropdown">{{_i}}Action{{/i}} <span class="caret"></span></button>
                <ul class="dropdown-menu">
                  <li><a href="#">{{_i}}Action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Another action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Something else here{{/i}}</a></li>
                  <li class="divider"></li>
                  <li><a href="#">{{_i}}Separated link{{/i}}</a></li>
                </ul>
              </div><!-- /btn-group -->
              <div class="btn-group">
                <button class="btn btn-primary dropdown-toggle" data-toggle="dropdown">{{_i}}Action{{/i}} <span class="caret"></span></button>
                <ul class="dropdown-menu">
                  <li><a href="#">{{_i}}Action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Another action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Something else here{{/i}}</a></li>
                  <li class="divider"></li>
                  <li><a href="#">{{_i}}Separated link{{/i}}</a></li>
                </ul>
              </div><!-- /btn-group -->
              <div class="btn-group">
                <button class="btn btn-danger dropdown-toggle" data-toggle="dropdown">{{_i}}Danger{{/i}} <span class="caret"></span></button>
                <ul class="dropdown-menu">
                  <li><a href="#">{{_i}}Action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Another action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Something else here{{/i}}</a></li>
                  <li class="divider"></li>
                  <li><a href="#">{{_i}}Separated link{{/i}}</a></li>
                </ul>
              </div><!-- /btn-group -->
              <div class="btn-group">
                <button class="btn btn-warning dropdown-toggle" data-toggle="dropdown">{{_i}}Warning{{/i}} <span class="caret"></span></button>
                <ul class="dropdown-menu">
                  <li><a href="#">{{_i}}Action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Another action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Something else here{{/i}}</a></li>
                  <li class="divider"></li>
                  <li><a href="#">{{_i}}Separated link{{/i}}</a></li>
                </ul>
              </div><!-- /btn-group -->
              <div class="btn-group">
                <button class="btn btn-success dropdown-toggle" data-toggle="dropdown">{{_i}}Success{{/i}} <span class="caret"></span></button>
                <ul class="dropdown-menu">
                  <li><a href="#">{{_i}}Action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Another action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Something else here{{/i}}</a></li>
                  <li class="divider"></li>
                  <li><a href="#">{{_i}}Separated link{{/i}}</a></li>
                </ul>
              </div><!-- /btn-group -->
              <div class="btn-group">
                <button class="btn btn-info dropdown-toggle" data-toggle="dropdown">{{_i}}Info{{/i}} <span class="caret"></span></button>
                <ul class="dropdown-menu">
                  <li><a href="#">{{_i}}Action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Another action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Something else here{{/i}}</a></li>
                  <li class="divider"></li>
                  <li><a href="#">{{_i}}Separated link{{/i}}</a></li>
                </ul>
              </div><!-- /btn-group -->
              <div class="btn-group">
                <button class="btn btn-inverse dropdown-toggle" data-toggle="dropdown">{{_i}}Inverse{{/i}} <span class="caret"></span></button>
                <ul class="dropdown-menu">
                  <li><a href="#">{{_i}}Action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Another action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Something else here{{/i}}</a></li>
                  <li class="divider"></li>
                  <li><a href="#">{{_i}}Separated link{{/i}}</a></li>
                </ul>
              </div><!-- /btn-group -->
            </div><!-- /btn-toolbar -->
          </div>{{! /example }}
<pre class="prettyprint linenums">
&lt;div class="btn-group"&gt;
  &lt;a class="btn dropdown-toggle" data-toggle="dropdown" href="#"&gt;
    {{_i}}Action{{/i}}
    &lt;span class="caret"&gt;&lt;/span&gt;
  &lt;/a&gt;
  &lt;ul class="dropdown-menu"&gt;
    &lt;!-- {{_i}}dropdown menu links{{/i}} --&gt;
  &lt;/ul&gt;
&lt;/div&gt;
</pre>

          <h3>{{_i}}Works with all button sizes{{/i}}</h3>
          <p>{{_i}}Button dropdowns work at any size:  <code>.btn-large</code>, <code>.btn-small</code>, or <code>.btn-mini</code>.{{/i}}</p>
          <div class="bs-docs-example">
            <div class="btn-toolbar" style="margin: 0;">
              <div class="btn-group">
                <button class="btn btn-large dropdown-toggle" data-toggle="dropdown">{{_i}}Large button{{/i}} <span class="caret"></span></button>
                <ul class="dropdown-menu">
                  <li><a href="#">{{_i}}Action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Another action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Something else here{{/i}}</a></li>
                  <li class="divider"></li>
                  <li><a href="#">{{_i}}Separated link{{/i}}</a></li>
                </ul>
              </div><!-- /btn-group -->
              <div class="btn-group">
                <button class="btn btn-small dropdown-toggle" data-toggle="dropdown">{{_i}}Small button{{/i}} <span class="caret"></span></button>
                <ul class="dropdown-menu">
                  <li><a href="#">{{_i}}Action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Another action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Something else here{{/i}}</a></li>
                  <li class="divider"></li>
                  <li><a href="#">{{_i}}Separated link{{/i}}</a></li>
                </ul>
              </div><!-- /btn-group -->
              <div class="btn-group">
                <button class="btn btn-mini dropdown-toggle" data-toggle="dropdown">{{_i}}Mini button{{/i}} <span class="caret"></span></button>
                <ul class="dropdown-menu">
                  <li><a href="#">{{_i}}Action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Another action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Something else here{{/i}}</a></li>
                  <li class="divider"></li>
                  <li><a href="#">{{_i}}Separated link{{/i}}</a></li>
                </ul>
              </div><!-- /btn-group -->
            </div><!-- /btn-toolbar -->
          </div>{{! /example }}

          <h3>{{_i}}Requires JavaScript{{/i}}</h3>
          <p>{{_i}}Button dropdowns require the <a href="./javascript.html#dropdowns">Bootstrap dropdown plugin</a> to function.{{/i}}</p>
          <p>{{_i}}In some cases&mdash;like mobile&mdash;dropdown menus will extend outside the viewport. You need to resolve the alignment manually or with custom JavaScript.{{/i}}</p>


          <hr class="bs-docs-separator">


          <h2>{{_i}}Split button dropdowns{{/i}}</h2>
          <p>{{_i}}Building on the button group styles and markup, we can easily create a split button. Split buttons feature a standard action on the left and a dropdown toggle on the right with contextual links.{{/i}}</p>
          <div class="bs-docs-example">
            <div class="btn-toolbar" style="margin: 0;">
              <div class="btn-group">
                <button class="btn">{{_i}}Action{{/i}}</button>
                <button class="btn dropdown-toggle" data-toggle="dropdown"><span class="caret"></span></button>
                <ul class="dropdown-menu">
                  <li><a href="#">{{_i}}Action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Another action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Something else here{{/i}}</a></li>
                  <li class="divider"></li>
                  <li><a href="#">{{_i}}Separated link{{/i}}</a></li>
                </ul>
              </div><!-- /btn-group -->
              <div class="btn-group">
                <button class="btn btn-primary">{{_i}}Action{{/i}}</button>
                <button class="btn btn-primary dropdown-toggle" data-toggle="dropdown"><span class="caret"></span></button>
                <ul class="dropdown-menu">
                  <li><a href="#">{{_i}}Action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Another action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Something else here{{/i}}</a></li>
                  <li class="divider"></li>
                  <li><a href="#">{{_i}}Separated link{{/i}}</a></li>
                </ul>
              </div><!-- /btn-group -->
              <div class="btn-group">
                <button class="btn btn-danger">{{_i}}Danger{{/i}}</button>
                <button class="btn btn-danger dropdown-toggle" data-toggle="dropdown"><span class="caret"></span></button>
                <ul class="dropdown-menu">
                  <li><a href="#">{{_i}}Action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Another action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Something else here{{/i}}</a></li>
                  <li class="divider"></li>
                  <li><a href="#">{{_i}}Separated link{{/i}}</a></li>
                </ul>
              </div><!-- /btn-group -->
              <div class="btn-group">
                <button class="btn btn-warning">{{_i}}Warning{{/i}}</button>
                <button class="btn btn-warning dropdown-toggle" data-toggle="dropdown"><span class="caret"></span></button>
                <ul class="dropdown-menu">
                  <li><a href="#">{{_i}}Action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Another action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Something else here{{/i}}</a></li>
                  <li class="divider"></li>
                  <li><a href="#">{{_i}}Separated link{{/i}}</a></li>
                </ul>
              </div><!-- /btn-group -->
              <div class="btn-group">
                <button class="btn btn-success">{{_i}}Success{{/i}}</button>
                <button class="btn btn-success dropdown-toggle" data-toggle="dropdown"><span class="caret"></span></button>
                <ul class="dropdown-menu">
                  <li><a href="#">{{_i}}Action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Another action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Something else here{{/i}}</a></li>
                  <li class="divider"></li>
                  <li><a href="#">{{_i}}Separated link{{/i}}</a></li>
                </ul>
              </div><!-- /btn-group -->
              <div class="btn-group">
                <button class="btn btn-info">{{_i}}Info{{/i}}</button>
                <button class="btn btn-info dropdown-toggle" data-toggle="dropdown"><span class="caret"></span></button>
                <ul class="dropdown-menu">
                  <li><a href="#">{{_i}}Action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Another action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Something else here{{/i}}</a></li>
                  <li class="divider"></li>
                  <li><a href="#">{{_i}}Separated link{{/i}}</a></li>
                </ul>
              </div><!-- /btn-group -->
              <div class="btn-group">
                <button class="btn btn-inverse">{{_i}}Inverse{{/i}}</button>
                <button class="btn btn-inverse dropdown-toggle" data-toggle="dropdown"><span class="caret"></span></button>
                <ul class="dropdown-menu">
                  <li><a href="#">{{_i}}Action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Another action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Something else here{{/i}}</a></li>
                  <li class="divider"></li>
                  <li><a href="#">{{_i}}Separated link{{/i}}</a></li>
                </ul>
              </div><!-- /btn-group -->
            </div><!-- /btn-toolbar -->
          </div>{{! /example }}
<pre class="prettyprint linenums">
&lt;div class="btn-group"&gt;
  &lt;button class="btn"&gt;{{_i}}Action{{/i}}&lt;/button&gt;
  &lt;button class="btn dropdown-toggle" data-toggle="dropdown"&gt;
    &lt;span class="caret"&gt;&lt;/span&gt;
  &lt;/button&gt;
  &lt;ul class="dropdown-menu"&gt;
    &lt;!-- {{_i}}dropdown menu links{{/i}} --&gt;
  &lt;/ul&gt;
&lt;/div&gt;
</pre>

          <h3>{{_i}}Sizes{{/i}}</h3>
          <p>{{_i}}Utilize the extra button classes <code>.btn-mini</code>, <code>.btn-small</code>, or <code>.btn-large</code> for sizing.{{/i}}</p>
          <div class="bs-docs-example">
            <div class="btn-toolbar">
              <div class="btn-group">
                <button class="btn btn-large">{{_i}}Large action{{/i}}</button>
                <button class="btn btn-large dropdown-toggle" data-toggle="dropdown"><span class="caret"></span></button>
                <ul class="dropdown-menu">
                  <li><a href="#">{{_i}}Action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Another action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Something else here{{/i}}</a></li>
                  <li class="divider"></li>
                  <li><a href="#">{{_i}}Separated link{{/i}}</a></li>
                </ul>
              </div><!-- /btn-group -->
            </div><!-- /btn-toolbar -->
            <div class="btn-toolbar">
              <div class="btn-group">
                <button class="btn btn-small">{{_i}}Small action{{/i}}</button>
                <button class="btn btn-small dropdown-toggle" data-toggle="dropdown"><span class="caret"></span></button>
                <ul class="dropdown-menu">
                  <li><a href="#">{{_i}}Action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Another action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Something else here{{/i}}</a></li>
                  <li class="divider"></li>
                  <li><a href="#">{{_i}}Separated link{{/i}}</a></li>
                </ul>
              </div><!-- /btn-group -->
            </div><!-- /btn-toolbar -->
            <div class="btn-toolbar">
              <div class="btn-group">
                <button class="btn btn-mini">{{_i}}Mini action{{/i}}</button>
                <button class="btn btn-mini dropdown-toggle" data-toggle="dropdown"><span class="caret"></span></button>
                <ul class="dropdown-menu">
                  <li><a href="#">{{_i}}Action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Another action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Something else here{{/i}}</a></li>
                  <li class="divider"></li>
                  <li><a href="#">{{_i}}Separated link{{/i}}</a></li>
                </ul>
              </div><!-- /btn-group -->
            </div><!-- /btn-toolbar -->
          </div>{{! /example }}
<pre class="prettyprint linenums">
&lt;div class="btn-group"&gt;
  &lt;button class="btn btn-mini"&gt;{{_i}}Action{{/i}}&lt;/button&gt;
  &lt;button class="btn btn-mini dropdown-toggle" data-toggle="dropdown"&gt;
    &lt;span class="caret"&gt;&lt;/span&gt;
  &lt;/button&gt;
  &lt;ul class="dropdown-menu"&gt;
    &lt;!-- {{_i}}dropdown menu links{{/i}} --&gt;
  &lt;/ul&gt;
&lt;/div&gt;
</pre>

          <h3>{{_i}}Dropup menus{{/i}}</h3>
          <p>{{_i}}Dropdown menus can also be toggled from the bottom up by adding a single class to the immediate parent of <code>.dropdown-menu</code>. It will flip the direction of the <code>.caret</code> and reposition the menu itself to move from the bottom up instead of top down.{{/i}}</p>
          <div class="bs-docs-example">
            <div class="btn-toolbar" style="margin: 0;">
              <div class="btn-group dropup">
                <button class="btn">{{_i}}Dropup{{/i}}</button>
                <button class="btn dropdown-toggle" data-toggle="dropdown"><span class="caret"></span></button>
                <ul class="dropdown-menu">
                  <li><a href="#">{{_i}}Action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Another action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Something else here{{/i}}</a></li>
                  <li class="divider"></li>
                  <li><a href="#">{{_i}}Separated link{{/i}}</a></li>
                </ul>
              </div><!-- /btn-group -->
              <div class="btn-group dropup">
                <button class="btn primary">{{_i}}Right dropup{{/i}}</button>
                <button class="btn primary dropdown-toggle" data-toggle="dropdown"><span class="caret"></span></button>
                <ul class="dropdown-menu pull-right">
                  <li><a href="#">{{_i}}Action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Another action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Something else here{{/i}}</a></li>
                  <li class="divider"></li>
                  <li><a href="#">{{_i}}Separated link{{/i}}</a></li>
                </ul>
              </div><!-- /btn-group -->
            </div>
          </div>{{! /example }}
<pre class="prettyprint linenums">
&lt;div class="btn-group dropup"&gt;
  &lt;button class="btn"&gt;{{_i}}Dropup{{/i}}&lt;/button&gt;
  &lt;button class="btn dropdown-toggle" data-toggle="dropdown"&gt;
    &lt;span class="caret"&gt;&lt;/span&gt;
  &lt;/button&gt;
  &lt;ul class="dropdown-menu"&gt;
    &lt;!-- {{_i}}dropdown menu links{{/i}} --&gt;
  &lt;/ul&gt;
&lt;/div&gt;
</pre>

        </section>



        <!-- Nav, Tabs, & Pills
        ================================================== -->
        <section id="navs">
          <div class="page-header">
            <h1>{{_i}}Nav: tabs, pills, and lists{{/i}}</small></h1>
          </div>

          <h2>{{_i}}Lightweight defaults{{/i}} <small>{{_i}}Same markup, different classes{{/i}}</small></h2>
          <p>{{_i}}All nav components here&mdash;tabs, pills, and lists&mdash;<strong>share the same base markup and styles</strong> through the <code>.nav</code> class.{{/i}}</p>

          <h3>{{_i}}Basic tabs{{/i}}</h3>
          <p>{{_i}}Take a regular <code>&lt;ul&gt;</code> of links and add <code>.nav-tabs</code>:{{/i}}</p>
          <div class="bs-docs-example">
            <ul class="nav nav-tabs">
              <li class="active"><a href="#">{{_i}}Home{{/i}}</a></li>
              <li><a href="#">{{_i}}Profile{{/i}}</a></li>
              <li><a href="#">{{_i}}Messages{{/i}}</a></li>
            </ul>
          </div>
<pre class="prettyprint linenums">
&lt;ul class="nav nav-tabs"&gt;
  &lt;li class="active"&gt;
    &lt;a href="#"&gt;{{_i}}Home{{/i}}&lt;/a&gt;
  &lt;/li&gt;
  &lt;li&gt;&lt;a href="#"&gt;...&lt;/a&gt;&lt;/li&gt;
  &lt;li&gt;&lt;a href="#"&gt;...&lt;/a&gt;&lt;/li&gt;
&lt;/ul&gt;
</pre>

          <h3>{{_i}}Basic pills{{/i}}</h3>
          <p>{{_i}}Take that same HTML, but use <code>.nav-pills</code> instead:{{/i}}</p>
          <div class="bs-docs-example">
            <ul class="nav nav-pills">
              <li class="active"><a href="#">{{_i}}Home{{/i}}</a></li>
              <li><a href="#">{{_i}}Profile{{/i}}</a></li>
              <li><a href="#">{{_i}}Messages{{/i}}</a></li>
            </ul>
          </div>
<pre class="prettyprint linenums">
&lt;ul class="nav nav-pills"&gt;
  &lt;li class="active"&gt;
    &lt;a href="#"&gt;{{_i}}Home{{/i}}&lt;/a&gt;
  &lt;/li&gt;
  &lt;li&gt;&lt;a href="#"&gt;...&lt;/a&gt;&lt;/li&gt;
  &lt;li&gt;&lt;a href="#"&gt;...&lt;/a&gt;&lt;/li&gt;
&lt;/ul&gt;
</pre>

          <h3>{{_i}}Disabled state{{/i}}</h3>
          <p>{{_i}}For any nav component (tabs, pills, or list), add <code>.disabled</code> for <strong>gray links and no hover effects</strong>. Links will remain clickable, however, unless you remove the <code>href</code> attribute. Alternatively, you could implement custom JavaScript to prevent those clicks.{{/i}}</p>
          <div class="bs-docs-example">
            <ul class="nav nav-pills">
              <li><a href="#">{{_i}}Clickable link{{/i}}</a></li>
              <li><a href="#">{{_i}}Clickable link{{/i}}</a></li>
              <li class="disabled"><a href="#">{{_i}}Disabled link{{/i}}</a></li>
            </ul>
          </div>
<pre class="prettyprint linenums">
&lt;ul class="nav nav-pills"&gt;
  ...
  &lt;li class="disabled"&gt;&lt;a href="#"&gt;{{_i}}Home{{/i}}&lt;/a&gt;&lt;/li&gt;
  ...
&lt;/ul&gt;
</pre>

          <h3>{{_i}}Component alignment{{/i}}</h3>
          <p>{{_i}}To align nav links, use the <code>.pull-left</code> or <code>.pull-right</code> utility classes. Both classes will add a CSS float in the specified direction.{{/i}}</p>


          <hr class="bs-docs-separator">


          <h2>{{_i}}Stackable{{/i}}</h2>
          <p>{{_i}}As tabs and pills are horizontal by default, just add a second class, <code>.nav-stacked</code>, to make them appear vertically stacked.{{/i}}</p>

          <h3>{{_i}}Stacked tabs{{/i}}</h3>
          <div class="bs-docs-example">
            <ul class="nav nav-tabs nav-stacked">
              <li class="active"><a href="#">{{_i}}Home{{/i}}</a></li>
              <li><a href="#">{{_i}}Profile{{/i}}</a></li>
              <li><a href="#">{{_i}}Messages{{/i}}</a></li>
            </ul>
          </div>
<pre class="prettyprint linenums">
&lt;ul class="nav nav-tabs nav-stacked"&gt;
  ...
&lt;/ul&gt;
</pre>

          <h3>{{_i}}Stacked pills{{/i}}</h3>
          <div class="bs-docs-example">
            <ul class="nav nav-pills nav-stacked">
              <li class="active"><a href="#">{{_i}}Home{{/i}}</a></li>
              <li><a href="#">{{_i}}Profile{{/i}}</a></li>
              <li><a href="#">{{_i}}Messages{{/i}}</a></li>
            </ul>
          </div>
<pre class="prettyprint linenums">
&lt;ul class="nav nav-pills nav-stacked"&gt;
  ...
&lt;/ul&gt;
</pre>


          <hr class="bs-docs-separator">


          <h2>{{_i}}Dropdowns{{/i}}</h2>
          <p>{{_i}}Add dropdown menus with a little extra HTML and the <a href="./javascript.html#dropdowns">dropdowns JavaScript plugin</a>.{{/i}}</p>

          <h3>{{_i}}Tabs with dropdowns{{/i}}</h3>
          <div class="bs-docs-example">
            <ul class="nav nav-tabs">
              <li class="active"><a href="#">{{_i}}Home{{/i}}</a></li>
              <li><a href="#">{{_i}}Help{{/i}}</a></li>
              <li class="dropdown">
                <a class="dropdown-toggle" data-toggle="dropdown" href="#">{{_i}}Dropdown{{/i}} <b class="caret"></b></a>
                <ul class="dropdown-menu">
                  <li><a href="#">{{_i}}Action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Another action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Something else here{{/i}}</a></li>
                  <li class="divider"></li>
                  <li><a href="#">{{_i}}Separated link{{/i}}</a></li>
                </ul>
              </li>
            </ul>
          </div>
<pre class="prettyprint linenums">
&lt;ul class="nav nav-tabs"&gt;
  &lt;li class="dropdown"&gt;
    &lt;a class="dropdown-toggle"
       data-toggle="dropdown"
       href="#"&gt;
        {{_i}}Dropdown{{/i}}
        &lt;b class="caret"&gt;&lt;/b&gt;
      &lt;/a&gt;
    &lt;ul class="dropdown-menu"&gt;
      &lt;!-- {{_i}}links{{/i}} --&gt;
    &lt;/ul&gt;
  &lt;/li&gt;
&lt;/ul&gt;
</pre>

          <h3>{{_i}}Pills with dropdowns{{/i}}</h3>
          <div class="bs-docs-example">
            <ul class="nav nav-pills">
              <li class="active"><a href="#">{{_i}}Home{{/i}}</a></li>
              <li><a href="#">{{_i}}Help{{/i}}</a></li>
              <li class="dropdown">
                <a class="dropdown-toggle" data-toggle="dropdown" href="#">{{_i}}Dropdown{{/i}} <b class="caret"></b></a>
                <ul class="dropdown-menu">
                  <li><a href="#">{{_i}}Action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Another action{{/i}}</a></li>
                  <li><a href="#">{{_i}}Something else here{{/i}}</a></li>
                  <li class="divider"></li>
                  <li><a href="#">{{_i}}Separated link{{/i}}</a></li>
                </ul>
              </li>
            </ul>
          </div>{{! /example }}
<pre class="prettyprint linenums">
&lt;ul class="nav nav-pills"&gt;
  &lt;li class="dropdown"&gt;
    &lt;a class="dropdown-toggle"
       data-toggle="dropdown"
       href="#"&gt;
        {{_i}}Dropdown{{/i}}
        &lt;b class="caret"&gt;&lt;/b&gt;
      &lt;/a&gt;
    &lt;ul class="dropdown-menu"&gt;
      &lt;!-- {{_i}}links{{/i}} --&gt;
    &lt;/ul&gt;
  &lt;/li&gt;
&lt;/ul&gt;
</pre>


          <hr class="bs-docs-separator">


          <h2>{{_i}}Nav lists{{/i}}</h2>
          <p>{{_i}}A simple and easy way to build groups of nav links with optional headers. They're best used in sidebars like the Finder in OS X.{{/i}}</p>

          <h3>{{_i}}Example nav list{{/i}}</h3>
          <p>{{_i}}Take a list of links and add <code>class="nav nav-list"</code>:{{/i}}</p>
          <div class="bs-docs-example">
            <div class="well" style="max-width: 340px; padding: 8px 0;">
              <ul class="nav nav-list">
                <li class="nav-header">{{_i}}List header{{/i}}</li>
                <li class="active"><a href="#">{{_i}}Home{{/i}}</a></li>
                <li><a href="#">{{_i}}Library{{/i}}</a></li>
                <li><a href="#">{{_i}}Applications{{/i}}</a></li>
                <li class="nav-header">{{_i}}Another list header{{/i}}</li>
                <li><a href="#">{{_i}}Profile{{/i}}</a></li>
                <li><a href="#">{{_i}}Settings{{/i}}</a></li>
                <li class="divider"></li>
                <li><a href="#">{{_i}}Help{{/i}}</a></li>
              </ul>
            </div> <!-- /well -->
          </div>{{! /example }}
<pre class="prettyprint linenums">
&lt;ul class="nav nav-list"&gt;
  &lt;li class="nav-header"&gt;{{_i}}List header{{/i}}&lt;/li&gt;
  &lt;li class="active"&gt;&lt;a href="#"&gt;{{_i}}Home{{/i}}&lt;/a&gt;&lt;/li&gt;
  &lt;li&gt;&lt;a href="#"&gt;{{_i}}Library{{/i}}&lt;/a&gt;&lt;/li&gt;
  ...
&lt;/ul&gt;
</pre>
          <p>
            <span class="label label-info">{{_i}}Note{{/i}}</span>
            {{_i}}For nesting within a nav list, include <code>class="nav nav-list"</code> on any nested <code>&lt;ul&gt;</code>.{{/i}}
          </p>

          <h3>{{_i}}Horizontal dividers{{/i}}</h3>
          <p>{{_i}}Add a horizontal divider by creating an empty list item with the class <code>.divider</code>, like so:{{/i}}</p>
<pre class="prettyprint linenums">
&lt;ul class="nav nav-list"&gt;
  ...
  &lt;li class="divider"&gt;&lt;/li&gt;
  ...
&lt;/ul&gt;
</pre>


          <hr class="bs-docs-separator">


          <h2>{{_i}}Tabbable nav{{/i}}</h2>
          <p>{{_i}}Bring your tabs to life with a simple plugin to toggle between content via tabs. Bootstrap integrates tabbable tabs in four styles: top (default), right, bottom, and left.{{/i}}</p>

          <h3>{{_i}}Tabbable example{{/i}}</h3>
          <p>{{_i}}To make tabs tabbable, create a <code>.tab-pane</code> with unique ID for every tab and wrap them in <code>.tab-content</code>.{{/i}}</p>
          <div class="bs-docs-example">
            <div class="tabbable" style="margin-bottom: 18px;">
              <ul class="nav nav-tabs">
                <li class="active"><a href="#tab1" data-toggle="tab">{{_i}}Section 1{{/i}}</a></li>
                <li><a href="#tab2" data-toggle="tab">{{_i}}Section 2{{/i}}</a></li>
                <li><a href="#tab3" data-toggle="tab">{{_i}}Section 3{{/i}}</a></li>
              </ul>
              <div class="tab-content" style="padding-bottom: 9px; border-bottom: 1px solid #ddd;">
                <div class="tab-pane active" id="tab1">
                  <p>{{_i}}I'm in Section 1.{{/i}}</p>
                </div>
                <div class="tab-pane" id="tab2">
                  <p>{{_i}}Howdy, I'm in Section 2.{{/i}}</p>
                </div>
                <div class="tab-pane" id="tab3">
                  <p>{{_i}}What up girl, this is Section 3.{{/i}}</p>
                </div>
              </div>
            </div> <!-- /tabbable -->
          </div>{{! /example }}
<pre class="prettyprint linenums">
&lt;div class="tabbable"&gt; &lt;!-- Only required for left/right tabs --&gt;
  &lt;ul class="nav nav-tabs"&gt;
    &lt;li class="active"&gt;&lt;a href="#tab1" data-toggle="tab"&gt;{{_i}}Section 1{{/i}}&lt;/a&gt;&lt;/li&gt;
    &lt;li&gt;&lt;a href="#tab2" data-toggle="tab"&gt;{{_i}}Section 2{{/i}}&lt;/a&gt;&lt;/li&gt;
  &lt;/ul&gt;
  &lt;div class="tab-content"&gt;
    &lt;div class="tab-pane active" id="tab1"&gt;
      &lt;p&gt;{{_i}}I'm in Section 1.{{/i}}&lt;/p&gt;
    &lt;/div&gt;
    &lt;div class="tab-pane" id="tab2"&gt;
      &lt;p&gt;{{_i}}Howdy, I'm in Section 2.{{/i}}&lt;/p&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/div&gt;
</pre>

          <h4>{{_i}}Fade in tabs{{/i}}</h4>
          <p>{{_i}}To make tabs fade in, add <code>.fade</code> to each <code>.tab-pane</code>.{{/i}}</p>

          <h4>{{_i}}Requires jQuery plugin{{/i}}</h4>
          <p>{{_i}}All tabbable tabs are powered by our lightweight jQuery plugin. Read more about how to bring tabbable tabs to life <a href="./javascript.html#tabs">on the JavaScript docs page</a>.{{/i}}</p>

          <h3>{{_i}}Tabbable in any direction{{/i}}</h3>

          <h4>{{_i}}Tabs on the bottom{{/i}}</h4>
          <p>{{_i}}Flip the order of the HTML and add a class to put tabs on the bottom.{{/i}}</p>
          <div class="bs-docs-example">
            <div class="tabbable tabs-below">
              <div class="tab-content">
                <div class="tab-pane active" id="A">
                  <p>{{_i}}I'm in Section A.{{/i}}</p>
                </div>
                <div class="tab-pane" id="B">
                  <p>{{_i}}Howdy, I'm in Section B.{{/i}}</p>
                </div>
                <div class="tab-pane" id="C">
                  <p>{{_i}}What up girl, this is Section C.{{/i}}</p>
                </div>
              </div>
              <ul class="nav nav-tabs">
                <li class="active"><a href="#A" data-toggle="tab">{{_i}}Section 1{{/i}}</a></li>
                <li><a href="#B" data-toggle="tab">{{_i}}Section 2{{/i}}</a></li>
                <li><a href="#C" data-toggle="tab">{{_i}}Section 3{{/i}}</a></li>
              </ul>
            </div> <!-- /tabbable -->
          </div>{{! /example }}
<pre class="prettyprint linenums">
&lt;div class="tabbable tabs-below"&gt;
  &lt;div class="tab-content"&gt;
    ...
  &lt;/div&gt;
  &lt;ul class="nav nav-tabs"&gt;
    ...
  &lt;/ul&gt;
&lt;/div&gt;
</pre>

          <h4>{{_i}}Tabs on the left{{/i}}</h4>
          <p>{{_i}}Swap the class to put tabs on the left.{{/i}}</p>
          <div class="bs-docs-example">
            <div class="tabbable tabs-left">
              <ul class="nav nav-tabs">
                <li class="active"><a href="#lA" data-toggle="tab">{{_i}}Section 1{{/i}}</a></li>
                <li><a href="#lB" data-toggle="tab">{{_i}}Section 2{{/i}}</a></li>
                <li><a href="#lC" data-toggle="tab">{{_i}}Section 3{{/i}}</a></li>
              </ul>
              <div class="tab-content">
                <div class="tab-pane active" id="lA">
                  <p>{{_i}}I'm in Section A.{{/i}}</p>
                </div>
                <div class="tab-pane" id="lB">
                  <p>{{_i}}Howdy, I'm in Section B.{{/i}}</p>
                </div>
                <div class="tab-pane" id="lC">
                  <p>{{_i}}What up girl, this is Section C.{{/i}}</p>
                </div>
              </div>
            </div> <!-- /tabbable -->
          </div>{{! /example }}
<pre class="prettyprint linenums">
&lt;div class="tabbable tabs-left"&gt;
  &lt;ul class="nav nav-tabs"&gt;
    ...
  &lt;/ul&gt;
  &lt;div class="tab-content"&gt;
    ...
  &lt;/div&gt;
&lt;/div&gt;
</pre>

          <h4>{{_i}}Tabs on the right{{/i}}</h4>
          <p>{{_i}}Swap the class to put tabs on the right.{{/i}}</p>
          <div class="bs-docs-example">
            <div class="tabbable tabs-right">
              <ul class="nav nav-tabs">
                <li class="active"><a href="#rA" data-toggle="tab">{{_i}}Section 1{{/i}}</a></li>
                <li><a href="#rB" data-toggle="tab">{{_i}}Section 2{{/i}}</a></li>
                <li><a href="#rC" data-toggle="tab">{{_i}}Section 3{{/i}}</a></li>
              </ul>
              <div class="tab-content">
                <div class="tab-pane active" id="rA">
                  <p>{{_i}}I'm in Section A.{{/i}}</p>
                </div>
                <div class="tab-pane" id="rB">
                  <p>{{_i}}Howdy, I'm in Section B.{{/i}}</p>
                </div>
                <div class="tab-pane" id="rC">
                  <p>{{_i}}What up girl, this is Section C.{{/i}}</p>
                </div>
              </div>
            </div> <!-- /tabbable -->
          </div>{{! /example }}
<pre class="prettyprint linenums">
&lt;div class="tabbable tabs-right"&gt;
  &lt;ul class="nav nav-tabs"&gt;
    ...
  &lt;/ul&gt;
  &lt;div class="tab-content"&gt;
    ...
  &lt;/div&gt;
&lt;/div&gt;
</pre>

        </section>



        <!-- Navbar
        ================================================== -->
        <section id="navbar">
          <div class="page-header">
            <h1>{{_i}}Navbar{{/i}}</h1>
          </div>


          <h2>{{_i}}Basic navbar{{/i}}</h2>
          <p>{{_i}}To start, navbars are static (not fixed to the top) and include support for a project name and basic navigation. Place one anywhere within a <code>.container</code>, which sets the width of your site and content.{{/i}}</p>
          <div class="bs-docs-example">
            <div class="navbar">
              <div class="navbar-inner">
                <a class="brand" href="#">{{_i}}Title{{/i}}</a>
                <ul class="nav">
                  <li class="active"><a href="#">{{_i}}Home{{/i}}</a></li>
                  <li><a href="#">{{_i}}Link{{/i}}</a></li>
                  <li><a href="#">{{_i}}Link{{/i}}</a></li>
                </ul>
              </div>
            </div>
          </div>{{! /example }}
<pre class="prettyprint linenums">
&lt;div class="navbar"&gt;
  &lt;div class="navbar-inner"&gt;
    &lt;a class="brand" href="#"&gt;{{_i}}Title{{/i}}&lt;/a&gt;
    &lt;ul class="nav"&gt;
      &lt;li class="active"&gt;&lt;a href="#"&gt;{{_i}}Home{{/i}}&lt;/a&gt;&lt;/li&gt;
      &lt;li&gt;&lt;a href="#"&gt;{{_i}}Link{{/i}}&lt;/a&gt;&lt;/li&gt;
      &lt;li&gt;&lt;a href="#"&gt;{{_i}}Link{{/i}}&lt;/a&gt;&lt;/li&gt;
    &lt;/ul&gt;
  &lt;/div&gt;
&lt;/div&gt;
</pre>


          <hr class="bs-docs-separator">


          <h2>{{_i}}Navbar components{{/i}}</h2>

          <h3>{{_i}}Brand{{/i}}</h3>
          <p>{{_i}}A simple link to show your brand or project name only requires an anchor tag.{{/i}}</p>
          <div class="bs-docs-example">
            <div class="navbar">
              <div class="navbar-inner">
                <a class="brand" href="#">{{_i}}Title{{/i}}</a>
              </div>
            </div>
          </div>{{! /example }}
<pre class="prettyprint linenums">
&lt;a class="brand" href="#"&gt;{{_i}}Project name{{/i}}&lt;/a&gt;
</pre>

          <h3>{{_i}}Nav links{{/i}}</h3>
          <p>{{_i}}Nav items are simple to add via unordered lists.{{/i}}</p>
          <div class="bs-docs-example">
            <div class="navbar">
              <div class="navbar-inner">
                <ul class="nav">
                  <li class="active"><a href="#">{{_i}}Home{{/i}}</a></li>
                  <li><a href="#">{{_i}}Link{{/i}}</a></li>
                  <li><a href="#">{{_i}}Link{{/i}}</a></li>
                </ul>
              </div>
            </div>
          </div>{{! /example }}
<pre class="prettyprint linenums">
&lt;ul class="nav"&gt;
  &lt;li class="active"&gt;
    &lt;a href="#">{{_i}}Home{{/i}}&lt;/a&gt;
  &lt;/li&gt;
  &lt;li&gt;&lt;a href="#"&gt;{{_i}}Link{{/i}}&lt;/a&gt;&lt;/li&gt;
  &lt;li&gt;&lt;a href="#"&gt;{{_i}}Link{{/i}}&lt;/a&gt;&lt;/li&gt;
&lt;/ul&gt;
</pre>
          <p>{{_i}}You can easily add dividers to your nav links with an empty list item and a simple class. Just add this between links:{{/i}}</p>
          <div class="bs-docs-example">
            <div class="navbar">
              <div class="navbar-inner">
                <ul class="nav">
                  <li class="active"><a href="#">{{_i}}Home{{/i}}</a></li>
                  <li class="divider-vertical"></li>
                  <li><a href="#">{{_i}}Link{{/i}}</a></li>
                  <li class="divider-vertical"></li>
                  <li><a href="#">{{_i}}Link{{/i}}</a></li>
                  <li class="divider-vertical"></li>
                </ul>
              </div>
            </div>
          </div>{{! /example }}
<pre class="prettyprint linenums">
&lt;ul class="nav"&gt;
  ...
  &lt;li class="divider-vertical"&gt;&lt;/li&gt;
  ...
&lt;/ul&gt;
</pre>

          <h3>{{_i}}Forms{{/i}}</h3>
          <p>{{_i}}To properly style and position a form within the navbar, add the appropriate classes as shown below. For a default form, include <code>.navbar-form</code> and either <code>.pull-left</code> or <code>.pull-right</code> to properly align it.{{/i}}</p>
          <div class="bs-docs-example">
            <div class="navbar">
              <div class="navbar-inner">
                <form class="navbar-form pull-left">
                  <input type="text" class="span2">
                  <button type="submit" class="btn">{{_i}}Submit{{/i}}</button>
                </form>
              </div>
            </div>
          </div>{{! /example }}
<pre class="prettyprint linenums">
&lt;form class="navbar-form pull-left"&gt;
  &lt;input type="text" class="span2"&gt;
  &lt;button type="submit" class="btn"&gt;{{_i}}Submit{{/i}}&lt;/button&gt;
&lt;/form&gt;
</pre>

          <h3>{{_i}}Search form{{/i}}</h3>
          <p>{{_i}}For a more customized search form, add <code>.navbar-search</code> to the <code>form</code> and <code>.search-query</code> to the input for specialized styles in the navbar.{{/i}}</p>
          <div class="bs-docs-example">
            <div class="navbar">
              <div class="navbar-inner">
                <form class="navbar-search pull-left">
                  <input type="text" class="search-query" placeholder="{{_i}}Search{{/i}}">
                </form>
              </div>
            </div>
          </div>{{! /example }}
<pre class="prettyprint linenums">
&lt;form class="navbar-search pull-left"&gt;
  &lt;input type="text" class="search-query" placeholder="{{_i}}Search{{/i}}"&gt;
&lt;/form&gt;
</pre>

          <h3>{{_i}}Component alignment{{/i}}</h3>
          <p>{{_i}}Align nav links, search form, or text, use the <code>.pull-left</code> or <code>.pull-right</code> utility classes. Both classes will add a CSS float in the specified direction.{{/i}}</p>

          <h3>{{_i}}Using dropdowns{{/i}}</h3>
          <p>{{_i}}Add dropdowns and dropups to the nav with a bit of markup and the <a href="./javascript.html#dropdowns">dropdowns JavaScript plugin</a>.{{/i}}</p>
<pre class="prettyprint linenums">
&lt;ul class="nav"&gt;
  &lt;li class="dropdown"&gt;
    &lt;a href="#" class="dropdown-toggle" data-toggle="dropdown">
      {{_i}}Account{{/i}}
      &lt;b class="caret"&gt;&lt;/b&gt;
    &lt;/a&gt;
    &lt;ul class="dropdown-menu"&gt;
      ...
    &lt;/ul&gt;
  &lt;/li&gt;
&lt;/ul&gt;
</pre>
          <p>{{_i}}Visit the <a href="./javascript.html#dropdowns">JavaScript dropdowns documentation</a> for more markup and information on calling dropdowns.{{/i}}</p>

          <h3>{{_i}}Text{{/i}}</h3>
          <p>{{_i}}Wrap strings of text in an element with <code>.navbar-text</code>, usually on a <code>&lt;p&gt;</code> tag for proper leading and color.{{/i}}</p>


          <hr class="bs-docs-separator">


          <h2>{{_i}}Optional display variations{{/i}}</h2>
          <p>{{_i}}Fix the navbar to the top or bottom of the viewport with an additional class on the outermost div, <code>.navbar</code>.{{/i}}</p>

          <h3>Fixed to top</h3>
          <p>{{_i}}Add <code>.navbar-fixed-top</code> and remember to account for the hidden area underneath it by adding at least 40px <code>padding</code> to the <code>&lt;body&gt;</code>. Be sure to add this after the core Bootstrap CSS and before the optional responsive CSS.{{/i}}</p>
          <div class="bs-docs-example bs-navbar-top-example">
            <div class="navbar navbar-fixed-top" style="position: absolute;">
              <div class="navbar-inner">
                <div class="container" style="width: auto; padding: 0 20px;">
                  <a class="brand" href="#">{{_i}}Title{{/i}}</a>
                  <ul class="nav">
                    <li class="active"><a href="#">{{_i}}Home{{/i}}</a></li>
                    <li><a href="#">{{_i}}Link{{/i}}</a></li>
                    <li><a href="#">{{_i}}Link{{/i}}</a></li>
                  </ul>
                </div>
              </div>
            </div>
          </div>{{! /example }}
<pre class="prettyprint linenums">
&lt;div class="navbar navbar-fixed-top"&gt;
  ...
&lt;/div&gt;
</pre>

          <h3>Fixed to bottom</h3>
          <p>{{_i}}Add <code>.navbar-fixed-bottom</code> instead.{{/i}}</p>
          <div class="bs-docs-example bs-navbar-bottom-example">
            <div class="navbar navbar-fixed-bottom" style="position: absolute;">
              <div class="navbar-inner">
                <div class="container" style="width: auto; padding: 0 20px;">
                  <a class="brand" href="#">{{_i}}Title{{/i}}</a>
                  <ul class="nav">
                    <li class="active"><a href="#">{{_i}}Home{{/i}}</a></li>
                    <li><a href="#">{{_i}}Link{{/i}}</a></li>
                    <li><a href="#">{{_i}}Link{{/i}}</a></li>
                  </ul>
                </div>
              </div>
            </div>
          </div>{{! /example }}
<pre class="prettyprint linenums">
&lt;div class="navbar navbar-fixed-bottom"&gt;
  ...
&lt;/div&gt;
</pre>

          <h3>{{_i}}Static top navbar{{/i}}</h3>
          <p>{{_i}}Create a full-width navbar that scrolls away with the page by adding <code>.navbar-static-top</code>. Unlike the <code>.navbar-fixed-top</code> class, you do not need to change any padding on the <code>body</code>.{{/i}}</p>
          <div class="bs-docs-example bs-navbar-top-example">
            <div class="navbar navbar-static-top" style="margin: -1px -1px 0;">
              <div class="navbar-inner">
                <div class="container" style="width: auto; padding: 0 20px;">
                  <a class="brand" href="#">{{_i}}Title{{/i}}</a>
                  <ul class="nav">
                    <li class="active"><a href="#">{{_i}}Home{{/i}}</a></li>
                    <li><a href="#">{{_i}}Link{{/i}}</a></li>
                    <li><a href="#">{{_i}}Link{{/i}}</a></li>
                  </ul>
                </div>
              </div>
            </div>
          </div>{{! /example }}
<pre class="prettyprint linenums">
&lt;div class="navbar navbar-static-top"&gt;
  ...
&lt;/div&gt;
</pre>


          <hr class="bs-docs-separator">


          <h2>{{_i}}Responsive navbar{{/i}}</h2>
          <p>{{_i}}To implement a collapsing responsive navbar, wrap your navbar content in a containing div, <code>.nav-collapse.collapse</code>, and add the navbar toggle button, <code>.btn-navbar</code>.{{/i}}</p>
          <div class="bs-docs-example">
            <div class="navbar">
              <div class="navbar-inner">
                <div class="container">
                  <a class="btn btn-navbar" data-toggle="collapse" data-target=".navbar-responsive-collapse">
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                  </a>
                  <a class="brand" href="#">{{_i}}Title{{/i}}</a>
                  <div class="nav-collapse collapse navbar-responsive-collapse">
                    <ul class="nav">
                      <li class="active"><a href="#">{{_i}}Home{{/i}}</a></li>
                      <li><a href="#">{{_i}}Link{{/i}}</a></li>
                      <li><a href="#">{{_i}}Link{{/i}}</a></li>
                      <li class="dropdown">
                        <a href="#" class="dropdown-toggle" data-toggle="dropdown">{{_i}}Dropdown{{/i}} <b class="caret"></b></a>
                        <ul class="dropdown-menu">
                          <li><a href="#">{{_i}}Action{{/i}}</a></li>
                          <li><a href="#">{{_i}}Another action{{/i}}</a></li>
                          <li><a href="#">{{_i}}Something else here{{/i}}</a></li>
                          <li class="divider"></li>
                          <li class="nav-header">Nav header</li>
                          <li><a href="#">{{_i}}Separated link{{/i}}</a></li>
                          <li><a href="#">{{_i}}One more separated link{{/i}}</a></li>
                        </ul>
                      </li>
                    </ul>
                    <form class="navbar-search pull-left" action="">
                      <input type="text" class="search-query span2" placeholder="Search">
                    </form>
                    <ul class="nav pull-right">
                      <li><a href="#">{{_i}}Link{{/i}}</a></li>
                      <li class="divider-vertical"></li>
                      <li class="dropdown">
                        <a href="#" class="dropdown-toggle" data-toggle="dropdown">{{_i}}Dropdown{{/i}} <b class="caret"></b></a>
                        <ul class="dropdown-menu">
                          <li><a href="#">{{_i}}Action{{/i}}</a></li>
                          <li><a href="#">{{_i}}Another action{{/i}}</a></li>
                          <li><a href="#">{{_i}}Something else here{{/i}}</a></li>
                          <li class="divider"></li>
                          <li><a href="#">{{_i}}Separated link{{/i}}</a></li>
                        </ul>
                      </li>
                    </ul>
                  </div><!-- /.nav-collapse -->
                </div>
              </div><!-- /navbar-inner -->
            </div><!-- /navbar -->
          </div>{{! /example }}
<pre class="prettyprint linenums">
&lt;div class="navbar"&gt;
  &lt;div class="navbar-inner"&gt;
    &lt;div class="container"&gt;

      &lt;!-- {{_i}}.btn-navbar is used as the toggle for collapsed navbar content{{/i}} --&gt;
      &lt;a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"&gt;
        &lt;span class="icon-bar"&gt;&lt;/span&gt;
        &lt;span class="icon-bar"&gt;&lt;/span&gt;
        &lt;span class="icon-bar"&gt;&lt;/span&gt;
      &lt;/a&gt;

      &lt;!-- {{_i}}Be sure to leave the brand out there if you want it shown{{/i}} --&gt;
      &lt;a class="brand" href="#"&gt;{{_i}}Project name{{/i}}&lt;/a&gt;

      &lt;!-- {{_i}}Everything you want hidden at 940px or less, place within here{{/i}} --&gt;
      &lt;div class="nav-collapse collapse"&gt;
        &lt;!-- .nav, .navbar-search, .navbar-form, etc --&gt;
      &lt;/div&gt;

    &lt;/div&gt;
  &lt;/div&gt;
&lt;/div&gt;
</pre>
          <div class="alert alert-info">
            <strong>{{_i}}Heads up!{{/i}}</strong> The responsive navbar requires the <a href="./javascript.html#collapse">collapse plugin</a> and <a href="./scaffolding.html#responsive">responsive Bootstrap CSS file</a>.
          </div>


          <hr class="bs-docs-separator">


          <h2>{{_i}}Inverted variation{{/i}}</h2>
          <p>{{_i}}Modify the look of the navbar by adding <code>.navbar-inverse</code>.{{/i}}</p>
          <div class="bs-docs-example">
            <div class="navbar navbar-inverse" style="position: static;">
              <div class="navbar-inner">
                <div class="container">
                  <a class="btn btn-navbar" data-toggle="collapse" data-target=".navbar-inverse-collapse">
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                  </a>
                  <a class="brand" href="#">{{_i}}Title{{/i}}</a>
                  <div class="nav-collapse collapse navbar-inverse-collapse">
                    <ul class="nav">
                      <li class="active"><a href="#">{{_i}}Home{{/i}}</a></li>
                      <li><a href="#">{{_i}}Link{{/i}}</a></li>
                      <li><a href="#">{{_i}}Link{{/i}}</a></li>
                      <li class="dropdown">
                        <a href="#" class="dropdown-toggle" data-toggle="dropdown">{{_i}}Dropdown{{/i}} <b class="caret"></b></a>
                        <ul class="dropdown-menu">
                          <li><a href="#">{{_i}}Action{{/i}}</a></li>
                          <li><a href="#">{{_i}}Another action{{/i}}</a></li>
                          <li><a href="#">{{_i}}Something else here{{/i}}</a></li>
                          <li class="divider"></li>
                          <li class="nav-header">Nav header</li>
                          <li><a href="#">{{_i}}Separated link{{/i}}</a></li>
                          <li><a href="#">{{_i}}One more separated link{{/i}}</a></li>
                        </ul>
                      </li>
                    </ul>
                    <form class="navbar-search pull-left" action="">
                      <input type="text" class="search-query span2" placeholder="Search">
                    </form>
                    <ul class="nav pull-right">
                      <li><a href="#">{{_i}}Link{{/i}}</a></li>
                      <li class="divider-vertical"></li>
                      <li class="dropdown">
                        <a href="#" class="dropdown-toggle" data-toggle="dropdown">{{_i}}Dropdown{{/i}} <b class="caret"></b></a>
                        <ul class="dropdown-menu">
                          <li><a href="#">{{_i}}Action{{/i}}</a></li>
                          <li><a href="#">{{_i}}Another action{{/i}}</a></li>
                          <li><a href="#">{{_i}}Something else here{{/i}}</a></li>
                          <li class="divider"></li>
                          <li><a href="#">{{_i}}Separated link{{/i}}</a></li>
                        </ul>
                      </li>
                    </ul>
                  </div><!-- /.nav-collapse -->
                </div>
              </div><!-- /navbar-inner -->
            </div><!-- /navbar -->
          </div>{{! /example }}
<pre class="prettyprint linenums">
&lt;div class="navbar navbar-inverse"&gt;
  ...
&lt;/div&gt;
</pre>

        </section>



        <!-- Breadcrumbs
        ================================================== -->
        <section id="breadcrumbs">
          <div class="page-header">
            <h1>{{_i}}Breadcrumbs{{/i}} <small></small></h1>
          </div>

          <h2>{{_i}}Examples{{/i}}</h2>
          <p>{{_i}}A single example shown as it might be displayed across multiple pages.{{/i}}</p>
          <div class="bs-docs-example">
            <ul class="breadcrumb">
              <li class="active">{{_i}}Home{{/i}}</li>
            </ul>
            <ul class="breadcrumb">
              <li><a href="#">{{_i}}Home{{/i}}</a> <span class="divider">/</span></li>
              <li class="active">{{_i}}Library{{/i}}</li>
            </ul>
            <ul class="breadcrumb" style="margin-bottom: 5px;">
              <li><a href="#">{{_i}}Home{{/i}}</a> <span class="divider">/</span></li>
              <li><a href="#">{{_i}}Library{{/i}}</a> <span class="divider">/</span></li>
              <li class="active">{{_i}}Data{{/i}}</li>
            </ul>
          </div>
<pre class="prettyprint linenums">
&lt;ul class="breadcrumb"&gt;
  &lt;li&gt;&lt;a href="#"&gt;{{_i}}Home{{/i}}&lt;/a&gt; &lt;span class="divider"&gt;/&lt;/span&gt;&lt;/li&gt;
  &lt;li&gt;&lt;a href="#"&gt;{{_i}}Library{{/i}}&lt;/a&gt; &lt;span class="divider"&gt;/&lt;/span&gt;&lt;/li&gt;
  &lt;li class="active"&gt;{{_i}}Data{{/i}}&lt;/li&gt;
&lt;/ul&gt;
</pre>

        </section>



        <!-- Pagination
        ================================================== -->
        <section id="pagination">
          <div class="page-header">
            <h1>{{_i}}Pagination{{/i}} <small>{{_i}}Two options for paging through content{{/i}}</small></h1>
          </div>

          <h2>{{_i}}Standard pagination{{/i}}</h2>
          <p>{{_i}}Simple pagination inspired by Rdio, great for apps and search results. The large block is hard to miss, easily scalable, and provides large click areas.{{/i}}</p>
          <div class="bs-docs-example">
            <div class="pagination">
              <ul>
                <li><a href="#">&laquo;</a></li>
                <li><a href="#">1</a></li>
                <li><a href="#">2</a></li>
                <li><a href="#">3</a></li>
                <li><a href="#">4</a></li>
                <li><a href="#">5</a></li>
                <li><a href="#">&raquo;</a></li>
              </ul>
            </div>
          </div>
<pre class="prettyprint linenums">
&lt;div class="pagination"&gt;
  &lt;ul&gt;
    &lt;li&gt;&lt;a href="#"&gt;Prev&lt;/a&gt;&lt;/li&gt;
    &lt;li&gt;&lt;a href="#"&gt;1&lt;/a&gt;&lt;/li&gt;
    &lt;li&gt;&lt;a href="#"&gt;2&lt;/a&gt;&lt;/li&gt;
    &lt;li&gt;&lt;a href="#"&gt;3&lt;/a&gt;&lt;/li&gt;
    &lt;li&gt;&lt;a href="#"&gt;4&lt;/a&gt;&lt;/li&gt;
    &lt;li&gt;&lt;a href="#"&gt;5&lt;/a&gt;&lt;/li&gt;
    &lt;li&gt;&lt;a href="#"&gt;Next&lt;/a&gt;&lt;/li&gt;
  &lt;/ul&gt;
&lt;/div&gt;
</pre>


          <hr class="bs-docs-separator">


          <h2>{{_i}}Options{{/i}}</h2>

          <h3>{{_i}}Disabled and active states{{/i}}</h3>
          <p>{{_i}}Links are customizable for different circumstances. Use <code>.disabled</code> for unclickable links and <code>.active</code> to indicate the current page.{{/i}}</p>
          <div class="bs-docs-example">
            <div class="pagination pagination-centered">
              <ul>
                <li class="disabled"><a href="#">&laquo;</a></li>
                <li class="active"><a href="#">1</a></li>
                <li><a href="#">2</a></li>
                <li><a href="#">3</a></li>
                <li><a href="#">4</a></li>
                <li><a href="#">5</a></li>
                <li><a href="#">&raquo;</a></li>
             </ul>
            </div>
          </div>
<pre class="prettyprint linenums">
&lt;div class="pagination"&gt;
  &lt;ul&gt;
    &lt;li class="disabled"&gt;&lt;a href="#"&gt;&amp;laquo;&lt;/a&gt;&lt;/li&gt;
    &lt;li class="active"&gt;&lt;a href="#"&gt;1&lt;/a&gt;&lt;/li&gt;
    ...
  &lt;/ul&gt;
&lt;/div&gt;
</pre>
          <p>{{_i}}You can optionally swap out active or disabled anchors for spans to remove click functionality while retaining intended styles.{{/i}}</p>
<pre class="prettyprint linenums">
&lt;div class="pagination"&gt;
  &lt;ul&gt;
    &lt;li class="disabled"&gt;&lt;span&gt;&amp;laquo;&lt;/span&gt;&lt;/li&gt;
    &lt;li class="active"&gt;&lt;span&gt;1&lt;/span&gt;&lt;/li&gt;
    ...
  &lt;/ul&gt;
&lt;/div&gt;
</pre>

          <h3>{{_i}}Sizes{{/i}}</h3>
          <p>{{_i}}Fancy larger or smaller pagination? Add <code>.pagination-large</code>, <code>.pagination-small</code>, or <code>.pagination-mini</code> for additional sizes.{{/i}}</p>
          <div class="bs-docs-example">
            <div class="pagination pagination-large">
              <ul>
                <li><a href="#">&laquo;</a></li>
                <li><a href="#">1</a></li>
                <li><a href="#">2</a></li>
                <li><a href="#">3</a></li>
                <li><a href="#">4</a></li>
                <li><a href="#">5</a></li>
                <li><a href="#">&raquo;</a></li>
              </ul>
            </div>
            <div class="pagination">
              <ul>
                <li><a href="#">&laquo;</a></li>
                <li><a href="#">1</a></li>
                <li><a href="#">2</a></li>
                <li><a href="#">3</a></li>
                <li><a href="#">4</a></li>
                <li><a href="#">5</a></li>
                <li><a href="#">&raquo;</a></li>
             </ul>
            </div>
            <div class="pagination pagination-small">
              <ul>
                <li><a href="#">&laquo;</a></li>
                <li><a href="#">1</a></li>
                <li><a href="#">2</a></li>
                <li><a href="#">3</a></li>
                <li><a href="#">4</a></li>
                <li><a href="#">5</a></li>
                <li><a href="#">&raquo;</a></li>
              </ul>
            </div>
            <div class="pagination pagination-mini">
              <ul>
                <li><a href="#">&laquo;</a></li>
                <li><a href="#">1</a></li>
                <li><a href="#">2</a></li>
                <li><a href="#">3</a></li>
                <li><a href="#">4</a></li>
                <li><a href="#">5</a></li>
                <li><a href="#">&raquo;</a></li>
              </ul>
            </div>
          </div>
<pre class="prettyprint linenums">
&lt;div class="pagination pagination-large"&gt;
  &lt;ul&gt;
    ...
  &lt;/ul&gt;
&lt;/div&gt;
&lt;div class="pagination"&gt;
  &lt;ul&gt;
    ...
  &lt;/ul&gt;
&lt;/div&gt;
&lt;div class="pagination pagination-small"&gt;
  &lt;ul&gt;
    ...
  &lt;/ul&gt;
&lt;/div&gt;
&lt;div class="pagination pagination-mini"&gt;
  &lt;ul&gt;
    ...
  &lt;/ul&gt;
&lt;/div&gt;
</pre>

          <h3>{{_i}}Alignment{{/i}}</h3>
          <p>{{_i}}Add one of two optional classes to change the alignment of pagination links: <code>.pagination-centered</code> and <code>.pagination-right</code>.{{/i}}</p>
          <div class="bs-docs-example">
            <div class="pagination pagination-centered">
              <ul>
                <li><a href="#">&laquo;</a></li>
                <li><a href="#">1</a></li>
                <li><a href="#">2</a></li>
                <li><a href="#">3</a></li>
                <li><a href="#">4</a></li>
                <li><a href="#">5</a></li>
                <li><a href="#">&raquo;</a></li>
             </ul>
            </div>
          </div>
<pre class="prettyprint linenums">
&lt;div class="pagination pagination-centered"&gt;
  ...
&lt;/div&gt;
</pre>
          <div class="bs-docs-example">
            <div class="pagination pagination-right">
              <ul>
                <li><a href="#">&laquo;</a></li>
                <li><a href="#">1</a></li>
                <li><a href="#">2</a></li>
                <li><a href="#">3</a></li>
                <li><a href="#">4</a></li>
                <li><a href="#">5</a></li>
                <li><a href="#">&raquo;</a></li>
              </ul>
            </div>
          </div>
<pre class="prettyprint linenums">
&lt;div class="pagination pagination-right"&gt;
  ...
&lt;/div&gt;
</pre>


          <hr class="bs-docs-separator">


          <h2>{{_i}}Pager{{/i}}</h2>
          <p>{{_i}}Quick previous and next links for simple pagination implementations with light markup and styles. It's great for simple sites like blogs or magazines.{{/i}}</p>

          <h3>{{_i}}Default example{{/i}}</h3>
          <p>{{_i}}By default, the pager centers links.{{/i}}</p>
          <div class="bs-docs-example">
            <ul class="pager">
              <li><a href="#">{{_i}}Previous{{/i}}</a></li>
              <li><a href="#">{{_i}}Next{{/i}}</a></li>
            </ul>
          </div>
<pre class="prettyprint linenums">
&lt;ul class="pager"&gt;
  &lt;li&gt;&lt;a href="#"&gt;{{_i}}Previous{{/i}}&lt;/a&gt;&lt;/li&gt;
  &lt;li&gt;&lt;a href="#"&gt;{{_i}}Next{{/i}}&lt;/a&gt;&lt;/li&gt;
&lt;/ul&gt;
</pre>

          <h3>{{_i}}Aligned links{{/i}}</h3>
          <p>{{_i}}Alternatively, you can align each link to the sides:{{/i}}</p>
          <div class="bs-docs-example">
            <ul class="pager">
              <li class="previous"><a href="#">{{_i}}&larr; Older{{/i}}</a></li>
              <li class="next"><a href="#">{{_i}}Newer &rarr;{{/i}}</a></li>
            </ul>
          </div>
<pre class="prettyprint linenums">
&lt;ul class="pager"&gt;
  &lt;li class="previous"&gt;
    &lt;a href="#"&gt;{{_i}}&amp;larr; Older{{/i}}&lt;/a&gt;
  &lt;/li&gt;
  &lt;li class="next"&gt;
    &lt;a href="#"&gt;{{_i}}Newer &amp;rarr;{{/i}}&lt;/a&gt;
  &lt;/li&gt;
&lt;/ul&gt;
</pre>

          <h3>{{_i}}Optional disabled state{{/i}}</h3>
          <p>{{_i}}Pager links also use the general <code>.disabled</code> utility class from the pagination.{{/i}}</p>
          <div class="bs-docs-example">
            <ul class="pager">
              <li class="previous disabled"><a href="#">{{_i}}&larr; Older{{/i}}</a></li>
              <li class="next"><a href="#">{{_i}}Newer &rarr;{{/i}}</a></li>
            </ul>
          </div>
<pre class="prettyprint linenums">
&lt;ul class="pager"&gt;
  &lt;li class="previous disabled"&gt;
    &lt;a href="#"&gt;{{_i}}&amp;larr; Older{{/i}}&lt;/a&gt;
  &lt;/li&gt;
  ...
&lt;/ul&gt;
</pre>

        </section>



        <!-- Labels and badges
        ================================================== -->
        <section id="labels-badges">
          <div class="page-header">
            <h1>{{_i}}Labels and badges{{/i}}</h1>
          </div>
          <h3>{{_i}}Labels{{/i}}</h3>
          <table class="table table-bordered table-striped">
            <thead>
              <tr>
                <th>{{_i}}Labels{{/i}}</th>
                <th>{{_i}}Markup{{/i}}</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>
                  <span class="label">{{_i}}Default{{/i}}</span>
                </td>
                <td>
                  <code>&lt;span class="label"&gt;{{_i}}Default{{/i}}&lt;/span&gt;</code>
                </td>
              </tr>
              <tr>
                <td>
                  <span class="label label-success">{{_i}}Success{{/i}}</span>
                </td>
                <td>
                  <code>&lt;span class="label label-success"&gt;{{_i}}Success{{/i}}&lt;/span&gt;</code>
                </td>
              </tr>
              <tr>
                <td>
                  <span class="label label-warning">{{_i}}Warning{{/i}}</span>
                </td>
                <td>
                  <code>&lt;span class="label label-warning"&gt;{{_i}}Warning{{/i}}&lt;/span&gt;</code>
                </td>
              </tr>
              <tr>
                <td>
                  <span class="label label-important">{{_i}}Important{{/i}}</span>
                </td>
                <td>
                  <code>&lt;span class="label label-important"&gt;{{_i}}Important{{/i}}&lt;/span&gt;</code>
                </td>
              </tr>
              <tr>
                <td>
                  <span class="label label-info">{{_i}}Info{{/i}}</span>
                </td>
                <td>
                  <code>&lt;span class="label label-info"&gt;{{_i}}Info{{/i}}&lt;/span&gt;</code>
                </td>
              </tr>
              <tr>
                <td>
                  <span class="label label-inverse">{{_i}}Inverse{{/i}}</span>
                </td>
                <td>
                  <code>&lt;span class="label label-inverse"&gt;{{_i}}Inverse{{/i}}&lt;/span&gt;</code>
                </td>
              </tr>
            </tbody>
          </table>

          <h3>{{_i}}Badges{{/i}}</h3>
          <table class="table table-bordered table-striped">
            <thead>
              <tr>
                <th>{{_i}}Name{{/i}}</th>
                <th>{{_i}}Example{{/i}}</th>
                <th>{{_i}}Markup{{/i}}</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>
                  {{_i}}Default{{/i}}
                </td>
                <td>
                  <span class="badge">1</span>
                </td>
                <td>
                  <code>&lt;span class="badge"&gt;1&lt;/span&gt;</code>
                </td>
              </tr>
              <tr>
                <td>
                  {{_i}}Success{{/i}}
                </td>
                <td>
                  <span class="badge badge-success">2</span>
                </td>
                <td>
                  <code>&lt;span class="badge badge-success"&gt;2&lt;/span&gt;</code>
                </td>
              </tr>
              <tr>
                <td>
                  {{_i}}Warning{{/i}}
                </td>
                <td>
                  <span class="badge badge-warning">4</span>
                </td>
                <td>
                  <code>&lt;span class="badge badge-warning"&gt;4&lt;/span&gt;</code>
                </td>
              </tr>
              <tr>
                <td>
                  {{_i}}Important{{/i}}
                </td>
                <td>
                  <span class="badge badge-important">6</span>
                </td>
                <td>
                  <code>&lt;span class="badge badge-important"&gt;6&lt;/span&gt;</code>
                </td>
              </tr>
              <tr>
                <td>
                  {{_i}}Info{{/i}}
                </td>
                <td>
                  <span class="badge badge-info">8</span>
                </td>
                <td>
                  <code>&lt;span class="badge badge-info"&gt;8&lt;/span&gt;</code>
                </td>
              </tr>
              <tr>
                <td>
                  {{_i}}Inverse{{/i}}
                </td>
                <td>
                  <span class="badge badge-inverse">10</span>
                </td>
                <td>
                  <code>&lt;span class="badge badge-inverse"&gt;10&lt;/span&gt;</code>
                </td>
              </tr>
            </tbody>
          </table>

          <h3>{{_i}}Easily collapsible{{/i}}</h3>
          <p>{{_i}}For easy implementation, labels and badges will simply collapse (via CSS's <code>:empty</code> selector) when no content exists within.{{/i}}</p>

        </section>



        <!-- Typographic components
        ================================================== -->
        <section id="typography">
          <div class="page-header">
            <h1>{{_i}}Typographic components{{/i}}</h1>
          </div>

          <h2>{{_i}}Hero unit{{/i}}</h2>
          <p>{{_i}}A lightweight, flexible component to showcase key content on your site. It works well on marketing and content-heavy sites.{{/i}}</p>
          <div class="bs-docs-example">
            <div class="hero-unit">
              <h1>{{_i}}Hello, world!{{/i}}</h1>
              <p>{{_i}}This is a simple hero unit, a simple jumbotron-style component for calling extra attention to featured content or information.{{/i}}</p>
              <p><a class="btn btn-primary btn-large">{{_i}}Learn more{{/i}}</a></p>
            </div>
          </div>
<pre class="prettyprint linenums">
&lt;div class="hero-unit"&gt;
  &lt;h1&gt;{{_i}}Heading{{/i}}&lt;/h1&gt;
  &lt;p&gt;{{_i}}Tagline{{/i}}&lt;/p&gt;
  &lt;p&gt;
    &lt;a class="btn btn-primary btn-large"&gt;
      {{_i}}Learn more{{/i}}
    &lt;/a&gt;
  &lt;/p&gt;
&lt;/div&gt;
</pre>

          <h2>{{_i}}Page header{{/i}}</h2>
          <p>{{_i}}A simple shell for an <code>h1</code> to appropriately space out and segment sections of content on a page. It can utilize the <code>h1</code>'s default <code>small</code>, element as well most other components (with additional styles).{{/i}}</p>
          <div class="bs-docs-example">
            <div class="page-header">
              <h1>{{_i}}Example page header{{/i}} <small>{{_i}}Subtext for header{{/i}}</small></h1>
            </div>
          </div>
<pre class="prettyprint linenums">
&lt;div class="page-header"&gt;
  &lt;h1&gt;{{_i}}Example page header{{/i}} &lt;small&gt;{{_i}}Subtext for header{{/i}}&lt;/small&gt;&lt;/h1&gt;
&lt;/div&gt;
</pre>

        </section>



        <!-- Thumbnails
        ================================================== -->
        <section id="thumbnails">
          <div class="page-header">
            <h1>{{_i}}Thumbnails{{/i}} <small>{{_i}}Grids of images, videos, text, and more{{/i}}</small></h1>
          </div>

          <h2>{{_i}}Default thumbnails{{/i}}</h2>
          <p>{{_i}}By default, Bootstrap's thumbnails are designed to showcase linked images with minimal required markup.{{/i}}</p>
          <div class="row-fluid">
            <ul class="thumbnails">
              <li class="span3">
                <a href="#" class="thumbnail">
                  <img data-src="holder.js/260x180" alt="">
                </a>
              </li>
              <li class="span3">
                <a href="#" class="thumbnail">
                  <img data-src="holder.js/260x180" alt="">
                </a>
              </li>
              <li class="span3">
                <a href="#" class="thumbnail">
                  <img data-src="holder.js/260x180" alt="">
                </a>
              </li>
              <li class="span3">
                <a href="#" class="thumbnail">
                  <img data-src="holder.js/260x180" alt="">
                </a>
              </li>
            </ul>
          </div>

          <h2>{{_i}}Highly customizable{{/i}}</h2>
          <p>{{_i}}With a bit of extra markup, it's possible to add any kind of HTML content like headings, paragraphs, or buttons into thumbnails.{{/i}}</p>
          <div class="row-fluid">
            <ul class="thumbnails">
              <li class="span4">
                <div class="thumbnail">
                  <img data-src="holder.js/300x200" alt="">
                  <div class="caption">
                    <h3>{{_i}}Thumbnail label{{/i}}</h3>
                    <p>Cras justo odio, dapibus ac facilisis in, egestas eget quam. Donec id elit non mi porta gravida at eget metus. Nullam id dolor id nibh ultricies vehicula ut id elit.</p>
                    <p><a href="#" class="btn btn-primary">{{_i}}Action{{/i}}</a> <a href="#" class="btn">{{_i}}Action{{/i}}</a></p>
                  </div>
                </div>
              </li>
              <li class="span4">
                <div class="thumbnail">
                  <img data-src="holder.js/300x200" alt="">
                  <div class="caption">
                    <h3>{{_i}}Thumbnail label{{/i}}</h3>
                    <p>Cras justo odio, dapibus ac facilisis in, egestas eget quam. Donec id elit non mi porta gravida at eget metus. Nullam id dolor id nibh ultricies vehicula ut id elit.</p>
                    <p><a href="#" class="btn btn-primary">{{_i}}Action{{/i}}</a> <a href="#" class="btn">{{_i}}Action{{/i}}</a></p>
                  </div>
                </div>
              </li>
              <li class="span4">
                <div class="thumbnail">
                  <img data-src="holder.js/300x200" alt="">
                  <div class="caption">
                    <h3>{{_i}}Thumbnail label{{/i}}</h3>
                    <p>Cras justo odio, dapibus ac facilisis in, egestas eget quam. Donec id elit non mi porta gravida at eget metus. Nullam id dolor id nibh ultricies vehicula ut id elit.</p>
                    <p><a href="#" class="btn btn-primary">{{_i}}Action{{/i}}</a> <a href="#" class="btn">{{_i}}Action{{/i}}</a></p>
                  </div>
                </div>
              </li>
            </ul>
          </div>

          <h3>{{_i}}Why use thumbnails{{/i}}</h3>
          <p>{{_i}}Thumbnails (previously <code>.media-grid</code> up until v1.4) are great for grids of photos or videos, image search results, retail products, portfolios, and much more. They can be links or static content.{{/i}}</p>

          <h3>{{_i}}Simple, flexible markup{{/i}}</h3>
          <p>{{_i}}Thumbnail markup is simple&mdash;a <code>ul</code> with any number of <code>li</code> elements is all that is required. It's also super flexible, allowing for any type of content with just a bit more markup to wrap your contents.{{/i}}</p>

          <h3>{{_i}}Uses grid column sizes{{/i}}</h3>
          <p>{{_i}}Lastly, the thumbnails component uses existing grid system classes&mdash;like <code>.span2</code> or <code>.span3</code>&mdash;for control of thumbnail dimensions.{{/i}}</p>

          <h2>{{_i}}Markup{{/i}}</h2>
          <p>{{_i}}As mentioned previously, the required markup for thumbnails is light and straightforward. Here's a look at the default setup <strong>for linked images</strong>:{{/i}}</p>
<pre class="prettyprint linenums">
&lt;ul class="thumbnails"&gt;
  &lt;li class="span4"&gt;
    &lt;a href="#" class="thumbnail"&gt;
      &lt;img data-src="holder.js/300x200" alt=""&gt;
    &lt;/a&gt;
  &lt;/li&gt;
  ...
&lt;/ul&gt;
</pre>
          <p>{{_i}}For custom HTML content in thumbnails, the markup changes slightly. To allow block level content anywhere, we swap the <code>&lt;a&gt;</code> for a <code>&lt;div&gt;</code> like so:{{/i}}</p>
<pre class="prettyprint linenums">
&lt;ul class="thumbnails"&gt;
  &lt;li class="span4"&gt;
    &lt;div class="thumbnail"&gt;
      &lt;img data-src="holder.js/300x200" alt=""&gt;
      &lt;h3&gt;{{_i}}Thumbnail label{{/i}}&lt;/h3&gt;
      &lt;p&gt;{{_i}}Thumbnail caption...{{/i}}&lt;/p&gt;
    &lt;/div&gt;
  &lt;/li&gt;
  ...
&lt;/ul&gt;
</pre>

          <h2>{{_i}}More examples{{/i}}</h2>
          <p>{{_i}}Explore all your options with the various grid classes available to you. You can also mix and match different sizes.{{/i}}</p>
          <ul class="thumbnails">
            <li class="span4">
              <a href="#" class="thumbnail">
                <img data-src="holder.js/360x270" alt="">
              </a>
            </li>
            <li class="span3">
              <a href="#" class="thumbnail">
                <img data-src="holder.js/260x120" alt="">
              </a>
            </li>
            <li class="span2">
              <a href="#" class="thumbnail">
                <img data-src="holder.js/160x120" alt="">
              </a>
            </li>
            <li class="span3">
              <a href="#" class="thumbnail">
                <img data-src="holder.js/260x120" alt="">
              </a>
            </li>
            <li class="span2">
              <a href="#" class="thumbnail">
                <img data-src="holder.js/160x120" alt="">
              </a>
            </li>
          </ul>

        </section>




        <!-- Alerts
        ================================================== -->
        <section id="alerts">
          <div class="page-header">
            <h1>{{_i}}Alerts{{/i}} <small>{{_i}}Styles for success, warning, and error messages{{/i}}</small></h1>
          </div>

          <h2>{{_i}}Default alert{{/i}}</h2>
          <p>{{_i}}Wrap any text and an optional dismiss button in <code>.alert</code> for a basic warning alert message.{{/i}}</p>
          <div class="bs-docs-example">
            <div class="alert">
              <button type="button" class="close" data-dismiss="alert">&times;</button>
              <strong>{{_i}}Warning!{{/i}}</strong> {{_i}}Best check yo self, you're not looking too good.{{/i}}
            </div>
          </div>
<pre class="prettyprint linenums">
&lt;div class="alert"&gt;
  &lt;button type="button" class="close" data-dismiss="alert"&gt;&amp;times;&lt;/button&gt;
  &lt;strong&gt;{{_i}}Warning!{{/i}}&lt;/strong&gt; {{_i}}Best check yo self, you're not looking too good.{{/i}}
&lt;/div&gt;
</pre>

          <h3>{{_i}}Dismiss buttons{{/i}}</h3>
          <p>{{_i}}Mobile Safari and Mobile Opera browsers, in addition to the <code>data-dismiss="alert"</code> attribute, require an <code>href="#"</code> for the dismissal of alerts when using an <code>&lt;a&gt;</code> tag.{{/i}}</p>
          <pre class="prettyprint linenums">&lt;a href="#" class="close" data-dismiss="alert"&gt;&amp;times;&lt;/a&gt;</pre>
          <p>{{_i}}Alternatively, you may use a <code>&lt;button&gt;</code> element with the data attribute, which we have opted to do for our docs. When using <code>&lt;button&gt;</code>, you must include <code>type="button"</code> or your forms may not submit.{{/i}}</p>
          <pre class="prettyprint linenums">&lt;button type="button" class="close" data-dismiss="alert"&gt;&amp;times;&lt;/button&gt;</pre>

          <h3>{{_i}}Dismiss alerts via JavaScript{{/i}}</h3>
          <p>{{_i}}Use the <a href="./javascript.html#alerts">alerts jQuery plugin</a> for quick and easy dismissal of alerts.{{/i}}</p>


          <hr class="bs-docs-separator">


          <h2>{{_i}}Options{{/i}}</h2>
          <p>{{_i}}For longer messages, increase the padding on the top and bottom of the alert wrapper by adding <code>.alert-block</code>.{{/i}}</p>
          <div class="bs-docs-example">
            <div class="alert alert-block">
              <button type="button" class="close" data-dismiss="alert">&times;</button>
              <h4>{{_i}}Warning!{{/i}}</h4>
              <p>{{_i}}Best check yo self, you're not looking too good.{{/i}} Nulla vitae elit libero, a pharetra augue. Praesent commodo cursus magna, vel scelerisque nisl consectetur et.</p>
            </div>
          </div>
<pre class="prettyprint linenums">
&lt;div class="alert alert-block"&gt;
  &lt;button type="button" class="close" data-dismiss="alert"&gt;&amp;times;&lt;/button&gt;
  &lt;h4&gt;{{_i}}Warning!{{/i}}&lt;/h4&gt;
  {{_i}}Best check yo self, you're not...{{/i}}
&lt;/div&gt;
</pre>


          <hr class="bs-docs-separator">


          <h2>{{_i}}Contextual alternatives{{/i}}</h2>
          <p>{{_i}}Add optional classes to change an alert's connotation.{{/i}}</p>

          <h3>{{_i}}Error or danger{{/i}}</h3>
          <div class="bs-docs-example">
            <div class="alert alert-error">
              <button type="button" class="close" data-dismiss="alert">&times;</button>
              <strong>{{_i}}Oh snap!{{/i}}</strong> {{_i}}Change a few things up and try submitting again.{{/i}}
            </div>
          </div>
<pre class="prettyprint linenums">
&lt;div class="alert alert-error"&gt;
  ...
&lt;/div&gt;
</pre>

          <h3>{{_i}}Success{{/i}}</h3>
          <div class="bs-docs-example">
            <div class="alert alert-success">
              <button type="button" class="close" data-dismiss="alert">&times;</button>
              <strong>{{_i}}Well done!{{/i}}</strong> {{_i}}You successfully read this important alert message.{{/i}}
            </div>
          </div>
<pre class="prettyprint linenums">
&lt;div class="alert alert-success"&gt;
  ...
&lt;/div&gt;
</pre>

          <h3>{{_i}}Information{{/i}}</h3>
          <div class="bs-docs-example">
            <div class="alert alert-info">
              <button type="button" class="close" data-dismiss="alert">&times;</button>
              <strong>{{_i}}Heads up!{{/i}}</strong> {{_i}}This alert needs your attention, but it's not super important.{{/i}}
            </div>
          </div>
<pre class="prettyprint linenums">
&lt;div class="alert alert-info"&gt;
  ...
&lt;/div&gt;
</pre>

        </section>




        <!-- Progress bars
        ================================================== -->
        <section id="progress">
          <div class="page-header">
            <h1>{{_i}}Progress bars{{/i}} <small>{{_i}}For loading, redirecting, or action status{{/i}}</small></h1>
          </div>

          <h2>{{_i}}Examples and markup{{/i}}</h2>

          <h3>{{_i}}Basic{{/i}}</h3>
          <p>{{_i}}Default progress bar with a vertical gradient.{{/i}}</p>
          <div class="bs-docs-example">
            <div class="progress">
              <div class="bar" style="width: 60%;"></div>
            </div>
          </div>
<pre class="prettyprint linenums">
&lt;div class="progress"&gt;
  &lt;div class="bar" style="width: 60%;"&gt;&lt;/div&gt;
&lt;/div&gt;
</pre>

          <h3>{{_i}}Striped{{/i}}</h3>
          <p>{{_i}}Uses a gradient to create a striped effect. Not available in IE7-8.{{/i}}</p>
          <div class="bs-docs-example">
            <div class="progress progress-striped">
              <div class="bar" style="width: 20%;"></div>
            </div>
          </div>
<pre class="prettyprint linenums">
&lt;div class="progress progress-striped"&gt;
  &lt;div class="bar" style="width: 20%;"&gt;&lt;/div&gt;
&lt;/div&gt;
</pre>

          <h3>{{_i}}Animated{{/i}}</h3>
          <p>{{_i}}Add <code>.active</code> to <code>.progress-striped</code> to animate the stripes right to left. Not available in all versions of IE.{{/i}}</p>
          <div class="bs-docs-example">
            <div class="progress progress-striped active">
              <div class="bar" style="width: 45%"></div>
            </div>
          </div>
<pre class="prettyprint linenums">
&lt;div class="progress progress-striped active"&gt;
  &lt;div class="bar" style="width: 40%;"&gt;&lt;/div&gt;
&lt;/div&gt;
</pre>

          <h3>Stacked</h3>
          <p>Place multiple bars into the same <code>.progress</code> to stack them.</p>
          <div class="bs-docs-example">
            <div class="progress">
              <div class="bar bar-success" style="width: 35%"></div>
              <div class="bar bar-warning" style="width: 20%"></div>
              <div class="bar bar-danger" style="width: 10%"></div>
            </div>
          </div>
<pre class="prettyprint linenums">
&lt;div class="progress"&gt;
  &lt;div class="bar bar-success" style="width: 35%;"&gt;&lt;/div&gt;
  &lt;div class="bar bar-warning" style="width: 20%;"&gt;&lt;/div&gt;
  &lt;div class="bar bar-danger" style="width: 10%;"&gt;&lt;/div&gt;
&lt;/div&gt;
</pre>


          <hr class="bs-docs-separator">


          <h2>{{_i}}Options{{/i}}</h2>

          <h3>{{_i}}Additional colors{{/i}}</h3>
          <p>{{_i}}Progress bars use some of the same button and alert classes for consistent styles.{{/i}}</p>
          <div class="bs-docs-example">
            <div class="progress progress-info" style="margin-bottom: 9px;">
              <div class="bar" style="width: 20%"></div>
            </div>
            <div class="progress progress-success" style="margin-bottom: 9px;">
              <div class="bar" style="width: 40%"></div>
            </div>
            <div class="progress progress-warning" style="margin-bottom: 9px;">
              <div class="bar" style="width: 60%"></div>
            </div>
            <div class="progress progress-danger">
              <div class="bar" style="width: 80%"></div>
            </div>
          </div>
<pre class="prettyprint linenums">
&lt;div class="progress progress-info"&gt;
  &lt;div class="bar" style="width: 20%"&gt;&lt;/div&gt;
&lt;/div&gt;
&lt;div class="progress progress-success"&gt;
  &lt;div class="bar" style="width: 40%"&gt;&lt;/div&gt;
&lt;/div&gt;
&lt;div class="progress progress-warning"&gt;
  &lt;div class="bar" style="width: 60%"&gt;&lt;/div&gt;
&lt;/div&gt;
&lt;div class="progress progress-danger"&gt;
  &lt;div class="bar" style="width: 80%"&gt;&lt;/div&gt;
&lt;/div&gt;
</pre>

          <h3>{{_i}}Striped bars{{/i}}</h3>
          <p>{{_i}}Similar to the solid colors, we have varied striped progress bars.{{/i}}</p>
          <div class="bs-docs-example">
            <div class="progress progress-info progress-striped" style="margin-bottom: 9px;">
              <div class="bar" style="width: 20%"></div>
            </div>
            <div class="progress progress-success progress-striped" style="margin-bottom: 9px;">
              <div class="bar" style="width: 40%"></div>
            </div>
            <div class="progress progress-warning progress-striped" style="margin-bottom: 9px;">
              <div class="bar" style="width: 60%"></div>
            </div>
            <div class="progress progress-danger progress-striped">
              <div class="bar" style="width: 80%"></div>
            </div>
          </div>
<pre class="prettyprint linenums">
&lt;div class="progress progress-info progress-striped"&gt;
  &lt;div class="bar" style="width: 20%"&gt;&lt;/div&gt;
&lt;/div&gt;
&lt;div class="progress progress-success progress-striped"&gt;
  &lt;div class="bar" style="width: 40%"&gt;&lt;/div&gt;
&lt;/div&gt;
&lt;div class="progress progress-warning progress-striped"&gt;
  &lt;div class="bar" style="width: 60%"&gt;&lt;/div&gt;
&lt;/div&gt;
&lt;div class="progress progress-danger progress-striped"&gt;
  &lt;div class="bar" style="width: 80%"&gt;&lt;/div&gt;
&lt;/div&gt;
</pre>


          <hr class="bs-docs-separator">


          <h2>{{_i}}Browser support{{/i}}</h2>
          <p>{{_i}}Progress bars use CSS3 gradients, transitions, and animations to achieve all their effects. These features are not supported in IE7-9 or older versions of Firefox.{{/i}}</p>
          <p>{{_i}}Versions earlier than Internet Explorer 10 and Opera 12 do not support animations.{{/i}}</p>

        </section>




        <!-- Media object
        ================================================== -->
        <section id="media">
          <div class="page-header">
            <h1>{{_i}}Media object{{/i}}</h1>
          </div>
          <p class="lead">{{_i}}Abstract object styles for building various types of components (like blog comments, Tweets, etc) that feature a left- or right-aligned image alongside textual content.{{/i}}</p>

          <h2>{{_i}}Default example{{/i}}</h2>
          <p>{{_i}}The default media allow to float a media object (images, video, audio) to the left or right of a content block.{{/i}}</p>
          <div class="bs-docs-example">
            <div class="media">
              <a class="pull-left" href="#">
                <img class="media-object" data-src="holder.js/64x64">
              </a>
              <div class="media-body">
                <h4 class="media-heading">{{_i}}Media heading{{/i}}</h4>
                Cras sit amet nibh libero, in gravida nulla. Nulla vel metus scelerisque ante sollicitudin commodo. Cras purus odio, vestibulum in vulputate at, tempus viverra turpis. Fusce condimentum nunc ac nisi vulputate fringilla. Donec lacinia congue felis in faucibus.
              </div>
            </div>
            <div class="media">
              <a class="pull-left" href="#">
                <img class="media-object" data-src="holder.js/64x64">
              </a>
              <div class="media-body">
                <h4 class="media-heading">{{_i}}Media heading{{/i}}</h4>
                Cras sit amet nibh libero, in gravida nulla. Nulla vel metus scelerisque ante sollicitudin commodo. Cras purus odio, vestibulum in vulputate at, tempus viverra turpis. Fusce condimentum nunc ac nisi vulputate fringilla. Donec lacinia congue felis in faucibus.
                <div class="media">
                  <a class="pull-left" href="#">
                    <img class="media-object" data-src="holder.js/64x64">
                  </a>
                  <div class="media-body">
                    <h4 class="media-heading">{{_i}}Media heading{{/i}}</h4>
                    Cras sit amet nibh libero, in gravida nulla. Nulla vel metus scelerisque ante sollicitudin commodo. Cras purus odio, vestibulum in vulputate at, tempus viverra turpis. Fusce condimentum nunc ac nisi vulputate fringilla. Donec lacinia congue felis in faucibus.
                  </div>
                </div>
              </div>
            </div>
          </div>{{! /.bs-docs-example }}
<pre class="prettyprint linenums">
&lt;div class="media"&gt;
  &lt;a class="pull-left" href="#"&gt;
    &lt;img class="media-object" data-src="holder.js/64x64"&gt;
  &lt;/a&gt;
  &lt;div class="media-body"&gt;
    &lt;h4 class="media-heading"&gt;{{_i}}Media heading{{/i}}&lt;/h4&gt;
    ...

    &lt;!-- Nested media object --&gt;
    &lt;div class="media"&gt;
      ...
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/div&gt;
</pre>


          <hr class="bs-docs-separator">


          <h2>{{_i}}Media list{{/i}}</h2>
          <p>{{_i}}With a bit of extra markup, you can use media inside list (useful for comment threads or articles lists).{{/i}}</p>
          <div class="bs-docs-example">
            <ul class="media-list">
              <li class="media">
                <a class="pull-left" href="#">
                  <img class="media-object" data-src="holder.js/64x64">
                </a>
                <div class="media-body">
                  <h4 class="media-heading">{{_i}}Media heading{{/i}}</h4>
                  <p>Cras sit amet nibh libero, in gravida nulla. Nulla vel metus scelerisque ante sollicitudin commodo. Cras purus odio, vestibulum in vulputate at, tempus viverra turpis.</p>
                  <!-- Nested media object -->
                  <div class="media">
                    <a class="pull-left" href="#">
                      <img class="media-object" data-src="holder.js/64x64">
                    </a>
                    <div class="media-body">
                      <h4 class="media-heading">{{_i}}Nested media heading{{/i}}</h4>
                      Cras sit amet nibh libero, in gravida nulla. Nulla vel metus scelerisque ante sollicitudin commodo. Cras purus odio, vestibulum in vulputate at, tempus viverra turpis.
                      <!-- Nested media object -->
                      <div class="media">
                        <a class="pull-left" href="#">
                          <img class="media-object" data-src="holder.js/64x64">
                        </a>
                        <div class="media-body">
                          <h4 class="media-heading">{{_i}}Nested media heading{{/i}}</h4>
                          Cras sit amet nibh libero, in gravida nulla. Nulla vel metus scelerisque ante sollicitudin commodo. Cras purus odio, vestibulum in vulputate at, tempus viverra turpis.
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- Nested media object -->
                  <div class="media">
                    <a class="pull-left" href="#">
                      <img class="media-object" data-src="holder.js/64x64">
                    </a>
                    <div class="media-body">
                      <h4 class="media-heading">{{_i}}Nested media heading{{/i}}</h4>
                      Cras sit amet nibh libero, in gravida nulla. Nulla vel metus scelerisque ante sollicitudin commodo. Cras purus odio, vestibulum in vulputate at, tempus viverra turpis.
                    </div>
                  </div>
                </div>
              </li>
              <li class="media">
                <a class="pull-right" href="#">
                  <img class="media-object" data-src="holder.js/64x64">
                </a>
                <div class="media-body">
                  <h4 class="media-heading">{{_i}}Media heading{{/i}}</h4>
                  Cras sit amet nibh libero, in gravida nulla. Nulla vel metus scelerisque ante sollicitudin commodo. Cras purus odio, vestibulum in vulputate at, tempus viverra turpis.
                </div>
              </li>
            </ul>
          </div>
<pre class="prettyprint linenums">
&lt;ul class="media-list"&gt;
  &lt;li class="media"&gt;
    &lt;a class="pull-left" href="#"&gt;
      &lt;img class="media-object" data-src="holder.js/64x64"&gt;
    &lt;/a&gt;
    &lt;div class="media-body"&gt;
      &lt;h4 class="media-heading"&gt;{{_i}}Media heading{{/i}}&lt;/h4&gt;
      ...

      &lt;!-- Nested media object --&gt;
      &lt;div class="media"&gt;
        ...
     &lt;/div&gt;
    &lt;/div&gt;
  &lt;/li&gt;
&lt;/ul&gt;
</pre>

</section>





        <!-- Miscellaneous
        ================================================== -->
        <section id="misc">
          <div class="page-header">
            <h1>{{_i}}Miscellaneous{{/i}} <small>{{_i}}Lightweight utility components{{/i}}</small></h1>
          </div>

          <h2>{{_i}}Wells{{/i}}</h2>
          <p>{{_i}}Use the well as a simple effect on an element to give it an inset effect.{{/i}}</p>
          <div class="bs-docs-example">
            <div class="well">
              {{_i}}Look, I'm in a well!{{/i}}
            </div>
          </div>
<pre class="prettyprint linenums">
&lt;div class="well"&gt;
  ...
&lt;/div&gt;
</pre>
          <h3>{{_i}}Optional classes{{/i}}</h3>
          <p>{{_i}}Control padding and rounded corners with two optional modifier classes.{{/i}}</p>
          <div class="bs-docs-example">
            <div class="well well-large">
              {{_i}}Look, I'm in a well!{{/i}}
            </div>
          </div>
<pre class="prettyprint linenums">
&lt;div class="well well-large"&gt;
  ...
&lt;/div&gt;
</pre>
          <div class="bs-docs-example">
            <div class="well well-small">
              {{_i}}Look, I'm in a well!{{/i}}
            </div>
          </div>
<pre class="prettyprint linenums">
&lt;div class="well well-small"&gt;
  ...
&lt;/div&gt;
</pre>

          <h2>{{_i}}Close icon{{/i}}</h2>
          <p>{{_i}}Use the generic close icon for dismissing content like modals and alerts.{{/i}}</p>
          <div class="bs-docs-example">
            <p><button class="close" style="float: none;">&times;</button></p>
          </div>
          <pre class="prettyprint linenums">&lt;button class="close"&gt;&amp;times;&lt;/button&gt;</pre>
          <p>{{_i}}iOS devices require an <code>href="#"</code> for click events if you would rather use an anchor.{{/i}}</p>
          <pre class="prettyprint linenums">&lt;a class="close" href="#"&gt;&amp;times;&lt;/a&gt;</pre>

          <h2>{{_i}}Helper classes{{/i}}</h2>
          <p>{{_i}}Simple, focused classes for small display or behavior tweaks.{{/i}}</p>

          <h4>{{_i}}.pull-left{{/i}}</h4>
          <p>{{_i}}Float an element left{{/i}}</p>
<pre class="prettyprint linenums">
class="pull-left"
</pre>
<pre class="prettyprint linenums">
.pull-left {
  float: left;
}
</pre>

          <h4>{{_i}}.pull-right{{/i}}</h4>
          <p>{{_i}}Float an element right{{/i}}</p>
<pre class="prettyprint linenums">
class="pull-right"
</pre>
<pre class="prettyprint linenums">
.pull-right {
  float: right;
}
</pre>

          <h4>{{_i}}.muted{{/i}}</h4>
          <p>{{_i}}Change an element's color to <code>#999</code>{{/i}}</p>
<pre class="prettyprint linenums">
class="muted"
</pre>
<pre class="prettyprint linenums">
.muted {
  color: #999;
}
</pre>

          <h4>{{_i}}.clearfix{{/i}}</h4>
          <p>{{_i}}Clear the <code>float</code> on any element{{/i}}</p>
<pre class="prettyprint linenums">
class="clearfix"
</pre>
<pre class="prettyprint linenums">
.clearfix {
  *zoom: 1;
  &:before,
  &:after {
    display: table;
    content: "";
  }
  &:after {
    clear: both;
  }
}
</pre>

        </section>



      </div>{{! /span9 }}
    </div>{{! row}}

  </div>{{! /.container }}
