!function(e,t){"use strict";function n(e,t){for(var n,r=[],i=0;i<e.length;++i){if(n=s[e[i]]||o(e[i]),!n)throw"module definition dependecy not found: "+e[i];r.push(n)}t.apply(null,r)}function r(e,r,i){if("string"!=typeof e)throw"invalid module definition, module id must be defined and be a string";if(r===t)throw"invalid module definition, dependencies must be specified";if(i===t)throw"invalid module definition, definition function must be specified";n(r,function(){s[e]=i.apply(null,arguments)})}function i(e){return!!s[e]}function o(t){for(var n=e,r=t.split(/[.\/]/),i=0;i<r.length;++i){if(!n[r[i]])return;n=n[r[i]]}return n}function a(n){for(var r=0;r<n.length;r++){for(var i=e,o=n[r],a=o.split(/[.\/]/),l=0;l<a.length-1;++l)i[a[l]]===t&&(i[a[l]]={}),i=i[a[l]];i[a[a.length-1]]=s[o]}}var s={},l="tinymce/pasteplugin/Utils",c="tinymce/util/Tools",d="tinymce/html/DomParser",u="tinymce/html/Schema",f="tinymce/pasteplugin/Clipboard",p="tinymce/Env",m="tinymce/util/VK",h="tinymce/pasteplugin/WordFilter",g="tinymce/html/Serializer",v="tinymce/html/Node",y="tinymce/pasteplugin/Quirks",b="tinymce/pasteplugin/Plugin",C="tinymce/PluginManager";r(l,[c,d,u],function(e,t,n){function r(t,n){return e.each(n,function(e){t=e.constructor==RegExp?t.replace(e,""):t.replace(e[0],e[1])}),t}function i(r){function i(e){var t=e.name,n=e;if("br"===t)return void(s+="\n");if(l[t]&&(s+=" "),c[t])return void(s+=" ");if(3==e.type&&(s+=e.value),!e.shortEnded&&(e=e.firstChild))do i(e);while(e=e.next);d[t]&&n.next&&(s+="\n","p"==t&&(s+="\n"))}var o=new n,a=new t({},o),s="",l=o.getShortEndedElements(),c=e.makeMap("script noscript style textarea video audio iframe object"," "),d=o.getBlockElements();return i(a.parse(r)),s}return{filter:r,innerText:i}}),r(f,[p,m,l],function(e,t,n){return function(r){function i(e){var t,n=r.dom;if(t=r.fire("BeforePastePreProcess",{content:e}),t=r.fire("PastePreProcess",t),e=t.content,!t.isDefaultPrevented()){if(r.hasEventListeners("PastePostProcess")&&!t.isDefaultPrevented()){var i=n.add(r.getBody(),"div",{style:"display:none"},e);t=r.fire("PastePostProcess",{node:i}),n.remove(i),e=t.node.innerHTML}t.isDefaultPrevented()||r.insertContent(e)}}function o(e){e=r.dom.encode(e).replace(/\r\n/g,"\n");var t=r.dom.getParent(r.selection.getStart(),r.dom.isBlock),o=r.settings.forced_root_block,a;o&&(a=r.dom.createHTML(o,r.settings.forced_root_block_attrs),a=a.substr(0,a.length-3)+">"),t&&/^(PRE|DIV)$/.test(t.nodeName)||!o?e=n.filter(e,[[/\n/g,"<br>"]]):(e=n.filter(e,[[/\n\n/g,"</p>"+a],[/^(.*<\/p>)(<p>)$/,a+"$1"],[/\n/g,"<br />"]]),-1!=e.indexOf("<p>")&&(e=a+e)),i(e)}function a(){var t=r.dom,n=r.getBody(),i=r.dom.getViewPort(r.getWin()),o=i.y,a=20,s;if(v=r.selection.getRng(),r.inline&&(s=r.selection.getScrollContainer(),s&&(o=s.scrollTop)),v.getClientRects){var l=v.getClientRects();if(l.length)a=o+(l[0].top-t.getPos(n).y);else{a=o;var c=v.startContainer;c&&(3==c.nodeType&&c.parentNode!=n&&(c=c.parentNode),1==c.nodeType&&(a=t.getPos(c,s||n).y))}}g=t.add(r.getBody(),"div",{id:"mcepastebin",contentEditable:!0,"data-mce-bogus":"1",style:"position: absolute; top: "+a+"px;width: 10px; height: 10px; overflow: hidden; opacity: 0"},b),(e.ie||e.gecko)&&t.setStyle(g,"left","rtl"==t.getStyle(n,"direction",!0)?65535:-65535),t.bind(g,"beforedeactivate focusin focusout",function(e){e.stopPropagation()}),g.focus(),r.selection.select(g,!0)}function s(){if(g){for(var e;e=r.dom.get("mcepastebin");)r.dom.remove(e),r.dom.unbind(e);v&&r.selection.setRng(v)}C=!1,g=v=null}function l(){var e=b,t,n;for(t=r.dom.select("div[id=mcepastebin]"),n=t.length;n--;){var i=t[n].innerHTML;e==b&&(e=""),i.length>e.length&&(e=i)}return e}function c(e){var t={};if(e&&e.types){var n=e.getData("Text");n&&n.length>0&&(t["text/plain"]=n);for(var r=0;r<e.types.length;r++){var i=e.types[r];t[i]=e.getData(i)}}return t}function d(e){return c(e.clipboardData||r.getDoc().dataTransfer)}function u(e,t){function n(e){if("image/png"==o[a].type){var t=new FileReader;return t.onload=function(){i('<img src="'+t.result+'">')},t.readAsDataURL(e.getAsFile()),!0}}if(!(!r.settings.paste_data_images||"text/html"in t||"text/plain"in t)&&e.clipboardData){var o=e.clipboardData.items;if(o)for(var a=0;a<o.length;a++)if(n(o[a]))return!0}}function f(e){var t=r.getDoc(),n;if(t.caretPositionFromPoint){var i=t.caretPositionFromPoint(e.clientX,e.clientY);n=t.createRange(),n.setStart(i.offsetNode,i.offset),n.collapse(!0)}else t.caretRangeFromPoint&&(n=t.caretRangeFromPoint(e.clientX,e.clientY));return n}function p(e,t){return t in e&&e[t].length>0}function m(){r.on("keydown",function(n){if(!n.isDefaultPrevented()&&(t.metaKeyPressed(n)&&86==n.keyCode||n.shiftKey&&45==n.keyCode)){if(C=n.shiftKey&&86==n.keyCode,n.stopImmediatePropagation(),y=(new Date).getTime(),e.ie&&C)return n.preventDefault(),void r.fire("paste",{ieFake:!0});s(),a()}}),r.on("paste",function(t){var c=d(t),f=(new Date).getTime()-y<1e3,m="text"==h.pasteFormat||C;return t.isDefaultPrevented()?void s():u(t,c)?void s():(f||t.preventDefault(),!e.ie||f&&!t.ieFake||(a(),r.dom.bind(g,"paste",function(e){e.stopPropagation()}),r.getDoc().execCommand("Paste",!1,null),c["text/html"]=l()),void setTimeout(function(){var e=l();return g&&g.firstChild&&"mcepastebin"===g.firstChild.id&&(m=!0),s(),!m&&f&&e&&e!=b&&(c["text/html"]=e),e!=b&&f||(e=c["text/html"]||c["text/plain"]||b,e!=b)?(!p(c,"text/html")&&p(c,"text/plain")&&(m=!0),void(m?o(c["text/plain"]||n.innerText(e)):i(e))):void(f||r.windowManager.alert("Please use Ctrl+V/Cmd+V keyboard shortcuts to paste contents."))},0))}),r.on("dragstart",function(e){if(e.dataTransfer.types)try{e.dataTransfer.setData("mce-internal",r.selection.getContent())}catch(t){}}),r.on("drop",function(e){var t=f(e);if(t&&!e.isDefaultPrevented()){var n=c(e.dataTransfer),a=n["mce-internal"]||n["text/html"]||n["text/plain"];a&&(e.preventDefault(),r.undoManager.transact(function(){n["mce-internal"]&&r.execCommand("Delete"),r.selection.setRng(t),n["text/html"]?i(a):o(a)}))}})}var h=this,g,v,y=0,b="%MCEPASTEBIN%",C;h.pasteHtml=i,h.pasteText=o,r.on("preInit",function(){m(),r.parser.addNodeFilter("img",function(t){if(!r.settings.paste_data_images)for(var n=t.length;n--;){var i=t[n].attributes.map.src;i&&0===i.indexOf("data:image")&&(t[n].attr("data-mce-object")||i===e.transparentSrc||t[n].remove())}})}),r.on("PreProcess",function(){r.dom.remove(r.dom.get("mcepastebin"))})}}),r(h,[c,d,u,g,v,l],function(e,t,n,r,i,o){function a(e){return/<font face="Times New Roman"|class="?Mso|style="[^"]*\bmso-|style='[^'']*\bmso-|w:WordDocument/i.test(e)||/class="OutlineElement/.test(e)||/id="?docs\-internal\-guid\-/.test(e)}function s(s){var l=s.settings;s.on("BeforePastePreProcess",function(c){function d(e){function t(e,t,a,s){var l=e._listLevel||o;l!=o&&(o>l?n&&(n=n.parent.parent):(r=n,n=null)),n&&n.name==a?n.append(e):(r=r||n,n=new i(a,1),s>1&&n.attr("start",""+s),e.wrap(n)),e.name="li",t.value="";var c=t.next;c&&3==c.type&&(c.value=c.value.replace(/^\u00a0+/,"")),l>o&&r&&r.lastChild.append(n),o=l}for(var n,r,o=1,a=e.getAll("p"),s=0;s<a.length;s++)if(e=a[s],"p"==e.name&&e.firstChild){for(var l="",c=e.firstChild;c&&!(l=c.value);)c=c.firstChild;if(/^\s*[\u2022\u00b7\u00a7\u00d8\u25CF]\s*$/.test(l)){t(e,c,"ul");continue}if(/^\s*\w+\.$/.test(l)){var d=/([0-9])\./.exec(l),u=1;d&&(u=parseInt(d[1],10)),t(e,c,"ol",u);continue}n=null}}function u(t,n){var r={},o=s.dom.parseStyle(n);if("p"===t.name){var a=/mso-list:\w+ \w+([0-9]+)/.exec(n);a&&(t._listLevel=parseInt(a[1],10))}return e.each(o,function(e,n){switch(n){case"horiz-align":n="text-align";break;case"vert-align":n="vertical-align";break;case"font-color":case"mso-foreground":n="color";break;case"mso-background":case"mso-highlight":n="background";break;case"font-weight":case"font-style":return void("normal"!=e&&(r[n]=e));case"mso-element":if(/^(comment|comment-list)$/i.test(e))return void t.remove()}return 0===n.indexOf("mso-comment")?void t.remove():void(0!==n.indexOf("mso-")&&("all"==p||m&&m[n])&&(r[n]=e))}),/(bold)/i.test(r["font-weight"])&&(delete r["font-weight"],t.wrap(new i("b",1))),/(italic)/i.test(r["font-style"])&&(delete r["font-style"],t.wrap(new i("i",1))),r=s.dom.serializeStyle(r,t.name),r?r:null}var f=c.content,p,m;if(p=l.paste_retain_style_properties,p&&(m=e.makeMap(p.split(/[, ]/))),l.paste_enable_default_filters!==!1&&a(c.content)){c.wordContent=!0,f=o.filter(f,[/<!--[\s\S]+?-->/gi,/<(!|script[^>]*>.*?<\/script(?=[>\s])|\/?(\?xml(:\w+)?|img|meta|link|style|\w:\w+)(?=[\s\/>]))[^>]*>/gi,[/<(\/?)s>/gi,"<$1strike>"],[/&nbsp;/gi,"\xa0"],[/<span\s+style\s*=\s*"\s*mso-spacerun\s*:\s*yes\s*;?\s*"\s*>([\s\u00a0]*)<\/span>/gi,function(e,t){return t.length>0?t.replace(/./," ").slice(Math.floor(t.length/2)).split("").join("\xa0"):""}]]);var h=l.paste_word_valid_elements;h||(h="-strong/b,-em/i,-span,-p,-ol,-ul,-li,-h1,-h2,-h3,-h4,-h5,-h6,-p/div,-table[width],-tr,-td[colspan|rowspan|width],-th,-thead,-tfoot,-tbody,-a[href|name],sub,sup,strike,br,del");var g=new n({valid_elements:h,valid_children:"-li[p]"});e.each(g.elements,function(e){e.attributes["class"]||(e.attributes["class"]={},e.attributesOrder.push("class")),e.attributes.style||(e.attributes.style={},e.attributesOrder.push("style"))});var v=new t({},g);v.addAttributeFilter("style",function(e){for(var t=e.length,n;t--;)n=e[t],n.attr("style",u(n,n.attr("style"))),"span"==n.name&&n.parent&&!n.attributes.length&&n.unwrap()}),v.addAttributeFilter("class",function(e){for(var t=e.length,n,r;t--;)n=e[t],r=n.attr("class"),/^(MsoCommentReference|MsoCommentText|msoDel)$/i.test(r)&&n.remove(),n.attr("class",null)}),v.addNodeFilter("del",function(e){for(var t=e.length;t--;)e[t].remove()}),v.addNodeFilter("a",function(e){for(var t=e.length,n,r,i;t--;)if(n=e[t],r=n.attr("href"),i=n.attr("name"),r&&-1!=r.indexOf("#_msocom_"))n.remove();else if(r&&0===r.indexOf("file://")&&(r=r.split("#")[1],r&&(r="#"+r)),r||i){if(i&&!/^_?(?:toc|edn|ftn)/i.test(i)){n.unwrap();continue}n.attr({href:r,name:i})}else n.unwrap()});var y=v.parse(f);d(y),c.content=new r({},g).serialize(y)}})}return s.isWordContent=a,s}),r(y,[p,c,h,l],function(e,t,n,r){return function(i){function o(e){i.on("BeforePastePreProcess",function(t){t.content=e(t.content)})}function a(e){return e=r.filter(e,[/^[\s\S]*<body[^>]*>\s*<!--StartFragment-->|<!--EndFragment-->\s*<\/body[^>]*>[\s\S]*$/g,/<!--StartFragment-->|<!--EndFragment-->/g,[/<span class="Apple-converted-space">\u00a0<\/span>/g,"\xa0"],/<br>$/i])}function s(e){if(!n.isWordContent(e))return e;var o=[];t.each(i.schema.getBlockElements(),function(e,t){o.push(t)});var a=new RegExp("(?:<br>&nbsp;[\\s\\r\\n]+|<br>)*(<\\/?("+o.join("|")+")[^>]*>)(?:<br>&nbsp;[\\s\\r\\n]+|<br>)*","g");return e=r.filter(e,[[a,"$1"]]),e=r.filter(e,[[/<br><br>/g,"<BR><BR>"],[/<br>/g," "],[/<BR><BR>/g,"<br>"]])}function l(e){if(n.isWordContent(e))return e;var t=i.settings.paste_webkit_styles;if(i.settings.paste_remove_styles_if_webkit===!1||"all"==t)return e;if(t&&(t=t.split(/[, ]/)),t){var r=i.dom,o=i.selection.getNode();e=e.replace(/ style=\"([^\"]+)\"/gi,function(e,n){var i=r.parseStyle(n,"span"),a={};if("none"===t)return"";for(var s=0;s<t.length;s++){var l=i[t[s]],c=r.getStyle(o,t[s],!0);/color/.test(t[s])&&(l=r.toHex(l),c=r.toHex(c)),c!=l&&(a[t[s]]=l)}return a=r.serializeStyle(a,"span"),a?' style="'+a+'"':""})}else e=e.replace(/ style=\"[^\"]+\"/gi,"");return e=e.replace(/ data-mce-style=\"([^\"]+)\"/gi,function(e,t){return' style="'+t+'"'+e})}e.webkit&&(o(l),o(a)),e.ie&&o(s)}}),r(b,[C,f,h,y],function(e,t,n,r){var i;e.add("paste",function(e){function o(){"text"==s.pasteFormat?(this.active(!1),s.pasteFormat="html"):(s.pasteFormat="text",this.active(!0),i||(e.windowManager.alert("Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off."),i=!0))}var a=this,s,l=e.settings;a.clipboard=s=new t(e),a.quirks=new r(e),a.wordFilter=new n(e),e.settings.paste_as_text&&(a.clipboard.pasteFormat="text"),l.paste_preprocess&&e.on("PastePreProcess",function(e){l.paste_preprocess.call(a,a,e)}),l.paste_postprocess&&e.on("PastePostProcess",function(e){l.paste_postprocess.call(a,a,e)}),e.addCommand("mceInsertClipboardContent",function(e,t){t.content&&a.clipboard.pasteHtml(t.content),t.text&&a.clipboard.pasteText(t.text)}),e.paste_block_drop&&e.on("dragend dragover draggesture dragdrop drop drag",function(e){e.preventDefault(),e.stopPropagation()}),e.settings.paste_data_images||e.on("drop",function(e){var t=e.dataTransfer;t&&t.files&&t.files.length>0&&e.preventDefault()}),e.addButton("pastetext",{icon:"pastetext",tooltip:"Paste as text",onclick:o,active:"text"==a.clipboard.pasteFormat}),e.addMenuItem("pastetext",{text:"Paste as text",selectable:!0,active:s.pasteFormat,onclick:o})})}),a([l,f,h,y,b])}(this);