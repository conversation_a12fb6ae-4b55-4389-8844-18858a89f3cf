-- timeslip sync token.
insert into `config` (`created`, `key`, `value`, `modified`)
values (now(), 'account_timeslip_sync_token', '', now());

-- `timeslip` table.
drop table if exists `accounttimeslip`;
create table `accounttimeslip`
(
    `account_timeslip_code` varchar(191),
    `account_user_code`     varchar(255),
    `account_project_code`  varchar(255),
    `account_task_code`     varchar(255),
    `account_client_code`   varchar(255),
    `email`                 varchar(191),
    `hours`                 float unsigned   not null,
    `is_billable`           tinyint(1)       not null,
    `billable_rate`         double           not null,
    `created`               datetime(6)      not null,
    `modified`              datetime(6)      not null,
    `created_at`            datetime         not null,
    `updated_at`            datetime         not null,
    `comment`               text,
    `client_id`             int(11) unsigned null,
    `channel_id`            int(11) unsigned null,
    `source_id`             int(11) unsigned null,
    `fulfillmentservice_id` int(11) unsigned null,
    `estimate_id`           int(11) unsigned null,
    `ticket_id`             int(11) unsigned null,
    `department`            varchar(255),
    `action`                varchar(255),
    primary key (`account_timeslip_code`),
    index `index_accounttimeslip_client_id` (`client_id`),
    index `index_accounttimeslip_account_timeslip_code` (`account_timeslip_code`),
    index `index_accounttimeslip_email` (`email`),
    index `index_accounttimeslip_created_at` (`created_at`)
) engine = innodb
  default charset = utf8mb4
  collate = utf8mb4_unicode_ci
;