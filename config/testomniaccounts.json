{"version": "v3", "server": {"host": "localhost", "debug": false, "port": 13237, "logFile": "connector.log", "threaded": false}, "updates": {"domain": "apifact.com", "username": "testomniaccounts", "password": "asdf"}, "packages": [{"package": "gomedia_omniaccounts", "config": "config_testomniaccounts", "module": "api_testomniaccounts", "class": "Api", "route": "omniaccounts"}], "tunnel": {"config": {"serverAddr": "www.apifact.com:8080", "trustHostRootCerts": true, "inspectAddr": "localhost:8888", "tunnels": [{"host": "testomniaccounts", "auth": "testomniaccounts:asdf", "proto": "https"}]}}}