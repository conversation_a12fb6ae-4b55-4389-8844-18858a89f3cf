{"version": "v3", "server": {"host": "localhost", "debug": false, "port": 13337, "logFile": "connector.log", "threaded": false}, "updates": {"domain": "apifact.com", "username": "dupaintandtools", "password": "colour-brush-linseed"}, "packages": [{"package": "gomedia_evolution", "config": "config_dupaintandtools", "module": "api_dupaintandtools", "class": "Api", "route": "evolution"}], "tunnel": {"config": {"serverAddr": "www.apifact.com:8080", "trustHostRootCerts": true, "inspectAddr": "localhost:8888", "tunnels": [{"host": "dupaintandtools", "auth": "dupaintandtools:colour-brush-linseed", "proto": "https"}]}}}