{"version": "v3", "server": {"host": "localhost", "debug": false, "port": 1337, "logFile": "connector.log", "threaded": false}, "updates": {"domain": "*************", "username": "stable", "password": "indigent-beshrew-cygnet"}, "packages": [{"package": "gomedia_evolution", "config": "config_stable", "module": "api_stable", "class": "Api", "route": "evolution"}], "tunnel": {"config": {"serverAddr": "www.apifact.com:8080", "trustHostRootCerts": true, "inspectAddr": "localhost:8888", "tunnels": [{"host": "stable", "auth": "stable:indigent-beshrew-cygnet", "proto": "https"}]}}}