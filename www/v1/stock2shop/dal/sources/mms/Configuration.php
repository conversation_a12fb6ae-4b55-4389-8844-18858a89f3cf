<?php

namespace stock2shop\dal\sources\mms;

use Exception;
use stock2shop\dal\connector\ConnectorConfiguration;
use stock2shop\dal\oauth2\Constants;
use stock2shop\dal\source\ProductsIngressMapperArgs;
use stock2shop\exceptions;
use stock2shop\helpers\Singleton;
use stock2shop\lib;

class Configuration extends Singleton
{
    /**
     * @var Configuration
     */
    private static $instance;

    /**
     * @param bool $freshStart If true, a new instance will be created - should only be used by unit tests.
     * @return Configuration
     */
    public static function getInstance(bool $freshStart = false): Configuration
    {
        if ($freshStart || !isset(self::$instance)) {
            self::$instance = new static();
        }
        return self::$instance;
    }

    public static function clearInstance()
    {
        self::$instance = null;
    }

    const DATE_FORMAT = 'Ymd';
    const STORE_CODE = '7420';
    const STORE_VIEW_ID = 1;
    const TRANSACTION_DELIVERY_METHOD_HOME = 'home';
    const TRANSACTION_DELIVERY_METHOD_STORE = 'store';
    const TRANSACTION_TYPE_INVOICE = 'invoice';
    const TRANSACTION_TYPE_CREDIT_NOTE = 'creditnote';
    const COMPANY_ID = 1;

    const CREDIT_NOTE_REFUND_TYPE_CREDIT = 'credit';
    const CREDIT_NOTE_SHORT_PICKED = 'short_picked';
    const CREDIT_NOTE_ZERO_PICKED_CONFIRMED = 'zero_picked_confirmed';

    const INVOICE_TAX_RULE_NAME = 'VAT';
    const INVOICE_DISCOUNT_CODE = '8270';
    const INVOICE_DISCOUNT_DESCRIPTION = 'Test';
    const INVOICE_ADDITIONAL_CHARGE_DESCRIPTION_SHIPPING = 'Shipping';
    const INVOICE_ADDITIONAL_DISCOUNT_DESCRIPTION = 'Additional Discount';
    const INVOICE_PAYMENT_METHOD_WALLET = 'wallet';
    const INVOICE_PAYMENT_PROVIDER_PEACH = 'peachpayments_hosted_card';
    const INVOICE_PAYMENT_DEFAULT_RESULT_CODE = '000.000.000';
    const INVOICE_STORE_CREDIT_NOTE_REASON_REDEMPTION = 'Redemption';

    const BASE_URL_KEY = 'base_url';
    const TOKEN_ROUTE = '/oauth2/v1/token';
    const PACKSLIP_ACK_ROUTE = '/pep-mms-inbound-queue-v1/ecomPackSlipAck';
    const STOCK_ADJUSTMENT_ROUTE = '/pep-mms-inbound-queue-v1/ecomStockAdj';

    const BARCODE_ACTIVE_INDICATOR = 'Y';

    const VAT_AS_PERCENTAGE = 15;

    // Inbound constants
    const FROM_BRANCH_NO = 'FromBranchNo';
    const TO_BRANCH_NO   = 'ToBranchNo';

    // Additional constants for RefundToCreditNote
    const CODE_DISCREPANCY = 'discrep';
    const MANUAL_COMMENT = 'manual';
    const STATUS_TYPE_ZERO_PICKED_CONFIRMED = 'zero_picked_confirmed';
    const STATUS_TYPE_SHORT_PICKED = 'short_picked';
    const STATUS_TYPE_CANCELLED = 'cancelled';
    const REFUND_PROVIDER_ZERO_CREDIT = 'zero credit';
    const REFUND_PROVIDER_CUSTOMER_CREDIT = 'customer credit';
    const DEFAULT_RESULT_CODE = '000';
    const DEFAULT_NONE = 'None';

    // Payment method constants
    const PAYMENT_METHOD_STORE_CREDIT = 'store_credit';
    const PAYMENT_METHOD_WALLET = 'wallet';

    // Payment provider constants
    const PAYMENT_PROVIDER_PEACH_APLUS = 'peachpayments_hosted_aplus';
    const PAYMENT_PROVIDER_PEACH_CARD = 'peachpayments_hosted_card';
    const PAYMENT_PROVIDER_SHOPIFY_STORE_CREDIT = 'shopify_store_credit';

    const STOCK_ADJ_FROM_BRANCH_NO = 'stock_adj_from_branch_no';

    const DEFAULT_PRODUCT_FIELD_MAP = [
        'product_ingress_field_source_product_code'   => MMSProductFields::COLUMN_STYLE_STYLE_CODE,
        'product_ingress_field_collection'            => MMSProductFields::COLUMN_STYLE_FAMILY_NAME,
        'product_ingress_field_title'                 => MMSProductFields::COLUMN_STYLE_STYLE_DESC,
        'product_ingress_field_body_html'             => MMSProductFields::COLUMN_STYLE_STYLE_SHORT_DESC,
        'product_ingress_field_vendor'                => MMSProductFields::COLUMN_STYLE_BRAND_NAME,
        'product_ingress_field_sku'                   => MMSProductFields::COLUMN_SKU_SKU_NO,
        'product_ingress_field_source_variant_code'   => MMSProductFields::COLUMN_SKU_SKU_NO,
        'product_ingress_field_price'                 => MMSProductFields::COLUMN_SKU_SKU_SELL_PRICES_CURR_LINC_SELL_PRC_CURRENT,
        'product_ingress_field_price_was'             => MMSProductFields::COLUMN_SKU_SKU_SELL_PRICES_PREV_LINC_SELL_PRC_CURRENT,
        'product_ingress_field_option_colour'         => MMSProductFields::COLUMN_SKU_COLOUR_DESC,
        'product_ingress_field_option_size'           => MMSProductFields::COLUMN_SKU_PROD_SIZE_NAME,
        'product_ingress_field_variant_meta_barcodes' => MMSProductFields::COLUMN_SKU_SKU_BAR_CODE_SKU_BAR_CODE_NO_CSV,

        // Product Style Meta Fields
        'product_ingress_field_meta_activeInd'                    => MMSProductFields::COLUMN_STYLE_ACTIVE_IND,
        'product_ingress_field_meta_buyerStyleRefCode'            => MMSProductFields::COLUMN_STYLE_BUYER_STYLE_REF_CODE,
        'product_ingress_field_meta_cardProdInd'                  => MMSProductFields::COLUMN_STYLE_CARD_PROD_IND,
        'product_ingress_field_meta_cashForSchoolInd'             => MMSProductFields::COLUMN_STYLE_CASH_FOR_SCHOOL_IND,
        'product_ingress_field_meta_cellAudioDesc'                => MMSProductFields::COLUMN_STYLE_CELL_AUDIO_DESC,
        'product_ingress_field_meta_cellFeatureTypeDesc'          => MMSProductFields::COLUMN_STYLE_CELL_FEATURE_TYPE_DESC,
        'product_ingress_field_meta_cellManufacturerName'         => MMSProductFields::COLUMN_STYLE_CELL_MANUFACTURER_NAME,
        'product_ingress_field_meta_cellMemoryDesc'               => MMSProductFields::COLUMN_STYLE_CELL_MEMORY_DESC,
        'product_ingress_field_meta_cellPowerSupplyDesc'          => MMSProductFields::COLUMN_STYLE_CELL_POWER_SUPPLY_DESC,
        'product_ingress_field_meta_companyIdNo'                  => MMSProductFields::COLUMN_STYLE_COMPANY_ID_NO,
        'product_ingress_field_meta_customsUnitMeasureDesc'       => MMSProductFields::COLUMN_STYLE_CUSTOMS_UNIT_MEASURE_DESC,
        'product_ingress_field_meta_darkStoreStockAvailability'   => MMSProductFields::COLUMN_STYLE_DARK_STORE_STOCK_AVAILABILITY,
        'product_ingress_field_meta_dcNo'                         => MMSProductFields::COLUMN_STYLE_DC_NO,
        'product_ingress_field_meta_deviceBandwidthDesc'          => MMSProductFields::COLUMN_STYLE_DEVICE_BANDWIDTH_DESC,
        'product_ingress_field_meta_deviceBatteryCapacityDesc'    => MMSProductFields::COLUMN_STYLE_DEVICE_BATTERY_CAPACITY_DESC,
        'product_ingress_field_meta_deviceCameraDesc'             => MMSProductFields::COLUMN_STYLE_DEVICE_CAMERA_DESC,
        'product_ingress_field_meta_deviceNetworkLockedInd'       => MMSProductFields::COLUMN_STYLE_DEVICE_NETWORK_LOCKED_IND,
        'product_ingress_field_meta_deviceOpeSystemDesc'          => MMSProductFields::COLUMN_STYLE_DEVICE_OPE_SYSTEM_DESC,
        'product_ingress_field_meta_deviceRamDesc'                => MMSProductFields::COLUMN_STYLE_DEVICE_RAM_DESC,
        'product_ingress_field_meta_deviceRomDesc'                => MMSProductFields::COLUMN_STYLE_DEVICE_ROM_DESC,
        'product_ingress_field_meta_deviceScreenSizeDesc'         => MMSProductFields::COLUMN_STYLE_DEVICE_SCREEN_SIZE_DESC,
        'product_ingress_field_meta_deviceSourceTypeDesc'         => MMSProductFields::COLUMN_STYLE_DEVICE_SOURCE_TYPE_DESC,
        'product_ingress_field_meta_discDate'                     => MMSProductFields::COLUMN_STYLE_DISC_DATE,
        'product_ingress_field_meta_distributionScheduledforPickingDate' => MMSProductFields::COLUMN_STYLE_DISTRIBUTION_SCHEDULED_FOR_PICKING_DATE,
        'product_ingress_field_meta_effEndDate'                   => MMSProductFields::COLUMN_STYLE_EFF_END_DATE,
        'product_ingress_field_meta_effStartDate'                 => MMSProductFields::COLUMN_STYLE_EFF_START_DATE,
        'product_ingress_field_meta_fabric'                       => MMSProductFields::COLUMN_STYLE_FABRIC,
        'product_ingress_field_meta_fabricCode'                   => MMSProductFields::COLUMN_STYLE_FABRIC_CODE,
        'product_ingress_field_meta_fabricCompositionDesc'        => MMSProductFields::COLUMN_STYLE_FABRIC_COMPOSITION_DESC,
        'product_ingress_field_meta_familyVariant'                => MMSProductFields::COLUMN_STYLE_FAMILY_VARIANT,
        'product_ingress_field_meta_finNonSaleInd'                => MMSProductFields::COLUMN_STYLE_FIN_NON_SALE_IND,
        'product_ingress_field_meta_foneYamIndicator'             => MMSProductFields::COLUMN_STYLE_FONE_YAM_INDICATOR,
        'product_ingress_field_meta_grossItemWeightG'             => MMSProductFields::COLUMN_STYLE_GROSS_ITEM_WEIGHT_G,
        'product_ingress_field_meta_hangerTypeDesc'               => MMSProductFields::COLUMN_STYLE_HANGER_TYPE_DESC,
        'product_ingress_field_meta_hasFuturePO'                  => MMSProductFields::COLUMN_STYLE_HAS_FUTURE_PO,
        'product_ingress_field_meta_hazardousInd'                 => MMSProductFields::COLUMN_STYLE_HAZARDOUS_IND,
        'product_ingress_field_meta_heelHeightDesc'               => MMSProductFields::COLUMN_STYLE_HEEL_HEIGHT_DESC,
        'product_ingress_field_meta_heelTypeDesc'                 => MMSProductFields::COLUMN_STYLE_HEEL_TYPE_DESC,
        'product_ingress_field_meta_holdBackInd'                  => MMSProductFields::COLUMN_STYLE_HOLD_BACK_IND,
        'product_ingress_field_meta_inBranchDate'                 => MMSProductFields::COLUMN_STYLE_IN_BRANCH_DATE,
        'product_ingress_field_meta_jeEndDnldDate'                => MMSProductFields::COLUMN_STYLE_JE_END_DNLD_DATE,
        'product_ingress_field_meta_jeStartDnldDate'              => MMSProductFields::COLUMN_STYLE_JE_START_DNLD_DATE,
        'product_ingress_field_meta_lastReplDate'                 => MMSProductFields::COLUMN_STYLE_LAST_REPL_DATE,
        'product_ingress_field_meta_merchandiseType'              => MMSProductFields::COLUMN_STYLE_MERCHANDISE_TYPE,
        'product_ingress_field_meta_modelCalcStartDate'           => MMSProductFields::COLUMN_STYLE_MODEL_CALC_START_DATE,
        'product_ingress_field_meta_modelDesc'                    => MMSProductFields::COLUMN_STYLE_MODEL_DESC,
        'product_ingress_field_meta_nettItemWeightG'              => MMSProductFields::COLUMN_STYLE_NETT_ITEM_WEIGHT_G,
        'product_ingress_field_meta_perishableInd'                => MMSProductFields::COLUMN_STYLE_PERISHABLE_IND,
        'product_ingress_field_meta_pimEnabled'                   => MMSProductFields::COLUMN_STYLE_PIM_ENABLED,
        'product_ingress_field_meta_planInd'                      => MMSProductFields::COLUMN_STYLE_PLAN_IND,
        'product_ingress_field_meta_planInterfaceInd'             => MMSProductFields::COLUMN_STYLE_PLAN_INTERFACE_IND,
        'product_ingress_field_meta_posMsgVariationDesc'          => MMSProductFields::COLUMN_STYLE_POS_MSG_VARIATION_DESC,
        'product_ingress_field_meta_priceCategoryDesc'            => MMSProductFields::COLUMN_STYLE_PRICE_CATEGORY_DESC,
        'product_ingress_field_meta_primaryPromoInd'              => MMSProductFields::COLUMN_STYLE_PRIMARY_PROMO_IND,
        'product_ingress_field_meta_prodDbnTypeDesc'              => MMSProductFields::COLUMN_STYLE_PROD_DBN_TYPE_DESC,
        'product_ingress_field_meta_prodMerchTypeName'            => MMSProductFields::COLUMN_STYLE_PROD_MERCH_TYPE_NAME,
        'product_ingress_field_meta_promoCategoryDesc'            => MMSProductFields::COLUMN_STYLE_PROMO_CATEGORY_DESC,
        'product_ingress_field_meta_promoDesc'                    => MMSProductFields::COLUMN_STYLE_PROMO_DESC,
        'product_ingress_field_meta_promoIdNo'                    => MMSProductFields::COLUMN_STYLE_PROMO_ID_NO,
        'product_ingress_field_meta_rangeStackerDesc'             => MMSProductFields::COLUMN_STYLE_RANGE_STACKER_DESC,
        'product_ingress_field_meta_replInd'                      => MMSProductFields::COLUMN_STYLE_REPL_IND,
        'product_ingress_field_meta_royaltyCharacter'             => MMSProductFields::COLUMN_STYLE_ROYALTY_CHARACTER,
        'product_ingress_field_meta_royaltyCommentDesc'           => MMSProductFields::COLUMN_STYLE_ROYALTY_COMMENT_DESC,
        'product_ingress_field_meta_royaltyContractName'          => MMSProductFields::COLUMN_STYLE_ROYALTY_CONTRACT_NAME,
        'product_ingress_field_meta_royaltyThemeDesc'             => MMSProductFields::COLUMN_STYLE_ROYALTY_THEME_DESC,
        'product_ingress_field_meta_sarsUnitMeasureCode'          => MMSProductFields::COLUMN_STYLE_SARS_UNIT_MEASURE_CODE,
        'product_ingress_field_meta_seasonName'                   => MMSProductFields::COLUMN_STYLE_SEASON_NAME,
        'product_ingress_field_meta_silhouetteDesc'               => MMSProductFields::COLUMN_STYLE_SILHOUETTE_DESC,
        'product_ingress_field_meta_stockAvailability'            => MMSProductFields::COLUMN_STYLE_STOCK_AVAILABILITY,
        'product_ingress_field_meta_storyName'                    => MMSProductFields::COLUMN_STYLE_STORY_NAME,
        'product_ingress_field_meta_subBrandCode'                 => MMSProductFields::COLUMN_STYLE_SUB_BRAND_CODE,
        'product_ingress_field_meta_subBrandName'                 => MMSProductFields::COLUMN_STYLE_SUB_BRAND_NAME,
        'product_ingress_field_meta_subClassCode'                 => MMSProductFields::COLUMN_STYLE_SUB_CLASS_CODE,
        'product_ingress_field_meta_subClassName'                 => MMSProductFields::COLUMN_STYLE_SUB_CLASS_NAME,
        'product_ingress_field_meta_supplierOriginCode'           => MMSProductFields::COLUMN_STYLE_SUPPLIER_ORIGIN_CODE,
        'product_ingress_field_meta_supplierProdCode'             => MMSProductFields::COLUMN_STYLE_SUPPLIER_PROD_CODE,
        'product_ingress_field_meta_themeDesc'                    => MMSProductFields::COLUMN_STYLE_THEME_DESC,
        'product_ingress_field_meta_ticketTypeDesc'               => MMSProductFields::COLUMN_STYLE_TICKET_TYPE_DESC,
        'product_ingress_field_meta_upperMaterial'                => MMSProductFields::COLUMN_STYLE_UPPER_MATERIAL,
        'product_ingress_field_meta_vatRateTypeDesc'              => MMSProductFields::COLUMN_STYLE_VAT_RATE_TYPE_DESC,
        'product_ingress_field_meta_virtualProdInd'               => MMSProductFields::COLUMN_STYLE_VIRTUAL_PROD_IND,
        'product_ingress_field_meta_websiteInd'                   => MMSProductFields::COLUMN_STYLE_WEBSITE_IND,
        'product_ingress_field_meta_websiteIndUpdateDate'         => MMSProductFields::COLUMN_STYLE_WEBSITE_IND_UPDATE_DATE,
        'product_ingress_field_meta_websiteStyleDesc'             => MMSProductFields::COLUMN_STYLE_WEBSITE_STYLE_DESC,
        'product_ingress_field_meta_yrSeasonCode'                 => MMSProductFields::COLUMN_STYLE_YR_SEASON_CODE,
        'product_ingress_field_meta_plusMoreInd'                  => MMSProductFields::COLUMN_STYLE_PLUS_MORE_IND,
        'product_ingress_field_meta_article_group'                => MMSProductFields::COLUMN_STYLE_SUB_CLASS_NAME,
        'product_ingress_field_meta_buyInd'                       => MMSProductFields::COLUMN_STYLE_BUY_IND,

        // Product SKU Meta Fields
        'product_ingress_field_variant_meta_activeInd'                    => MMSProductFields::COLUMN_SKU_ACTIVE_IND,
        'product_ingress_field_variant_meta_cellBundleTypeDesc'           => MMSProductFields::COLUMN_SKU_CELL_BUNDLE_TYPE_DESC,
        'product_ingress_field_variant_meta_familyName'                   => MMSProductFields::COLUMN_SKU_FAMILY_NAME,
        'product_ingress_field_variant_meta_noOfUnitsPerCubicMeter'       => MMSProductFields::COLUMN_SKU_NO_OF_UNITS_PER_CUBIC_METER,
        'product_ingress_field_variant_meta_pimEnabled'                   => MMSProductFields::COLUMN_SKU_PIM_ENABLED,
        'product_ingress_field_variant_meta_planInd'                      => MMSProductFields::COLUMN_SKU_PLAN_IND,
        'product_ingress_field_variant_meta_primaryColourDesc'            => MMSProductFields::COLUMN_SKU_PRIMARY_COLOUR_DESC,
        'product_ingress_field_variant_meta_replInd'                      => MMSProductFields::COLUMN_SKU_REPL_IND,
        'product_ingress_field_variant_meta_sizeSortSeqNo'                => MMSProductFields::COLUMN_SKU_SIZE_SORT_SEQ_NO,
        'product_ingress_field_variant_meta_skuDesc'                      => MMSProductFields::COLUMN_SKU_SKU_DESC,
        'product_ingress_field_variant_meta_stockAvailability'            => MMSProductFields::COLUMN_SKU_STOCK_AVAILABILITY,
        'product_ingress_field_variant_meta_styleCode'                    => MMSProductFields::COLUMN_SKU_STYLE_CODE,
        'product_ingress_field_variant_meta_websiteInd'                   => MMSProductFields::COLUMN_SKU_WEBSITE_IND,
    ];

    //Hierarchy
    const PRODUCT_META_SUB_CLASS_CODE = 'meta_subClassCode';
    const META_HIERARCHY_CSV_URL      = 'hierarchy_csv_url';

    const COL_SUBCLASS_CODE = 'sub_class_code';
    const IGNORE_COLS       = [
        self::COL_SUBCLASS_CODE
    ];

    /** @var array<string, string> */
    private $products_field_map;

    /** @var string */
    private $base_url;

    /** @var string */
    private $client_id;

    /** @var string */
    private $client_secret;

    /** @var string */
    private $stock_adj_from_branch_no;

    /** @var HierarchyManager */
    private $hierarchy_manager;

    /** @var string */
    private $hierarchy_csv_url;

    /**
     * @throws Exception
     */
    public function __construct()
    {
        // call parent singleton constructor
        parent::__construct();
    }

    /**
     * Call init before other functions in connector code
     * @return void
     * @throws Exception
     */
    public function init()
    {
        $connectorConfig = ConnectorConfiguration::getInstance();
        if (!isset($this->products_field_map)) {
            $fields = $connectorConfig->getMetaRepository()->meta->prefixSearchAssociativeArray(
                ProductsIngressMapperArgs::META_PREFIX
            );
            $map = self::DEFAULT_PRODUCT_FIELD_MAP;
            foreach ($fields as $field => $column) {
                $map[ProductsIngressMapperArgs::META_PREFIX . $field] = $column;
            }
            $this->products_field_map = $map;
        }
        if (!isset($this->base_url)) {
            $this->base_url = $connectorConfig->getMetaRepository()->meta->getValue(self::BASE_URL_KEY);
        }
        if (!isset($this->client_id)) {
            $this->client_id = ConnectorConfiguration::getInstance()
                ->getSecretsRepository()->getValue(Constants::SECRET_OAUTH2_CLIENT_ID);
        }
        if (!isset($this->client_secret)) {
            $this->client_secret = ConnectorConfiguration::getInstance()
                ->getSecretsRepository()->getValue(Constants::SECRET_OAUTH2_CLIENT_SECRET);
        }
        if (!isset($this->stock_adj_from_branch_no)) {
            $this->stock_adj_from_branch_no = $connectorConfig->getMetaRepository()->meta->getValue(self::STOCK_ADJ_FROM_BRANCH_NO);
        }
        if (!$connectorConfig->hasGuzzleClient()) {
            $connectorConfig->setGuzzleClient(new Client());
        }
        if (!isset($this->hierarchy_manager)) {
            $this->hierarchy_manager = new HierarchyManager();
        }
        if (!isset($this->hierarchy_csv_url)) {
            $this->hierarchy_csv_url = $connectorConfig->getMetaRepository()->meta->getValue(self::META_HIERARCHY_CSV_URL);
        }
    }

    public function setProductsFieldMap(array $map)
    {
        $this->products_field_map = $map;
    }

    /**
     * @throws Exception
     */
    public function getProductsFieldMap(): array
    {
        return $this->products_field_map;
    }

    /**
     * @return string|null
     */
    public function getBaseUrl()
    {
        return $this->base_url;
    }

    /**
     *
     * @param string $base_url
     * @return void
     */
    public function setBaseUrl(string $base_url)
    {
        $this->base_url = $base_url;
    }

    /**
     * @return string
     */
    public function getTokenEndpoint(): string
    {
        return $this->getEndpointFromRoute(self::TOKEN_ROUTE);
    }

    /**
     * @param string $route
     * @return string
     */
    public function getEndpointFromRoute(string $route): string
    {
        return $this->getBaseUrl() . $route;
    }

    /**
     * @return string|null
     */
    public function getClientId()
    {
        return $this->client_id;
    }

    /**
     * @return string|null
     */
    public function getClientSecret()
    {
        return $this->client_secret;
    }

    /**
     * @throws Exception
     */
    public function validateForProductIngress()
    {
        foreach ($this->products_field_map as $field => $column) {
            if (!in_array($column, MMSProductFields::COLUMNS)) {
                throw new Exception("Invalid column in product ingress field mapping: $column");
            }
        }
    }

    /**
     * @param string $stock_adj_from_branch_no
     */
    public function setStockAdjFromBranchNo(string $stock_adj_from_branch_no)
    {
        $this->stock_adj_from_branch_no = $stock_adj_from_branch_no;
    }

    /**
     * @return string
     */
    public function getStockAdjFromBranchNo()
    {
        return $this->stock_adj_from_branch_no;
    }

    /**
     * @return HierarchyManager
     */
    public function getHierarchyManager(): HierarchyManager
    {
        return $this->hierarchy_manager;
    }

    /**
     * @return string
     */
    public function getHierarchyCSVUrl()
    {
        return $this->hierarchy_csv_url;
    }

    /**
     * Validates the configuration (checks required fields are set)
     * Throws exception if not.
     *
     * @throws Exception
     */
    public function validate()
    {
        ConnectorConfiguration::getInstance()->validate();
        if (lib\Utils::strIsEmpty($this->getBaseUrl())) {
            throw new exceptions\Validation(sprintf("Client secret not set: %s",
                self::BASE_URL_KEY));
        }

        if (lib\Utils::strIsEmpty($this->getClientId())) {
            throw new exceptions\Validation(sprintf("Client secret not set: %s",
                Constants::SECRET_OAUTH2_CLIENT_ID));
        }

        if (lib\Utils::strIsEmpty($this->getClientSecret())) {
            throw new exceptions\Validation(sprintf("Client secret not set: %s",
                Constants::SECRET_OAUTH2_CLIENT_SECRET));
        }
    }
}