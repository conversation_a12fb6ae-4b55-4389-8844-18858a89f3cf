<?php

namespace stock2shop\dal\sources\mms;

use Exception;
use stock2shop\dal\source\ProductsIngressMapperArgs;
use stock2shop\dal\source\ProductsIngressMapperInterface;

class ProductsIngressMapper implements ProductsIngressMapperInterface
{
    /**
     * @param ProductsIngressMapperArgs $args
     * @return array
     * @throws Exception
     */
    public static function map(ProductsIngressMapperArgs $args): array
    {
        $conf = Configuration::getInstance();
        $conf->init();
        $conf->validateForProductIngress();

        $rows = $args->rowsFromFieldMap($conf->getProductsFieldMap());
        foreach ($rows as &$row) {
            $conf->getHierarchyManager()->setHierarchyMeta($row);
            $row['product_active'] = true;
            $row['variant_active'] = true;
            $row['inventory_management'] = true;
        }
        return $rows;
    }
}
