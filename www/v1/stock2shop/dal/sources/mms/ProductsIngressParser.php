<?php

namespace stock2shop\dal\sources\mms;

use Exception;
use stock2shop\dal\connector\ConnectorConfiguration;
use stock2shop\dal\mms\models\products\ProductPayload;
use stock2shop\dal\source;
use stock2shop\dal\source\ProductsIngressParserArgs;
use stock2shop\lib;
use stock2shop\logs;
use stock2shop\vo\TaskPayloadLog;

class ProductsIngressParser implements source\ProductsIngressParserInterface
{
    /**
     * @param ProductsIngressParserArgs $args
     * @throws Exception
     */
    public static function parse(source\ProductsIngressParserArgs $args)
    {
        $conf = Configuration::getInstance();
        $conf->init();
        $conf->validateForProductIngress();

        $data = stream_get_contents($args->getStream()->handle);

        // log payload
        $clientConfig = ConnectorConfiguration::getInstance()->getClientConfig();
        $payload = new TaskPayloadLog();
        $payload->body = $data;
        $logEvent = new logs\event\Log($clientConfig, $payload);
        logs\Logger::event($logEvent);

        $rows = MMSProductFields::getFlattened(new ProductPayload(lib\Utils::jsonDecode($data)));

        $args->process($rows);
    }

}
