<?php

namespace stock2shop\dal\sources\mms;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;

class HierarchyManager
{
    const HIERARCHY     = 'hierarchy';
    const HIERARCHY_URL = 'https://docs.google.com/feeds/download/spreadsheets/Export?exportFormat=csv&key=14ItYxuof46THkkMdqu9q_bUoQL_r_0x30WmDG2Nlm-c&gid=811768995';

    const COL_SUBCLASS          = 'subclass';

    const IGNORE_COLS = [
        self::COL_SUBCLASS,
        'primary_secondary',
    ];

    /**
     * @var string
     */
    protected $csv;

    /**
     * @var Client
     */
    private $guzzleClient;

    /**
     * @var string[]
     */
    private $headers;

    private $meta_map;

    /**
     * @throws GuzzleException
     */
    public function __construct()
    {
        global $config;

        if ($config['virtualization']['enabled']) {
            $this->csv = file_get_contents(__DIR__ . '/../tests/v1/stock2shop/dal/sources/mms/data/pep_hierarchy_sample_data.csv');
        } else {
            $this->guzzleClient = new Client();
            $this->csv          = $this->getLookupCSV();
        }
            $this->loadMetaMap();
    }


    /**
     * Get the lookup csv
     *
     * @return string
     * @throws GuzzleException
     */
    public function getLookupCSV(): string
    {
        // We need to lookup labels from a csv that will be downloaded from a google sheet
        // Use guzzle to download the csv

        // TODO Replace with bucket
        $response = $this->guzzleClient->request('GET', self::HIERARCHY_URL);

        return $response->getBody()->getContents();
    }

    protected function loadMetaMap()
    {
        // Open the CSV data as a stream
        $stream = fopen('php://memory', 'r+');
        fwrite($stream, $this->csv);
        rewind($stream);

        // Get the header row (columns)
        $columns             = fgetcsv($stream) ?: [];
        $this->headers       = $columns;
        $headers_index = array_flip($columns);
        $this->meta_map            = [];

        // Iterate through each row using a while loop
        while (($row = fgetcsv($stream)) !== false) {
            $subclass = $row[$headers_index[self::COL_SUBCLASS]]; // Get the subclass from the row
            if (empty($this->meta_map[$subclass])) {
                $this->meta_map[$subclass] = [];
            }
            $this->meta_map[$subclass] = array_merge($this->meta_map[$subclass], $this->getMetaFromRow($row));
        }

        fclose($stream);
    }

    private function getMetaFromRow(array $row): array
    {
        $m       = [];
        foreach ($row as $key => $value) {
            if (in_array($this->headers[$key], self::IGNORE_COLS) || empty($value)) {
                continue;
            }
            $m['meta_' . $this->headers[$key]] = $value;
        }
        return $m;
    }

    /**
     * @param array $flat_product
     * @return void
     */
    public function setHierarchyMeta(array &$flat_product)
    {
        $product_subclass = $flat_product['meta_SubClassCode'] ?? null;

        if (empty($product_subclass)) {
            return;
        }
        if (!empty($this->meta_map[$product_subclass])) {
            $flat_product = array_merge($flat_product, $this->meta_map[$product_subclass]);
        }
    }
}