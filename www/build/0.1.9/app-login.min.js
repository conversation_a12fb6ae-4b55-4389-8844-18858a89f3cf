/*! b2b-trade - v0.1.9
* 2015-03-19
* http://stock2shop.com
* Copyright (c) 2015 - Stock 2 Shop */ 
function config(){this.credentials={},this.baseUrl=baseUrl,this.pagination={limit:20,offset:1}}function MainCtrl($scope,$location,auth,stock2shop,analytics){analytics.trackPage("/login","Login"),$scope.username="",$scope.password="",$scope.loginMode=!0,auth.removeCookie(),$scope.login=function(){analytics.trackEvent("Login","Attempt",$scope.username,1),stock2shop.users.authenticateUser({body:{system_user_auth:{username:$scope.username,password:$scope.password}}},function(result){if(result.data.system_user.customers){var headers=result.headers(),credentials=auth.createCredentials(result.data.system_user,headers["x-b2b-version"]);auth.setCookie(credentials),analytics.trackEvent("Login","Success",$scope.username),window.location.href="/"}else $scope.error="The user you have logged in with has not been assigned a customer, contact the administrator."},function(){analytics.trackEvent("Login","Failure",$scope.username,1),$scope.error="Invalid credentials"})},$scope.resetPassword=function(){console.info($scope.resetUsername),analytics.trackEvent("Login","Reset Password Attempt",$scope.resetUsername,1),stock2shop.users.requestPasswordReset({username:$scope.resetUsername},function(){analytics.trackEvent("Login","Reset Password Success",$scope.resetUsername,1),$scope.message="Reset instructions sent"},function(){analytics.trackEvent("Login","Reset Password Failure",$scope.resetUsername,1),$scope.error="Invalid username"})},$scope.toggleLogin=function(){$scope.loginMode=!$scope.loginMode,$scope.message=!1,$scope.error=!1}}function auth(config,$location,stock2shop){var cookieName="credentials";this.validToken=function(){stock2shop.users.validToken()},this.createCredentials=function(system_user,version){return{version:version,email:system_user.email,name:system_user.name,surname:system_user.surname,token:system_user.token,username:system_user.username,client_id:system_user.client_id,client_name:system_user.client_name,channels:system_user.channels,sources:system_user.sources,fulfillment_services:system_user.fulfillment_services,customer:system_user.customers[0]}},this.removeCookie=function(){$.removeCookie(cookieName,{path:"/"})},this.setCookie=function(credentials){$.cookie(cookieName,JSON.stringify(credentials),{expires:365,path:"/"})},this.setCredentials=function(credentials){config.credentials=credentials},this.getCredentials=function(){var cookie=$.cookie(cookieName),credentials=void 0;if(cookie)try{eval("credentials = "+cookie)}catch(e){}return credentials},this.removeCredentials=function(){config.credentials={}}}function cart($filter,utils,storeCache,price,analytics){var cookieName="cart",cookieData=$.cookie(cookieName);if(cookieData)try{eval("this.order = "+cookieData)}catch(e){}else this.order={};this.resetOrder=function(){this.order={},this.removeCookie()},this.getOrder=function(){var subTotal=0,taxTotal=0;angular.forEach(this.order.line_items,function(item){subTotal+=item.price*item.qty,angular.forEach(item.tax_lines,function(tax){taxTotal+=utils.round2Decimal(tax.price)})});var order=angular.copy(this.order);return order.sub_total=utils.round2Decimal(subTotal),order.tax_total=utils.round2Decimal(taxTotal),order},this.getVariant=function(sku){if(this.order.line_items)for(var i=0;i<this.order.line_items.length;i++)if(this.order.line_items[i].sku==sku)return this.order.line_items[i];return!1},this.updateQty=function(item){var settings=storeCache.get().settings;angular.forEach(this.order.line_items,function(existingItem){existingItem.sku==item.sku&&(existingItem.qty=item.qty,existingItem.tax_lines="true"==settings.tax?[{title:"VAT",rate:parseInt(settings.tax_rate),price:utils.round2Decimal(price.getTax(item.price)*item.qty),code:settings.tax_code}]:[])}),this.setCookie(this.order)},this.addItem=function(variant,title,qty){if(isNaN(qty)||0>=qty)return $.SmartMessageBox({title:"The value you have entered for qty is not valid",buttons:"[OK]"}),!1;if(qty>variant.qty)return $.SmartMessageBox({title:"You have ordered "+qty+" products, but "+variant.qty+" are left in stock.",buttons:"[OK]"}),!1;var settings=storeCache.get().settings,item={};item.sku=variant.sku,item.title=title,item.price=price.getExTax(variant.price),item.qty=parseInt(qty),item.product_id=variant.product_id,item.variant_id=variant.id,item.tax_lines="true"==settings.tax?[{title:"VAT",rate:parseInt(settings.tax_rate),price:utils.round2Decimal(price.getTax(variant.price)*item.qty),code:settings.tax_code}]:[];var mustAdd=!0;return angular.forEach(this.order.line_items,function(existingItem){existingItem.sku==item.sku&&(existingItem.qty=item.qty,mustAdd=!1)}),mustAdd&&(this.order.line_items=this.order.line_items||[],this.order.line_items.push(item),analytics.addProduct(item.sku,item.title,"","",item.variant_id,item.price,item.qty),analytics.trackCart("add")),this.setCookie(this.order),!0},this.removeItem=function(sku){this.order.line_items=$filter("filter")(this.order.line_items,{sku:"!"+sku}),this.setCookie(this.order),analytics.addProduct(sku),analytics.trackCart("remove")},this.setID=function(){this.order.id=utils.randomString(10)},this.setShippingLines=function(lines){this.order.shipping_lines=lines||[]},this.setBillingAddress=function(address){this.order.billing_address=address||{}},this.setShippingAddress=function(address){this.order.shipping_address=address||{}},this.setInstruction=function(instruction){this.order.instruction=instruction},this.setCustomer=function(customer){this.order.customer=customer},this.setNotes=function(notes){this.order.notes=notes||""},this.removeCookie=function(){$.removeCookie(cookieName,{path:"/"})},this.setCookie=function(order){$.cookie(cookieName,JSON.stringify(order),{expires:365,path:"/"})}}function price(storeCache,utils){this.get=function(value){value=utils.round2Decimal(value);var settings=storeCache.get().settings,includingTax=settings.price_inclusive||"false",displayTax=settings.price_display||!1;return"true"==includingTax?"inclusive"==displayTax?value:utils.round2Decimal(value-value*parseInt(settings.tax_rate)/(1+settings.tax_rate)):"inclusive"==displayTax?utils.round2Decimal(value*(1+settings.tax_rate/100)):value},this.getExTax=function(value){value=utils.round2Decimal(value);var settings=storeCache.get().settings,includingTax=settings.price_inclusive||"false";return"true"==includingTax?utils.round2Decimal(value-value*parseInt(settings.tax_rate)/(1+settings.tax_rate)):value},this.getTax=function(value){value=utils.round2Decimal(value);var settings=storeCache.get().settings,includingTax=settings.price_inclusive||"false";if("true"==includingTax){var exclusive=value/(1+settings.tax_rate/100);return utils.round2Decimal(value-exclusive)}var inclusive=value*(1+settings.tax_rate/100);return utils.round2Decimal(inclusive-value)}}function storeCache($q,stock2shop,config){this.store=!1,this.queue=!1,this.set=function(){var self=this;return this.queue||(this.queue=$q.all([stock2shop.products.getMetaData({channel_id:config.credentials.channels[0].id}),stock2shop.channel_meta.getChannelMeta({channel_id:config.credentials.channels[0].id}),stock2shop.customers.getCustomer({customer_id:config.credentials.customer.id})]).then(function(data){var channel={};return angular.forEach(data[1].system_channel_meta,function(meta){channel[meta.key]=meta.value}),self.store={product_meta:data[0].system_product_meta,settings:channel,customer:data[2].system_customer},self.store})),this.queue},this.get=function(){return this.store},this.reset=function(){this.store=!1,this.queue=!1}}function utils($location){String.prototype.trim||(String.prototype.trim=function(){return this.replace(/^\s+|\s+$/g,"")},String.prototype.ltrim=function(){return this.replace(/^\s+/,"")},String.prototype.rtrim=function(){return this.replace(/\s+$/,"")},String.prototype.fulltrim=function(){return this.replace(/(?:(?:^|\n)\s+|\s+(?:$|\n))/g,"").replace(/\s+/g," ")}),this.createMap=function(values,key){var map={};return angular.forEach(values,function(value){map[value[key]]=value}),map},this.findMatch=function(obj,search,padding){padding=padding||30;var s=JSON.stringify(obj),start=s.toLowerCase().indexOf(search.toLowerCase());if(-1==start)return"No match";var end=start+search.length;return start=start-padding>0?start-padding:0,end=end+padding<s.length?end+padding:s.length,s.substring(start,end)},this.wrapStrong=function(text,search){var r=new RegExp(search,"ig");return text.replace(r,"<strong>"+search+"</strong>")},this.randomString=function(length){for(var mask="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ",result="",i=length;i>0;--i)result+=mask[Math.round(Math.random()*(mask.length-1))];return result},this.round2Decimal=function(amount){return amount-=0,Math.round(100*amount)/100},this.money=function(amount){return amount-=0,amount=Math.round(100*amount)/100,amount=amount==Math.floor(amount)?amount+".00":10*amount==Math.floor(10*amount)?amount+"0":amount,"R"+amount},this.objectLength=function(object){var length=0;for(var key in object)object.hasOwnProperty(key)&&++length;return length},this.stockLevels=function(qty){var string='<span class="text-danger">out of stock</span>';return 10>qty&&qty>0?string='<span class="text-warning">only '+qty+" items in stock</span>":qty>=10&&(string='<span class="text-success">in stock</span>'),string},this.shuffleArray=function(array){for(var i=array.length-1;i>0;i--){var j=Math.floor(Math.random()*(i+1)),temp=array[i];array[i]=array[j],array[j]=temp}return array},this.changeRoute=function(route,params){var query="";params=params||{},angular.forEach(params,function(value,key){"undefined"!=typeof value&&null!==value&&(query+=0==query.length?"?":"&",query+=encodeURIComponent(key)+"="+encodeURIComponent(value))}),$location.url(route+query)},this.md5=function(str){var xl,k,AA,BB,CC,DD,a,b,c,d,rotateLeft=function(lValue,iShiftBits){return lValue<<iShiftBits|lValue>>>32-iShiftBits},addUnsigned=function(lX,lY){var lX4,lY4,lX8,lY8,lResult;return lX8=2147483648&lX,lY8=2147483648&lY,lX4=1073741824&lX,lY4=1073741824&lY,lResult=(1073741823&lX)+(1073741823&lY),lX4&lY4?2147483648^lResult^lX8^lY8:lX4|lY4?1073741824&lResult?3221225472^lResult^lX8^lY8:1073741824^lResult^lX8^lY8:lResult^lX8^lY8},_F=function(x,y,z){return x&y|~x&z},_G=function(x,y,z){return x&z|y&~z},_H=function(x,y,z){return x^y^z},_I=function(x,y,z){return y^(x|~z)},_FF=function(a,b,c,d,x,s,ac){return a=addUnsigned(a,addUnsigned(addUnsigned(_F(b,c,d),x),ac)),addUnsigned(rotateLeft(a,s),b)},_GG=function(a,b,c,d,x,s,ac){return a=addUnsigned(a,addUnsigned(addUnsigned(_G(b,c,d),x),ac)),addUnsigned(rotateLeft(a,s),b)},_HH=function(a,b,c,d,x,s,ac){return a=addUnsigned(a,addUnsigned(addUnsigned(_H(b,c,d),x),ac)),addUnsigned(rotateLeft(a,s),b)},_II=function(a,b,c,d,x,s,ac){return a=addUnsigned(a,addUnsigned(addUnsigned(_I(b,c,d),x),ac)),addUnsigned(rotateLeft(a,s),b)},convertToWordArray=function(str){for(var lWordCount,lMessageLength=str.length,lNumberOfWords_temp1=lMessageLength+8,lNumberOfWords_temp2=(lNumberOfWords_temp1-lNumberOfWords_temp1%64)/64,lNumberOfWords=16*(lNumberOfWords_temp2+1),lWordArray=new Array(lNumberOfWords-1),lBytePosition=0,lByteCount=0;lMessageLength>lByteCount;)lWordCount=(lByteCount-lByteCount%4)/4,lBytePosition=lByteCount%4*8,lWordArray[lWordCount]=lWordArray[lWordCount]|str.charCodeAt(lByteCount)<<lBytePosition,lByteCount++;return lWordCount=(lByteCount-lByteCount%4)/4,lBytePosition=lByteCount%4*8,lWordArray[lWordCount]=lWordArray[lWordCount]|128<<lBytePosition,lWordArray[lNumberOfWords-2]=lMessageLength<<3,lWordArray[lNumberOfWords-1]=lMessageLength>>>29,lWordArray},wordToHex=function(lValue){var lByte,lCount,wordToHexValue="",wordToHexValue_temp="";for(lCount=0;3>=lCount;lCount++)lByte=lValue>>>8*lCount&255,wordToHexValue_temp="0"+lByte.toString(16),wordToHexValue+=wordToHexValue_temp.substr(wordToHexValue_temp.length-2,2);return wordToHexValue},x=[],S11=7,S12=12,S13=17,S14=22,S21=5,S22=9,S23=14,S24=20,S31=4,S32=11,S33=16,S34=23,S41=6,S42=10,S43=15,S44=21;for(x=convertToWordArray(str),a=1732584193,b=4023233417,c=2562383102,d=271733878,xl=x.length,k=0;xl>k;k+=16)AA=a,BB=b,CC=c,DD=d,a=_FF(a,b,c,d,x[k+0],S11,3614090360),d=_FF(d,a,b,c,x[k+1],S12,3905402710),c=_FF(c,d,a,b,x[k+2],S13,606105819),b=_FF(b,c,d,a,x[k+3],S14,3250441966),a=_FF(a,b,c,d,x[k+4],S11,4118548399),d=_FF(d,a,b,c,x[k+5],S12,1200080426),c=_FF(c,d,a,b,x[k+6],S13,2821735955),b=_FF(b,c,d,a,x[k+7],S14,4249261313),a=_FF(a,b,c,d,x[k+8],S11,1770035416),d=_FF(d,a,b,c,x[k+9],S12,2336552879),c=_FF(c,d,a,b,x[k+10],S13,4294925233),b=_FF(b,c,d,a,x[k+11],S14,2304563134),a=_FF(a,b,c,d,x[k+12],S11,1804603682),d=_FF(d,a,b,c,x[k+13],S12,4254626195),c=_FF(c,d,a,b,x[k+14],S13,2792965006),b=_FF(b,c,d,a,x[k+15],S14,1236535329),a=_GG(a,b,c,d,x[k+1],S21,4129170786),d=_GG(d,a,b,c,x[k+6],S22,3225465664),c=_GG(c,d,a,b,x[k+11],S23,643717713),b=_GG(b,c,d,a,x[k+0],S24,3921069994),a=_GG(a,b,c,d,x[k+5],S21,3593408605),d=_GG(d,a,b,c,x[k+10],S22,38016083),c=_GG(c,d,a,b,x[k+15],S23,3634488961),b=_GG(b,c,d,a,x[k+4],S24,3889429448),a=_GG(a,b,c,d,x[k+9],S21,568446438),d=_GG(d,a,b,c,x[k+14],S22,3275163606),c=_GG(c,d,a,b,x[k+3],S23,4107603335),b=_GG(b,c,d,a,x[k+8],S24,1163531501),a=_GG(a,b,c,d,x[k+13],S21,2850285829),d=_GG(d,a,b,c,x[k+2],S22,4243563512),c=_GG(c,d,a,b,x[k+7],S23,1735328473),b=_GG(b,c,d,a,x[k+12],S24,2368359562),a=_HH(a,b,c,d,x[k+5],S31,4294588738),d=_HH(d,a,b,c,x[k+8],S32,2272392833),c=_HH(c,d,a,b,x[k+11],S33,1839030562),b=_HH(b,c,d,a,x[k+14],S34,4259657740),a=_HH(a,b,c,d,x[k+1],S31,2763975236),d=_HH(d,a,b,c,x[k+4],S32,1272893353),c=_HH(c,d,a,b,x[k+7],S33,4139469664),b=_HH(b,c,d,a,x[k+10],S34,3200236656),a=_HH(a,b,c,d,x[k+13],S31,681279174),d=_HH(d,a,b,c,x[k+0],S32,3936430074),c=_HH(c,d,a,b,x[k+3],S33,3572445317),b=_HH(b,c,d,a,x[k+6],S34,76029189),a=_HH(a,b,c,d,x[k+9],S31,3654602809),d=_HH(d,a,b,c,x[k+12],S32,3873151461),c=_HH(c,d,a,b,x[k+15],S33,530742520),b=_HH(b,c,d,a,x[k+2],S34,3299628645),a=_II(a,b,c,d,x[k+0],S41,4096336452),d=_II(d,a,b,c,x[k+7],S42,1126891415),c=_II(c,d,a,b,x[k+14],S43,2878612391),b=_II(b,c,d,a,x[k+5],S44,4237533241),a=_II(a,b,c,d,x[k+12],S41,1700485571),d=_II(d,a,b,c,x[k+3],S42,2399980690),c=_II(c,d,a,b,x[k+10],S43,4293915773),b=_II(b,c,d,a,x[k+1],S44,2240044497),a=_II(a,b,c,d,x[k+8],S41,1873313359),d=_II(d,a,b,c,x[k+15],S42,4264355552),c=_II(c,d,a,b,x[k+6],S43,2734768916),b=_II(b,c,d,a,x[k+13],S44,1309151649),a=_II(a,b,c,d,x[k+4],S41,4149444226),d=_II(d,a,b,c,x[k+11],S42,3174756917),c=_II(c,d,a,b,x[k+2],S43,718787259),b=_II(b,c,d,a,x[k+9],S44,3951481745),a=addUnsigned(a,AA),b=addUnsigned(b,BB),c=addUnsigned(c,CC),d=addUnsigned(d,DD);var temp=wordToHex(a)+wordToHex(b)+wordToHex(c)+wordToHex(d);return temp.toLowerCase()}}function validation(){var self=this;this.validationTypes=function(){return{number:{message:"Must be a valid number",valid:function(value){return!isNaN(Number(value))}}}},this.validate=function(type,value){var types=self.validationTypes();return 1==arguments.length?types[type]?types[type].message:"":types[type]?types[type].valid(value):!1}}function version(){this.url=function(url){return""!=production?url:url+"?v="+version}}!function(window,undefined){function isArraylike(obj){var length=obj.length,type=jQuery.type(obj);return jQuery.isWindow(obj)?!1:1===obj.nodeType&&length?!0:"array"===type||"function"!==type&&(0===length||"number"==typeof length&&length>0&&length-1 in obj)}function createOptions(options){var object=optionsCache[options]={};return jQuery.each(options.match(core_rnotwhite)||[],function(_,flag){object[flag]=!0}),object}function Data(){Object.defineProperty(this.cache={},0,{get:function(){return{}}}),this.expando=jQuery.expando+Math.random()}function dataAttr(elem,key,data){var name;if(data===undefined&&1===elem.nodeType)if(name="data-"+key.replace(rmultiDash,"-$1").toLowerCase(),data=elem.getAttribute(name),"string"==typeof data){try{data="true"===data?!0:"false"===data?!1:"null"===data?null:+data+""===data?+data:rbrace.test(data)?JSON.parse(data):data}catch(e){}data_user.set(elem,key,data)}else data=undefined;return data}function returnTrue(){return!0}function returnFalse(){return!1}function safeActiveElement(){try{return document.activeElement}catch(err){}}function sibling(cur,dir){for(;(cur=cur[dir])&&1!==cur.nodeType;);return cur}function winnow(elements,qualifier,not){if(jQuery.isFunction(qualifier))return jQuery.grep(elements,function(elem,i){return!!qualifier.call(elem,i,elem)!==not});if(qualifier.nodeType)return jQuery.grep(elements,function(elem){return elem===qualifier!==not});if("string"==typeof qualifier){if(isSimple.test(qualifier))return jQuery.filter(qualifier,elements,not);qualifier=jQuery.filter(qualifier,elements)}return jQuery.grep(elements,function(elem){return core_indexOf.call(qualifier,elem)>=0!==not})}function manipulationTarget(elem,content){return jQuery.nodeName(elem,"table")&&jQuery.nodeName(1===content.nodeType?content:content.firstChild,"tr")?elem.getElementsByTagName("tbody")[0]||elem.appendChild(elem.ownerDocument.createElement("tbody")):elem}function disableScript(elem){return elem.type=(null!==elem.getAttribute("type"))+"/"+elem.type,elem}function restoreScript(elem){var match=rscriptTypeMasked.exec(elem.type);return match?elem.type=match[1]:elem.removeAttribute("type"),elem}function setGlobalEval(elems,refElements){for(var l=elems.length,i=0;l>i;i++)data_priv.set(elems[i],"globalEval",!refElements||data_priv.get(refElements[i],"globalEval"))}function cloneCopyEvent(src,dest){var i,l,type,pdataOld,pdataCur,udataOld,udataCur,events;if(1===dest.nodeType){if(data_priv.hasData(src)&&(pdataOld=data_priv.access(src),pdataCur=data_priv.set(dest,pdataOld),events=pdataOld.events)){delete pdataCur.handle,pdataCur.events={};for(type in events)for(i=0,l=events[type].length;l>i;i++)jQuery.event.add(dest,type,events[type][i])}data_user.hasData(src)&&(udataOld=data_user.access(src),udataCur=jQuery.extend({},udataOld),data_user.set(dest,udataCur))}}function getAll(context,tag){var ret=context.getElementsByTagName?context.getElementsByTagName(tag||"*"):context.querySelectorAll?context.querySelectorAll(tag||"*"):[];return tag===undefined||tag&&jQuery.nodeName(context,tag)?jQuery.merge([context],ret):ret}function fixInput(src,dest){var nodeName=dest.nodeName.toLowerCase();"input"===nodeName&&manipulation_rcheckableType.test(src.type)?dest.checked=src.checked:("input"===nodeName||"textarea"===nodeName)&&(dest.defaultValue=src.defaultValue)}function vendorPropName(style,name){if(name in style)return name;for(var capName=name.charAt(0).toUpperCase()+name.slice(1),origName=name,i=cssPrefixes.length;i--;)if(name=cssPrefixes[i]+capName,name in style)return name;return origName}function isHidden(elem,el){return elem=el||elem,"none"===jQuery.css(elem,"display")||!jQuery.contains(elem.ownerDocument,elem)}function getStyles(elem){return window.getComputedStyle(elem,null)}function showHide(elements,show){for(var display,elem,hidden,values=[],index=0,length=elements.length;length>index;index++)elem=elements[index],elem.style&&(values[index]=data_priv.get(elem,"olddisplay"),display=elem.style.display,show?(values[index]||"none"!==display||(elem.style.display=""),""===elem.style.display&&isHidden(elem)&&(values[index]=data_priv.access(elem,"olddisplay",css_defaultDisplay(elem.nodeName)))):values[index]||(hidden=isHidden(elem),(display&&"none"!==display||!hidden)&&data_priv.set(elem,"olddisplay",hidden?display:jQuery.css(elem,"display"))));for(index=0;length>index;index++)elem=elements[index],elem.style&&(show&&"none"!==elem.style.display&&""!==elem.style.display||(elem.style.display=show?values[index]||"":"none"));return elements}function setPositiveNumber(elem,value,subtract){var matches=rnumsplit.exec(value);return matches?Math.max(0,matches[1]-(subtract||0))+(matches[2]||"px"):value}function augmentWidthOrHeight(elem,name,extra,isBorderBox,styles){for(var i=extra===(isBorderBox?"border":"content")?4:"width"===name?1:0,val=0;4>i;i+=2)"margin"===extra&&(val+=jQuery.css(elem,extra+cssExpand[i],!0,styles)),isBorderBox?("content"===extra&&(val-=jQuery.css(elem,"padding"+cssExpand[i],!0,styles)),"margin"!==extra&&(val-=jQuery.css(elem,"border"+cssExpand[i]+"Width",!0,styles))):(val+=jQuery.css(elem,"padding"+cssExpand[i],!0,styles),"padding"!==extra&&(val+=jQuery.css(elem,"border"+cssExpand[i]+"Width",!0,styles)));return val}function getWidthOrHeight(elem,name,extra){var valueIsBorderBox=!0,val="width"===name?elem.offsetWidth:elem.offsetHeight,styles=getStyles(elem),isBorderBox=jQuery.support.boxSizing&&"border-box"===jQuery.css(elem,"boxSizing",!1,styles);if(0>=val||null==val){if(val=curCSS(elem,name,styles),(0>val||null==val)&&(val=elem.style[name]),rnumnonpx.test(val))return val;valueIsBorderBox=isBorderBox&&(jQuery.support.boxSizingReliable||val===elem.style[name]),val=parseFloat(val)||0}return val+augmentWidthOrHeight(elem,name,extra||(isBorderBox?"border":"content"),valueIsBorderBox,styles)+"px"}function css_defaultDisplay(nodeName){var doc=document,display=elemdisplay[nodeName];return display||(display=actualDisplay(nodeName,doc),"none"!==display&&display||(iframe=(iframe||jQuery("<iframe frameborder='0' width='0' height='0'/>").css("cssText","display:block !important")).appendTo(doc.documentElement),doc=(iframe[0].contentWindow||iframe[0].contentDocument).document,doc.write("<!doctype html><html><body>"),doc.close(),display=actualDisplay(nodeName,doc),iframe.detach()),elemdisplay[nodeName]=display),display}function actualDisplay(name,doc){var elem=jQuery(doc.createElement(name)).appendTo(doc.body),display=jQuery.css(elem[0],"display");return elem.remove(),display}function buildParams(prefix,obj,traditional,add){var name;if(jQuery.isArray(obj))jQuery.each(obj,function(i,v){traditional||rbracket.test(prefix)?add(prefix,v):buildParams(prefix+"["+("object"==typeof v?i:"")+"]",v,traditional,add)});else if(traditional||"object"!==jQuery.type(obj))add(prefix,obj);else for(name in obj)buildParams(prefix+"["+name+"]",obj[name],traditional,add)}function addToPrefiltersOrTransports(structure){return function(dataTypeExpression,func){"string"!=typeof dataTypeExpression&&(func=dataTypeExpression,dataTypeExpression="*");var dataType,i=0,dataTypes=dataTypeExpression.toLowerCase().match(core_rnotwhite)||[];if(jQuery.isFunction(func))for(;dataType=dataTypes[i++];)"+"===dataType[0]?(dataType=dataType.slice(1)||"*",(structure[dataType]=structure[dataType]||[]).unshift(func)):(structure[dataType]=structure[dataType]||[]).push(func)}}function inspectPrefiltersOrTransports(structure,options,originalOptions,jqXHR){function inspect(dataType){var selected;return inspected[dataType]=!0,jQuery.each(structure[dataType]||[],function(_,prefilterOrFactory){var dataTypeOrTransport=prefilterOrFactory(options,originalOptions,jqXHR);return"string"!=typeof dataTypeOrTransport||seekingTransport||inspected[dataTypeOrTransport]?seekingTransport?!(selected=dataTypeOrTransport):void 0:(options.dataTypes.unshift(dataTypeOrTransport),inspect(dataTypeOrTransport),!1)}),selected}var inspected={},seekingTransport=structure===transports;return inspect(options.dataTypes[0])||!inspected["*"]&&inspect("*")}function ajaxExtend(target,src){var key,deep,flatOptions=jQuery.ajaxSettings.flatOptions||{};for(key in src)src[key]!==undefined&&((flatOptions[key]?target:deep||(deep={}))[key]=src[key]);return deep&&jQuery.extend(!0,target,deep),target}function ajaxHandleResponses(s,jqXHR,responses){for(var ct,type,finalDataType,firstDataType,contents=s.contents,dataTypes=s.dataTypes;"*"===dataTypes[0];)dataTypes.shift(),ct===undefined&&(ct=s.mimeType||jqXHR.getResponseHeader("Content-Type"));if(ct)for(type in contents)if(contents[type]&&contents[type].test(ct)){dataTypes.unshift(type);break}if(dataTypes[0]in responses)finalDataType=dataTypes[0];else{for(type in responses){if(!dataTypes[0]||s.converters[type+" "+dataTypes[0]]){finalDataType=type;break}firstDataType||(firstDataType=type)}finalDataType=finalDataType||firstDataType}return finalDataType?(finalDataType!==dataTypes[0]&&dataTypes.unshift(finalDataType),responses[finalDataType]):void 0}function ajaxConvert(s,response,jqXHR,isSuccess){var conv2,current,conv,tmp,prev,converters={},dataTypes=s.dataTypes.slice();if(dataTypes[1])for(conv in s.converters)converters[conv.toLowerCase()]=s.converters[conv];for(current=dataTypes.shift();current;)if(s.responseFields[current]&&(jqXHR[s.responseFields[current]]=response),!prev&&isSuccess&&s.dataFilter&&(response=s.dataFilter(response,s.dataType)),prev=current,current=dataTypes.shift())if("*"===current)current=prev;else if("*"!==prev&&prev!==current){if(conv=converters[prev+" "+current]||converters["* "+current],!conv)for(conv2 in converters)if(tmp=conv2.split(" "),tmp[1]===current&&(conv=converters[prev+" "+tmp[0]]||converters["* "+tmp[0]])){conv===!0?conv=converters[conv2]:converters[conv2]!==!0&&(current=tmp[0],dataTypes.unshift(tmp[1]));break}if(conv!==!0)if(conv&&s["throws"])response=conv(response);else try{response=conv(response)}catch(e){return{state:"parsererror",error:conv?e:"No conversion from "+prev+" to "+current}}}return{state:"success",data:response}}function createFxNow(){return setTimeout(function(){fxNow=undefined}),fxNow=jQuery.now()}function createTween(value,prop,animation){for(var tween,collection=(tweeners[prop]||[]).concat(tweeners["*"]),index=0,length=collection.length;length>index;index++)if(tween=collection[index].call(animation,prop,value))return tween}function Animation(elem,properties,options){var result,stopped,index=0,length=animationPrefilters.length,deferred=jQuery.Deferred().always(function(){delete tick.elem}),tick=function(){if(stopped)return!1;for(var currentTime=fxNow||createFxNow(),remaining=Math.max(0,animation.startTime+animation.duration-currentTime),temp=remaining/animation.duration||0,percent=1-temp,index=0,length=animation.tweens.length;length>index;index++)animation.tweens[index].run(percent);return deferred.notifyWith(elem,[animation,percent,remaining]),1>percent&&length?remaining:(deferred.resolveWith(elem,[animation]),!1)},animation=deferred.promise({elem:elem,props:jQuery.extend({},properties),opts:jQuery.extend(!0,{specialEasing:{}},options),originalProperties:properties,originalOptions:options,startTime:fxNow||createFxNow(),duration:options.duration,tweens:[],createTween:function(prop,end){var tween=jQuery.Tween(elem,animation.opts,prop,end,animation.opts.specialEasing[prop]||animation.opts.easing);return animation.tweens.push(tween),tween},stop:function(gotoEnd){var index=0,length=gotoEnd?animation.tweens.length:0;if(stopped)return this;for(stopped=!0;length>index;index++)animation.tweens[index].run(1);return gotoEnd?deferred.resolveWith(elem,[animation,gotoEnd]):deferred.rejectWith(elem,[animation,gotoEnd]),this}}),props=animation.props;for(propFilter(props,animation.opts.specialEasing);length>index;index++)if(result=animationPrefilters[index].call(animation,elem,props,animation.opts))return result;return jQuery.map(props,createTween,animation),jQuery.isFunction(animation.opts.start)&&animation.opts.start.call(elem,animation),jQuery.fx.timer(jQuery.extend(tick,{elem:elem,anim:animation,queue:animation.opts.queue})),animation.progress(animation.opts.progress).done(animation.opts.done,animation.opts.complete).fail(animation.opts.fail).always(animation.opts.always)}function propFilter(props,specialEasing){var index,name,easing,value,hooks;for(index in props)if(name=jQuery.camelCase(index),easing=specialEasing[name],value=props[index],jQuery.isArray(value)&&(easing=value[1],value=props[index]=value[0]),index!==name&&(props[name]=value,delete props[index]),hooks=jQuery.cssHooks[name],hooks&&"expand"in hooks){value=hooks.expand(value),delete props[name];for(index in value)index in props||(props[index]=value[index],specialEasing[index]=easing)}else specialEasing[name]=easing}function defaultPrefilter(elem,props,opts){var prop,value,toggle,tween,hooks,oldfire,anim=this,orig={},style=elem.style,hidden=elem.nodeType&&isHidden(elem),dataShow=data_priv.get(elem,"fxshow");opts.queue||(hooks=jQuery._queueHooks(elem,"fx"),null==hooks.unqueued&&(hooks.unqueued=0,oldfire=hooks.empty.fire,hooks.empty.fire=function(){hooks.unqueued||oldfire()}),hooks.unqueued++,anim.always(function(){anim.always(function(){hooks.unqueued--,jQuery.queue(elem,"fx").length||hooks.empty.fire()})})),1===elem.nodeType&&("height"in props||"width"in props)&&(opts.overflow=[style.overflow,style.overflowX,style.overflowY],"inline"===jQuery.css(elem,"display")&&"none"===jQuery.css(elem,"float")&&(style.display="inline-block")),opts.overflow&&(style.overflow="hidden",anim.always(function(){style.overflow=opts.overflow[0],style.overflowX=opts.overflow[1],style.overflowY=opts.overflow[2]}));for(prop in props)if(value=props[prop],rfxtypes.exec(value)){if(delete props[prop],toggle=toggle||"toggle"===value,value===(hidden?"hide":"show")){if("show"!==value||!dataShow||dataShow[prop]===undefined)continue;hidden=!0}orig[prop]=dataShow&&dataShow[prop]||jQuery.style(elem,prop)}if(!jQuery.isEmptyObject(orig)){dataShow?"hidden"in dataShow&&(hidden=dataShow.hidden):dataShow=data_priv.access(elem,"fxshow",{}),toggle&&(dataShow.hidden=!hidden),hidden?jQuery(elem).show():anim.done(function(){jQuery(elem).hide()}),anim.done(function(){var prop;data_priv.remove(elem,"fxshow");for(prop in orig)jQuery.style(elem,prop,orig[prop])});for(prop in orig)tween=createTween(hidden?dataShow[prop]:0,prop,anim),prop in dataShow||(dataShow[prop]=tween.start,hidden&&(tween.end=tween.start,tween.start="width"===prop||"height"===prop?1:0))}}function Tween(elem,options,prop,end,easing){return new Tween.prototype.init(elem,options,prop,end,easing)}function genFx(type,includeWidth){var which,attrs={height:type},i=0;for(includeWidth=includeWidth?1:0;4>i;i+=2-includeWidth)which=cssExpand[i],attrs["margin"+which]=attrs["padding"+which]=type;return includeWidth&&(attrs.opacity=attrs.width=type),attrs}function getWindow(elem){return jQuery.isWindow(elem)?elem:9===elem.nodeType&&elem.defaultView}var rootjQuery,readyList,core_strundefined=typeof undefined,location=window.location,document=window.document,docElem=document.documentElement,_jQuery=window.jQuery,_$=window.$,class2type={},core_deletedIds=[],core_version="2.0.3",core_concat=core_deletedIds.concat,core_push=core_deletedIds.push,core_slice=core_deletedIds.slice,core_indexOf=core_deletedIds.indexOf,core_toString=class2type.toString,core_hasOwn=class2type.hasOwnProperty,core_trim=core_version.trim,jQuery=function(selector,context){return new jQuery.fn.init(selector,context,rootjQuery)},core_pnum=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,core_rnotwhite=/\S+/g,rquickExpr=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/,rsingleTag=/^<(\w+)\s*\/?>(?:<\/\1>|)$/,rmsPrefix=/^-ms-/,rdashAlpha=/-([\da-z])/gi,fcamelCase=function(all,letter){return letter.toUpperCase()},completed=function(){document.removeEventListener("DOMContentLoaded",completed,!1),window.removeEventListener("load",completed,!1),jQuery.ready()};jQuery.fn=jQuery.prototype={jquery:core_version,constructor:jQuery,init:function(selector,context,rootjQuery){var match,elem;if(!selector)return this;if("string"==typeof selector){if(match="<"===selector.charAt(0)&&">"===selector.charAt(selector.length-1)&&selector.length>=3?[null,selector,null]:rquickExpr.exec(selector),!match||!match[1]&&context)return!context||context.jquery?(context||rootjQuery).find(selector):this.constructor(context).find(selector);if(match[1]){if(context=context instanceof jQuery?context[0]:context,jQuery.merge(this,jQuery.parseHTML(match[1],context&&context.nodeType?context.ownerDocument||context:document,!0)),rsingleTag.test(match[1])&&jQuery.isPlainObject(context))for(match in context)jQuery.isFunction(this[match])?this[match](context[match]):this.attr(match,context[match]);return this}return elem=document.getElementById(match[2]),elem&&elem.parentNode&&(this.length=1,this[0]=elem),this.context=document,this.selector=selector,this}return selector.nodeType?(this.context=this[0]=selector,this.length=1,this):jQuery.isFunction(selector)?rootjQuery.ready(selector):(selector.selector!==undefined&&(this.selector=selector.selector,this.context=selector.context),jQuery.makeArray(selector,this))
},selector:"",length:0,toArray:function(){return core_slice.call(this)},get:function(num){return null==num?this.toArray():0>num?this[this.length+num]:this[num]},pushStack:function(elems){var ret=jQuery.merge(this.constructor(),elems);return ret.prevObject=this,ret.context=this.context,ret},each:function(callback,args){return jQuery.each(this,callback,args)},ready:function(fn){return jQuery.ready.promise().done(fn),this},slice:function(){return this.pushStack(core_slice.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(i){var len=this.length,j=+i+(0>i?len:0);return this.pushStack(j>=0&&len>j?[this[j]]:[])},map:function(callback){return this.pushStack(jQuery.map(this,function(elem,i){return callback.call(elem,i,elem)}))},end:function(){return this.prevObject||this.constructor(null)},push:core_push,sort:[].sort,splice:[].splice},jQuery.fn.init.prototype=jQuery.fn,jQuery.extend=jQuery.fn.extend=function(){var options,name,src,copy,copyIsArray,clone,target=arguments[0]||{},i=1,length=arguments.length,deep=!1;for("boolean"==typeof target&&(deep=target,target=arguments[1]||{},i=2),"object"==typeof target||jQuery.isFunction(target)||(target={}),length===i&&(target=this,--i);length>i;i++)if(null!=(options=arguments[i]))for(name in options)src=target[name],copy=options[name],target!==copy&&(deep&&copy&&(jQuery.isPlainObject(copy)||(copyIsArray=jQuery.isArray(copy)))?(copyIsArray?(copyIsArray=!1,clone=src&&jQuery.isArray(src)?src:[]):clone=src&&jQuery.isPlainObject(src)?src:{},target[name]=jQuery.extend(deep,clone,copy)):copy!==undefined&&(target[name]=copy));return target},jQuery.extend({expando:"jQuery"+(core_version+Math.random()).replace(/\D/g,""),noConflict:function(deep){return window.$===jQuery&&(window.$=_$),deep&&window.jQuery===jQuery&&(window.jQuery=_jQuery),jQuery},isReady:!1,readyWait:1,holdReady:function(hold){hold?jQuery.readyWait++:jQuery.ready(!0)},ready:function(wait){(wait===!0?--jQuery.readyWait:jQuery.isReady)||(jQuery.isReady=!0,wait!==!0&&--jQuery.readyWait>0||(readyList.resolveWith(document,[jQuery]),jQuery.fn.trigger&&jQuery(document).trigger("ready").off("ready")))},isFunction:function(obj){return"function"===jQuery.type(obj)},isArray:Array.isArray,isWindow:function(obj){return null!=obj&&obj===obj.window},isNumeric:function(obj){return!isNaN(parseFloat(obj))&&isFinite(obj)},type:function(obj){return null==obj?String(obj):"object"==typeof obj||"function"==typeof obj?class2type[core_toString.call(obj)]||"object":typeof obj},isPlainObject:function(obj){if("object"!==jQuery.type(obj)||obj.nodeType||jQuery.isWindow(obj))return!1;try{if(obj.constructor&&!core_hasOwn.call(obj.constructor.prototype,"isPrototypeOf"))return!1}catch(e){return!1}return!0},isEmptyObject:function(obj){var name;for(name in obj)return!1;return!0},error:function(msg){throw new Error(msg)},parseHTML:function(data,context,keepScripts){if(!data||"string"!=typeof data)return null;"boolean"==typeof context&&(keepScripts=context,context=!1),context=context||document;var parsed=rsingleTag.exec(data),scripts=!keepScripts&&[];return parsed?[context.createElement(parsed[1])]:(parsed=jQuery.buildFragment([data],context,scripts),scripts&&jQuery(scripts).remove(),jQuery.merge([],parsed.childNodes))},parseJSON:JSON.parse,parseXML:function(data){var xml,tmp;if(!data||"string"!=typeof data)return null;try{tmp=new DOMParser,xml=tmp.parseFromString(data,"text/xml")}catch(e){xml=undefined}return(!xml||xml.getElementsByTagName("parsererror").length)&&jQuery.error("Invalid XML: "+data),xml},noop:function(){},globalEval:function(code){var script,indirect=eval;code=jQuery.trim(code),code&&(1===code.indexOf("use strict")?(script=document.createElement("script"),script.text=code,document.head.appendChild(script).parentNode.removeChild(script)):indirect(code))},camelCase:function(string){return string.replace(rmsPrefix,"ms-").replace(rdashAlpha,fcamelCase)},nodeName:function(elem,name){return elem.nodeName&&elem.nodeName.toLowerCase()===name.toLowerCase()},each:function(obj,callback,args){var value,i=0,length=obj.length,isArray=isArraylike(obj);if(args){if(isArray)for(;length>i&&(value=callback.apply(obj[i],args),value!==!1);i++);else for(i in obj)if(value=callback.apply(obj[i],args),value===!1)break}else if(isArray)for(;length>i&&(value=callback.call(obj[i],i,obj[i]),value!==!1);i++);else for(i in obj)if(value=callback.call(obj[i],i,obj[i]),value===!1)break;return obj},trim:function(text){return null==text?"":core_trim.call(text)},makeArray:function(arr,results){var ret=results||[];return null!=arr&&(isArraylike(Object(arr))?jQuery.merge(ret,"string"==typeof arr?[arr]:arr):core_push.call(ret,arr)),ret},inArray:function(elem,arr,i){return null==arr?-1:core_indexOf.call(arr,elem,i)},merge:function(first,second){var l=second.length,i=first.length,j=0;if("number"==typeof l)for(;l>j;j++)first[i++]=second[j];else for(;second[j]!==undefined;)first[i++]=second[j++];return first.length=i,first},grep:function(elems,callback,inv){var retVal,ret=[],i=0,length=elems.length;for(inv=!!inv;length>i;i++)retVal=!!callback(elems[i],i),inv!==retVal&&ret.push(elems[i]);return ret},map:function(elems,callback,arg){var value,i=0,length=elems.length,isArray=isArraylike(elems),ret=[];if(isArray)for(;length>i;i++)value=callback(elems[i],i,arg),null!=value&&(ret[ret.length]=value);else for(i in elems)value=callback(elems[i],i,arg),null!=value&&(ret[ret.length]=value);return core_concat.apply([],ret)},guid:1,proxy:function(fn,context){var tmp,args,proxy;return"string"==typeof context&&(tmp=fn[context],context=fn,fn=tmp),jQuery.isFunction(fn)?(args=core_slice.call(arguments,2),proxy=function(){return fn.apply(context||this,args.concat(core_slice.call(arguments)))},proxy.guid=fn.guid=fn.guid||jQuery.guid++,proxy):undefined},access:function(elems,fn,key,value,chainable,emptyGet,raw){var i=0,length=elems.length,bulk=null==key;if("object"===jQuery.type(key)){chainable=!0;for(i in key)jQuery.access(elems,fn,i,key[i],!0,emptyGet,raw)}else if(value!==undefined&&(chainable=!0,jQuery.isFunction(value)||(raw=!0),bulk&&(raw?(fn.call(elems,value),fn=null):(bulk=fn,fn=function(elem,key,value){return bulk.call(jQuery(elem),value)})),fn))for(;length>i;i++)fn(elems[i],key,raw?value:value.call(elems[i],i,fn(elems[i],key)));return chainable?elems:bulk?fn.call(elems):length?fn(elems[0],key):emptyGet},now:Date.now,swap:function(elem,options,callback,args){var ret,name,old={};for(name in options)old[name]=elem.style[name],elem.style[name]=options[name];ret=callback.apply(elem,args||[]);for(name in options)elem.style[name]=old[name];return ret}}),jQuery.ready.promise=function(obj){return readyList||(readyList=jQuery.Deferred(),"complete"===document.readyState?setTimeout(jQuery.ready):(document.addEventListener("DOMContentLoaded",completed,!1),window.addEventListener("load",completed,!1))),readyList.promise(obj)},jQuery.each("Boolean Number String Function Array Date RegExp Object Error".split(" "),function(i,name){class2type["[object "+name+"]"]=name.toLowerCase()}),rootjQuery=jQuery(document),function(window,undefined){function Sizzle(selector,context,results,seed){var match,elem,m,nodeType,i,groups,old,nid,newContext,newSelector;if((context?context.ownerDocument||context:preferredDoc)!==document&&setDocument(context),context=context||document,results=results||[],!selector||"string"!=typeof selector)return results;if(1!==(nodeType=context.nodeType)&&9!==nodeType)return[];if(documentIsHTML&&!seed){if(match=rquickExpr.exec(selector))if(m=match[1]){if(9===nodeType){if(elem=context.getElementById(m),!elem||!elem.parentNode)return results;if(elem.id===m)return results.push(elem),results}else if(context.ownerDocument&&(elem=context.ownerDocument.getElementById(m))&&contains(context,elem)&&elem.id===m)return results.push(elem),results}else{if(match[2])return push.apply(results,context.getElementsByTagName(selector)),results;if((m=match[3])&&support.getElementsByClassName&&context.getElementsByClassName)return push.apply(results,context.getElementsByClassName(m)),results}if(support.qsa&&(!rbuggyQSA||!rbuggyQSA.test(selector))){if(nid=old=expando,newContext=context,newSelector=9===nodeType&&selector,1===nodeType&&"object"!==context.nodeName.toLowerCase()){for(groups=tokenize(selector),(old=context.getAttribute("id"))?nid=old.replace(rescape,"\\$&"):context.setAttribute("id",nid),nid="[id='"+nid+"'] ",i=groups.length;i--;)groups[i]=nid+toSelector(groups[i]);newContext=rsibling.test(selector)&&context.parentNode||context,newSelector=groups.join(",")}if(newSelector)try{return push.apply(results,newContext.querySelectorAll(newSelector)),results}catch(qsaError){}finally{old||context.removeAttribute("id")}}}return select(selector.replace(rtrim,"$1"),context,results,seed)}function createCache(){function cache(key,value){return keys.push(key+=" ")>Expr.cacheLength&&delete cache[keys.shift()],cache[key]=value}var keys=[];return cache}function markFunction(fn){return fn[expando]=!0,fn}function assert(fn){var div=document.createElement("div");try{return!!fn(div)}catch(e){return!1}finally{div.parentNode&&div.parentNode.removeChild(div),div=null}}function addHandle(attrs,handler){for(var arr=attrs.split("|"),i=attrs.length;i--;)Expr.attrHandle[arr[i]]=handler}function siblingCheck(a,b){var cur=b&&a,diff=cur&&1===a.nodeType&&1===b.nodeType&&(~b.sourceIndex||MAX_NEGATIVE)-(~a.sourceIndex||MAX_NEGATIVE);if(diff)return diff;if(cur)for(;cur=cur.nextSibling;)if(cur===b)return-1;return a?1:-1}function createInputPseudo(type){return function(elem){var name=elem.nodeName.toLowerCase();return"input"===name&&elem.type===type}}function createButtonPseudo(type){return function(elem){var name=elem.nodeName.toLowerCase();return("input"===name||"button"===name)&&elem.type===type}}function createPositionalPseudo(fn){return markFunction(function(argument){return argument=+argument,markFunction(function(seed,matches){for(var j,matchIndexes=fn([],seed.length,argument),i=matchIndexes.length;i--;)seed[j=matchIndexes[i]]&&(seed[j]=!(matches[j]=seed[j]))})})}function setFilters(){}function tokenize(selector,parseOnly){var matched,match,tokens,type,soFar,groups,preFilters,cached=tokenCache[selector+" "];if(cached)return parseOnly?0:cached.slice(0);for(soFar=selector,groups=[],preFilters=Expr.preFilter;soFar;){(!matched||(match=rcomma.exec(soFar)))&&(match&&(soFar=soFar.slice(match[0].length)||soFar),groups.push(tokens=[])),matched=!1,(match=rcombinators.exec(soFar))&&(matched=match.shift(),tokens.push({value:matched,type:match[0].replace(rtrim," ")}),soFar=soFar.slice(matched.length));for(type in Expr.filter)!(match=matchExpr[type].exec(soFar))||preFilters[type]&&!(match=preFilters[type](match))||(matched=match.shift(),tokens.push({value:matched,type:type,matches:match}),soFar=soFar.slice(matched.length));if(!matched)break}return parseOnly?soFar.length:soFar?Sizzle.error(selector):tokenCache(selector,groups).slice(0)}function toSelector(tokens){for(var i=0,len=tokens.length,selector="";len>i;i++)selector+=tokens[i].value;return selector}function addCombinator(matcher,combinator,base){var dir=combinator.dir,checkNonElements=base&&"parentNode"===dir,doneName=done++;return combinator.first?function(elem,context,xml){for(;elem=elem[dir];)if(1===elem.nodeType||checkNonElements)return matcher(elem,context,xml)}:function(elem,context,xml){var data,cache,outerCache,dirkey=dirruns+" "+doneName;if(xml){for(;elem=elem[dir];)if((1===elem.nodeType||checkNonElements)&&matcher(elem,context,xml))return!0}else for(;elem=elem[dir];)if(1===elem.nodeType||checkNonElements)if(outerCache=elem[expando]||(elem[expando]={}),(cache=outerCache[dir])&&cache[0]===dirkey){if((data=cache[1])===!0||data===cachedruns)return data===!0}else if(cache=outerCache[dir]=[dirkey],cache[1]=matcher(elem,context,xml)||cachedruns,cache[1]===!0)return!0}}function elementMatcher(matchers){return matchers.length>1?function(elem,context,xml){for(var i=matchers.length;i--;)if(!matchers[i](elem,context,xml))return!1;return!0}:matchers[0]}function condense(unmatched,map,filter,context,xml){for(var elem,newUnmatched=[],i=0,len=unmatched.length,mapped=null!=map;len>i;i++)(elem=unmatched[i])&&(!filter||filter(elem,context,xml))&&(newUnmatched.push(elem),mapped&&map.push(i));return newUnmatched}function setMatcher(preFilter,selector,matcher,postFilter,postFinder,postSelector){return postFilter&&!postFilter[expando]&&(postFilter=setMatcher(postFilter)),postFinder&&!postFinder[expando]&&(postFinder=setMatcher(postFinder,postSelector)),markFunction(function(seed,results,context,xml){var temp,i,elem,preMap=[],postMap=[],preexisting=results.length,elems=seed||multipleContexts(selector||"*",context.nodeType?[context]:context,[]),matcherIn=!preFilter||!seed&&selector?elems:condense(elems,preMap,preFilter,context,xml),matcherOut=matcher?postFinder||(seed?preFilter:preexisting||postFilter)?[]:results:matcherIn;if(matcher&&matcher(matcherIn,matcherOut,context,xml),postFilter)for(temp=condense(matcherOut,postMap),postFilter(temp,[],context,xml),i=temp.length;i--;)(elem=temp[i])&&(matcherOut[postMap[i]]=!(matcherIn[postMap[i]]=elem));if(seed){if(postFinder||preFilter){if(postFinder){for(temp=[],i=matcherOut.length;i--;)(elem=matcherOut[i])&&temp.push(matcherIn[i]=elem);postFinder(null,matcherOut=[],temp,xml)}for(i=matcherOut.length;i--;)(elem=matcherOut[i])&&(temp=postFinder?indexOf.call(seed,elem):preMap[i])>-1&&(seed[temp]=!(results[temp]=elem))}}else matcherOut=condense(matcherOut===results?matcherOut.splice(preexisting,matcherOut.length):matcherOut),postFinder?postFinder(null,results,matcherOut,xml):push.apply(results,matcherOut)})}function matcherFromTokens(tokens){for(var checkContext,matcher,j,len=tokens.length,leadingRelative=Expr.relative[tokens[0].type],implicitRelative=leadingRelative||Expr.relative[" "],i=leadingRelative?1:0,matchContext=addCombinator(function(elem){return elem===checkContext},implicitRelative,!0),matchAnyContext=addCombinator(function(elem){return indexOf.call(checkContext,elem)>-1},implicitRelative,!0),matchers=[function(elem,context,xml){return!leadingRelative&&(xml||context!==outermostContext)||((checkContext=context).nodeType?matchContext(elem,context,xml):matchAnyContext(elem,context,xml))}];len>i;i++)if(matcher=Expr.relative[tokens[i].type])matchers=[addCombinator(elementMatcher(matchers),matcher)];else{if(matcher=Expr.filter[tokens[i].type].apply(null,tokens[i].matches),matcher[expando]){for(j=++i;len>j&&!Expr.relative[tokens[j].type];j++);return setMatcher(i>1&&elementMatcher(matchers),i>1&&toSelector(tokens.slice(0,i-1).concat({value:" "===tokens[i-2].type?"*":""})).replace(rtrim,"$1"),matcher,j>i&&matcherFromTokens(tokens.slice(i,j)),len>j&&matcherFromTokens(tokens=tokens.slice(j)),len>j&&toSelector(tokens))}matchers.push(matcher)}return elementMatcher(matchers)}function matcherFromGroupMatchers(elementMatchers,setMatchers){var matcherCachedRuns=0,bySet=setMatchers.length>0,byElement=elementMatchers.length>0,superMatcher=function(seed,context,xml,results,expandContext){var elem,j,matcher,setMatched=[],matchedCount=0,i="0",unmatched=seed&&[],outermost=null!=expandContext,contextBackup=outermostContext,elems=seed||byElement&&Expr.find.TAG("*",expandContext&&context.parentNode||context),dirrunsUnique=dirruns+=null==contextBackup?1:Math.random()||.1;for(outermost&&(outermostContext=context!==document&&context,cachedruns=matcherCachedRuns);null!=(elem=elems[i]);i++){if(byElement&&elem){for(j=0;matcher=elementMatchers[j++];)if(matcher(elem,context,xml)){results.push(elem);break}outermost&&(dirruns=dirrunsUnique,cachedruns=++matcherCachedRuns)}bySet&&((elem=!matcher&&elem)&&matchedCount--,seed&&unmatched.push(elem))}if(matchedCount+=i,bySet&&i!==matchedCount){for(j=0;matcher=setMatchers[j++];)matcher(unmatched,setMatched,context,xml);if(seed){if(matchedCount>0)for(;i--;)unmatched[i]||setMatched[i]||(setMatched[i]=pop.call(results));setMatched=condense(setMatched)}push.apply(results,setMatched),outermost&&!seed&&setMatched.length>0&&matchedCount+setMatchers.length>1&&Sizzle.uniqueSort(results)}return outermost&&(dirruns=dirrunsUnique,outermostContext=contextBackup),unmatched};return bySet?markFunction(superMatcher):superMatcher}function multipleContexts(selector,contexts,results){for(var i=0,len=contexts.length;len>i;i++)Sizzle(selector,contexts[i],results);return results}function select(selector,context,results,seed){var i,tokens,token,type,find,match=tokenize(selector);if(!seed&&1===match.length){if(tokens=match[0]=match[0].slice(0),tokens.length>2&&"ID"===(token=tokens[0]).type&&support.getById&&9===context.nodeType&&documentIsHTML&&Expr.relative[tokens[1].type]){if(context=(Expr.find.ID(token.matches[0].replace(runescape,funescape),context)||[])[0],!context)return results;selector=selector.slice(tokens.shift().value.length)}for(i=matchExpr.needsContext.test(selector)?0:tokens.length;i--&&(token=tokens[i],!Expr.relative[type=token.type]);)if((find=Expr.find[type])&&(seed=find(token.matches[0].replace(runescape,funescape),rsibling.test(tokens[0].type)&&context.parentNode||context))){if(tokens.splice(i,1),selector=seed.length&&toSelector(tokens),!selector)return push.apply(results,seed),results;break}}return compile(selector,match)(seed,context,!documentIsHTML,results,rsibling.test(selector)),results}var i,support,cachedruns,Expr,getText,isXML,compile,outermostContext,sortInput,setDocument,document,docElem,documentIsHTML,rbuggyQSA,rbuggyMatches,matches,contains,expando="sizzle"+-new Date,preferredDoc=window.document,dirruns=0,done=0,classCache=createCache(),tokenCache=createCache(),compilerCache=createCache(),hasDuplicate=!1,sortOrder=function(a,b){return a===b?(hasDuplicate=!0,0):0},strundefined=typeof undefined,MAX_NEGATIVE=1<<31,hasOwn={}.hasOwnProperty,arr=[],pop=arr.pop,push_native=arr.push,push=arr.push,slice=arr.slice,indexOf=arr.indexOf||function(elem){for(var i=0,len=this.length;len>i;i++)if(this[i]===elem)return i;return-1},booleans="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",whitespace="[\\x20\\t\\r\\n\\f]",characterEncoding="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",identifier=characterEncoding.replace("w","w#"),attributes="\\["+whitespace+"*("+characterEncoding+")"+whitespace+"*(?:([*^$|!~]?=)"+whitespace+"*(?:(['\"])((?:\\\\.|[^\\\\])*?)\\3|("+identifier+")|)|)"+whitespace+"*\\]",pseudos=":("+characterEncoding+")(?:\\(((['\"])((?:\\\\.|[^\\\\])*?)\\3|((?:\\\\.|[^\\\\()[\\]]|"+attributes.replace(3,8)+")*)|.*)\\)|)",rtrim=new RegExp("^"+whitespace+"+|((?:^|[^\\\\])(?:\\\\.)*)"+whitespace+"+$","g"),rcomma=new RegExp("^"+whitespace+"*,"+whitespace+"*"),rcombinators=new RegExp("^"+whitespace+"*([>+~]|"+whitespace+")"+whitespace+"*"),rsibling=new RegExp(whitespace+"*[+~]"),rattributeQuotes=new RegExp("="+whitespace+"*([^\\]'\"]*)"+whitespace+"*\\]","g"),rpseudo=new RegExp(pseudos),ridentifier=new RegExp("^"+identifier+"$"),matchExpr={ID:new RegExp("^#("+characterEncoding+")"),CLASS:new RegExp("^\\.("+characterEncoding+")"),TAG:new RegExp("^("+characterEncoding.replace("w","w*")+")"),ATTR:new RegExp("^"+attributes),PSEUDO:new RegExp("^"+pseudos),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+whitespace+"*(even|odd|(([+-]|)(\\d*)n|)"+whitespace+"*(?:([+-]|)"+whitespace+"*(\\d+)|))"+whitespace+"*\\)|)","i"),bool:new RegExp("^(?:"+booleans+")$","i"),needsContext:new RegExp("^"+whitespace+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+whitespace+"*((?:-\\d)?\\d*)"+whitespace+"*\\)|)(?=[^-]|$)","i")},rnative=/^[^{]+\{\s*\[native \w/,rquickExpr=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,rinputs=/^(?:input|select|textarea|button)$/i,rheader=/^h\d$/i,rescape=/'|\\/g,runescape=new RegExp("\\\\([\\da-f]{1,6}"+whitespace+"?|("+whitespace+")|.)","ig"),funescape=function(_,escaped,escapedWhitespace){var high="0x"+escaped-65536;return high!==high||escapedWhitespace?escaped:0>high?String.fromCharCode(high+65536):String.fromCharCode(high>>10|55296,1023&high|56320)};try{push.apply(arr=slice.call(preferredDoc.childNodes),preferredDoc.childNodes),arr[preferredDoc.childNodes.length].nodeType}catch(e){push={apply:arr.length?function(target,els){push_native.apply(target,slice.call(els))}:function(target,els){for(var j=target.length,i=0;target[j++]=els[i++];);target.length=j-1}}}isXML=Sizzle.isXML=function(elem){var documentElement=elem&&(elem.ownerDocument||elem).documentElement;return documentElement?"HTML"!==documentElement.nodeName:!1},support=Sizzle.support={},setDocument=Sizzle.setDocument=function(node){var doc=node?node.ownerDocument||node:preferredDoc,parent=doc.defaultView;return doc!==document&&9===doc.nodeType&&doc.documentElement?(document=doc,docElem=doc.documentElement,documentIsHTML=!isXML(doc),parent&&parent.attachEvent&&parent!==parent.top&&parent.attachEvent("onbeforeunload",function(){setDocument()}),support.attributes=assert(function(div){return div.className="i",!div.getAttribute("className")}),support.getElementsByTagName=assert(function(div){return div.appendChild(doc.createComment("")),!div.getElementsByTagName("*").length}),support.getElementsByClassName=assert(function(div){return div.innerHTML="<div class='a'></div><div class='a i'></div>",div.firstChild.className="i",2===div.getElementsByClassName("i").length}),support.getById=assert(function(div){return docElem.appendChild(div).id=expando,!doc.getElementsByName||!doc.getElementsByName(expando).length}),support.getById?(Expr.find.ID=function(id,context){if(typeof context.getElementById!==strundefined&&documentIsHTML){var m=context.getElementById(id);return m&&m.parentNode?[m]:[]}},Expr.filter.ID=function(id){var attrId=id.replace(runescape,funescape);return function(elem){return elem.getAttribute("id")===attrId}}):(delete Expr.find.ID,Expr.filter.ID=function(id){var attrId=id.replace(runescape,funescape);return function(elem){var node=typeof elem.getAttributeNode!==strundefined&&elem.getAttributeNode("id");return node&&node.value===attrId}}),Expr.find.TAG=support.getElementsByTagName?function(tag,context){return typeof context.getElementsByTagName!==strundefined?context.getElementsByTagName(tag):void 0}:function(tag,context){var elem,tmp=[],i=0,results=context.getElementsByTagName(tag);if("*"===tag){for(;elem=results[i++];)1===elem.nodeType&&tmp.push(elem);return tmp}return results},Expr.find.CLASS=support.getElementsByClassName&&function(className,context){return typeof context.getElementsByClassName!==strundefined&&documentIsHTML?context.getElementsByClassName(className):void 0},rbuggyMatches=[],rbuggyQSA=[],(support.qsa=rnative.test(doc.querySelectorAll))&&(assert(function(div){div.innerHTML="<select><option selected=''></option></select>",div.querySelectorAll("[selected]").length||rbuggyQSA.push("\\["+whitespace+"*(?:value|"+booleans+")"),div.querySelectorAll(":checked").length||rbuggyQSA.push(":checked")}),assert(function(div){var input=doc.createElement("input");input.setAttribute("type","hidden"),div.appendChild(input).setAttribute("t",""),div.querySelectorAll("[t^='']").length&&rbuggyQSA.push("[*^$]="+whitespace+"*(?:''|\"\")"),div.querySelectorAll(":enabled").length||rbuggyQSA.push(":enabled",":disabled"),div.querySelectorAll("*,:x"),rbuggyQSA.push(",.*:")})),(support.matchesSelector=rnative.test(matches=docElem.webkitMatchesSelector||docElem.mozMatchesSelector||docElem.oMatchesSelector||docElem.msMatchesSelector))&&assert(function(div){support.disconnectedMatch=matches.call(div,"div"),matches.call(div,"[s!='']:x"),rbuggyMatches.push("!=",pseudos)}),rbuggyQSA=rbuggyQSA.length&&new RegExp(rbuggyQSA.join("|")),rbuggyMatches=rbuggyMatches.length&&new RegExp(rbuggyMatches.join("|")),contains=rnative.test(docElem.contains)||docElem.compareDocumentPosition?function(a,b){var adown=9===a.nodeType?a.documentElement:a,bup=b&&b.parentNode;return a===bup||!(!bup||1!==bup.nodeType||!(adown.contains?adown.contains(bup):a.compareDocumentPosition&&16&a.compareDocumentPosition(bup)))}:function(a,b){if(b)for(;b=b.parentNode;)if(b===a)return!0;return!1},sortOrder=docElem.compareDocumentPosition?function(a,b){if(a===b)return hasDuplicate=!0,0;var compare=b.compareDocumentPosition&&a.compareDocumentPosition&&a.compareDocumentPosition(b);return compare?1&compare||!support.sortDetached&&b.compareDocumentPosition(a)===compare?a===doc||contains(preferredDoc,a)?-1:b===doc||contains(preferredDoc,b)?1:sortInput?indexOf.call(sortInput,a)-indexOf.call(sortInput,b):0:4&compare?-1:1:a.compareDocumentPosition?-1:1}:function(a,b){var cur,i=0,aup=a.parentNode,bup=b.parentNode,ap=[a],bp=[b];if(a===b)return hasDuplicate=!0,0;if(!aup||!bup)return a===doc?-1:b===doc?1:aup?-1:bup?1:sortInput?indexOf.call(sortInput,a)-indexOf.call(sortInput,b):0;if(aup===bup)return siblingCheck(a,b);for(cur=a;cur=cur.parentNode;)ap.unshift(cur);for(cur=b;cur=cur.parentNode;)bp.unshift(cur);for(;ap[i]===bp[i];)i++;return i?siblingCheck(ap[i],bp[i]):ap[i]===preferredDoc?-1:bp[i]===preferredDoc?1:0},doc):document},Sizzle.matches=function(expr,elements){return Sizzle(expr,null,null,elements)},Sizzle.matchesSelector=function(elem,expr){if((elem.ownerDocument||elem)!==document&&setDocument(elem),expr=expr.replace(rattributeQuotes,"='$1']"),!(!support.matchesSelector||!documentIsHTML||rbuggyMatches&&rbuggyMatches.test(expr)||rbuggyQSA&&rbuggyQSA.test(expr)))try{var ret=matches.call(elem,expr);if(ret||support.disconnectedMatch||elem.document&&11!==elem.document.nodeType)return ret}catch(e){}return Sizzle(expr,document,null,[elem]).length>0},Sizzle.contains=function(context,elem){return(context.ownerDocument||context)!==document&&setDocument(context),contains(context,elem)},Sizzle.attr=function(elem,name){(elem.ownerDocument||elem)!==document&&setDocument(elem);var fn=Expr.attrHandle[name.toLowerCase()],val=fn&&hasOwn.call(Expr.attrHandle,name.toLowerCase())?fn(elem,name,!documentIsHTML):undefined;return val===undefined?support.attributes||!documentIsHTML?elem.getAttribute(name):(val=elem.getAttributeNode(name))&&val.specified?val.value:null:val},Sizzle.error=function(msg){throw new Error("Syntax error, unrecognized expression: "+msg)},Sizzle.uniqueSort=function(results){var elem,duplicates=[],j=0,i=0;if(hasDuplicate=!support.detectDuplicates,sortInput=!support.sortStable&&results.slice(0),results.sort(sortOrder),hasDuplicate){for(;elem=results[i++];)elem===results[i]&&(j=duplicates.push(i));for(;j--;)results.splice(duplicates[j],1)}return results},getText=Sizzle.getText=function(elem){var node,ret="",i=0,nodeType=elem.nodeType;if(nodeType){if(1===nodeType||9===nodeType||11===nodeType){if("string"==typeof elem.textContent)return elem.textContent;for(elem=elem.firstChild;elem;elem=elem.nextSibling)ret+=getText(elem)}else if(3===nodeType||4===nodeType)return elem.nodeValue}else for(;node=elem[i];i++)ret+=getText(node);return ret},Expr=Sizzle.selectors={cacheLength:50,createPseudo:markFunction,match:matchExpr,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(match){return match[1]=match[1].replace(runescape,funescape),match[3]=(match[4]||match[5]||"").replace(runescape,funescape),"~="===match[2]&&(match[3]=" "+match[3]+" "),match.slice(0,4)},CHILD:function(match){return match[1]=match[1].toLowerCase(),"nth"===match[1].slice(0,3)?(match[3]||Sizzle.error(match[0]),match[4]=+(match[4]?match[5]+(match[6]||1):2*("even"===match[3]||"odd"===match[3])),match[5]=+(match[7]+match[8]||"odd"===match[3])):match[3]&&Sizzle.error(match[0]),match},PSEUDO:function(match){var excess,unquoted=!match[5]&&match[2];return matchExpr.CHILD.test(match[0])?null:(match[3]&&match[4]!==undefined?match[2]=match[4]:unquoted&&rpseudo.test(unquoted)&&(excess=tokenize(unquoted,!0))&&(excess=unquoted.indexOf(")",unquoted.length-excess)-unquoted.length)&&(match[0]=match[0].slice(0,excess),match[2]=unquoted.slice(0,excess)),match.slice(0,3))}},filter:{TAG:function(nodeNameSelector){var nodeName=nodeNameSelector.replace(runescape,funescape).toLowerCase();return"*"===nodeNameSelector?function(){return!0}:function(elem){return elem.nodeName&&elem.nodeName.toLowerCase()===nodeName}},CLASS:function(className){var pattern=classCache[className+" "];return pattern||(pattern=new RegExp("(^|"+whitespace+")"+className+"("+whitespace+"|$)"))&&classCache(className,function(elem){return pattern.test("string"==typeof elem.className&&elem.className||typeof elem.getAttribute!==strundefined&&elem.getAttribute("class")||"")})},ATTR:function(name,operator,check){return function(elem){var result=Sizzle.attr(elem,name);return null==result?"!="===operator:operator?(result+="","="===operator?result===check:"!="===operator?result!==check:"^="===operator?check&&0===result.indexOf(check):"*="===operator?check&&result.indexOf(check)>-1:"$="===operator?check&&result.slice(-check.length)===check:"~="===operator?(" "+result+" ").indexOf(check)>-1:"|="===operator?result===check||result.slice(0,check.length+1)===check+"-":!1):!0}},CHILD:function(type,what,argument,first,last){var simple="nth"!==type.slice(0,3),forward="last"!==type.slice(-4),ofType="of-type"===what;return 1===first&&0===last?function(elem){return!!elem.parentNode}:function(elem,context,xml){var cache,outerCache,node,diff,nodeIndex,start,dir=simple!==forward?"nextSibling":"previousSibling",parent=elem.parentNode,name=ofType&&elem.nodeName.toLowerCase(),useCache=!xml&&!ofType;if(parent){if(simple){for(;dir;){for(node=elem;node=node[dir];)if(ofType?node.nodeName.toLowerCase()===name:1===node.nodeType)return!1;start=dir="only"===type&&!start&&"nextSibling"}return!0}if(start=[forward?parent.firstChild:parent.lastChild],forward&&useCache){for(outerCache=parent[expando]||(parent[expando]={}),cache=outerCache[type]||[],nodeIndex=cache[0]===dirruns&&cache[1],diff=cache[0]===dirruns&&cache[2],node=nodeIndex&&parent.childNodes[nodeIndex];node=++nodeIndex&&node&&node[dir]||(diff=nodeIndex=0)||start.pop();)if(1===node.nodeType&&++diff&&node===elem){outerCache[type]=[dirruns,nodeIndex,diff];break}}else if(useCache&&(cache=(elem[expando]||(elem[expando]={}))[type])&&cache[0]===dirruns)diff=cache[1];else for(;(node=++nodeIndex&&node&&node[dir]||(diff=nodeIndex=0)||start.pop())&&((ofType?node.nodeName.toLowerCase()!==name:1!==node.nodeType)||!++diff||(useCache&&((node[expando]||(node[expando]={}))[type]=[dirruns,diff]),node!==elem)););return diff-=last,diff===first||diff%first===0&&diff/first>=0}}},PSEUDO:function(pseudo,argument){var args,fn=Expr.pseudos[pseudo]||Expr.setFilters[pseudo.toLowerCase()]||Sizzle.error("unsupported pseudo: "+pseudo);return fn[expando]?fn(argument):fn.length>1?(args=[pseudo,pseudo,"",argument],Expr.setFilters.hasOwnProperty(pseudo.toLowerCase())?markFunction(function(seed,matches){for(var idx,matched=fn(seed,argument),i=matched.length;i--;)idx=indexOf.call(seed,matched[i]),seed[idx]=!(matches[idx]=matched[i])}):function(elem){return fn(elem,0,args)}):fn}},pseudos:{not:markFunction(function(selector){var input=[],results=[],matcher=compile(selector.replace(rtrim,"$1"));return matcher[expando]?markFunction(function(seed,matches,context,xml){for(var elem,unmatched=matcher(seed,null,xml,[]),i=seed.length;i--;)(elem=unmatched[i])&&(seed[i]=!(matches[i]=elem))}):function(elem,context,xml){return input[0]=elem,matcher(input,null,xml,results),!results.pop()}}),has:markFunction(function(selector){return function(elem){return Sizzle(selector,elem).length>0}}),contains:markFunction(function(text){return function(elem){return(elem.textContent||elem.innerText||getText(elem)).indexOf(text)>-1}}),lang:markFunction(function(lang){return ridentifier.test(lang||"")||Sizzle.error("unsupported lang: "+lang),lang=lang.replace(runescape,funescape).toLowerCase(),function(elem){var elemLang;do if(elemLang=documentIsHTML?elem.lang:elem.getAttribute("xml:lang")||elem.getAttribute("lang"))return elemLang=elemLang.toLowerCase(),elemLang===lang||0===elemLang.indexOf(lang+"-");while((elem=elem.parentNode)&&1===elem.nodeType);
return!1}}),target:function(elem){var hash=window.location&&window.location.hash;return hash&&hash.slice(1)===elem.id},root:function(elem){return elem===docElem},focus:function(elem){return elem===document.activeElement&&(!document.hasFocus||document.hasFocus())&&!!(elem.type||elem.href||~elem.tabIndex)},enabled:function(elem){return elem.disabled===!1},disabled:function(elem){return elem.disabled===!0},checked:function(elem){var nodeName=elem.nodeName.toLowerCase();return"input"===nodeName&&!!elem.checked||"option"===nodeName&&!!elem.selected},selected:function(elem){return elem.parentNode&&elem.parentNode.selectedIndex,elem.selected===!0},empty:function(elem){for(elem=elem.firstChild;elem;elem=elem.nextSibling)if(elem.nodeName>"@"||3===elem.nodeType||4===elem.nodeType)return!1;return!0},parent:function(elem){return!Expr.pseudos.empty(elem)},header:function(elem){return rheader.test(elem.nodeName)},input:function(elem){return rinputs.test(elem.nodeName)},button:function(elem){var name=elem.nodeName.toLowerCase();return"input"===name&&"button"===elem.type||"button"===name},text:function(elem){var attr;return"input"===elem.nodeName.toLowerCase()&&"text"===elem.type&&(null==(attr=elem.getAttribute("type"))||attr.toLowerCase()===elem.type)},first:createPositionalPseudo(function(){return[0]}),last:createPositionalPseudo(function(matchIndexes,length){return[length-1]}),eq:createPositionalPseudo(function(matchIndexes,length,argument){return[0>argument?argument+length:argument]}),even:createPositionalPseudo(function(matchIndexes,length){for(var i=0;length>i;i+=2)matchIndexes.push(i);return matchIndexes}),odd:createPositionalPseudo(function(matchIndexes,length){for(var i=1;length>i;i+=2)matchIndexes.push(i);return matchIndexes}),lt:createPositionalPseudo(function(matchIndexes,length,argument){for(var i=0>argument?argument+length:argument;--i>=0;)matchIndexes.push(i);return matchIndexes}),gt:createPositionalPseudo(function(matchIndexes,length,argument){for(var i=0>argument?argument+length:argument;++i<length;)matchIndexes.push(i);return matchIndexes})}},Expr.pseudos.nth=Expr.pseudos.eq;for(i in{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})Expr.pseudos[i]=createInputPseudo(i);for(i in{submit:!0,reset:!0})Expr.pseudos[i]=createButtonPseudo(i);setFilters.prototype=Expr.filters=Expr.pseudos,Expr.setFilters=new setFilters,compile=Sizzle.compile=function(selector,group){var i,setMatchers=[],elementMatchers=[],cached=compilerCache[selector+" "];if(!cached){for(group||(group=tokenize(selector)),i=group.length;i--;)cached=matcherFromTokens(group[i]),cached[expando]?setMatchers.push(cached):elementMatchers.push(cached);cached=compilerCache(selector,matcherFromGroupMatchers(elementMatchers,setMatchers))}return cached},support.sortStable=expando.split("").sort(sortOrder).join("")===expando,support.detectDuplicates=hasDuplicate,setDocument(),support.sortDetached=assert(function(div1){return 1&div1.compareDocumentPosition(document.createElement("div"))}),assert(function(div){return div.innerHTML="<a href='#'></a>","#"===div.firstChild.getAttribute("href")})||addHandle("type|href|height|width",function(elem,name,isXML){return isXML?void 0:elem.getAttribute(name,"type"===name.toLowerCase()?1:2)}),support.attributes&&assert(function(div){return div.innerHTML="<input/>",div.firstChild.setAttribute("value",""),""===div.firstChild.getAttribute("value")})||addHandle("value",function(elem,name,isXML){return isXML||"input"!==elem.nodeName.toLowerCase()?void 0:elem.defaultValue}),assert(function(div){return null==div.getAttribute("disabled")})||addHandle(booleans,function(elem,name,isXML){var val;return isXML?void 0:(val=elem.getAttributeNode(name))&&val.specified?val.value:elem[name]===!0?name.toLowerCase():null}),jQuery.find=Sizzle,jQuery.expr=Sizzle.selectors,jQuery.expr[":"]=jQuery.expr.pseudos,jQuery.unique=Sizzle.uniqueSort,jQuery.text=Sizzle.getText,jQuery.isXMLDoc=Sizzle.isXML,jQuery.contains=Sizzle.contains}(window);var optionsCache={};jQuery.Callbacks=function(options){options="string"==typeof options?optionsCache[options]||createOptions(options):jQuery.extend({},options);var memory,fired,firing,firingStart,firingLength,firingIndex,list=[],stack=!options.once&&[],fire=function(data){for(memory=options.memory&&data,fired=!0,firingIndex=firingStart||0,firingStart=0,firingLength=list.length,firing=!0;list&&firingLength>firingIndex;firingIndex++)if(list[firingIndex].apply(data[0],data[1])===!1&&options.stopOnFalse){memory=!1;break}firing=!1,list&&(stack?stack.length&&fire(stack.shift()):memory?list=[]:self.disable())},self={add:function(){if(list){var start=list.length;!function add(args){jQuery.each(args,function(_,arg){var type=jQuery.type(arg);"function"===type?options.unique&&self.has(arg)||list.push(arg):arg&&arg.length&&"string"!==type&&add(arg)})}(arguments),firing?firingLength=list.length:memory&&(firingStart=start,fire(memory))}return this},remove:function(){return list&&jQuery.each(arguments,function(_,arg){for(var index;(index=jQuery.inArray(arg,list,index))>-1;)list.splice(index,1),firing&&(firingLength>=index&&firingLength--,firingIndex>=index&&firingIndex--)}),this},has:function(fn){return fn?jQuery.inArray(fn,list)>-1:!(!list||!list.length)},empty:function(){return list=[],firingLength=0,this},disable:function(){return list=stack=memory=undefined,this},disabled:function(){return!list},lock:function(){return stack=undefined,memory||self.disable(),this},locked:function(){return!stack},fireWith:function(context,args){return!list||fired&&!stack||(args=args||[],args=[context,args.slice?args.slice():args],firing?stack.push(args):fire(args)),this},fire:function(){return self.fireWith(this,arguments),this},fired:function(){return!!fired}};return self},jQuery.extend({Deferred:function(func){var tuples=[["resolve","done",jQuery.Callbacks("once memory"),"resolved"],["reject","fail",jQuery.Callbacks("once memory"),"rejected"],["notify","progress",jQuery.Callbacks("memory")]],state="pending",promise={state:function(){return state},always:function(){return deferred.done(arguments).fail(arguments),this},then:function(){var fns=arguments;return jQuery.Deferred(function(newDefer){jQuery.each(tuples,function(i,tuple){var action=tuple[0],fn=jQuery.isFunction(fns[i])&&fns[i];deferred[tuple[1]](function(){var returned=fn&&fn.apply(this,arguments);returned&&jQuery.isFunction(returned.promise)?returned.promise().done(newDefer.resolve).fail(newDefer.reject).progress(newDefer.notify):newDefer[action+"With"](this===promise?newDefer.promise():this,fn?[returned]:arguments)})}),fns=null}).promise()},promise:function(obj){return null!=obj?jQuery.extend(obj,promise):promise}},deferred={};return promise.pipe=promise.then,jQuery.each(tuples,function(i,tuple){var list=tuple[2],stateString=tuple[3];promise[tuple[1]]=list.add,stateString&&list.add(function(){state=stateString},tuples[1^i][2].disable,tuples[2][2].lock),deferred[tuple[0]]=function(){return deferred[tuple[0]+"With"](this===deferred?promise:this,arguments),this},deferred[tuple[0]+"With"]=list.fireWith}),promise.promise(deferred),func&&func.call(deferred,deferred),deferred},when:function(subordinate){var progressValues,progressContexts,resolveContexts,i=0,resolveValues=core_slice.call(arguments),length=resolveValues.length,remaining=1!==length||subordinate&&jQuery.isFunction(subordinate.promise)?length:0,deferred=1===remaining?subordinate:jQuery.Deferred(),updateFunc=function(i,contexts,values){return function(value){contexts[i]=this,values[i]=arguments.length>1?core_slice.call(arguments):value,values===progressValues?deferred.notifyWith(contexts,values):--remaining||deferred.resolveWith(contexts,values)}};if(length>1)for(progressValues=new Array(length),progressContexts=new Array(length),resolveContexts=new Array(length);length>i;i++)resolveValues[i]&&jQuery.isFunction(resolveValues[i].promise)?resolveValues[i].promise().done(updateFunc(i,resolveContexts,resolveValues)).fail(deferred.reject).progress(updateFunc(i,progressContexts,progressValues)):--remaining;return remaining||deferred.resolveWith(resolveContexts,resolveValues),deferred.promise()}}),jQuery.support=function(support){var input=document.createElement("input"),fragment=document.createDocumentFragment(),div=document.createElement("div"),select=document.createElement("select"),opt=select.appendChild(document.createElement("option"));return input.type?(input.type="checkbox",support.checkOn=""!==input.value,support.optSelected=opt.selected,support.reliableMarginRight=!0,support.boxSizingReliable=!0,support.pixelPosition=!1,input.checked=!0,support.noCloneChecked=input.cloneNode(!0).checked,select.disabled=!0,support.optDisabled=!opt.disabled,input=document.createElement("input"),input.value="t",input.type="radio",support.radioValue="t"===input.value,input.setAttribute("checked","t"),input.setAttribute("name","t"),fragment.appendChild(input),support.checkClone=fragment.cloneNode(!0).cloneNode(!0).lastChild.checked,support.focusinBubbles="onfocusin"in window,div.style.backgroundClip="content-box",div.cloneNode(!0).style.backgroundClip="",support.clearCloneStyle="content-box"===div.style.backgroundClip,jQuery(function(){var container,marginDiv,divReset="padding:0;margin:0;border:0;display:block;-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box",body=document.getElementsByTagName("body")[0];body&&(container=document.createElement("div"),container.style.cssText="border:0;width:0;height:0;position:absolute;top:0;left:-9999px;margin-top:1px",body.appendChild(container).appendChild(div),div.innerHTML="",div.style.cssText="-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;padding:1px;border:1px;display:block;width:4px;margin-top:1%;position:absolute;top:1%",jQuery.swap(body,null!=body.style.zoom?{zoom:1}:{},function(){support.boxSizing=4===div.offsetWidth}),window.getComputedStyle&&(support.pixelPosition="1%"!==(window.getComputedStyle(div,null)||{}).top,support.boxSizingReliable="4px"===(window.getComputedStyle(div,null)||{width:"4px"}).width,marginDiv=div.appendChild(document.createElement("div")),marginDiv.style.cssText=div.style.cssText=divReset,marginDiv.style.marginRight=marginDiv.style.width="0",div.style.width="1px",support.reliableMarginRight=!parseFloat((window.getComputedStyle(marginDiv,null)||{}).marginRight)),body.removeChild(container))}),support):support}({});var data_user,data_priv,rbrace=/(?:\{[\s\S]*\}|\[[\s\S]*\])$/,rmultiDash=/([A-Z])/g;Data.uid=1,Data.accepts=function(owner){return owner.nodeType?1===owner.nodeType||9===owner.nodeType:!0},Data.prototype={key:function(owner){if(!Data.accepts(owner))return 0;var descriptor={},unlock=owner[this.expando];if(!unlock){unlock=Data.uid++;try{descriptor[this.expando]={value:unlock},Object.defineProperties(owner,descriptor)}catch(e){descriptor[this.expando]=unlock,jQuery.extend(owner,descriptor)}}return this.cache[unlock]||(this.cache[unlock]={}),unlock},set:function(owner,data,value){var prop,unlock=this.key(owner),cache=this.cache[unlock];if("string"==typeof data)cache[data]=value;else if(jQuery.isEmptyObject(cache))jQuery.extend(this.cache[unlock],data);else for(prop in data)cache[prop]=data[prop];return cache},get:function(owner,key){var cache=this.cache[this.key(owner)];return key===undefined?cache:cache[key]},access:function(owner,key,value){var stored;return key===undefined||key&&"string"==typeof key&&value===undefined?(stored=this.get(owner,key),stored!==undefined?stored:this.get(owner,jQuery.camelCase(key))):(this.set(owner,key,value),value!==undefined?value:key)},remove:function(owner,key){var i,name,camel,unlock=this.key(owner),cache=this.cache[unlock];if(key===undefined)this.cache[unlock]={};else{jQuery.isArray(key)?name=key.concat(key.map(jQuery.camelCase)):(camel=jQuery.camelCase(key),key in cache?name=[key,camel]:(name=camel,name=name in cache?[name]:name.match(core_rnotwhite)||[])),i=name.length;for(;i--;)delete cache[name[i]]}},hasData:function(owner){return!jQuery.isEmptyObject(this.cache[owner[this.expando]]||{})},discard:function(owner){owner[this.expando]&&delete this.cache[owner[this.expando]]}},data_user=new Data,data_priv=new Data,jQuery.extend({acceptData:Data.accepts,hasData:function(elem){return data_user.hasData(elem)||data_priv.hasData(elem)},data:function(elem,name,data){return data_user.access(elem,name,data)},removeData:function(elem,name){data_user.remove(elem,name)},_data:function(elem,name,data){return data_priv.access(elem,name,data)},_removeData:function(elem,name){data_priv.remove(elem,name)}}),jQuery.fn.extend({data:function(key,value){var attrs,name,elem=this[0],i=0,data=null;if(key===undefined){if(this.length&&(data=data_user.get(elem),1===elem.nodeType&&!data_priv.get(elem,"hasDataAttrs"))){for(attrs=elem.attributes;i<attrs.length;i++)name=attrs[i].name,0===name.indexOf("data-")&&(name=jQuery.camelCase(name.slice(5)),dataAttr(elem,name,data[name]));data_priv.set(elem,"hasDataAttrs",!0)}return data}return"object"==typeof key?this.each(function(){data_user.set(this,key)}):jQuery.access(this,function(value){var data,camelKey=jQuery.camelCase(key);if(elem&&value===undefined){if(data=data_user.get(elem,key),data!==undefined)return data;if(data=data_user.get(elem,camelKey),data!==undefined)return data;if(data=dataAttr(elem,camelKey,undefined),data!==undefined)return data}else this.each(function(){var data=data_user.get(this,camelKey);data_user.set(this,camelKey,value),-1!==key.indexOf("-")&&data!==undefined&&data_user.set(this,key,value)})},null,value,arguments.length>1,null,!0)},removeData:function(key){return this.each(function(){data_user.remove(this,key)})}}),jQuery.extend({queue:function(elem,type,data){var queue;return elem?(type=(type||"fx")+"queue",queue=data_priv.get(elem,type),data&&(!queue||jQuery.isArray(data)?queue=data_priv.access(elem,type,jQuery.makeArray(data)):queue.push(data)),queue||[]):void 0},dequeue:function(elem,type){type=type||"fx";var queue=jQuery.queue(elem,type),startLength=queue.length,fn=queue.shift(),hooks=jQuery._queueHooks(elem,type),next=function(){jQuery.dequeue(elem,type)};"inprogress"===fn&&(fn=queue.shift(),startLength--),fn&&("fx"===type&&queue.unshift("inprogress"),delete hooks.stop,fn.call(elem,next,hooks)),!startLength&&hooks&&hooks.empty.fire()},_queueHooks:function(elem,type){var key=type+"queueHooks";return data_priv.get(elem,key)||data_priv.access(elem,key,{empty:jQuery.Callbacks("once memory").add(function(){data_priv.remove(elem,[type+"queue",key])})})}}),jQuery.fn.extend({queue:function(type,data){var setter=2;return"string"!=typeof type&&(data=type,type="fx",setter--),arguments.length<setter?jQuery.queue(this[0],type):data===undefined?this:this.each(function(){var queue=jQuery.queue(this,type,data);jQuery._queueHooks(this,type),"fx"===type&&"inprogress"!==queue[0]&&jQuery.dequeue(this,type)})},dequeue:function(type){return this.each(function(){jQuery.dequeue(this,type)})},delay:function(time,type){return time=jQuery.fx?jQuery.fx.speeds[time]||time:time,type=type||"fx",this.queue(type,function(next,hooks){var timeout=setTimeout(next,time);hooks.stop=function(){clearTimeout(timeout)}})},clearQueue:function(type){return this.queue(type||"fx",[])},promise:function(type,obj){var tmp,count=1,defer=jQuery.Deferred(),elements=this,i=this.length,resolve=function(){--count||defer.resolveWith(elements,[elements])};for("string"!=typeof type&&(obj=type,type=undefined),type=type||"fx";i--;)tmp=data_priv.get(elements[i],type+"queueHooks"),tmp&&tmp.empty&&(count++,tmp.empty.add(resolve));return resolve(),defer.promise(obj)}});var nodeHook,boolHook,rclass=/[\t\r\n\f]/g,rreturn=/\r/g,rfocusable=/^(?:input|select|textarea|button)$/i;jQuery.fn.extend({attr:function(name,value){return jQuery.access(this,jQuery.attr,name,value,arguments.length>1)},removeAttr:function(name){return this.each(function(){jQuery.removeAttr(this,name)})},prop:function(name,value){return jQuery.access(this,jQuery.prop,name,value,arguments.length>1)},removeProp:function(name){return this.each(function(){delete this[jQuery.propFix[name]||name]})},addClass:function(value){var classes,elem,cur,clazz,j,i=0,len=this.length,proceed="string"==typeof value&&value;if(jQuery.isFunction(value))return this.each(function(j){jQuery(this).addClass(value.call(this,j,this.className))});if(proceed)for(classes=(value||"").match(core_rnotwhite)||[];len>i;i++)if(elem=this[i],cur=1===elem.nodeType&&(elem.className?(" "+elem.className+" ").replace(rclass," "):" ")){for(j=0;clazz=classes[j++];)cur.indexOf(" "+clazz+" ")<0&&(cur+=clazz+" ");elem.className=jQuery.trim(cur)}return this},removeClass:function(value){var classes,elem,cur,clazz,j,i=0,len=this.length,proceed=0===arguments.length||"string"==typeof value&&value;if(jQuery.isFunction(value))return this.each(function(j){jQuery(this).removeClass(value.call(this,j,this.className))});if(proceed)for(classes=(value||"").match(core_rnotwhite)||[];len>i;i++)if(elem=this[i],cur=1===elem.nodeType&&(elem.className?(" "+elem.className+" ").replace(rclass," "):"")){for(j=0;clazz=classes[j++];)for(;cur.indexOf(" "+clazz+" ")>=0;)cur=cur.replace(" "+clazz+" "," ");elem.className=value?jQuery.trim(cur):""}return this},toggleClass:function(value,stateVal){var type=typeof value;return"boolean"==typeof stateVal&&"string"===type?stateVal?this.addClass(value):this.removeClass(value):this.each(jQuery.isFunction(value)?function(i){jQuery(this).toggleClass(value.call(this,i,this.className,stateVal),stateVal)}:function(){if("string"===type)for(var className,i=0,self=jQuery(this),classNames=value.match(core_rnotwhite)||[];className=classNames[i++];)self.hasClass(className)?self.removeClass(className):self.addClass(className);else(type===core_strundefined||"boolean"===type)&&(this.className&&data_priv.set(this,"__className__",this.className),this.className=this.className||value===!1?"":data_priv.get(this,"__className__")||"")})},hasClass:function(selector){for(var className=" "+selector+" ",i=0,l=this.length;l>i;i++)if(1===this[i].nodeType&&(" "+this[i].className+" ").replace(rclass," ").indexOf(className)>=0)return!0;return!1},val:function(value){var hooks,ret,isFunction,elem=this[0];{if(arguments.length)return isFunction=jQuery.isFunction(value),this.each(function(i){var val;1===this.nodeType&&(val=isFunction?value.call(this,i,jQuery(this).val()):value,null==val?val="":"number"==typeof val?val+="":jQuery.isArray(val)&&(val=jQuery.map(val,function(value){return null==value?"":value+""})),hooks=jQuery.valHooks[this.type]||jQuery.valHooks[this.nodeName.toLowerCase()],hooks&&"set"in hooks&&hooks.set(this,val,"value")!==undefined||(this.value=val))});if(elem)return hooks=jQuery.valHooks[elem.type]||jQuery.valHooks[elem.nodeName.toLowerCase()],hooks&&"get"in hooks&&(ret=hooks.get(elem,"value"))!==undefined?ret:(ret=elem.value,"string"==typeof ret?ret.replace(rreturn,""):null==ret?"":ret)}}}),jQuery.extend({valHooks:{option:{get:function(elem){var val=elem.attributes.value;return!val||val.specified?elem.value:elem.text}},select:{get:function(elem){for(var value,option,options=elem.options,index=elem.selectedIndex,one="select-one"===elem.type||0>index,values=one?null:[],max=one?index+1:options.length,i=0>index?max:one?index:0;max>i;i++)if(option=options[i],!(!option.selected&&i!==index||(jQuery.support.optDisabled?option.disabled:null!==option.getAttribute("disabled"))||option.parentNode.disabled&&jQuery.nodeName(option.parentNode,"optgroup"))){if(value=jQuery(option).val(),one)return value;values.push(value)}return values},set:function(elem,value){for(var optionSet,option,options=elem.options,values=jQuery.makeArray(value),i=options.length;i--;)option=options[i],(option.selected=jQuery.inArray(jQuery(option).val(),values)>=0)&&(optionSet=!0);return optionSet||(elem.selectedIndex=-1),values}}},attr:function(elem,name,value){var hooks,ret,nType=elem.nodeType;if(elem&&3!==nType&&8!==nType&&2!==nType)return typeof elem.getAttribute===core_strundefined?jQuery.prop(elem,name,value):(1===nType&&jQuery.isXMLDoc(elem)||(name=name.toLowerCase(),hooks=jQuery.attrHooks[name]||(jQuery.expr.match.bool.test(name)?boolHook:nodeHook)),value===undefined?hooks&&"get"in hooks&&null!==(ret=hooks.get(elem,name))?ret:(ret=jQuery.find.attr(elem,name),null==ret?undefined:ret):null!==value?hooks&&"set"in hooks&&(ret=hooks.set(elem,value,name))!==undefined?ret:(elem.setAttribute(name,value+""),value):void jQuery.removeAttr(elem,name))},removeAttr:function(elem,value){var name,propName,i=0,attrNames=value&&value.match(core_rnotwhite);if(attrNames&&1===elem.nodeType)for(;name=attrNames[i++];)propName=jQuery.propFix[name]||name,jQuery.expr.match.bool.test(name)&&(elem[propName]=!1),elem.removeAttribute(name)},attrHooks:{type:{set:function(elem,value){if(!jQuery.support.radioValue&&"radio"===value&&jQuery.nodeName(elem,"input")){var val=elem.value;return elem.setAttribute("type",value),val&&(elem.value=val),value}}}},propFix:{"for":"htmlFor","class":"className"},prop:function(elem,name,value){var ret,hooks,notxml,nType=elem.nodeType;if(elem&&3!==nType&&8!==nType&&2!==nType)return notxml=1!==nType||!jQuery.isXMLDoc(elem),notxml&&(name=jQuery.propFix[name]||name,hooks=jQuery.propHooks[name]),value!==undefined?hooks&&"set"in hooks&&(ret=hooks.set(elem,value,name))!==undefined?ret:elem[name]=value:hooks&&"get"in hooks&&null!==(ret=hooks.get(elem,name))?ret:elem[name]},propHooks:{tabIndex:{get:function(elem){return elem.hasAttribute("tabindex")||rfocusable.test(elem.nodeName)||elem.href?elem.tabIndex:-1}}}}),boolHook={set:function(elem,value,name){return value===!1?jQuery.removeAttr(elem,name):elem.setAttribute(name,name),name}},jQuery.each(jQuery.expr.match.bool.source.match(/\w+/g),function(i,name){var getter=jQuery.expr.attrHandle[name]||jQuery.find.attr;jQuery.expr.attrHandle[name]=function(elem,name,isXML){var fn=jQuery.expr.attrHandle[name],ret=isXML?undefined:(jQuery.expr.attrHandle[name]=undefined)!=getter(elem,name,isXML)?name.toLowerCase():null;return jQuery.expr.attrHandle[name]=fn,ret}}),jQuery.support.optSelected||(jQuery.propHooks.selected={get:function(elem){var parent=elem.parentNode;return parent&&parent.parentNode&&parent.parentNode.selectedIndex,null}}),jQuery.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){jQuery.propFix[this.toLowerCase()]=this}),jQuery.each(["radio","checkbox"],function(){jQuery.valHooks[this]={set:function(elem,value){return jQuery.isArray(value)?elem.checked=jQuery.inArray(jQuery(elem).val(),value)>=0:void 0}},jQuery.support.checkOn||(jQuery.valHooks[this].get=function(elem){return null===elem.getAttribute("value")?"on":elem.value})});var rkeyEvent=/^key/,rmouseEvent=/^(?:mouse|contextmenu)|click/,rfocusMorph=/^(?:focusinfocus|focusoutblur)$/,rtypenamespace=/^([^.]*)(?:\.(.+)|)$/;jQuery.event={global:{},add:function(elem,types,handler,data,selector){var handleObjIn,eventHandle,tmp,events,t,handleObj,special,handlers,type,namespaces,origType,elemData=data_priv.get(elem);if(elemData){for(handler.handler&&(handleObjIn=handler,handler=handleObjIn.handler,selector=handleObjIn.selector),handler.guid||(handler.guid=jQuery.guid++),(events=elemData.events)||(events=elemData.events={}),(eventHandle=elemData.handle)||(eventHandle=elemData.handle=function(e){return typeof jQuery===core_strundefined||e&&jQuery.event.triggered===e.type?undefined:jQuery.event.dispatch.apply(eventHandle.elem,arguments)},eventHandle.elem=elem),types=(types||"").match(core_rnotwhite)||[""],t=types.length;t--;)tmp=rtypenamespace.exec(types[t])||[],type=origType=tmp[1],namespaces=(tmp[2]||"").split(".").sort(),type&&(special=jQuery.event.special[type]||{},type=(selector?special.delegateType:special.bindType)||type,special=jQuery.event.special[type]||{},handleObj=jQuery.extend({type:type,origType:origType,data:data,handler:handler,guid:handler.guid,selector:selector,needsContext:selector&&jQuery.expr.match.needsContext.test(selector),namespace:namespaces.join(".")},handleObjIn),(handlers=events[type])||(handlers=events[type]=[],handlers.delegateCount=0,special.setup&&special.setup.call(elem,data,namespaces,eventHandle)!==!1||elem.addEventListener&&elem.addEventListener(type,eventHandle,!1)),special.add&&(special.add.call(elem,handleObj),handleObj.handler.guid||(handleObj.handler.guid=handler.guid)),selector?handlers.splice(handlers.delegateCount++,0,handleObj):handlers.push(handleObj),jQuery.event.global[type]=!0);elem=null}},remove:function(elem,types,handler,selector,mappedTypes){var j,origCount,tmp,events,t,handleObj,special,handlers,type,namespaces,origType,elemData=data_priv.hasData(elem)&&data_priv.get(elem);if(elemData&&(events=elemData.events)){for(types=(types||"").match(core_rnotwhite)||[""],t=types.length;t--;)if(tmp=rtypenamespace.exec(types[t])||[],type=origType=tmp[1],namespaces=(tmp[2]||"").split(".").sort(),type){for(special=jQuery.event.special[type]||{},type=(selector?special.delegateType:special.bindType)||type,handlers=events[type]||[],tmp=tmp[2]&&new RegExp("(^|\\.)"+namespaces.join("\\.(?:.*\\.|)")+"(\\.|$)"),origCount=j=handlers.length;j--;)handleObj=handlers[j],!mappedTypes&&origType!==handleObj.origType||handler&&handler.guid!==handleObj.guid||tmp&&!tmp.test(handleObj.namespace)||selector&&selector!==handleObj.selector&&("**"!==selector||!handleObj.selector)||(handlers.splice(j,1),handleObj.selector&&handlers.delegateCount--,special.remove&&special.remove.call(elem,handleObj));origCount&&!handlers.length&&(special.teardown&&special.teardown.call(elem,namespaces,elemData.handle)!==!1||jQuery.removeEvent(elem,type,elemData.handle),delete events[type])}else for(type in events)jQuery.event.remove(elem,type+types[t],handler,selector,!0);jQuery.isEmptyObject(events)&&(delete elemData.handle,data_priv.remove(elem,"events"))}},trigger:function(event,data,elem,onlyHandlers){var i,cur,tmp,bubbleType,ontype,handle,special,eventPath=[elem||document],type=core_hasOwn.call(event,"type")?event.type:event,namespaces=core_hasOwn.call(event,"namespace")?event.namespace.split("."):[];if(cur=tmp=elem=elem||document,3!==elem.nodeType&&8!==elem.nodeType&&!rfocusMorph.test(type+jQuery.event.triggered)&&(type.indexOf(".")>=0&&(namespaces=type.split("."),type=namespaces.shift(),namespaces.sort()),ontype=type.indexOf(":")<0&&"on"+type,event=event[jQuery.expando]?event:new jQuery.Event(type,"object"==typeof event&&event),event.isTrigger=onlyHandlers?2:3,event.namespace=namespaces.join("."),event.namespace_re=event.namespace?new RegExp("(^|\\.)"+namespaces.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,event.result=undefined,event.target||(event.target=elem),data=null==data?[event]:jQuery.makeArray(data,[event]),special=jQuery.event.special[type]||{},onlyHandlers||!special.trigger||special.trigger.apply(elem,data)!==!1)){if(!onlyHandlers&&!special.noBubble&&!jQuery.isWindow(elem)){for(bubbleType=special.delegateType||type,rfocusMorph.test(bubbleType+type)||(cur=cur.parentNode);cur;cur=cur.parentNode)eventPath.push(cur),tmp=cur;tmp===(elem.ownerDocument||document)&&eventPath.push(tmp.defaultView||tmp.parentWindow||window)}for(i=0;(cur=eventPath[i++])&&!event.isPropagationStopped();)event.type=i>1?bubbleType:special.bindType||type,handle=(data_priv.get(cur,"events")||{})[event.type]&&data_priv.get(cur,"handle"),handle&&handle.apply(cur,data),handle=ontype&&cur[ontype],handle&&jQuery.acceptData(cur)&&handle.apply&&handle.apply(cur,data)===!1&&event.preventDefault();return event.type=type,onlyHandlers||event.isDefaultPrevented()||special._default&&special._default.apply(eventPath.pop(),data)!==!1||!jQuery.acceptData(elem)||ontype&&jQuery.isFunction(elem[type])&&!jQuery.isWindow(elem)&&(tmp=elem[ontype],tmp&&(elem[ontype]=null),jQuery.event.triggered=type,elem[type](),jQuery.event.triggered=undefined,tmp&&(elem[ontype]=tmp)),event.result}},dispatch:function(event){event=jQuery.event.fix(event);var i,j,ret,matched,handleObj,handlerQueue=[],args=core_slice.call(arguments),handlers=(data_priv.get(this,"events")||{})[event.type]||[],special=jQuery.event.special[event.type]||{};if(args[0]=event,event.delegateTarget=this,!special.preDispatch||special.preDispatch.call(this,event)!==!1){for(handlerQueue=jQuery.event.handlers.call(this,event,handlers),i=0;(matched=handlerQueue[i++])&&!event.isPropagationStopped();)for(event.currentTarget=matched.elem,j=0;(handleObj=matched.handlers[j++])&&!event.isImmediatePropagationStopped();)(!event.namespace_re||event.namespace_re.test(handleObj.namespace))&&(event.handleObj=handleObj,event.data=handleObj.data,ret=((jQuery.event.special[handleObj.origType]||{}).handle||handleObj.handler).apply(matched.elem,args),ret!==undefined&&(event.result=ret)===!1&&(event.preventDefault(),event.stopPropagation()));return special.postDispatch&&special.postDispatch.call(this,event),event.result}},handlers:function(event,handlers){var i,matches,sel,handleObj,handlerQueue=[],delegateCount=handlers.delegateCount,cur=event.target;if(delegateCount&&cur.nodeType&&(!event.button||"click"!==event.type))for(;cur!==this;cur=cur.parentNode||this)if(cur.disabled!==!0||"click"!==event.type){for(matches=[],i=0;delegateCount>i;i++)handleObj=handlers[i],sel=handleObj.selector+" ",matches[sel]===undefined&&(matches[sel]=handleObj.needsContext?jQuery(sel,this).index(cur)>=0:jQuery.find(sel,this,null,[cur]).length),matches[sel]&&matches.push(handleObj);matches.length&&handlerQueue.push({elem:cur,handlers:matches})}return delegateCount<handlers.length&&handlerQueue.push({elem:this,handlers:handlers.slice(delegateCount)}),handlerQueue},props:"altKey bubbles cancelable ctrlKey currentTarget eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(event,original){return null==event.which&&(event.which=null!=original.charCode?original.charCode:original.keyCode),event}},mouseHooks:{props:"button buttons clientX clientY offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(event,original){var eventDoc,doc,body,button=original.button;return null==event.pageX&&null!=original.clientX&&(eventDoc=event.target.ownerDocument||document,doc=eventDoc.documentElement,body=eventDoc.body,event.pageX=original.clientX+(doc&&doc.scrollLeft||body&&body.scrollLeft||0)-(doc&&doc.clientLeft||body&&body.clientLeft||0),event.pageY=original.clientY+(doc&&doc.scrollTop||body&&body.scrollTop||0)-(doc&&doc.clientTop||body&&body.clientTop||0)),event.which||button===undefined||(event.which=1&button?1:2&button?3:4&button?2:0),event}},fix:function(event){if(event[jQuery.expando])return event;var i,prop,copy,type=event.type,originalEvent=event,fixHook=this.fixHooks[type];for(fixHook||(this.fixHooks[type]=fixHook=rmouseEvent.test(type)?this.mouseHooks:rkeyEvent.test(type)?this.keyHooks:{}),copy=fixHook.props?this.props.concat(fixHook.props):this.props,event=new jQuery.Event(originalEvent),i=copy.length;i--;)prop=copy[i],event[prop]=originalEvent[prop];return event.target||(event.target=document),3===event.target.nodeType&&(event.target=event.target.parentNode),fixHook.filter?fixHook.filter(event,originalEvent):event},special:{load:{noBubble:!0},focus:{trigger:function(){return this!==safeActiveElement()&&this.focus?(this.focus(),!1):void 0},delegateType:"focusin"},blur:{trigger:function(){return this===safeActiveElement()&&this.blur?(this.blur(),!1):void 0},delegateType:"focusout"},click:{trigger:function(){return"checkbox"===this.type&&this.click&&jQuery.nodeName(this,"input")?(this.click(),!1):void 0},_default:function(event){return jQuery.nodeName(event.target,"a")}},beforeunload:{postDispatch:function(event){event.result!==undefined&&(event.originalEvent.returnValue=event.result)}}},simulate:function(type,elem,event,bubble){var e=jQuery.extend(new jQuery.Event,event,{type:type,isSimulated:!0,originalEvent:{}});bubble?jQuery.event.trigger(e,null,elem):jQuery.event.dispatch.call(elem,e),e.isDefaultPrevented()&&event.preventDefault()
}},jQuery.removeEvent=function(elem,type,handle){elem.removeEventListener&&elem.removeEventListener(type,handle,!1)},jQuery.Event=function(src,props){return this instanceof jQuery.Event?(src&&src.type?(this.originalEvent=src,this.type=src.type,this.isDefaultPrevented=src.defaultPrevented||src.getPreventDefault&&src.getPreventDefault()?returnTrue:returnFalse):this.type=src,props&&jQuery.extend(this,props),this.timeStamp=src&&src.timeStamp||jQuery.now(),void(this[jQuery.expando]=!0)):new jQuery.Event(src,props)},jQuery.Event.prototype={isDefaultPrevented:returnFalse,isPropagationStopped:returnFalse,isImmediatePropagationStopped:returnFalse,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=returnTrue,e&&e.preventDefault&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=returnTrue,e&&e.stopPropagation&&e.stopPropagation()},stopImmediatePropagation:function(){this.isImmediatePropagationStopped=returnTrue,this.stopPropagation()}},jQuery.each({mouseenter:"mouseover",mouseleave:"mouseout"},function(orig,fix){jQuery.event.special[orig]={delegateType:fix,bindType:fix,handle:function(event){var ret,target=this,related=event.relatedTarget,handleObj=event.handleObj;return(!related||related!==target&&!jQuery.contains(target,related))&&(event.type=handleObj.origType,ret=handleObj.handler.apply(this,arguments),event.type=fix),ret}}}),jQuery.support.focusinBubbles||jQuery.each({focus:"focusin",blur:"focusout"},function(orig,fix){var attaches=0,handler=function(event){jQuery.event.simulate(fix,event.target,jQuery.event.fix(event),!0)};jQuery.event.special[fix]={setup:function(){0===attaches++&&document.addEventListener(orig,handler,!0)},teardown:function(){0===--attaches&&document.removeEventListener(orig,handler,!0)}}}),jQuery.fn.extend({on:function(types,selector,data,fn,one){var origFn,type;if("object"==typeof types){"string"!=typeof selector&&(data=data||selector,selector=undefined);for(type in types)this.on(type,selector,data,types[type],one);return this}if(null==data&&null==fn?(fn=selector,data=selector=undefined):null==fn&&("string"==typeof selector?(fn=data,data=undefined):(fn=data,data=selector,selector=undefined)),fn===!1)fn=returnFalse;else if(!fn)return this;return 1===one&&(origFn=fn,fn=function(event){return jQuery().off(event),origFn.apply(this,arguments)},fn.guid=origFn.guid||(origFn.guid=jQuery.guid++)),this.each(function(){jQuery.event.add(this,types,fn,data,selector)})},one:function(types,selector,data,fn){return this.on(types,selector,data,fn,1)},off:function(types,selector,fn){var handleObj,type;if(types&&types.preventDefault&&types.handleObj)return handleObj=types.handleObj,jQuery(types.delegateTarget).off(handleObj.namespace?handleObj.origType+"."+handleObj.namespace:handleObj.origType,handleObj.selector,handleObj.handler),this;if("object"==typeof types){for(type in types)this.off(type,selector,types[type]);return this}return(selector===!1||"function"==typeof selector)&&(fn=selector,selector=undefined),fn===!1&&(fn=returnFalse),this.each(function(){jQuery.event.remove(this,types,fn,selector)})},trigger:function(type,data){return this.each(function(){jQuery.event.trigger(type,data,this)})},triggerHandler:function(type,data){var elem=this[0];return elem?jQuery.event.trigger(type,data,elem,!0):void 0}});var isSimple=/^.[^:#\[\.,]*$/,rparentsprev=/^(?:parents|prev(?:Until|All))/,rneedsContext=jQuery.expr.match.needsContext,guaranteedUnique={children:!0,contents:!0,next:!0,prev:!0};jQuery.fn.extend({find:function(selector){var i,ret=[],self=this,len=self.length;if("string"!=typeof selector)return this.pushStack(jQuery(selector).filter(function(){for(i=0;len>i;i++)if(jQuery.contains(self[i],this))return!0}));for(i=0;len>i;i++)jQuery.find(selector,self[i],ret);return ret=this.pushStack(len>1?jQuery.unique(ret):ret),ret.selector=this.selector?this.selector+" "+selector:selector,ret},has:function(target){var targets=jQuery(target,this),l=targets.length;return this.filter(function(){for(var i=0;l>i;i++)if(jQuery.contains(this,targets[i]))return!0})},not:function(selector){return this.pushStack(winnow(this,selector||[],!0))},filter:function(selector){return this.pushStack(winnow(this,selector||[],!1))},is:function(selector){return!!winnow(this,"string"==typeof selector&&rneedsContext.test(selector)?jQuery(selector):selector||[],!1).length},closest:function(selectors,context){for(var cur,i=0,l=this.length,matched=[],pos=rneedsContext.test(selectors)||"string"!=typeof selectors?jQuery(selectors,context||this.context):0;l>i;i++)for(cur=this[i];cur&&cur!==context;cur=cur.parentNode)if(cur.nodeType<11&&(pos?pos.index(cur)>-1:1===cur.nodeType&&jQuery.find.matchesSelector(cur,selectors))){cur=matched.push(cur);break}return this.pushStack(matched.length>1?jQuery.unique(matched):matched)},index:function(elem){return elem?"string"==typeof elem?core_indexOf.call(jQuery(elem),this[0]):core_indexOf.call(this,elem.jquery?elem[0]:elem):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(selector,context){var set="string"==typeof selector?jQuery(selector,context):jQuery.makeArray(selector&&selector.nodeType?[selector]:selector),all=jQuery.merge(this.get(),set);return this.pushStack(jQuery.unique(all))},addBack:function(selector){return this.add(null==selector?this.prevObject:this.prevObject.filter(selector))}}),jQuery.each({parent:function(elem){var parent=elem.parentNode;return parent&&11!==parent.nodeType?parent:null},parents:function(elem){return jQuery.dir(elem,"parentNode")},parentsUntil:function(elem,i,until){return jQuery.dir(elem,"parentNode",until)},next:function(elem){return sibling(elem,"nextSibling")},prev:function(elem){return sibling(elem,"previousSibling")},nextAll:function(elem){return jQuery.dir(elem,"nextSibling")},prevAll:function(elem){return jQuery.dir(elem,"previousSibling")},nextUntil:function(elem,i,until){return jQuery.dir(elem,"nextSibling",until)},prevUntil:function(elem,i,until){return jQuery.dir(elem,"previousSibling",until)},siblings:function(elem){return jQuery.sibling((elem.parentNode||{}).firstChild,elem)},children:function(elem){return jQuery.sibling(elem.firstChild)},contents:function(elem){return elem.contentDocument||jQuery.merge([],elem.childNodes)}},function(name,fn){jQuery.fn[name]=function(until,selector){var matched=jQuery.map(this,fn,until);return"Until"!==name.slice(-5)&&(selector=until),selector&&"string"==typeof selector&&(matched=jQuery.filter(selector,matched)),this.length>1&&(guaranteedUnique[name]||jQuery.unique(matched),rparentsprev.test(name)&&matched.reverse()),this.pushStack(matched)}}),jQuery.extend({filter:function(expr,elems,not){var elem=elems[0];return not&&(expr=":not("+expr+")"),1===elems.length&&1===elem.nodeType?jQuery.find.matchesSelector(elem,expr)?[elem]:[]:jQuery.find.matches(expr,jQuery.grep(elems,function(elem){return 1===elem.nodeType}))},dir:function(elem,dir,until){for(var matched=[],truncate=until!==undefined;(elem=elem[dir])&&9!==elem.nodeType;)if(1===elem.nodeType){if(truncate&&jQuery(elem).is(until))break;matched.push(elem)}return matched},sibling:function(n,elem){for(var matched=[];n;n=n.nextSibling)1===n.nodeType&&n!==elem&&matched.push(n);return matched}});var rxhtmlTag=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:]+)[^>]*)\/>/gi,rtagName=/<([\w:]+)/,rhtml=/<|&#?\w+;/,rnoInnerhtml=/<(?:script|style|link)/i,manipulation_rcheckableType=/^(?:checkbox|radio)$/i,rchecked=/checked\s*(?:[^=]|=\s*.checked.)/i,rscriptType=/^$|\/(?:java|ecma)script/i,rscriptTypeMasked=/^true\/(.*)/,rcleanScript=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g,wrapMap={option:[1,"<select multiple='multiple'>","</select>"],thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};wrapMap.optgroup=wrapMap.option,wrapMap.tbody=wrapMap.tfoot=wrapMap.colgroup=wrapMap.caption=wrapMap.thead,wrapMap.th=wrapMap.td,jQuery.fn.extend({text:function(value){return jQuery.access(this,function(value){return value===undefined?jQuery.text(this):this.empty().append((this[0]&&this[0].ownerDocument||document).createTextNode(value))},null,value,arguments.length)},append:function(){return this.domManip(arguments,function(elem){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var target=manipulationTarget(this,elem);target.appendChild(elem)}})},prepend:function(){return this.domManip(arguments,function(elem){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var target=manipulationTarget(this,elem);target.insertBefore(elem,target.firstChild)}})},before:function(){return this.domManip(arguments,function(elem){this.parentNode&&this.parentNode.insertBefore(elem,this)})},after:function(){return this.domManip(arguments,function(elem){this.parentNode&&this.parentNode.insertBefore(elem,this.nextSibling)})},remove:function(selector,keepData){for(var elem,elems=selector?jQuery.filter(selector,this):this,i=0;null!=(elem=elems[i]);i++)keepData||1!==elem.nodeType||jQuery.cleanData(getAll(elem)),elem.parentNode&&(keepData&&jQuery.contains(elem.ownerDocument,elem)&&setGlobalEval(getAll(elem,"script")),elem.parentNode.removeChild(elem));return this},empty:function(){for(var elem,i=0;null!=(elem=this[i]);i++)1===elem.nodeType&&(jQuery.cleanData(getAll(elem,!1)),elem.textContent="");return this},clone:function(dataAndEvents,deepDataAndEvents){return dataAndEvents=null==dataAndEvents?!1:dataAndEvents,deepDataAndEvents=null==deepDataAndEvents?dataAndEvents:deepDataAndEvents,this.map(function(){return jQuery.clone(this,dataAndEvents,deepDataAndEvents)})},html:function(value){return jQuery.access(this,function(value){var elem=this[0]||{},i=0,l=this.length;if(value===undefined&&1===elem.nodeType)return elem.innerHTML;if("string"==typeof value&&!rnoInnerhtml.test(value)&&!wrapMap[(rtagName.exec(value)||["",""])[1].toLowerCase()]){value=value.replace(rxhtmlTag,"<$1></$2>");try{for(;l>i;i++)elem=this[i]||{},1===elem.nodeType&&(jQuery.cleanData(getAll(elem,!1)),elem.innerHTML=value);elem=0}catch(e){}}elem&&this.empty().append(value)},null,value,arguments.length)},replaceWith:function(){var args=jQuery.map(this,function(elem){return[elem.nextSibling,elem.parentNode]}),i=0;return this.domManip(arguments,function(elem){var next=args[i++],parent=args[i++];parent&&(next&&next.parentNode!==parent&&(next=this.nextSibling),jQuery(this).remove(),parent.insertBefore(elem,next))},!0),i?this:this.remove()},detach:function(selector){return this.remove(selector,!0)},domManip:function(args,callback,allowIntersection){args=core_concat.apply([],args);var fragment,first,scripts,hasScripts,node,doc,i=0,l=this.length,set=this,iNoClone=l-1,value=args[0],isFunction=jQuery.isFunction(value);if(isFunction||!(1>=l||"string"!=typeof value||jQuery.support.checkClone)&&rchecked.test(value))return this.each(function(index){var self=set.eq(index);isFunction&&(args[0]=value.call(this,index,self.html())),self.domManip(args,callback,allowIntersection)});if(l&&(fragment=jQuery.buildFragment(args,this[0].ownerDocument,!1,!allowIntersection&&this),first=fragment.firstChild,1===fragment.childNodes.length&&(fragment=first),first)){for(scripts=jQuery.map(getAll(fragment,"script"),disableScript),hasScripts=scripts.length;l>i;i++)node=fragment,i!==iNoClone&&(node=jQuery.clone(node,!0,!0),hasScripts&&jQuery.merge(scripts,getAll(node,"script"))),callback.call(this[i],node,i);if(hasScripts)for(doc=scripts[scripts.length-1].ownerDocument,jQuery.map(scripts,restoreScript),i=0;hasScripts>i;i++)node=scripts[i],rscriptType.test(node.type||"")&&!data_priv.access(node,"globalEval")&&jQuery.contains(doc,node)&&(node.src?jQuery._evalUrl(node.src):jQuery.globalEval(node.textContent.replace(rcleanScript,"")))}return this}}),jQuery.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(name,original){jQuery.fn[name]=function(selector){for(var elems,ret=[],insert=jQuery(selector),last=insert.length-1,i=0;last>=i;i++)elems=i===last?this:this.clone(!0),jQuery(insert[i])[original](elems),core_push.apply(ret,elems.get());return this.pushStack(ret)}}),jQuery.extend({clone:function(elem,dataAndEvents,deepDataAndEvents){var i,l,srcElements,destElements,clone=elem.cloneNode(!0),inPage=jQuery.contains(elem.ownerDocument,elem);if(!(jQuery.support.noCloneChecked||1!==elem.nodeType&&11!==elem.nodeType||jQuery.isXMLDoc(elem)))for(destElements=getAll(clone),srcElements=getAll(elem),i=0,l=srcElements.length;l>i;i++)fixInput(srcElements[i],destElements[i]);if(dataAndEvents)if(deepDataAndEvents)for(srcElements=srcElements||getAll(elem),destElements=destElements||getAll(clone),i=0,l=srcElements.length;l>i;i++)cloneCopyEvent(srcElements[i],destElements[i]);else cloneCopyEvent(elem,clone);return destElements=getAll(clone,"script"),destElements.length>0&&setGlobalEval(destElements,!inPage&&getAll(elem,"script")),clone},buildFragment:function(elems,context,scripts,selection){for(var elem,tmp,tag,wrap,contains,j,i=0,l=elems.length,fragment=context.createDocumentFragment(),nodes=[];l>i;i++)if(elem=elems[i],elem||0===elem)if("object"===jQuery.type(elem))jQuery.merge(nodes,elem.nodeType?[elem]:elem);else if(rhtml.test(elem)){for(tmp=tmp||fragment.appendChild(context.createElement("div")),tag=(rtagName.exec(elem)||["",""])[1].toLowerCase(),wrap=wrapMap[tag]||wrapMap._default,tmp.innerHTML=wrap[1]+elem.replace(rxhtmlTag,"<$1></$2>")+wrap[2],j=wrap[0];j--;)tmp=tmp.lastChild;jQuery.merge(nodes,tmp.childNodes),tmp=fragment.firstChild,tmp.textContent=""}else nodes.push(context.createTextNode(elem));for(fragment.textContent="",i=0;elem=nodes[i++];)if((!selection||-1===jQuery.inArray(elem,selection))&&(contains=jQuery.contains(elem.ownerDocument,elem),tmp=getAll(fragment.appendChild(elem),"script"),contains&&setGlobalEval(tmp),scripts))for(j=0;elem=tmp[j++];)rscriptType.test(elem.type||"")&&scripts.push(elem);return fragment},cleanData:function(elems){for(var data,elem,events,type,key,j,special=jQuery.event.special,i=0;(elem=elems[i])!==undefined;i++){if(Data.accepts(elem)&&(key=elem[data_priv.expando],key&&(data=data_priv.cache[key]))){if(events=Object.keys(data.events||{}),events.length)for(j=0;(type=events[j])!==undefined;j++)special[type]?jQuery.event.remove(elem,type):jQuery.removeEvent(elem,type,data.handle);data_priv.cache[key]&&delete data_priv.cache[key]}delete data_user.cache[elem[data_user.expando]]}},_evalUrl:function(url){return jQuery.ajax({url:url,type:"GET",dataType:"script",async:!1,global:!1,"throws":!0})}}),jQuery.fn.extend({wrapAll:function(html){var wrap;return jQuery.isFunction(html)?this.each(function(i){jQuery(this).wrapAll(html.call(this,i))}):(this[0]&&(wrap=jQuery(html,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&wrap.insertBefore(this[0]),wrap.map(function(){for(var elem=this;elem.firstElementChild;)elem=elem.firstElementChild;return elem}).append(this)),this)},wrapInner:function(html){return this.each(jQuery.isFunction(html)?function(i){jQuery(this).wrapInner(html.call(this,i))}:function(){var self=jQuery(this),contents=self.contents();contents.length?contents.wrapAll(html):self.append(html)})},wrap:function(html){var isFunction=jQuery.isFunction(html);return this.each(function(i){jQuery(this).wrapAll(isFunction?html.call(this,i):html)})},unwrap:function(){return this.parent().each(function(){jQuery.nodeName(this,"body")||jQuery(this).replaceWith(this.childNodes)}).end()}});var curCSS,iframe,rdisplayswap=/^(none|table(?!-c[ea]).+)/,rmargin=/^margin/,rnumsplit=new RegExp("^("+core_pnum+")(.*)$","i"),rnumnonpx=new RegExp("^("+core_pnum+")(?!px)[a-z%]+$","i"),rrelNum=new RegExp("^([+-])=("+core_pnum+")","i"),elemdisplay={BODY:"block"},cssShow={position:"absolute",visibility:"hidden",display:"block"},cssNormalTransform={letterSpacing:0,fontWeight:400},cssExpand=["Top","Right","Bottom","Left"],cssPrefixes=["Webkit","O","Moz","ms"];jQuery.fn.extend({css:function(name,value){return jQuery.access(this,function(elem,name,value){var styles,len,map={},i=0;if(jQuery.isArray(name)){for(styles=getStyles(elem),len=name.length;len>i;i++)map[name[i]]=jQuery.css(elem,name[i],!1,styles);return map}return value!==undefined?jQuery.style(elem,name,value):jQuery.css(elem,name)},name,value,arguments.length>1)},show:function(){return showHide(this,!0)},hide:function(){return showHide(this)},toggle:function(state){return"boolean"==typeof state?state?this.show():this.hide():this.each(function(){isHidden(this)?jQuery(this).show():jQuery(this).hide()})}}),jQuery.extend({cssHooks:{opacity:{get:function(elem,computed){if(computed){var ret=curCSS(elem,"opacity");return""===ret?"1":ret}}}},cssNumber:{columnCount:!0,fillOpacity:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{"float":"cssFloat"},style:function(elem,name,value,extra){if(elem&&3!==elem.nodeType&&8!==elem.nodeType&&elem.style){var ret,type,hooks,origName=jQuery.camelCase(name),style=elem.style;return name=jQuery.cssProps[origName]||(jQuery.cssProps[origName]=vendorPropName(style,origName)),hooks=jQuery.cssHooks[name]||jQuery.cssHooks[origName],value===undefined?hooks&&"get"in hooks&&(ret=hooks.get(elem,!1,extra))!==undefined?ret:style[name]:(type=typeof value,"string"===type&&(ret=rrelNum.exec(value))&&(value=(ret[1]+1)*ret[2]+parseFloat(jQuery.css(elem,name)),type="number"),null==value||"number"===type&&isNaN(value)||("number"!==type||jQuery.cssNumber[origName]||(value+="px"),jQuery.support.clearCloneStyle||""!==value||0!==name.indexOf("background")||(style[name]="inherit"),hooks&&"set"in hooks&&(value=hooks.set(elem,value,extra))===undefined||(style[name]=value)),void 0)}},css:function(elem,name,extra,styles){var val,num,hooks,origName=jQuery.camelCase(name);return name=jQuery.cssProps[origName]||(jQuery.cssProps[origName]=vendorPropName(elem.style,origName)),hooks=jQuery.cssHooks[name]||jQuery.cssHooks[origName],hooks&&"get"in hooks&&(val=hooks.get(elem,!0,extra)),val===undefined&&(val=curCSS(elem,name,styles)),"normal"===val&&name in cssNormalTransform&&(val=cssNormalTransform[name]),""===extra||extra?(num=parseFloat(val),extra===!0||jQuery.isNumeric(num)?num||0:val):val}}),curCSS=function(elem,name,_computed){var width,minWidth,maxWidth,computed=_computed||getStyles(elem),ret=computed?computed.getPropertyValue(name)||computed[name]:undefined,style=elem.style;return computed&&(""!==ret||jQuery.contains(elem.ownerDocument,elem)||(ret=jQuery.style(elem,name)),rnumnonpx.test(ret)&&rmargin.test(name)&&(width=style.width,minWidth=style.minWidth,maxWidth=style.maxWidth,style.minWidth=style.maxWidth=style.width=ret,ret=computed.width,style.width=width,style.minWidth=minWidth,style.maxWidth=maxWidth)),ret},jQuery.each(["height","width"],function(i,name){jQuery.cssHooks[name]={get:function(elem,computed,extra){return computed?0===elem.offsetWidth&&rdisplayswap.test(jQuery.css(elem,"display"))?jQuery.swap(elem,cssShow,function(){return getWidthOrHeight(elem,name,extra)}):getWidthOrHeight(elem,name,extra):void 0},set:function(elem,value,extra){var styles=extra&&getStyles(elem);return setPositiveNumber(elem,value,extra?augmentWidthOrHeight(elem,name,extra,jQuery.support.boxSizing&&"border-box"===jQuery.css(elem,"boxSizing",!1,styles),styles):0)}}}),jQuery(function(){jQuery.support.reliableMarginRight||(jQuery.cssHooks.marginRight={get:function(elem,computed){return computed?jQuery.swap(elem,{display:"inline-block"},curCSS,[elem,"marginRight"]):void 0}}),!jQuery.support.pixelPosition&&jQuery.fn.position&&jQuery.each(["top","left"],function(i,prop){jQuery.cssHooks[prop]={get:function(elem,computed){return computed?(computed=curCSS(elem,prop),rnumnonpx.test(computed)?jQuery(elem).position()[prop]+"px":computed):void 0}}})}),jQuery.expr&&jQuery.expr.filters&&(jQuery.expr.filters.hidden=function(elem){return elem.offsetWidth<=0&&elem.offsetHeight<=0},jQuery.expr.filters.visible=function(elem){return!jQuery.expr.filters.hidden(elem)}),jQuery.each({margin:"",padding:"",border:"Width"},function(prefix,suffix){jQuery.cssHooks[prefix+suffix]={expand:function(value){for(var i=0,expanded={},parts="string"==typeof value?value.split(" "):[value];4>i;i++)expanded[prefix+cssExpand[i]+suffix]=parts[i]||parts[i-2]||parts[0];return expanded}},rmargin.test(prefix)||(jQuery.cssHooks[prefix+suffix].set=setPositiveNumber)});var r20=/%20/g,rbracket=/\[\]$/,rCRLF=/\r?\n/g,rsubmitterTypes=/^(?:submit|button|image|reset|file)$/i,rsubmittable=/^(?:input|select|textarea|keygen)/i;jQuery.fn.extend({serialize:function(){return jQuery.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var elements=jQuery.prop(this,"elements");return elements?jQuery.makeArray(elements):this}).filter(function(){var type=this.type;return this.name&&!jQuery(this).is(":disabled")&&rsubmittable.test(this.nodeName)&&!rsubmitterTypes.test(type)&&(this.checked||!manipulation_rcheckableType.test(type))}).map(function(i,elem){var val=jQuery(this).val();return null==val?null:jQuery.isArray(val)?jQuery.map(val,function(val){return{name:elem.name,value:val.replace(rCRLF,"\r\n")}}):{name:elem.name,value:val.replace(rCRLF,"\r\n")}}).get()}}),jQuery.param=function(a,traditional){var prefix,s=[],add=function(key,value){value=jQuery.isFunction(value)?value():null==value?"":value,s[s.length]=encodeURIComponent(key)+"="+encodeURIComponent(value)};if(traditional===undefined&&(traditional=jQuery.ajaxSettings&&jQuery.ajaxSettings.traditional),jQuery.isArray(a)||a.jquery&&!jQuery.isPlainObject(a))jQuery.each(a,function(){add(this.name,this.value)});else for(prefix in a)buildParams(prefix,a[prefix],traditional,add);return s.join("&").replace(r20,"+")},jQuery.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu".split(" "),function(i,name){jQuery.fn[name]=function(data,fn){return arguments.length>0?this.on(name,null,data,fn):this.trigger(name)}}),jQuery.fn.extend({hover:function(fnOver,fnOut){return this.mouseenter(fnOver).mouseleave(fnOut||fnOver)},bind:function(types,data,fn){return this.on(types,null,data,fn)},unbind:function(types,fn){return this.off(types,null,fn)},delegate:function(selector,types,data,fn){return this.on(types,selector,data,fn)},undelegate:function(selector,types,fn){return 1===arguments.length?this.off(selector,"**"):this.off(types,selector||"**",fn)}});var ajaxLocParts,ajaxLocation,ajax_nonce=jQuery.now(),ajax_rquery=/\?/,rhash=/#.*$/,rts=/([?&])_=[^&]*/,rheaders=/^(.*?):[ \t]*([^\r\n]*)$/gm,rlocalProtocol=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,rnoContent=/^(?:GET|HEAD)$/,rprotocol=/^\/\//,rurl=/^([\w.+-]+:)(?:\/\/([^\/?#:]*)(?::(\d+)|)|)/,_load=jQuery.fn.load,prefilters={},transports={},allTypes="*/".concat("*");try{ajaxLocation=location.href}catch(e){ajaxLocation=document.createElement("a"),ajaxLocation.href="",ajaxLocation=ajaxLocation.href}ajaxLocParts=rurl.exec(ajaxLocation.toLowerCase())||[],jQuery.fn.load=function(url,params,callback){if("string"!=typeof url&&_load)return _load.apply(this,arguments);var selector,type,response,self=this,off=url.indexOf(" ");return off>=0&&(selector=url.slice(off),url=url.slice(0,off)),jQuery.isFunction(params)?(callback=params,params=undefined):params&&"object"==typeof params&&(type="POST"),self.length>0&&jQuery.ajax({url:url,type:type,dataType:"html",data:params}).done(function(responseText){response=arguments,self.html(selector?jQuery("<div>").append(jQuery.parseHTML(responseText)).find(selector):responseText)}).complete(callback&&function(jqXHR,status){self.each(callback,response||[jqXHR.responseText,status,jqXHR])}),this},jQuery.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(i,type){jQuery.fn[type]=function(fn){return this.on(type,fn)}}),jQuery.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:ajaxLocation,type:"GET",isLocal:rlocalProtocol.test(ajaxLocParts[1]),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":allTypes,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/xml/,html:/html/,json:/json/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":jQuery.parseJSON,"text xml":jQuery.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(target,settings){return settings?ajaxExtend(ajaxExtend(target,jQuery.ajaxSettings),settings):ajaxExtend(jQuery.ajaxSettings,target)},ajaxPrefilter:addToPrefiltersOrTransports(prefilters),ajaxTransport:addToPrefiltersOrTransports(transports),ajax:function(url,options){function done(status,nativeStatusText,responses,headers){var isSuccess,success,error,response,modified,statusText=nativeStatusText;2!==state&&(state=2,timeoutTimer&&clearTimeout(timeoutTimer),transport=undefined,responseHeadersString=headers||"",jqXHR.readyState=status>0?4:0,isSuccess=status>=200&&300>status||304===status,responses&&(response=ajaxHandleResponses(s,jqXHR,responses)),response=ajaxConvert(s,response,jqXHR,isSuccess),isSuccess?(s.ifModified&&(modified=jqXHR.getResponseHeader("Last-Modified"),modified&&(jQuery.lastModified[cacheURL]=modified),modified=jqXHR.getResponseHeader("etag"),modified&&(jQuery.etag[cacheURL]=modified)),204===status||"HEAD"===s.type?statusText="nocontent":304===status?statusText="notmodified":(statusText=response.state,success=response.data,error=response.error,isSuccess=!error)):(error=statusText,(status||!statusText)&&(statusText="error",0>status&&(status=0))),jqXHR.status=status,jqXHR.statusText=(nativeStatusText||statusText)+"",isSuccess?deferred.resolveWith(callbackContext,[success,statusText,jqXHR]):deferred.rejectWith(callbackContext,[jqXHR,statusText,error]),jqXHR.statusCode(statusCode),statusCode=undefined,fireGlobals&&globalEventContext.trigger(isSuccess?"ajaxSuccess":"ajaxError",[jqXHR,s,isSuccess?success:error]),completeDeferred.fireWith(callbackContext,[jqXHR,statusText]),fireGlobals&&(globalEventContext.trigger("ajaxComplete",[jqXHR,s]),--jQuery.active||jQuery.event.trigger("ajaxStop")))}"object"==typeof url&&(options=url,url=undefined),options=options||{};var transport,cacheURL,responseHeadersString,responseHeaders,timeoutTimer,parts,fireGlobals,i,s=jQuery.ajaxSetup({},options),callbackContext=s.context||s,globalEventContext=s.context&&(callbackContext.nodeType||callbackContext.jquery)?jQuery(callbackContext):jQuery.event,deferred=jQuery.Deferred(),completeDeferred=jQuery.Callbacks("once memory"),statusCode=s.statusCode||{},requestHeaders={},requestHeadersNames={},state=0,strAbort="canceled",jqXHR={readyState:0,getResponseHeader:function(key){var match;if(2===state){if(!responseHeaders)for(responseHeaders={};match=rheaders.exec(responseHeadersString);)responseHeaders[match[1].toLowerCase()]=match[2];match=responseHeaders[key.toLowerCase()]}return null==match?null:match},getAllResponseHeaders:function(){return 2===state?responseHeadersString:null},setRequestHeader:function(name,value){var lname=name.toLowerCase();return state||(name=requestHeadersNames[lname]=requestHeadersNames[lname]||name,requestHeaders[name]=value),this},overrideMimeType:function(type){return state||(s.mimeType=type),this},statusCode:function(map){var code;if(map)if(2>state)for(code in map)statusCode[code]=[statusCode[code],map[code]];else jqXHR.always(map[jqXHR.status]);return this},abort:function(statusText){var finalText=statusText||strAbort;return transport&&transport.abort(finalText),done(0,finalText),this}};if(deferred.promise(jqXHR).complete=completeDeferred.add,jqXHR.success=jqXHR.done,jqXHR.error=jqXHR.fail,s.url=((url||s.url||ajaxLocation)+"").replace(rhash,"").replace(rprotocol,ajaxLocParts[1]+"//"),s.type=options.method||options.type||s.method||s.type,s.dataTypes=jQuery.trim(s.dataType||"*").toLowerCase().match(core_rnotwhite)||[""],null==s.crossDomain&&(parts=rurl.exec(s.url.toLowerCase()),s.crossDomain=!(!parts||parts[1]===ajaxLocParts[1]&&parts[2]===ajaxLocParts[2]&&(parts[3]||("http:"===parts[1]?"80":"443"))===(ajaxLocParts[3]||("http:"===ajaxLocParts[1]?"80":"443")))),s.data&&s.processData&&"string"!=typeof s.data&&(s.data=jQuery.param(s.data,s.traditional)),inspectPrefiltersOrTransports(prefilters,s,options,jqXHR),2===state)return jqXHR;fireGlobals=s.global,fireGlobals&&0===jQuery.active++&&jQuery.event.trigger("ajaxStart"),s.type=s.type.toUpperCase(),s.hasContent=!rnoContent.test(s.type),cacheURL=s.url,s.hasContent||(s.data&&(cacheURL=s.url+=(ajax_rquery.test(cacheURL)?"&":"?")+s.data,delete s.data),s.cache===!1&&(s.url=rts.test(cacheURL)?cacheURL.replace(rts,"$1_="+ajax_nonce++):cacheURL+(ajax_rquery.test(cacheURL)?"&":"?")+"_="+ajax_nonce++)),s.ifModified&&(jQuery.lastModified[cacheURL]&&jqXHR.setRequestHeader("If-Modified-Since",jQuery.lastModified[cacheURL]),jQuery.etag[cacheURL]&&jqXHR.setRequestHeader("If-None-Match",jQuery.etag[cacheURL])),(s.data&&s.hasContent&&s.contentType!==!1||options.contentType)&&jqXHR.setRequestHeader("Content-Type",s.contentType),jqXHR.setRequestHeader("Accept",s.dataTypes[0]&&s.accepts[s.dataTypes[0]]?s.accepts[s.dataTypes[0]]+("*"!==s.dataTypes[0]?", "+allTypes+"; q=0.01":""):s.accepts["*"]);for(i in s.headers)jqXHR.setRequestHeader(i,s.headers[i]);if(s.beforeSend&&(s.beforeSend.call(callbackContext,jqXHR,s)===!1||2===state))return jqXHR.abort();strAbort="abort";for(i in{success:1,error:1,complete:1})jqXHR[i](s[i]);if(transport=inspectPrefiltersOrTransports(transports,s,options,jqXHR)){jqXHR.readyState=1,fireGlobals&&globalEventContext.trigger("ajaxSend",[jqXHR,s]),s.async&&s.timeout>0&&(timeoutTimer=setTimeout(function(){jqXHR.abort("timeout")},s.timeout));try{state=1,transport.send(requestHeaders,done)}catch(e){if(!(2>state))throw e;done(-1,e)}}else done(-1,"No Transport");return jqXHR},getJSON:function(url,data,callback){return jQuery.get(url,data,callback,"json")},getScript:function(url,callback){return jQuery.get(url,undefined,callback,"script")}}),jQuery.each(["get","post"],function(i,method){jQuery[method]=function(url,data,callback,type){return jQuery.isFunction(data)&&(type=type||callback,callback=data,data=undefined),jQuery.ajax({url:url,type:method,dataType:type,data:data,success:callback})}}),jQuery.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/(?:java|ecma)script/},converters:{"text script":function(text){return jQuery.globalEval(text),text}}}),jQuery.ajaxPrefilter("script",function(s){s.cache===undefined&&(s.cache=!1),s.crossDomain&&(s.type="GET")}),jQuery.ajaxTransport("script",function(s){if(s.crossDomain){var script,callback;return{send:function(_,complete){script=jQuery("<script>").prop({async:!0,charset:s.scriptCharset,src:s.url}).on("load error",callback=function(evt){script.remove(),callback=null,evt&&complete("error"===evt.type?404:200,evt.type)}),document.head.appendChild(script[0])},abort:function(){callback&&callback()}}}});var oldCallbacks=[],rjsonp=/(=)\?(?=&|$)|\?\?/;jQuery.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var callback=oldCallbacks.pop()||jQuery.expando+"_"+ajax_nonce++;return this[callback]=!0,callback}}),jQuery.ajaxPrefilter("json jsonp",function(s,originalSettings,jqXHR){var callbackName,overwritten,responseContainer,jsonProp=s.jsonp!==!1&&(rjsonp.test(s.url)?"url":"string"==typeof s.data&&!(s.contentType||"").indexOf("application/x-www-form-urlencoded")&&rjsonp.test(s.data)&&"data");return jsonProp||"jsonp"===s.dataTypes[0]?(callbackName=s.jsonpCallback=jQuery.isFunction(s.jsonpCallback)?s.jsonpCallback():s.jsonpCallback,jsonProp?s[jsonProp]=s[jsonProp].replace(rjsonp,"$1"+callbackName):s.jsonp!==!1&&(s.url+=(ajax_rquery.test(s.url)?"&":"?")+s.jsonp+"="+callbackName),s.converters["script json"]=function(){return responseContainer||jQuery.error(callbackName+" was not called"),responseContainer[0]
},s.dataTypes[0]="json",overwritten=window[callbackName],window[callbackName]=function(){responseContainer=arguments},jqXHR.always(function(){window[callbackName]=overwritten,s[callbackName]&&(s.jsonpCallback=originalSettings.jsonpCallback,oldCallbacks.push(callbackName)),responseContainer&&jQuery.isFunction(overwritten)&&overwritten(responseContainer[0]),responseContainer=overwritten=undefined}),"script"):void 0}),jQuery.ajaxSettings.xhr=function(){try{return new XMLHttpRequest}catch(e){}};var xhrSupported=jQuery.ajaxSettings.xhr(),xhrSuccessStatus={0:200,1223:204},xhrId=0,xhrCallbacks={};window.ActiveXObject&&jQuery(window).on("unload",function(){for(var key in xhrCallbacks)xhrCallbacks[key]();xhrCallbacks=undefined}),jQuery.support.cors=!!xhrSupported&&"withCredentials"in xhrSupported,jQuery.support.ajax=xhrSupported=!!xhrSupported,jQuery.ajaxTransport(function(options){var callback;return jQuery.support.cors||xhrSupported&&!options.crossDomain?{send:function(headers,complete){var i,id,xhr=options.xhr();if(xhr.open(options.type,options.url,options.async,options.username,options.password),options.xhrFields)for(i in options.xhrFields)xhr[i]=options.xhrFields[i];options.mimeType&&xhr.overrideMimeType&&xhr.overrideMimeType(options.mimeType),options.crossDomain||headers["X-Requested-With"]||(headers["X-Requested-With"]="XMLHttpRequest");for(i in headers)xhr.setRequestHeader(i,headers[i]);callback=function(type){return function(){callback&&(delete xhrCallbacks[id],callback=xhr.onload=xhr.onerror=null,"abort"===type?xhr.abort():"error"===type?complete(xhr.status||404,xhr.statusText):complete(xhrSuccessStatus[xhr.status]||xhr.status,xhr.statusText,"string"==typeof xhr.responseText?{text:xhr.responseText}:undefined,xhr.getAllResponseHeaders()))}},xhr.onload=callback(),xhr.onerror=callback("error"),callback=xhrCallbacks[id=xhrId++]=callback("abort"),xhr.send(options.hasContent&&options.data||null)},abort:function(){callback&&callback()}}:void 0});var fxNow,timerId,rfxtypes=/^(?:toggle|show|hide)$/,rfxnum=new RegExp("^(?:([+-])=|)("+core_pnum+")([a-z%]*)$","i"),rrun=/queueHooks$/,animationPrefilters=[defaultPrefilter],tweeners={"*":[function(prop,value){var tween=this.createTween(prop,value),target=tween.cur(),parts=rfxnum.exec(value),unit=parts&&parts[3]||(jQuery.cssNumber[prop]?"":"px"),start=(jQuery.cssNumber[prop]||"px"!==unit&&+target)&&rfxnum.exec(jQuery.css(tween.elem,prop)),scale=1,maxIterations=20;if(start&&start[3]!==unit){unit=unit||start[3],parts=parts||[],start=+target||1;do scale=scale||".5",start/=scale,jQuery.style(tween.elem,prop,start+unit);while(scale!==(scale=tween.cur()/target)&&1!==scale&&--maxIterations)}return parts&&(start=tween.start=+start||+target||0,tween.unit=unit,tween.end=parts[1]?start+(parts[1]+1)*parts[2]:+parts[2]),tween}]};jQuery.Animation=jQuery.extend(Animation,{tweener:function(props,callback){jQuery.isFunction(props)?(callback=props,props=["*"]):props=props.split(" ");for(var prop,index=0,length=props.length;length>index;index++)prop=props[index],tweeners[prop]=tweeners[prop]||[],tweeners[prop].unshift(callback)},prefilter:function(callback,prepend){prepend?animationPrefilters.unshift(callback):animationPrefilters.push(callback)}}),jQuery.Tween=Tween,Tween.prototype={constructor:Tween,init:function(elem,options,prop,end,easing,unit){this.elem=elem,this.prop=prop,this.easing=easing||"swing",this.options=options,this.start=this.now=this.cur(),this.end=end,this.unit=unit||(jQuery.cssNumber[prop]?"":"px")},cur:function(){var hooks=Tween.propHooks[this.prop];return hooks&&hooks.get?hooks.get(this):Tween.propHooks._default.get(this)},run:function(percent){var eased,hooks=Tween.propHooks[this.prop];return this.pos=eased=this.options.duration?jQuery.easing[this.easing](percent,this.options.duration*percent,0,1,this.options.duration):percent,this.now=(this.end-this.start)*eased+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),hooks&&hooks.set?hooks.set(this):Tween.propHooks._default.set(this),this}},Tween.prototype.init.prototype=Tween.prototype,Tween.propHooks={_default:{get:function(tween){var result;return null==tween.elem[tween.prop]||tween.elem.style&&null!=tween.elem.style[tween.prop]?(result=jQuery.css(tween.elem,tween.prop,""),result&&"auto"!==result?result:0):tween.elem[tween.prop]},set:function(tween){jQuery.fx.step[tween.prop]?jQuery.fx.step[tween.prop](tween):tween.elem.style&&(null!=tween.elem.style[jQuery.cssProps[tween.prop]]||jQuery.cssHooks[tween.prop])?jQuery.style(tween.elem,tween.prop,tween.now+tween.unit):tween.elem[tween.prop]=tween.now}}},Tween.propHooks.scrollTop=Tween.propHooks.scrollLeft={set:function(tween){tween.elem.nodeType&&tween.elem.parentNode&&(tween.elem[tween.prop]=tween.now)}},jQuery.each(["toggle","show","hide"],function(i,name){var cssFn=jQuery.fn[name];jQuery.fn[name]=function(speed,easing,callback){return null==speed||"boolean"==typeof speed?cssFn.apply(this,arguments):this.animate(genFx(name,!0),speed,easing,callback)}}),jQuery.fn.extend({fadeTo:function(speed,to,easing,callback){return this.filter(isHidden).css("opacity",0).show().end().animate({opacity:to},speed,easing,callback)},animate:function(prop,speed,easing,callback){var empty=jQuery.isEmptyObject(prop),optall=jQuery.speed(speed,easing,callback),doAnimation=function(){var anim=Animation(this,jQuery.extend({},prop),optall);(empty||data_priv.get(this,"finish"))&&anim.stop(!0)};return doAnimation.finish=doAnimation,empty||optall.queue===!1?this.each(doAnimation):this.queue(optall.queue,doAnimation)},stop:function(type,clearQueue,gotoEnd){var stopQueue=function(hooks){var stop=hooks.stop;delete hooks.stop,stop(gotoEnd)};return"string"!=typeof type&&(gotoEnd=clearQueue,clearQueue=type,type=undefined),clearQueue&&type!==!1&&this.queue(type||"fx",[]),this.each(function(){var dequeue=!0,index=null!=type&&type+"queueHooks",timers=jQuery.timers,data=data_priv.get(this);if(index)data[index]&&data[index].stop&&stopQueue(data[index]);else for(index in data)data[index]&&data[index].stop&&rrun.test(index)&&stopQueue(data[index]);for(index=timers.length;index--;)timers[index].elem!==this||null!=type&&timers[index].queue!==type||(timers[index].anim.stop(gotoEnd),dequeue=!1,timers.splice(index,1));(dequeue||!gotoEnd)&&jQuery.dequeue(this,type)})},finish:function(type){return type!==!1&&(type=type||"fx"),this.each(function(){var index,data=data_priv.get(this),queue=data[type+"queue"],hooks=data[type+"queueHooks"],timers=jQuery.timers,length=queue?queue.length:0;for(data.finish=!0,jQuery.queue(this,type,[]),hooks&&hooks.stop&&hooks.stop.call(this,!0),index=timers.length;index--;)timers[index].elem===this&&timers[index].queue===type&&(timers[index].anim.stop(!0),timers.splice(index,1));for(index=0;length>index;index++)queue[index]&&queue[index].finish&&queue[index].finish.call(this);delete data.finish})}}),jQuery.each({slideDown:genFx("show"),slideUp:genFx("hide"),slideToggle:genFx("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(name,props){jQuery.fn[name]=function(speed,easing,callback){return this.animate(props,speed,easing,callback)}}),jQuery.speed=function(speed,easing,fn){var opt=speed&&"object"==typeof speed?jQuery.extend({},speed):{complete:fn||!fn&&easing||jQuery.isFunction(speed)&&speed,duration:speed,easing:fn&&easing||easing&&!jQuery.isFunction(easing)&&easing};return opt.duration=jQuery.fx.off?0:"number"==typeof opt.duration?opt.duration:opt.duration in jQuery.fx.speeds?jQuery.fx.speeds[opt.duration]:jQuery.fx.speeds._default,(null==opt.queue||opt.queue===!0)&&(opt.queue="fx"),opt.old=opt.complete,opt.complete=function(){jQuery.isFunction(opt.old)&&opt.old.call(this),opt.queue&&jQuery.dequeue(this,opt.queue)},opt},jQuery.easing={linear:function(p){return p},swing:function(p){return.5-Math.cos(p*Math.PI)/2}},jQuery.timers=[],jQuery.fx=Tween.prototype.init,jQuery.fx.tick=function(){var timer,timers=jQuery.timers,i=0;for(fxNow=jQuery.now();i<timers.length;i++)timer=timers[i],timer()||timers[i]!==timer||timers.splice(i--,1);timers.length||jQuery.fx.stop(),fxNow=undefined},jQuery.fx.timer=function(timer){timer()&&jQuery.timers.push(timer)&&jQuery.fx.start()},jQuery.fx.interval=13,jQuery.fx.start=function(){timerId||(timerId=setInterval(jQuery.fx.tick,jQuery.fx.interval))},jQuery.fx.stop=function(){clearInterval(timerId),timerId=null},jQuery.fx.speeds={slow:600,fast:200,_default:400},jQuery.fx.step={},jQuery.expr&&jQuery.expr.filters&&(jQuery.expr.filters.animated=function(elem){return jQuery.grep(jQuery.timers,function(fn){return elem===fn.elem}).length}),jQuery.fn.offset=function(options){if(arguments.length)return options===undefined?this:this.each(function(i){jQuery.offset.setOffset(this,options,i)});var docElem,win,elem=this[0],box={top:0,left:0},doc=elem&&elem.ownerDocument;if(doc)return docElem=doc.documentElement,jQuery.contains(docElem,elem)?(typeof elem.getBoundingClientRect!==core_strundefined&&(box=elem.getBoundingClientRect()),win=getWindow(doc),{top:box.top+win.pageYOffset-docElem.clientTop,left:box.left+win.pageXOffset-docElem.clientLeft}):box},jQuery.offset={setOffset:function(elem,options,i){var curPosition,curLeft,curCSSTop,curTop,curOffset,curCSSLeft,calculatePosition,position=jQuery.css(elem,"position"),curElem=jQuery(elem),props={};"static"===position&&(elem.style.position="relative"),curOffset=curElem.offset(),curCSSTop=jQuery.css(elem,"top"),curCSSLeft=jQuery.css(elem,"left"),calculatePosition=("absolute"===position||"fixed"===position)&&(curCSSTop+curCSSLeft).indexOf("auto")>-1,calculatePosition?(curPosition=curElem.position(),curTop=curPosition.top,curLeft=curPosition.left):(curTop=parseFloat(curCSSTop)||0,curLeft=parseFloat(curCSSLeft)||0),jQuery.isFunction(options)&&(options=options.call(elem,i,curOffset)),null!=options.top&&(props.top=options.top-curOffset.top+curTop),null!=options.left&&(props.left=options.left-curOffset.left+curLeft),"using"in options?options.using.call(elem,props):curElem.css(props)}},jQuery.fn.extend({position:function(){if(this[0]){var offsetParent,offset,elem=this[0],parentOffset={top:0,left:0};return"fixed"===jQuery.css(elem,"position")?offset=elem.getBoundingClientRect():(offsetParent=this.offsetParent(),offset=this.offset(),jQuery.nodeName(offsetParent[0],"html")||(parentOffset=offsetParent.offset()),parentOffset.top+=jQuery.css(offsetParent[0],"borderTopWidth",!0),parentOffset.left+=jQuery.css(offsetParent[0],"borderLeftWidth",!0)),{top:offset.top-parentOffset.top-jQuery.css(elem,"marginTop",!0),left:offset.left-parentOffset.left-jQuery.css(elem,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var offsetParent=this.offsetParent||docElem;offsetParent&&!jQuery.nodeName(offsetParent,"html")&&"static"===jQuery.css(offsetParent,"position");)offsetParent=offsetParent.offsetParent;return offsetParent||docElem})}}),jQuery.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(method,prop){var top="pageYOffset"===prop;jQuery.fn[method]=function(val){return jQuery.access(this,function(elem,method,val){var win=getWindow(elem);return val===undefined?win?win[prop]:elem[method]:void(win?win.scrollTo(top?window.pageXOffset:val,top?val:window.pageYOffset):elem[method]=val)},method,val,arguments.length,null)}}),jQuery.each({Height:"height",Width:"width"},function(name,type){jQuery.each({padding:"inner"+name,content:type,"":"outer"+name},function(defaultExtra,funcName){jQuery.fn[funcName]=function(margin,value){var chainable=arguments.length&&(defaultExtra||"boolean"!=typeof margin),extra=defaultExtra||(margin===!0||value===!0?"margin":"border");return jQuery.access(this,function(elem,type,value){var doc;return jQuery.isWindow(elem)?elem.document.documentElement["client"+name]:9===elem.nodeType?(doc=elem.documentElement,Math.max(elem.body["scroll"+name],doc["scroll"+name],elem.body["offset"+name],doc["offset"+name],doc["client"+name])):value===undefined?jQuery.css(elem,type,extra):jQuery.style(elem,type,value,extra)},type,chainable?margin:undefined,chainable,null)}})}),jQuery.fn.size=function(){return this.length},jQuery.fn.andSelf=jQuery.fn.addBack,"object"==typeof module&&module&&"object"==typeof module.exports?module.exports=jQuery:"function"==typeof define&&define.amd&&define("jquery",[],function(){return jQuery}),"object"==typeof window&&"object"==typeof window.document&&(window.jQuery=window.$=jQuery)}(window),function(window,document,undefined){"use strict";function minErr(module,ErrorConstructor){return ErrorConstructor=ErrorConstructor||Error,function(){var message,i,code=arguments[0],prefix="["+(module?module+":":"")+code+"] ",template=arguments[1],templateArgs=arguments,stringify=function(obj){return"function"==typeof obj?obj.toString().replace(/ \{[\s\S]*$/,""):"undefined"==typeof obj?"undefined":"string"!=typeof obj?JSON.stringify(obj):obj};for(message=prefix+template.replace(/\{\d+\}/g,function(match){var arg,index=+match.slice(1,-1);return index+2<templateArgs.length?(arg=templateArgs[index+2],"function"==typeof arg?arg.toString().replace(/ ?\{[\s\S]*$/,""):"undefined"==typeof arg?"undefined":"string"!=typeof arg?toJson(arg):arg):match}),message=message+"\nhttp://errors.angularjs.org/1.3.0-rc.4/"+(module?module+"/":"")+code,i=2;i<arguments.length;i++)message=message+(2==i?"?":"&")+"p"+(i-2)+"="+encodeURIComponent(stringify(arguments[i]));return new ErrorConstructor(message)}}function isArrayLike(obj){if(null==obj||isWindow(obj))return!1;var length=obj.length;return 1===obj.nodeType&&length?!0:isString(obj)||isArray(obj)||0===length||"number"==typeof length&&length>0&&length-1 in obj}function forEach(obj,iterator,context){var key,length;if(obj)if(isFunction(obj))for(key in obj)"prototype"==key||"length"==key||"name"==key||obj.hasOwnProperty&&!obj.hasOwnProperty(key)||iterator.call(context,obj[key],key,obj);else if(isArray(obj)||isArrayLike(obj)){var isPrimitive="object"!=typeof obj;for(key=0,length=obj.length;length>key;key++)(isPrimitive||key in obj)&&iterator.call(context,obj[key],key,obj)}else if(obj.forEach&&obj.forEach!==forEach)obj.forEach(iterator,context,obj);else for(key in obj)obj.hasOwnProperty(key)&&iterator.call(context,obj[key],key,obj);return obj}function sortedKeys(obj){var keys=[];for(var key in obj)obj.hasOwnProperty(key)&&keys.push(key);return keys.sort()}function forEachSorted(obj,iterator,context){for(var keys=sortedKeys(obj),i=0;i<keys.length;i++)iterator.call(context,obj[keys[i]],keys[i]);return keys}function reverseParams(iteratorFn){return function(value,key){iteratorFn(key,value)}}function nextUid(){return++uid}function setHashKey(obj,h){h?obj.$$hashKey=h:delete obj.$$hashKey}function extend(dst){for(var h=dst.$$hashKey,i=1,ii=arguments.length;ii>i;i++){var obj=arguments[i];if(obj)for(var keys=Object.keys(obj),j=0,jj=keys.length;jj>j;j++){var key=keys[j];dst[key]=obj[key]}}return setHashKey(dst,h),dst}function int(str){return parseInt(str,10)}function inherit(parent,extra){return extend(new(extend(function(){},{prototype:parent})),extra)}function noop(){}function identity($){return $}function valueFn(value){return function(){return value}}function isUndefined(value){return"undefined"==typeof value}function isDefined(value){return"undefined"!=typeof value}function isObject(value){return null!==value&&"object"==typeof value}function isString(value){return"string"==typeof value}function isNumber(value){return"number"==typeof value}function isDate(value){return"[object Date]"===toString.call(value)}function isFunction(value){return"function"==typeof value}function isRegExp(value){return"[object RegExp]"===toString.call(value)}function isWindow(obj){return obj&&obj.window===obj}function isScope(obj){return obj&&obj.$evalAsync&&obj.$watch}function isFile(obj){return"[object File]"===toString.call(obj)}function isBlob(obj){return"[object Blob]"===toString.call(obj)}function isBoolean(value){return"boolean"==typeof value}function isPromiseLike(obj){return obj&&isFunction(obj.then)}function isElement(node){return!(!node||!(node.nodeName||node.prop&&node.attr&&node.find))}function makeMap(str){var i,obj={},items=str.split(",");for(i=0;i<items.length;i++)obj[items[i]]=!0;return obj}function nodeName_(element){return lowercase(element.nodeName||element[0].nodeName)}function arrayRemove(array,value){var index=array.indexOf(value);return index>=0&&array.splice(index,1),value}function copy(source,destination,stackSource,stackDest){if(isWindow(source)||isScope(source))throw ngMinErr("cpws","Can't copy! Making copies of Window or Scope instances is not supported.");if(destination){if(source===destination)throw ngMinErr("cpi","Can't copy! Source and destination are identical.");if(stackSource=stackSource||[],stackDest=stackDest||[],isObject(source)){var index=stackSource.indexOf(source);if(-1!==index)return stackDest[index];stackSource.push(source),stackDest.push(destination)}var result;if(isArray(source)){destination.length=0;for(var i=0;i<source.length;i++)result=copy(source[i],null,stackSource,stackDest),isObject(source[i])&&(stackSource.push(source[i]),stackDest.push(result)),destination.push(result)}else{var h=destination.$$hashKey;isArray(destination)?destination.length=0:forEach(destination,function(value,key){delete destination[key]});for(var key in source)source.hasOwnProperty(key)&&(result=copy(source[key],null,stackSource,stackDest),isObject(source[key])&&(stackSource.push(source[key]),stackDest.push(result)),destination[key]=result);setHashKey(destination,h)}}else if(destination=source,source)if(isArray(source))destination=copy(source,[],stackSource,stackDest);else if(isDate(source))destination=new Date(source.getTime());else if(isRegExp(source))destination=new RegExp(source.source,source.toString().match(/[^\/]*$/)[0]),destination.lastIndex=source.lastIndex;else if(isObject(source)){var emptyObject=Object.create(Object.getPrototypeOf(source));destination=copy(source,emptyObject,stackSource,stackDest)}return destination}function shallowCopy(src,dst){if(isArray(src)){dst=dst||[];for(var i=0,ii=src.length;ii>i;i++)dst[i]=src[i]}else if(isObject(src)){dst=dst||{};for(var key in src)("$"!==key.charAt(0)||"$"!==key.charAt(1))&&(dst[key]=src[key])}return dst||src}function equals(o1,o2){if(o1===o2)return!0;if(null===o1||null===o2)return!1;if(o1!==o1&&o2!==o2)return!0;var length,key,keySet,t1=typeof o1,t2=typeof o2;if(t1==t2&&"object"==t1){if(!isArray(o1)){if(isDate(o1))return isDate(o2)?equals(o1.getTime(),o2.getTime()):!1;if(isRegExp(o1)&&isRegExp(o2))return o1.toString()==o2.toString();if(isScope(o1)||isScope(o2)||isWindow(o1)||isWindow(o2)||isArray(o2))return!1;keySet={};for(key in o1)if("$"!==key.charAt(0)&&!isFunction(o1[key])){if(!equals(o1[key],o2[key]))return!1;keySet[key]=!0}for(key in o2)if(!keySet.hasOwnProperty(key)&&"$"!==key.charAt(0)&&o2[key]!==undefined&&!isFunction(o2[key]))return!1;return!0}if(!isArray(o2))return!1;if((length=o1.length)==o2.length){for(key=0;length>key;key++)if(!equals(o1[key],o2[key]))return!1;return!0}}return!1}function concat(array1,array2,index){return array1.concat(slice.call(array2,index))}function sliceArgs(args,startIndex){return slice.call(args,startIndex||0)}function bind(self,fn){var curryArgs=arguments.length>2?sliceArgs(arguments,2):[];return!isFunction(fn)||fn instanceof RegExp?fn:curryArgs.length?function(){return arguments.length?fn.apply(self,curryArgs.concat(slice.call(arguments,0))):fn.apply(self,curryArgs)}:function(){return arguments.length?fn.apply(self,arguments):fn.call(self)}}function toJsonReplacer(key,value){var val=value;return"string"==typeof key&&"$"===key.charAt(0)&&"$"===key.charAt(1)?val=undefined:isWindow(value)?val="$WINDOW":value&&document===value?val="$DOCUMENT":isScope(value)&&(val="$SCOPE"),val}function toJson(obj,pretty){return"undefined"==typeof obj?undefined:JSON.stringify(obj,toJsonReplacer,pretty?"  ":null)}function fromJson(json){return isString(json)?JSON.parse(json):json}function startingTag(element){element=jqLite(element).clone();try{element.empty()}catch(e){}var TEXT_NODE=3,elemHtml=jqLite("<div>").append(element).html();try{return element[0].nodeType===TEXT_NODE?lowercase(elemHtml):elemHtml.match(/^(<[^>]+>)/)[1].replace(/^<([\w\-]+)/,function(match,nodeName){return"<"+lowercase(nodeName)})}catch(e){return lowercase(elemHtml)}}function tryDecodeURIComponent(value){try{return decodeURIComponent(value)}catch(e){}}function parseKeyValue(keyValue){var key_value,key,obj={};return forEach((keyValue||"").split("&"),function(keyValue){if(keyValue&&(key_value=keyValue.replace(/\+/g,"%20").split("="),key=tryDecodeURIComponent(key_value[0]),isDefined(key))){var val=isDefined(key_value[1])?tryDecodeURIComponent(key_value[1]):!0;hasOwnProperty.call(obj,key)?isArray(obj[key])?obj[key].push(val):obj[key]=[obj[key],val]:obj[key]=val}}),obj}function toKeyValue(obj){var parts=[];return forEach(obj,function(value,key){isArray(value)?forEach(value,function(arrayValue){parts.push(encodeUriQuery(key,!0)+(arrayValue===!0?"":"="+encodeUriQuery(arrayValue,!0)))}):parts.push(encodeUriQuery(key,!0)+(value===!0?"":"="+encodeUriQuery(value,!0)))}),parts.length?parts.join("&"):""}function encodeUriSegment(val){return encodeUriQuery(val,!0).replace(/%26/gi,"&").replace(/%3D/gi,"=").replace(/%2B/gi,"+")}function encodeUriQuery(val,pctEncodeSpaces){return encodeURIComponent(val).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%3B/gi,";").replace(/%20/g,pctEncodeSpaces?"%20":"+")}function getNgAttribute(element,ngAttr){var attr,i,ii=ngAttrPrefixes.length;for(element=jqLite(element),i=0;ii>i;++i)if(attr=ngAttrPrefixes[i]+ngAttr,isString(attr=element.attr(attr)))return attr;return null}function angularInit(element,bootstrap){var appElement,module,config={};forEach(ngAttrPrefixes,function(prefix){var name=prefix+"app";!appElement&&element.hasAttribute&&element.hasAttribute(name)&&(appElement=element,module=element.getAttribute(name))}),forEach(ngAttrPrefixes,function(prefix){var candidate,name=prefix+"app";!appElement&&(candidate=element.querySelector("["+name.replace(":","\\:")+"]"))&&(appElement=candidate,module=candidate.getAttribute(name))}),appElement&&(config.strictDi=null!==getNgAttribute(appElement,"strict-di"),bootstrap(appElement,module?[module]:[],config))}function bootstrap(element,modules,config){isObject(config)||(config={});var defaultConfig={strictDi:!1};config=extend(defaultConfig,config);var doBootstrap=function(){if(element=jqLite(element),element.injector()){var tag=element[0]===document?"document":startingTag(element);throw ngMinErr("btstrpd","App Already Bootstrapped with this Element '{0}'",tag.replace(/</,"&lt;").replace(/>/,"&gt;"))}modules=modules||[],modules.unshift(["$provide",function($provide){$provide.value("$rootElement",element)}]),config.debugInfoEnabled&&modules.push(["$compileProvider",function($compileProvider){$compileProvider.debugInfoEnabled(!0)}]),modules.unshift("ng");var injector=createInjector(modules,config.strictDi);return injector.invoke(["$rootScope","$rootElement","$compile","$injector",function(scope,element,compile,injector){scope.$apply(function(){element.data("$injector",injector),compile(element)(scope)})}]),injector},NG_ENABLE_DEBUG_INFO=/^NG_ENABLE_DEBUG_INFO!/,NG_DEFER_BOOTSTRAP=/^NG_DEFER_BOOTSTRAP!/;return window&&NG_ENABLE_DEBUG_INFO.test(window.name)&&(config.debugInfoEnabled=!0,window.name=window.name.replace(NG_ENABLE_DEBUG_INFO,"")),window&&!NG_DEFER_BOOTSTRAP.test(window.name)?doBootstrap():(window.name=window.name.replace(NG_DEFER_BOOTSTRAP,""),void(angular.resumeBootstrap=function(extraModules){forEach(extraModules,function(module){modules.push(module)}),doBootstrap()}))}function reloadWithDebugInfo(){window.name="NG_ENABLE_DEBUG_INFO!"+window.name,window.location.reload()}function getTestability(rootElement){return angular.element(rootElement).injector().get("$$testability")}function snake_case(name,separator){return separator=separator||"_",name.replace(SNAKE_CASE_REGEXP,function(letter,pos){return(pos?separator:"")+letter.toLowerCase()})}function bindJQuery(){var originalCleanData;bindJQueryFired||(jQuery=window.jQuery,jQuery&&jQuery.fn.on?(jqLite=jQuery,extend(jQuery.fn,{scope:JQLitePrototype.scope,isolateScope:JQLitePrototype.isolateScope,controller:JQLitePrototype.controller,injector:JQLitePrototype.injector,inheritedData:JQLitePrototype.inheritedData}),originalCleanData=jQuery.cleanData,jQuery.cleanData=function(elems){var events;if(skipDestroyOnNextJQueryCleanData)skipDestroyOnNextJQueryCleanData=!1;else for(var elem,i=0;null!=(elem=elems[i]);i++)events=jQuery._data(elem,"events"),events&&events.$destroy&&jQuery(elem).triggerHandler("$destroy");originalCleanData(elems)}):jqLite=JQLite,angular.element=jqLite,bindJQueryFired=!0)}function assertArg(arg,name,reason){if(!arg)throw ngMinErr("areq","Argument '{0}' is {1}",name||"?",reason||"required");return arg}function assertArgFn(arg,name,acceptArrayAnnotation){return acceptArrayAnnotation&&isArray(arg)&&(arg=arg[arg.length-1]),assertArg(isFunction(arg),name,"not a function, got "+(arg&&"object"==typeof arg?arg.constructor.name||"Object":typeof arg)),arg}function assertNotHasOwnProperty(name,context){if("hasOwnProperty"===name)throw ngMinErr("badname","hasOwnProperty is not a valid {0} name",context)}function getter(obj,path,bindFnToScope){if(!path)return obj;for(var key,keys=path.split("."),lastInstance=obj,len=keys.length,i=0;len>i;i++)key=keys[i],obj&&(obj=(lastInstance=obj)[key]);return!bindFnToScope&&isFunction(obj)?bind(lastInstance,obj):obj}function getBlockNodes(nodes){var node=nodes[0],endNode=nodes[nodes.length-1],blockNodes=[node];do{if(node=node.nextSibling,!node)break;blockNodes.push(node)}while(node!==endNode);return jqLite(blockNodes)}function createMap(){return Object.create(null)}function setupModuleLoader(window){function ensure(obj,name,factory){return obj[name]||(obj[name]=factory())}var $injectorMinErr=minErr("$injector"),ngMinErr=minErr("ng"),angular=ensure(window,"angular",Object);return angular.$$minErr=angular.$$minErr||minErr,ensure(angular,"module",function(){var modules={};return function(name,requires,configFn){var assertNotHasOwnProperty=function(name,context){if("hasOwnProperty"===name)throw ngMinErr("badname","hasOwnProperty is not a valid {0} name",context)};return assertNotHasOwnProperty(name,"module"),requires&&modules.hasOwnProperty(name)&&(modules[name]=null),ensure(modules,name,function(){function invokeLater(provider,method,insertMethod,queue){return queue||(queue=invokeQueue),function(){return queue[insertMethod||"push"]([provider,method,arguments]),moduleInstance}}if(!requires)throw $injectorMinErr("nomod","Module '{0}' is not available! You either misspelled the module name or forgot to load it. If registering a module ensure that you specify the dependencies as the second argument.",name);var invokeQueue=[],configBlocks=[],runBlocks=[],config=invokeLater("$injector","invoke","push",configBlocks),moduleInstance={_invokeQueue:invokeQueue,_configBlocks:configBlocks,_runBlocks:runBlocks,requires:requires,name:name,provider:invokeLater("$provide","provider"),factory:invokeLater("$provide","factory"),service:invokeLater("$provide","service"),value:invokeLater("$provide","value"),constant:invokeLater("$provide","constant","unshift"),animation:invokeLater("$animateProvider","register"),filter:invokeLater("$filterProvider","register"),controller:invokeLater("$controllerProvider","register"),directive:invokeLater("$compileProvider","directive"),config:config,run:function(block){return runBlocks.push(block),this}};return configFn&&config(configFn),moduleInstance})}})}function publishExternalAPI(angular){extend(angular,{bootstrap:bootstrap,copy:copy,extend:extend,equals:equals,element:jqLite,forEach:forEach,injector:createInjector,noop:noop,bind:bind,toJson:toJson,fromJson:fromJson,identity:identity,isUndefined:isUndefined,isDefined:isDefined,isString:isString,isFunction:isFunction,isObject:isObject,isNumber:isNumber,isElement:isElement,isArray:isArray,version:version,isDate:isDate,lowercase:lowercase,uppercase:uppercase,callbacks:{counter:0},getTestability:getTestability,$$minErr:minErr,$$csp:csp,reloadWithDebugInfo:reloadWithDebugInfo,$$hasClass:jqLiteHasClass}),angularModule=setupModuleLoader(window);try{angularModule("ngLocale")}catch(e){angularModule("ngLocale",[]).provider("$locale",$LocaleProvider)}angularModule("ng",["ngLocale"],["$provide",function($provide){$provide.provider({$$sanitizeUri:$$SanitizeUriProvider}),$provide.provider("$compile",$CompileProvider).directive({a:htmlAnchorDirective,input:inputDirective,textarea:inputDirective,form:formDirective,script:scriptDirective,select:selectDirective,style:styleDirective,option:optionDirective,ngBind:ngBindDirective,ngBindHtml:ngBindHtmlDirective,ngBindTemplate:ngBindTemplateDirective,ngClass:ngClassDirective,ngClassEven:ngClassEvenDirective,ngClassOdd:ngClassOddDirective,ngCloak:ngCloakDirective,ngController:ngControllerDirective,ngForm:ngFormDirective,ngHide:ngHideDirective,ngIf:ngIfDirective,ngInclude:ngIncludeDirective,ngInit:ngInitDirective,ngNonBindable:ngNonBindableDirective,ngPluralize:ngPluralizeDirective,ngRepeat:ngRepeatDirective,ngShow:ngShowDirective,ngStyle:ngStyleDirective,ngSwitch:ngSwitchDirective,ngSwitchWhen:ngSwitchWhenDirective,ngSwitchDefault:ngSwitchDefaultDirective,ngOptions:ngOptionsDirective,ngTransclude:ngTranscludeDirective,ngModel:ngModelDirective,ngList:ngListDirective,ngChange:ngChangeDirective,pattern:patternDirective,ngPattern:patternDirective,required:requiredDirective,ngRequired:requiredDirective,minlength:minlengthDirective,ngMinlength:minlengthDirective,maxlength:maxlengthDirective,ngMaxlength:maxlengthDirective,ngValue:ngValueDirective,ngModelOptions:ngModelOptionsDirective}).directive({ngInclude:ngIncludeFillContentDirective}).directive(ngAttributeAliasDirectives).directive(ngEventDirectives),$provide.provider({$anchorScroll:$AnchorScrollProvider,$animate:$AnimateProvider,$browser:$BrowserProvider,$cacheFactory:$CacheFactoryProvider,$controller:$ControllerProvider,$document:$DocumentProvider,$exceptionHandler:$ExceptionHandlerProvider,$filter:$FilterProvider,$interpolate:$InterpolateProvider,$interval:$IntervalProvider,$http:$HttpProvider,$httpBackend:$HttpBackendProvider,$location:$LocationProvider,$log:$LogProvider,$parse:$ParseProvider,$rootScope:$RootScopeProvider,$q:$QProvider,$$q:$$QProvider,$sce:$SceProvider,$sceDelegate:$SceDelegateProvider,$sniffer:$SnifferProvider,$templateCache:$TemplateCacheProvider,$templateRequest:$TemplateRequestProvider,$$testability:$$TestabilityProvider,$timeout:$TimeoutProvider,$window:$WindowProvider,$$rAF:$$RAFProvider,$$asyncCallback:$$AsyncCallbackProvider})}])}function jqNextId(){return++jqId}function camelCase(name){return name.replace(SPECIAL_CHARS_REGEXP,function(_,separator,letter,offset){return offset?letter.toUpperCase():letter}).replace(MOZ_HACK_REGEXP,"Moz$1")}function jqLiteIsTextNode(html){return!HTML_REGEXP.test(html)}function jqLiteAcceptsData(node){var nodeType=node.nodeType;return 1===nodeType||!nodeType||9===nodeType}function jqLiteBuildFragment(html,context){var tmp,tag,wrap,i,fragment=context.createDocumentFragment(),nodes=[];if(jqLiteIsTextNode(html))nodes.push(context.createTextNode(html));else{for(tmp=tmp||fragment.appendChild(context.createElement("div")),tag=(TAG_NAME_REGEXP.exec(html)||["",""])[1].toLowerCase(),wrap=wrapMap[tag]||wrapMap._default,tmp.innerHTML=wrap[1]+html.replace(XHTML_TAG_REGEXP,"<$1></$2>")+wrap[2],i=wrap[0];i--;)tmp=tmp.lastChild;nodes=concat(nodes,tmp.childNodes),tmp=fragment.firstChild,tmp.textContent=""}return fragment.textContent="",fragment.innerHTML="",forEach(nodes,function(node){fragment.appendChild(node)}),fragment}function jqLiteParseHTML(html,context){context=context||document;var parsed;return(parsed=SINGLE_TAG_REGEXP.exec(html))?[context.createElement(parsed[1])]:(parsed=jqLiteBuildFragment(html,context))?parsed.childNodes:[]}function JQLite(element){if(element instanceof JQLite)return element;
var argIsString;if(isString(element)&&(element=trim(element),argIsString=!0),!(this instanceof JQLite)){if(argIsString&&"<"!=element.charAt(0))throw jqLiteMinErr("nosel","Looking up elements via selectors is not supported by jqLite! See: http://docs.angularjs.org/api/angular.element");return new JQLite(element)}argIsString?jqLiteAddNodes(this,jqLiteParseHTML(element)):jqLiteAddNodes(this,element)}function jqLiteClone(element){return element.cloneNode(!0)}function jqLiteDealoc(element,onlyDescendants){if(onlyDescendants||jqLiteRemoveData(element),element.querySelectorAll)for(var descendants=element.querySelectorAll("*"),i=0,l=descendants.length;l>i;i++)jqLiteRemoveData(descendants[i])}function jqLiteOff(element,type,fn,unsupported){if(isDefined(unsupported))throw jqLiteMinErr("offargs","jqLite#off() does not support the `selector` argument");var expandoStore=jqLiteExpandoStore(element),events=expandoStore&&expandoStore.events,handle=expandoStore&&expandoStore.handle;if(handle)if(type)forEach(type.split(" "),function(type){isUndefined(fn)?(removeEventListenerFn(element,type,events[type]),delete events[type]):arrayRemove(events[type]||[],fn)});else for(type in events)"$destroy"!==type&&removeEventListenerFn(element,type,events[type]),delete events[type]}function jqLiteRemoveData(element,name){var expandoId=element.ng339,expandoStore=expandoId&&jqCache[expandoId];if(expandoStore){if(name)return void delete expandoStore.data[name];expandoStore.handle&&(expandoStore.events.$destroy&&expandoStore.handle({},"$destroy"),jqLiteOff(element)),delete jqCache[expandoId],element.ng339=undefined}}function jqLiteExpandoStore(element,createIfNecessary){var expandoId=element.ng339,expandoStore=expandoId&&jqCache[expandoId];return createIfNecessary&&!expandoStore&&(element.ng339=expandoId=jqNextId(),expandoStore=jqCache[expandoId]={events:{},data:{},handle:undefined}),expandoStore}function jqLiteData(element,key,value){if(jqLiteAcceptsData(element)){var isSimpleSetter=isDefined(value),isSimpleGetter=!isSimpleSetter&&key&&!isObject(key),massGetter=!key,expandoStore=jqLiteExpandoStore(element,!isSimpleGetter),data=expandoStore&&expandoStore.data;if(isSimpleSetter)data[key]=value;else{if(massGetter)return data;if(isSimpleGetter)return data&&data[key];extend(data,key)}}}function jqLiteHasClass(element,selector){return element.getAttribute?(" "+(element.getAttribute("class")||"")+" ").replace(/[\n\t]/g," ").indexOf(" "+selector+" ")>-1:!1}function jqLiteRemoveClass(element,cssClasses){cssClasses&&element.setAttribute&&forEach(cssClasses.split(" "),function(cssClass){element.setAttribute("class",trim((" "+(element.getAttribute("class")||"")+" ").replace(/[\n\t]/g," ").replace(" "+trim(cssClass)+" "," ")))})}function jqLiteAddClass(element,cssClasses){if(cssClasses&&element.setAttribute){var existingClasses=(" "+(element.getAttribute("class")||"")+" ").replace(/[\n\t]/g," ");forEach(cssClasses.split(" "),function(cssClass){cssClass=trim(cssClass),-1===existingClasses.indexOf(" "+cssClass+" ")&&(existingClasses+=cssClass+" ")}),element.setAttribute("class",trim(existingClasses))}}function jqLiteAddNodes(root,elements){if(elements)if(elements.nodeType)root[root.length++]=elements;else{var length=elements.length;if("number"==typeof length&&elements.window!==elements){if(length)for(var i=0;length>i;i++)root[root.length++]=elements[i]}else root[root.length++]=elements}}function jqLiteController(element,name){return jqLiteInheritedData(element,"$"+(name||"ngController")+"Controller")}function jqLiteInheritedData(element,name,value){9==element.nodeType&&(element=element.documentElement);for(var names=isArray(name)?name:[name];element;){for(var i=0,ii=names.length;ii>i;i++)if((value=jqLite.data(element,names[i]))!==undefined)return value;element=element.parentNode||11===element.nodeType&&element.host}}function jqLiteEmpty(element){for(jqLiteDealoc(element,!0);element.firstChild;)element.removeChild(element.firstChild)}function jqLiteRemove(element,keepData){keepData||jqLiteDealoc(element);var parent=element.parentNode;parent&&parent.removeChild(element)}function getBooleanAttrName(element,name){var booleanAttr=BOOLEAN_ATTR[name.toLowerCase()];return booleanAttr&&BOOLEAN_ELEMENTS[nodeName_(element)]&&booleanAttr}function getAliasedAttrName(element,name){var nodeName=element.nodeName;return("INPUT"===nodeName||"TEXTAREA"===nodeName)&&ALIASED_ATTR[name]}function createEventHandler(element,events){var eventHandler=function(event,type){event.isDefaultPrevented=function(){return event.defaultPrevented};var eventFns=events[type||event.type],eventFnsLength=eventFns?eventFns.length:0;if(eventFnsLength){if(isUndefined(event.immediatePropagationStopped)){var originalStopImmediatePropagation=event.stopImmediatePropagation;event.stopImmediatePropagation=function(){event.immediatePropagationStopped=!0,event.stopPropagation&&event.stopPropagation(),originalStopImmediatePropagation&&originalStopImmediatePropagation.call(event)}}event.isImmediatePropagationStopped=function(){return event.immediatePropagationStopped===!0},eventFnsLength>1&&(eventFns=shallowCopy(eventFns));for(var i=0;eventFnsLength>i;i++)event.isImmediatePropagationStopped()||eventFns[i].call(element,event)}};return eventHandler.elem=element,eventHandler}function hashKey(obj,nextUidFn){var key=obj&&obj.$$hashKey;if(key)return"function"==typeof key&&(key=obj.$$hashKey()),key;var objType=typeof obj;return key="function"==objType||"object"==objType&&null!==obj?obj.$$hashKey=objType+":"+(nextUidFn||nextUid)():objType+":"+obj}function HashMap(array,isolatedUid){if(isolatedUid){var uid=0;this.nextUid=function(){return++uid}}forEach(array,this.put,this)}function anonFn(fn){var fnText=fn.toString().replace(STRIP_COMMENTS,""),args=fnText.match(FN_ARGS);return args?"function("+(args[1]||"").replace(/[\s\r\n]+/," ")+")":"fn"}function annotate(fn,strictDi,name){var $inject,fnText,argDecl,last;if("function"==typeof fn){if(!($inject=fn.$inject)){if($inject=[],fn.length){if(strictDi)throw isString(name)&&name||(name=fn.name||anonFn(fn)),$injectorMinErr("strictdi","{0} is not using explicit annotation and cannot be invoked in strict mode",name);fnText=fn.toString().replace(STRIP_COMMENTS,""),argDecl=fnText.match(FN_ARGS),forEach(argDecl[1].split(FN_ARG_SPLIT),function(arg){arg.replace(FN_ARG,function(all,underscore,name){$inject.push(name)})})}fn.$inject=$inject}}else isArray(fn)?(last=fn.length-1,assertArgFn(fn[last],"fn"),$inject=fn.slice(0,last)):assertArgFn(fn,"fn",!0);return $inject}function createInjector(modulesToLoad,strictDi){function supportObject(delegate){return function(key,value){return isObject(key)?void forEach(key,reverseParams(delegate)):delegate(key,value)}}function provider(name,provider_){if(assertNotHasOwnProperty(name,"service"),(isFunction(provider_)||isArray(provider_))&&(provider_=providerInjector.instantiate(provider_)),!provider_.$get)throw $injectorMinErr("pget","Provider '{0}' must define $get factory method.",name);return providerCache[name+providerSuffix]=provider_}function factory(name,factoryFn){return provider(name,{$get:factoryFn})}function service(name,constructor){return factory(name,["$injector",function($injector){return $injector.instantiate(constructor)}])}function value(name,val){return factory(name,valueFn(val))}function constant(name,value){assertNotHasOwnProperty(name,"constant"),providerCache[name]=value,instanceCache[name]=value}function decorator(serviceName,decorFn){var origProvider=providerInjector.get(serviceName+providerSuffix),orig$get=origProvider.$get;origProvider.$get=function(){var origInstance=instanceInjector.invoke(orig$get,origProvider);return instanceInjector.invoke(decorFn,null,{$delegate:origInstance})}}function loadModules(modulesToLoad){var moduleFn,runBlocks=[];return forEach(modulesToLoad,function(module){function runInvokeQueue(queue){var i,ii;for(i=0,ii=queue.length;ii>i;i++){var invokeArgs=queue[i],provider=providerInjector.get(invokeArgs[0]);provider[invokeArgs[1]].apply(provider,invokeArgs[2])}}if(!loadedModules.get(module)){loadedModules.put(module,!0);try{isString(module)?(moduleFn=angularModule(module),runBlocks=runBlocks.concat(loadModules(moduleFn.requires)).concat(moduleFn._runBlocks),runInvokeQueue(moduleFn._invokeQueue),runInvokeQueue(moduleFn._configBlocks)):isFunction(module)?runBlocks.push(providerInjector.invoke(module)):isArray(module)?runBlocks.push(providerInjector.invoke(module)):assertArgFn(module,"module")}catch(e){throw isArray(module)&&(module=module[module.length-1]),e.message&&e.stack&&-1==e.stack.indexOf(e.message)&&(e=e.message+"\n"+e.stack),$injectorMinErr("modulerr","Failed to instantiate module {0} due to:\n{1}",module,e.stack||e.message||e)}}}),runBlocks}function createInternalInjector(cache,factory){function getService(serviceName){if(cache.hasOwnProperty(serviceName)){if(cache[serviceName]===INSTANTIATING)throw $injectorMinErr("cdep","Circular dependency found: {0}",serviceName+" <- "+path.join(" <- "));return cache[serviceName]}try{return path.unshift(serviceName),cache[serviceName]=INSTANTIATING,cache[serviceName]=factory(serviceName)}catch(err){throw cache[serviceName]===INSTANTIATING&&delete cache[serviceName],err}finally{path.shift()}}function invoke(fn,self,locals,serviceName){"string"==typeof locals&&(serviceName=locals,locals=null);var length,i,key,args=[],$inject=annotate(fn,strictDi,serviceName);for(i=0,length=$inject.length;length>i;i++){if(key=$inject[i],"string"!=typeof key)throw $injectorMinErr("itkn","Incorrect injection token! Expected service name as string, got {0}",key);args.push(locals&&locals.hasOwnProperty(key)?locals[key]:getService(key))}return isArray(fn)&&(fn=fn[length]),fn.apply(self,args)}function instantiate(Type,locals,serviceName){var instance,returnedValue,Constructor=function(){};return Constructor.prototype=(isArray(Type)?Type[Type.length-1]:Type).prototype,instance=new Constructor,returnedValue=invoke(Type,instance,locals,serviceName),isObject(returnedValue)||isFunction(returnedValue)?returnedValue:instance}return{invoke:invoke,instantiate:instantiate,get:getService,annotate:annotate,has:function(name){return providerCache.hasOwnProperty(name+providerSuffix)||cache.hasOwnProperty(name)}}}strictDi=strictDi===!0;var INSTANTIATING={},providerSuffix="Provider",path=[],loadedModules=new HashMap([],!0),providerCache={$provide:{provider:supportObject(provider),factory:supportObject(factory),service:supportObject(service),value:supportObject(value),constant:supportObject(constant),decorator:decorator}},providerInjector=providerCache.$injector=createInternalInjector(providerCache,function(){throw $injectorMinErr("unpr","Unknown provider: {0}",path.join(" <- "))}),instanceCache={},instanceInjector=instanceCache.$injector=createInternalInjector(instanceCache,function(servicename){var provider=providerInjector.get(servicename+providerSuffix);return instanceInjector.invoke(provider.$get,provider,undefined,servicename)});return forEach(loadModules(modulesToLoad),function(fn){instanceInjector.invoke(fn||noop)}),instanceInjector}function $AnchorScrollProvider(){var autoScrollingEnabled=!0;this.disableAutoScrolling=function(){autoScrollingEnabled=!1},this.$get=["$window","$location","$rootScope",function($window,$location,$rootScope){function getFirstAnchor(list){var result=null;return forEach(list,function(element){result||"a"!==nodeName_(element)||(result=element)}),result}function scroll(){var elm,hash=$location.hash();hash?(elm=document.getElementById(hash))?elm.scrollIntoView():(elm=getFirstAnchor(document.getElementsByName(hash)))?elm.scrollIntoView():"top"===hash&&$window.scrollTo(0,0):$window.scrollTo(0,0)}var document=$window.document;return autoScrollingEnabled&&$rootScope.$watch(function(){return $location.hash()},function(){$rootScope.$evalAsync(scroll)}),scroll}]}function $$AsyncCallbackProvider(){this.$get=["$$rAF","$timeout",function($$rAF,$timeout){return $$rAF.supported?function(fn){return $$rAF(fn)}:function(fn){return $timeout(fn,0,!1)}}]}function Browser(window,document,$log,$sniffer){function completeOutstandingRequest(fn){try{fn.apply(null,sliceArgs(arguments,1))}finally{if(outstandingRequestCount--,0===outstandingRequestCount)for(;outstandingRequestCallbacks.length;)try{outstandingRequestCallbacks.pop()()}catch(e){$log.error(e)}}}function startPoller(interval,setTimeout){!function check(){forEach(pollFns,function(pollFn){pollFn()}),pollTimeout=setTimeout(check,interval)}()}function fireUrlChange(){newLocation=null,lastBrowserUrl!=self.url()&&(lastBrowserUrl=self.url(),forEach(urlChangeListeners,function(listener){listener(self.url())}))}var self=this,rawDocument=document[0],location=window.location,history=window.history,setTimeout=window.setTimeout,clearTimeout=window.clearTimeout,pendingDeferIds={};self.isMock=!1;var outstandingRequestCount=0,outstandingRequestCallbacks=[];self.$$completeOutstandingRequest=completeOutstandingRequest,self.$$incOutstandingRequestCount=function(){outstandingRequestCount++},self.notifyWhenNoOutstandingRequests=function(callback){forEach(pollFns,function(pollFn){pollFn()}),0===outstandingRequestCount?callback():outstandingRequestCallbacks.push(callback)};var pollTimeout,pollFns=[];self.addPollFn=function(fn){return isUndefined(pollTimeout)&&startPoller(100,setTimeout),pollFns.push(fn),fn};var lastBrowserUrl=location.href,baseElement=document.find("base"),newLocation=null;self.url=function(url,replace){if(location!==window.location&&(location=window.location),history!==window.history&&(history=window.history),url){if(lastBrowserUrl==url)return;return lastBrowserUrl=url,$sniffer.history?replace?history.replaceState(null,"",url):history.pushState(null,"",url):(newLocation=url,replace?location.replace(url):location.href=url),self}return newLocation||location.href.replace(/%27/g,"'")};var urlChangeListeners=[],urlChangeInit=!1;self.onUrlChange=function(callback){return urlChangeInit||($sniffer.history&&jqLite(window).on("popstate",fireUrlChange),$sniffer.hashchange?jqLite(window).on("hashchange",fireUrlChange):self.addPollFn(fireUrlChange),urlChangeInit=!0),urlChangeListeners.push(callback),callback},self.$$checkUrlChange=fireUrlChange,self.baseHref=function(){var href=baseElement.attr("href");return href?href.replace(/^(https?\:)?\/\/[^\/]*/,""):""};var lastCookies={},lastCookieString="",cookiePath=self.baseHref();self.cookies=function(name,value){var cookieLength,cookieArray,cookie,i,index;if(!name){if(rawDocument.cookie!==lastCookieString)for(lastCookieString=rawDocument.cookie,cookieArray=lastCookieString.split("; "),lastCookies={},i=0;i<cookieArray.length;i++)cookie=cookieArray[i],index=cookie.indexOf("="),index>0&&(name=decodeURIComponent(cookie.substring(0,index)),lastCookies[name]===undefined&&(lastCookies[name]=decodeURIComponent(cookie.substring(index+1))));return lastCookies}value===undefined?rawDocument.cookie=encodeURIComponent(name)+"=;path="+cookiePath+";expires=Thu, 01 Jan 1970 00:00:00 GMT":isString(value)&&(cookieLength=(rawDocument.cookie=encodeURIComponent(name)+"="+encodeURIComponent(value)+";path="+cookiePath).length+1,cookieLength>4096&&$log.warn("Cookie '"+name+"' possibly not set or overflowed because it was too large ("+cookieLength+" > 4096 bytes)!"))},self.defer=function(fn,delay){var timeoutId;return outstandingRequestCount++,timeoutId=setTimeout(function(){delete pendingDeferIds[timeoutId],completeOutstandingRequest(fn)},delay||0),pendingDeferIds[timeoutId]=!0,timeoutId},self.defer.cancel=function(deferId){return pendingDeferIds[deferId]?(delete pendingDeferIds[deferId],clearTimeout(deferId),completeOutstandingRequest(noop),!0):!1}}function $BrowserProvider(){this.$get=["$window","$log","$sniffer","$document",function($window,$log,$sniffer,$document){return new Browser($window,$document,$log,$sniffer)}]}function $CacheFactoryProvider(){this.$get=function(){function cacheFactory(cacheId,options){function refresh(entry){entry!=freshEnd&&(staleEnd?staleEnd==entry&&(staleEnd=entry.n):staleEnd=entry,link(entry.n,entry.p),link(entry,freshEnd),freshEnd=entry,freshEnd.n=null)}function link(nextEntry,prevEntry){nextEntry!=prevEntry&&(nextEntry&&(nextEntry.p=prevEntry),prevEntry&&(prevEntry.n=nextEntry))}if(cacheId in caches)throw minErr("$cacheFactory")("iid","CacheId '{0}' is already taken!",cacheId);var size=0,stats=extend({},options,{id:cacheId}),data={},capacity=options&&options.capacity||Number.MAX_VALUE,lruHash={},freshEnd=null,staleEnd=null;return caches[cacheId]={put:function(key,value){if(capacity<Number.MAX_VALUE){var lruEntry=lruHash[key]||(lruHash[key]={key:key});refresh(lruEntry)}if(!isUndefined(value))return key in data||size++,data[key]=value,size>capacity&&this.remove(staleEnd.key),value},get:function(key){if(capacity<Number.MAX_VALUE){var lruEntry=lruHash[key];if(!lruEntry)return;refresh(lruEntry)}return data[key]},remove:function(key){if(capacity<Number.MAX_VALUE){var lruEntry=lruHash[key];if(!lruEntry)return;lruEntry==freshEnd&&(freshEnd=lruEntry.p),lruEntry==staleEnd&&(staleEnd=lruEntry.n),link(lruEntry.n,lruEntry.p),delete lruHash[key]}delete data[key],size--},removeAll:function(){data={},size=0,lruHash={},freshEnd=staleEnd=null},destroy:function(){data=null,stats=null,lruHash=null,delete caches[cacheId]},info:function(){return extend({},stats,{size:size})}}}var caches={};return cacheFactory.info=function(){var info={};return forEach(caches,function(cache,cacheId){info[cacheId]=cache.info()}),info},cacheFactory.get=function(cacheId){return caches[cacheId]},cacheFactory}}function $TemplateCacheProvider(){this.$get=["$cacheFactory",function($cacheFactory){return $cacheFactory("templates")}]}function $CompileProvider($provide,$$sanitizeUriProvider){function parseIsolateBindings(scope,directiveName){var LOCAL_REGEXP=/^\s*([@=&])(\??)\s*(\w*)\s*$/,bindings={};return forEach(scope,function(definition,scopeName){var match=definition.match(LOCAL_REGEXP);if(!match)throw $compileMinErr("iscp","Invalid isolate scope definition for directive '{0}'. Definition: {... {1}: '{2}' ...}",directiveName,scopeName,definition);bindings[scopeName]={attrName:match[3]||scopeName,mode:match[1],optional:"?"===match[2]}}),bindings}var hasDirectives={},Suffix="Directive",COMMENT_DIRECTIVE_REGEXP=/^\s*directive\:\s*([\d\w_\-]+)\s+(.*)$/,CLASS_DIRECTIVE_REGEXP=/(([\d\w_\-]+)(?:\:([^;]+))?;?)/,ALL_OR_NOTHING_ATTRS=makeMap("ngSrc,ngSrcset,src,srcset"),REQUIRE_PREFIX_REGEXP=/^(?:(\^\^?)?(\?)?(\^\^?)?)?/,EVENT_HANDLER_ATTR_REGEXP=/^(on[a-z]+|formaction)$/;this.directive=function registerDirective(name,directiveFactory){return assertNotHasOwnProperty(name,"directive"),isString(name)?(assertArg(directiveFactory,"directiveFactory"),hasDirectives.hasOwnProperty(name)||(hasDirectives[name]=[],$provide.factory(name+Suffix,["$injector","$exceptionHandler",function($injector,$exceptionHandler){var directives=[];return forEach(hasDirectives[name],function(directiveFactory,index){try{var directive=$injector.invoke(directiveFactory);isFunction(directive)?directive={compile:valueFn(directive)}:!directive.compile&&directive.link&&(directive.compile=valueFn(directive.link)),directive.priority=directive.priority||0,directive.index=index,directive.name=directive.name||name,directive.require=directive.require||directive.controller&&directive.name,directive.restrict=directive.restrict||"EA",isObject(directive.scope)&&(directive.$$isolateBindings=parseIsolateBindings(directive.scope,directive.name)),directives.push(directive)}catch(e){$exceptionHandler(e)}}),directives}])),hasDirectives[name].push(directiveFactory)):forEach(name,reverseParams(registerDirective)),this},this.aHrefSanitizationWhitelist=function(regexp){return isDefined(regexp)?($$sanitizeUriProvider.aHrefSanitizationWhitelist(regexp),this):$$sanitizeUriProvider.aHrefSanitizationWhitelist()},this.imgSrcSanitizationWhitelist=function(regexp){return isDefined(regexp)?($$sanitizeUriProvider.imgSrcSanitizationWhitelist(regexp),this):$$sanitizeUriProvider.imgSrcSanitizationWhitelist()};var debugInfoEnabled=!0;this.debugInfoEnabled=function(enabled){return isDefined(enabled)?(debugInfoEnabled=enabled,this):debugInfoEnabled},this.$get=["$injector","$interpolate","$exceptionHandler","$templateRequest","$parse","$controller","$rootScope","$document","$sce","$animate","$$sanitizeUri",function($injector,$interpolate,$exceptionHandler,$templateRequest,$parse,$controller,$rootScope,$document,$sce,$animate,$$sanitizeUri){function safeAddClass($element,className){try{$element.addClass(className)}catch(e){}}function compile($compileNodes,transcludeFn,maxPriority,ignoreDirective,previousCompileContext){$compileNodes instanceof jqLite||($compileNodes=jqLite($compileNodes)),forEach($compileNodes,function(node,index){3==node.nodeType&&node.nodeValue.match(/\S+/)&&($compileNodes[index]=jqLite(node).wrap("<span></span>").parent()[0])});var compositeLinkFn=compileNodes($compileNodes,transcludeFn,$compileNodes,maxPriority,ignoreDirective,previousCompileContext);compile.$$addScopeClass($compileNodes);var lastCompileNode,namespace=null,namespaceAdaptedCompileNodes=$compileNodes;return function(scope,cloneConnectFn,transcludeControllers,parentBoundTranscludeFn,futureParentElement){assertArg(scope,"scope"),namespace||(namespace=detectNamespaceForChildElements(futureParentElement)),"html"!==namespace&&$compileNodes[0]!==lastCompileNode&&(namespaceAdaptedCompileNodes=jqLite(wrapTemplate(namespace,jqLite("<div>").append($compileNodes).html()))),lastCompileNode=$compileNodes[0];var $linkNode=cloneConnectFn?JQLitePrototype.clone.call(namespaceAdaptedCompileNodes):namespaceAdaptedCompileNodes;if(transcludeControllers)for(var controllerName in transcludeControllers)$linkNode.data("$"+controllerName+"Controller",transcludeControllers[controllerName].instance);return compile.$$addScopeInfo($linkNode,scope),cloneConnectFn&&cloneConnectFn($linkNode,scope),compositeLinkFn&&compositeLinkFn(scope,$linkNode,$linkNode,parentBoundTranscludeFn),$linkNode}}function detectNamespaceForChildElements(parentElement){var node=parentElement&&parentElement[0];return node&&"foreignobject"!==nodeName_(node)&&node.toString().match(/SVG/)?"svg":"html"}function compileNodes(nodeList,transcludeFn,$rootElement,maxPriority,ignoreDirective,previousCompileContext){function compositeLinkFn(scope,nodeList,$rootElement,parentBoundTranscludeFn){var nodeLinkFn,childLinkFn,node,childScope,i,ii,idx,childBoundTranscludeFn,stableNodeList;if(nodeLinkFnFound){var nodeListLength=nodeList.length;for(stableNodeList=new Array(nodeListLength),i=0;i<linkFns.length;i+=3)idx=linkFns[i],stableNodeList[idx]=nodeList[idx]}else stableNodeList=nodeList;for(i=0,ii=linkFns.length;ii>i;)node=stableNodeList[linkFns[i++]],nodeLinkFn=linkFns[i++],childLinkFn=linkFns[i++],nodeLinkFn?(nodeLinkFn.scope?(childScope=scope.$new(),compile.$$addScopeInfo(jqLite(node),childScope)):childScope=scope,childBoundTranscludeFn=nodeLinkFn.transcludeOnThisElement?createBoundTranscludeFn(scope,nodeLinkFn.transclude,parentBoundTranscludeFn,nodeLinkFn.elementTranscludeOnThisElement):!nodeLinkFn.templateOnThisElement&&parentBoundTranscludeFn?parentBoundTranscludeFn:!parentBoundTranscludeFn&&transcludeFn?createBoundTranscludeFn(scope,transcludeFn):null,nodeLinkFn(childLinkFn,childScope,node,$rootElement,childBoundTranscludeFn)):childLinkFn&&childLinkFn(scope,node.childNodes,undefined,parentBoundTranscludeFn)}for(var attrs,directives,nodeLinkFn,childNodes,childLinkFn,linkFnFound,nodeLinkFnFound,linkFns=[],i=0;i<nodeList.length;i++)attrs=new Attributes,directives=collectDirectives(nodeList[i],[],attrs,0===i?maxPriority:undefined,ignoreDirective),nodeLinkFn=directives.length?applyDirectivesToNode(directives,nodeList[i],attrs,transcludeFn,$rootElement,null,[],[],previousCompileContext):null,nodeLinkFn&&nodeLinkFn.scope&&compile.$$addScopeClass(attrs.$$element),childLinkFn=nodeLinkFn&&nodeLinkFn.terminal||!(childNodes=nodeList[i].childNodes)||!childNodes.length?null:compileNodes(childNodes,nodeLinkFn?(nodeLinkFn.transcludeOnThisElement||!nodeLinkFn.templateOnThisElement)&&nodeLinkFn.transclude:transcludeFn),(nodeLinkFn||childLinkFn)&&(linkFns.push(i,nodeLinkFn,childLinkFn),linkFnFound=!0,nodeLinkFnFound=nodeLinkFnFound||nodeLinkFn),previousCompileContext=null;return linkFnFound?compositeLinkFn:null}function createBoundTranscludeFn(scope,transcludeFn,previousBoundTranscludeFn){var boundTranscludeFn=function(transcludedScope,cloneFn,controllers,futureParentElement,containingScope){return transcludedScope||(transcludedScope=scope.$new(!1,containingScope),transcludedScope.$$transcluded=!0),transcludeFn(transcludedScope,cloneFn,controllers,previousBoundTranscludeFn,futureParentElement)};return boundTranscludeFn}function collectDirectives(node,directives,attrs,maxPriority,ignoreDirective){var match,className,nodeType=node.nodeType,attrsMap=attrs.$attr;switch(nodeType){case 1:addDirective(directives,directiveNormalize(nodeName_(node)),"E",maxPriority,ignoreDirective);for(var attr,name,nName,ngAttrName,value,isNgAttr,nAttrs=node.attributes,j=0,jj=nAttrs&&nAttrs.length;jj>j;j++){var attrStartName=!1,attrEndName=!1;if(attr=nAttrs[j],!msie||msie>=8||attr.specified){name=attr.name,value=trim(attr.value),ngAttrName=directiveNormalize(name),(isNgAttr=NG_ATTR_BINDING.test(ngAttrName))&&(name=snake_case(ngAttrName.substr(6),"-"));var directiveNName=ngAttrName.replace(/(Start|End)$/,"");directiveIsMultiElement(directiveNName)&&ngAttrName===directiveNName+"Start"&&(attrStartName=name,attrEndName=name.substr(0,name.length-5)+"end",name=name.substr(0,name.length-6)),nName=directiveNormalize(name.toLowerCase()),attrsMap[nName]=name,(isNgAttr||!attrs.hasOwnProperty(nName))&&(attrs[nName]=value,getBooleanAttrName(node,nName)&&(attrs[nName]=!0)),addAttrInterpolateDirective(node,directives,value,nName,isNgAttr),addDirective(directives,nName,"A",maxPriority,ignoreDirective,attrStartName,attrEndName)}}if(className=node.className,isString(className)&&""!==className)for(;match=CLASS_DIRECTIVE_REGEXP.exec(className);)nName=directiveNormalize(match[2]),addDirective(directives,nName,"C",maxPriority,ignoreDirective)&&(attrs[nName]=trim(match[3])),className=className.substr(match.index+match[0].length);break;case 3:addTextInterpolateDirective(directives,node.nodeValue);break;case 8:try{match=COMMENT_DIRECTIVE_REGEXP.exec(node.nodeValue),match&&(nName=directiveNormalize(match[1]),addDirective(directives,nName,"M",maxPriority,ignoreDirective)&&(attrs[nName]=trim(match[2])))}catch(e){}}return directives.sort(byPriority),directives}function groupScan(node,attrStart,attrEnd){var nodes=[],depth=0;if(attrStart&&node.hasAttribute&&node.hasAttribute(attrStart)){do{if(!node)throw $compileMinErr("uterdir","Unterminated attribute, found '{0}' but no matching '{1}' found.",attrStart,attrEnd);1==node.nodeType&&(node.hasAttribute(attrStart)&&depth++,node.hasAttribute(attrEnd)&&depth--),nodes.push(node),node=node.nextSibling}while(depth>0)}else nodes.push(node);return jqLite(nodes)}function groupElementsLinkFnWrapper(linkFn,attrStart,attrEnd){return function(scope,element,attrs,controllers,transcludeFn){return element=groupScan(element[0],attrStart,attrEnd),linkFn(scope,element,attrs,controllers,transcludeFn)}}function applyDirectivesToNode(directives,compileNode,templateAttrs,transcludeFn,jqCollection,originalReplaceDirective,preLinkFns,postLinkFns,previousCompileContext){function addLinkFns(pre,post,attrStart,attrEnd){pre&&(attrStart&&(pre=groupElementsLinkFnWrapper(pre,attrStart,attrEnd)),pre.require=directive.require,pre.directiveName=directiveName,(newIsolateScopeDirective===directive||directive.$$isolateScope)&&(pre=cloneAndAnnotateFn(pre,{isolateScope:!0})),preLinkFns.push(pre)),post&&(attrStart&&(post=groupElementsLinkFnWrapper(post,attrStart,attrEnd)),post.require=directive.require,post.directiveName=directiveName,(newIsolateScopeDirective===directive||directive.$$isolateScope)&&(post=cloneAndAnnotateFn(post,{isolateScope:!0})),postLinkFns.push(post))}function getControllers(directiveName,require,$element,elementControllers){var value,match,retrievalMethod="data",optional=!1,$searchElement=$element;if(isString(require)){if(match=require.match(REQUIRE_PREFIX_REGEXP),require=require.substring(match[0].length),match[3]&&(match[1]?match[3]=null:match[1]=match[3]),"^"===match[1]?retrievalMethod="inheritedData":"^^"===match[1]&&(retrievalMethod="inheritedData",$searchElement=$element.parent()),"?"===match[2]&&(optional=!0),value=null,elementControllers&&"data"===retrievalMethod&&(value=elementControllers[require])&&(value=value.instance),value=value||$searchElement[retrievalMethod]("$"+require+"Controller"),!value&&!optional)throw $compileMinErr("ctreq","Controller '{0}', required by directive '{1}', can't be found!",require,directiveName);return value}return isArray(require)&&(value=[],forEach(require,function(require){value.push(getControllers(directiveName,require,$element,elementControllers))})),value}function nodeLinkFn(childLinkFn,scope,linkNode,$rootElement,boundTranscludeFn){function controllersBoundTransclude(scope,cloneAttachFn,futureParentElement){var transcludeControllers;return isScope(scope)||(futureParentElement=cloneAttachFn,cloneAttachFn=scope,scope=undefined),hasElementTranscludeDirective&&(transcludeControllers=elementControllers),futureParentElement||(futureParentElement=hasElementTranscludeDirective?$element.parent():$element),boundTranscludeFn(scope,cloneAttachFn,transcludeControllers,futureParentElement,scopeToChild)}var i,ii,linkFn,controller,isolateScope,elementControllers,transcludeFn,$element,attrs;if(compileNode===linkNode?(attrs=templateAttrs,$element=templateAttrs.$$element):($element=jqLite(linkNode),attrs=new Attributes($element,templateAttrs)),newIsolateScopeDirective&&(isolateScope=scope.$new(!0)),transcludeFn=boundTranscludeFn&&controllersBoundTransclude,controllerDirectives&&(controllers={},elementControllers={},forEach(controllerDirectives,function(directive){var controllerInstance,locals={$scope:directive===newIsolateScopeDirective||directive.$$isolateScope?isolateScope:scope,$element:$element,$attrs:attrs,$transclude:transcludeFn};controller=directive.controller,"@"==controller&&(controller=attrs[directive.name]),controllerInstance=$controller(controller,locals,!0,directive.controllerAs),elementControllers[directive.name]=controllerInstance,hasElementTranscludeDirective||$element.data("$"+directive.name+"Controller",controllerInstance.instance),controllers[directive.name]=controllerInstance})),newIsolateScopeDirective){compile.$$addScopeInfo($element,isolateScope,!0,!(templateDirective&&(templateDirective===newIsolateScopeDirective||templateDirective===newIsolateScopeDirective.$$originalDirective))),compile.$$addScopeClass($element,!0);var isolateScopeController=controllers&&controllers[newIsolateScopeDirective.name],isolateBindingContext=isolateScope;isolateScopeController&&isolateScopeController.identifier&&newIsolateScopeDirective.bindToController===!0&&(isolateBindingContext=isolateScopeController.instance),forEach(isolateScope.$$isolateBindings=newIsolateScopeDirective.$$isolateBindings,function(definition,scopeName){var lastValue,parentGet,parentSet,compare,attrName=definition.attrName,optional=definition.optional,mode=definition.mode;switch(mode){case"@":attrs.$observe(attrName,function(value){isolateBindingContext[scopeName]=value}),attrs.$$observers[attrName].$$scope=scope,attrs[attrName]&&(isolateBindingContext[scopeName]=$interpolate(attrs[attrName])(scope));break;case"=":if(optional&&!attrs[attrName])return;parentGet=$parse(attrs[attrName]),compare=parentGet.literal?equals:function(a,b){return a===b||a!==a&&b!==b},parentSet=parentGet.assign||function(){throw lastValue=isolateBindingContext[scopeName]=parentGet(scope),$compileMinErr("nonassign","Expression '{0}' used with directive '{1}' is non-assignable!",attrs[attrName],newIsolateScopeDirective.name)},lastValue=isolateBindingContext[scopeName]=parentGet(scope);var parentValueWatch=function(parentValue){return compare(parentValue,isolateBindingContext[scopeName])||(compare(parentValue,lastValue)?parentSet(scope,parentValue=isolateBindingContext[scopeName]):isolateBindingContext[scopeName]=parentValue),lastValue=parentValue
};parentValueWatch.$stateful=!0;var unwatch=scope.$watch($parse(attrs[attrName],parentValueWatch),null,parentGet.literal);isolateScope.$on("$destroy",unwatch);break;case"&":parentGet=$parse(attrs[attrName]),isolateBindingContext[scopeName]=function(locals){return parentGet(scope,locals)}}})}for(controllers&&(forEach(controllers,function(controller){controller()}),controllers=null),i=0,ii=preLinkFns.length;ii>i;i++)linkFn=preLinkFns[i],invokeLinkFn(linkFn,linkFn.isolateScope?isolateScope:scope,$element,attrs,linkFn.require&&getControllers(linkFn.directiveName,linkFn.require,$element,elementControllers),transcludeFn);var scopeToChild=scope;for(newIsolateScopeDirective&&(newIsolateScopeDirective.template||null===newIsolateScopeDirective.templateUrl)&&(scopeToChild=isolateScope),childLinkFn&&childLinkFn(scopeToChild,linkNode.childNodes,undefined,boundTranscludeFn),i=postLinkFns.length-1;i>=0;i--)linkFn=postLinkFns[i],invokeLinkFn(linkFn,linkFn.isolateScope?isolateScope:scope,$element,attrs,linkFn.require&&getControllers(linkFn.directiveName,linkFn.require,$element,elementControllers),transcludeFn)}previousCompileContext=previousCompileContext||{};for(var newScopeDirective,controllers,directive,directiveName,$template,linkFn,directiveValue,terminalPriority=-Number.MAX_VALUE,controllerDirectives=previousCompileContext.controllerDirectives,newIsolateScopeDirective=previousCompileContext.newIsolateScopeDirective,templateDirective=previousCompileContext.templateDirective,nonTlbTranscludeDirective=previousCompileContext.nonTlbTranscludeDirective,hasTranscludeDirective=!1,hasTemplate=!1,hasElementTranscludeDirective=previousCompileContext.hasElementTranscludeDirective,$compileNode=templateAttrs.$$element=jqLite(compileNode),replaceDirective=originalReplaceDirective,childTranscludeFn=transcludeFn,i=0,ii=directives.length;ii>i;i++){directive=directives[i];var attrStart=directive.$$start,attrEnd=directive.$$end;if(attrStart&&($compileNode=groupScan(compileNode,attrStart,attrEnd)),$template=undefined,terminalPriority>directive.priority)break;if((directiveValue=directive.scope)&&(directive.templateUrl||(isObject(directiveValue)?(assertNoDuplicate("new/isolated scope",newIsolateScopeDirective||newScopeDirective,directive,$compileNode),newIsolateScopeDirective=directive):assertNoDuplicate("new/isolated scope",newIsolateScopeDirective,directive,$compileNode)),newScopeDirective=newScopeDirective||directive),directiveName=directive.name,!directive.templateUrl&&directive.controller&&(directiveValue=directive.controller,controllerDirectives=controllerDirectives||{},assertNoDuplicate("'"+directiveName+"' controller",controllerDirectives[directiveName],directive,$compileNode),controllerDirectives[directiveName]=directive),(directiveValue=directive.transclude)&&(hasTranscludeDirective=!0,directive.$$tlb||(assertNoDuplicate("transclusion",nonTlbTranscludeDirective,directive,$compileNode),nonTlbTranscludeDirective=directive),"element"==directiveValue?(hasElementTranscludeDirective=!0,terminalPriority=directive.priority,$template=$compileNode,$compileNode=templateAttrs.$$element=jqLite(document.createComment(" "+directiveName+": "+templateAttrs[directiveName]+" ")),compileNode=$compileNode[0],replaceWith(jqCollection,sliceArgs($template),compileNode),childTranscludeFn=compile($template,transcludeFn,terminalPriority,replaceDirective&&replaceDirective.name,{nonTlbTranscludeDirective:nonTlbTranscludeDirective})):($template=jqLite(jqLiteClone(compileNode)).contents(),$compileNode.empty(),childTranscludeFn=compile($template,transcludeFn))),directive.template)if(hasTemplate=!0,assertNoDuplicate("template",templateDirective,directive,$compileNode),templateDirective=directive,directiveValue=isFunction(directive.template)?directive.template($compileNode,templateAttrs):directive.template,directiveValue=denormalizeTemplate(directiveValue),directive.replace){if(replaceDirective=directive,$template=jqLiteIsTextNode(directiveValue)?[]:jqLite(wrapTemplate(directive.templateNamespace,trim(directiveValue))),compileNode=$template[0],1!=$template.length||1!==compileNode.nodeType)throw $compileMinErr("tplrt","Template for directive '{0}' must have exactly one root element. {1}",directiveName,"");replaceWith(jqCollection,$compileNode,compileNode);var newTemplateAttrs={$attr:{}},templateDirectives=collectDirectives(compileNode,[],newTemplateAttrs),unprocessedDirectives=directives.splice(i+1,directives.length-(i+1));newIsolateScopeDirective&&markDirectivesAsIsolate(templateDirectives),directives=directives.concat(templateDirectives).concat(unprocessedDirectives),mergeTemplateAttributes(templateAttrs,newTemplateAttrs),ii=directives.length}else $compileNode.html(directiveValue);if(directive.templateUrl)hasTemplate=!0,assertNoDuplicate("template",templateDirective,directive,$compileNode),templateDirective=directive,directive.replace&&(replaceDirective=directive),nodeLinkFn=compileTemplateUrl(directives.splice(i,directives.length-i),$compileNode,templateAttrs,jqCollection,hasTranscludeDirective&&childTranscludeFn,preLinkFns,postLinkFns,{controllerDirectives:controllerDirectives,newIsolateScopeDirective:newIsolateScopeDirective,templateDirective:templateDirective,nonTlbTranscludeDirective:nonTlbTranscludeDirective}),ii=directives.length;else if(directive.compile)try{linkFn=directive.compile($compileNode,templateAttrs,childTranscludeFn),isFunction(linkFn)?addLinkFns(null,linkFn,attrStart,attrEnd):linkFn&&addLinkFns(linkFn.pre,linkFn.post,attrStart,attrEnd)}catch(e){$exceptionHandler(e,startingTag($compileNode))}directive.terminal&&(nodeLinkFn.terminal=!0,terminalPriority=Math.max(terminalPriority,directive.priority))}return nodeLinkFn.scope=newScopeDirective&&newScopeDirective.scope===!0,nodeLinkFn.transcludeOnThisElement=hasTranscludeDirective,nodeLinkFn.elementTranscludeOnThisElement=hasElementTranscludeDirective,nodeLinkFn.templateOnThisElement=hasTemplate,nodeLinkFn.transclude=childTranscludeFn,previousCompileContext.hasElementTranscludeDirective=hasElementTranscludeDirective,nodeLinkFn}function markDirectivesAsIsolate(directives){for(var j=0,jj=directives.length;jj>j;j++)directives[j]=inherit(directives[j],{$$isolateScope:!0})}function addDirective(tDirectives,name,location,maxPriority,ignoreDirective,startAttrName,endAttrName){if(name===ignoreDirective)return null;var match=null;if(hasDirectives.hasOwnProperty(name))for(var directive,directives=$injector.get(name+Suffix),i=0,ii=directives.length;ii>i;i++)try{directive=directives[i],(maxPriority===undefined||maxPriority>directive.priority)&&-1!=directive.restrict.indexOf(location)&&(startAttrName&&(directive=inherit(directive,{$$start:startAttrName,$$end:endAttrName})),tDirectives.push(directive),match=directive)}catch(e){$exceptionHandler(e)}return match}function directiveIsMultiElement(name){if(hasDirectives.hasOwnProperty(name))for(var directive,directives=$injector.get(name+Suffix),i=0,ii=directives.length;ii>i;i++)if(directive=directives[i],directive.multiElement)return!0;return!1}function mergeTemplateAttributes(dst,src){var srcAttr=src.$attr,dstAttr=dst.$attr,$element=dst.$$element;forEach(dst,function(value,key){"$"!=key.charAt(0)&&(src[key]&&src[key]!==value&&(value+=("style"===key?";":" ")+src[key]),dst.$set(key,value,!0,srcAttr[key]))}),forEach(src,function(value,key){"class"==key?(safeAddClass($element,value),dst["class"]=(dst["class"]?dst["class"]+" ":"")+value):"style"==key?($element.attr("style",$element.attr("style")+";"+value),dst.style=(dst.style?dst.style+";":"")+value):"$"==key.charAt(0)||dst.hasOwnProperty(key)||(dst[key]=value,dstAttr[key]=srcAttr[key])})}function compileTemplateUrl(directives,$compileNode,tAttrs,$rootElement,childTranscludeFn,preLinkFns,postLinkFns,previousCompileContext){var afterTemplateNodeLinkFn,afterTemplateChildLinkFn,linkQueue=[],beforeTemplateCompileNode=$compileNode[0],origAsyncDirective=directives.shift(),derivedSyncDirective=extend({},origAsyncDirective,{templateUrl:null,transclude:null,replace:null,$$originalDirective:origAsyncDirective}),templateUrl=isFunction(origAsyncDirective.templateUrl)?origAsyncDirective.templateUrl($compileNode,tAttrs):origAsyncDirective.templateUrl,templateNamespace=origAsyncDirective.templateNamespace;return $compileNode.empty(),$templateRequest($sce.getTrustedResourceUrl(templateUrl)).then(function(content){var compileNode,tempTemplateAttrs,$template,childBoundTranscludeFn;if(content=denormalizeTemplate(content),origAsyncDirective.replace){if($template=jqLiteIsTextNode(content)?[]:jqLite(wrapTemplate(templateNamespace,trim(content))),compileNode=$template[0],1!=$template.length||1!==compileNode.nodeType)throw $compileMinErr("tplrt","Template for directive '{0}' must have exactly one root element. {1}",origAsyncDirective.name,templateUrl);tempTemplateAttrs={$attr:{}},replaceWith($rootElement,$compileNode,compileNode);var templateDirectives=collectDirectives(compileNode,[],tempTemplateAttrs);isObject(origAsyncDirective.scope)&&markDirectivesAsIsolate(templateDirectives),directives=templateDirectives.concat(directives),mergeTemplateAttributes(tAttrs,tempTemplateAttrs)}else compileNode=beforeTemplateCompileNode,$compileNode.html(content);for(directives.unshift(derivedSyncDirective),afterTemplateNodeLinkFn=applyDirectivesToNode(directives,compileNode,tAttrs,childTranscludeFn,$compileNode,origAsyncDirective,preLinkFns,postLinkFns,previousCompileContext),forEach($rootElement,function(node,i){node==compileNode&&($rootElement[i]=$compileNode[0])}),afterTemplateChildLinkFn=compileNodes($compileNode[0].childNodes,childTranscludeFn);linkQueue.length;){var scope=linkQueue.shift(),beforeTemplateLinkNode=linkQueue.shift(),linkRootElement=linkQueue.shift(),boundTranscludeFn=linkQueue.shift(),linkNode=$compileNode[0];if(!scope.$$destroyed){if(beforeTemplateLinkNode!==beforeTemplateCompileNode){var oldClasses=beforeTemplateLinkNode.className;previousCompileContext.hasElementTranscludeDirective&&origAsyncDirective.replace||(linkNode=jqLiteClone(compileNode)),replaceWith(linkRootElement,jqLite(beforeTemplateLinkNode),linkNode),safeAddClass(jqLite(linkNode),oldClasses)}childBoundTranscludeFn=afterTemplateNodeLinkFn.transcludeOnThisElement?createBoundTranscludeFn(scope,afterTemplateNodeLinkFn.transclude,boundTranscludeFn):boundTranscludeFn,afterTemplateNodeLinkFn(afterTemplateChildLinkFn,scope,linkNode,$rootElement,childBoundTranscludeFn)}}linkQueue=null}),function(ignoreChildLinkFn,scope,node,rootElement,boundTranscludeFn){var childBoundTranscludeFn=boundTranscludeFn;scope.$$destroyed||(linkQueue?(linkQueue.push(scope),linkQueue.push(node),linkQueue.push(rootElement),linkQueue.push(childBoundTranscludeFn)):(afterTemplateNodeLinkFn.transcludeOnThisElement&&(childBoundTranscludeFn=createBoundTranscludeFn(scope,afterTemplateNodeLinkFn.transclude,boundTranscludeFn)),afterTemplateNodeLinkFn(afterTemplateChildLinkFn,scope,node,rootElement,childBoundTranscludeFn)))}}function byPriority(a,b){var diff=b.priority-a.priority;return 0!==diff?diff:a.name!==b.name?a.name<b.name?-1:1:a.index-b.index}function assertNoDuplicate(what,previousDirective,directive,element){if(previousDirective)throw $compileMinErr("multidir","Multiple directives [{0}, {1}] asking for {2} on: {3}",previousDirective.name,directive.name,what,startingTag(element))}function addTextInterpolateDirective(directives,text){var interpolateFn=$interpolate(text,!0);interpolateFn&&directives.push({priority:0,compile:function(templateNode){var templateNodeParent=templateNode.parent(),hasCompileParent=!!templateNodeParent.length;return hasCompileParent&&compile.$$addBindingClass(templateNodeParent),function(scope,node){var parent=node.parent();hasCompileParent||compile.$$addBindingClass(parent),compile.$$addBindingInfo(parent,interpolateFn.expressions),scope.$watch(interpolateFn,function(value){node[0].nodeValue=value})}}})}function wrapTemplate(type,template){switch(type=lowercase(type||"html")){case"svg":case"math":var wrapper=document.createElement("div");return wrapper.innerHTML="<"+type+">"+template+"</"+type+">",wrapper.childNodes[0].childNodes;default:return template}}function getTrustedContext(node,attrNormalizedName){if("srcdoc"==attrNormalizedName)return $sce.HTML;var tag=nodeName_(node);return"xlinkHref"==attrNormalizedName||"form"==tag&&"action"==attrNormalizedName||"img"!=tag&&("src"==attrNormalizedName||"ngSrc"==attrNormalizedName)?$sce.RESOURCE_URL:void 0}function addAttrInterpolateDirective(node,directives,value,name,allOrNothing){var interpolateFn=$interpolate(value,!0);if(interpolateFn){if("multiple"===name&&"select"===nodeName_(node))throw $compileMinErr("selmulti","Binding to the 'multiple' attribute is not supported. Element: {0}",startingTag(node));directives.push({priority:100,compile:function(){return{pre:function(scope,element,attr){var $$observers=attr.$$observers||(attr.$$observers={});if(EVENT_HANDLER_ATTR_REGEXP.test(name))throw $compileMinErr("nodomevents","Interpolations for HTML DOM event attributes are disallowed.  Please use the ng- versions (such as ng-click instead of onclick) instead.");interpolateFn=$interpolate(attr[name],!0,getTrustedContext(node,name),ALL_OR_NOTHING_ATTRS[name]||allOrNothing),interpolateFn&&(attr[name]=interpolateFn(scope),($$observers[name]||($$observers[name]=[])).$$inter=!0,(attr.$$observers&&attr.$$observers[name].$$scope||scope).$watch(interpolateFn,function(newValue,oldValue){"class"===name&&newValue!=oldValue?attr.$updateClass(newValue,oldValue):attr.$set(name,newValue)}))}}}})}}function replaceWith($rootElement,elementsToRemove,newNode){var i,ii,firstElementToRemove=elementsToRemove[0],removeCount=elementsToRemove.length,parent=firstElementToRemove.parentNode;if($rootElement)for(i=0,ii=$rootElement.length;ii>i;i++)if($rootElement[i]==firstElementToRemove){$rootElement[i++]=newNode;for(var j=i,j2=j+removeCount-1,jj=$rootElement.length;jj>j;j++,j2++)jj>j2?$rootElement[j]=$rootElement[j2]:delete $rootElement[j];$rootElement.length-=removeCount-1,$rootElement.context===firstElementToRemove&&($rootElement.context=newNode);break}parent&&parent.replaceChild(newNode,firstElementToRemove);var fragment=document.createDocumentFragment();fragment.appendChild(firstElementToRemove),jqLite(newNode).data(jqLite(firstElementToRemove).data()),jQuery?(skipDestroyOnNextJQueryCleanData=!0,jQuery.cleanData([firstElementToRemove])):delete jqLite.cache[firstElementToRemove[jqLite.expando]];for(var k=1,kk=elementsToRemove.length;kk>k;k++){var element=elementsToRemove[k];jqLite(element).remove(),fragment.appendChild(element),delete elementsToRemove[k]}elementsToRemove[0]=newNode,elementsToRemove.length=1}function cloneAndAnnotateFn(fn,annotation){return extend(function(){return fn.apply(null,arguments)},fn,annotation)}function invokeLinkFn(linkFn,scope,$element,attrs,controllers,transcludeFn){try{linkFn(scope,$element,attrs,controllers,transcludeFn)}catch(e){$exceptionHandler(e,startingTag($element))}}var Attributes=function(element,attributesToCopy){if(attributesToCopy){var i,l,key,keys=Object.keys(attributesToCopy);for(i=0,l=keys.length;l>i;i++)key=keys[i],this[key]=attributesToCopy[key]}else this.$attr={};this.$$element=element};Attributes.prototype={$normalize:directiveNormalize,$addClass:function(classVal){classVal&&classVal.length>0&&$animate.addClass(this.$$element,classVal)},$removeClass:function(classVal){classVal&&classVal.length>0&&$animate.removeClass(this.$$element,classVal)},$updateClass:function(newClasses,oldClasses){var toAdd=tokenDifference(newClasses,oldClasses);toAdd&&toAdd.length&&$animate.addClass(this.$$element,toAdd);var toRemove=tokenDifference(oldClasses,newClasses);toRemove&&toRemove.length&&$animate.removeClass(this.$$element,toRemove)},$set:function(key,value,writeAttr,attrName){var nodeName,node=this.$$element[0],booleanKey=getBooleanAttrName(node,key),aliasedKey=getAliasedAttrName(node,key),observer=key;if(booleanKey?(this.$$element.prop(key,value),attrName=booleanKey):aliasedKey&&(this[aliasedKey]=value,observer=aliasedKey),this[key]=value,attrName?this.$attr[key]=attrName:(attrName=this.$attr[key],attrName||(this.$attr[key]=attrName=snake_case(key,"-"))),nodeName=nodeName_(this.$$element),"a"===nodeName&&"href"===key||"img"===nodeName&&"src"===key)this[key]=value=$$sanitizeUri(value,"src"===key);else if("img"===nodeName&&"srcset"===key){for(var result="",trimmedSrcset=trim(value),srcPattern=/(\s+\d+x\s*,|\s+\d+w\s*,|\s+,|,\s+)/,pattern=/\s/.test(trimmedSrcset)?srcPattern:/(,)/,rawUris=trimmedSrcset.split(pattern),nbrUrisWith2parts=Math.floor(rawUris.length/2),i=0;nbrUrisWith2parts>i;i++){var innerIdx=2*i;result+=$$sanitizeUri(trim(rawUris[innerIdx]),!0),result+=" "+trim(rawUris[innerIdx+1])}var lastTuple=trim(rawUris[2*i]).split(/\s/);result+=$$sanitizeUri(trim(lastTuple[0]),!0),2===lastTuple.length&&(result+=" "+trim(lastTuple[1])),this[key]=value=result}writeAttr!==!1&&(null===value||value===undefined?this.$$element.removeAttr(attrName):this.$$element.attr(attrName,value));var $$observers=this.$$observers;$$observers&&forEach($$observers[observer],function(fn){try{fn(value)}catch(e){$exceptionHandler(e)}})},$observe:function(key,fn){var attrs=this,$$observers=attrs.$$observers||(attrs.$$observers=Object.create(null)),listeners=$$observers[key]||($$observers[key]=[]);return listeners.push(fn),$rootScope.$evalAsync(function(){listeners.$$inter||fn(attrs[key])}),function(){arrayRemove(listeners,fn)}}};var startSymbol=$interpolate.startSymbol(),endSymbol=$interpolate.endSymbol(),denormalizeTemplate="{{"==startSymbol||"}}"==endSymbol?identity:function(template){return template.replace(/\{\{/g,startSymbol).replace(/}}/g,endSymbol)},NG_ATTR_BINDING=/^ngAttr[A-Z]/;return compile.$$addBindingInfo=debugInfoEnabled?function($element,binding){var bindings=$element.data("$binding")||[];isArray(binding)?bindings=bindings.concat(binding):bindings.push(binding),$element.data("$binding",bindings)}:noop,compile.$$addBindingClass=debugInfoEnabled?function($element){safeAddClass($element,"ng-binding")}:noop,compile.$$addScopeInfo=debugInfoEnabled?function($element,scope,isolated,noTemplate){var dataName=isolated?noTemplate?"$isolateScopeNoTemplate":"$isolateScope":"$scope";$element.data(dataName,scope)}:noop,compile.$$addScopeClass=debugInfoEnabled?function($element,isolated){safeAddClass($element,isolated?"ng-isolate-scope":"ng-scope")}:noop,compile}]}function directiveNormalize(name){return camelCase(name.replace(PREFIX_REGEXP,""))}function tokenDifference(str1,str2){var values="",tokens1=str1.split(/\s+/),tokens2=str2.split(/\s+/);outer:for(var i=0;i<tokens1.length;i++){for(var token=tokens1[i],j=0;j<tokens2.length;j++)if(token==tokens2[j])continue outer;values+=(values.length>0?" ":"")+token}return values}function $ControllerProvider(){var controllers={},globals=!1,CNTRL_REG=/^(\S+)(\s+as\s+(\w+))?$/;this.register=function(name,constructor){assertNotHasOwnProperty(name,"controller"),isObject(name)?extend(controllers,name):controllers[name]=constructor},this.allowGlobals=function(){globals=!0},this.$get=["$injector","$window",function($injector,$window){function addIdentifier(locals,identifier,instance,name){if(!locals||!isObject(locals.$scope))throw minErr("$controller")("noscp","Cannot export controller '{0}' as '{1}'! No $scope object provided via `locals`.",name,identifier);locals.$scope[identifier]=instance}return function(expression,locals,later,ident){var instance,match,constructor,identifier;if(later=later===!0,ident&&isString(ident)&&(identifier=ident),isString(expression)&&(match=expression.match(CNTRL_REG),constructor=match[1],identifier=identifier||match[3],expression=controllers.hasOwnProperty(constructor)?controllers[constructor]:getter(locals.$scope,constructor,!0)||(globals?getter($window,constructor,!0):undefined),assertArgFn(expression,constructor,!0)),later){var Constructor=function(){};return Constructor.prototype=(isArray(expression)?expression[expression.length-1]:expression).prototype,instance=new Constructor,identifier&&addIdentifier(locals,identifier,instance,constructor||expression.name),extend(function(){return $injector.invoke(expression,instance,locals,constructor),instance},{instance:instance,identifier:identifier})}return instance=$injector.instantiate(expression,locals,constructor),identifier&&addIdentifier(locals,identifier,instance,constructor||expression.name),instance}}]}function $DocumentProvider(){this.$get=["$window",function(window){return jqLite(window.document)}]}function $ExceptionHandlerProvider(){this.$get=["$log",function($log){return function(){$log.error.apply($log,arguments)}}]}function parseHeaders(headers){var key,val,i,parsed={};return headers?(forEach(headers.split("\n"),function(line){i=line.indexOf(":"),key=lowercase(trim(line.substr(0,i))),val=trim(line.substr(i+1)),key&&(parsed[key]=parsed[key]?parsed[key]+", "+val:val)}),parsed):parsed}function headersGetter(headers){var headersObj=isObject(headers)?headers:undefined;return function(name){return headersObj||(headersObj=parseHeaders(headers)),name?headersObj[lowercase(name)]||null:headersObj}}function transformData(data,headers,fns){return isFunction(fns)?fns(data,headers):(forEach(fns,function(fn){data=fn(data,headers)}),data)}function isSuccess(status){return status>=200&&300>status}function $HttpProvider(){var JSON_START=/^\s*(\[|\{[^\{])/,JSON_END=/[\}\]]\s*$/,PROTECTION_PREFIX=/^\)\]\}',?\n/,CONTENT_TYPE_APPLICATION_JSON={"Content-Type":"application/json;charset=utf-8"},defaults=this.defaults={transformResponse:[function(data){return isString(data)&&(data=data.replace(PROTECTION_PREFIX,""),JSON_START.test(data)&&JSON_END.test(data)&&(data=fromJson(data))),data}],transformRequest:[function(d){return!isObject(d)||isFile(d)||isBlob(d)?d:toJson(d)}],headers:{common:{Accept:"application/json, text/plain, */*"},post:shallowCopy(CONTENT_TYPE_APPLICATION_JSON),put:shallowCopy(CONTENT_TYPE_APPLICATION_JSON),patch:shallowCopy(CONTENT_TYPE_APPLICATION_JSON)},xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN"},useApplyAsync=!1;this.useApplyAsync=function(value){return isDefined(value)?(useApplyAsync=!!value,this):useApplyAsync};var interceptorFactories=this.interceptors=[];this.$get=["$httpBackend","$browser","$cacheFactory","$rootScope","$q","$injector",function($httpBackend,$browser,$cacheFactory,$rootScope,$q,$injector){function $http(requestConfig){function transformResponse(response){var resp=extend({},response,{data:transformData(response.data,response.headers,config.transformResponse)});return isSuccess(response.status)?resp:$q.reject(resp)}function mergeHeaders(config){function execHeaders(headers){var headerContent;forEach(headers,function(headerFn,header){isFunction(headerFn)&&(headerContent=headerFn(),null!=headerContent?headers[header]=headerContent:delete headers[header])})}var defHeaderName,lowercaseDefHeaderName,reqHeaderName,defHeaders=defaults.headers,reqHeaders=extend({},config.headers);defHeaders=extend({},defHeaders.common,defHeaders[lowercase(config.method)]);defaultHeadersIteration:for(defHeaderName in defHeaders){lowercaseDefHeaderName=lowercase(defHeaderName);for(reqHeaderName in reqHeaders)if(lowercase(reqHeaderName)===lowercaseDefHeaderName)continue defaultHeadersIteration;reqHeaders[defHeaderName]=defHeaders[defHeaderName]}return execHeaders(reqHeaders),reqHeaders}var config={method:"get",transformRequest:defaults.transformRequest,transformResponse:defaults.transformResponse},headers=mergeHeaders(requestConfig);extend(config,requestConfig),config.headers=headers,config.method=uppercase(config.method);var serverRequest=function(config){headers=config.headers;var reqData=transformData(config.data,headersGetter(headers),config.transformRequest);return isUndefined(reqData)&&forEach(headers,function(value,header){"content-type"===lowercase(header)&&delete headers[header]}),isUndefined(config.withCredentials)&&!isUndefined(defaults.withCredentials)&&(config.withCredentials=defaults.withCredentials),sendReq(config,reqData,headers).then(transformResponse,transformResponse)},chain=[serverRequest,undefined],promise=$q.when(config);for(forEach(reversedInterceptors,function(interceptor){(interceptor.request||interceptor.requestError)&&chain.unshift(interceptor.request,interceptor.requestError),(interceptor.response||interceptor.responseError)&&chain.push(interceptor.response,interceptor.responseError)});chain.length;){var thenFn=chain.shift(),rejectFn=chain.shift();promise=promise.then(thenFn,rejectFn)}return promise.success=function(fn){return promise.then(function(response){fn(response.data,response.status,response.headers,config)}),promise},promise.error=function(fn){return promise.then(null,function(response){fn(response.data,response.status,response.headers,config)}),promise},promise}function createShortMethods(){forEach(arguments,function(name){$http[name]=function(url,config){return $http(extend(config||{},{method:name,url:url}))}})}function createShortMethodsWithData(){forEach(arguments,function(name){$http[name]=function(url,data,config){return $http(extend(config||{},{method:name,url:url,data:data}))}})}function sendReq(config,reqData,reqHeaders){function done(status,response,headersString,statusText){function resolveHttpPromise(){resolvePromise(response,status,headersString,statusText)}cache&&(isSuccess(status)?cache.put(url,[status,response,parseHeaders(headersString),statusText]):cache.remove(url)),useApplyAsync?$rootScope.$applyAsync(resolveHttpPromise):(resolveHttpPromise(),$rootScope.$$phase||$rootScope.$apply())}function resolvePromise(response,status,headers,statusText){status=Math.max(status,0),(isSuccess(status)?deferred.resolve:deferred.reject)({data:response,status:status,headers:headersGetter(headers),config:config,statusText:statusText})}function removePendingReq(){var idx=$http.pendingRequests.indexOf(config);-1!==idx&&$http.pendingRequests.splice(idx,1)}var cache,cachedResp,deferred=$q.defer(),promise=deferred.promise,url=buildUrl(config.url,config.params);if($http.pendingRequests.push(config),promise.then(removePendingReq,removePendingReq),!config.cache&&!defaults.cache||config.cache===!1||"GET"!==config.method&&"JSONP"!==config.method||(cache=isObject(config.cache)?config.cache:isObject(defaults.cache)?defaults.cache:defaultCache),cache)if(cachedResp=cache.get(url),isDefined(cachedResp)){if(isPromiseLike(cachedResp))return cachedResp.then(removePendingReq,removePendingReq),cachedResp;isArray(cachedResp)?resolvePromise(cachedResp[1],cachedResp[0],shallowCopy(cachedResp[2]),cachedResp[3]):resolvePromise(cachedResp,200,{},"OK")}else cache.put(url,promise);if(isUndefined(cachedResp)){var xsrfValue=urlIsSameOrigin(config.url)?$browser.cookies()[config.xsrfCookieName||defaults.xsrfCookieName]:undefined;xsrfValue&&(reqHeaders[config.xsrfHeaderName||defaults.xsrfHeaderName]=xsrfValue),$httpBackend(config.method,url,reqData,done,reqHeaders,config.timeout,config.withCredentials,config.responseType)}return promise}function buildUrl(url,params){if(!params)return url;var parts=[];return forEachSorted(params,function(value,key){null===value||isUndefined(value)||(isArray(value)||(value=[value]),forEach(value,function(v){isObject(v)&&(v=isDate(v)?v.toISOString():toJson(v)),parts.push(encodeUriQuery(key)+"="+encodeUriQuery(v))}))}),parts.length>0&&(url+=(-1==url.indexOf("?")?"?":"&")+parts.join("&")),url}var defaultCache=$cacheFactory("$http"),reversedInterceptors=[];return forEach(interceptorFactories,function(interceptorFactory){reversedInterceptors.unshift(isString(interceptorFactory)?$injector.get(interceptorFactory):$injector.invoke(interceptorFactory))}),$http.pendingRequests=[],createShortMethods("get","delete","head","jsonp"),createShortMethodsWithData("post","put","patch"),$http.defaults=defaults,$http}]}function createXhr(method){if(8>=msie&&(!method.match(/^(get|post|head|put|delete|options)$/i)||!window.XMLHttpRequest))return new window.ActiveXObject("Microsoft.XMLHTTP");if(window.XMLHttpRequest)return new window.XMLHttpRequest;throw minErr("$httpBackend")("noxhr","This browser does not support XMLHttpRequest.")}function $HttpBackendProvider(){this.$get=["$browser","$window","$document",function($browser,$window,$document){return createHttpBackend($browser,createXhr,$browser.defer,$window.angular.callbacks,$document[0])}]}function createHttpBackend($browser,createXhr,$browserDefer,callbacks,rawDocument){function jsonpReq(url,callbackId,done){var script=rawDocument.createElement("script"),callback=null;return script.type="text/javascript",script.src=url,script.async=!0,callback=function(event){removeEventListenerFn(script,"load",callback),removeEventListenerFn(script,"error",callback),rawDocument.body.removeChild(script),script=null;var status=-1,text="unknown";event&&("load"!==event.type||callbacks[callbackId].called||(event={type:"error"}),text=event.type,status="error"===event.type?404:200),done&&done(status,text)},addEventListenerFn(script,"load",callback),addEventListenerFn(script,"error",callback),rawDocument.body.appendChild(script),callback}var ABORTED=-1;return function(method,url,post,callback,headers,timeout,withCredentials,responseType){function timeoutRequest(){status=ABORTED,jsonpDone&&jsonpDone(),xhr&&xhr.abort()}function completeRequest(callback,status,response,headersString,statusText){timeoutId&&$browserDefer.cancel(timeoutId),jsonpDone=xhr=null,0===status&&(status=response?200:"file"==urlResolve(url).protocol?404:0),status=1223===status?204:status,statusText=statusText||"",callback(status,response,headersString,statusText),$browser.$$completeOutstandingRequest(noop)}var status;if($browser.$$incOutstandingRequestCount(),url=url||$browser.url(),"jsonp"==lowercase(method)){var callbackId="_"+(callbacks.counter++).toString(36);callbacks[callbackId]=function(data){callbacks[callbackId].data=data,callbacks[callbackId].called=!0};var jsonpDone=jsonpReq(url.replace("JSON_CALLBACK","angular.callbacks."+callbackId),callbackId,function(status,text){completeRequest(callback,status,callbacks[callbackId].data,"",text),callbacks[callbackId]=noop})}else{var xhr=createXhr(method);if(xhr.open(method,url,!0),forEach(headers,function(value,key){isDefined(value)&&xhr.setRequestHeader(key,value)}),xhr.onreadystatechange=function(){if(xhr&&4==xhr.readyState){var responseHeaders=null,response=null,statusText="";status!==ABORTED&&(responseHeaders=xhr.getAllResponseHeaders(),response="response"in xhr?xhr.response:xhr.responseText),status===ABORTED&&10>msie||(statusText=xhr.statusText),completeRequest(callback,status||xhr.status,response,responseHeaders,statusText)}},withCredentials&&(xhr.withCredentials=!0),responseType)try{xhr.responseType=responseType}catch(e){if("json"!==responseType)throw e}xhr.send(post||null)}if(timeout>0)var timeoutId=$browserDefer(timeoutRequest,timeout);else isPromiseLike(timeout)&&timeout.then(timeoutRequest)}}function $InterpolateProvider(){var startSymbol="{{",endSymbol="}}";this.startSymbol=function(value){return value?(startSymbol=value,this):startSymbol},this.endSymbol=function(value){return value?(endSymbol=value,this):endSymbol},this.$get=["$parse","$exceptionHandler","$sce",function($parse,$exceptionHandler,$sce){function escape(ch){return"\\\\\\"+ch}function $interpolate(text,mustHaveExpression,trustedContext,allOrNothing){function unescapeText(text){return text.replace(escapedStartRegexp,startSymbol).replace(escapedEndRegexp,endSymbol)}function parseStringifyInterceptor(value){try{return stringify(getValue(value))}catch(err){var newErr=$interpolateMinErr("interr","Can't interpolate: {0}\n{1}",text,err.toString());$exceptionHandler(newErr)}}allOrNothing=!!allOrNothing;for(var startIndex,endIndex,exp,index=0,expressions=[],parseFns=[],textLength=text.length,concat=[],expressionPositions=[];textLength>index;){if(-1==(startIndex=text.indexOf(startSymbol,index))||-1==(endIndex=text.indexOf(endSymbol,startIndex+startSymbolLength))){index!==textLength&&concat.push(unescapeText(text.substring(index)));break}index!==startIndex&&concat.push(unescapeText(text.substring(index,startIndex))),exp=text.substring(startIndex+startSymbolLength,endIndex),expressions.push(exp),parseFns.push($parse(exp,parseStringifyInterceptor)),index=endIndex+endSymbolLength,expressionPositions.push(concat.length),concat.push("")
}if(trustedContext&&concat.length>1)throw $interpolateMinErr("noconcat","Error while interpolating: {0}\nStrict Contextual Escaping disallows interpolations that concatenate multiple expressions when a trusted value is required.  See http://docs.angularjs.org/api/ng.$sce",text);if(!mustHaveExpression||expressions.length){var compute=function(values){for(var i=0,ii=expressions.length;ii>i;i++){if(allOrNothing&&isUndefined(values[i]))return;concat[expressionPositions[i]]=values[i]}return concat.join("")},getValue=function(value){return trustedContext?$sce.getTrusted(trustedContext,value):$sce.valueOf(value)},stringify=function(value){if(null==value)return"";switch(typeof value){case"string":break;case"number":value=""+value;break;default:value=toJson(value)}return value};return extend(function(context){var i=0,ii=expressions.length,values=new Array(ii);try{for(;ii>i;i++)values[i]=parseFns[i](context);return compute(values)}catch(err){var newErr=$interpolateMinErr("interr","Can't interpolate: {0}\n{1}",text,err.toString());$exceptionHandler(newErr)}},{exp:text,expressions:expressions,$$watchDelegate:function(scope,listener,objectEquality){var lastValue;return scope.$watchGroup(parseFns,function(values,oldValues){var currValue=compute(values);isFunction(listener)&&listener.call(this,currValue,values!==oldValues?lastValue:currValue,scope),lastValue=currValue},objectEquality)}})}}var startSymbolLength=startSymbol.length,endSymbolLength=endSymbol.length,escapedStartRegexp=new RegExp(startSymbol.replace(/./g,escape),"g"),escapedEndRegexp=new RegExp(endSymbol.replace(/./g,escape),"g");return $interpolate.startSymbol=function(){return startSymbol},$interpolate.endSymbol=function(){return endSymbol},$interpolate}]}function $IntervalProvider(){this.$get=["$rootScope","$window","$q","$$q",function($rootScope,$window,$q,$$q){function interval(fn,delay,count,invokeApply){var setInterval=$window.setInterval,clearInterval=$window.clearInterval,iteration=0,skipApply=isDefined(invokeApply)&&!invokeApply,deferred=(skipApply?$$q:$q).defer(),promise=deferred.promise;return count=isDefined(count)?count:0,promise.then(null,null,fn),promise.$$intervalId=setInterval(function(){deferred.notify(iteration++),count>0&&iteration>=count&&(deferred.resolve(iteration),clearInterval(promise.$$intervalId),delete intervals[promise.$$intervalId]),skipApply||$rootScope.$apply()},delay),intervals[promise.$$intervalId]=deferred,promise}var intervals={};return interval.cancel=function(promise){return promise&&promise.$$intervalId in intervals?(intervals[promise.$$intervalId].reject("canceled"),$window.clearInterval(promise.$$intervalId),delete intervals[promise.$$intervalId],!0):!1},interval}]}function $LocaleProvider(){this.$get=function(){return{id:"en-us",NUMBER_FORMATS:{DECIMAL_SEP:".",GROUP_SEP:",",PATTERNS:[{minInt:1,minFrac:0,maxFrac:3,posPre:"",posSuf:"",negPre:"-",negSuf:"",gSize:3,lgSize:3},{minInt:1,minFrac:2,maxFrac:2,posPre:"¤",posSuf:"",negPre:"(¤",negSuf:")",gSize:3,lgSize:3}],CURRENCY_SYM:"$"},DATETIME_FORMATS:{MONTH:"January,February,March,April,May,June,July,August,September,October,November,December".split(","),SHORTMONTH:"Jan,Feb,Mar,Apr,May,Jun,Jul,Aug,Sep,Oct,Nov,Dec".split(","),DAY:"Sunday,Monday,Tuesday,Wednesday,Thursday,Friday,Saturday".split(","),SHORTDAY:"Sun,Mon,Tue,Wed,Thu,Fri,Sat".split(","),AMPMS:["AM","PM"],medium:"MMM d, y h:mm:ss a","short":"M/d/yy h:mm a",fullDate:"EEEE, MMMM d, y",longDate:"MMMM d, y",mediumDate:"MMM d, y",shortDate:"M/d/yy",mediumTime:"h:mm:ss a",shortTime:"h:mm a"},pluralCat:function(num){return 1===num?"one":"other"}}}}function encodePath(path){for(var segments=path.split("/"),i=segments.length;i--;)segments[i]=encodeUriSegment(segments[i]);return segments.join("/")}function parseAbsoluteUrl(absoluteUrl,locationObj,appBase){var parsedUrl=urlResolve(absoluteUrl,appBase);locationObj.$$protocol=parsedUrl.protocol,locationObj.$$host=parsedUrl.hostname,locationObj.$$port=int(parsedUrl.port)||DEFAULT_PORTS[parsedUrl.protocol]||null}function parseAppUrl(relativeUrl,locationObj,appBase){var prefixed="/"!==relativeUrl.charAt(0);prefixed&&(relativeUrl="/"+relativeUrl);var match=urlResolve(relativeUrl,appBase);locationObj.$$path=decodeURIComponent(prefixed&&"/"===match.pathname.charAt(0)?match.pathname.substring(1):match.pathname),locationObj.$$search=parseKeyValue(match.search),locationObj.$$hash=decodeURIComponent(match.hash),locationObj.$$path&&"/"!=locationObj.$$path.charAt(0)&&(locationObj.$$path="/"+locationObj.$$path)}function beginsWith(begin,whole){return 0===whole.indexOf(begin)?whole.substr(begin.length):void 0}function stripHash(url){var index=url.indexOf("#");return-1==index?url:url.substr(0,index)}function stripFile(url){return url.substr(0,stripHash(url).lastIndexOf("/")+1)}function serverBase(url){return url.substring(0,url.indexOf("/",url.indexOf("//")+2))}function LocationHtml5Url(appBase,basePrefix){this.$$html5=!0,basePrefix=basePrefix||"";var appBaseNoFile=stripFile(appBase);parseAbsoluteUrl(appBase,this,appBase),this.$$parse=function(url){var pathUrl=beginsWith(appBaseNoFile,url);if(!isString(pathUrl))throw $locationMinErr("ipthprfx",'Invalid url "{0}", missing path prefix "{1}".',url,appBaseNoFile);parseAppUrl(pathUrl,this,appBase),this.$$path||(this.$$path="/"),this.$$compose()},this.$$compose=function(){var search=toKeyValue(this.$$search),hash=this.$$hash?"#"+encodeUriSegment(this.$$hash):"";this.$$url=encodePath(this.$$path)+(search?"?"+search:"")+hash,this.$$absUrl=appBaseNoFile+this.$$url.substr(1)},this.$$parseLinkUrl=function(url,relHref){if(relHref&&"#"===relHref[0])return this.hash(relHref.slice(1)),!0;var appUrl,prevAppUrl,rewrittenUrl;return(appUrl=beginsWith(appBase,url))!==undefined?(prevAppUrl=appUrl,rewrittenUrl=(appUrl=beginsWith(basePrefix,appUrl))!==undefined?appBaseNoFile+(beginsWith("/",appUrl)||appUrl):appBase+prevAppUrl):(appUrl=beginsWith(appBaseNoFile,url))!==undefined?rewrittenUrl=appBaseNoFile+appUrl:appBaseNoFile==url+"/"&&(rewrittenUrl=appBaseNoFile),rewrittenUrl&&this.$$parse(rewrittenUrl),!!rewrittenUrl}}function LocationHashbangUrl(appBase,hashPrefix){var appBaseNoFile=stripFile(appBase);parseAbsoluteUrl(appBase,this,appBase),this.$$parse=function(url){function removeWindowsDriveName(path,url,base){var firstPathSegmentMatch,windowsFilePathExp=/^\/[A-Z]:(\/.*)/;return 0===url.indexOf(base)&&(url=url.replace(base,"")),windowsFilePathExp.exec(url)?path:(firstPathSegmentMatch=windowsFilePathExp.exec(path),firstPathSegmentMatch?firstPathSegmentMatch[1]:path)}var withoutBaseUrl=beginsWith(appBase,url)||beginsWith(appBaseNoFile,url),withoutHashUrl="#"==withoutBaseUrl.charAt(0)?beginsWith(hashPrefix,withoutBaseUrl):this.$$html5?withoutBaseUrl:"";if(!isString(withoutHashUrl))throw $locationMinErr("ihshprfx",'Invalid url "{0}", missing hash prefix "{1}".',url,hashPrefix);parseAppUrl(withoutHashUrl,this,appBase),this.$$path=removeWindowsDriveName(this.$$path,withoutHashUrl,appBase),this.$$compose()},this.$$compose=function(){var search=toKeyValue(this.$$search),hash=this.$$hash?"#"+encodeUriSegment(this.$$hash):"";this.$$url=encodePath(this.$$path)+(search?"?"+search:"")+hash,this.$$absUrl=appBase+(this.$$url?hashPrefix+this.$$url:"")},this.$$parseLinkUrl=function(url){return stripHash(appBase)==stripHash(url)?(this.$$parse(url),!0):!1}}function LocationHashbangInHtml5Url(appBase,hashPrefix){this.$$html5=!0,LocationHashbangUrl.apply(this,arguments);var appBaseNoFile=stripFile(appBase);this.$$parseLinkUrl=function(url,relHref){if(relHref&&"#"===relHref[0])return this.hash(relHref.slice(1)),!0;var rewrittenUrl,appUrl;return appBase==stripHash(url)?rewrittenUrl=url:(appUrl=beginsWith(appBaseNoFile,url))?rewrittenUrl=appBase+hashPrefix+appUrl:appBaseNoFile===url+"/"&&(rewrittenUrl=appBaseNoFile),rewrittenUrl&&this.$$parse(rewrittenUrl),!!rewrittenUrl},this.$$compose=function(){var search=toKeyValue(this.$$search),hash=this.$$hash?"#"+encodeUriSegment(this.$$hash):"";this.$$url=encodePath(this.$$path)+(search?"?"+search:"")+hash,this.$$absUrl=appBase+hashPrefix+this.$$url}}function locationGetter(property){return function(){return this[property]}}function locationGetterSetter(property,preprocess){return function(value){return isUndefined(value)?this[property]:(this[property]=preprocess(value),this.$$compose(),this)}}function $LocationProvider(){var hashPrefix="",html5Mode={enabled:!1,requireBase:!0};this.hashPrefix=function(prefix){return isDefined(prefix)?(hashPrefix=prefix,this):hashPrefix},this.html5Mode=function(mode){return isBoolean(mode)?(html5Mode.enabled=mode,this):isObject(mode)?(html5Mode.enabled=isBoolean(mode.enabled)?mode.enabled:html5Mode.enabled,html5Mode.requireBase=isBoolean(mode.requireBase)?mode.requireBase:html5Mode.requireBase,this):html5Mode},this.$get=["$rootScope","$browser","$sniffer","$rootElement",function($rootScope,$browser,$sniffer,$rootElement){function afterLocationChange(oldUrl){$rootScope.$broadcast("$locationChangeSuccess",$location.absUrl(),oldUrl)}var $location,LocationMode,appBase,baseHref=$browser.baseHref(),initialUrl=$browser.url();if(html5Mode.enabled){if(!baseHref&&html5Mode.requireBase)throw $locationMinErr("nobase","$location in HTML5 mode requires a <base> tag to be present!");appBase=serverBase(initialUrl)+(baseHref||"/"),LocationMode=$sniffer.history?LocationHtml5Url:LocationHashbangInHtml5Url}else appBase=stripHash(initialUrl),LocationMode=LocationHashbangUrl;$location=new LocationMode(appBase,"#"+hashPrefix),$location.$$parseLinkUrl(initialUrl,initialUrl);var IGNORE_URI_REGEXP=/^\s*(javascript|mailto):/i;$rootElement.on("click",function(event){if(!event.ctrlKey&&!event.metaKey&&2!=event.which){for(var elm=jqLite(event.target);"a"!==nodeName_(elm[0]);)if(elm[0]===$rootElement[0]||!(elm=elm.parent())[0])return;var absHref=elm.prop("href"),relHref=elm.attr("href")||elm.attr("xlink:href");isObject(absHref)&&"[object SVGAnimatedString]"===absHref.toString()&&(absHref=urlResolve(absHref.animVal).href),IGNORE_URI_REGEXP.test(absHref)||!absHref||elm.attr("target")||event.isDefaultPrevented()||$location.$$parseLinkUrl(absHref,relHref)&&(event.preventDefault(),$location.absUrl()!=$browser.url()&&($rootScope.$apply(),window.angular["ff-684208-preventDefault"]=!0))}}),$location.absUrl()!=initialUrl&&$browser.url($location.absUrl(),!0),$browser.onUrlChange(function(newUrl){$location.absUrl()!=newUrl&&($rootScope.$evalAsync(function(){var oldUrl=$location.absUrl();$location.$$parse(newUrl),$rootScope.$broadcast("$locationChangeStart",newUrl,oldUrl).defaultPrevented?($location.$$parse(oldUrl),$browser.url(oldUrl)):afterLocationChange(oldUrl)}),$rootScope.$$phase||$rootScope.$digest())});var changeCounter=0;return $rootScope.$watch(function(){var oldUrl=$browser.url(),currentReplace=$location.$$replace;return changeCounter&&oldUrl==$location.absUrl()||(changeCounter++,$rootScope.$evalAsync(function(){$rootScope.$broadcast("$locationChangeStart",$location.absUrl(),oldUrl).defaultPrevented?$location.$$parse(oldUrl):($browser.url($location.absUrl(),currentReplace),afterLocationChange(oldUrl))})),$location.$$replace=!1,changeCounter}),$location}]}function $LogProvider(){var debug=!0,self=this;this.debugEnabled=function(flag){return isDefined(flag)?(debug=flag,this):debug},this.$get=["$window",function($window){function formatError(arg){return arg instanceof Error&&(arg.stack?arg=arg.message&&-1===arg.stack.indexOf(arg.message)?"Error: "+arg.message+"\n"+arg.stack:arg.stack:arg.sourceURL&&(arg=arg.message+"\n"+arg.sourceURL+":"+arg.line)),arg}function consoleLog(type){var console=$window.console||{},logFn=console[type]||console.log||noop,hasApply=!1;try{hasApply=!!logFn.apply}catch(e){}return hasApply?function(){var args=[];return forEach(arguments,function(arg){args.push(formatError(arg))}),logFn.apply(console,args)}:function(arg1,arg2){logFn(arg1,null==arg2?"":arg2)}}return{log:consoleLog("log"),info:consoleLog("info"),warn:consoleLog("warn"),error:consoleLog("error"),debug:function(){var fn=consoleLog("debug");return function(){debug&&fn.apply(self,arguments)}}()}}]}function ensureSafeMemberName(name,fullExpression){if("__defineGetter__"===name||"__defineSetter__"===name||"__lookupGetter__"===name||"__lookupSetter__"===name||"__proto__"===name)throw $parseMinErr("isecfld","Attempting to access a disallowed field in Angular expressions! Expression: {0}",fullExpression);return name}function ensureSafeObject(obj,fullExpression){if(obj){if(obj.constructor===obj)throw $parseMinErr("isecfn","Referencing Function in Angular expressions is disallowed! Expression: {0}",fullExpression);if(obj.window===obj)throw $parseMinErr("isecwindow","Referencing the Window in Angular expressions is disallowed! Expression: {0}",fullExpression);if(obj.children&&(obj.nodeName||obj.prop&&obj.attr&&obj.find))throw $parseMinErr("isecdom","Referencing DOM nodes in Angular expressions is disallowed! Expression: {0}",fullExpression);if(obj===Object)throw $parseMinErr("isecobj","Referencing Object in Angular expressions is disallowed! Expression: {0}",fullExpression)}return obj}function ensureSafeFunction(obj,fullExpression){if(obj){if(obj.constructor===obj)throw $parseMinErr("isecfn","Referencing Function in Angular expressions is disallowed! Expression: {0}",fullExpression);if(obj===CALL||obj===APPLY||obj===BIND)throw $parseMinErr("isecff","Referencing call, apply or bind in Angular expressions is disallowed! Expression: {0}",fullExpression)}}function isConstant(exp){return exp.constant}function setter(obj,path,setValue,fullExp){ensureSafeObject(obj,fullExp);for(var key,element=path.split("."),i=0;element.length>1;i++){key=ensureSafeMemberName(element.shift(),fullExp);var propertyObj=ensureSafeObject(obj[key],fullExp);propertyObj||(propertyObj={},obj[key]=propertyObj),obj=propertyObj}return key=ensureSafeMemberName(element.shift(),fullExp),ensureSafeObject(obj[key],fullExp),obj[key]=setValue,setValue}function cspSafeGetterFn(key0,key1,key2,key3,key4,fullExp){return ensureSafeMemberName(key0,fullExp),ensureSafeMemberName(key1,fullExp),ensureSafeMemberName(key2,fullExp),ensureSafeMemberName(key3,fullExp),ensureSafeMemberName(key4,fullExp),function(scope,locals){var pathVal=locals&&locals.hasOwnProperty(key0)?locals:scope;return null==pathVal?pathVal:(pathVal=pathVal[key0],key1?null==pathVal?undefined:(pathVal=pathVal[key1],key2?null==pathVal?undefined:(pathVal=pathVal[key2],key3?null==pathVal?undefined:(pathVal=pathVal[key3],key4?null==pathVal?undefined:pathVal=pathVal[key4]:pathVal):pathVal):pathVal):pathVal)}}function getterFn(path,options,fullExp){var fn=getterFnCache[path];if(fn)return fn;var pathKeys=path.split("."),pathKeysLength=pathKeys.length;if(options.csp)fn=6>pathKeysLength?cspSafeGetterFn(pathKeys[0],pathKeys[1],pathKeys[2],pathKeys[3],pathKeys[4],fullExp):function(scope,locals){var val,i=0;do val=cspSafeGetterFn(pathKeys[i++],pathKeys[i++],pathKeys[i++],pathKeys[i++],pathKeys[i++],fullExp)(scope,locals),locals=undefined,scope=val;while(pathKeysLength>i);return val};else{var code="";forEach(pathKeys,function(key,index){ensureSafeMemberName(key,fullExp),code+="if(s == null) return undefined;\ns="+(index?"s":'((l&&l.hasOwnProperty("'+key+'"))?l:s)')+"."+key+";\n"}),code+="return s;";var evaledFnGetter=new Function("s","l",code);evaledFnGetter.toString=valueFn(code),fn=evaledFnGetter}return fn.sharedGetter=!0,fn.assign=function(self,value){return setter(self,path,value,path)},getterFnCache[path]=fn,fn}function $ParseProvider(){var cache=createMap(),$parseOptions={csp:!1};this.$get=["$filter","$sniffer",function($filter,$sniffer){function wrapSharedExpression(exp){var wrapped=exp;return exp.sharedGetter&&(wrapped=function(self,locals){return exp(self,locals)},wrapped.literal=exp.literal,wrapped.constant=exp.constant,wrapped.assign=exp.assign),wrapped}function collectExpressionInputs(inputs,list){for(var i=0,ii=inputs.length;ii>i;i++){var input=inputs[i];input.constant||(input.inputs?collectExpressionInputs(input.inputs,list):-1===list.indexOf(input)&&list.push(input))}return list}function expressionInputDirtyCheck(newValue,oldValueOfValue){return null==newValue||null==oldValueOfValue?newValue===oldValueOfValue:"object"==typeof newValue&&(newValue=newValue.valueOf(),"object"==typeof newValue)?!1:newValue===oldValueOfValue||newValue!==newValue&&oldValueOfValue!==oldValueOfValue}function inputsWatchDelegate(scope,listener,objectEquality,parsedExpression){var lastResult,inputExpressions=parsedExpression.$$inputs||(parsedExpression.$$inputs=collectExpressionInputs(parsedExpression.inputs,[]));if(1===inputExpressions.length){var oldInputValue=expressionInputDirtyCheck;return inputExpressions=inputExpressions[0],scope.$watch(function(scope){var newInputValue=inputExpressions(scope);return expressionInputDirtyCheck(newInputValue,oldInputValue)||(lastResult=parsedExpression(scope),oldInputValue=newInputValue&&newInputValue.valueOf()),lastResult},listener,objectEquality)}for(var oldInputValueOfValues=[],i=0,ii=inputExpressions.length;ii>i;i++)oldInputValueOfValues[i]=expressionInputDirtyCheck;return scope.$watch(function(scope){for(var changed=!1,i=0,ii=inputExpressions.length;ii>i;i++){var newInputValue=inputExpressions[i](scope);(changed||(changed=!expressionInputDirtyCheck(newInputValue,oldInputValueOfValues[i])))&&(oldInputValueOfValues[i]=newInputValue&&newInputValue.valueOf())}return changed&&(lastResult=parsedExpression(scope)),lastResult},listener,objectEquality)}function oneTimeWatchDelegate(scope,listener,objectEquality,parsedExpression){var unwatch,lastValue;return unwatch=scope.$watch(function(scope){return parsedExpression(scope)},function(value,old,scope){lastValue=value,isFunction(listener)&&listener.apply(this,arguments),isDefined(value)&&scope.$$postDigest(function(){isDefined(lastValue)&&unwatch()})},objectEquality)}function oneTimeLiteralWatchDelegate(scope,listener,objectEquality,parsedExpression){function isAllDefined(value){var allDefined=!0;return forEach(value,function(val){isDefined(val)||(allDefined=!1)}),allDefined}var unwatch;return unwatch=scope.$watch(function(scope){return parsedExpression(scope)},function(value,old,scope){isFunction(listener)&&listener.call(this,value,old,scope),isAllDefined(value)&&scope.$$postDigest(function(){isAllDefined(value)&&unwatch()})},objectEquality)}function constantWatchDelegate(scope,listener,objectEquality,parsedExpression){var unwatch;return unwatch=scope.$watch(function(scope){return parsedExpression(scope)},function(){isFunction(listener)&&listener.apply(this,arguments),unwatch()},objectEquality)}function addInterceptor(parsedExpression,interceptorFn){if(!interceptorFn)return parsedExpression;var fn=function(scope,locals){var value=parsedExpression(scope,locals),result=interceptorFn(value,scope,locals);return isDefined(value)?result:value};return parsedExpression.$$watchDelegate&&parsedExpression.$$watchDelegate!==inputsWatchDelegate?fn.$$watchDelegate=parsedExpression.$$watchDelegate:interceptorFn.$stateful||(fn.$$watchDelegate=inputsWatchDelegate,fn.inputs=[parsedExpression]),fn}return $parseOptions.csp=$sniffer.csp,function(exp,interceptorFn){var parsedExpression,oneTime,cacheKey;switch(typeof exp){case"string":if(cacheKey=exp=exp.trim(),parsedExpression=cache[cacheKey],!parsedExpression){":"===exp.charAt(0)&&":"===exp.charAt(1)&&(oneTime=!0,exp=exp.substring(2));var lexer=new Lexer($parseOptions),parser=new Parser(lexer,$filter,$parseOptions);parsedExpression=parser.parse(exp),parsedExpression.constant?parsedExpression.$$watchDelegate=constantWatchDelegate:oneTime?(parsedExpression=wrapSharedExpression(parsedExpression),parsedExpression.$$watchDelegate=parsedExpression.literal?oneTimeLiteralWatchDelegate:oneTimeWatchDelegate):parsedExpression.inputs&&(parsedExpression.$$watchDelegate=inputsWatchDelegate),cache[cacheKey]=parsedExpression}return addInterceptor(parsedExpression,interceptorFn);case"function":return addInterceptor(exp,interceptorFn);default:return addInterceptor(noop,interceptorFn)}}}]}function $QProvider(){this.$get=["$rootScope","$exceptionHandler",function($rootScope,$exceptionHandler){return qFactory(function(callback){$rootScope.$evalAsync(callback)},$exceptionHandler)}]}function $$QProvider(){this.$get=["$browser","$exceptionHandler",function($browser,$exceptionHandler){return qFactory(function(callback){$browser.defer(callback)},$exceptionHandler)}]}function qFactory(nextTick,exceptionHandler){function callOnce(self,resolveFn,rejectFn){function wrap(fn){return function(value){called||(called=!0,fn.call(self,value))}}var called=!1;return[wrap(resolveFn),wrap(rejectFn)]}function Promise(){this.$$state={status:0}}function simpleBind(context,fn){return function(value){fn.call(context,value)}}function processQueue(state){var fn,promise,pending;pending=state.pending,state.processScheduled=!1,state.pending=undefined;for(var i=0,ii=pending.length;ii>i;++i){promise=pending[i][0],fn=pending[i][state.status];try{isFunction(fn)?promise.resolve(fn(state.value)):1===state.status?promise.resolve(state.value):promise.reject(state.value)}catch(e){promise.reject(e),exceptionHandler(e)}}}function scheduleProcessQueue(state){!state.processScheduled&&state.pending&&(state.processScheduled=!0,nextTick(function(){processQueue(state)}))}function Deferred(){this.promise=new Promise,this.resolve=simpleBind(this,this.resolve),this.reject=simpleBind(this,this.reject),this.notify=simpleBind(this,this.notify)}function all(promises){var deferred=new Deferred,counter=0,results=isArray(promises)?[]:{};return forEach(promises,function(promise,key){counter++,when(promise).then(function(value){results.hasOwnProperty(key)||(results[key]=value,--counter||deferred.resolve(results))},function(reason){results.hasOwnProperty(key)||deferred.reject(reason)})}),0===counter&&deferred.resolve(results),deferred.promise}var $qMinErr=minErr("$q",TypeError),defer=function(){return new Deferred};Promise.prototype={then:function(onFulfilled,onRejected,progressBack){var result=new Deferred;return this.$$state.pending=this.$$state.pending||[],this.$$state.pending.push([result,onFulfilled,onRejected,progressBack]),this.$$state.status>0&&scheduleProcessQueue(this.$$state),result.promise},"catch":function(callback){return this.then(null,callback)},"finally":function(callback,progressBack){return this.then(function(value){return handleCallback(value,!0,callback)},function(error){return handleCallback(error,!1,callback)},progressBack)}},Deferred.prototype={resolve:function(val){this.promise.$$state.status||(val===this.promise?this.$$reject($qMinErr("qcycle","Expected promise to be resolved with value other than itself '{0}'",val)):this.$$resolve(val))},$$resolve:function(val){var then,fns;fns=callOnce(this,this.$$resolve,this.$$reject);try{(isObject(val)||isFunction(val))&&(then=val&&val.then),isFunction(then)?(this.promise.$$state.status=-1,then.call(val,fns[0],fns[1],this.notify)):(this.promise.$$state.value=val,this.promise.$$state.status=1,scheduleProcessQueue(this.promise.$$state))}catch(e){fns[1](e),exceptionHandler(e)}},reject:function(reason){this.promise.$$state.status||this.$$reject(reason)},$$reject:function(reason){this.promise.$$state.value=reason,this.promise.$$state.status=2,scheduleProcessQueue(this.promise.$$state)},notify:function(progress){var callbacks=this.promise.$$state.pending;this.promise.$$state.status<=0&&callbacks&&callbacks.length&&nextTick(function(){for(var callback,result,i=0,ii=callbacks.length;ii>i;i++){result=callbacks[i][0],callback=callbacks[i][3];try{result.notify(isFunction(callback)?callback(progress):progress)}catch(e){exceptionHandler(e)}}})}};var reject=function(reason){var result=new Deferred;return result.reject(reason),result.promise},makePromise=function(value,resolved){var result=new Deferred;return resolved?result.resolve(value):result.reject(value),result.promise},handleCallback=function(value,isResolved,callback){var callbackOutput=null;try{isFunction(callback)&&(callbackOutput=callback())}catch(e){return makePromise(e,!1)}return isPromiseLike(callbackOutput)?callbackOutput.then(function(){return makePromise(value,isResolved)},function(error){return makePromise(error,!1)}):makePromise(value,isResolved)},when=function(value,callback,errback,progressBack){var result=new Deferred;return result.resolve(value),result.promise.then(callback,errback,progressBack)},$Q=function Q(resolver){function resolveFn(value){deferred.resolve(value)}function rejectFn(reason){deferred.reject(reason)}if(!isFunction(resolver))throw $qMinErr("norslvr","Expected resolverFn, got '{0}'",resolver);if(!(this instanceof Q))return new Q(resolver);var deferred=new Deferred;return resolver(resolveFn,rejectFn),deferred.promise};return $Q.defer=defer,$Q.reject=reject,$Q.when=when,$Q.all=all,$Q}function $$RAFProvider(){this.$get=["$window","$timeout",function($window,$timeout){var requestAnimationFrame=$window.requestAnimationFrame||$window.webkitRequestAnimationFrame||$window.mozRequestAnimationFrame,cancelAnimationFrame=$window.cancelAnimationFrame||$window.webkitCancelAnimationFrame||$window.mozCancelAnimationFrame||$window.webkitCancelRequestAnimationFrame,rafSupported=!!requestAnimationFrame,raf=rafSupported?function(fn){var id=requestAnimationFrame(fn);return function(){cancelAnimationFrame(id)}}:function(fn){var timer=$timeout(fn,16.66,!1);return function(){$timeout.cancel(timer)}};return raf.supported=rafSupported,raf}]}function $RootScopeProvider(){var TTL=10,$rootScopeMinErr=minErr("$rootScope"),lastDirtyWatch=null,applyAsyncId=null;this.digestTtl=function(value){return arguments.length&&(TTL=value),TTL},this.$get=["$injector","$exceptionHandler","$parse","$browser",function($injector,$exceptionHandler,$parse,$browser){function Scope(){this.$id=nextUid(),this.$$phase=this.$parent=this.$$watchers=this.$$nextSibling=this.$$prevSibling=this.$$childHead=this.$$childTail=null,this.$root=this,this.$$destroyed=!1,this.$$listeners={},this.$$listenerCount={},this.$$isolateBindings=null}function beginPhase(phase){if($rootScope.$$phase)throw $rootScopeMinErr("inprog","{0} already in progress",$rootScope.$$phase);$rootScope.$$phase=phase}function clearPhase(){$rootScope.$$phase=null}function decrementListenerCount(current,count,name){do current.$$listenerCount[name]-=count,0===current.$$listenerCount[name]&&delete current.$$listenerCount[name];while(current=current.$parent)}function initWatchVal(){}function flushApplyAsync(){for(;applyAsyncQueue.length;)try{applyAsyncQueue.shift()()}catch(e){$exceptionHandler(e)}applyAsyncId=null}function scheduleApplyAsync(){null===applyAsyncId&&(applyAsyncId=$browser.defer(function(){$rootScope.$apply(flushApplyAsync)}))}Scope.prototype={constructor:Scope,$new:function(isolate,parent){function destroyChild(){child.$$destroyed=!0}var child;return parent=parent||this,isolate?(child=new Scope,child.$root=this.$root):(this.$$ChildScope||(this.$$ChildScope=function(){this.$$watchers=this.$$nextSibling=this.$$childHead=this.$$childTail=null,this.$$listeners={},this.$$listenerCount={},this.$id=nextUid(),this.$$ChildScope=null},this.$$ChildScope.prototype=this),child=new this.$$ChildScope),child.$parent=parent,child.$$prevSibling=parent.$$childTail,parent.$$childHead?(parent.$$childTail.$$nextSibling=child,parent.$$childTail=child):parent.$$childHead=parent.$$childTail=child,(isolate||parent!=this)&&child.$on("$destroy",destroyChild),child},$watch:function(watchExp,listener,objectEquality){var get=$parse(watchExp);if(get.$$watchDelegate)return get.$$watchDelegate(this,listener,objectEquality,get);var scope=this,array=scope.$$watchers,watcher={fn:listener,last:initWatchVal,get:get,exp:watchExp,eq:!!objectEquality};return lastDirtyWatch=null,isFunction(listener)||(watcher.fn=noop),array||(array=scope.$$watchers=[]),array.unshift(watcher),function(){arrayRemove(array,watcher),lastDirtyWatch=null}},$watchGroup:function(watchExpressions,listener){function watchGroupAction(){changeReactionScheduled=!1,firstRun?(firstRun=!1,listener(newValues,newValues,self)):listener(newValues,oldValues,self)}var oldValues=new Array(watchExpressions.length),newValues=new Array(watchExpressions.length),deregisterFns=[],self=this,changeReactionScheduled=!1,firstRun=!0;if(!watchExpressions.length){var shouldCall=!0;return self.$evalAsync(function(){shouldCall&&listener(newValues,newValues,self)}),function(){shouldCall=!1}}return 1===watchExpressions.length?this.$watch(watchExpressions[0],function(value,oldValue,scope){newValues[0]=value,oldValues[0]=oldValue,listener(newValues,value===oldValue?newValues:oldValues,scope)}):(forEach(watchExpressions,function(expr,i){var unwatchFn=self.$watch(expr,function(value,oldValue){newValues[i]=value,oldValues[i]=oldValue,changeReactionScheduled||(changeReactionScheduled=!0,self.$evalAsync(watchGroupAction))});deregisterFns.push(unwatchFn)}),function(){for(;deregisterFns.length;)deregisterFns.shift()()})},$watchCollection:function(obj,listener){function $watchCollectionInterceptor(_value){newValue=_value;var newLength,key,bothNaN,newItem,oldItem;if(isObject(newValue))if(isArrayLike(newValue)){oldValue!==internalArray&&(oldValue=internalArray,oldLength=oldValue.length=0,changeDetected++),newLength=newValue.length,oldLength!==newLength&&(changeDetected++,oldValue.length=oldLength=newLength);for(var i=0;newLength>i;i++)oldItem=oldValue[i],newItem=newValue[i],bothNaN=oldItem!==oldItem&&newItem!==newItem,bothNaN||oldItem===newItem||(changeDetected++,oldValue[i]=newItem)}else{oldValue!==internalObject&&(oldValue=internalObject={},oldLength=0,changeDetected++),newLength=0;for(key in newValue)newValue.hasOwnProperty(key)&&(newLength++,newItem=newValue[key],oldItem=oldValue[key],key in oldValue?(bothNaN=oldItem!==oldItem&&newItem!==newItem,bothNaN||oldItem===newItem||(changeDetected++,oldValue[key]=newItem)):(oldLength++,oldValue[key]=newItem,changeDetected++));if(oldLength>newLength){changeDetected++;for(key in oldValue)newValue.hasOwnProperty(key)||(oldLength--,delete oldValue[key])}}else oldValue!==newValue&&(oldValue=newValue,changeDetected++);return changeDetected}function $watchCollectionAction(){if(initRun?(initRun=!1,listener(newValue,newValue,self)):listener(newValue,veryOldValue,self),trackVeryOldValue)if(isObject(newValue))if(isArrayLike(newValue)){veryOldValue=new Array(newValue.length);for(var i=0;i<newValue.length;i++)veryOldValue[i]=newValue[i]}else{veryOldValue={};for(var key in newValue)hasOwnProperty.call(newValue,key)&&(veryOldValue[key]=newValue[key])}else veryOldValue=newValue}$watchCollectionInterceptor.$stateful=!0;var newValue,oldValue,veryOldValue,self=this,trackVeryOldValue=listener.length>1,changeDetected=0,changeDetector=$parse(obj,$watchCollectionInterceptor),internalArray=[],internalObject={},initRun=!0,oldLength=0;return this.$watch(changeDetector,$watchCollectionAction)},$digest:function(){var watch,value,last,watchers,length,dirty,next,current,logIdx,logMsg,asyncTask,ttl=TTL,target=this,watchLog=[];beginPhase("$digest"),$browser.$$checkUrlChange(),this===$rootScope&&null!==applyAsyncId&&($browser.defer.cancel(applyAsyncId),flushApplyAsync()),lastDirtyWatch=null;do{for(dirty=!1,current=target;asyncQueue.length;){try{asyncTask=asyncQueue.shift(),asyncTask.scope.$eval(asyncTask.expression)}catch(e){$exceptionHandler(e)}lastDirtyWatch=null}traverseScopesLoop:do{if(watchers=current.$$watchers)for(length=watchers.length;length--;)try{if(watch=watchers[length])if((value=watch.get(current))===(last=watch.last)||(watch.eq?equals(value,last):"number"==typeof value&&"number"==typeof last&&isNaN(value)&&isNaN(last))){if(watch===lastDirtyWatch){dirty=!1;break traverseScopesLoop}}else dirty=!0,lastDirtyWatch=watch,watch.last=watch.eq?copy(value,null):value,watch.fn(value,last===initWatchVal?value:last,current),5>ttl&&(logIdx=4-ttl,watchLog[logIdx]||(watchLog[logIdx]=[]),logMsg=isFunction(watch.exp)?"fn: "+(watch.exp.name||watch.exp.toString()):watch.exp,logMsg+="; newVal: "+toJson(value)+"; oldVal: "+toJson(last),watchLog[logIdx].push(logMsg))}catch(e){$exceptionHandler(e)
}if(!(next=current.$$childHead||current!==target&&current.$$nextSibling))for(;current!==target&&!(next=current.$$nextSibling);)current=current.$parent}while(current=next);if((dirty||asyncQueue.length)&&!ttl--)throw clearPhase(),$rootScopeMinErr("infdig","{0} $digest() iterations reached. Aborting!\nWatchers fired in the last 5 iterations: {1}",TTL,toJson(watchLog))}while(dirty||asyncQueue.length);for(clearPhase();postDigestQueue.length;)try{postDigestQueue.shift()()}catch(e){$exceptionHandler(e)}},$destroy:function(){if(!this.$$destroyed){var parent=this.$parent;if(this.$broadcast("$destroy"),this.$$destroyed=!0,this!==$rootScope){for(var eventName in this.$$listenerCount)decrementListenerCount(this,this.$$listenerCount[eventName],eventName);parent.$$childHead==this&&(parent.$$childHead=this.$$nextSibling),parent.$$childTail==this&&(parent.$$childTail=this.$$prevSibling),this.$$prevSibling&&(this.$$prevSibling.$$nextSibling=this.$$nextSibling),this.$$nextSibling&&(this.$$nextSibling.$$prevSibling=this.$$prevSibling),this.$destroy=this.$digest=this.$apply=this.$evalAsync=this.$applyAsync=noop,this.$on=this.$watch=this.$watchGroup=function(){return noop},this.$$listeners={},this.$parent=this.$$nextSibling=this.$$prevSibling=this.$$childHead=this.$$childTail=this.$root=this.$$watchers=null}}},$eval:function(expr,locals){return $parse(expr)(this,locals)},$evalAsync:function(expr){$rootScope.$$phase||asyncQueue.length||$browser.defer(function(){asyncQueue.length&&$rootScope.$digest()}),asyncQueue.push({scope:this,expression:expr})},$$postDigest:function(fn){postDigestQueue.push(fn)},$apply:function(expr){try{return beginPhase("$apply"),this.$eval(expr)}catch(e){$exceptionHandler(e)}finally{clearPhase();try{$rootScope.$digest()}catch(e){throw $exceptionHandler(e),e}}},$applyAsync:function(expr){function $applyAsyncExpression(){scope.$eval(expr)}var scope=this;expr&&applyAsyncQueue.push($applyAsyncExpression),scheduleApplyAsync()},$on:function(name,listener){var namedListeners=this.$$listeners[name];namedListeners||(this.$$listeners[name]=namedListeners=[]),namedListeners.push(listener);var current=this;do current.$$listenerCount[name]||(current.$$listenerCount[name]=0),current.$$listenerCount[name]++;while(current=current.$parent);var self=this;return function(){namedListeners[namedListeners.indexOf(listener)]=null,decrementListenerCount(self,1,name)}},$emit:function(name){var namedListeners,i,length,empty=[],scope=this,stopPropagation=!1,event={name:name,targetScope:scope,stopPropagation:function(){stopPropagation=!0},preventDefault:function(){event.defaultPrevented=!0},defaultPrevented:!1},listenerArgs=concat([event],arguments,1);do{for(namedListeners=scope.$$listeners[name]||empty,event.currentScope=scope,i=0,length=namedListeners.length;length>i;i++)if(namedListeners[i])try{namedListeners[i].apply(null,listenerArgs)}catch(e){$exceptionHandler(e)}else namedListeners.splice(i,1),i--,length--;if(stopPropagation)return event.currentScope=null,event;scope=scope.$parent}while(scope);return event.currentScope=null,event},$broadcast:function(name){var target=this,current=target,next=target,event={name:name,targetScope:target,preventDefault:function(){event.defaultPrevented=!0},defaultPrevented:!1};if(!target.$$listenerCount[name])return event;for(var listeners,i,length,listenerArgs=concat([event],arguments,1);current=next;){for(event.currentScope=current,listeners=current.$$listeners[name]||[],i=0,length=listeners.length;length>i;i++)if(listeners[i])try{listeners[i].apply(null,listenerArgs)}catch(e){$exceptionHandler(e)}else listeners.splice(i,1),i--,length--;if(!(next=current.$$listenerCount[name]&&current.$$childHead||current!==target&&current.$$nextSibling))for(;current!==target&&!(next=current.$$nextSibling);)current=current.$parent}return event.currentScope=null,event}};var $rootScope=new Scope,asyncQueue=$rootScope.$$asyncQueue=[],postDigestQueue=$rootScope.$$postDigestQueue=[],applyAsyncQueue=$rootScope.$$applyAsyncQueue=[];return $rootScope}]}function $$SanitizeUriProvider(){var aHrefSanitizationWhitelist=/^\s*(https?|ftp|mailto|tel|file):/,imgSrcSanitizationWhitelist=/^\s*((https?|ftp|file|blob):|data:image\/)/;this.aHrefSanitizationWhitelist=function(regexp){return isDefined(regexp)?(aHrefSanitizationWhitelist=regexp,this):aHrefSanitizationWhitelist},this.imgSrcSanitizationWhitelist=function(regexp){return isDefined(regexp)?(imgSrcSanitizationWhitelist=regexp,this):imgSrcSanitizationWhitelist},this.$get=function(){return function(uri,isImage){var normalizedVal,regex=isImage?imgSrcSanitizationWhitelist:aHrefSanitizationWhitelist;return msie&&!(msie>=8)||(normalizedVal=urlResolve(uri).href,""===normalizedVal||normalizedVal.match(regex))?uri:"unsafe:"+normalizedVal}}}function escapeForRegexp(s){return s.replace(/([-()\[\]{}+?*.$\^|,:#<!\\])/g,"\\$1").replace(/\x08/g,"\\x08")}function adjustMatcher(matcher){if("self"===matcher)return matcher;if(isString(matcher)){if(matcher.indexOf("***")>-1)throw $sceMinErr("iwcard","Illegal sequence *** in string matcher.  String: {0}",matcher);return matcher=escapeForRegexp(matcher).replace("\\*\\*",".*").replace("\\*","[^:/.?&;]*"),new RegExp("^"+matcher+"$")}if(isRegExp(matcher))return new RegExp("^"+matcher.source+"$");throw $sceMinErr("imatcher",'Matchers may only be "self", string patterns or RegExp objects')}function adjustMatchers(matchers){var adjustedMatchers=[];return isDefined(matchers)&&forEach(matchers,function(matcher){adjustedMatchers.push(adjustMatcher(matcher))}),adjustedMatchers}function $SceDelegateProvider(){this.SCE_CONTEXTS=SCE_CONTEXTS;var resourceUrlWhitelist=["self"],resourceUrlBlacklist=[];this.resourceUrlWhitelist=function(value){return arguments.length&&(resourceUrlWhitelist=adjustMatchers(value)),resourceUrlWhitelist},this.resourceUrlBlacklist=function(value){return arguments.length&&(resourceUrlBlacklist=adjustMatchers(value)),resourceUrlBlacklist},this.$get=["$injector",function($injector){function matchUrl(matcher,parsedUrl){return"self"===matcher?urlIsSameOrigin(parsedUrl):!!matcher.exec(parsedUrl.href)}function isResourceUrlAllowedByPolicy(url){var i,n,parsedUrl=urlResolve(url.toString()),allowed=!1;for(i=0,n=resourceUrlWhitelist.length;n>i;i++)if(matchUrl(resourceUrlWhitelist[i],parsedUrl)){allowed=!0;break}if(allowed)for(i=0,n=resourceUrlBlacklist.length;n>i;i++)if(matchUrl(resourceUrlBlacklist[i],parsedUrl)){allowed=!1;break}return allowed}function generateHolderType(Base){var holderType=function(trustedValue){this.$$unwrapTrustedValue=function(){return trustedValue}};return Base&&(holderType.prototype=new Base),holderType.prototype.valueOf=function(){return this.$$unwrapTrustedValue()},holderType.prototype.toString=function(){return this.$$unwrapTrustedValue().toString()},holderType}function trustAs(type,trustedValue){var Constructor=byType.hasOwnProperty(type)?byType[type]:null;if(!Constructor)throw $sceMinErr("icontext","Attempted to trust a value in invalid context. Context: {0}; Value: {1}",type,trustedValue);if(null===trustedValue||trustedValue===undefined||""===trustedValue)return trustedValue;if("string"!=typeof trustedValue)throw $sceMinErr("itype","Attempted to trust a non-string value in a content requiring a string: Context: {0}",type);return new Constructor(trustedValue)}function valueOf(maybeTrusted){return maybeTrusted instanceof trustedValueHolderBase?maybeTrusted.$$unwrapTrustedValue():maybeTrusted}function getTrusted(type,maybeTrusted){if(null===maybeTrusted||maybeTrusted===undefined||""===maybeTrusted)return maybeTrusted;var constructor=byType.hasOwnProperty(type)?byType[type]:null;if(constructor&&maybeTrusted instanceof constructor)return maybeTrusted.$$unwrapTrustedValue();if(type===SCE_CONTEXTS.RESOURCE_URL){if(isResourceUrlAllowedByPolicy(maybeTrusted))return maybeTrusted;throw $sceMinErr("insecurl","Blocked loading resource from url not allowed by $sceDelegate policy.  URL: {0}",maybeTrusted.toString())}if(type===SCE_CONTEXTS.HTML)return htmlSanitizer(maybeTrusted);throw $sceMinErr("unsafe","Attempting to use an unsafe value in a safe context.")}var htmlSanitizer=function(){throw $sceMinErr("unsafe","Attempting to use an unsafe value in a safe context.")};$injector.has("$sanitize")&&(htmlSanitizer=$injector.get("$sanitize"));var trustedValueHolderBase=generateHolderType(),byType={};return byType[SCE_CONTEXTS.HTML]=generateHolderType(trustedValueHolderBase),byType[SCE_CONTEXTS.CSS]=generateHolderType(trustedValueHolderBase),byType[SCE_CONTEXTS.URL]=generateHolderType(trustedValueHolderBase),byType[SCE_CONTEXTS.JS]=generateHolderType(trustedValueHolderBase),byType[SCE_CONTEXTS.RESOURCE_URL]=generateHolderType(byType[SCE_CONTEXTS.URL]),{trustAs:trustAs,getTrusted:getTrusted,valueOf:valueOf}}]}function $SceProvider(){var enabled=!0;this.enabled=function(value){return arguments.length&&(enabled=!!value),enabled},this.$get=["$parse","$sniffer","$sceDelegate",function($parse,$sniffer,$sceDelegate){if(enabled&&$sniffer.msie&&$sniffer.msieDocumentMode<8)throw $sceMinErr("iequirks","Strict Contextual Escaping does not support Internet Explorer version < 9 in quirks mode.  You can fix this by adding the text <!doctype html> to the top of your HTML document.  See http://docs.angularjs.org/api/ng.$sce for more information.");var sce=shallowCopy(SCE_CONTEXTS);sce.isEnabled=function(){return enabled},sce.trustAs=$sceDelegate.trustAs,sce.getTrusted=$sceDelegate.getTrusted,sce.valueOf=$sceDelegate.valueOf,enabled||(sce.trustAs=sce.getTrusted=function(type,value){return value},sce.valueOf=identity),sce.parseAs=function(type,expr){var parsed=$parse(expr);return parsed.literal&&parsed.constant?parsed:$parse(expr,function(value){return sce.getTrusted(type,value)})};var parse=sce.parseAs,getTrusted=sce.getTrusted,trustAs=sce.trustAs;return forEach(SCE_CONTEXTS,function(enumValue,name){var lName=lowercase(name);sce[camelCase("parse_as_"+lName)]=function(expr){return parse(enumValue,expr)},sce[camelCase("get_trusted_"+lName)]=function(value){return getTrusted(enumValue,value)},sce[camelCase("trust_as_"+lName)]=function(value){return trustAs(enumValue,value)}}),sce}]}function $SnifferProvider(){this.$get=["$window","$document",function($window,$document){var vendorPrefix,match,eventSupport={},android=int((/android (\d+)/.exec(lowercase(($window.navigator||{}).userAgent))||[])[1]),boxee=/Boxee/i.test(($window.navigator||{}).userAgent),document=$document[0]||{},documentMode=document.documentMode,vendorRegex=/^(Moz|webkit|O|ms)(?=[A-Z])/,bodyStyle=document.body&&document.body.style,transitions=!1,animations=!1;if(bodyStyle){for(var prop in bodyStyle)if(match=vendorRegex.exec(prop)){vendorPrefix=match[0],vendorPrefix=vendorPrefix.substr(0,1).toUpperCase()+vendorPrefix.substr(1);break}vendorPrefix||(vendorPrefix="WebkitOpacity"in bodyStyle&&"webkit"),transitions=!!("transition"in bodyStyle||vendorPrefix+"Transition"in bodyStyle),animations=!!("animation"in bodyStyle||vendorPrefix+"Animation"in bodyStyle),!android||transitions&&animations||(transitions=isString(document.body.style.webkitTransition),animations=isString(document.body.style.webkitAnimation))}return{history:!(!$window.history||!$window.history.pushState||4>android||boxee),hashchange:"onhashchange"in $window&&(!documentMode||documentMode>7),hasEvent:function(event){if("input"==event&&9==msie)return!1;if(isUndefined(eventSupport[event])){var divElm=document.createElement("div");eventSupport[event]="on"+event in divElm}return eventSupport[event]},csp:csp(),vendorPrefix:vendorPrefix,transitions:transitions,animations:animations,android:android,msie:msie,msieDocumentMode:documentMode}}]}function $TemplateRequestProvider(){this.$get=["$templateCache","$http","$q",function($templateCache,$http,$q){function handleRequestFn(tpl,ignoreRequestError){function handleError(){if(self.totalPendingRequests--,!ignoreRequestError)throw $compileMinErr("tpload","Failed to load template: {0}",tpl);return $q.reject()}var self=handleRequestFn;return self.totalPendingRequests++,$http.get(tpl,{cache:$templateCache}).then(function(response){var html=response.data;return html&&0!==html.length?(self.totalPendingRequests--,$templateCache.put(tpl,html),html):handleError()},handleError)}return handleRequestFn.totalPendingRequests=0,handleRequestFn}]}function $$TestabilityProvider(){this.$get=["$rootScope","$browser","$location",function($rootScope,$browser,$location){var testability={};return testability.findBindings=function(element,expression,opt_exactMatch){var bindings=element.getElementsByClassName("ng-binding"),matches=[];return forEach(bindings,function(binding){var dataBinding=angular.element(binding).data("$binding");dataBinding&&forEach(dataBinding,function(bindingName){if(opt_exactMatch){var matcher=new RegExp("(^|\\s)"+expression+"(\\s|\\||$)");matcher.test(bindingName)&&matches.push(binding)}else-1!=bindingName.indexOf(expression)&&matches.push(binding)})}),matches},testability.findModels=function(element,expression,opt_exactMatch){for(var prefixes=["ng-","data-ng-","ng\\:"],p=0;p<prefixes.length;++p){var attributeEquals=opt_exactMatch?"=":"*=",selector="["+prefixes[p]+"model"+attributeEquals+'"'+expression+'"]',elements=element.querySelectorAll(selector);if(elements.length)return elements}},testability.getLocation=function(){return $location.url()},testability.setLocation=function(url){url!==$location.url()&&($location.url(url),$rootScope.$digest())},testability.whenStable=function(callback){$browser.notifyWhenNoOutstandingRequests(callback)},testability}]}function $TimeoutProvider(){this.$get=["$rootScope","$browser","$q","$$q","$exceptionHandler",function($rootScope,$browser,$q,$$q,$exceptionHandler){function timeout(fn,delay,invokeApply){var timeoutId,skipApply=isDefined(invokeApply)&&!invokeApply,deferred=(skipApply?$$q:$q).defer(),promise=deferred.promise;return timeoutId=$browser.defer(function(){try{deferred.resolve(fn())}catch(e){deferred.reject(e),$exceptionHandler(e)}finally{delete deferreds[promise.$$timeoutId]}skipApply||$rootScope.$apply()},delay),promise.$$timeoutId=timeoutId,deferreds[timeoutId]=deferred,promise}var deferreds={};return timeout.cancel=function(promise){return promise&&promise.$$timeoutId in deferreds?(deferreds[promise.$$timeoutId].reject("canceled"),delete deferreds[promise.$$timeoutId],$browser.defer.cancel(promise.$$timeoutId)):!1},timeout}]}function urlResolve(url){var href=url;return msie&&(urlParsingNode.setAttribute("href",href),href=urlParsingNode.href),urlParsingNode.setAttribute("href",href),{href:urlParsingNode.href,protocol:urlParsingNode.protocol?urlParsingNode.protocol.replace(/:$/,""):"",host:urlParsingNode.host,search:urlParsingNode.search?urlParsingNode.search.replace(/^\?/,""):"",hash:urlParsingNode.hash?urlParsingNode.hash.replace(/^#/,""):"",hostname:urlParsingNode.hostname,port:urlParsingNode.port,pathname:"/"===urlParsingNode.pathname.charAt(0)?urlParsingNode.pathname:"/"+urlParsingNode.pathname}}function urlIsSameOrigin(requestUrl){var parsed=isString(requestUrl)?urlResolve(requestUrl):requestUrl;return parsed.protocol===originUrl.protocol&&parsed.host===originUrl.host}function $WindowProvider(){this.$get=valueFn(window)}function $FilterProvider($provide){function register(name,factory){if(isObject(name)){var filters={};return forEach(name,function(filter,key){filters[key]=register(key,filter)}),filters}return $provide.factory(name+suffix,factory)}var suffix="Filter";this.register=register,this.$get=["$injector",function($injector){return function(name){return $injector.get(name+suffix)}}],register("currency",currencyFilter),register("date",dateFilter),register("filter",filterFilter),register("json",jsonFilter),register("limitTo",limitToFilter),register("lowercase",lowercaseFilter),register("number",numberFilter),register("orderBy",orderByFilter),register("uppercase",uppercaseFilter)}function filterFilter(){return function(array,expression,comparator){if(!isArray(array))return array;var comparatorType=typeof comparator,predicates=[];predicates.check=function(value,index){for(var j=0;j<predicates.length;j++)if(!predicates[j](value,index))return!1;return!0},"function"!==comparatorType&&(comparator="boolean"===comparatorType&&comparator?function(obj,text){return angular.equals(obj,text)}:function(obj,text){if(obj&&text&&"object"==typeof obj&&"object"==typeof text){for(var objKey in obj)if("$"!==objKey.charAt(0)&&hasOwnProperty.call(obj,objKey)&&comparator(obj[objKey],text[objKey]))return!0;return!1}return text=(""+text).toLowerCase(),(""+obj).toLowerCase().indexOf(text)>-1});var search=function(obj,text){if("string"==typeof text&&"!"===text.charAt(0))return!search(obj,text.substr(1));switch(typeof obj){case"boolean":case"number":case"string":return comparator(obj,text);case"object":switch(typeof text){case"object":return comparator(obj,text);default:for(var objKey in obj)if("$"!==objKey.charAt(0)&&search(obj[objKey],text))return!0}return!1;case"array":for(var i=0;i<obj.length;i++)if(search(obj[i],text))return!0;return!1;default:return!1}};switch(typeof expression){case"boolean":case"number":case"string":expression={$:expression};case"object":for(var key in expression)!function(path){"undefined"!=typeof expression[path]&&predicates.push(function(value){return search("$"==path?value:value&&value[path],expression[path])})}(key);break;case"function":predicates.push(expression);break;default:return array}for(var filtered=[],j=0;j<array.length;j++){var value=array[j];predicates.check(value,j)&&filtered.push(value)}return filtered}}function currencyFilter($locale){var formats=$locale.NUMBER_FORMATS;return function(amount,currencySymbol){return isUndefined(currencySymbol)&&(currencySymbol=formats.CURRENCY_SYM),null==amount?amount:formatNumber(amount,formats.PATTERNS[1],formats.GROUP_SEP,formats.DECIMAL_SEP,2).replace(/\u00A4/g,currencySymbol)}}function numberFilter($locale){var formats=$locale.NUMBER_FORMATS;return function(number,fractionSize){return null==number?number:formatNumber(number,formats.PATTERNS[0],formats.GROUP_SEP,formats.DECIMAL_SEP,fractionSize)}}function formatNumber(number,pattern,groupSep,decimalSep,fractionSize){if(!isFinite(number)||isObject(number))return"";var isNegative=0>number;number=Math.abs(number);var numStr=number+"",formatedText="",parts=[],hasExponent=!1;if(-1!==numStr.indexOf("e")){var match=numStr.match(/([\d\.]+)e(-?)(\d+)/);match&&"-"==match[2]&&match[3]>fractionSize+1?(numStr="0",number=0):(formatedText=numStr,hasExponent=!0)}if(hasExponent)fractionSize>0&&number>-1&&1>number&&(formatedText=number.toFixed(fractionSize));else{var fractionLen=(numStr.split(DECIMAL_SEP)[1]||"").length;isUndefined(fractionSize)&&(fractionSize=Math.min(Math.max(pattern.minFrac,fractionLen),pattern.maxFrac)),number=+(Math.round(+(number.toString()+"e"+fractionSize)).toString()+"e"+-fractionSize),0===number&&(isNegative=!1);var fraction=(""+number).split(DECIMAL_SEP),whole=fraction[0];fraction=fraction[1]||"";var i,pos=0,lgroup=pattern.lgSize,group=pattern.gSize;if(whole.length>=lgroup+group)for(pos=whole.length-lgroup,i=0;pos>i;i++)(pos-i)%group===0&&0!==i&&(formatedText+=groupSep),formatedText+=whole.charAt(i);for(i=pos;i<whole.length;i++)(whole.length-i)%lgroup===0&&0!==i&&(formatedText+=groupSep),formatedText+=whole.charAt(i);for(;fraction.length<fractionSize;)fraction+="0";fractionSize&&"0"!==fractionSize&&(formatedText+=decimalSep+fraction.substr(0,fractionSize))}return parts.push(isNegative?pattern.negPre:pattern.posPre),parts.push(formatedText),parts.push(isNegative?pattern.negSuf:pattern.posSuf),parts.join("")}function padNumber(num,digits,trim){var neg="";for(0>num&&(neg="-",num=-num),num=""+num;num.length<digits;)num="0"+num;return trim&&(num=num.substr(num.length-digits)),neg+num}function dateGetter(name,size,offset,trim){return offset=offset||0,function(date){var value=date["get"+name]();return(offset>0||value>-offset)&&(value+=offset),0===value&&-12==offset&&(value=12),padNumber(value,size,trim)}}function dateStrGetter(name,shortForm){return function(date,formats){var value=date["get"+name](),get=uppercase(shortForm?"SHORT"+name:name);return formats[get][value]}}function timeZoneGetter(date){var zone=-1*date.getTimezoneOffset(),paddedZone=zone>=0?"+":"";return paddedZone+=padNumber(Math[zone>0?"floor":"ceil"](zone/60),2)+padNumber(Math.abs(zone%60),2)}function getFirstThursdayOfYear(year){var dayOfWeekOnFirst=new Date(year,0,1).getDay();return new Date(year,0,(4>=dayOfWeekOnFirst?5:12)-dayOfWeekOnFirst)}function getThursdayThisWeek(datetime){return new Date(datetime.getFullYear(),datetime.getMonth(),datetime.getDate()+(4-datetime.getDay()))}function weekGetter(size){return function(date){var firstThurs=getFirstThursdayOfYear(date.getFullYear()),thisThurs=getThursdayThisWeek(date),diff=+thisThurs-+firstThurs,result=1+Math.round(diff/6048e5);return padNumber(result,size)}}function ampmGetter(date,formats){return date.getHours()<12?formats.AMPMS[0]:formats.AMPMS[1]}function dateFilter($locale){function jsonStringToDate(string){var match;if(match=string.match(R_ISO8601_STR)){var date=new Date(0),tzHour=0,tzMin=0,dateSetter=match[8]?date.setUTCFullYear:date.setFullYear,timeSetter=match[8]?date.setUTCHours:date.setHours;match[9]&&(tzHour=int(match[9]+match[10]),tzMin=int(match[9]+match[11])),dateSetter.call(date,int(match[1]),int(match[2])-1,int(match[3]));var h=int(match[4]||0)-tzHour,m=int(match[5]||0)-tzMin,s=int(match[6]||0),ms=Math.round(1e3*parseFloat("0."+(match[7]||0)));return timeSetter.call(date,h,m,s,ms),date}return string}var R_ISO8601_STR=/^(\d{4})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/;return function(date,format,timezone){var fn,match,text="",parts=[];if(format=format||"mediumDate",format=$locale.DATETIME_FORMATS[format]||format,isString(date)&&(date=NUMBER_STRING.test(date)?int(date):jsonStringToDate(date)),isNumber(date)&&(date=new Date(date)),!isDate(date))return date;for(;format;)match=DATE_FORMATS_SPLIT.exec(format),match?(parts=concat(parts,match,1),format=parts.pop()):(parts.push(format),format=null);return timezone&&"UTC"===timezone&&(date=new Date(date.getTime()),date.setMinutes(date.getMinutes()+date.getTimezoneOffset())),forEach(parts,function(value){fn=DATE_FORMATS[value],text+=fn?fn(date,$locale.DATETIME_FORMATS):value.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),text}}function jsonFilter(){return function(object){return toJson(object,!0)}}function limitToFilter(){return function(input,limit){if(isNumber(input)&&(input=input.toString()),!isArray(input)&&!isString(input))return input;if(limit=1/0===Math.abs(Number(limit))?Number(limit):int(limit),isString(input))return limit?limit>=0?input.slice(0,limit):input.slice(limit,input.length):"";var i,n,out=[];for(limit>input.length?limit=input.length:limit<-input.length&&(limit=-input.length),limit>0?(i=0,n=limit):(i=input.length+limit,n=input.length);n>i;i++)out.push(input[i]);return out}}function orderByFilter($parse){return function(array,sortPredicate,reverseOrder){function comparator(o1,o2){for(var i=0;i<sortPredicate.length;i++){var comp=sortPredicate[i](o1,o2);if(0!==comp)return comp}return 0}function reverseComparator(comp,descending){return descending?function(a,b){return comp(b,a)}:comp}function compare(v1,v2){var t1=typeof v1,t2=typeof v2;return t1==t2?(isDate(v1)&&isDate(v2)&&(v1=v1.valueOf(),v2=v2.valueOf()),"string"==t1&&(v1=v1.toLowerCase(),v2=v2.toLowerCase()),v1===v2?0:v2>v1?-1:1):t2>t1?-1:1}if(!isArrayLike(array))return array;if(!sortPredicate)return array;sortPredicate=isArray(sortPredicate)?sortPredicate:[sortPredicate],sortPredicate=sortPredicate.map(function(predicate){var descending=!1,get=predicate||identity;if(isString(predicate)&&(("+"==predicate.charAt(0)||"-"==predicate.charAt(0))&&(descending="-"==predicate.charAt(0),predicate=predicate.substring(1)),get=$parse(predicate),get.constant)){var key=get();return reverseComparator(function(a,b){return compare(a[key],b[key])},descending)}return reverseComparator(function(a,b){return compare(get(a),get(b))},descending)});for(var arrayCopy=[],i=0;i<array.length;i++)arrayCopy.push(array[i]);return arrayCopy.sort(reverseComparator(comparator,reverseOrder))}}function ngDirective(directive){return isFunction(directive)&&(directive={link:directive}),directive.restrict=directive.restrict||"AC",valueFn(directive)}function nullFormRenameControl(control,name){control.$name=name}function FormController(element,attrs,$scope,$animate,$interpolate){var form=this,controls=[],parentForm=form.$$parentForm=element.parent().controller("form")||nullFormCtrl;form.$error={},form.$$success={},form.$pending=undefined,form.$name=$interpolate(attrs.name||attrs.ngForm||"")($scope),form.$dirty=!1,form.$pristine=!0,form.$valid=!0,form.$invalid=!1,form.$submitted=!1,parentForm.$addControl(form),form.$rollbackViewValue=function(){forEach(controls,function(control){control.$rollbackViewValue()})},form.$commitViewValue=function(){forEach(controls,function(control){control.$commitViewValue()})},form.$addControl=function(control){assertNotHasOwnProperty(control.$name,"input"),controls.push(control),control.$name&&(form[control.$name]=control)},form.$$renameControl=function(control,newName){var oldName=control.$name;form[oldName]===control&&delete form[oldName],form[newName]=control,control.$name=newName},form.$removeControl=function(control){control.$name&&form[control.$name]===control&&delete form[control.$name],forEach(form.$pending,function(value,name){form.$setValidity(name,null,control)}),forEach(form.$error,function(value,name){form.$setValidity(name,null,control)}),arrayRemove(controls,control)},addSetValidityMethod({ctrl:this,$element:element,set:function(object,property,control){var list=object[property];if(list){var index=list.indexOf(control);-1===index&&list.push(control)}else object[property]=[control]},unset:function(object,property,control){var list=object[property];list&&(arrayRemove(list,control),0===list.length&&delete object[property])},parentForm:parentForm,$animate:$animate}),form.$setDirty=function(){$animate.removeClass(element,PRISTINE_CLASS),$animate.addClass(element,DIRTY_CLASS),form.$dirty=!0,form.$pristine=!1,parentForm.$setDirty()},form.$setPristine=function(){$animate.setClass(element,PRISTINE_CLASS,DIRTY_CLASS+" "+SUBMITTED_CLASS),form.$dirty=!1,form.$pristine=!0,form.$submitted=!1,forEach(controls,function(control){control.$setPristine()})},form.$setUntouched=function(){forEach(controls,function(control){control.$setUntouched()})},form.$setSubmitted=function(){$animate.addClass(element,SUBMITTED_CLASS),form.$submitted=!0,parentForm.$setSubmitted()}}function stringBasedInputType(ctrl){ctrl.$formatters.push(function(value){return ctrl.$isEmpty(value)?value:value.toString()})}function textInputType(scope,element,attr,ctrl,$sniffer,$browser){baseInputType(scope,element,attr,ctrl,$sniffer,$browser),stringBasedInputType(ctrl)}function baseInputType(scope,element,attr,ctrl,$sniffer,$browser){var placeholder=(element.prop(VALIDITY_STATE_PROPERTY),element[0].placeholder),noevent={},type=lowercase(element[0].type);if(!$sniffer.android){var composing=!1;element.on("compositionstart",function(){composing=!0}),element.on("compositionend",function(){composing=!1,listener()})}var listener=function(ev){if(!composing){var value=element.val(),event=ev&&ev.type;if(msie&&"input"===(ev||noevent).type&&element[0].placeholder!==placeholder)return void(placeholder=element[0].placeholder);"password"===type||attr.ngTrim&&"false"===attr.ngTrim||(value=trim(value)),(ctrl.$viewValue!==value||""===value&&ctrl.$$hasNativeValidators)&&ctrl.$setViewValue(value,event)}};if($sniffer.hasEvent("input"))element.on("input",listener);else{var timeout,deferListener=function(ev){timeout||(timeout=$browser.defer(function(){listener(ev),timeout=null}))};element.on("keydown",function(event){var key=event.keyCode;91===key||key>15&&19>key||key>=37&&40>=key||deferListener(event)}),$sniffer.hasEvent("paste")&&element.on("paste cut",deferListener)}element.on("change",listener),ctrl.$render=function(){element.val(ctrl.$isEmpty(ctrl.$modelValue)?"":ctrl.$viewValue)}}function weekParser(isoWeek,existingDate){if(isDate(isoWeek))return isoWeek;if(isString(isoWeek)){WEEK_REGEXP.lastIndex=0;var parts=WEEK_REGEXP.exec(isoWeek);if(parts){var year=+parts[1],week=+parts[2],hours=0,minutes=0,seconds=0,milliseconds=0,firstThurs=getFirstThursdayOfYear(year),addDays=7*(week-1);return existingDate&&(hours=existingDate.getHours(),minutes=existingDate.getMinutes(),seconds=existingDate.getSeconds(),milliseconds=existingDate.getMilliseconds()),new Date(year,0,firstThurs.getDate()+addDays,hours,minutes,seconds,milliseconds)}}return 0/0}function createDateParser(regexp,mapping){return function(iso,date){var parts,map;if(isDate(iso))return iso;if(isString(iso)){if('"'==iso.charAt(0)&&'"'==iso.charAt(iso.length-1)&&(iso=iso.substring(1,iso.length-1)),ISO_DATE_REGEXP.test(iso))return new Date(iso);if(regexp.lastIndex=0,parts=regexp.exec(iso))return parts.shift(),map=date?{yyyy:date.getFullYear(),MM:date.getMonth()+1,dd:date.getDate(),HH:date.getHours(),mm:date.getMinutes(),ss:date.getSeconds(),sss:date.getMilliseconds()/1e3}:{yyyy:1970,MM:1,dd:1,HH:0,mm:0,ss:0,sss:0},forEach(parts,function(part,index){index<mapping.length&&(map[mapping[index]]=+part)}),new Date(map.yyyy,map.MM-1,map.dd,map.HH,map.mm,map.ss||0,1e3*map.sss||0)}return 0/0}}function createDateInputType(type,regexp,parseDate,format){return function(scope,element,attr,ctrl,$sniffer,$browser,$filter){function parseObservedDateValue(val){return isDefined(val)?isDate(val)?val:parseDate(val):undefined}badInputChecker(scope,element,attr,ctrl),baseInputType(scope,element,attr,ctrl,$sniffer,$browser);var previousDate,timezone=ctrl&&ctrl.$options&&ctrl.$options.timezone;if(ctrl.$$parserName=type,ctrl.$parsers.push(function(value){if(ctrl.$isEmpty(value))return null;if(regexp.test(value)){var parsedDate=parseDate(value,previousDate);return"UTC"===timezone&&parsedDate.setMinutes(parsedDate.getMinutes()-parsedDate.getTimezoneOffset()),parsedDate}return undefined}),ctrl.$formatters.push(function(value){if(!ctrl.$isEmpty(value)){if(!isDate(value))throw $ngModelMinErr("datefmt","Expected `{0}` to be a date",value);if(previousDate=value,previousDate&&"UTC"===timezone){var timezoneOffset=6e4*previousDate.getTimezoneOffset();previousDate=new Date(previousDate.getTime()+timezoneOffset)}return $filter("date")(value,format,timezone)}return previousDate=null,""}),isDefined(attr.min)||attr.ngMin){var minVal;ctrl.$validators.min=function(value){return ctrl.$isEmpty(value)||isUndefined(minVal)||parseDate(value)>=minVal},attr.$observe("min",function(val){minVal=parseObservedDateValue(val),ctrl.$validate()})}if(isDefined(attr.max)||attr.ngMax){var maxVal;ctrl.$validators.max=function(value){return ctrl.$isEmpty(value)||isUndefined(maxVal)||parseDate(value)<=maxVal},attr.$observe("max",function(val){maxVal=parseObservedDateValue(val),ctrl.$validate()})}ctrl.$isEmpty=function(value){return!value||value.getTime&&value.getTime()!==value.getTime()}}}function badInputChecker(scope,element,attr,ctrl){var node=element[0],nativeValidation=ctrl.$$hasNativeValidators=isObject(node.validity);nativeValidation&&ctrl.$parsers.push(function(value){var validity=element.prop(VALIDITY_STATE_PROPERTY)||{};return validity.badInput&&!validity.typeMismatch?undefined:value})}function numberInputType(scope,element,attr,ctrl,$sniffer,$browser){if(badInputChecker(scope,element,attr,ctrl),baseInputType(scope,element,attr,ctrl,$sniffer,$browser),ctrl.$$parserName="number",ctrl.$parsers.push(function(value){return ctrl.$isEmpty(value)?null:NUMBER_REGEXP.test(value)?parseFloat(value):undefined}),ctrl.$formatters.push(function(value){if(!ctrl.$isEmpty(value)){if(!isNumber(value))throw $ngModelMinErr("numfmt","Expected `{0}` to be a number",value);value=value.toString()}return value}),attr.min||attr.ngMin){var minVal;ctrl.$validators.min=function(value){return ctrl.$isEmpty(value)||isUndefined(minVal)||value>=minVal
},attr.$observe("min",function(val){isDefined(val)&&!isNumber(val)&&(val=parseFloat(val,10)),minVal=isNumber(val)&&!isNaN(val)?val:undefined,ctrl.$validate()})}if(attr.max||attr.ngMax){var maxVal;ctrl.$validators.max=function(value){return ctrl.$isEmpty(value)||isUndefined(maxVal)||maxVal>=value},attr.$observe("max",function(val){isDefined(val)&&!isNumber(val)&&(val=parseFloat(val,10)),maxVal=isNumber(val)&&!isNaN(val)?val:undefined,ctrl.$validate()})}}function urlInputType(scope,element,attr,ctrl,$sniffer,$browser){baseInputType(scope,element,attr,ctrl,$sniffer,$browser),stringBasedInputType(ctrl),ctrl.$$parserName="url",ctrl.$validators.url=function(value){return ctrl.$isEmpty(value)||URL_REGEXP.test(value)}}function emailInputType(scope,element,attr,ctrl,$sniffer,$browser){baseInputType(scope,element,attr,ctrl,$sniffer,$browser),stringBasedInputType(ctrl),ctrl.$$parserName="email",ctrl.$validators.email=function(value){return ctrl.$isEmpty(value)||EMAIL_REGEXP.test(value)}}function radioInputType(scope,element,attr,ctrl){isUndefined(attr.name)&&element.attr("name",nextUid());var listener=function(ev){element[0].checked&&ctrl.$setViewValue(attr.value,ev&&ev.type)};element.on("click",listener),ctrl.$render=function(){var value=attr.value;element[0].checked=value==ctrl.$viewValue},attr.$observe("value",ctrl.$render)}function parseConstantExpr($parse,context,name,expression,fallback){var parseFn;if(isDefined(expression)){if(parseFn=$parse(expression),!parseFn.constant)throw minErr("ngModel")("constexpr","Expected constant expression for `{0}`, but saw `{1}`.",name,expression);return parseFn(context)}return fallback}function checkboxInputType(scope,element,attr,ctrl,$sniffer,$browser,$filter,$parse){var trueValue=parseConstantExpr($parse,scope,"ngTrueValue",attr.ngTrueValue,!0),falseValue=parseConstantExpr($parse,scope,"ngFalseValue",attr.ngFalseValue,!1),listener=function(ev){ctrl.$setViewValue(element[0].checked,ev&&ev.type)};element.on("click",listener),ctrl.$render=function(){element[0].checked=ctrl.$viewValue},ctrl.$isEmpty=function(value){return value!==trueValue},ctrl.$formatters.push(function(value){return equals(value,trueValue)}),ctrl.$parsers.push(function(value){return value?trueValue:falseValue})}function addSetValidityMethod(context){function setValidity(validationErrorKey,state,options){state===undefined?createAndSet("$pending",validationErrorKey,options):unsetAndCleanup("$pending",validationErrorKey,options),isBoolean(state)?state?(unset(ctrl.$error,validationErrorKey,options),set(ctrl.$$success,validationErrorKey,options)):(set(ctrl.$error,validationErrorKey,options),unset(ctrl.$$success,validationErrorKey,options)):(unset(ctrl.$error,validationErrorKey,options),unset(ctrl.$$success,validationErrorKey,options)),ctrl.$pending?(cachedToggleClass(PENDING_CLASS,!0),ctrl.$valid=ctrl.$invalid=undefined,toggleValidationCss("",null)):(cachedToggleClass(PENDING_CLASS,!1),ctrl.$valid=isObjectEmpty(ctrl.$error),ctrl.$invalid=!ctrl.$valid,toggleValidationCss("",ctrl.$valid));var combinedState;combinedState=ctrl.$pending&&ctrl.$pending[validationErrorKey]?undefined:ctrl.$error[validationErrorKey]?!1:ctrl.$$success[validationErrorKey]?!0:null,toggleValidationCss(validationErrorKey,combinedState),parentForm.$setValidity(validationErrorKey,combinedState,ctrl)}function createAndSet(name,value,options){ctrl[name]||(ctrl[name]={}),set(ctrl[name],value,options)}function unsetAndCleanup(name,value,options){ctrl[name]&&unset(ctrl[name],value,options),isObjectEmpty(ctrl[name])&&(ctrl[name]=undefined)}function cachedToggleClass(className,switchValue){switchValue&&!classCache[className]?($animate.addClass($element,className),classCache[className]=!0):!switchValue&&classCache[className]&&($animate.removeClass($element,className),classCache[className]=!1)}function toggleValidationCss(validationErrorKey,isValid){validationErrorKey=validationErrorKey?"-"+snake_case(validationErrorKey,"-"):"",cachedToggleClass(VALID_CLASS+validationErrorKey,isValid===!0),cachedToggleClass(INVALID_CLASS+validationErrorKey,isValid===!1)}var ctrl=context.ctrl,$element=context.$element,classCache={},set=context.set,unset=context.unset,parentForm=context.parentForm,$animate=context.$animate;classCache[INVALID_CLASS]=!(classCache[VALID_CLASS]=$element.hasClass(VALID_CLASS)),ctrl.$setValidity=setValidity}function isObjectEmpty(obj){if(obj)for(var prop in obj)return!1;return!0}function classDirective(name,selector){return name="ngClass"+name,["$animate",function($animate){function arrayDifference(tokens1,tokens2){var values=[];outer:for(var i=0;i<tokens1.length;i++){for(var token=tokens1[i],j=0;j<tokens2.length;j++)if(token==tokens2[j])continue outer;values.push(token)}return values}function arrayClasses(classVal){if(isArray(classVal))return classVal;if(isString(classVal))return classVal.split(" ");if(isObject(classVal)){var classes=[];return forEach(classVal,function(v,k){v&&(classes=classes.concat(k.split(" ")))}),classes}return classVal}return{restrict:"AC",link:function(scope,element,attr){function addClasses(classes){var newClasses=digestClassCounts(classes,1);attr.$addClass(newClasses)}function removeClasses(classes){var newClasses=digestClassCounts(classes,-1);attr.$removeClass(newClasses)}function digestClassCounts(classes,count){var classCounts=element.data("$classCounts")||{},classesToUpdate=[];return forEach(classes,function(className){(count>0||classCounts[className])&&(classCounts[className]=(classCounts[className]||0)+count,classCounts[className]===+(count>0)&&classesToUpdate.push(className))}),element.data("$classCounts",classCounts),classesToUpdate.join(" ")}function updateClasses(oldClasses,newClasses){var toAdd=arrayDifference(newClasses,oldClasses),toRemove=arrayDifference(oldClasses,newClasses);toAdd=digestClassCounts(toAdd,1),toRemove=digestClassCounts(toRemove,-1),toAdd&&toAdd.length&&$animate.addClass(element,toAdd),toRemove&&toRemove.length&&$animate.removeClass(element,toRemove)}function ngClassWatchAction(newVal){if(selector===!0||scope.$index%2===selector){var newClasses=arrayClasses(newVal||[]);if(oldVal){if(!equals(newVal,oldVal)){var oldClasses=arrayClasses(oldVal);updateClasses(oldClasses,newClasses)}}else addClasses(newClasses)}oldVal=shallowCopy(newVal)}var oldVal;scope.$watch(attr[name],ngClassWatchAction,!0),attr.$observe("class",function(){ngClassWatchAction(scope.$eval(attr[name]))}),"ngClass"!==name&&scope.$watch("$index",function($index,old$index){var mod=1&$index;if(mod!==(1&old$index)){var classes=arrayClasses(scope.$eval(attr[name]));mod===selector?addClasses(classes):removeClasses(classes)}})}}}]}var REGEX_STRING_REGEXP=/^\/(.+)\/([a-z]*)$/,VALIDITY_STATE_PROPERTY="validity",lowercase=function(string){return isString(string)?string.toLowerCase():string},hasOwnProperty=Object.prototype.hasOwnProperty,uppercase=function(string){return isString(string)?string.toUpperCase():string},manualLowercase=function(s){return isString(s)?s.replace(/[A-Z]/g,function(ch){return String.fromCharCode(32|ch.charCodeAt(0))}):s},manualUppercase=function(s){return isString(s)?s.replace(/[a-z]/g,function(ch){return String.fromCharCode(-33&ch.charCodeAt(0))}):s};"i"!=="I".toLowerCase()&&(lowercase=manualLowercase,uppercase=manualUppercase);var msie,jqLite,jQuery,angularModule,slice=[].slice,push=[].push,toString=Object.prototype.toString,ngMinErr=minErr("ng"),angular=window.angular||(window.angular={}),uid=0;msie=int((/msie (\d+)/.exec(lowercase(navigator.userAgent))||[])[1]),isNaN(msie)&&(msie=int((/trident\/.*; rv:(\d+)/.exec(lowercase(navigator.userAgent))||[])[1])),noop.$inject=[],identity.$inject=[];var skipDestroyOnNextJQueryCleanData,isArray=Array.isArray,trim=function(value){return isString(value)?value.trim():value},csp=function(){if(isDefined(csp.isActive_))return csp.isActive_;var active=!(!document.querySelector("[ng-csp]")&&!document.querySelector("[data-ng-csp]"));if(!active)try{new Function("")}catch(e){active=!0}return csp.isActive_=active},ngAttrPrefixes=["ng-","data-ng-","ng:","x-ng-"],SNAKE_CASE_REGEXP=/[A-Z]/g,bindJQueryFired=!1,version={full:"1.3.0-rc.4",major:1,minor:3,dot:0,codeName:"unicorn-hydrafication"};JQLite.expando="ng339";var jqCache=JQLite.cache={},jqId=1,addEventListenerFn=function(element,type,fn){element.addEventListener(type,fn,!1)},removeEventListenerFn=function(element,type,fn){element.removeEventListener(type,fn,!1)};JQLite._data=function(node){return this.cache[node[this.expando]]||{}};var SPECIAL_CHARS_REGEXP=/([\:\-\_]+(.))/g,MOZ_HACK_REGEXP=/^moz([A-Z])/,MOUSE_EVENT_MAP={mouseleave:"mouseout",mouseenter:"mouseover"},jqLiteMinErr=minErr("jqLite"),SINGLE_TAG_REGEXP=/^<(\w+)\s*\/?>(?:<\/\1>|)$/,HTML_REGEXP=/<|&#?\w+;/,TAG_NAME_REGEXP=/<([\w:]+)/,XHTML_TAG_REGEXP=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:]+)[^>]*)\/>/gi,wrapMap={option:[1,'<select multiple="multiple">',"</select>"],thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};wrapMap.optgroup=wrapMap.option,wrapMap.tbody=wrapMap.tfoot=wrapMap.colgroup=wrapMap.caption=wrapMap.thead,wrapMap.th=wrapMap.td;var JQLitePrototype=JQLite.prototype={ready:function(fn){function trigger(){fired||(fired=!0,fn())}var fired=!1;"complete"===document.readyState?setTimeout(trigger):(this.on("DOMContentLoaded",trigger),JQLite(window).on("load",trigger),this.on("DOMContentLoaded",trigger))},toString:function(){var value=[];return forEach(this,function(e){value.push(""+e)}),"["+value.join(", ")+"]"},eq:function(index){return jqLite(index>=0?this[index]:this[this.length+index])},length:0,push:push,sort:[].sort,splice:[].splice},BOOLEAN_ATTR={};forEach("multiple,selected,checked,disabled,readOnly,required,open".split(","),function(value){BOOLEAN_ATTR[lowercase(value)]=value});var BOOLEAN_ELEMENTS={};forEach("input,select,option,textarea,button,form,details".split(","),function(value){BOOLEAN_ELEMENTS[value]=!0});var ALIASED_ATTR={ngMinlength:"minlength",ngMaxlength:"maxlength",ngMin:"min",ngMax:"max",ngPattern:"pattern"};forEach({data:jqLiteData,removeData:jqLiteRemoveData},function(fn,name){JQLite[name]=fn}),forEach({data:jqLiteData,inheritedData:jqLiteInheritedData,scope:function(element){return jqLite.data(element,"$scope")||jqLiteInheritedData(element.parentNode||element,["$isolateScope","$scope"])},isolateScope:function(element){return jqLite.data(element,"$isolateScope")||jqLite.data(element,"$isolateScopeNoTemplate")},controller:jqLiteController,injector:function(element){return jqLiteInheritedData(element,"$injector")},removeAttr:function(element,name){element.removeAttribute(name)},hasClass:jqLiteHasClass,css:function(element,name,value){return name=camelCase(name),isDefined(value)?void(element.style[name]=value):element.style[name]},attr:function(element,name,value){var lowercasedName=lowercase(name);if(BOOLEAN_ATTR[lowercasedName]){if(!isDefined(value))return element[name]||(element.attributes.getNamedItem(name)||noop).specified?lowercasedName:undefined;value?(element[name]=!0,element.setAttribute(name,lowercasedName)):(element[name]=!1,element.removeAttribute(lowercasedName))}else if(isDefined(value))element.setAttribute(name,value);else if(element.getAttribute){var ret=element.getAttribute(name,2);return null===ret?undefined:ret}},prop:function(element,name,value){return isDefined(value)?void(element[name]=value):element[name]},text:function(){function getText(element,value){if(isUndefined(value)){var nodeType=element.nodeType;return 1===nodeType||3===nodeType?element.textContent:""}element.textContent=value}return getText.$dv="",getText}(),val:function(element,value){if(isUndefined(value)){if(element.multiple&&"select"===nodeName_(element)){var result=[];return forEach(element.options,function(option){option.selected&&result.push(option.value||option.text)}),0===result.length?null:result}return element.value}element.value=value},html:function(element,value){return isUndefined(value)?element.innerHTML:(jqLiteDealoc(element,!0),void(element.innerHTML=value))},empty:jqLiteEmpty},function(fn,name){JQLite.prototype[name]=function(arg1,arg2){var i,key,nodeCount=this.length;if(fn!==jqLiteEmpty&&(2==fn.length&&fn!==jqLiteHasClass&&fn!==jqLiteController?arg1:arg2)===undefined){if(isObject(arg1)){for(i=0;nodeCount>i;i++)if(fn===jqLiteData)fn(this[i],arg1);else for(key in arg1)fn(this[i],key,arg1[key]);return this}for(var value=fn.$dv,jj=value===undefined?Math.min(nodeCount,1):nodeCount,j=0;jj>j;j++){var nodeValue=fn(this[j],arg1,arg2);value=value?value+nodeValue:nodeValue}return value}for(i=0;nodeCount>i;i++)fn(this[i],arg1,arg2);return this}}),forEach({removeData:jqLiteRemoveData,on:function jqLiteOn(element,type,fn,unsupported){if(isDefined(unsupported))throw jqLiteMinErr("onargs","jqLite#on() does not support the `selector` or `eventData` parameters");if(jqLiteAcceptsData(element)){var expandoStore=jqLiteExpandoStore(element,!0),events=expandoStore.events,handle=expandoStore.handle;handle||(handle=expandoStore.handle=createEventHandler(element,events));for(var types=type.indexOf(" ")>=0?type.split(" "):[type],i=types.length;i--;){type=types[i];var eventFns=events[type];eventFns||(events[type]=[],"mouseenter"===type||"mouseleave"===type?jqLiteOn(element,MOUSE_EVENT_MAP[type],function(event){var target=this,related=event.relatedTarget;(!related||related!==target&&!target.contains(related))&&handle(event,type)}):"$destroy"!==type&&addEventListenerFn(element,type,handle),eventFns=events[type]),eventFns.push(fn)}}},off:jqLiteOff,one:function(element,type,fn){element=jqLite(element),element.on(type,function onFn(){element.off(type,fn),element.off(type,onFn)}),element.on(type,fn)},replaceWith:function(element,replaceNode){var index,parent=element.parentNode;jqLiteDealoc(element),forEach(new JQLite(replaceNode),function(node){index?parent.insertBefore(node,index.nextSibling):parent.replaceChild(node,element),index=node})},children:function(element){var children=[];return forEach(element.childNodes,function(element){1===element.nodeType&&children.push(element)}),children},contents:function(element){return element.contentDocument||element.childNodes||[]},append:function(element,node){var nodeType=element.nodeType;if(1===nodeType||11===nodeType){node=new JQLite(node);for(var i=0,ii=node.length;ii>i;i++){var child=node[i];element.appendChild(child)}}},prepend:function(element,node){if(1===element.nodeType){var index=element.firstChild;forEach(new JQLite(node),function(child){element.insertBefore(child,index)})}},wrap:function(element,wrapNode){wrapNode=jqLite(wrapNode).eq(0).clone()[0];var parent=element.parentNode;parent&&parent.replaceChild(wrapNode,element),wrapNode.appendChild(element)},remove:jqLiteRemove,detach:function(element){jqLiteRemove(element,!0)},after:function(element,newElement){var index=element,parent=element.parentNode;newElement=new JQLite(newElement);for(var i=0,ii=newElement.length;ii>i;i++){var node=newElement[i];parent.insertBefore(node,index.nextSibling),index=node}},addClass:jqLiteAddClass,removeClass:jqLiteRemoveClass,toggleClass:function(element,selector,condition){selector&&forEach(selector.split(" "),function(className){var classCondition=condition;isUndefined(classCondition)&&(classCondition=!jqLiteHasClass(element,className)),(classCondition?jqLiteAddClass:jqLiteRemoveClass)(element,className)})},parent:function(element){var parent=element.parentNode;return parent&&11!==parent.nodeType?parent:null},next:function(element){return element.nextElementSibling},find:function(element,selector){return element.getElementsByTagName?element.getElementsByTagName(selector):[]},clone:jqLiteClone,triggerHandler:function(element,event,extraParameters){var dummyEvent,eventFnsCopy,handlerArgs,eventName=event.type||event,expandoStore=jqLiteExpandoStore(element),events=expandoStore&&expandoStore.events,eventFns=events&&events[eventName];eventFns&&(dummyEvent={preventDefault:function(){this.defaultPrevented=!0},isDefaultPrevented:function(){return this.defaultPrevented===!0},stopImmediatePropagation:function(){this.immediatePropagationStopped=!0},isImmediatePropagationStopped:function(){return this.immediatePropagationStopped===!0},stopPropagation:noop,type:eventName,target:element},event.type&&(dummyEvent=extend(dummyEvent,event)),eventFnsCopy=shallowCopy(eventFns),handlerArgs=extraParameters?[dummyEvent].concat(extraParameters):[dummyEvent],forEach(eventFnsCopy,function(fn){dummyEvent.isImmediatePropagationStopped()||fn.apply(element,handlerArgs)}))}},function(fn,name){JQLite.prototype[name]=function(arg1,arg2,arg3){for(var value,i=0,ii=this.length;ii>i;i++)isUndefined(value)?(value=fn(this[i],arg1,arg2,arg3),isDefined(value)&&(value=jqLite(value))):jqLiteAddNodes(value,fn(this[i],arg1,arg2,arg3));return isDefined(value)?value:this},JQLite.prototype.bind=JQLite.prototype.on,JQLite.prototype.unbind=JQLite.prototype.off}),HashMap.prototype={put:function(key,value){this[hashKey(key,this.nextUid)]=value},get:function(key){return this[hashKey(key,this.nextUid)]},remove:function(key){var value=this[key=hashKey(key,this.nextUid)];return delete this[key],value}};var FN_ARGS=/^function\s*[^\(]*\(\s*([^\)]*)\)/m,FN_ARG_SPLIT=/,/,FN_ARG=/^\s*(_?)(\S+?)\1\s*$/,STRIP_COMMENTS=/((\/\/.*$)|(\/\*[\s\S]*?\*\/))/gm,$injectorMinErr=minErr("$injector");createInjector.$$annotate=annotate;var $animateMinErr=minErr("$animate"),$AnimateProvider=["$provide",function($provide){this.$$selectors={},this.register=function(name,factory){var key=name+"-animation";if(name&&"."!=name.charAt(0))throw $animateMinErr("notcsel","Expecting class selector starting with '.' got '{0}'.",name);this.$$selectors[name.substr(1)]=key,$provide.factory(key,factory)},this.classNameFilter=function(expression){return 1===arguments.length&&(this.$$classNameFilter=expression instanceof RegExp?expression:null),this.$$classNameFilter},this.$get=["$$q","$$asyncCallback",function($$q,$$asyncCallback){function asyncPromise(){return currentDefer||(currentDefer=$$q.defer(),$$asyncCallback(function(){currentDefer.resolve(),currentDefer=null})),currentDefer.promise}var currentDefer;return{enter:function(element,parent,after){return after?after.after(element):parent.prepend(element),asyncPromise()},leave:function(element){return element.remove(),asyncPromise()},move:function(element,parent,after){return this.enter(element,parent,after)},addClass:function(element,className){return className=isString(className)?className:isArray(className)?className.join(" "):"",forEach(element,function(element){jqLiteAddClass(element,className)}),asyncPromise()},removeClass:function(element,className){return className=isString(className)?className:isArray(className)?className.join(" "):"",forEach(element,function(element){jqLiteRemoveClass(element,className)}),asyncPromise()},setClass:function(element,add,remove){return this.addClass(element,add),this.removeClass(element,remove),asyncPromise()},enabled:noop,cancel:noop}}]}],$compileMinErr=minErr("$compile");$CompileProvider.$inject=["$provide","$$sanitizeUriProvider"];var PREFIX_REGEXP=/^(x[\:\-_]|data[\:\-_])/i,$interpolateMinErr=minErr("$interpolate"),PATH_MATCH=/^([^\?#]*)(\?([^#]*))?(#(.*))?$/,DEFAULT_PORTS={http:80,https:443,ftp:21},$locationMinErr=minErr("$location");LocationHashbangInHtml5Url.prototype=LocationHashbangUrl.prototype=LocationHtml5Url.prototype={$$html5:!1,$$replace:!1,absUrl:locationGetter("$$absUrl"),url:function(url){if(isUndefined(url))return this.$$url;var match=PATH_MATCH.exec(url);return match[1]&&this.path(decodeURIComponent(match[1])),(match[2]||match[1])&&this.search(match[3]||""),this.hash(match[5]||""),this},protocol:locationGetter("$$protocol"),host:locationGetter("$$host"),port:locationGetter("$$port"),path:locationGetterSetter("$$path",function(path){return path=path?path.toString():"","/"==path.charAt(0)?path:"/"+path}),search:function(search,paramValue){switch(arguments.length){case 0:return this.$$search;case 1:if(isString(search)||isNumber(search))search=search.toString(),this.$$search=parseKeyValue(search);else{if(!isObject(search))throw $locationMinErr("isrcharg","The first argument of the `$location#search()` call must be a string or an object.");forEach(search,function(value,key){null==value&&delete search[key]}),this.$$search=search}break;default:isUndefined(paramValue)||null===paramValue?delete this.$$search[search]:this.$$search[search]=paramValue}return this.$$compose(),this},hash:locationGetterSetter("$$hash",function(hash){return hash?hash.toString():""}),replace:function(){return this.$$replace=!0,this}};var $parseMinErr=minErr("$parse"),CALL=Function.prototype.call,APPLY=Function.prototype.apply,BIND=Function.prototype.bind,CONSTANTS=createMap();forEach({"null":function(){return null},"true":function(){return!0},"false":function(){return!1},undefined:function(){}},function(constantGetter,name){constantGetter.constant=constantGetter.literal=constantGetter.sharedGetter=!0,CONSTANTS[name]=constantGetter}),CONSTANTS["this"]=function(self){return self},CONSTANTS["this"].sharedGetter=!0;var OPERATORS=extend(createMap(),{"+":function(self,locals,a,b){return a=a(self,locals),b=b(self,locals),isDefined(a)?isDefined(b)?a+b:a:isDefined(b)?b:undefined},"-":function(self,locals,a,b){return a=a(self,locals),b=b(self,locals),(isDefined(a)?a:0)-(isDefined(b)?b:0)},"*":function(self,locals,a,b){return a(self,locals)*b(self,locals)},"/":function(self,locals,a,b){return a(self,locals)/b(self,locals)},"%":function(self,locals,a,b){return a(self,locals)%b(self,locals)},"^":function(self,locals,a,b){return a(self,locals)^b(self,locals)},"===":function(self,locals,a,b){return a(self,locals)===b(self,locals)},"!==":function(self,locals,a,b){return a(self,locals)!==b(self,locals)},"==":function(self,locals,a,b){return a(self,locals)==b(self,locals)},"!=":function(self,locals,a,b){return a(self,locals)!=b(self,locals)},"<":function(self,locals,a,b){return a(self,locals)<b(self,locals)},">":function(self,locals,a,b){return a(self,locals)>b(self,locals)},"<=":function(self,locals,a,b){return a(self,locals)<=b(self,locals)},">=":function(self,locals,a,b){return a(self,locals)>=b(self,locals)},"&&":function(self,locals,a,b){return a(self,locals)&&b(self,locals)},"||":function(self,locals,a,b){return a(self,locals)||b(self,locals)},"&":function(self,locals,a,b){return a(self,locals)&b(self,locals)},"!":function(self,locals,a){return!a(self,locals)},"=":!0,"|":!0}),ESCAPE={n:"\n",f:"\f",r:"\r",t:"	",v:"","'":"'",'"':'"'},Lexer=function(options){this.options=options};Lexer.prototype={constructor:Lexer,lex:function(text){for(this.text=text,this.index=0,this.ch=undefined,this.tokens=[];this.index<this.text.length;)if(this.ch=this.text.charAt(this.index),this.is("\"'"))this.readString(this.ch);else if(this.isNumber(this.ch)||this.is(".")&&this.isNumber(this.peek()))this.readNumber();else if(this.isIdent(this.ch))this.readIdent();else if(this.is("(){}[].,;:?"))this.tokens.push({index:this.index,text:this.ch}),this.index++;else if(this.isWhitespace(this.ch))this.index++;else{var ch2=this.ch+this.peek(),ch3=ch2+this.peek(2),fn=OPERATORS[this.ch],fn2=OPERATORS[ch2],fn3=OPERATORS[ch3];fn3?(this.tokens.push({index:this.index,text:ch3,fn:fn3}),this.index+=3):fn2?(this.tokens.push({index:this.index,text:ch2,fn:fn2}),this.index+=2):fn?(this.tokens.push({index:this.index,text:this.ch,fn:fn}),this.index+=1):this.throwError("Unexpected next character ",this.index,this.index+1)}return this.tokens},is:function(chars){return-1!==chars.indexOf(this.ch)},peek:function(i){var num=i||1;return this.index+num<this.text.length?this.text.charAt(this.index+num):!1},isNumber:function(ch){return ch>="0"&&"9">=ch},isWhitespace:function(ch){return" "===ch||"\r"===ch||"	"===ch||"\n"===ch||""===ch||" "===ch},isIdent:function(ch){return ch>="a"&&"z">=ch||ch>="A"&&"Z">=ch||"_"===ch||"$"===ch},isExpOperator:function(ch){return"-"===ch||"+"===ch||this.isNumber(ch)},throwError:function(error,start,end){end=end||this.index;var colStr=isDefined(start)?"s "+start+"-"+this.index+" ["+this.text.substring(start,end)+"]":" "+end;throw $parseMinErr("lexerr","Lexer Error: {0} at column{1} in expression [{2}].",error,colStr,this.text)},readNumber:function(){for(var number="",start=this.index;this.index<this.text.length;){var ch=lowercase(this.text.charAt(this.index));if("."==ch||this.isNumber(ch))number+=ch;else{var peekCh=this.peek();if("e"==ch&&this.isExpOperator(peekCh))number+=ch;else if(this.isExpOperator(ch)&&peekCh&&this.isNumber(peekCh)&&"e"==number.charAt(number.length-1))number+=ch;else{if(!this.isExpOperator(ch)||peekCh&&this.isNumber(peekCh)||"e"!=number.charAt(number.length-1))break;this.throwError("Invalid exponent")}}this.index++}number=1*number,this.tokens.push({index:start,text:number,constant:!0,fn:function(){return number}})},readIdent:function(){for(var lastDot,peekIndex,methodName,ch,expression=this.text,ident="",start=this.index;this.index<this.text.length&&(ch=this.text.charAt(this.index),"."===ch||this.isIdent(ch)||this.isNumber(ch));)"."===ch&&(lastDot=this.index),ident+=ch,this.index++;if(lastDot&&"."===ident[ident.length-1]&&(this.index--,ident=ident.slice(0,-1),lastDot=ident.lastIndexOf("."),-1===lastDot&&(lastDot=undefined)),lastDot)for(peekIndex=this.index;peekIndex<this.text.length;){if(ch=this.text.charAt(peekIndex),"("===ch){methodName=ident.substr(lastDot-start+1),ident=ident.substr(0,lastDot-start),this.index=peekIndex;break}if(!this.isWhitespace(ch))break;peekIndex++}this.tokens.push({index:start,text:ident,fn:CONSTANTS[ident]||getterFn(ident,this.options,expression)}),methodName&&(this.tokens.push({index:lastDot,text:"."}),this.tokens.push({index:lastDot+1,text:methodName}))},readString:function(quote){var start=this.index;this.index++;for(var string="",rawString=quote,escape=!1;this.index<this.text.length;){var ch=this.text.charAt(this.index);if(rawString+=ch,escape){if("u"===ch){var hex=this.text.substring(this.index+1,this.index+5);hex.match(/[\da-f]{4}/i)||this.throwError("Invalid unicode escape [\\u"+hex+"]"),this.index+=4,string+=String.fromCharCode(parseInt(hex,16))}else{var rep=ESCAPE[ch];string+=rep||ch}escape=!1}else if("\\"===ch)escape=!0;else{if(ch===quote)return this.index++,void this.tokens.push({index:start,text:rawString,string:string,constant:!0,fn:function(){return string}});string+=ch}this.index++}this.throwError("Unterminated quote",start)}};var Parser=function(lexer,$filter,options){this.lexer=lexer,this.$filter=$filter,this.options=options};Parser.ZERO=extend(function(){return 0},{sharedGetter:!0,constant:!0}),Parser.prototype={constructor:Parser,parse:function(text){this.text=text,this.tokens=this.lexer.lex(text);var value=this.statements();return 0!==this.tokens.length&&this.throwError("is an unexpected token",this.tokens[0]),value.literal=!!value.literal,value.constant=!!value.constant,value},primary:function(){var primary;if(this.expect("("))primary=this.filterChain(),this.consume(")");else if(this.expect("["))primary=this.arrayDeclaration();else if(this.expect("{"))primary=this.object();else{var token=this.expect();primary=token.fn,primary||this.throwError("not a primary expression",token),token.constant&&(primary.constant=!0,primary.literal=!0)}for(var next,context;next=this.expect("(","[",".");)"("===next.text?(primary=this.functionCall(primary,context),context=null):"["===next.text?(context=primary,primary=this.objectIndex(primary)):"."===next.text?(context=primary,primary=this.fieldAccess(primary)):this.throwError("IMPOSSIBLE");return primary},throwError:function(msg,token){throw $parseMinErr("syntax","Syntax Error: Token '{0}' {1} at column {2} of the expression [{3}] starting at [{4}].",token.text,msg,token.index+1,this.text,this.text.substring(token.index))},peekToken:function(){if(0===this.tokens.length)throw $parseMinErr("ueoe","Unexpected end of expression: {0}",this.text);return this.tokens[0]},peek:function(e1,e2,e3,e4){if(this.tokens.length>0){var token=this.tokens[0],t=token.text;if(t===e1||t===e2||t===e3||t===e4||!e1&&!e2&&!e3&&!e4)return token}return!1},expect:function(e1,e2,e3,e4){var token=this.peek(e1,e2,e3,e4);return token?(this.tokens.shift(),token):!1},consume:function(e1){this.expect(e1)||this.throwError("is unexpected, expecting ["+e1+"]",this.peek())},unaryFn:function(fn,right){return extend(function(self,locals){return fn(self,locals,right)},{constant:right.constant,inputs:[right]})},binaryFn:function(left,fn,right,isBranching){return extend(function(self,locals){return fn(self,locals,left,right)},{constant:left.constant&&right.constant,inputs:!isBranching&&[left,right]})},statements:function(){for(var statements=[];;)if(this.tokens.length>0&&!this.peek("}",")",";","]")&&statements.push(this.filterChain()),!this.expect(";"))return 1===statements.length?statements[0]:function(self,locals){for(var value,i=0,ii=statements.length;ii>i;i++)value=statements[i](self,locals);return value}},filterChain:function(){for(var token,left=this.expression();token=this.expect("|");)left=this.filter(left);return left},filter:function(inputFn){var argsFn,args,token=this.expect(),fn=this.$filter(token.text);if(this.peek(":"))for(argsFn=[],args=[];this.expect(":");)argsFn.push(this.expression());var inputs=[inputFn].concat(argsFn||[]);return extend(function(self,locals){var input=inputFn(self,locals);if(args){args[0]=input;for(var i=argsFn.length;i--;)args[i+1]=argsFn[i](self,locals);return fn.apply(undefined,args)}return fn(input)},{constant:!fn.$stateful&&inputs.every(isConstant),inputs:!fn.$stateful&&inputs})},expression:function(){return this.assignment()},assignment:function(){var right,token,left=this.ternary();return(token=this.expect("="))?(left.assign||this.throwError("implies assignment but ["+this.text.substring(0,token.index)+"] can not be assigned to",token),right=this.ternary(),extend(function(scope,locals){return left.assign(scope,right(scope,locals),locals)},{inputs:[left,right]})):left},ternary:function(){var middle,token,left=this.logicalOR();if(token=this.expect("?")){if(middle=this.assignment(),token=this.expect(":")){var right=this.assignment();return extend(function(self,locals){return left(self,locals)?middle(self,locals):right(self,locals)},{constant:left.constant&&middle.constant&&right.constant})}this.throwError("expected :",token)}return left},logicalOR:function(){for(var token,left=this.logicalAND();token=this.expect("||");)left=this.binaryFn(left,token.fn,this.logicalAND(),!0);return left},logicalAND:function(){var token,left=this.equality();return(token=this.expect("&&"))&&(left=this.binaryFn(left,token.fn,this.logicalAND(),!0)),left},equality:function(){var token,left=this.relational();return(token=this.expect("==","!=","===","!=="))&&(left=this.binaryFn(left,token.fn,this.equality())),left},relational:function(){var token,left=this.additive();return(token=this.expect("<",">","<=",">="))&&(left=this.binaryFn(left,token.fn,this.relational())),left},additive:function(){for(var token,left=this.multiplicative();token=this.expect("+","-");)left=this.binaryFn(left,token.fn,this.multiplicative());return left},multiplicative:function(){for(var token,left=this.unary();token=this.expect("*","/","%");)left=this.binaryFn(left,token.fn,this.unary());return left},unary:function(){var token;return this.expect("+")?this.primary():(token=this.expect("-"))?this.binaryFn(Parser.ZERO,token.fn,this.unary()):(token=this.expect("!"))?this.unaryFn(token.fn,this.unary()):this.primary()},fieldAccess:function(object){var expression=this.text,field=this.expect().text,getter=getterFn(field,this.options,expression);return extend(function(scope,locals,self){return getter(self||object(scope,locals))},{assign:function(scope,value,locals){var o=object(scope,locals);return o||object.assign(scope,o={}),setter(o,field,value,expression)}})},objectIndex:function(obj){var expression=this.text,indexFn=this.expression();return this.consume("]"),extend(function(self,locals){var v,o=obj(self,locals),i=indexFn(self,locals);
return ensureSafeMemberName(i,expression),o?v=ensureSafeObject(o[i],expression):undefined},{assign:function(self,value,locals){var key=ensureSafeMemberName(indexFn(self,locals),expression),o=ensureSafeObject(obj(self,locals),expression);return o||obj.assign(self,o={}),o[key]=value}})},functionCall:function(fnGetter,contextGetter){var argsFn=[];if(")"!==this.peekToken().text)do argsFn.push(this.expression());while(this.expect(","));this.consume(")");var expressionText=this.text,args=argsFn.length?[]:null;return function(scope,locals){var context=contextGetter?contextGetter(scope,locals):scope,fn=fnGetter(scope,locals,context)||noop;if(args)for(var i=argsFn.length;i--;)args[i]=ensureSafeObject(argsFn[i](scope,locals),expressionText);ensureSafeObject(context,expressionText),ensureSafeFunction(fn,expressionText);var v=fn.apply?fn.apply(context,args):fn(args[0],args[1],args[2],args[3],args[4]);return ensureSafeObject(v,expressionText)}},arrayDeclaration:function(){var elementFns=[];if("]"!==this.peekToken().text)do{if(this.peek("]"))break;var elementFn=this.expression();elementFns.push(elementFn)}while(this.expect(","));return this.consume("]"),extend(function(self,locals){for(var array=[],i=0,ii=elementFns.length;ii>i;i++)array.push(elementFns[i](self,locals));return array},{literal:!0,constant:elementFns.every(isConstant),inputs:elementFns})},object:function(){var keys=[],valueFns=[];if("}"!==this.peekToken().text)do{if(this.peek("}"))break;var token=this.expect();keys.push(token.string||token.text),this.consume(":");var value=this.expression();valueFns.push(value)}while(this.expect(","));return this.consume("}"),extend(function(self,locals){for(var object={},i=0,ii=valueFns.length;ii>i;i++)object[keys[i]]=valueFns[i](self,locals);return object},{literal:!0,constant:valueFns.every(isConstant),inputs:valueFns})}};var getterFnCache=createMap(),$sceMinErr=minErr("$sce"),SCE_CONTEXTS={HTML:"html",CSS:"css",URL:"url",RESOURCE_URL:"resourceUrl",JS:"js"},$compileMinErr=minErr("$compile"),urlParsingNode=document.createElement("a"),originUrl=urlResolve(window.location.href,!0);$FilterProvider.$inject=["$provide"],currencyFilter.$inject=["$locale"],numberFilter.$inject=["$locale"];var DECIMAL_SEP=".",DATE_FORMATS={yyyy:dateGetter("FullYear",4),yy:dateGetter("FullYear",2,0,!0),y:dateGetter("FullYear",1),MMMM:dateStrGetter("Month"),MMM:dateStrGetter("Month",!0),MM:dateGetter("Month",2,1),M:dateGetter("Month",1,1),dd:dateGetter("Date",2),d:dateGetter("Date",1),HH:dateGetter("Hours",2),H:dateGetter("Hours",1),hh:dateGetter("Hours",2,-12),h:dateGetter("Hours",1,-12),mm:dateGetter("Minutes",2),m:dateGetter("Minutes",1),ss:dateGetter("Seconds",2),s:dateGetter("Seconds",1),sss:dateGetter("Milliseconds",3),EEEE:dateStrGetter("Day"),EEE:dateStrGetter("Day",!0),a:ampmGetter,Z:timeZoneGetter,ww:weekGetter(2),w:weekGetter(1)},DATE_FORMATS_SPLIT=/((?:[^yMdHhmsaZEw']+)|(?:'(?:[^']|'')*')|(?:E+|y+|M+|d+|H+|h+|m+|s+|a|Z|w+))(.*)/,NUMBER_STRING=/^\-?\d+$/;dateFilter.$inject=["$locale"];var lowercaseFilter=valueFn(lowercase),uppercaseFilter=valueFn(uppercase);orderByFilter.$inject=["$parse"];var htmlAnchorDirective=valueFn({restrict:"E",compile:function(element,attr){return 8>=msie&&(attr.href||attr.name||attr.$set("href",""),element.append(document.createComment("IE fix"))),attr.href||attr.xlinkHref||attr.name?void 0:function(scope,element){var href="[object SVGAnimatedString]"===toString.call(element.prop("href"))?"xlink:href":"href";element.on("click",function(event){element.attr(href)||event.preventDefault()})}}}),ngAttributeAliasDirectives={};forEach(BOOLEAN_ATTR,function(propName,attrName){if("multiple"!=propName){var normalized=directiveNormalize("ng-"+attrName);ngAttributeAliasDirectives[normalized]=function(){return{restrict:"A",priority:100,link:function(scope,element,attr){scope.$watch(attr[normalized],function(value){attr.$set(attrName,!!value)})}}}}}),forEach(ALIASED_ATTR,function(htmlAttr,ngAttr){ngAttributeAliasDirectives[ngAttr]=function(){return{priority:100,link:function(scope,element,attr){if("ngPattern"===ngAttr&&"/"==attr.ngPattern.charAt(0)){var match=attr.ngPattern.match(REGEX_STRING_REGEXP);if(match)return void attr.$set("ngPattern",new RegExp(match[1],match[2]))}scope.$watch(attr[ngAttr],function(value){attr.$set(ngAttr,value)})}}}}),forEach(["src","srcset","href"],function(attrName){var normalized=directiveNormalize("ng-"+attrName);ngAttributeAliasDirectives[normalized]=function(){return{priority:99,link:function(scope,element,attr){var propName=attrName,name=attrName;"href"===attrName&&"[object SVGAnimatedString]"===toString.call(element.prop("href"))&&(name="xlinkHref",attr.$attr[name]="xlink:href",propName=null),attr.$observe(normalized,function(value){return value?(attr.$set(name,value),void(msie&&propName&&element.prop(propName,attr[name]))):void("href"===attrName&&attr.$set(name,null))})}}}});var nullFormCtrl={$addControl:noop,$$renameControl:nullFormRenameControl,$removeControl:noop,$setValidity:noop,$$setPending:noop,$setDirty:noop,$setPristine:noop,$setSubmitted:noop,$$clearControlValidity:noop},SUBMITTED_CLASS="ng-submitted";FormController.$inject=["$element","$attrs","$scope","$animate","$interpolate"];var formDirectiveFactory=function(isNgForm){return["$timeout",function($timeout){var formDirective={name:"form",restrict:isNgForm?"EAC":"E",controller:FormController,compile:function(formElement){return formElement.addClass(PRISTINE_CLASS).addClass(VALID_CLASS),{pre:function(scope,formElement,attr,controller){if(!attr.action){var handleFormSubmission=function(event){scope.$apply(function(){controller.$commitViewValue(),controller.$setSubmitted()}),event.preventDefault?event.preventDefault():event.returnValue=!1};addEventListenerFn(formElement[0],"submit",handleFormSubmission),formElement.on("$destroy",function(){$timeout(function(){removeEventListenerFn(formElement[0],"submit",handleFormSubmission)},0,!1)})}var parentFormCtrl=controller.$$parentForm,alias=controller.$name;alias&&(setter(scope,alias,controller,alias),attr.$observe(attr.name?"name":"ngForm",function(newValue){alias!==newValue&&(setter(scope,alias,undefined,alias),alias=newValue,setter(scope,alias,controller,alias),parentFormCtrl.$$renameControl(controller,alias))})),parentFormCtrl!==nullFormCtrl&&formElement.on("$destroy",function(){parentFormCtrl.$removeControl(controller),alias&&setter(scope,alias,undefined,alias),extend(controller,nullFormCtrl)})}}}};return formDirective}]},formDirective=formDirectiveFactory(),ngFormDirective=formDirectiveFactory(!0),ISO_DATE_REGEXP=/\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z)/,URL_REGEXP=/^(ftp|http|https):\/\/(\w+:{0,1}\w*@)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%@!\-\/]))?$/,EMAIL_REGEXP=/^[a-z0-9!#$%&'*+\/=?^_`{|}~.-]+@[a-z0-9]([a-z0-9-]*[a-z0-9])?(\.[a-z0-9]([a-z0-9-]*[a-z0-9])?)*$/i,NUMBER_REGEXP=/^\s*(\-|\+)?(\d+|(\d*(\.\d*)))\s*$/,DATE_REGEXP=/^(\d{4})-(\d{2})-(\d{2})$/,DATETIMELOCAL_REGEXP=/^(\d{4})-(\d\d)-(\d\d)T(\d\d):(\d\d)(?::(\d\d)(\.\d{1,3})?)?$/,WEEK_REGEXP=/^(\d{4})-W(\d\d)$/,MONTH_REGEXP=/^(\d{4})-(\d\d)$/,TIME_REGEXP=/^(\d\d):(\d\d)(?::(\d\d)(\.\d{1,3})?)?$/,DEFAULT_REGEXP=/(\s+|^)default(\s+|$)/,$ngModelMinErr=new minErr("ngModel"),inputType={text:textInputType,date:createDateInputType("date",DATE_REGEXP,createDateParser(DATE_REGEXP,["yyyy","MM","dd"]),"yyyy-MM-dd"),"datetime-local":createDateInputType("datetimelocal",DATETIMELOCAL_REGEXP,createDateParser(DATETIMELOCAL_REGEXP,["yyyy","MM","dd","HH","mm","ss","sss"]),"yyyy-MM-ddTHH:mm:ss.sss"),time:createDateInputType("time",TIME_REGEXP,createDateParser(TIME_REGEXP,["HH","mm","ss","sss"]),"HH:mm:ss.sss"),week:createDateInputType("week",WEEK_REGEXP,weekParser,"yyyy-Www"),month:createDateInputType("month",MONTH_REGEXP,createDateParser(MONTH_REGEXP,["yyyy","MM"]),"yyyy-MM"),number:numberInputType,url:urlInputType,email:emailInputType,radio:radioInputType,checkbox:checkboxInputType,hidden:noop,button:noop,submit:noop,reset:noop,file:noop},inputDirective=["$browser","$sniffer","$filter","$parse",function($browser,$sniffer,$filter,$parse){return{restrict:"E",require:["?ngModel"],link:{pre:function(scope,element,attr,ctrls){ctrls[0]&&(inputType[lowercase(attr.type)]||inputType.text)(scope,element,attr,ctrls[0],$sniffer,$browser,$filter,$parse)}}}}],VALID_CLASS="ng-valid",INVALID_CLASS="ng-invalid",PRISTINE_CLASS="ng-pristine",DIRTY_CLASS="ng-dirty",UNTOUCHED_CLASS="ng-untouched",TOUCHED_CLASS="ng-touched",PENDING_CLASS="ng-pending",NgModelController=["$scope","$exceptionHandler","$attrs","$element","$parse","$animate","$timeout","$rootScope","$q","$interpolate",function($scope,$exceptionHandler,$attr,$element,$parse,$animate,$timeout,$rootScope,$q,$interpolate){this.$viewValue=Number.NaN,this.$modelValue=Number.NaN,this.$validators={},this.$asyncValidators={},this.$parsers=[],this.$formatters=[],this.$viewChangeListeners=[],this.$untouched=!0,this.$touched=!1,this.$pristine=!0,this.$dirty=!1,this.$valid=!0,this.$invalid=!1,this.$error={},this.$$success={},this.$pending=undefined,this.$name=$interpolate($attr.name||"",!1)($scope);var parsedNgModel=$parse($attr.ngModel),pendingDebounce=null,ctrl=this,ngModelGet=function(){var modelValue=parsedNgModel($scope);return ctrl.$options&&ctrl.$options.getterSetter&&isFunction(modelValue)&&(modelValue=modelValue()),modelValue},ngModelSet=function(){var getterSetter;ctrl.$options&&ctrl.$options.getterSetter&&isFunction(getterSetter=parsedNgModel($scope))?getterSetter(ctrl.$modelValue):parsedNgModel.assign($scope,ctrl.$modelValue)};this.$$setOptions=function(options){if(ctrl.$options=options,!(parsedNgModel.assign||options&&options.getterSetter))throw $ngModelMinErr("nonassign","Expression '{0}' is non-assignable. Element: {1}",$attr.ngModel,startingTag($element))},this.$render=noop,this.$isEmpty=function(value){return isUndefined(value)||""===value||null===value||value!==value};var parentForm=$element.inheritedData("$formController")||nullFormCtrl,currentValidationRunId=0;addSetValidityMethod({ctrl:this,$element:$element,set:function(object,property){object[property]=!0},unset:function(object,property){delete object[property]},parentForm:parentForm,$animate:$animate}),this.$setPristine=function(){ctrl.$dirty=!1,ctrl.$pristine=!0,$animate.removeClass($element,DIRTY_CLASS),$animate.addClass($element,PRISTINE_CLASS)},this.$setUntouched=function(){ctrl.$touched=!1,ctrl.$untouched=!0,$animate.setClass($element,UNTOUCHED_CLASS,TOUCHED_CLASS)},this.$setTouched=function(){ctrl.$touched=!0,ctrl.$untouched=!1,$animate.setClass($element,TOUCHED_CLASS,UNTOUCHED_CLASS)},this.$rollbackViewValue=function(){$timeout.cancel(pendingDebounce),ctrl.$viewValue=ctrl.$$lastCommittedViewValue,ctrl.$render()},this.$validate=function(){isNumber(ctrl.$modelValue)&&isNaN(ctrl.$modelValue)||this.$$parseAndValidate()},this.$$runValidators=function(parseValid,modelValue,viewValue,doneCallback){function processParseErrors(parseValid){var errorKey=ctrl.$$parserName||"parse";if(parseValid===undefined)setValidity(errorKey,null);else if(setValidity(errorKey,parseValid),!parseValid)return forEach(ctrl.$validators,function(v,name){setValidity(name,null)}),forEach(ctrl.$asyncValidators,function(v,name){setValidity(name,null)}),!1;return!0}function processSyncValidators(){var syncValidatorsValid=!0;return forEach(ctrl.$validators,function(validator,name){var result=validator(modelValue,viewValue);syncValidatorsValid=syncValidatorsValid&&result,setValidity(name,result)}),syncValidatorsValid?!0:(forEach(ctrl.$asyncValidators,function(v,name){setValidity(name,null)}),!1)}function processAsyncValidators(){var validatorPromises=[],allValid=!0;forEach(ctrl.$asyncValidators,function(validator,name){var promise=validator(modelValue,viewValue);if(!isPromiseLike(promise))throw $ngModelMinErr("$asyncValidators","Expected asynchronous validator to return a promise but got '{0}' instead.",promise);setValidity(name,undefined),validatorPromises.push(promise.then(function(){setValidity(name,!0)},function(){allValid=!1,setValidity(name,!1)}))}),validatorPromises.length?$q.all(validatorPromises).then(function(){validationDone(allValid)},noop):validationDone(!0)}function setValidity(name,isValid){localValidationRunId===currentValidationRunId&&ctrl.$setValidity(name,isValid)}function validationDone(allValid){localValidationRunId===currentValidationRunId&&doneCallback(allValid)}currentValidationRunId++;var localValidationRunId=currentValidationRunId;return processParseErrors(parseValid)&&processSyncValidators()?void processAsyncValidators():void validationDone(!1)},this.$commitViewValue=function(){var viewValue=ctrl.$viewValue;$timeout.cancel(pendingDebounce),(ctrl.$$lastCommittedViewValue!==viewValue||""===viewValue&&ctrl.$$hasNativeValidators)&&(ctrl.$$lastCommittedViewValue=viewValue,ctrl.$pristine&&(ctrl.$dirty=!0,ctrl.$pristine=!1,$animate.removeClass($element,PRISTINE_CLASS),$animate.addClass($element,DIRTY_CLASS),parentForm.$setDirty()),this.$$parseAndValidate())},this.$$parseAndValidate=function(){function writeToModelIfNeeded(){ctrl.$modelValue!==prevModelValue&&ctrl.$$writeModelToScope()}var viewValue=ctrl.$$lastCommittedViewValue,modelValue=viewValue,parserValid=isUndefined(modelValue)?undefined:!0;if(parserValid)for(var i=0;i<ctrl.$parsers.length;i++)if(modelValue=ctrl.$parsers[i](modelValue),isUndefined(modelValue)){parserValid=!1;break}isNumber(ctrl.$modelValue)&&isNaN(ctrl.$modelValue)&&(ctrl.$modelValue=ngModelGet());var prevModelValue=ctrl.$modelValue,allowInvalid=ctrl.$options&&ctrl.$options.allowInvalid;allowInvalid&&(ctrl.$modelValue=modelValue,writeToModelIfNeeded()),ctrl.$$runValidators(parserValid,modelValue,viewValue,function(allValid){allowInvalid||(ctrl.$modelValue=allValid?modelValue:undefined,writeToModelIfNeeded())})},this.$$writeModelToScope=function(){ngModelSet(ctrl.$modelValue),forEach(ctrl.$viewChangeListeners,function(listener){try{listener()}catch(e){$exceptionHandler(e)}})},this.$setViewValue=function(value,trigger){ctrl.$viewValue=value,(!ctrl.$options||ctrl.$options.updateOnDefault)&&ctrl.$$debounceViewValueCommit(trigger)},this.$$debounceViewValueCommit=function(trigger){var debounce,debounceDelay=0,options=ctrl.$options;options&&isDefined(options.debounce)&&(debounce=options.debounce,isNumber(debounce)?debounceDelay=debounce:isNumber(debounce[trigger])?debounceDelay=debounce[trigger]:isNumber(debounce["default"])&&(debounceDelay=debounce["default"])),$timeout.cancel(pendingDebounce),debounceDelay?pendingDebounce=$timeout(function(){ctrl.$commitViewValue()},debounceDelay):$rootScope.$$phase?ctrl.$commitViewValue():$scope.$apply(function(){ctrl.$commitViewValue()})},$scope.$watch(function(){var modelValue=ngModelGet();if(modelValue!==ctrl.$modelValue){ctrl.$modelValue=modelValue;for(var formatters=ctrl.$formatters,idx=formatters.length,viewValue=modelValue;idx--;)viewValue=formatters[idx](viewValue);ctrl.$viewValue!==viewValue&&(ctrl.$viewValue=ctrl.$$lastCommittedViewValue=viewValue,ctrl.$render(),ctrl.$$runValidators(undefined,modelValue,viewValue,noop))}return modelValue})}],ngModelDirective=function(){return{restrict:"A",require:["ngModel","^?form","^?ngModelOptions"],controller:NgModelController,priority:1,compile:function(element){return element.addClass(PRISTINE_CLASS).addClass(UNTOUCHED_CLASS).addClass(VALID_CLASS),{pre:function(scope,element,attr,ctrls){var modelCtrl=ctrls[0],formCtrl=ctrls[1]||nullFormCtrl;modelCtrl.$$setOptions(ctrls[2]&&ctrls[2].$options),formCtrl.$addControl(modelCtrl),attr.$observe("name",function(newValue){modelCtrl.$name!==newValue&&formCtrl.$$renameControl(modelCtrl,newValue)}),scope.$on("$destroy",function(){formCtrl.$removeControl(modelCtrl)})},post:function(scope,element,attr,ctrls){var modelCtrl=ctrls[0];modelCtrl.$options&&modelCtrl.$options.updateOn&&element.on(modelCtrl.$options.updateOn,function(ev){modelCtrl.$$debounceViewValueCommit(ev&&ev.type)}),element.on("blur",function(){modelCtrl.$touched||scope.$apply(function(){modelCtrl.$setTouched()})})}}}}},ngChangeDirective=valueFn({restrict:"A",require:"ngModel",link:function(scope,element,attr,ctrl){ctrl.$viewChangeListeners.push(function(){scope.$eval(attr.ngChange)})}}),requiredDirective=function(){return{restrict:"A",require:"?ngModel",link:function(scope,elm,attr,ctrl){ctrl&&(attr.required=!0,ctrl.$validators.required=function(value){return!attr.required||!ctrl.$isEmpty(value)},attr.$observe("required",function(){ctrl.$validate()}))}}},patternDirective=function(){return{restrict:"A",require:"?ngModel",link:function(scope,elm,attr,ctrl){if(ctrl){var regexp,patternExp=attr.ngPattern||attr.pattern;attr.$observe("pattern",function(regex){if(isString(regex)&&regex.length>0&&(regex=new RegExp(regex)),regex&&!regex.test)throw minErr("ngPattern")("noregexp","Expected {0} to be a RegExp but was {1}. Element: {2}",patternExp,regex,startingTag(elm));regexp=regex||undefined,ctrl.$validate()}),ctrl.$validators.pattern=function(value){return ctrl.$isEmpty(value)||isUndefined(regexp)||regexp.test(value)}}}}},maxlengthDirective=function(){return{restrict:"A",require:"?ngModel",link:function(scope,elm,attr,ctrl){if(ctrl){var maxlength=0;attr.$observe("maxlength",function(value){maxlength=int(value)||0,ctrl.$validate()}),ctrl.$validators.maxlength=function(modelValue,viewValue){return ctrl.$isEmpty(modelValue)||viewValue.length<=maxlength}}}}},minlengthDirective=function(){return{restrict:"A",require:"?ngModel",link:function(scope,elm,attr,ctrl){if(ctrl){var minlength=0;attr.$observe("minlength",function(value){minlength=int(value)||0,ctrl.$validate()}),ctrl.$validators.minlength=function(modelValue,viewValue){return ctrl.$isEmpty(modelValue)||viewValue.length>=minlength}}}}},ngListDirective=function(){return{restrict:"A",priority:100,require:"ngModel",link:function(scope,element,attr,ctrl){var ngList=element.attr(attr.$attr.ngList)||", ",trimValues="false"!==attr.ngTrim,separator=trimValues?trim(ngList):ngList,parse=function(viewValue){if(!isUndefined(viewValue)){var list=[];return viewValue&&forEach(viewValue.split(separator),function(value){value&&list.push(trimValues?trim(value):value)}),list}};ctrl.$parsers.push(parse),ctrl.$formatters.push(function(value){return isArray(value)?value.join(ngList):undefined}),ctrl.$isEmpty=function(value){return!value||!value.length}}}},CONSTANT_VALUE_REGEXP=/^(true|false|\d+)$/,ngValueDirective=function(){return{restrict:"A",priority:100,compile:function(tpl,tplAttr){return CONSTANT_VALUE_REGEXP.test(tplAttr.ngValue)?function(scope,elm,attr){attr.$set("value",scope.$eval(attr.ngValue))}:function(scope,elm,attr){scope.$watch(attr.ngValue,function(value){attr.$set("value",value)})}}}},ngModelOptionsDirective=function(){return{restrict:"A",controller:["$scope","$attrs",function($scope,$attrs){var that=this;this.$options=$scope.$eval($attrs.ngModelOptions),this.$options.updateOn!==undefined?(this.$options.updateOnDefault=!1,this.$options.updateOn=trim(this.$options.updateOn.replace(DEFAULT_REGEXP,function(){return that.$options.updateOnDefault=!0," "}))):this.$options.updateOnDefault=!0}]}},ngBindDirective=["$compile",function($compile){return{restrict:"AC",compile:function(templateElement){return $compile.$$addBindingClass(templateElement),function(scope,element,attr){$compile.$$addBindingInfo(element,attr.ngBind),scope.$watch(attr.ngBind,function(value){element.text(value==undefined?"":value)})}}}}],ngBindTemplateDirective=["$interpolate","$compile",function($interpolate,$compile){return{compile:function(templateElement){return $compile.$$addBindingClass(templateElement),function(scope,element,attr){var interpolateFn=$interpolate(element.attr(attr.$attr.ngBindTemplate));$compile.$$addBindingInfo(element,interpolateFn.expressions),attr.$observe("ngBindTemplate",function(value){element.text(value)})}}}}],ngBindHtmlDirective=["$sce","$parse","$compile",function($sce,$parse,$compile){return{restrict:"A",compile:function(tElement,tAttrs){var ngBindHtmlGetter=$parse(tAttrs.ngBindHtml),ngBindHtmlWatch=$parse(tAttrs.ngBindHtml,function(value){return(value||"").toString()});return $compile.$$addBindingClass(tElement),function(scope,element,attr){$compile.$$addBindingInfo(element,attr.ngBindHtml),scope.$watch(ngBindHtmlWatch,function(){element.html($sce.getTrustedHtml(ngBindHtmlGetter(scope))||"")})}}}}],ngClassDirective=classDirective("",!0),ngClassOddDirective=classDirective("Odd",0),ngClassEvenDirective=classDirective("Even",1),ngCloakDirective=ngDirective({compile:function(element,attr){attr.$set("ngCloak",undefined),element.removeClass("ng-cloak")}}),ngControllerDirective=[function(){return{restrict:"A",scope:!0,controller:"@",priority:500}}],ngEventDirectives={},forceAsyncEvents={blur:!0,focus:!0};forEach("click dblclick mousedown mouseup mouseover mouseout mousemove mouseenter mouseleave keydown keyup keypress submit focus blur copy cut paste".split(" "),function(eventName){var directiveName=directiveNormalize("ng-"+eventName);ngEventDirectives[directiveName]=["$parse","$rootScope",function($parse,$rootScope){return{restrict:"A",compile:function($element,attr){var fn=$parse(attr[directiveName]);return function(scope,element){element.on(eventName,function(event){var callback=function(){fn(scope,{$event:event})};forceAsyncEvents[eventName]&&$rootScope.$$phase?scope.$evalAsync(callback):scope.$apply(callback)})}}}}]});var ngIfDirective=["$animate",function($animate){return{multiElement:!0,transclude:"element",priority:600,terminal:!0,restrict:"A",$$tlb:!0,link:function($scope,$element,$attr,ctrl,$transclude){var block,childScope,previousElements;$scope.$watch($attr.ngIf,function(value){value?childScope||$transclude(function(clone,newScope){childScope=newScope,clone[clone.length++]=document.createComment(" end ngIf: "+$attr.ngIf+" "),block={clone:clone},$animate.enter(clone,$element.parent(),$element)}):(previousElements&&(previousElements.remove(),previousElements=null),childScope&&(childScope.$destroy(),childScope=null),block&&(previousElements=getBlockNodes(block.clone),$animate.leave(previousElements).then(function(){previousElements=null}),block=null))})}}}],ngIncludeDirective=["$templateRequest","$anchorScroll","$animate","$sce",function($templateRequest,$anchorScroll,$animate,$sce){return{restrict:"ECA",priority:400,terminal:!0,transclude:"element",controller:angular.noop,compile:function(element,attr){var srcExp=attr.ngInclude||attr.src,onloadExp=attr.onload||"",autoScrollExp=attr.autoscroll;return function(scope,$element,$attr,ctrl,$transclude){var currentScope,previousElement,currentElement,changeCounter=0,cleanupLastIncludeContent=function(){previousElement&&(previousElement.remove(),previousElement=null),currentScope&&(currentScope.$destroy(),currentScope=null),currentElement&&($animate.leave(currentElement).then(function(){previousElement=null}),previousElement=currentElement,currentElement=null)};scope.$watch($sce.parseAsResourceUrl(srcExp),function(src){var afterAnimation=function(){!isDefined(autoScrollExp)||autoScrollExp&&!scope.$eval(autoScrollExp)||$anchorScroll()},thisChangeId=++changeCounter;src?($templateRequest(src,!0).then(function(response){if(thisChangeId===changeCounter){var newScope=scope.$new();ctrl.template=response;var clone=$transclude(newScope,function(clone){cleanupLastIncludeContent(),$animate.enter(clone,null,$element).then(afterAnimation)});currentScope=newScope,currentElement=clone,currentScope.$emit("$includeContentLoaded",src),scope.$eval(onloadExp)}},function(){thisChangeId===changeCounter&&(cleanupLastIncludeContent(),scope.$emit("$includeContentError",src))}),scope.$emit("$includeContentRequested",src)):(cleanupLastIncludeContent(),ctrl.template=null)})}}}}],ngIncludeFillContentDirective=["$compile",function($compile){return{restrict:"ECA",priority:-400,require:"ngInclude",link:function(scope,$element,$attr,ctrl){return/SVG/.test($element[0].toString())?($element.empty(),void $compile(jqLiteBuildFragment(ctrl.template,document).childNodes)(scope,function(clone){$element.append(clone)},undefined,undefined,$element)):($element.html(ctrl.template),void $compile($element.contents())(scope))}}}],ngInitDirective=ngDirective({priority:450,compile:function(){return{pre:function(scope,element,attrs){scope.$eval(attrs.ngInit)}}}}),ngNonBindableDirective=ngDirective({terminal:!0,priority:1e3}),ngPluralizeDirective=["$locale","$interpolate",function($locale,$interpolate){var BRACE=/{}/g;return{restrict:"EA",link:function(scope,element,attr){var numberExp=attr.count,whenExp=attr.$attr.when&&element.attr(attr.$attr.when),offset=attr.offset||0,whens=scope.$eval(whenExp)||{},whensExpFns={},startSymbol=$interpolate.startSymbol(),endSymbol=$interpolate.endSymbol(),isWhen=/^when(Minus)?(.+)$/;forEach(attr,function(expression,attributeName){isWhen.test(attributeName)&&(whens[lowercase(attributeName.replace("when","").replace("Minus","-"))]=element.attr(attr.$attr[attributeName]))}),forEach(whens,function(expression,key){whensExpFns[key]=$interpolate(expression.replace(BRACE,startSymbol+numberExp+"-"+offset+endSymbol))}),scope.$watch(function(){var value=parseFloat(scope.$eval(numberExp));return isNaN(value)?"":(value in whens||(value=$locale.pluralCat(value-offset)),whensExpFns[value](scope))},function(newVal){element.text(newVal)})}}}],ngRepeatDirective=["$parse","$animate",function($parse,$animate){var NG_REMOVED="$$NG_REMOVED",ngRepeatMinErr=minErr("ngRepeat"),updateScope=function(scope,index,valueIdentifier,value,keyIdentifier,key,arrayLength){scope[valueIdentifier]=value,keyIdentifier&&(scope[keyIdentifier]=key),scope.$index=index,scope.$first=0===index,scope.$last=index===arrayLength-1,scope.$middle=!(scope.$first||scope.$last),scope.$odd=!(scope.$even=0===(1&index))},getBlockStart=function(block){return block.clone[0]},getBlockEnd=function(block){return block.clone[block.clone.length-1]};return{restrict:"A",multiElement:!0,transclude:"element",priority:1e3,terminal:!0,$$tlb:!0,compile:function($element,$attr){var expression=$attr.ngRepeat,ngRepeatEndComment=document.createComment(" end ngRepeat: "+expression+" "),match=expression.match(/^\s*([\s\S]+?)\s+in\s+([\s\S]+?)(?:\s+as\s+([\s\S]+?))?(?:\s+track\s+by\s+([\s\S]+?))?\s*$/);if(!match)throw ngRepeatMinErr("iexp","Expected expression in form of '_item_ in _collection_[ track by _id_]' but got '{0}'.",expression);var lhs=match[1],rhs=match[2],aliasAs=match[3],trackByExp=match[4];if(match=lhs.match(/^(?:([\$\w]+)|\(([\$\w]+)\s*,\s*([\$\w]+)\))$/),!match)throw ngRepeatMinErr("iidexp","'_item_' in '_item_ in _collection_' should be an identifier or '(_key_, _value_)' expression, but got '{0}'.",lhs);var valueIdentifier=match[3]||match[1],keyIdentifier=match[2];if(aliasAs&&(!/^[$a-zA-Z_][$a-zA-Z0-9_]*$/.test(aliasAs)||/^(null|undefined|this|\$index|\$first|\$middle|\$last|\$even|\$odd|\$parent)$/.test(aliasAs)))throw ngRepeatMinErr("badident","alias '{0}' is invalid --- must be a valid JS identifier which is not a reserved name.",aliasAs);var trackByExpGetter,trackByIdExpFn,trackByIdArrayFn,trackByIdObjFn,hashFnLocals={$id:hashKey};return trackByExp?trackByExpGetter=$parse(trackByExp):(trackByIdArrayFn=function(key,value){return hashKey(value)},trackByIdObjFn=function(key){return key}),function($scope,$element,$attr,ctrl,$transclude){trackByExpGetter&&(trackByIdExpFn=function(key,value,index){return keyIdentifier&&(hashFnLocals[keyIdentifier]=key),hashFnLocals[valueIdentifier]=value,hashFnLocals.$index=index,trackByExpGetter($scope,hashFnLocals)});var lastBlockMap=createMap();$scope.$watchCollection(rhs,function(collection){var index,length,nextNode,collectionLength,key,value,trackById,trackByIdFn,collectionKeys,block,nextBlockOrder,elementsToRemove,previousNode=$element[0],nextBlockMap=createMap();if(aliasAs&&($scope[aliasAs]=collection),isArrayLike(collection))collectionKeys=collection,trackByIdFn=trackByIdExpFn||trackByIdArrayFn;else{trackByIdFn=trackByIdExpFn||trackByIdObjFn,collectionKeys=[];for(var itemKey in collection)collection.hasOwnProperty(itemKey)&&"$"!=itemKey.charAt(0)&&collectionKeys.push(itemKey);collectionKeys.sort()}for(collectionLength=collectionKeys.length,nextBlockOrder=new Array(collectionLength),index=0;collectionLength>index;index++)if(key=collection===collectionKeys?index:collectionKeys[index],value=collection[key],trackById=trackByIdFn(key,value,index),lastBlockMap[trackById])block=lastBlockMap[trackById],delete lastBlockMap[trackById],nextBlockMap[trackById]=block,nextBlockOrder[index]=block;else{if(nextBlockMap[trackById])throw forEach(nextBlockOrder,function(block){block&&block.scope&&(lastBlockMap[block.id]=block)}),ngRepeatMinErr("dupes","Duplicates in a repeater are not allowed. Use 'track by' expression to specify unique keys. Repeater: {0}, Duplicate key: {1}, Duplicate value: {2}",expression,trackById,toJson(value));nextBlockOrder[index]={id:trackById,scope:undefined,clone:undefined},nextBlockMap[trackById]=!0}for(var blockKey in lastBlockMap){if(block=lastBlockMap[blockKey],elementsToRemove=getBlockNodes(block.clone),$animate.leave(elementsToRemove),elementsToRemove[0].parentNode)for(index=0,length=elementsToRemove.length;length>index;index++)elementsToRemove[index][NG_REMOVED]=!0;block.scope.$destroy()}for(index=0;collectionLength>index;index++)if(key=collection===collectionKeys?index:collectionKeys[index],value=collection[key],block=nextBlockOrder[index],block.scope){nextNode=previousNode;do nextNode=nextNode.nextSibling;while(nextNode&&nextNode[NG_REMOVED]);getBlockStart(block)!=nextNode&&$animate.move(getBlockNodes(block.clone),null,jqLite(previousNode)),previousNode=getBlockEnd(block),updateScope(block.scope,index,valueIdentifier,value,keyIdentifier,key,collectionLength)}else $transclude(function(clone,scope){block.scope=scope;var endNode=ngRepeatEndComment.cloneNode(!1);clone[clone.length++]=endNode,$animate.enter(clone,null,jqLite(previousNode)),previousNode=endNode,block.clone=clone,nextBlockMap[block.id]=block,updateScope(block.scope,index,valueIdentifier,value,keyIdentifier,key,collectionLength)});lastBlockMap=nextBlockMap})}}}}],ngShowDirective=["$animate",function($animate){return{restrict:"A",multiElement:!0,link:function(scope,element,attr){scope.$watch(attr.ngShow,function(value){$animate[value?"removeClass":"addClass"](element,"ng-hide")})}}}],ngHideDirective=["$animate",function($animate){return{restrict:"A",multiElement:!0,link:function(scope,element,attr){scope.$watch(attr.ngHide,function(value){$animate[value?"addClass":"removeClass"](element,"ng-hide")})}}}],ngStyleDirective=ngDirective(function(scope,element,attr){scope.$watch(attr.ngStyle,function(newStyles,oldStyles){oldStyles&&newStyles!==oldStyles&&forEach(oldStyles,function(val,style){element.css(style,"")}),newStyles&&element.css(newStyles)},!0)}),ngSwitchDirective=["$animate",function($animate){return{restrict:"EA",require:"ngSwitch",controller:["$scope",function(){this.cases={}}],link:function(scope,element,attr,ngSwitchController){var watchExpr=attr.ngSwitch||attr.on,selectedTranscludes=[],selectedElements=[],previousLeaveAnimations=[],selectedScopes=[],spliceFactory=function(array,index){return function(){array.splice(index,1)}};scope.$watch(watchExpr,function(value){var i,ii;for(i=0,ii=previousLeaveAnimations.length;ii>i;++i)$animate.cancel(previousLeaveAnimations[i]);for(previousLeaveAnimations.length=0,i=0,ii=selectedScopes.length;ii>i;++i){var selected=getBlockNodes(selectedElements[i].clone);selectedScopes[i].$destroy();var promise=previousLeaveAnimations[i]=$animate.leave(selected);promise.then(spliceFactory(previousLeaveAnimations,i))}selectedElements.length=0,selectedScopes.length=0,(selectedTranscludes=ngSwitchController.cases["!"+value]||ngSwitchController.cases["?"])&&forEach(selectedTranscludes,function(selectedTransclude){selectedTransclude.transclude(function(caseElement,selectedScope){selectedScopes.push(selectedScope);var anchor=selectedTransclude.element;caseElement[caseElement.length++]=document.createComment(" end ngSwitchWhen: ");var block={clone:caseElement};
selectedElements.push(block),$animate.enter(caseElement,anchor.parent(),anchor)})})})}}}],ngSwitchWhenDirective=ngDirective({transclude:"element",priority:1200,require:"^ngSwitch",multiElement:!0,link:function(scope,element,attrs,ctrl,$transclude){ctrl.cases["!"+attrs.ngSwitchWhen]=ctrl.cases["!"+attrs.ngSwitchWhen]||[],ctrl.cases["!"+attrs.ngSwitchWhen].push({transclude:$transclude,element:element})}}),ngSwitchDefaultDirective=ngDirective({transclude:"element",priority:1200,require:"^ngSwitch",multiElement:!0,link:function(scope,element,attr,ctrl,$transclude){ctrl.cases["?"]=ctrl.cases["?"]||[],ctrl.cases["?"].push({transclude:$transclude,element:element})}}),ngTranscludeDirective=ngDirective({restrict:"EAC",link:function($scope,$element,$attrs,controller,$transclude){if(!$transclude)throw minErr("ngTransclude")("orphan","Illegal use of ngTransclude directive in the template! No parent directive that requires a transclusion found. Element: {0}",startingTag($element));$transclude(function(clone){$element.empty(),$element.append(clone)})}}),scriptDirective=["$templateCache",function($templateCache){return{restrict:"E",terminal:!0,compile:function(element,attr){if("text/ng-template"==attr.type){var templateUrl=attr.id,text=element[0].text;$templateCache.put(templateUrl,text)}}}}],ngOptionsMinErr=minErr("ngOptions"),ngOptionsDirective=valueFn({restrict:"A",terminal:!0}),selectDirective=["$compile","$parse",function($compile,$parse){var NG_OPTIONS_REGEXP=/^\s*([\s\S]+?)(?:\s+as\s+([\s\S]+?))?(?:\s+group\s+by\s+([\s\S]+?))?\s+for\s+(?:([\$\w][\$\w]*)|(?:\(\s*([\$\w][\$\w]*)\s*,\s*([\$\w][\$\w]*)\s*\)))\s+in\s+([\s\S]+?)(?:\s+track\s+by\s+([\s\S]+?))?$/,nullModelCtrl={$setViewValue:noop};return{restrict:"E",require:["select","?ngModel"],controller:["$element","$scope","$attrs",function($element,$scope,$attrs){var nullOption,unknownOption,self=this,optionsMap={},ngModelCtrl=nullModelCtrl;self.databound=$attrs.ngModel,self.init=function(ngModelCtrl_,nullOption_,unknownOption_){ngModelCtrl=ngModelCtrl_,nullOption=nullOption_,unknownOption=unknownOption_},self.addOption=function(value,element){assertNotHasOwnProperty(value,'"option value"'),optionsMap[value]=!0,ngModelCtrl.$viewValue==value&&($element.val(value),unknownOption.parent()&&unknownOption.remove()),element[0].hasAttribute("selected")&&(element[0].selected=!0)},self.removeOption=function(value){this.hasOption(value)&&(delete optionsMap[value],ngModelCtrl.$viewValue==value&&this.renderUnknownOption(value))},self.renderUnknownOption=function(val){var unknownVal="? "+hashKey(val)+" ?";unknownOption.val(unknownVal),$element.prepend(unknownOption),$element.val(unknownVal),unknownOption.prop("selected",!0)},self.hasOption=function(value){return optionsMap.hasOwnProperty(value)},$scope.$on("$destroy",function(){self.renderUnknownOption=noop})}],link:function(scope,element,attr,ctrls){function setupAsSingle(scope,selectElement,ngModelCtrl,selectCtrl){ngModelCtrl.$render=function(){var viewValue=ngModelCtrl.$viewValue;selectCtrl.hasOption(viewValue)?(unknownOption.parent()&&unknownOption.remove(),selectElement.val(viewValue),""===viewValue&&emptyOption.prop("selected",!0)):isUndefined(viewValue)&&emptyOption?selectElement.val(""):selectCtrl.renderUnknownOption(viewValue)},selectElement.on("change",function(){scope.$apply(function(){unknownOption.parent()&&unknownOption.remove(),ngModelCtrl.$setViewValue(selectElement.val())})})}function setupAsMultiple(scope,selectElement,ctrl){var lastView;ctrl.$render=function(){var items=new HashMap(ctrl.$viewValue);forEach(selectElement.find("option"),function(option){option.selected=isDefined(items.get(option.value))})},scope.$watch(function(){equals(lastView,ctrl.$viewValue)||(lastView=shallowCopy(ctrl.$viewValue),ctrl.$render())}),selectElement.on("change",function(){scope.$apply(function(){var array=[];forEach(selectElement.find("option"),function(option){option.selected&&array.push(option.value)}),ctrl.$setViewValue(array)})})}function setupAsOptions(scope,selectElement,ctrl){function getSelectedSet(){var selectedSet=!1;if(multiple){var modelValue=ctrl.$modelValue;if(trackFn&&isArray(modelValue)){selectedSet=new HashMap([]);for(var locals={},trackIndex=0;trackIndex<modelValue.length;trackIndex++)locals[valueName]=modelValue[trackIndex],selectedSet.put(trackFn(scope,locals),modelValue[trackIndex])}else selectedSet=new HashMap(modelValue)}return selectedSet}function scheduleRendering(){renderScheduled||(scope.$$postDigest(render),renderScheduled=!0)}function render(){renderScheduled=!1;var optionGroupName,optionGroup,option,existingParent,existingOptions,existingOption,key,groupLength,length,groupIndex,index,selected,lastElement,element,label,optionGroups={"":[]},optionGroupNames=[""],modelValue=ctrl.$modelValue,values=valuesFn(scope)||[],keys=keyName?sortedKeys(values):values,locals={},selectedSet=getSelectedSet();for(index=0;length=keys.length,length>index;index++){if(key=index,keyName){if(key=keys[index],"$"===key.charAt(0))continue;locals[keyName]=key}if(locals[valueName]=values[key],optionGroupName=groupByFn(scope,locals)||"",(optionGroup=optionGroups[optionGroupName])||(optionGroup=optionGroups[optionGroupName]=[],optionGroupNames.push(optionGroupName)),multiple)selected=isDefined(selectedSet.remove(trackFn?trackFn(scope,locals):valueFn(scope,locals)));else{if(trackFn){var modelCast={};modelCast[valueName]=modelValue,selected=trackFn(scope,modelCast)===trackFn(scope,locals)}else selected=modelValue===valueFn(scope,locals);selectedSet=selectedSet||selected}label=displayFn(scope,locals),label=isDefined(label)?label:"",optionGroup.push({id:trackFn?trackFn(scope,locals):keyName?keys[index]:index,label:label,selected:selected})}for(multiple||(nullOption||null===modelValue?optionGroups[""].unshift({id:"",label:"",selected:!selectedSet}):selectedSet||optionGroups[""].unshift({id:"?",label:"",selected:!0})),groupIndex=0,groupLength=optionGroupNames.length;groupLength>groupIndex;groupIndex++){for(optionGroupName=optionGroupNames[groupIndex],optionGroup=optionGroups[optionGroupName],optionGroupsCache.length<=groupIndex?(existingParent={element:optGroupTemplate.clone().attr("label",optionGroupName),label:optionGroup.label},existingOptions=[existingParent],optionGroupsCache.push(existingOptions),selectElement.append(existingParent.element)):(existingOptions=optionGroupsCache[groupIndex],existingParent=existingOptions[0],existingParent.label!=optionGroupName&&existingParent.element.attr("label",existingParent.label=optionGroupName)),lastElement=null,index=0,length=optionGroup.length;length>index;index++)option=optionGroup[index],(existingOption=existingOptions[index+1])?(lastElement=existingOption.element,existingOption.label!==option.label&&lastElement.text(existingOption.label=option.label),existingOption.id!==option.id&&lastElement.val(existingOption.id=option.id),lastElement[0].selected!==option.selected&&(lastElement.prop("selected",existingOption.selected=option.selected),msie&&lastElement.prop("selected",existingOption.selected))):(""===option.id&&nullOption?element=nullOption:(element=optionTemplate.clone()).val(option.id).prop("selected",option.selected).attr("selected",option.selected).text(option.label),existingOptions.push(existingOption={element:element,label:option.label,id:option.id,selected:option.selected}),selectCtrl.addOption(option.label,element),lastElement?lastElement.after(element):existingParent.element.append(element),lastElement=element);for(index++;existingOptions.length>index;)option=existingOptions.pop(),selectCtrl.removeOption(option.label),option.element.remove()}for(;optionGroupsCache.length>groupIndex;)optionGroupsCache.pop()[0].element.remove()}var match;if(!(match=optionsExp.match(NG_OPTIONS_REGEXP)))throw ngOptionsMinErr("iexp","Expected expression in form of '_select_ (as _label_)? for (_key_,)?_value_ in _collection_' but got '{0}'. Element: {1}",optionsExp,startingTag(selectElement));var displayFn=$parse(match[2]||match[1]),valueName=match[4]||match[6],keyName=match[5],groupByFn=$parse(match[3]||""),valueFn=$parse(match[2]?match[1]:valueName),valuesFn=$parse(match[7]),track=match[8],trackFn=track?$parse(match[8]):null,optionGroupsCache=[[{element:selectElement,label:""}]];nullOption&&($compile(nullOption)(scope),nullOption.removeClass("ng-scope"),nullOption.remove()),selectElement.empty(),selectElement.on("change",function(){scope.$apply(function(){var optionGroup,key,value,optionElement,index,groupIndex,length,groupLength,trackIndex,collection=valuesFn(scope)||[],locals={};if(multiple){for(value=[],groupIndex=0,groupLength=optionGroupsCache.length;groupLength>groupIndex;groupIndex++)for(optionGroup=optionGroupsCache[groupIndex],index=1,length=optionGroup.length;length>index;index++)if((optionElement=optionGroup[index].element)[0].selected){if(key=optionElement.val(),keyName&&(locals[keyName]=key),trackFn)for(trackIndex=0;trackIndex<collection.length&&(locals[valueName]=collection[trackIndex],trackFn(scope,locals)!=key);trackIndex++);else locals[valueName]=collection[key];value.push(valueFn(scope,locals))}}else if(key=selectElement.val(),"?"==key)value=undefined;else if(""===key)value=null;else if(trackFn){for(trackIndex=0;trackIndex<collection.length;trackIndex++)if(locals[valueName]=collection[trackIndex],trackFn(scope,locals)==key){value=valueFn(scope,locals);break}}else locals[valueName]=collection[key],keyName&&(locals[keyName]=key),value=valueFn(scope,locals);ctrl.$setViewValue(value),render()})}),ctrl.$render=render,scope.$watchCollection(valuesFn,scheduleRendering),scope.$watchCollection(function(){var locals={},values=valuesFn(scope);if(values){for(var toDisplay=new Array(values.length),i=0,ii=values.length;ii>i;i++)locals[valueName]=values[i],toDisplay[i]=displayFn(scope,locals);return toDisplay}},scheduleRendering),multiple&&scope.$watchCollection(function(){return ctrl.$modelValue},scheduleRendering)}if(ctrls[1]){for(var emptyOption,selectCtrl=ctrls[0],ngModelCtrl=ctrls[1],multiple=attr.multiple,optionsExp=attr.ngOptions,nullOption=!1,renderScheduled=!1,optionTemplate=jqLite(document.createElement("option")),optGroupTemplate=jqLite(document.createElement("optgroup")),unknownOption=optionTemplate.clone(),i=0,children=element.children(),ii=children.length;ii>i;i++)if(""===children[i].value){emptyOption=nullOption=children.eq(i);break}selectCtrl.init(ngModelCtrl,nullOption,unknownOption),multiple&&(ngModelCtrl.$isEmpty=function(value){return!value||0===value.length}),optionsExp?setupAsOptions(scope,element,ngModelCtrl):multiple?setupAsMultiple(scope,element,ngModelCtrl):setupAsSingle(scope,element,ngModelCtrl,selectCtrl)}}}}],optionDirective=["$interpolate",function($interpolate){var nullSelectCtrl={addOption:noop,removeOption:noop};return{restrict:"E",priority:100,compile:function(element,attr){if(isUndefined(attr.value)){var interpolateFn=$interpolate(element.text(),!0);interpolateFn||attr.$set("value",element.text())}return function(scope,element,attr){var selectCtrlName="$selectController",parent=element.parent(),selectCtrl=parent.data(selectCtrlName)||parent.parent().data(selectCtrlName);selectCtrl&&selectCtrl.databound||(selectCtrl=nullSelectCtrl),interpolateFn?scope.$watch(interpolateFn,function(newVal,oldVal){attr.$set("value",newVal),oldVal!==newVal&&selectCtrl.removeOption(oldVal),selectCtrl.addOption(newVal,element)}):selectCtrl.addOption(attr.value,element),element.on("$destroy",function(){selectCtrl.removeOption(attr.value)})}}}}],styleDirective=valueFn({restrict:"E",terminal:!1});return window.angular.bootstrap?void console.log("WARNING: Tried to load angular more than once."):(bindJQuery(),publishExternalAPI(angular),void jqLite(document).ready(function(){angularInit(document,bootstrap)}))}(window,document),!window.angular.$$csp()&&window.angular.element(document).find("head").prepend('<style type="text/css">@charset "UTF-8";[ng\\:cloak],[ng-cloak],[data-ng-cloak],[x-ng-cloak],.ng-cloak,.x-ng-cloak,.ng-hide:not(.ng-animate){display:none !important;}ng\\:form{display:block;}</style>'),function(window,angular){"use strict";function $RouteProvider(){function inherit(parent,extra){return angular.extend(new(angular.extend(function(){},{prototype:parent})),extra)}function pathRegExp(path,opts){var insensitive=opts.caseInsensitiveMatch,ret={originalPath:path,regexp:path},keys=ret.keys=[];return path=path.replace(/([().])/g,"\\$1").replace(/(\/)?:(\w+)([\?\*])?/g,function(_,slash,key,option){var optional="?"===option?option:null,star="*"===option?option:null;return keys.push({name:key,optional:!!optional}),slash=slash||"",""+(optional?"":slash)+"(?:"+(optional?slash:"")+(star&&"(.+?)"||"([^/]+)")+(optional||"")+")"+(optional||"")}).replace(/([\/$\*])/g,"\\$1"),ret.regexp=new RegExp("^"+path+"$",insensitive?"i":""),ret}var routes={};this.when=function(path,route){if(routes[path]=angular.extend({reloadOnSearch:!0},route,path&&pathRegExp(path,route)),path){var redirectPath="/"==path[path.length-1]?path.substr(0,path.length-1):path+"/";routes[redirectPath]=angular.extend({redirectTo:path},pathRegExp(redirectPath,route))}return this},this.otherwise=function(params){return"string"==typeof params&&(params={redirectTo:params}),this.when(null,params),this},this.$get=["$rootScope","$location","$routeParams","$q","$injector","$templateRequest","$sce",function($rootScope,$location,$routeParams,$q,$injector,$templateRequest,$sce){function switchRouteMatcher(on,route){var keys=route.keys,params={};if(!route.regexp)return null;var m=route.regexp.exec(on);if(!m)return null;for(var i=1,len=m.length;len>i;++i){var key=keys[i-1],val=m[i];key&&val&&(params[key.name]=val)}return params}function updateRoute(){var next=parseRoute(),last=$route.current;next&&last&&next.$$route===last.$$route&&angular.equals(next.pathParams,last.pathParams)&&!next.reloadOnSearch&&!forceReload?(last.params=next.params,angular.copy(last.params,$routeParams),$rootScope.$broadcast("$routeUpdate",last)):(next||last)&&(forceReload=!1,$rootScope.$broadcast("$routeChangeStart",next,last),$route.current=next,next&&next.redirectTo&&(angular.isString(next.redirectTo)?$location.path(interpolate(next.redirectTo,next.params)).search(next.params).replace():$location.url(next.redirectTo(next.pathParams,$location.path(),$location.search())).replace()),$q.when(next).then(function(){if(next){var template,templateUrl,locals=angular.extend({},next.resolve);return angular.forEach(locals,function(value,key){locals[key]=angular.isString(value)?$injector.get(value):$injector.invoke(value,null,null,key)}),angular.isDefined(template=next.template)?angular.isFunction(template)&&(template=template(next.params)):angular.isDefined(templateUrl=next.templateUrl)&&(angular.isFunction(templateUrl)&&(templateUrl=templateUrl(next.params)),templateUrl=$sce.getTrustedResourceUrl(templateUrl),angular.isDefined(templateUrl)&&(next.loadedTemplateUrl=templateUrl,template=$templateRequest(templateUrl))),angular.isDefined(template)&&(locals.$template=template),$q.all(locals)}}).then(function(locals){next==$route.current&&(next&&(next.locals=locals,angular.copy(next.params,$routeParams)),$rootScope.$broadcast("$routeChangeSuccess",next,last))},function(error){next==$route.current&&$rootScope.$broadcast("$routeChangeError",next,last,error)}))}function parseRoute(){var params,match;return angular.forEach(routes,function(route){!match&&(params=switchRouteMatcher($location.path(),route))&&(match=inherit(route,{params:angular.extend({},$location.search(),params),pathParams:params}),match.$$route=route)}),match||routes[null]&&inherit(routes[null],{params:{},pathParams:{}})}function interpolate(string,params){var result=[];return angular.forEach((string||"").split(":"),function(segment,i){if(0===i)result.push(segment);else{var segmentMatch=segment.match(/(\w+)(.*)/),key=segmentMatch[1];result.push(params[key]),result.push(segmentMatch[2]||""),delete params[key]}}),result.join("")}var forceReload=!1,$route={routes:routes,reload:function(){forceReload=!0,$rootScope.$evalAsync(updateRoute)},updateParams:function(newParams){if(!this.current||!this.current.$$route)throw $routeMinErr("norout","Tried updating route when with no current route");var searchParams={},self=this;angular.forEach(Object.keys(newParams),function(key){self.current.pathParams[key]||(searchParams[key]=newParams[key])}),newParams=angular.extend({},this.current.params,newParams),$location.path(interpolate(this.current.$$route.originalPath,newParams)),$location.search(angular.extend({},$location.search(),searchParams))}};return $rootScope.$on("$locationChangeSuccess",updateRoute),$route}]}function $RouteParamsProvider(){this.$get=function(){return{}}}function ngViewFactory($route,$anchorScroll,$animate){return{restrict:"ECA",terminal:!0,priority:400,transclude:"element",link:function(scope,$element,attr,ctrl,$transclude){function cleanupLastView(){previousLeaveAnimation&&($animate.cancel(previousLeaveAnimation),previousLeaveAnimation=null),currentScope&&(currentScope.$destroy(),currentScope=null),currentElement&&(previousLeaveAnimation=$animate.leave(currentElement),previousLeaveAnimation.then(function(){previousLeaveAnimation=null}),currentElement=null)}function update(){var locals=$route.current&&$route.current.locals,template=locals&&locals.$template;if(angular.isDefined(template)){var newScope=scope.$new(),current=$route.current,clone=$transclude(newScope,function(clone){$animate.enter(clone,null,currentElement||$element).then(function(){!angular.isDefined(autoScrollExp)||autoScrollExp&&!scope.$eval(autoScrollExp)||$anchorScroll()}),cleanupLastView()});currentElement=clone,currentScope=current.scope=newScope,currentScope.$emit("$viewContentLoaded"),currentScope.$eval(onloadExp)}else cleanupLastView()}var currentScope,currentElement,previousLeaveAnimation,autoScrollExp=attr.autoscroll,onloadExp=attr.onload||"";scope.$on("$routeChangeSuccess",update),update()}}}function ngViewFillContentFactory($compile,$controller,$route){return{restrict:"ECA",priority:-400,link:function(scope,$element){var current=$route.current,locals=current.locals;$element.html(locals.$template);var link=$compile($element.contents());if(current.controller){locals.$scope=scope;var controller=$controller(current.controller,locals);current.controllerAs&&(scope[current.controllerAs]=controller),$element.data("$ngControllerController",controller),$element.children().data("$ngControllerController",controller)}link(scope)}}}var ngRouteModule=angular.module("ngRoute",["ng"]).provider("$route",$RouteProvider),$routeMinErr=angular.$$minErr("ngRoute");ngRouteModule.provider("$routeParams",$RouteParamsProvider),ngRouteModule.directive("ngView",ngViewFactory),ngRouteModule.directive("ngView",ngViewFillContentFactory),ngViewFactory.$inject=["$route","$anchorScroll","$animate"],ngViewFillContentFactory.$inject=["$compile","$controller","$route"]}(window,window.angular),function(window,angular,undefined){"use strict";angular.module("ngCookies",["ng"]).factory("$cookies",["$rootScope","$browser",function($rootScope,$browser){function push(){var name,value,browserCookies,updated;for(name in lastCookies)isUndefined(cookies[name])&&$browser.cookies(name,undefined);for(name in cookies)value=cookies[name],angular.isString(value)||(value=""+value,cookies[name]=value),value!==lastCookies[name]&&($browser.cookies(name,value),updated=!0);if(updated){updated=!1,browserCookies=$browser.cookies();for(name in cookies)cookies[name]!==browserCookies[name]&&(isUndefined(browserCookies[name])?delete cookies[name]:cookies[name]=browserCookies[name],updated=!0)}}var lastBrowserCookies,cookies={},lastCookies={},runEval=!1,copy=angular.copy,isUndefined=angular.isUndefined;return $browser.addPollFn(function(){var currentCookies=$browser.cookies();lastBrowserCookies!=currentCookies&&(lastBrowserCookies=currentCookies,copy(currentCookies,lastCookies),copy(currentCookies,cookies),runEval&&$rootScope.$apply())})(),runEval=!0,$rootScope.$watch(push),cookies}]).factory("$cookieStore",["$cookies",function($cookies){return{get:function(key){var value=$cookies[key];return value?angular.fromJson(value):value},put:function(key,value){$cookies[key]=angular.toJson(value)},remove:function(key){delete $cookies[key]}}}])}(window,window.angular),function($,document,undefined){function raw(s){return s}function decoded(s){return unRfc2068(decodeURIComponent(s.replace(pluses," ")))}function unRfc2068(value){return 0===value.indexOf('"')&&(value=value.slice(1,-1).replace(/\\"/g,'"').replace(/\\\\/g,"\\")),value}function fromJSON(value){return config.json?JSON.parse(value):value}var pluses=/\+/g,config=$.cookie=function(key,value,options){if(value!==undefined){if(options=$.extend({},config.defaults,options),null===value&&(options.expires=-1),"number"==typeof options.expires){var days=options.expires,t=options.expires=new Date;t.setDate(t.getDate()+days)}return value=config.json?JSON.stringify(value):String(value),document.cookie=[encodeURIComponent(key),"=",config.raw?value:encodeURIComponent(value),options.expires?"; expires="+options.expires.toUTCString():"",options.path?"; path="+options.path:"",options.domain?"; domain="+options.domain:"",options.secure?"; secure":""].join("")}for(var decode=config.raw?raw:decoded,cookies=document.cookie.split("; "),result=key?null:{},i=0,l=cookies.length;l>i;i++){var parts=cookies[i].split("="),name=decode(parts.shift()),cookie=decode(parts.join("="));if(key&&key===name){result=fromJSON(cookie);break}key||(result[name]=fromJSON(cookie))}return result};config.defaults={},$.removeCookie=function(key,options){return null!==$.cookie(key)?($.cookie(key,null,options),!0):!1}}(jQuery,document),angular.module("angular-google-analytics",[]).provider("Analytics",function(){var accountId,displayFeatures,domainName,removeRegExp,experimentId,crossLinkDomains,created=!1,trackRoutes=!0,trackPrefix="",analyticsJS=!1,pageEvent="$routeChangeSuccess",cookieConfig="auto",ecommerce=!1,enhancedEcommerce=!1,enhancedLinkAttribution=!1,ignoreFirstPageLoad=!1,crossDomainLinker=!1,linkerConfig={allowLinker:!0},trackUrlParams=!1,delayScriptTag=!1;this._logs=[],this.setAccount=function(id){return accountId=id,!0},this.trackPages=function(doTrack){return trackRoutes=doTrack,!0},this.trackPrefix=function(prefix){return trackPrefix=prefix,!0},this.setDomainName=function(domain){return domainName=domain,!0},this.useDisplayFeatures=function(val){return displayFeatures=!!val,!0},this.useAnalytics=function(val){return analyticsJS=!!val,!0},this.useEnhancedLinkAttribution=function(val){return enhancedLinkAttribution=!!val,!0},this.useCrossDomainLinker=function(val){return crossDomainLinker=!!val,!0},this.setCrossLinkDomains=function(domains){return crossLinkDomains=domains,!0},this.setPageEvent=function(name){return pageEvent=name,!0},this.setCookieConfig=function(config){return cookieConfig=config,!0},this.useECommerce=function(val,enhanced){return ecommerce=!!val,enhancedEcommerce=!!enhanced,!0},this.setRemoveRegExp=function(regex){return regex instanceof RegExp?(removeRegExp=regex,!0):!1},this.setExperimentId=function(id){return experimentId=id,!0},this.ignoreFirstPageLoad=function(val){return ignoreFirstPageLoad=!!val,!0},this.trackUrlParams=function(val){return trackUrlParams=!!val,!0},this.delayScriptTag=function(val){return delayScriptTag=!!val,!0},this.$get=["$document","$location","$log","$rootScope","$window",function($document,$location,$log,$rootScope,$window){function _gaJs(fn){!analyticsJS&&$window._gaq&&"function"==typeof fn&&fn()}function _analyticsJs(fn){analyticsJS&&$window.ga&&"function"==typeof fn&&fn()}function _generateCommandName(commandName,config){return!angular.isUndefined(config)&&"name"in config&&config.name?config.name+"."+commandName:commandName}function _checkOption(key,config){return key in config&&config[key]}var me=this,getUrl=function(){var url=trackUrlParams?$location.url():$location.path();return removeRegExp?url.replace(removeRegExp,""):url};return this._log=function(){arguments.length>0&&(arguments.length>1&&"warn"===arguments[0]&&$log.warn(Array.prototype.slice.call(arguments,1)),this._logs.push(arguments))},this._createScriptTag=function(){if(!accountId)return void me._log("warn","No account id set to create script tag");if(created)return void me._log("warn","Script tag already created");$window._gaq=[],$window._gaq.push(["_setAccount",accountId]),domainName&&$window._gaq.push(["_setDomainName",domainName]),enhancedLinkAttribution&&$window._gaq.push(["_require","inpage_linkid","//www.google-analytics.com/plugins/ga/inpage_linkid.js"]),trackRoutes&&!ignoreFirstPageLoad&&$window._gaq.push(removeRegExp?["_trackPageview",getUrl()]:["_trackPageview"]);var gaSrc;return gaSrc=displayFeatures?("https:"===document.location.protocol?"https://":"http://")+"stats.g.doubleclick.net/dc.js":("https:"===document.location.protocol?"https://ssl":"http://www")+".google-analytics.com/ga.js",function(){var document=$document[0],ga=document.createElement("script");ga.type="text/javascript",ga.async=!0,ga.src=gaSrc;var s=document.getElementsByTagName("script")[0];s.parentNode.insertBefore(ga,s)}(gaSrc),created=!0},this._createAnalyticsScriptTag=function(){if(!accountId)return void me._log("warn","No account id set to create analytics script tag");if(created)return void me._log("warn","Analytics script tag already created");if(function(i,s,o,g,r,a,m){i.GoogleAnalyticsObject=r,i[r]=i[r]||function(){(i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date,a=s.createElement(o),m=s.getElementsByTagName(o)[0],a.async=1,a.src=g,m.parentNode.insertBefore(a,m)}(window,document,"script","//www.google-analytics.com/analytics.js","ga"),angular.isArray(accountId)?accountId.forEach(function(trackerObj){var options,_cookieConfig="cookieConfig"in trackerObj?trackerObj.cookieConfig:cookieConfig;_checkOption("crossDomainLinker",trackerObj)&&(trackerObj.allowLinker=trackerObj.crossDomainLinker),angular.forEach(["name","allowLinker"],function(key){key in trackerObj&&(angular.isUndefined(options)&&(options={}),options[key]=trackerObj[key])}),angular.isUndefined(options)?$window.ga("create",trackerObj.tracker,_cookieConfig):$window.ga("create",trackerObj.tracker,_cookieConfig,options),options&&"allowLinker"in options&&options.allowLinker&&($window.ga(_generateCommandName("require",trackerObj),"linker"),_checkOption("crossLinkDomains",trackerObj)&&$window.ga(_generateCommandName("linker:autoLink",trackerObj),trackerObj.crossLinkDomains))}):crossDomainLinker?($window.ga("create",accountId,cookieConfig,linkerConfig),$window.ga("require","linker"),crossLinkDomains&&$window.ga("linker:autoLink",crossLinkDomains)):$window.ga("create",accountId,cookieConfig),displayFeatures&&$window.ga("require","displayfeatures"),trackRoutes&&!ignoreFirstPageLoad&&$window.ga("send","pageview",getUrl()),$window.ga&&(ecommerce&&(enhancedEcommerce?$window.ga("require","ec","ec.js"):$window.ga("require","ecommerce","ecommerce.js")),enhancedLinkAttribution&&$window.ga("require","linkid","linkid.js"),experimentId)){var expScript=document.createElement("script"),s=document.getElementsByTagName("script")[0];expScript.src="//www.google-analytics.com/cx/api.js?experiment="+experimentId,s.parentNode.insertBefore(expScript,s)}return created=!0},this._ecommerceEnabled=function(){return ecommerce?enhancedEcommerce?(this._log("warn","Enhanced ecommerce plugin is enabled. Only one plugin(ecommerce/ec) can be used at a time. Use AnalyticsProvider.setECommerce(true, false);"),!1):!0:(this._log("warn","ecommerce not set. Use AnalyticsProvider.setECommerce(true, false);"),!1)},this._enhancedEcommerceEnabled=function(){return ecommerce?enhancedEcommerce?!0:(this._log("warn","Enhanced ecommerce plugin is disabled. Use AnalyticsProvider.setECommerce(true, true);"),!1):(this._log("warn","ecommerce not set. Use AnalyticsProvider.setECommerce(true, true);"),!1)},this._trackPage=function(url,title,custom){var that=this,args=arguments;url=url?url:getUrl(),title=title?title:$document[0].title,_gaJs(function(){$window._gaq.push(["_set","title",title]),$window._gaq.push(["_trackPageview",trackPrefix+url]),that._log("_trackPageview",url,title,args)}),_analyticsJs(function(){var opt_fieldObject={page:trackPrefix+url,title:title};angular.isObject(custom)&&angular.extend(opt_fieldObject,custom),angular.isArray(accountId)?accountId.forEach(function(trackerObj){$window.ga(_generateCommandName("send",trackerObj),"pageview",opt_fieldObject)}):$window.ga("send","pageview",opt_fieldObject),that._log("pageview",url,title,args)})},this._trackEvent=function(category,action,label,value,noninteraction,custom){var that=this,args=arguments;_gaJs(function(){$window._gaq.push(["_trackEvent",category,action,label,value,!!noninteraction]),that._log("trackEvent",args)}),_analyticsJs(function(){var opt_fieldObject={};angular.isDefined(noninteraction)&&(opt_fieldObject.nonInteraction=!!noninteraction),angular.isObject(custom)&&angular.extend(opt_fieldObject,custom),angular.isArray(accountId)?accountId.forEach(function(trackerObj){_checkOption("trackEvent",trackerObj)&&$window.ga(_generateCommandName("send",trackerObj),"event",category,action,label,value,opt_fieldObject)}):$window.ga("send","event",category,action,label,value,opt_fieldObject),that._log("event",args)})},this._addTrans=function(transactionId,affiliation,total,tax,shipping,city,state,country,currency){var that=this,args=arguments;_gaJs(function(){$window._gaq.push(["_addTrans",transactionId,affiliation,total,tax,shipping,city,state,country]),that._log("_addTrans",args)}),_analyticsJs(function(){that._ecommerceEnabled()&&($window.ga("ecommerce:addTransaction",{id:transactionId,affiliation:affiliation,revenue:total,tax:tax,shipping:shipping,currency:currency||"USD"}),that._log("ecommerce:addTransaction",args))})},this._addItem=function(transactionId,sku,name,category,price,quantity){var that=this,args=arguments;_gaJs(function(){$window._gaq.push(["_addItem",transactionId,sku,name,category,price,quantity]),that._log("_addItem",args)}),_analyticsJs(function(){that._ecommerceEnabled()&&($window.ga("ecommerce:addItem",{id:transactionId,name:name,sku:sku,category:category,price:price,quantity:quantity}),that._log("ecommerce:addItem",args))})},this._trackTrans=function(){var that=this,args=arguments;_gaJs(function(){$window._gaq.push(["_trackTrans"]),that._log("_trackTrans",args)}),_analyticsJs(function(){that._ecommerceEnabled()&&($window.ga("ecommerce:send"),that._log("ecommerce:send",args))})},this._clearTrans=function(){var that=this,args=arguments;_analyticsJs(function(){that._ecommerceEnabled()&&($window.ga("ecommerce:clear"),that._log("ecommerce:clear",args))})},this._addProduct=function(productId,name,category,brand,variant,price,quantity,coupon,position){var that=this,args=arguments;_gaJs(function(){$window._gaq.push(["_addProduct",productId,name,category,brand,variant,price,quantity,coupon,position]),that._log("_addProduct",args)}),_analyticsJs(function(){that._enhancedEcommerceEnabled()&&($window.ga("ec:addProduct",{id:productId,name:name,category:category,brand:brand,variant:variant,price:price,quantity:quantity,coupon:coupon,position:position}),that._log("ec:addProduct",args))})},this._addImpression=function(id,name,list,brand,category,variant,position,price){var that=this,args=arguments;_gaJs(function(){$window._gaq.push(["_addImpression",id,name,list,brand,category,variant,position,price]),that._log("_addImpression",args)}),_analyticsJs(function(){that._enhancedEcommerceEnabled()&&$window.ga("ec:addImpression",{id:id,name:name,category:category,brand:brand,variant:variant,list:list,position:position,price:price}),that._log("ec:addImpression",args)})},this._addPromo=function(productId,name,creative,position){var that=this,args=arguments;_gaJs(function(){$window._gaq.push(["_addPromo",productId,name,creative,position]),that._log("_addPromo",arguments)}),_analyticsJs(function(){that._enhancedEcommerceEnabled()&&($window.ga("ec:addPromo",{id:productId,name:name,creative:creative,position:position}),that._log("ec:addPromo",args))
})},this._getActionFieldObject=function(id,affiliation,revenue,tax,shipping,coupon,list,step,option){var obj={};return id&&(obj.id=id),affiliation&&(obj.affiliation=affiliation),revenue&&(obj.revenue=revenue),tax&&(obj.tax=tax),shipping&&(obj.shipping=shipping),coupon&&(obj.coupon=coupon),list&&(obj.list=list),step&&(obj.step=step),option&&(obj.option=option),obj},this._setAction=function(action,obj){var that=this,args=arguments;_gaJs(function(){$window._gaq.push(["_setAction",action,obj]),that._log("__setAction",args)}),_analyticsJs(function(){that._enhancedEcommerceEnabled()&&($window.ga("ec:setAction",action,obj),that._log("ec:setAction",args))})},this._trackTransaction=function(transactionId,affiliation,revenue,tax,shipping,coupon,list,step,option){this._setAction("purchase",this._getActionFieldObject(transactionId,affiliation,revenue,tax,shipping,coupon,list,step,option)),this._pageView()},this._trackRefund=function(transactionId){this._setAction("refund",this._getActionFieldObject(transactionId)),this._pageView()},this._trackCheckOut=function(step,option){this._setAction("checkout",this._getActionFieldObject(null,null,null,null,null,null,null,step,option)),this._pageView()},this._trackCart=function(action){-1!==["add","remove"].indexOf(action)&&(this._setAction(action),this._send("event","UX","click",action+" to cart"))},this._promoClick=function(promotionName){this._setAction("promo_click"),this._send("event","Internal Promotions","click",promotionName)},this._productClick=function(listName){this._setAction("click",this._getActionFieldObject(null,null,null,null,null,null,listName,null,null)),this._send("event","UX","click",listName)},this._send=function(){var that=this,args=Array.prototype.slice.call(arguments);args.unshift("send"),_analyticsJs(function(){$window.ga.apply(this,args),that._log(args)})},this._pageView=function(){this._send("pageview")},this._set=function(name,value){var that=this;_analyticsJs(function(){$window.ga("set",name,value),that._log("set",name,value)})},delayScriptTag||(analyticsJS?this._createAnalyticsScriptTag():this._createScriptTag()),trackRoutes&&$rootScope.$on(pageEvent,function(){me._trackPage()}),{_logs:me._logs,displayFeatures:displayFeatures,ecommerce:ecommerce,enhancedEcommerce:enhancedEcommerce,enhancedLinkAttribution:enhancedLinkAttribution,getUrl:getUrl,experimentId:experimentId,ignoreFirstPageLoad:ignoreFirstPageLoad,delayScriptTag:delayScriptTag,setCookieConfig:me._setCookieConfig,getCookieConfig:function(){return cookieConfig},createAnalyticsScriptTag:function(config){return config&&(cookieConfig=config),me._createAnalyticsScriptTag()},createScriptTag:function(config){return config&&(cookieConfig=config),me._createScriptTag()},ecommerceEnabled:function(){return me._ecommerceEnabled()},enhancedEcommerceEnabled:function(){return me._enhancedEcommerceEnabled()},trackPage:function(url,title,custom){me._trackPage(url,title,custom)},trackEvent:function(category,action,label,value,noninteraction,custom){me._trackEvent(category,action,label,value,noninteraction,custom)},addTrans:function(transactionId,affiliation,total,tax,shipping,city,state,country,currency){me._addTrans(transactionId,affiliation,total,tax,shipping,city,state,country,currency)},addItem:function(transactionId,sku,name,category,price,quantity){me._addItem(transactionId,sku,name,category,price,quantity)},trackTrans:function(){me._trackTrans()},clearTrans:function(){me._clearTrans()},addProduct:function(productId,name,category,brand,variant,price,quantity,coupon,position){me._addProduct(productId,name,category,brand,variant,price,quantity,coupon,position)},addPromo:function(productId,name,creative,position){me._addPromo(productId,name,creative,position)},addImpression:function(productId,name,list,brand,category,variant,position,price){me._addImpression(productId,name,list,brand,category,variant,position,price)},productClick:function(listName){me._productClick(listName)},promoClick:function(promotionName){me._promoClick(promotionName)},trackDetail:function(){me._setAction("detail"),me._pageView()},trackCart:function(action){me._trackCart(action)},trackCheckout:function(step,option){me._trackCheckOut(step,option)},trackTransaction:function(transactionId,affiliation,revenue,tax,shipping,coupon,list,step,option){me._trackTransaction(transactionId,affiliation,revenue,tax,shipping,coupon,list,step,option)},setAction:function(action,obj){me._setAction(action,obj)},send:function(obj){me._send(obj)},pageView:function(){me._pageView()},set:function(name,value){me._set(name,value)}}}]}).directive("gaTrackEvent",["Analytics","$parse",function(Analytics,$parse){return{restrict:"A",link:function(scope,element,attrs){var options=$parse(attrs.gaTrackEvent)({});element.bind("click",function(){options.length>1&&Analytics.trackEvent.apply(Analytics,options)})}}}]),angular.module("myApp",["angular-google-analytics"]).config(["AnalyticsProvider",function(AnalyticsProvider){AnalyticsProvider.setAccount("UA-********-2"),AnalyticsProvider.trackPages(!1),AnalyticsProvider.useDisplayFeatures(!0),AnalyticsProvider.useAnalytics(!0),AnalyticsProvider.ignoreFirstPageLoad(!0),AnalyticsProvider.useECommerce(!0,!0)}]),angular.module("myApp.controllers",[]),angular.module("myApp.directives",[]),angular.module("myApp.filters",[]),angular.module("myApp.services",[]),angular.module("myApp").service("config",["$rootScope",config]),angular.module("myApp").controller("MainCtrl",["$scope","$location","auth","stock2shop","Analytics",MainCtrl]),angular.module("myApp").directive("ngBindHtmlUnsafe",[function(){return function(scope,element,attr){element.addClass("ng-binding").data("$binding",attr.ngBindHtmlUnsafe),scope.$watch(attr.ngBindHtmlUnsafe,function(value){element.html(value||"")})}}]),angular.module("myApp").directive("focusInvalid",function(){return{link:function(scope,elem){elem.on("submit",function(){var firstInvalid=angular.element(elem[0].querySelector(".ng-invalid"));firstInvalid&&(firstInvalid.removeClass("ng-pristine"),firstInvalid.addClass("ng-dirty"),firstInvalid.$dirty=!0,firstInvalid[0]&&firstInvalid[0].focus())})}}}),angular.module("myApp").directive("loading",["$http",function($http){return{restrict:"A",link:function(scope,elm,attrs){var option=attrs.loading,isLoading=function(){return $http.pendingRequests.length>0};scope.$watch(isLoading,function(loading){loading?"show"==option?elm.show():elm.hide():"show"==option?elm.hide():elm.show()})}}}]),angular.module("myApp.filters",[]).filter("boolean",function(){return function(input){return input?"yes":"no"}}),angular.module("myApp").service("auth",["config","$location","stock2shop",auth]),angular.module("myApp").service("cart",["$filter","utils","storeCache","price","Analytics",cart]),angular.module("myApp").service("price",["storeCache","utils",price]),angular.module("myApp").service("storeCache",["$q","stock2shop","config",storeCache]),angular.module("myApp").service("utils",["$location",utils]),angular.module("myApp").service("validation",validation),angular.module("myApp").service("version",[version]);