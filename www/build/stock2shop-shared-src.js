/*!
 * j<PERSON><PERSON><PERSON>lugin v1.3.1
 * https://github.com/carhartl/jquery-cookie
 *
 * Copyright 2013 <PERSON>
 * Released under the MIT license
 */
(function ($, document, undefined) {

	var pluses = /\+/g;

	function raw(s) {
		return s;
	}

	function decoded(s) {
		return unRfc2068(decodeURIComponent(s.replace(pluses, ' ')));
	}

	function unRfc2068(value) {
		if (value.indexOf('"') === 0) {
			// This is a quoted cookie as according to RFC2068, unescape
			value = value.slice(1, -1).replace(/\\"/g, '"').replace(/\\\\/g, '\\');
		}
		return value;
	}

	function fromJSON(value) {
		return config.json ? JSON.parse(value) : value;
	}

	var config = $.cookie = function (key, value, options) {

		// write
		if (value !== undefined) {
			options = $.extend({}, config.defaults, options);

			if (value === null) {
				options.expires = -1;
			}

			if (typeof options.expires === 'number') {
				var days = options.expires, t = options.expires = new Date();
				t.setDate(t.getDate() + days);
			}

			value = config.json ? JSON.stringify(value) : String(value);

			return (document.cookie = [
				encodeURIComponent(key), '=', config.raw ? value : encodeURIComponent(value),
				options.expires ? '; expires=' + options.expires.toUTCString() : '', // use expires attribute, max-age is not supported by IE
				options.path    ? '; path=' + options.path : '',
				options.domain  ? '; domain=' + options.domain : '',
				options.secure  ? '; secure' : ''
			].join(''));
		}

		// read
		var decode = config.raw ? raw : decoded;
		var cookies = document.cookie.split('; ');
		var result = key ? null : {};
		for (var i = 0, l = cookies.length; i < l; i++) {
			var parts = cookies[i].split('=');
			var name = decode(parts.shift());
			var cookie = decode(parts.join('='));

			if (key && key === name) {
				result = fromJSON(cookie);
				break;
			}

			if (!key) {
				result[name] = fromJSON(cookie);
			}
		}

		return result;
	};

	config.defaults = {};

	$.removeCookie = function (key, options) {
		if ($.cookie(key) !== null) {
			$.cookie(key, null, options);
			return true;
		}
		return false;
	};

})(jQuery, document);
;/*
 angular-file-upload v2.3.4
 https://github.com/nervgh/angular-file-upload
*/

!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports["angular-file-upload"]=t():e["angular-file-upload"]=t()}(this,function(){return function(e){function t(n){if(o[n])return o[n].exports;var r=o[n]={exports:{},id:n,loaded:!1};return e[n].call(r.exports,r,r.exports,t),r.loaded=!0,r.exports}var o={};return t.m=e,t.c=o,t.p="",t(0)}([function(e,t,o){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}var r=o(1),i=n(r),s=o(2),a=n(s),l=o(3),u=n(l),p=o(4),c=n(p),f=o(5),d=n(f),h=o(6),y=n(h),m=o(7),_=n(m),v=o(8),g=n(v),F=o(9),b=n(F),O=o(10),C=n(O),I=o(11),U=n(I),w=o(12),A=n(w);angular.module(i["default"].name,[]).value("fileUploaderOptions",a["default"]).factory("FileUploader",u["default"]).factory("FileLikeObject",c["default"]).factory("FileItem",d["default"]).factory("FileDirective",y["default"]).factory("FileSelect",_["default"]).factory("FileDrop",g["default"]).factory("FileOver",b["default"]).directive("nvFileSelect",C["default"]).directive("nvFileDrop",U["default"]).directive("nvFileOver",A["default"]).run(["FileUploader","FileLikeObject","FileItem","FileDirective","FileSelect","FileDrop","FileOver",function(e,t,o,n,r,i,s){e.FileLikeObject=t,e.FileItem=o,e.FileDirective=n,e.FileSelect=r,e.FileDrop=i,e.FileOver=s}])},function(e,t){e.exports={name:"angularFileUpload"}},function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t["default"]={url:"/",alias:"file",headers:{},queue:[],progress:0,autoUpload:!1,removeAfterUpload:!1,method:"POST",filters:[],formData:[],queueLimit:Number.MAX_VALUE,withCredentials:!1,disableMultipart:!1}},function(e,t,o){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t,o,n,i,s,a){var m=n.File,_=n.FormData,v=function(){function n(t){r(this,n);var o=l(e);u(this,o,t,{isUploading:!1,_nextIndex:0,_failFilterIndex:-1,_directives:{select:[],drop:[],over:[]}}),this.filters.unshift({name:"queueLimit",fn:this._queueLimitFilter}),this.filters.unshift({name:"folder",fn:this._folderFilter})}return n.prototype.addToQueue=function(e,t,o){var n=this,r=this.isArrayLikeObject(e)?e:[e],i=this._getFilters(o),l=this.queue.length,u=[];p(r,function(e){var o=new s(e);if(n._isValidFile(o,i,t)){var r=new a(n,e,t);u.push(r),n.queue.push(r),n._onAfterAddingFile(r)}else{var l=i[n._failFilterIndex];n._onWhenAddingFileFailed(o,l,t)}}),this.queue.length!==l&&(this._onAfterAddingAll(u),this.progress=this._getTotalProgress()),this._render(),this.autoUpload&&this.uploadAll()},n.prototype.removeFromQueue=function(e){var t=this.getIndexOfItem(e),o=this.queue[t];o.isUploading&&o.cancel(),this.queue.splice(t,1),o._destroy(),this.progress=this._getTotalProgress()},n.prototype.clearQueue=function(){for(;this.queue.length;)this.queue[0].remove();this.progress=0},n.prototype.uploadItem=function(e){var t=this.getIndexOfItem(e),o=this.queue[t],n=this.isHTML5?"_xhrTransport":"_iframeTransport";o._prepareToUploading(),this.isUploading||(this._onBeforeUploadItem(o),o.isCancel||(o.isUploading=!0,this.isUploading=!0,this[n](o),this._render()))},n.prototype.cancelItem=function(e){var t=this,o=this.getIndexOfItem(e),n=this.queue[o],r=this.isHTML5?"_xhr":"_form";n&&(n.isCancel=!0,n.isUploading?n[r].abort():!function(){var e=[void 0,0,{}],o=function(){t._onCancelItem.apply(t,[n].concat(e)),t._onCompleteItem.apply(t,[n].concat(e))};i(o)}())},n.prototype.uploadAll=function(){var e=this.getNotUploadedItems().filter(function(e){return!e.isUploading});e.length&&(p(e,function(e){return e._prepareToUploading()}),e[0].upload())},n.prototype.cancelAll=function(){var e=this.getNotUploadedItems();p(e,function(e){return e.cancel()})},n.prototype.isFile=function(e){return this.constructor.isFile(e)},n.prototype.isFileLikeObject=function(e){return this.constructor.isFileLikeObject(e)},n.prototype.isArrayLikeObject=function(e){return this.constructor.isArrayLikeObject(e)},n.prototype.getIndexOfItem=function(e){return f(e)?e:this.queue.indexOf(e)},n.prototype.getNotUploadedItems=function(){return this.queue.filter(function(e){return!e.isUploaded})},n.prototype.getReadyItems=function(){return this.queue.filter(function(e){return e.isReady&&!e.isUploading}).sort(function(e,t){return e.index-t.index})},n.prototype.destroy=function(){var e=this;p(this._directives,function(t){p(e._directives[t],function(e){e.destroy()})})},n.prototype.onAfterAddingAll=function(e){},n.prototype.onAfterAddingFile=function(e){},n.prototype.onWhenAddingFileFailed=function(e,t,o){},n.prototype.onBeforeUploadItem=function(e){},n.prototype.onProgressItem=function(e,t){},n.prototype.onProgressAll=function(e){},n.prototype.onSuccessItem=function(e,t,o,n){},n.prototype.onErrorItem=function(e,t,o,n){},n.prototype.onCancelItem=function(e,t,o,n){},n.prototype.onCompleteItem=function(e,t,o,n){},n.prototype.onCompleteAll=function(){},n.prototype._getTotalProgress=function(e){if(this.removeAfterUpload)return e||0;var t=this.getNotUploadedItems().length,o=t?this.queue.length-t:this.queue.length,n=100/this.queue.length,r=(e||0)*n/100;return Math.round(o*n+r)},n.prototype._getFilters=function(e){if(!e)return this.filters;if(h(e))return e;var t=e.match(/[^\s,]+/g);return this.filters.filter(function(e){return-1!==t.indexOf(e.name)})},n.prototype._render=function(){t.$$phase||t.$apply()},n.prototype._folderFilter=function(e){return!(!e.size&&!e.type)},n.prototype._queueLimitFilter=function(){return this.queue.length<this.queueLimit},n.prototype._isValidFile=function(e,t,o){var n=this;return this._failFilterIndex=-1,t.length?t.every(function(t){return n._failFilterIndex++,t.fn.call(n,e,o)}):!0},n.prototype._isSuccessCode=function(e){return e>=200&&300>e||304===e},n.prototype._transformResponse=function(e,t){var n=this._headersGetter(t);return p(o.defaults.transformResponse,function(t){e=t(e,n)}),e},n.prototype._parseHeaders=function(e){var t,o,n,r={};return e?(p(e.split("\n"),function(e){n=e.indexOf(":"),t=e.slice(0,n).trim().toLowerCase(),o=e.slice(n+1).trim(),t&&(r[t]=r[t]?r[t]+", "+o:o)}),r):r},n.prototype._headersGetter=function(e){return function(t){return t?e[t.toLowerCase()]||null:e}},n.prototype._xhrTransport=function(e){var t,o=this,n=e._xhr=new XMLHttpRequest;if(e.disableMultipart?t=e._file:(t=new _,p(e.formData,function(e){p(e,function(e,o){t.append(o,e)})}),t.append(e.alias,e._file,e.file.name)),"number"!=typeof e._file.size)throw new TypeError("The file specified is no longer valid");n.upload.onprogress=function(t){var n=Math.round(t.lengthComputable?100*t.loaded/t.total:0);o._onProgressItem(e,n)},n.onload=function(){var t=o._parseHeaders(n.getAllResponseHeaders()),r=o._transformResponse(n.response,t),i=o._isSuccessCode(n.status)?"Success":"Error",s="_on"+i+"Item";o[s](e,r,n.status,t),o._onCompleteItem(e,r,n.status,t)},n.onerror=function(){var t=o._parseHeaders(n.getAllResponseHeaders()),r=o._transformResponse(n.response,t);o._onErrorItem(e,r,n.status,t),o._onCompleteItem(e,r,n.status,t)},n.onabort=function(){var t=o._parseHeaders(n.getAllResponseHeaders()),r=o._transformResponse(n.response,t);o._onCancelItem(e,r,n.status,t),o._onCompleteItem(e,r,n.status,t)},n.open(e.method,e.url,!0),n.withCredentials=e.withCredentials,p(e.headers,function(e,t){n.setRequestHeader(t,e)}),n.send(t)},n.prototype._iframeTransport=function(e){var t=this,o=y('<form style="display: none;" />'),n=y('<iframe name="iframeTransport'+Date.now()+'">'),r=e._input;e._form&&e._form.replaceWith(r),e._form=o,r.prop("name",e.alias),p(e.formData,function(e){p(e,function(e,t){var n=y('<input type="hidden" name="'+t+'" />');n.val(e),o.append(n)})}),o.prop({action:e.url,method:"POST",target:n.prop("name"),enctype:"multipart/form-data",encoding:"multipart/form-data"}),n.bind("load",function(){var o="",r=200;try{o=n[0].contentDocument.body.innerHTML}catch(i){r=500}var s={response:o,status:r,dummy:!0},a={},l=t._transformResponse(s.response,a);t._onSuccessItem(e,l,s.status,a),t._onCompleteItem(e,l,s.status,a)}),o.abort=function(){var i,s={status:0,dummy:!0},a={};n.unbind("load").prop("src","javascript:false;"),o.replaceWith(r),t._onCancelItem(e,i,s.status,a),t._onCompleteItem(e,i,s.status,a)},r.after(o),o.append(r).append(n),o[0].submit()},n.prototype._onWhenAddingFileFailed=function(e,t,o){this.onWhenAddingFileFailed(e,t,o)},n.prototype._onAfterAddingFile=function(e){this.onAfterAddingFile(e)},n.prototype._onAfterAddingAll=function(e){this.onAfterAddingAll(e)},n.prototype._onBeforeUploadItem=function(e){e._onBeforeUpload(),this.onBeforeUploadItem(e)},n.prototype._onProgressItem=function(e,t){var o=this._getTotalProgress(t);this.progress=o,e._onProgress(t),this.onProgressItem(e,t),this.onProgressAll(o),this._render()},n.prototype._onSuccessItem=function(e,t,o,n){e._onSuccess(t,o,n),this.onSuccessItem(e,t,o,n)},n.prototype._onErrorItem=function(e,t,o,n){e._onError(t,o,n),this.onErrorItem(e,t,o,n)},n.prototype._onCancelItem=function(e,t,o,n){e._onCancel(t,o,n),this.onCancelItem(e,t,o,n)},n.prototype._onCompleteItem=function(e,t,o,n){e._onComplete(t,o,n),this.onCompleteItem(e,t,o,n);var r=this.getReadyItems()[0];return this.isUploading=!1,d(r)?void r.upload():(this.onCompleteAll(),this.progress=this._getTotalProgress(),void this._render())},n.isFile=function(e){return m&&e instanceof m},n.isFileLikeObject=function(e){return e instanceof s},n.isArrayLikeObject=function(e){return c(e)&&"length"in e},n.inherit=function(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.super_=t},n}();return v.prototype.isHTML5=!(!m||!_),v.isHTML5=v.prototype.isHTML5,v}Object.defineProperty(t,"__esModule",{value:!0}),t["default"]=i;var s=o(1),a=(n(s),angular),l=a.copy,u=a.extend,p=a.forEach,c=a.isObject,f=a.isNumber,d=a.isDefined,h=a.isArray,y=a.element;i.$inject=["fileUploaderOptions","$rootScope","$http","$window","$timeout","FileLikeObject","FileItem"]},function(e,t,o){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(){return function(){function e(t){r(this,e);var o=u(t),n=o?t.value:t,i=p(n)?"FakePath":"Object",s="_createFrom"+i;this[s](n)}return e.prototype._createFromFakePath=function(e){this.lastModifiedDate=null,this.size=null,this.type="like/"+e.slice(e.lastIndexOf(".")+1).toLowerCase(),this.name=e.slice(e.lastIndexOf("/")+e.lastIndexOf("\\")+2)},e.prototype._createFromObject=function(e){this.lastModifiedDate=l(e.lastModifiedDate),this.size=e.size,this.type=e.type,this.name=e.name},e}()}Object.defineProperty(t,"__esModule",{value:!0}),t["default"]=i;var s=o(1),a=(n(s),angular),l=a.copy,u=a.isElement,p=a.isString},function(e,t,o){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){return function(){function o(e,n,i){r(this,o);var s=c(n),a=s?p(n):null,f=s?null:n;u(this,{url:e.url,alias:e.alias,headers:l(e.headers),formData:l(e.formData),removeAfterUpload:e.removeAfterUpload,withCredentials:e.withCredentials,disableMultipart:e.disableMultipart,method:e.method},i,{uploader:e,file:new t(n),isReady:!1,isUploading:!1,isUploaded:!1,isSuccess:!1,isCancel:!1,isError:!1,progress:0,index:null,_file:f,_input:a}),a&&this._replaceNode(a)}return o.prototype.upload=function(){try{this.uploader.uploadItem(this)}catch(e){this.uploader._onCompleteItem(this,"",0,[]),this.uploader._onErrorItem(this,"",0,[])}},o.prototype.cancel=function(){this.uploader.cancelItem(this)},o.prototype.remove=function(){this.uploader.removeFromQueue(this)},o.prototype.onBeforeUpload=function(){},o.prototype.onProgress=function(e){},o.prototype.onSuccess=function(e,t,o){},o.prototype.onError=function(e,t,o){},o.prototype.onCancel=function(e,t,o){},o.prototype.onComplete=function(e,t,o){},o.prototype._onBeforeUpload=function(){this.isReady=!0,this.isUploading=!1,this.isUploaded=!1,this.isSuccess=!1,this.isCancel=!1,this.isError=!1,this.progress=0,this.onBeforeUpload()},o.prototype._onProgress=function(e){this.progress=e,this.onProgress(e)},o.prototype._onSuccess=function(e,t,o){this.isReady=!1,this.isUploading=!1,this.isUploaded=!0,this.isSuccess=!0,this.isCancel=!1,this.isError=!1,this.progress=100,this.index=null,this.onSuccess(e,t,o)},o.prototype._onError=function(e,t,o){this.isReady=!1,this.isUploading=!1,this.isUploaded=!0,this.isSuccess=!1,this.isCancel=!1,this.isError=!0,this.progress=0,this.index=null,this.onError(e,t,o)},o.prototype._onCancel=function(e,t,o){this.isReady=!1,this.isUploading=!1,this.isUploaded=!1,this.isSuccess=!1,this.isCancel=!0,this.isError=!1,this.progress=0,this.index=null,this.onCancel(e,t,o)},o.prototype._onComplete=function(e,t,o){this.onComplete(e,t,o),this.removeAfterUpload&&this.remove()},o.prototype._destroy=function(){this._input&&this._input.remove(),this._form&&this._form.remove(),delete this._form,delete this._input},o.prototype._prepareToUploading=function(){this.index=this.index||++this.uploader._nextIndex,this.isReady=!0},o.prototype._replaceNode=function(t){var o=e(t.clone())(t.scope());o.prop("value",null),t.css("display","none"),t.after(o)},o}()}Object.defineProperty(t,"__esModule",{value:!0}),t["default"]=i;var s=o(1),a=(n(s),angular),l=a.copy,u=a.extend,p=a.element,c=a.isElement;i.$inject=["$compile","FileLikeObject"]},function(e,t,o){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(){var e=function(){function e(t){r(this,e),l(this,t),this.uploader._directives[this.prop].push(this),this._saveLinks(),this.bind()}return e.prototype.bind=function(){for(var e in this.events){var t=this.events[e];this.element.bind(e,this[t])}},e.prototype.unbind=function(){for(var e in this.events)this.element.unbind(e,this.events[e])},e.prototype.destroy=function(){var e=this.uploader._directives[this.prop].indexOf(this);this.uploader._directives[this.prop].splice(e,1),this.unbind()},e.prototype._saveLinks=function(){for(var e in this.events){var t=this.events[e];this[t]=this[t].bind(this)}},e}();return e.prototype.events={},e}Object.defineProperty(t,"__esModule",{value:!0}),t["default"]=i;var s=o(1),a=(n(s),angular),l=a.extend},function(e,t,o){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function s(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}function a(e,t){return function(t){function o(e){r(this,o);var n=p(e,{events:{$destroy:"destroy",change:"onChange"},prop:"select"}),s=i(this,t.call(this,n));return s.uploader.isHTML5||s.element.removeAttr("multiple"),s.element.prop("value",null),s}return s(o,t),o.prototype.getOptions=function(){},o.prototype.getFilters=function(){},o.prototype.isEmptyAfterSelection=function(){return!!this.element.attr("multiple")},o.prototype.onChange=function(){var t=this.uploader.isHTML5?this.element[0].files:this.element[0],o=this.getOptions(),n=this.getFilters();this.uploader.isHTML5||this.destroy(),this.uploader.addToQueue(t,o,n),this.isEmptyAfterSelection()&&(this.element.prop("value",null),this.element.replaceWith(e(this.element.clone())(this.scope)))},o}(t)}Object.defineProperty(t,"__esModule",{value:!0}),t["default"]=a;var l=o(1),u=(n(l),angular),p=u.extend;a.$inject=["$compile","FileDirective"]},function(e,t,o){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function s(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}function a(e){return function(e){function t(o){r(this,t);var n=p(o,{events:{$destroy:"destroy",drop:"onDrop",dragover:"onDragOver",dragleave:"onDragLeave"},prop:"drop"});return i(this,e.call(this,n))}return s(t,e),t.prototype.getOptions=function(){},t.prototype.getFilters=function(){},t.prototype.onDrop=function(e){var t=this._getTransfer(e);if(t){var o=this.getOptions(),n=this.getFilters();this._preventAndStop(e),c(this.uploader._directives.over,this._removeOverClass,this),this.uploader.addToQueue(t.files,o,n)}},t.prototype.onDragOver=function(e){var t=this._getTransfer(e);this._haveFiles(t.types)&&(t.dropEffect="copy",this._preventAndStop(e),c(this.uploader._directives.over,this._addOverClass,this))},t.prototype.onDragLeave=function(e){e.currentTarget!==this.element[0]&&(this._preventAndStop(e),c(this.uploader._directives.over,this._removeOverClass,this))},t.prototype._getTransfer=function(e){return e.dataTransfer?e.dataTransfer:e.originalEvent.dataTransfer},t.prototype._preventAndStop=function(e){e.preventDefault(),e.stopPropagation()},t.prototype._haveFiles=function(e){return e?e.indexOf?-1!==e.indexOf("Files"):e.contains?e.contains("Files"):!1:!1},t.prototype._addOverClass=function(e){e.addOverClass()},t.prototype._removeOverClass=function(e){e.removeOverClass()},t}(e)}Object.defineProperty(t,"__esModule",{value:!0}),t["default"]=a;var l=o(1),u=(n(l),angular),p=u.extend,c=u.forEach;a.$inject=["FileDirective"]},function(e,t,o){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function s(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}function a(e){return function(e){function t(o){r(this,t);var n=p(o,{events:{$destroy:"destroy"},prop:"over",overClass:"nv-file-over"});return i(this,e.call(this,n))}return s(t,e),t.prototype.addOverClass=function(){this.element.addClass(this.getOverClass())},t.prototype.removeOverClass=function(){this.element.removeClass(this.getOverClass())},t.prototype.getOverClass=function(){return this.overClass},t}(e)}Object.defineProperty(t,"__esModule",{value:!0}),t["default"]=a;var l=o(1),u=(n(l),angular),p=u.extend;a.$inject=["FileDirective"]},function(e,t,o){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function r(e,t,o){return{link:function(n,r,i){var s=n.$eval(i.uploader);if(!(s instanceof t))throw new TypeError('"Uploader" must be an instance of FileUploader');var a=new o({uploader:s,element:r,scope:n});a.getOptions=e(i.options).bind(a,n),a.getFilters=function(){return i.filters}}}}Object.defineProperty(t,"__esModule",{value:!0}),t["default"]=r;var i=o(1);n(i);r.$inject=["$parse","FileUploader","FileSelect"]},function(e,t,o){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function r(e,t,o){return{link:function(n,r,i){var s=n.$eval(i.uploader);if(!(s instanceof t))throw new TypeError('"Uploader" must be an instance of FileUploader');if(s.isHTML5){var a=new o({uploader:s,element:r});a.getOptions=e(i.options).bind(a,n),a.getFilters=function(){return i.filters}}}}}Object.defineProperty(t,"__esModule",{value:!0}),t["default"]=r;var i=o(1);n(i);r.$inject=["$parse","FileUploader","FileDrop"]},function(e,t,o){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function r(e,t){return{link:function(o,n,r){var i=o.$eval(r.uploader);if(!(i instanceof e))throw new TypeError('"Uploader" must be an instance of FileUploader');var s=new t({uploader:i,element:n});s.getOverClass=function(){return r.overClass||s.overClass}}}}Object.defineProperty(t,"__esModule",{value:!0}),t["default"]=r;var i=o(1);n(i);r.$inject=["FileUploader","FileOver"]}])});
//# sourceMappingURL=angular-file-upload.min.js.map;'use strict';

angular.module('myApp').service("config", [
    function() {
        var domain = location.protocol + '//' + location.hostname + (location.port ? ':' + location.port : '');
        var domainPath = domain + location.pathname;
        // Strip trailing slash.
        if (domainPath.substr(-1) == '/') {
            domainPath = domainPath.substr(0, domainPath.length - 1);
        }

        var appHostname = location.hostname;
        if (window.config.production) {
            appHostname = appHostname.replace("b2b.", "app.");
        } else {
            appHostname = appHostname.replace("b2b.", "");
        }
        var maintenanceDomain = location.protocol + '//' +
            appHostname + (location.port ? ':' + location.port : '');

        // We might want to set additional config on the php landing pages.
        var config = window.config || {};

        // DEPRECATED As of 2017-05-05 CORS is enabled on stock2shop API
        // // Domain is what the site is currently running off,
        // // if baseUrl does not start with domain we have to use the proxy.
        // // Note that baseUrl is used on the index page to load the api client.
        // if (config.baseUrl.indexOf(domain) !== 0) {
        //     console.info("baseUrl does not start with domain, using proxy");
        //     config.baseUrl = domain + "/v1";
        // }

        // Use CORS baseUrl as set by v1/api-docs/codegen

        var pagination = {
            // Don't change limit inside the controllers!
            // The new limit will then be used until the page is reloaded.
            limit: 50,
            // Offset and page is not the same thing.
            // Offset starts at 0 and page starts at 1: offset = (page - 1) * limit
            // Don't use this value for initialization, it is changed by the controllers.
            page: 1,

            // TODO Don't set offset here, see comment for page above and stock2shop console.
            // TODO Refactor controllers as per stock2shop console.
            offset: 1,

            // These properties are set by the controller or angular ui,
            // we list them here so they are documented.
            total: undefined,
            display: undefined
        };

        config =  angular.extend(config, {
            credentials: {},

            // Bug in angular pagination on hash change, we have to assign pagination as a reference.
            // Unfortunately this means we can change the pagination defaults inside the controllers,
            // if we did we would change the default for all controllers.
            pagination: pagination,

            // Get debugging info from the swagger client
            debug: false,

            domain: domainPath,
            maintenanceDomain: maintenanceDomain
        });
        return config;
    }
]);


;// ...........................................................................
// Namespace for attaching utils.
window.gm = window.gm || {};
/**
 * This is a helper method to play with angular in the browser console,
 * for example: nb = window.nb.getService("nb")
 * @param serviceName
 * @returns {*}
 */
window.gm.getService = function(serviceName) {
    return angular.element(document).injector().get(serviceName);
};

/**
 * Get query parameter by name
 * http://stackoverflow.com/a/901144/639133
 * @param name
 * @returns {string}
 */
window.gm.getParameterByName = function(name) {
    name = name.replace(/[\[]/, "\\[").replace(/[\]]/, "\\]");
    var regex = new RegExp("[\\?&]" + name + "=([^&#]*)"),
        results = regex.exec(location.search);
    return results === null ? "" : decodeURIComponent(results[1].replace(/\+/g, " "));
};

/**
 * We have to create this function here to be able to use it
 * outside the context of the angular application.
 * @param url
 * @returns {*}
 */
window.gm.noCacheURL = function(url) {
    if (window.config.production) {
        return url;
    } else {
        // The noCacheTimestamp is set on the index page,
        // since we need to use it for custom partials as well.
        return url + "?ts=" + window.config["noCacheTimestamp"];
    }
};
;'use strict';

function AccountCtrl($scope, utils, storeCache, stock2shop, $q) {
    $scope.pageReady = false;
    $scope.cache = storeCache.get();
    $scope.currency = utils.currency;
    $scope.account_statement = utils.toBool($scope.cache.settings.account_statement);
    $scope.account_invoice = utils.toBool($scope.cache.settings.account_invoice);
    var meta = $scope.cache.customer.meta;
    var account_display = false;
    if ($scope.cache.settings.account_display) {
        account_display = JSON.parse($scope.cache.settings.account_display);
        $scope.cache.account_display = {};
        angular.forEach(account_display, function(value, key) {
            if(value.indexOf("meta_") === 0) {
                value = value.replace("meta_", "");
                if(meta[value]) {
                    $scope.cache.account_display[key] = meta[value];
                } else {
                    $scope.cache.account_display[key] = "n/a";
                }
            } else {
                if($scope.cache.settings[value]) {
                    $scope.cache.account_display[key] = $scope.cache.settings[value];
                } else {
                    $scope.cache.account_display[key] = value;
                }
            }
        });
    }
    var searchOptions = {
        active: 1,
        offset: 0,
        limit: 5,
        channel_id: $scope.cache.customer.channel_id,
        customer_id: $scope.cache.customer.id
    };
    $q.all([
        stock2shop.orders.searchOrders(searchOptions)
    ]).then(function (data) {
        $scope.cache.orders = data[0];
        $scope.pageReady = true;
    });

    $scope.showAddress = function() {
        return (
            $scope.cache.customer.addresses[0] &&
            $scope.cache.customer.addresses[0].address1 !== ""
        );
    };

    $scope.downloadStatementURL = function() {
        var url = config.baseUrl;
        url += "/source_customers/download/statement/" + $scope.cache.customer.id;
        url += "?source_id=" + $scope.cache.customer.sources[0].source_id;
        url += "&customer_id=" + $scope.cache.customer.id;
        url += "&token=" + config.credentials.token;
        return url;
    };

}

angular.module('myApp')
    .controller('AccountCtrl', ['$scope', 'utils', 'storeCache', 'stock2shop', '$q', AccountCtrl]);;'use strict';

function BulkOrderCtrl($scope, $location, $modal, config, stock2shop, FileUploader, url, price, storeCache) {
    if ($scope.allow_bulk_order === false) {
        $location.path('/');
    }

    var settings = storeCache.get().settings;
    var upload_url = config.baseUrl + "/orders/import/upload";
    var upload_url_params = "?format=json&token=" + config.credentials.token + "&channel_id=" + config.credentials.channels[0].id;

    // Tax settings based of store settings
    // TODO this should be based off shipping location
    var tax_rate = price.getTaxRate(false, false); // Tax set by store
    var tax_code = (tax_rate > 0)? 'taxed': 'exempt';
    var tax_description = settings.tax_description || "TAX";

    $scope.checkoutFields = angular.fromJson(settings.checkout_fields);
    $scope.checkoutFieldsMap = {};

    $scope.data = {
        file_size_limit: 5, // 5mb
        error_message: null,
        results: {},
        channel_id: config.credentials.channels[0].id,
        step: 1,
        select_all: false,
        map: {
            system_order: {
                channel_order_code: null,
                shipping_address: {
                    company: null,
                    first_name: null,
                    last_name: null,
                    address1: null,
                    address2: null,
                    city: null,
                    country: null,
                    country_code: null,
                    phone: null,
                    province: null,
                    province_code: null,
                    zip: null
                },
                line_items: [
                    {
                        sku: null,
                        qty: null,
                        tax_lines: [
                            {
                                "rate": tax_rate,
                                "code": tax_code,
                                "title": tax_description
                            }
                        ]
                    }
                ],
                notes: null
            },
            params: {}
        },
        channel_order_codes: {}
    };

    // Uploader
    var uploader = $scope.uploader = new FileUploader({
        url: upload_url + upload_url_params
    });


    // FILTERS
    uploader.filters.push({
        name: 'csvFilter',
        fn: function(item /*{File|FileLikeObject}*/, options) {
            var extension = item.name.slice(-4).toLowerCase();
            var valid = extension === '.csv';
            if(!valid) {
                $scope.data.error_message = "File must be csv";
                return false;
            }
            return true;
        }
    });
    uploader.filters.push({
        name: 'sizeFilter',
        fn: function(item /*{File|FileLikeObject}*/, options) {
            var size = item.size/1024/1024;
            if(size > $scope.data.file_size_limit) {
                $scope.data.error_message = "File to big, max image size 5mb";
                return false;
            }
            return true;
        }
    });

    // CALLBACKS
    uploader.onWhenAddingFileFailed = function(item /*{File|FileLikeObject}*/, filter, options) {
        // console.info('onWhenAddingFileFailed', item, filter, options);
    };
    uploader.onAfterAddingFile = function(fileItem) {
        $scope.data.error_message = null;
        // console.info('onAfterAddingFile', fileItem);
    };
    uploader.onAfterAddingAll = function(addedFileItems) {
        // console.info('onAfterAddingAll', addedFileItems);
    };
    uploader.onBeforeUploadItem = function(item) {
        // console.info('onBeforeUploadItem', item);
    };
    uploader.onProgressItem = function(fileItem, progress) {
        // console.info('onProgressItem', fileItem, progress);
    };
    uploader.onProgressAll = function(progress) {
        // console.info('onProgressAll', progress);
    };
    uploader.onSuccessItem = function(fileItem, response, status, headers) {
        // console.info('onSuccessItem', fileItem, response, status, headers);
    };
    uploader.onErrorItem = function(fileItem, response, status, headers) {
        // console.info('onErrorItem', fileItem, response, status, headers);
    };
    uploader.onCancelItem = function(fileItem, response, status, headers) {
        // console.info('onCancelItem', fileItem, response, status, headers);
    };
    uploader.onCompleteItem = function(fileItem, response, status, headers) {
        // console.info('onCompleteItem', fileItem, response, status, headers);
        $scope.data.results.system_order_upload = response.system_order_upload;
        $scope.data.step = 2;
        $scope.setGivenOptions();
    };
    uploader.onCompleteAll = function() {
        // console.info('onCompleteAll');
    };
    // console.info('uploader', uploader);

    /**
     * Set the option values that have matching column names in imported CSV file
     */
    $scope.setGivenOptions = function() {
        var columns = $scope.data.results.system_order_upload.columns;

        // Set required fields
        var index = columns.indexOf('sku');
        if (index > -1) {
            $scope.data.map.system_order.line_items[0].sku = columns[index];
        }

        index = columns.indexOf('qty');
        if (index > -1) {
            $scope.data.map.system_order.line_items[0].qty = columns[index];
        }

        index = columns.indexOf('order_number');
        if (index > -1) {
            $scope.data.map.system_order.channel_order_code = columns[index];
        }

        // Set checkout fields
        angular.forEach($scope.checkoutFields, function(value, key) {
            if (columns.indexOf(key.replace('params.', '')) > -1) {
                $scope.checkoutFieldsMap[key] = key.replace('params.', '');
                $scope.setCheckoutField(key);
            }
        });

        // Set shipping address fields
        angular.forEach($scope.data.map.system_order.shipping_address, function(value, key) {
            if (columns.indexOf(key) > -1) {
                $scope.data.map.system_order.shipping_address[key] = key;
            }
        });
    }

    /**
     * All required map fields must be added to process orders
     * @returns {boolean}
     */
    $scope.canProcess = function() {
        return (
            (typeof $scope.data.map.system_order.channel_order_code === "string") &&
            (typeof $scope.data.map.system_order.line_items[0].sku === "string") &&
            (typeof $scope.data.map.system_order.line_items[0].qty === "string")

        );
    };

    /**
     * Add checkout fields to order map
     * @param selected_field
     */
    $scope.setCheckoutField = function(selected_field) {
        angular.forEach($scope.checkoutFieldsMap, function(map_value, map_key) {
            if (map_key === selected_field) {
                if (selected_field.indexOf('params.') > -1) {

                    // Add params to map
                    var selected_field_key = selected_field.replace('params.', '');
                    $scope.data.map.params[selected_field_key] = "{{" + map_value + "}}";
                } else {

                    // We assume that a field with no "params." prefix is a system order field
                    if ($scope.data.map.system_order.hasOwnProperty(selected_field)) {
                        $scope.data.map.system_order[selected_field] = "{{" + map_value + "}}";
                    }
                }
            }
        });
    }


    $scope.process = function(channel_order_codes) {
        $scope.data.error_message = null;
        var map = $scope.getHandlebarsMap();
        var params = {
            channel_id: config.credentials.channels[0].id,
            storage_code: $scope.data.results.system_order_upload.storage_code,
            body: map
        };
        if(channel_order_codes) {
            params.channel_order_codes = channel_order_codes;
        }
        stock2shop.orders.importOrdersQueue(params).then(function(data) {
            $scope.data.results.system_order_import = data.system_order_import;
            $scope.data.step = 3;
        }).catch(function(e){
            $scope.data.error_message = e.data.errors;
        });
    };

    $scope.getHandlebarsMap = function() {
        var deep_clone = JSON.parse(JSON.stringify($scope.data.map));
        deep_clone.system_order.channel_order_code = "{{" + deep_clone.system_order.channel_order_code + "}}";
        deep_clone.system_order.line_items[0].sku = "{{" + deep_clone.system_order.line_items[0].sku + "}}";
        deep_clone.system_order.line_items[0].qty = "{{" + deep_clone.system_order.line_items[0].qty + "}}";

        // Map shipping address fields
        angular.forEach(deep_clone.system_order.shipping_address, function(value, key) {
            if (value !== null) {
                deep_clone.system_order.shipping_address[key] = "{{" + value + "}}";
            }
        });
        return deep_clone;
    };

    $scope.getStatus = function(item) {
        if(item.queued) {
            return "processing";
        }
        if(item.errors.length > 0) {
            return "errors";
        }
        return "valid";
    };

    $scope.send = function() {
        var codes = "";
        angular.forEach($scope.data.channel_order_codes, function(value, key) {
            if(value) {
                if(codes !== "") {
                    codes += ",";
                }
                codes += key;
            }
        });
        if(codes === "") {
            $.SmartMessageBox({
                title: "Warning ",
                content: "You have not selected any orders to process",
                buttons: '[OK]'
            });
            return false;
        }
        $scope.data.channel_order_codes = null;
        $scope.data.select_all = false;
        $scope.process(codes);
    };

    $scope.selectAll = function() {
        if(!$scope.data.select_all) {
            $scope.data.channel_order_codes = {};
        } else {
            angular.forEach($scope.data.results.system_order_import, function(value, key) {
                if(value.errors.length === 0 && !value.queued) {
                    $scope.data.channel_order_codes[value.params.import_order_number] = true;
                }
            });
        }
    };

    $scope.showOrder = function(order) {
        var modalInstance = $modal.open({
            templateUrl: url.get('partials/client/bulk-order-modal.html'),
            controller: 'BulkOrderModalCtrl',
            size: 'lg',
            resolve: {
                order: function () {
                    return order;
                }
            }
        });
    }

    $scope.allowShippingAddressEdit = function() {
        return $scope.settings.hasOwnProperty('edit_shipping_address') &&
            ($scope.settings.edit_shipping_address === "true" || $scope.settings.edit_shipping_address === "1");
    }

}

angular.module('myApp')
    .controller('BulkOrderCtrl', ['$scope', '$location', '$modal', 'config', 'stock2shop', 'FileUploader', 'url', 'price', 'storeCache', BulkOrderCtrl]);
;'use strict';

function BulkOrderModalCtrl($scope, $modalInstance, order, utils, storeCache) {
    $scope.order = order;
    $scope.money = utils.money;
    $scope.totals = $scope.order.system_order.has_taxes_incl ? $scope.order.system_order.totals_incl : $scope.order.system_order;
    $scope.line_totals = {}

    // Get tax description from last line item description
    $scope.tax_description = "TAX";
    angular.forEach($scope.order.system_order.line_items, function(item) {
        if(item.tax_lines && item.tax_lines.length>0) {
            $scope.tax_description = item.tax_lines[0].title || "TAX";
        }
        $scope.line_totals[item.sku] = $scope.order.system_order.has_taxes_incl ? item.totals_incl : item;
    });

    $scope.cancel = function () {
        $modalInstance.dismiss(false);
    };

    $scope.accept = function () {
        $modalInstance.close(true);
    };

    $scope.hasParams = function() {
        return $scope.order.hasOwnProperty('params') && Object.keys($scope.order.params).length > 0;
    };

    $scope.hasShippingAddress = function() {
        return $scope.order.system_order.shipping_address.address1 != null ||
            $scope.order.system_order.shipping_address.address2 != null ||
            $scope.order.system_order.shipping_address.company != null ||
            $scope.order.system_order.shipping_address.first_name != null ||
            $scope.order.system_order.shipping_address.last_name != null ||
            $scope.order.system_order.shipping_address.phone != null ||
            $scope.order.system_order.shipping_address.city != null ||
            $scope.order.system_order.shipping_address.state != null ||
            $scope.order.system_order.shipping_address.zip != null ||
            $scope.order.system_order.shipping_address.province != null ||
            $scope.order.system_order.shipping_address.province_code != null ||
            $scope.order.system_order.shipping_address.country != null ||
            $scope.order.system_order.shipping_address.country_code != null;
    };

    $scope.allowShippingAddressEdit = function() {
        return storeCache.get().settings.hasOwnProperty('edit_shipping_address') &&
            (storeCache.get().settings.edit_shipping_address === "true" || storeCache.get().settings.edit_shipping_address === "1");
    }

}

angular.module('myApp')
    .controller('BulkOrderModalCtrl', ['$scope', '$modalInstance', 'order', 'utils', 'storeCache', BulkOrderModalCtrl]);
;'use strict';

function CheckoutCtrl($scope, $q, $modal, $timeout, cart, utils, stock2shop, config, url, cache, analytics, $http, price, qty) {

    analytics.trackPage('/checkout/', 'Checkout');
    analytics.trackCheckout(1, 'Start');

    $scope.pageReady = false;
    $scope.order = cart.getOrder();
    $scope.use_coupon = false;
    $scope.coupon = "";
    $scope.coupon_redeemed = false;
    $scope.coupon_used = "";
    $scope.shipping = {
        "lines": {},
        "selected": {}
    };
    $scope.payment = {
        "methods": {},
        "selected": ""
    };
    $scope.checkoutFields = angular.fromJson(cache.settings.checkout_fields);

    // override default checkout field values with values stored in order
    if($scope.order.checkout_fields && $scope.order.checkout_fields.length > 0) {
        angular.forEach($scope.order.checkout_fields, function(field, key) {
            if($scope.checkoutFields[key]) {
                if ($scope.checkoutFields[key].type === 'date' && field.value) {
                    $scope.checkoutFields[key].value = new Date(field.value);
                }
            }
        });
    }
    $scope.client_settings = cache.settings;
    $scope.customer = cache.customer;
    $scope.hide_tax = (cache.settings.hide_tax === 'true');

    // Shipping and billing index is the position of the address on customer.addresses
    $scope.shipping_address_index = 0;
    $scope.billing_address_index = 0;

    // Set payment methods.
    try {
        $scope.payment.methods = generatePaymentMethods();
        $scope.payment.selected = $scope.payment.methods[0].method;
    } catch(e) {
        // Backwards compatible with channels that have no payment methods configured.
        $scope.payment.methods = [{
            "method": "OnAccount",
            "description": "On Account"
        }];
        // The default is OnAccount
        $scope.payment.selected = "OnAccount";
    }

    function generatePaymentMethods() {
        var methods = JSON.parse(cache.settings.payment_methods);

        // We allow duplicate payment methods for type "OnAccount"
        // If there are multiple OnAccount methods, we will append a number to the method name starting from 1.

        var count = 1;
        angular.forEach(methods, function(method) {
            if (method.method === 'OnAccount') {
                method.method += count;
                count++;
            }
        });
        return methods;
    }

    function getSelectedPaymentMethod() {
        if ($scope.payment.selected.indexOf("OnAccount") === 0) {
            return "OnAccount";
        }
        return $scope.payment.selected;
    }

    function getSelectedPaymentMethodDescription() {
        var description = null;
        angular.forEach($scope.payment.methods, function(method) {
            if (method.method === $scope.payment.selected) {
                description = method.description;
            }
        });
        return description || "";
    }

    function confirmOrder() {
        $scope.setCheckoutFields();
        stock2shop.orders.confirmOrder({
            channel_id: config.credentials.channels[0].id,
            body: {
                system_order_confirm: {
                    system_order: cart.getOrder(),
                    params: getOrderParams()
                }
            }
        }).then(function(data) {
            $scope.system_order_confirm = data.system_order_confirm;
            $scope.pageReady = true;
            $scope.accepted = false;
            $scope.money = utils.money;
            $scope.setDefaultAddress(cache.customer.addresses);

            // Set line item titles
            cart.setLineTitles($scope.system_order_confirm.system_order);

            // Get discount and salesrules
            angular.forEach($scope.system_order_confirm.salesrules, function(rule) {
                if(rule.use_coupon) {
                    $scope.use_coupon = true;
                }
            });
            cart.setDiscount($scope.system_order_confirm.total_discount);
            $scope.order = cart.setTotals($scope.system_order_confirm.system_order);
            if($scope.system_order_confirm.salesrulecoupons_used.length > 0) {
                $scope.coupon_used = $scope.system_order_confirm.salesrulecoupons_used[0].code;
            } else {
                $scope.coupon_used = "";
            }
            if($scope.coupon !== "") {
                $scope.coupon_redeemed = true;
            }

            // Shipping lines
            $scope.shipping.lines = $scope.system_order_confirm.shipping_lines || [];
            $scope.order.shipping_lines = $scope.system_order_confirm.system_order.shipping_lines;

            // if shipping method already selected, set it
            if($scope.order.shipping_lines && $scope.order.shipping_lines.length > 0) {
                $scope.shipping.selected = $scope.order.shipping_lines[0].title;
                $scope.shippingChanged(false);
            } else {
                // set the first shipping method as selected
                // This will mean we will call confirmOrder again.
                // There may need to be messages displayed depending
                // on the shipping method selected
                // TODO can be refactored to selected cheapest
                if($scope.shipping.lines.length > 0) {
                    $scope.shipping.selected = $scope.shipping.lines[0].title;
                    $scope.shippingChanged(true);
                }
            }

            $scope.totals = $scope.order.has_taxes_incl ? $scope.order.totals_incl : $scope.order;
            $scope.line_totals = {}

            angular.forEach($scope.order.line_items, function(item) {
                $scope.line_totals[item.sku] = $scope.order.has_taxes_incl ? item.totals_incl : item;
            });

            // Must set line item messages after cart.getOrder,
            // cart doesn't contain messages from order confirmation
            setLineItemMessages();

            if ($scope.system_order_confirm.errors === undefined) {
                $scope.system_order_confirm.errors = [];
            }

            // Show credit limit confirmation message if one was provided
            toggleCreditLimitConfirmationMessage();

            // error with confirm order, no way to tell what the issue is
        }, function() {
            cart.resetOrder();
            $.SmartMessageBox({
                title: "Products removed from cart!",
                content: "There are issues with products in this cart, they have been removed. This is most likely due to a system upgrade, please try add these items back and order again.",
                buttons: '[OK]'
            });
            $scope.order = cart.getOrder();
            $scope.pageReady = true;
        });
    }

    function setLineItemMessages() {
        // var lineItemMessageMap = utils.createMap($scope.system_order_confirm.line_item_messages, "sku");
        var msgs = [];
        angular.forEach($scope.system_order_confirm.line_item_messages, function(msg) {
            if(!msgs[msg.sku]) {
                msgs[msg.sku] = [];
            }
            msgs[msg.sku].push(msg);
        });
        angular.forEach($scope.order.line_items, function(line_item) {

            // reset
            // multiple messages for each line
            line_item.messages = [];

            // used to set message type for highlighting the line
            line_item.message = {};

            // Set messages
            if(msgs[line_item.sku]) {
                line_item.messages = msgs[line_item.sku];

                // Create message type for line item.
                // If all message types are the same for the line
                // item then we can set it the same.
                line_item.message.type = msgs[line_item.sku][0].type;
                for(var i = 1; i < msgs[line_item.sku].length; i++) {
                    if(msgs[line_item.sku][i].type !== line_item.message.type) {
                        line_item.message.type = '';
                        break;
                    }
                }
            }
        });
    }

    function toggleCreditLimitConfirmationMessage() {
        if ($scope.displayCreditLimit() && $scope.customer.meta.credit_limit - $scope.totals.total < 0) {
            var message = cache.settings.checkout_credit_limit_exceeded_confirmation_message || '';
            if ($scope.customer.meta.checkout_credit_limit_exceeded_confirmation_message !== undefined) {
                message = $scope.customer.meta.checkout_credit_limit_exceeded_confirmation_message;
            }
            if (message) {
                if ($scope.system_order_confirm.messages === undefined) {
                    $scope.system_order_confirm.messages = [];
                }
                $scope.system_order_confirm.messages.push({
                    confirm: true,
                    message: message
                });
            }
        }
    }

    fetchLineItems(null, function(data, variantsMap) {
        var failedItems = [];
        angular.forEach($scope.order.line_items, function(item) {
            var variant = variantsMap[item.variant_id];
            if (variant) {
                // Assume order items are always ex tax
                var tax_rate = price.getTaxRate(variant, variant.product);
                var new_price = price.getExTax(variant.price, tax_rate);
                if (item.price !== new_price) {
                    item.price = new_price;
                    cart.updatePrice(item, tax_rate);
                }
            } else {
                failedItems.push(item.sku);
                cart.removeItem(item.variant_id);
            }
        });
        if(failedItems.length > 0) {
            $.SmartMessageBox({
                title: "Products removed from cart!",
                content: "These products are no longer valid and have been removed: <br>" + failedItems.join('<br>'),
                buttons: '[OK]'
            });
        }
        confirmOrder();
    });

    $scope.removeItem = function (item) {
        $.SmartMessageBox({
            title: "Removing item from cart",
            content: "Are you sure you want to remove " + item.sku + " from your cart?",
            buttons: '[No][Yes]'
        }, function (ButtonPressed) {
            if (ButtonPressed === "Yes") {
                cart.removeItem(item.variant_id);
                $scope.order = cart.getOrder();
                // cart.calculateTotals($scope.order);
                confirmOrder();
            }
        });
    };

    var qtyChangedPromise = null;
    $scope.qtyChanged = function(index, item) {
        // Cancel existing timeout if set
        $timeout.cancel(qtyChangedPromise);
        // Use timeout to prevent running this function for every key press
        qtyChangedPromise = $timeout(function(){
            if (item.qty && item.qty > 0) {
                // Update cart
                cart.updateQty(item);
                $scope.order = cart.getOrder();
                // cart.calculateTotals($scope.order);
                confirmOrder();
            }
        }, 1000);
    };

    /**
     * Queue getProduct requests for order line items
     * @param lineItemCallback
     *  Called for each line item on the order
     * @param successCallback (data, variantsMap)
     *  Called after all queued requests have completed
     */
    function fetchLineItems(lineItemCallback, successCallback) {
        lineItemCallback = lineItemCallback || function() {};
        successCallback = successCallback || function() {};

        // check each product
        var queue = [];
        var lineItemsMap = {};
        angular.forEach($scope.order.line_items, function (item) {
            lineItemCallback(item);

            lineItemsMap[item.variant_id] = item;

            // Make sure qty has not changed and stock still available.
            // We queue all requests using $q.all, we must therefore catch errors for each promise
            // so $q.all().then()  still succeeds.
            // We simply exclude the failed requests from the variant map and the user will be notified.
            var promise = stock2shop.products.getProduct({product_id: item.product_id}).catch(function () {
                return false;
            });
            queue.push(promise);
        });

        $q.all(queue).then(function(data) {
            var variantsMap = {};
            angular.forEach(data, function(response) {

                // since we are already catching the promise there may be undefined response
                if(response) {
                    angular.forEach(response.system_product.variants, function(variant) {
                        if (lineItemsMap[variant.id]) {
                            variant.product_title = response.system_product.title;
                            variant.price = parseFloat(variant.price);

                            // This variant is a line item on the order
                            variantsMap[variant.id] = variant;
                            variantsMap[variant.id].product = response.system_product;
                        }
                    });
                }
            });
            // we use the variant map for displaying options and other items.
            // Put this onto scope
            $scope.variant_map = variantsMap;
            successCallback(data, variantsMap);
        });
    }

    function getOrderParams() {
        var params = {};

        // checkout fields
        if($scope.checkoutFields) {
            angular.forEach($scope.checkoutFields, function(field, key) {
                var items = key.split('.');
                if(items[0] === 'params') {
                    if (field.type === 'date' && field.value instanceof Date && !isNaN(field.value)) {

                        /**
                         * Bugfix:
                         * Date values from checkout fields are set to previous day after submission
                         * This happens because the js date object itself is posted instead of a string value
                         * So we need to convert the date to a string value before submission
                         * https://github.com/stock2shop/app/issues/1269
                         */

                        // Value comes from date picker, so we only care about the date not time
                        // The string format however does matter as it is sometimes used to set DateTime values in sources
                        params[items[1]] = utils.getYYYYMMDD(field.value, '-') + 'T00:00:00.000Z';
                    } else {
                        params[items[1]] = field.value;
                    }
                }
            });
        }

        // payment
        params.payment_method = getSelectedPaymentMethod();
        params.payment_description = getSelectedPaymentMethodDescription();

        // qty_availability
        if (
            cache.settings.customer_warehouse_override &&
            cache.settings.customer_warehouse_override.trim().length > 0
        ) {
            // Only override if the customer user has qty_availability set
            if (cache.customer.user && cache.customer.user.qty_availability) {
                params[cache.settings.customer_warehouse_override] =
                    cache.customer.user.qty_availability;
            }
        }

        // Add console_user_id as order param if set
        if (config.credentials.console_user_id) {
            params.console_user_id = config.credentials.console_user_id;
        }
        params.coupon = $scope.coupon;
        return params;
    }

    $scope.setCheckoutFields = function () {
        if($scope.checkoutFields) {

            // Keep reference to originally stored checkout fields
            var tempCartCheckoutFields = cart.order.checkout_fields;

            // Default stored checkout fields to empty object before setting to the stored values
            // This is to ensure that any fields that are no longer present in the checkout fields
            // are removed from the stored values.
            cart.setCheckoutFields($scope.checkoutFields);
            if (tempCartCheckoutFields) {
                angular.forEach($scope.checkoutFields, function (field, key) {
                    if (tempCartCheckoutFields[key]) {
                        if (
                            field.type === 'date' &&
                            tempCartCheckoutFields[key].value
                        ) {

                            // Input field of type "date" does not accept a date string when using ng-model.
                            // We have to use a date object instead.
                            tempCartCheckoutFields[key].value = new Date(tempCartCheckoutFields[key].value);
                        } else if (field.type === 'dropdown') {
                            tempCartCheckoutFields[key].options = field.options;
                        }
                        $scope.checkoutFields[key] = tempCartCheckoutFields[key];
                    }
                });
            }
            angular.forEach($scope.checkoutFields, function(field, key) {
                var items = key.split('.');
                if(items[0] !== 'params') {
                    cart.setField(key, field.value);
                }
            });
        }
    }

    $scope.displayCreditLimit = function () {
        if ($scope.customer.meta.credit_limit === undefined || $scope.order.line_items.length === 0) {
            return false
        }
        if ($scope.customer.meta.checkout_display_credit_limit !== undefined) {
            return utils.toBool($scope.customer.meta.checkout_display_credit_limit);
        }
        return utils.toBool(cache.settings.checkout_display_credit_limit);
    }

    function getMinOrderAmount() {
        if ($scope.customer.meta.min_order_amount) {
            return $scope.customer.meta.min_order_amount;
        }
        if (cache.settings.min_order_amount) {
            return cache.settings.min_order_amount;
        }
        return false;
    }

    $scope.sendOrder = function () {
        analytics.trackCheckout(2, 'Terms');
        $scope.pageReady = false;

        // validate checkout  fields and display error if incorrect
        if($scope.checkoutFields) {
            var failedField = false;
            angular.forEach($scope.checkoutFields, function(field, key) {
                if(field.required) {
                    if (!field.value || field.value.length === 0) {
                        failedField = field;
                        failedField.error_message = failedField.description + " required";
                    }
                }
                if(!failedField && field.min_chars) {
                    if (field.value.length < field.min_chars) {
                        failedField = field;
                        failedField.error_message = failedField.description + " needs a minimum of " + field.min_chars + " characters";
                    }
                }
                if(!failedField && field.max_chars) {
                    if (field.value.length > field.max_chars) {
                        failedField = field;
                        failedField.error_message = failedField.description + " must not exceed " + field.max_chars + " characters";
                    }
                }
                // Check date formats
                if(!failedField && field.type === 'date') {
                    if(field.valid_after_days) {
                        var date = new Date(field.value);
                        var minDate = new Date();
                        minDate.setDate(minDate.getDate() + parseInt(field.valid_after_days));
                        if(date.getTime() < minDate.getTime()) {
                            failedField = field;
                            failedField.error_message = field.description + " must be after " + minDate.toString().substring(0,15);
                        }
                    }
                }
            });
            if(failedField) {
                $.SmartMessageBox({
                    title: failedField.error_message,
                    buttons: '[OK]'
                });
                $scope.pageReady = true;
                return false;
            }
        }

        // set the checkout fields on the order
        $scope.setCheckoutFields();

        // Get the minimum order amount
        var minOrderAmount = getMinOrderAmount();
        if(minOrderAmount && ($scope.order.sub_total + $scope.order.shipping_total) < minOrderAmount) {
            $.SmartMessageBox({
                title: "The minimum order amount is " + utils.money(minOrderAmount),
                buttons: '[OK]'
            });
            $scope.pageReady = true;
            return false;
        }

        // Ensure there are no errors with the order before proceeding
        if ($scope.system_order_confirm.errors.length > 0) {
            var errors_list_html = '';
            angular.forEach($scope.system_order_confirm.errors, function(error) {
                if (error) {
                    errors_list_html += '<li>' + error + '</li>';
                }
            });

            if (errors_list_html) {
                $.SmartMessageBox({
                    title: "There are errors with the order",
                    content: "<ul>" + errors_list_html + "</ul>",
                    buttons: '[OK]'
                });
                $scope.pageReady = true;
                return false;
            }
        }

        // Check system_order_confirm messages have been accepted
        if($scope.system_order_confirm.messages) {
            for (var i = 0; i < $scope.system_order_confirm.messages.length; i++) {
                var message = $scope.system_order_confirm.messages[i];
                if (message.confirm) {
                    if (!message["confirmed"]) {
                        $.SmartMessageBox({
                            title: "You have not accepted the order conditions",
                            buttons: '[OK]'
                        });
                        $scope.pageReady = true;
                        return false;
                    }
                }
            }
        }

        // no terms accepted
        if (!$scope.accepted) {
            $.SmartMessageBox({
                title: "You have not accepted the terms",
                buttons: '[OK]'
            });
            $scope.pageReady = true;
            return false;
        }

        // For each line item
        fetchLineItems(null, function(data, variantsMap) {
            // Check line items
            var line_messages = "";
            for (var i = 0; i < $scope.order.line_items.length; i++) {
                var item = $scope.order.line_items[i];
                var variant = variantsMap[item.variant_id];
                if (variant) {

                    // Check qty is valid.
                    var qty_check = qty.validate(variant, variant.product, item.qty);
                    if(!qty_check.valid) {
                        line_messages += (line_messages === "")? "": "<br>";
                        line_messages += qty_check.error;
                    }

                    // Check price is still correct
                    var tax_rate = price.getTaxRate(variant, variant.product);
                    var new_price = price.getExTax(variant.price, tax_rate);
                    var current_item_tax = cart.getItemTax(item.variant_id);
                    if (utils.round2Decimal(item.price) !== new_price || tax_rate !== current_item_tax) {
                        line_messages += (line_messages === "")? "": "<br>";
                        line_messages += "Price changed for " + item.sku +  " (cart updated)" + ".";
                        item.price = new_price;
                        cart.updatePrice(item, tax_rate);
                        $scope.order = cart.getOrder();
                        // cart.calculateTotals($scope.order);
                    }

                } else {
                    // This could happen if the variant is deleted on the server?
                    $.SmartMessageBox({
                        title: "Error with product " + item.sku,
                        content: "This item is no longer available, it will be removed from your cart",
                        buttons: '[OK]'
                    });
                    cart.removeItem(item.variant_id);
                    $scope.pageReady = true;
                    return false;
                }
            }
            if(line_messages !== "") {
                $.SmartMessageBox({
                    title: "Warning ",
                    content: line_messages,
                    buttons: '[OK]'
                });
                $scope.pageReady = true;
                return false;
            }

            // Give the order a fresh random id
            cart.setID(null, function() {

                // Set the order instruction, but note that it
                // will be overwritten by trade\OrderTransform.
                cart.setInstruction('unpaid_order');

                // add Customer
                cart.setCustomer(
                    {
                        "id": cache.customer.channel_customer_code,
                        "first_name": cache.customer.first_name,
                        "last_name": cache.customer.last_name,
                        "email": cache.customer.email,
                        "addresses": cache.customer.addresses
                    }
                );

                // Add Addresses
                cart.setShippingAddress($scope.shipping_address);
                cart.setBillingAddress($scope.billing_address);

                // log to console
                var orderPayload = {
                    system_order: cart.getOrder(),
                    params: getOrderParams()
                };
                // Useful for debugging on live
                console.info('order', orderPayload);

                // confirm
                var sendingOrder = false;
                $.SmartMessageBox({
                    title: "Confirm order",
                    content: "Are you sure you want to send this order?",
                    buttons: '[No][Yes]'
                }, function (ButtonPressed) {
                    if (!sendingOrder && ButtonPressed === "Yes") {
                        // Prevent sending order twice when the user
                        // double clicks the OK button.
                        // Using a flag like this is bad UI,
                        // but unavoidable with the way
                        // this function is structured.
                        sendingOrder = true;

                        analytics.trackCheckout(3, 'Confirm');
                        stock2shop.orders.addOrderQueue(
                            {
                                channel_id: config.credentials.channels[0].id,
                                body: orderPayload
                            }
                        ).then(function () {
                            // Rewrite rule on proxy does not work if trailing slash is omitted.
                            var url = "/payment/" + getSelectedPaymentMethod() + "/" +
                                "?channel_id=" + config["credentials"]["channels"][0]["id"] +
                                "&channel_order_code=" + cart.order.id +
                                "&token=" + config["credentials"]["token"] +
                                "&amount=" + utils.currency($scope.order.has_taxes_incl ? $scope.order.totals_incl.total : $scope.order.total, false);

                            if (getSelectedPaymentMethod() === "OnAccount") {
                                // OnAccount payment page responds with json
                                // Note that this is using the same baseUrl as the stock2shop service,
                                // and it might also use the proxy to get around same domain policy.
                                // The format=json query param is required to make error messages
                                // work properly when testing locally.
                                url = config["baseUrl"] + url + "&format=json";

                                $http({
                                    method: 'GET',
                                    url: url
                                }).then(function successCallback() {
                                    utils.changeRoute("confirmation");
                                }, function errorCallback(response) {
                                    // At this point the order webhook has been posted,
                                    // the order will be created in the system via the queue.
                                    // The client will have to use the console to either
                                    // cancel the order or process it further.
                                    cart.resetOrder();

                                    utils.changeRoute("products");

                                    var errors = response["data"]["errors"];
                                    if (errors) {
                                        $.SmartMessageBox({
                                            title: errors,
                                            buttons: '[OK]'
                                        });
                                    }
                                });

                            } else {
                                // Redirect to payment page
                                // TODO Some gateways might not required a redirect,
                                // for example if the payment form is hosted on stock2shop,
                                // in these cases we can use the same approach as OnAccount.
                                url = config.originalBaseUrl + url;
                                window.location = url;
                            }
                        });
                    } else {
                        $scope.$apply(function() {
                            $scope.pageReady = true;
                        });
                    }
                });
            });

        });
    };

    $scope.clearCart = function () {
        $.SmartMessageBox({
            title: "Confirm cart removal",
            content: "Are you sure you want to remove all items in your cart?",
            buttons: '[No][Yes]'
        }, function (ButtonPressed) {
            if (ButtonPressed === "Yes") {
                $scope.$apply(function () {
                    cart.resetOrder();
                    $scope.order = cart.getOrder();
                });
            }
        });
    };

    $scope.terms = function() {
        var modalInstance = $modal.open({
            templateUrl: url.get('partials/client/terms-modal.html'),
            controller: 'TermsModalCtrl',
            size: 'md',
            resolve: {
                settings: function () {
                    return $scope.settings;
                }
            }
        });
        modalInstance.result.then(function (result) {
            $scope.accepted = result;
        }, function (result) {
            $scope.accepted = result;
        });
    };

    $scope.canEditAddress = function(type) {
        var billing = cache.settings.edit_billing_address;
        var shipping = cache.settings.edit_shipping_address;
        if(type === "shipping") {
            return (shipping && shipping.toLowerCase() === 'true');
        } else if (type === "billing") {
            return (billing && billing.toLowerCase() === 'true');
        }
        return false;
    };

    $scope.canManageCustomerAddress = function() {
        var customer_address = cache.settings.manage_customer_address;
        return (customer_address && customer_address.toLowerCase() === 'true');
    };

    $scope.canDisplayAddress = function(type) {
        var billing = cache.settings.display_billing_address;
        var shipping = cache.settings.display_shipping_address;
        if(type === "shipping") {
            return (shipping && shipping.toLowerCase() === 'true');
        } else if (type === "billing") {
            return (billing && billing.toLowerCase() === 'true');
        }
        return false;
    };

    $scope.setDefaultAddress = function(addresses) {

        // set default address
        angular.forEach(addresses, function(address, key) {
            if(address.type === 'shipping') {
                if(address.default || !$scope.shipping_address) {
                    $scope.shipping_address_index = key;
                    $scope.shipping_address = angular.copy(address);
                }
            }
            if(address.type === 'billing') {
                if(address.default || !$scope.billing_address) {
                    $scope.billing_address_index = key;
                    $scope.billing_address = angular.copy(address);
                }
            }
        });

        // set address if no default given
        if(!$scope.shipping_address) {
            if(addresses.length > 0) {
                $scope.shipping_address_index = 0;
                $scope.shipping_address = angular.copy(addresses[0]);
            } else {
                $scope.shipping_address = {};
            }
        }

        // set address if no default given
        if(!$scope.billing_address) {
            if(addresses.length > 0) {
                $scope.billing_address_index = 0;
                $scope.billing_address = angular.copy(addresses[0]);
            } else {
                $scope.billing_address = {};
            }
        }
    };

    $scope.setAddress = function(type, index) {
        if(type == "shipping") {
            $scope.shipping_address = angular.copy(cache.customer.addresses[index]);
        } else if (type == "billing") {
            $scope.billing_address = angular.copy(cache.customer.addresses[index]);
        }
    };

    $scope.paymentChanged = function() {
        confirmOrder();
    };

    $scope.shippingChanged = function(reload) {
        var shippingSet = false;
        angular.forEach($scope.shipping.lines, function(value) {
            if(value.title === $scope.shipping.selected) {
                cart.setShippingLines([value]);
                $scope.order = cart.getOrder();
                // cart.calculateTotals($scope.order);
                if(reload) {
                    confirmOrder();
                }
                shippingSet = true;
            }
        });
        if(!shippingSet) {
            cart.removeShippingLines();
            $scope.order = cart.getOrder();
            // cart.calculateTotals($scope.order);
            if(reload) {
                confirmOrder();
            }
        }
    };

    $scope.printCart = function(printSectionId) {
        var innerContents = document.getElementById(printSectionId).innerHTML;
        var popupWinindow = window.open('', '_blank', 'width=800,height=1000,scrollbars=no,menubar=no,toolbar=no,location=no,status=no,titlebar=no');
        popupWinindow.document.open();
        popupWinindow.document.write('<html><head><link rel="stylesheet" type="text/css" href="css/theme.css?ts=' + new Date() + '" /></head><body onload="window.print()">' + innerContents + '</html>');
        popupWinindow.document.close();
        return true;
    };

    $scope.hasCheckoutFields = function() {
        var hasfields = false;
        angular.forEach($scope.checkoutFields, function(item) {
            if(item.value !== '') {
                hasfields = true;
            }
        });
        return hasfields;
    };

    $scope.checkCoupon = function() {
        confirmOrder();
    };

    $scope.refreshCoupon = function() {
        $scope.coupon_redeemed = false;
    };

    /**
     * Return address label to display in drop down
     * @param address
     * @returns {*|string}
     */
    $scope.addressLabel = function(address) {
        var label = "";
        if(address.address1 && address.address1 !== "") {
            if(label !== "") {
                label += ", ";
            }
            label += address.address1;
        }
        if(address.address2 && address.address2 !== "") {
            if(label !== "") {
                label += ", ";
            }
            label += address.address2;
        }
        if(address.city && address.city !== "") {
            if(label !== "") {
                label += ", ";
            }
            label += address.city;
        }
        if(address.zip && address.zip !== "") {
            if(label !== "") {
                label += ", ";
            }
            label += address.zip;
        }
        return label;
    };
}

angular.module('myApp')
    .controller('CheckoutCtrl', ['$scope', '$q', '$modal', '$timeout', 'cart', 'utils', 'stock2shop', 'config', 'url', 'cache', 'Analytics', "$http", "price", "qty", CheckoutCtrl]);

;'use strict';

function ConfirmationCtrl($scope, analytics, cart, $routeParams, auth) {
    $scope.response = {};
    $scope.response.status = $routeParams["status"] ? $routeParams["status"] : "success";
    $scope.response.message = $routeParams["message"] ? $routeParams["message"] : "No error message";

    var order = cart.getOrder();
    if ($scope.response.status == "success") {
        analytics.trackCheckout(4, 'Receipt');
        analytics.trackTransaction(
            order.id,
            'B2B Web Store',
            order.sub_total,
            order.tax_total,
            0
        );
    } else {
        // TODO Add analytics
    }
    cart.resetOrder();

    $scope.logout = function () {
        auth.removeCookie();
        auth.redirectToLogin();
    };
}

angular.module('myApp')
    .controller('ConfirmationCtrl', ['$scope', "Analytics", "cart", "$routeParams", "auth", ConfirmationCtrl]);;'use strict';

function CustomerCtrl($scope, utils, cache, stock2shop) {

    $scope.data = {
        page_ready: true,
        editable_address : undefined,
        customer : cache.customer
    };

    $scope.save = function() {
        $scope.data.page_ready = false;
            stock2shop.customers.updateCustomer(
            {
                customer_id: $scope.data.customer.id,
                body: {
                    system_customer: $scope.data.customer
                }
            }
        ).then(function() {
            $scope.data.page_ready = true;
        });
    };

    $scope.addAddress = function() {
        var address_code = utils.randomString(10);
        $scope.data.customer.addresses.push({
            "address_code": address_code
        });
        $scope.data.editable_address = $scope.data.customer.addresses.length - 1;
    };


}

angular.module('myApp')
    .controller('CustomerCtrl', ['$scope', 'utils', 'cache', 'stock2shop', CustomerCtrl]);;'use strict';

function DashboardCtrl($scope, $location, config, cache, analytics) {

    analytics.trackPage('/dashboard', 'Dashboard');

    $scope.pageReady = true;
    $scope.client_name = cache.settings.display_name;

    if (config.credentials.token) {
        $scope.meta = cache.product_meta;
    }

    $scope.openCollection = function (collection) {
        var params = "offset=1&collection=" + encodeURIComponent(collection.description);
        $location.path('products').search(params);
    };

    $scope.openType = function (collection, type) {
        var params = "offset=1&collection=" + encodeURIComponent(collection.description) + "&product_type=" + encodeURIComponent(type.product_type);
        $location.path('products').search(params);
    };

    $scope.openVendor = function (item) {
        var params = "offset=1&vendor=" + encodeURIComponent(item.vendor);
        $location.path('products').search(params);
    };

    $scope.openTags = function (tag) {
        var params = "offset=1&search=" + encodeURIComponent(tag.value);
        $location.path('products').search(params);
    };


}

angular.module('myApp')
    .controller('DashboardCtrl', ['$scope', '$location', 'config', 'cache', 'Analytics', DashboardCtrl]);;'use strict';

function FilterCtrl($scope, $routeParams, elasticsearch, storeCache, utils, route, $location) {

    var currentPath = route.getKey($location.path());
    if(currentPath !== "/products") {
        var query = elasticsearch.setFilterQuery();
        var aggsMap = elasticsearch.getAggregateMap();
        query = elasticsearch.setFilterAggs(query, elasticsearch.getAggregateQuery());
        var params = {
            "exclude_products": true
        };
        elasticsearch.search(query, params).then(function() {
            utils.setAggregationsDisplay($scope.es_data.aggregations, aggsMap, $routeParams);
        });
    }

    $scope.filterDisplay = function(label) {
        // Based on the users settings, display casing
        var cache = storeCache.get();
        if(cache.settings.filter_text_case) {
            switch(cache.settings.filter_text_case) {
                case "upper_case":
                    return label.toString().toUpperCase();
                case "lower_case":
                    return label.toString().toLowerCase();
                case "capitalise":
                    return utils.capitalise(label);
            }
        }

        // default is capitalise
        return utils.capitalise(label);
    };

    /**
     * Show hide filter
     * @param filter
     */
    $scope.toggleFilter = function(filter) {
        angular.forEach($scope.es_data.aggregations, function(item) {
            if(item.key === filter) {
                item.open = (item.open)? false: true;
            }
        });
    };

    /**
     * Take the existing routeParams, add in new filter, append timestamp
     * Route to the product search page
     */
    $scope.applyFilter = function(filter_key, filter_value) {
        var params = {};
        if(filter_key && filter_value) {
            var aggs = elasticsearch.getAggregateMap();
            params = $routeParams;
            params[aggs[filter_key].field] = filter_value;
        }

        // pass through existing params
        if($routeParams.template) {
            params['template'] = $routeParams.template;
        }
        if($routeParams.limit) {
            params['limit'] = $routeParams.limit;
        }
        if($routeParams.sort_by) {
            params['sort_by'] = $routeParams.sort_by;
        }
        if($routeParams.sort_order) {
            params['sort_order'] = $routeParams.sort_order;
        }

        utils.changeRoute(route.get("/products"), params, true);
    };

}
angular.module('myApp')
    .controller('FilterCtrl',
        ['$scope', '$routeParams', 'elasticsearch', 'storeCache', 'utils', 'route', '$location', FilterCtrl]);;'use strict';

function FilterLabelsCtrl($scope, $routeParams, route, elasticsearch, utils, $location) {

    var searchPath = route.get('/products');
    $scope.labels = {};

    // Set all labels based on aggregations
    var aggs = elasticsearch.getAggregateMap();
    angular.forEach(aggs, function(value, key) {
        $scope.labels[key] = {
            "field": value.field,
            "value": $routeParams[value.field] || false
        };
        $scope.labels.Search = {
            "field": 'q',
            "value": $routeParams[value.field] || false
        };
    });


    function addLabels() {
        angular.forEach($scope.labels, function(value, key) {
            if($routeParams[value.field]) {
                $scope.labels[key].value = $routeParams[value.field];
            }
        });

        // Add search label
        if($routeParams.q) {
            $scope.labels.Search.value = "'" + decodeURIComponent($routeParams.q) + "'";
        }
    }

    // On each route change setup labels
    $scope.$on('$routeChangeSuccess', function (event, current, previous) {

        // if product search page loaded and no params, clear all
        if($location.path() === searchPath) {
            var hasParams = false;
            angular.forEach(current.params, function(param, key) {
                if($scope.labels[key]) {
                    hasParams = true;
                    return;
                }
            });
            // no params, reset
            if(!hasParams) {
                angular.forEach($scope.labels, function(label, key) {
                    label.value = false;
                });
            }
        }
        addLabels();
    });

    $scope.showClearAll = function() {
        var hasLabel = false;
        angular.forEach($scope.labels, function(label) {
            if(label.value) {
                hasLabel = true;
                return;
            }
        });
        return hasLabel;
    };

    $scope.getActiveLabels = function() {
        var labels = {};
        angular.forEach($scope.labels, function(label, key) {
            if(label.value) {
                labels[key] = label;
            }
        });
        return labels;
    };


    $scope.clearAll = function() {
        angular.forEach($scope.labels, function(label, key) {
            label.value = false;
        });
        utils.changeRoute(searchPath);
    };

    /**
     * Take the existing routeParams, add in new filter, append timestamp
     * Route to the search page
     */
    $scope.removeFilter = function(filter_key) {
        var params = {};
        angular.forEach($scope.labels, function(label, key) {
            if(filter_key === label.field) {
                label.value = false;
            }
            if(label.value) {
                params[label.field] = label.value;
            }
        });
        utils.changeRoute(searchPath, params);
    };


}
angular.module('myApp')
    .controller('FilterLabelsCtrl',
        ['$scope', '$routeParams', 'route', 'elasticsearch', 'utils', '$location', FilterLabelsCtrl]);;'use strict';

function HomeCtrl($scope, $location, config, cache, analytics, utils) {

    analytics.trackPage('/home', 'Dashboard');

    $scope.pageReady = true;
    $scope.client_name = cache.settings.display_name;

    if (config.credentials.token) {
        $scope.meta = cache.product_meta;
    }
    $scope.customer = cache.customer;
    $scope.utils = utils;

    // TODO add more data to scope so this can be used in custom html
    // Consider last x orders
    // other stats (perhaps ES order reports)

    if(!cache.settings.welcome_html) {
        $scope.html = "<h1>Welcome</h1><h5>Place your orders with {{client_name}} on this portal.</h5>"
    } else {
        $scope.html = cache.settings.welcome_html;
    }
}

angular.module('myApp')
    .controller('HomeCtrl', ['$scope', '$location', 'config', 'cache', 'Analytics', 'utils', HomeCtrl]);;'use strict';

function NavTopCtrl($scope, utils, $routeParams, auth, route, cart, stock2shop, elasticsearch) {

    $scope.selected = undefined;
    $scope.typeahead = {
        "selected" : undefined
    };

    $scope.search = function(q) {
        // var params = {
        //     "limit": $routeParams.limit || 10,
        //     "sort_by": $routeParams.sort_by || "_score",
        //     "sort_order": $routeParams.sort_order || "desc"
        // };
        // if($routeParams.template) {
        //     params['template'] = $routeParams.template;
        // }


        var params = $routeParams;
        params.page = 1;
        if(q) {
            params.q = $scope.typeahead.selected;
        }
        utils.changeRoute(route.get("/products"), params);
    };

    $scope.logout = function () {
        auth.removeCookie();
        cart.removeCookie();
        auth.redirectToLogin();
    };


    $scope.setSuggest = function(item) {
        var params = {
            "id": item._source.id,
            "limit": $routeParams.limit || 10,
            "sort_by": $routeParams.sort_by || "_score",
            "sort_order": $routeParams.sort_order || "desc"
        };
        if($routeParams.template) {
            params['template'] = $routeParams.template;
        }
        utils.changeRoute(route.get("/products"), params);
    };

    /**
     * Get Suggestions
     * Combines multiple suggests into one array
     * @returns {*}
     */
    $scope.getSuggest = function(q) {
        var query = elasticsearch.setSuggests(q);
        return elasticsearch.suggest(query).then(function(data) {
            return data.hits.hits;
        });
    };

}
angular.module('myApp')
    .controller('NavTopCtrl',
        ['$scope', 'utils', '$routeParams', 'auth', 'route', 'cart', 'stock2shop', 'elasticsearch', NavTopCtrl]);
;'use strict';

function OrderCtrl($scope, $q, $routeParams, stock2shop, utils, cart, route, cache) {
    $scope.pageReady = false;
    $scope.money = utils.money;

    $scope.data = {};
    $scope.display = {
        "ORDER NO": "order.channel_order_code",
        "SALES ORDER NO.": "order.sources[0].source_order_code"
    };
    // Columns to show
    if(cache.settings.order_view_display) {
        $scope.display = JSON.parse(cache.settings.order_view_display);
    }

    $q.all([
            stock2shop.orders.getOrder({
                order_id: $routeParams['order_id']
            }),
            stock2shop.orders.getOrderStatuses()
        ])
        .then(function (data) {
            $scope.order = data[0].system_order;
            $scope.order.params = (data[0].params)? data[0].params: {};

            if(data[0].sources[0].source_order_code) {
                $scope.sourceOrderCode = data[0].sources[0].source_order_code;
            }

            $scope.data.system_order_statuses = data[1]["system_order_statuses"];
            $scope.data.statusDescriptionMap = utils.createMap(data[1]["system_order_statuses"], "status");

            if ($scope.sourceOrderCode && !$scope.order.status) {
                $scope.order.status = "ordered";
            }

            // Set the source on the order so it can be accessed when displaying info
            $scope.order.sources = data[0].sources;

            $scope.totals = $scope.order.has_taxes_incl ? $scope.order.totals_incl : $scope.order;
            $scope.line_totals = {}

            // Get tax description from last line item description
            $scope.tax_description = "TAX";
            angular.forEach($scope.order.line_items, function(item) {
                if(item.tax_lines && item.tax_lines.length>0) {
                    $scope.tax_description = item.tax_lines[0].title || "TAX";
                }
                $scope.line_totals[item.sku] = $scope.order.has_taxes_incl ? item.totals_incl : item;
            });


            $scope.pageReady = true;
        }
    );

    $scope.reOrder = function(order) {
        $.SmartMessageBox({
            title: "Re-order #" + order.channel_order_code,
            content: "This will add all the line items in this order to your cart. <br>If any of the line items no longer exist, you will be notified and they will not be added. <br>If you have existing items in your cart, they will remain in your cart. <br>Would you like to proceed?",
            buttons: '[No][Yes]'
        }, function (ButtonPressed) {
            if (ButtonPressed === "Yes") {
                cart.reOrder(
                    order.id,
                    function(response) {
                        if(response.added.length > 0) {
                            utils.changeRoute(route.get("/checkout"));
                        } else {
                            $.SmartMessageBox({
                                title: "No items added to cart, they are no longer valid, manually add your required items.",
                                buttons: '[OK]'
                            });
                        }
                    },
                    function() {
                        $.SmartMessageBox({
                            title: "Failed to add order to cart",
                            buttons: '[OK]'
                        });
                    });
            }
        });
    };

}

angular.module('myApp')
    .controller('OrderCtrl', ['$scope', '$q', '$routeParams', 'stock2shop', 'utils', 'cart', 'route', 'cache', OrderCtrl]);;'use strict';

function OrdersCtrl($scope, $q, $routeParams, $location, cart, config, route, stock2shop, utils, elasticsearch, cache) {

    $scope.data = {
        columns : {
            "ORDER NO.": "order.id",
            "SALES ORDER NO.": "order.sources[0].source_order_code",
            "DATE": "order.created",
            "TOTAL": "order.total"
        }
    };
    $scope.pageReady = false;
    $scope.pagination = config.pagination;
    $scope.pagination.offset = $routeParams.offset || $scope.pagination.offset;

    // Columns to show
    if(cache.settings.order_columns) {
        $scope.data.columns = JSON.parse(cache.settings.order_columns);
    }


    var query = elasticsearch.setFilterAggs({}, {});
    query = elasticsearch.setFilterSort(query, 'created', 'desc');
    $q.all([
        stock2shop.orders.elasticSearchOrders({
            body: query,
            channel_id: config.credentials.channels[0].id,
            customer_id: config.credentials.customer.id
        }),
        stock2shop.orders.getOrderStatuses()
    ]).then(function (data) {
        $scope.pagination.total = data[0].hits.total;
        $scope.pagination.displaying = $scope.pagination.limit * $scope.pagination.offset;
        $scope.pagination.displaying = ($scope.pagination.displaying > $scope.pagination.total) ? $scope.pagination.total : $scope.pagination.displaying;
        $scope.orders = data[0].system_orders;

        $scope.data.system_order_statuses = data[1]["system_order_statuses"];
        $scope.data.statusDescriptionMap = utils.createMap(data[1]["system_order_statuses"], "status");

        angular.forEach($scope.orders, function (order) {
            // Always display exclusive total,
            // see comments here https://github.com/stock2shop/b2b/issues/14
            order.total = utils.currency(order.has_taxes_incl ? order.totals_incl.total : order.total, true);
            if (order.source_order_code && !order.status) {
                order.status = "ordered";
            }
            order.created = utils.getClientDate(order.created);
            order.modified = utils.getClientDate(order.modified);

            // create meta map useful for columns
            var meta = order.meta;
            order.meta = {};
            angular.forEach(meta, function (item) {
                order.meta[item.key] = item.value;
            });
        });
        $scope.pageReady = true;
    });

    $scope.paginateChange = function () {
        var params = "offset=" + $scope.pagination.offset;
        $location.search(params);
    };

    $scope.showOrder = function(order){
        $scope.pageReady = false;
        $location.path('orders/' + order.id).search();
    };

    $scope.reOrder = function(order) {
        $.SmartMessageBox({
            title: "Re-order #" + order.channel_order_code,
            content: "This will add all the line items in this order to your cart. <br>If any of the line items no longer exist, you will be notified and they will not be added. <br>If you have existing items in your cart, they will remain in your cart. <br>Would you like to proceed?",
            buttons: '[No][Yes]'
        }, function (ButtonPressed) {
            if (ButtonPressed === "Yes") {
                cart.reOrder(
                    order.id,
                    function(response) {
                        if(response.added.length > 0) {
                            utils.changeRoute(route.get("/checkout"));
                        } else {
                            $.SmartMessageBox({
                                title: "No items added to cart, they are no longer valid, manually add your required items.",
                                buttons: '[OK]'
                            });
                        }
                    },
                    function() {
                        $.SmartMessageBox({
                            title: "Failed to add order to cart",
                            buttons: '[OK]'
                        });
                    });
            }
        });
    };

    $scope.downloadInvoiceURL = function(order) {
        var url = config.baseUrl;
        url += "/source_orders/download/" + order.id;
        url += "?token=" + config.credentials.token;
        url += "&source_id=" + order.sources[0].source_id;
        url += "&customer_id=" + order.customer_id;
        return url;
    };

    $scope.canDownloadInvoice = function(order) {
        if(
            utils.toBool(cache.settings.account_invoice) &&
            order.sources[0].source_order_code
        ) {
            return true;
        }
        return false;
    };
}

angular.module('myApp')
    .controller('OrdersCtrl', ['$scope', '$q', '$routeParams', '$location', 'cart', 'config', 'route', 'stock2shop', 'utils', 'elasticsearch', 'cache', OrdersCtrl]);;'use strict';

function ProductCtrl($scope, $routeParams, $q, stock2shop, utils, cart, price, cache, analytics, qty) {

    $scope.data = {};

    $scope.pageReady = false;
    $scope.money = utils.money;
    $scope.options = {};
    $scope.selectedOption = {};
    $scope.selectedVariant = false;
    $scope.selectedVariants = false;
    $scope.selectedImage = false;
    $scope.showAvailabilityUnits = false;


    // Display productmeta if set
    var productmetaDisplayMap = {};
    if (cache.settings["productmeta_display"]) {
        var productmeta_display = cache.settings["productmeta_display"].split(",");
        angular.forEach(productmeta_display, function(value) {
            productmetaDisplayMap[value] = true;
        });
    }

    // Units available display
    if (
        cache.settings.show_availability_units &&
        cache.settings.show_availability_units.toLowerCase() === "true"
    ) {
        $scope.showAvailabilityUnits = true;
    }

    $q.all([
            stock2shop.products.getProduct({product_id: $routeParams['product_id']})
        ])
        .then(function (data) {
            $scope.product = data[0].system_product;

            if($scope.product.tags !== "") {
                $scope.product.tagsArray = $scope.product.tags.split(',');
            }
            if($scope.product.images.length > 0) {
                $scope.selectedImage = $scope.product.images[0];
            }

            $scope.product.image_map = utils.createMap($scope.product.images, 'id');

            // check if product in cart
            $scope.itemsInCart = getItemsInCart();

            setupOptions();
            setVariant();

            $scope.pageReady = true;

            // productmeta display
            $scope.product.meta_display = [];
            angular.forEach($scope.product.meta, function(item) {
                if (productmetaDisplayMap[item["key"]]) {
                    $scope.product.meta_display.push(item);
                }
            });

            // Analytics: track product detail
            analytics.addProduct(
                $scope.product.id,
                $scope.product.title,
                $scope.product.collection,
                $scope.product.vendor
            );
            analytics.trackDetail();
            analytics.trackPage('/product/' + $scope.product.title, 'Product View');

        });

    $scope.hideAvailability = function () {
        return cache.settings["hide_availability_enabled"] &&
            cache.settings["hide_availability_enabled"].toLowerCase() === 'true';
    };

    $scope.inStock = function (/*variant*/) {
        if (cache.settings["over_order_enabled"] &&
            cache.settings["over_order_enabled"].toLowerCase() === 'true'
            ) {
            return true;
        }
        return ($scope.selectedVariant.qty > 0);
    };

    $scope.inStockVariant = function (variant) {
        if(!variant) {
            return false;
        }
        if(variant.inventory_management === false) {
            return true;
        }
        return (variant.qty > 0);
    };

    // add item to order
    $scope.addToCart = function () {
        if(!$scope.selectedVariant) {
            $.SmartMessageBox({
                title: "You have not selected a product",
                buttons: '[OK]'
            });
            return false;
        }
        var cartItem = cart.addItem($scope.selectedVariant, $scope.product, $scope.qty);
        if (cartItem.added) {
            $scope.itemsInCart = getItemsInCart();
            
            $.bigBox({
                title: $scope.qty + " x " + $scope.product.title,
                content: "<strong>Item(s) added to your cart</strong> <p>Checkout by clicking the cart icon on the top</p>",
                color: "#739E73",
                icon: "fa fa-check",
                timeout: 4000
            });

            // Analytics: track product detail
            analytics.addProduct(
                $scope.product.id,
                $scope.product.title,
                $scope.product.collection,
                $scope.product.vendor
            );
            analytics.trackDetail();
        } else {
            $.SmartMessageBox({
                title: "Item not added!",
                content: cartItem.error,
                buttons: '[OK]'
            });
        }
    };

    $scope.optionSelect = function (position) {
        angular.forEach($scope.options, function (value, key) {
            if (parseInt(position) < parseInt(key)) {
                delete $scope.selectedOption[key];
            }
        });
        // remove null values from option
        if(!$scope.selectedOption[position]) {
            delete $scope.selectedOption[position];
        }
        setupOptions();
        setVariant();
    };

    $scope.showOption = function (position) {
        if (position === 1) {
            return true;
        }
        return ($scope.selectedOption[position - 1]);
    };

    /**
     * takes all selected variants and returns a range of prices or one if only one variant
     */
    $scope.priceRange = function () {
        if ($scope.selectedVariant) {
            return $scope.displayPrice;
        } else {
            var low = false;
            var high = false;
            angular.forEach($scope.selectedVariants, function (variant) {
                if (low > price.get(variant, $scope.product) || !low) {
                    low = price.get(variant, $scope.product);
                }
                if (high < price.get(variant, $scope.product) || !high) {
                    high = price.get(variant, $scope.product);
                }
            });
            if (low === high) {
                return utils.money(low);
            } else {
                return utils.money(low) + ' - ' + utils.money(high);
            }
        }
    };

    $scope.removeItemFromCart = function (item) {
        $.SmartMessageBox({
            title: "Removing item from cart",
            content: "Are you sure you want to remove " + item.sku + " from your cart?",
            buttons: '[No][Yes]'
        }, function (ButtonPressed) {
            if (ButtonPressed === "Yes") {
                cart.removeItem(item.variant_id);
                $scope.$apply(function () {
                    $scope.order = cart.getOrder();
                    
                    $scope.itemsInCart = getItemsInCart();
                });
            }
        });
    };

    $scope.selectImage = function(image) {
        $scope.selectedImage = image;

        // if image is linked to variant, set options and selected variant.
        var variant = false;
        angular.forEach($scope.product.variants, function(v) {
            if(v.image_id === image.id) {
                variant = v;
            }
        });
        if(variant) {

            // max 3 options
            for (var i = 1; i <= 3; i++) {
                if (variant['option' + i]) {
                    $scope.selectedOption[i] = variant['option' + i];
                    $scope.optionSelect(i);
                }
            }
        } else {

            // no variant set, set variant to nothing
            $scope.selectedOption = {};
            setVariant();
        }
    };

    /**
     * find the variant that satisfies all selected options
     */
    function setVariant() {
        $scope.selectedVariant = false;
        $scope.selectedVariants = [];
        angular.forEach($scope.product.variants, function (variant) {
            var match = 0;
            variant.multiples_of = qty.getMultiplesOf(variant, $scope.product);
            angular.forEach($scope.product.options, function (option) {
                if (variant['option' + option.position] == $scope.selectedOption[option.position]) {
                    match++;
                }
            });
            if (match === utils.objectLength($scope.selectedOption)) {
                if (match === $scope.product.options.length) {
                    $scope.qty = qty.getDefaultItemQty(variant, $scope.product);
                    $scope.selectedVariant = variant;
                    $scope.selectedVariant.isSaleable = qty.isSaleable(variant, $scope.product, $scope.qty);
                    $scope.displayPrice = utils.money(price.get(variant, $scope.product));

                    // For simple products we only want to set the 'selectedImage' when $scope.pageReady is false (meaning when the page loaded for the first time)
                    // For configurable products we want to set 'selectedImage' each time the variant changes
                    if(($scope.pageReady === false || $scope.product.options.length > 0) && $scope.selectedVariant.image_id) {
                        $scope.selectedImage = $scope.product.image_map[$scope.selectedVariant.image_id];
                    }
                }
                $scope.selectedVariants.push(variant);
            }
        });
    }

    /**
     * Sets up options.
     */
    function setupOptions() {
        $scope.options = {};
        angular.forEach($scope.product.options, function (option) {
            if ($scope.selectedOption[option.position - 1] || option.position == 1) {
                angular.forEach($scope.product.variants, function (variant) {
                    $scope.options[option.position] = $scope.options[option.position] || [];
                    if ($scope.options[option.position].indexOf(variant['option' + option.position]) === -1) {
                        if (variantHasOption(variant, option.position) || option.position == 1) {
                            $scope.options[option.position].push(variant['option' + option.position]);
                        }
                    }
                });
            }
        });

        // Sort options
        sortOptions();
    }

    /**
     * Sort $scope.options according to order set in channel meta "option_sort_order"
     * Example structure of JSON object in channel meta:
     * {
     *   "Logo Colour": ["Blue", "Black"],
     *   "Size": ["XS", "S", "M", "L", "XL"]
     * }
     * NOTE: OPTION_NAME and values are NOT case sensitive
     */
    function sortOptions() {
        angular.forEach($scope.product.options, function (option) {
            var name = option.name.toLowerCase();
            var optionArray = $scope.options[option.position];
            if (optionArray) {
                var sortingArr = null;

                if (cache.settings['option_sort_order']) {

                    // Set sort object of set on channel meta
                    var sortingObj = JSON.parse(cache.settings['option_sort_order'].toLowerCase());

                    //Get sorting values for option name
                    sortingArr = sortingObj[name];
                }

                if (sortingArr) {
                    optionArray.sort(function(a, b){
                        var aIndex = sortingArr.indexOf(a.toLowerCase());
                        var bIndex = sortingArr.indexOf(b.toLowerCase());
                        if (aIndex === -1 && bIndex === -1) {
                            // All values are missing from sorting array, sort alphabetically
                            return a.toLowerCase().localeCompare(b.toLowerCase());
                        } else if (aIndex === -1) {

                            // If value is missing from sorting array, set it to last
                            return 1;
                        } else if (bIndex === -1) {

                            // If value is missing from sorting array, set it to last
                            return -1;
                        } else {
                            // All values exist in sorting array, sort by index
                            return aIndex - bIndex;
                        }
                    });
                } else {
                    // Sort alphabetically
                    optionArray.sort(function(a, b){
                        return a.toLowerCase().localeCompare(b.toLowerCase());
                    });
                }
            }
        });
    }

    /**
     * check all selected options against variant.
     * It must have all options to be included.
     *
     * @param variant
     * @param position
     * @returns {boolean}
     */
    function variantHasOption(variant, position) {
        var hasOption = 0;
        for (var i = 1; i < position; i++) {
            if ($scope.selectedOption[i] == variant['option' + i]) {
                hasOption++;
            }
        }
        return hasOption == position - 1;
    }


    /**
     * returns items i cart with sku and qty
     * @returns {*}
     */
    function getItemsInCart() {
        var order = cart.getOrder();
        var items = [];
        angular.forEach(order.line_items, function (item) {
            angular.forEach($scope.product.variants, function(variant) {
                if (item.variant_id == variant.id) {
                    items.push({
                        variant_id: variant.id,
                        qty: item.qty,
                        sku: item.sku,
                        option1: variant.option1,
                        option2: variant.option2,
                        option3: variant.option3
                    });
                }
            });
        });
        return items;
    }
}

angular.module('myApp')
    .controller('ProductCtrl', ['$scope', '$routeParams', '$q', 'stock2shop', 'utils', 'cart', 'price', 'cache', 'Analytics', 'qty', ProductCtrl]);;'use strict';

function ProductsExportCtrl($scope, stock2shop, config, analytics) {

    analytics.trackPage('/product/export', 'Product export');
    $scope.link = false;
    $scope.processing = false;
    $scope.showProductExport = false;

    stock2shop.products.getFieldMap({
        channel_id: config.credentials.channels[0].id
    }).then(function(data) {
        var product_field_map = data["product_field_map"];
        $scope.fieldMap = [];
        angular.forEach(product_field_map, function(item) {
            $scope.fieldMap.push({
                selected: true,
                key: item["key"],
                value: item["value"],
                description: item["description"]
            })
        });

        $scope.fieldFlags = {};

        $scope.showProductExport = true;
    });

    $scope.exportProducts = function() {
        analytics.trackEvent('Product', 'Export', 'Start');
        $scope.processing = true;
        var fieldMap = {};
        angular.forEach($scope.fieldMap, function(field) {
            if(field.selected) {
                fieldMap[field.key] = field.value;
            }
        });

        var params = {
            format: 'csv',
            channel_id: config.credentials.channels[0].id,
            body: {field_map: fieldMap}
        };

        angular.forEach($scope.fieldFlags, function(value, key) {
            params[key] = String(value);
        });

        stock2shop.products.exportProducts(
            params,
            function(result) {
                analytics.trackEvent('Product', 'Export', 'Complete');
                $scope.link = result.data["system_product_export"].path;
            }
        )
    };
}

angular.module('myApp')
    .controller('ProductsExportCtrl', ['$scope', 'stock2shop', 'config', 'Analytics', ProductsExportCtrl]);;'use strict';

function ProductsSearchCtrl(
    $scope, $location, $routeParams, price, utils, config,
    cart, cache, analytics, elasticsearch, route, qty) {

    $scope.data = {};

    analytics.trackPage($location.url(), 'Product List');
    $scope.pageReady = false;
    $scope.search = $routeParams.search || '';
    $scope.system_products = [];
    $scope.pagination = config.pagination;
    $scope.pagination.page = $routeParams.page || 1;
    $scope.pagination.limit = $routeParams.limit || 10;
    $scope.sort_by = $routeParams.sort_by || "_score";
    $scope.sort_order = $routeParams.sort_order || "desc";
    $scope.path = $location.path();
    $scope.hide_tax = false;
    $scope.hideOptionsOnListView = false;

    // list, grid, or both
    $scope.allowed_product_display_type = getDisplayType();

    // List of templates allowed
    $scope.templates = getAllowedDisplayTemplates();

    // default is grid
    $scope.template = getSelectedTemplate($scope.allowed_product_display_type);

    // default template for channel, if set
    if(cache.settings.product_template) {
        $scope.template = getSelectedTemplate(cache.settings.product_template);
    }

    // hide tax
    if(cache.settings.hide_tax && cache.settings.hide_tax === "true") {
        $scope.hide_tax = true;
    }

    // tax description
    if(cache.settings.price_display && cache.settings.price_display === "inclusive") {
        $scope.tax_description_incl = 'incl '
    } else {
        $scope.tax_description_incl = 'excl '
    }
    $scope.tax_description = cache.settings.tax_description || "tax";

    // User activated template - ensure
    if($routeParams.template) {
        $scope.template = getSelectedTemplate($routeParams.template);
    }

    // Hide options on list view
    if (
        cache.settings.hide_options_on_list_view &&
        cache.settings.hide_options_on_list_view.toLowerCase() === "true"
    ) {
        $scope.hideOptionsOnListView = true;
    }

    // Display product info if set
    var productInfo = {};
    if (cache.settings.product_info_display) {
        productInfo = JSON.parse(cache.settings.product_info_display);
    } else {
        productInfo = {
            "Brand": "vendor",
            "Category": "collection",
            "Type": "product_type"
        };
    }

    search();
    // constructor ends here....................................................

    function search() {
        // Build elastic search query
        var aggs = elasticsearch.getAggregateQuery();
        var aggsMap = elasticsearch.getAggregateMap();
        var query = elasticsearch.setFilterAggs({}, aggs);
        if($routeParams['q']) {
            query = elasticsearch.setFilterQueryString(
                query,
                $routeParams['q']
            );
        }

        // product given
        if($routeParams['id']) {
            query = elasticsearch.setFilterID(query, $routeParams['id']);
            query = elasticsearch.setFilterSize(query, 1);
            query = elasticsearch.setFilterFrom(query, 0);
        } else {
            // Each route param gets set as a term filter on the query
            angular.forEach($routeParams, function(value, key) {
                query = elasticsearch.setFilterTerm(query, key, value);
            });

            // paging
            query = elasticsearch.setFilterSize(query, $scope.pagination.limit);
            query = elasticsearch.setFilterFrom(query, ($scope.pagination.page - 1) * $scope.pagination.limit);
        }

        // sort results
        query = elasticsearch.setFilterSort(query, $scope.sort_by, $scope.sort_order);

        // Do search
        elasticsearch.search(query).then(function() {
            $scope.system_products = $scope.es_data.system_products;
            $scope.pagination.total = $scope.es_data.hits.total;
            $scope.pagination.displaying = $scope.pagination.page * $scope.pagination.limit;
            if ($scope.es_data.hits.total < $scope.pagination.displaying) {
                $scope.pagination.displaying = $scope.es_data.hits.total;
            }
            utils.setAggregationsDisplay($scope.es_data.aggregations, aggsMap, $routeParams);

            angular.forEach($scope.system_products, function (product) {
                // price display
                var low = false;
                var high = false;
                var total_qty = 0;
                product.qty_in_cart = 0;

                // Sort variants by options
                sortOptions(product);

                angular.forEach(product.variants, function (variant) {
                    variant.display_price = utils.money(price.get(variant, product));
                    variant.display_tax_rate = price.getTaxRate(variant, product);
                    variant.display_tax = utils.money(price.getTax(variant.price, variant.display_tax_rate));
                    if (low > price.get(variant, product) || !low) {
                        low = price.get(variant, product);
                    }
                    if (high < price.get(variant, product) || !high) {
                        high = price.get(variant, product);
                    }
                    total_qty += variant.qty;

                    var variantInCart = cart.getVariant(variant.id);
                    if (variantInCart) {
                        product.qty_in_cart += parseInt(variantInCart.qty);
                        variant.qty_in_cart = parseInt(variantInCart.qty);
                    } else {
                        variant.qty_in_cart = 0;
                    }
                    variant.qty_to_add = qty.getDefaultItemQty(variant, product);
                    variant.multiples_of = qty.getMultiplesOf(variant, product);

                    variant.isSaleable = qty.isSaleable(variant, product, variant.qty_to_add);

                    // display options for selection
                    variant.display_option = variant.sku;
                    angular.forEach(product.options, function(option) {
                        variant.display_option +=  " - " + variant["option" + option.position];
                    });
                    // variant.display_option +=  " (" + variant.display_price + ")";

                });
                product.price_variance = (low !== high);
                product.min_price = utils.money(low);
                product.max_price = utils.money(high);
                product.total_qty = total_qty;
                product.availability = utils.stockLevels(total_qty);
                product.image_map = utils.createMap(product.images, 'id');

                //select first variant if only one variant
                if(product.variants.length === 1) {
                    product.selected_variant = product.variants[0];
                }

                // product info display
                product.info_display = {};
                var meta = utils.createMap(product.meta, 'key');
                angular.forEach(productInfo, function(value, key) {
                    if(value.indexOf("meta_") === 0) {
                        value = value.replace("meta_", "");
                        if(meta[value]) {
                            product.info_display[key] = meta[value].value;
                        } else {
                            product.info_display[key] = "n/a";
                        }

                    } else {
                        product.info_display[key] = product[value];
                    }
                });


                // Units available display
                if (
                    cache.settings.show_availability_units &&
                    cache.settings.show_availability_units.toLowerCase() === "true"
                ) {
                    $scope.data.showAvailabilityUnits = true;
                }
            });

            utils.focus('search');
            $scope.pageReady = true;
        });
    }

    $scope.objectKeys = function(obj){
        return Object.keys(obj);
    };

    $scope.isSaleable = function(product, variant, amount) {
        return qty.isSaleable(variant, product, amount);
    };

    $scope.inStockVariant = function (variant) {
        if(!variant) {
            return false;
        }
        if(variant.inventory_management === false) {
            return true;
        }
        return (variant.qty > 0);
    };

    $scope.overOrderEnabled = function(meta) {
        meta = meta || [];

        // Check if enabled on the product
        var meta_map = utils.createMap(meta, 'key');
        if (meta_map && meta_map.over_order_enabled) {
            return meta_map.over_order_enabled.value.toLowerCase() === 'true';
        }

        // Check if enabled on the channel
        if (cache.settings["over_order_enabled"] &&
            cache.settings["over_order_enabled"].toLowerCase() === 'true') {
            return true;
        }
        return false;
    };

    $scope.hideAvailability = function () {
        return cache.settings.hide_availability_enabled &&
            cache.settings.hide_availability_enabled.toLowerCase() === 'true';
    };

    $scope.setTemplate = function(userTemplate) {
        $scope.template = userTemplate;
        $scope.pageChanged();
    };

    $scope.setSortOrder = function(userSort) {
        $scope.sort_order = userSort;
        $scope.pageChanged();
    };

    $scope.view = function (product_id) {
        $scope.pageReady = false;
        $location.path(route.get("/product") + '/' + product_id);
    };

    $scope.pageChanged = function() {
        var params = $routeParams;
        params["page"] = $scope.pagination.page;
        params["limit"] = $scope.pagination.limit;
        params["sort_by"] = $scope.sort_by;
        params["sort_order"] = $scope.sort_order;
        params["template"] = $scope.template;
        utils.changeRoute(route.get("/products"), params, true);
    };

    $scope.addToCart = function(product, variant) {
        var cartItem = cart.addItem(variant, product, variant.qty_to_add);
        if (cartItem.added) {
            $.bigBox({
                title: variant.qty_to_add + " x " + product.title,
                content: "<strong>Item(s) added to your cart</strong> <p>Checkout by clicking the cart icon on the top</p>",
                color: "#739E73",
                icon: "fa fa-check",
                timeout: 4000
            });
            variant.qty_in_cart = parseInt(variant.qty_to_add);

            // Analytics: track product detail
            analytics.addProduct(
                product.id,
                product.title,
                product.collection,
                product.vendor
            );
            analytics.trackDetail();
        } else {
            $.SmartMessageBox({
                title: "Item not added!",
                content: cartItem.error,
                buttons: '[OK]'
            });
        }
    };

    $scope.removeItemFromCart = function (item) {
        $.SmartMessageBox({
            title: "Removing item from cart",
            content: "Are you sure you want to remove " + item.sku + " from your cart?",
            buttons: '[No][Yes]'
        }, function (ButtonPressed) {
            if (ButtonPressed === "Yes") {
                cart.removeItem(item.id);
                $scope.$apply(function () {
                    $scope.order = cart.getOrder();

                    item.qty_in_cart = 0;
                });
            }
        });
    };

    /**
     * Sort variants by channel meta "option_sort_order"
     * Example structure of JSON object in channel meta:
     * {
     *   "Logo Colour": ["Blue", "Black"],
     *   "Size": ["XS", "S", "M", "L", "XL"]
     * }
     *
     * NOTE: OPTION_NAME and values are NOT case sensitive
     * @param product
     */
    function sortOptions(product) {
        var sortingArr = [];
        angular.forEach(product.options, function (option) {
            var name = option.name.toLowerCase();
            if (cache.settings["option_sort_order"]) {
                var sortingObj = JSON.parse(cache.settings['option_sort_order'].toLowerCase());
                sortingArr[option.position] = sortingObj[name];
            }
        });

        // Below sorting function is used for sorting array of objects on multiple fields i.e. option1, option2, option3
        // Ref: https://stackoverflow.com/questions/13820759/javascript-sorting-an-array-of-objects-by-three-values
        product.variants.sort(function (a, b) {
            var optionA;
            var optionB;
            if (sortingArr[1]) {
                optionA = sortingArr[1].indexOf(a.option1.toLowerCase());
                optionB = sortingArr[1].indexOf(b.option1.toLowerCase());
                if (optionA < optionB) {return -1;}
                if (optionA > optionB) {return 1;}
            }
            if (sortingArr[2]) {
                optionA = sortingArr[2].indexOf(a.option2.toLowerCase());
                optionB = sortingArr[2].indexOf(b.option2.toLowerCase());
                if (optionA < optionB) {return -1;}
                if (optionA > optionB) {return 1;}
            }
            if (sortingArr[3]) {
                optionA = sortingArr[3].indexOf(a.option3.toLowerCase());
                optionB = sortingArr[3].indexOf(b.option3.toLowerCase());
                if (optionA < optionB) {return -1;}
                if (optionA > optionB) {return 1;}
            }

            // Sort by SKU if "option_sort_order" not set on channel meta
            if (a.sku < b.sku) {
                return -1;
            }
            if (a.sku > b.sku) {
                return 1;
            }

            return 0;
        });
    }

    function isValidDisplayType(type) {
        var allowedTypes = ['grid', 'list', 'both'];
        return allowedTypes.indexOf(type.toLowerCase()) !== -1;
    }

    function isValidTemplate(template) {
        var allowedTemplates = ['grid', 'list'];
        return allowedTemplates.indexOf(template.toLowerCase()) !== -1;
    }

    function getDisplayType() {
        var displayType = cache.settings.allowed_product_display_type || 'both';
        if (!isValidDisplayType(displayType)) {
            displayType = "both";
        }
        return displayType.toLowerCase();
    }

    function getAllowedDisplayTemplates() {
        var templates = {};
        if ($scope.allowed_product_display_type === 'both') {
            templates = {
                "grid": {
                    url: "partials/client/product-grid-wide.html?ts=" + new Date().getTime()
                },
                "list": {
                    url: "partials/client/product-list.html?ts=" + new Date().getTime()
                }
            };
        } else {
            var template_name = ($scope.allowed_product_display_type === 'grid')
                ? 'product-grid-wide' : 'product-list';
            templates[$scope.allowed_product_display_type] = {
                url: "partials/client/" + template_name + ".html?ts=" + new Date().getTime()
            };
        }
        return templates;
    }

    function getSelectedTemplate(template) {

        // If only a single display type is allowed, return that
        if ($scope.allowed_product_display_type !== 'both') {
            return $scope.allowed_product_display_type;
        }

        // If template is not valid, default to "grid"
        if (!isValidTemplate(template)) {
            return 'grid';
        }
        return template;
    }

    // Execute setTemplate function only after it has loaded
    $scope.setTemplate($scope.template);
}

angular.module('myApp')
    .controller('ProductsSearchCtrl', ['$scope', '$location', '$routeParams',
        'price', 'utils', 'config', 'cart',
        'cache', 'Analytics', 'elasticsearch', 'route', 'qty', ProductsSearchCtrl]);
;'use strict';

function QuickOrderCtrl($scope, stock2shop, elasticsearch, cache, analytics, cart, utils, $timeout, qty, config, price, $location) {


    if ($scope.allow_quick_order === false) {
        $location.path('/');
    }

    analytics.trackPage('/quick_order', 'Quick Order');

    $scope.data = {
        pageReady : false,
        order : null,
        q: "",
        results: [],
        showAvailabilityUnits: (
            cache.settings.show_availability_units &&
            cache.settings.show_availability_units.toLowerCase() === "true"
        ),
        showCart: false,
        columns: {
            "DESCRIPTION": "title"
        },
        hide_tax: false
    };
    $scope.money = utils.money;

    // hide tax
    if(cache.settings.hide_tax && cache.settings.hide_tax === "true") {
        $scope.data.hide_tax = true;
    }

    // Columns to show
    if(cache.settings.quick_order_columns) {
        $scope.data.columns = JSON.parse(cache.settings.quick_order_columns);
    }


    var timeout; // used to monitor timeout
    $scope.$watch('data.q', function (current, previous, callerScope) {
        $scope.data.pageReady = false;
        if(timeout) {
            $timeout.cancel(timeout);
        }
        timeout = $timeout(function() {
            if(current.length > 1) {
                callerScope.getSuggest(current).then(function(data) {
                    $scope.data.pageReady = true;
                    $scope.data.results = [];
                    angular.forEach(data.system_products, function(item) {
                        angular.forEach(item.variants, function(variant) {
                            // set meta
                            angular.forEach(item.meta, function(meta) {
                                variant['meta_' + meta.key.toLowerCase()] = meta.value;
                            });
                            variant.title = item.title;
                            if(variant.option1) {
                                variant.title += " [" + variant.option1;
                                if(variant.option2) {
                                    variant.title += " - " + variant.option2;
                                }
                                if(variant.option3) {
                                    variant.title += " - " + variant.option3;
                                }
                                variant.title += "]";
                            }
                            variant.product_id = item.id;
                            variant.options = item.options;
                            variant.tags = item.tags;
                            variant.vendor = item.vendor;
                            variant.product_type = item.product_type;
                            variant.source_product_code = item.source_product_code;
                            variant.collection = item.collection;
                            variant.qty_ordered = qty.getDefaultItemQty(variant, item);
                            variant.multiples_of = qty.getMultiplesOf(variant, item);
                            variant.isSaleable = qty.isSaleable(variant, item, variant.qty_ordered);
                            variant.display_price = utils.money(price.get(variant, item));
                            $scope.data.results.push(variant);
                        });
                    });
                });
            }
        }, 200); // delay 200 ms
    });


    /**
     * Get Suggestions
     * Combines multiple suggests into one array
     * @returns {*}
     */
    $scope.getSuggest = function(q) {
        var query = elasticsearch.setSuggests(q, 20);
        return elasticsearch.suggest(query, false);
    };

    $scope.removeItem = function (item) {
        cart.removeItem(item.variant_id);
        $scope.confirmOrder(cart.getOrder()).then(function() {
            if($scope.data.order.line_items.length === 0) {
                $scope.data.showCart = false;
            }
        });
    };

    $scope.addItem = function (variant_id, qty_to_add) {

        // find the correct product
        var query = elasticsearch.setTerm({}, 'variants.id', variant_id);
        elasticsearch.search(query).then(function(data) {
            angular.forEach(elasticsearch.data.system_products[0].variants, function(variant ,key) {
                if(parseInt(variant.id) === parseInt(variant_id)) {
                    var cartItem = cart.addItem(variant, elasticsearch.data.system_products[0], qty_to_add);
                    if(cartItem.added) {
                        $scope.confirmOrder(cart.getOrder());
                    } else {
                        $.SmartMessageBox({
                            title: "Item not added!",
                            content: cartItem.error,
                            buttons: '[OK]'
                        });
                    }
                }
            });

            //
        });

    };

    $scope.inStockVariant = function (variant) {
        if(!variant) {
            return false;
        }
        if(variant.inventory_management === false) {
            return true;
        }
        return (variant.qty > 0);
    };

    $scope.hideAvailability = function () {
        return cache.settings.hide_availability_enabled &&
            cache.settings.hide_availability_enabled.toLowerCase() === 'true';
    };

    $scope.overOrderEnabled = function(item) {
        item = item || {};

        // Check if enabled on the product
        if (item && item.meta_over_order_enabled &&
            item.meta_over_order_enabled.toLowerCase() === 'true') {
            return true;
        }

        // Check if enabled on the channel
        if (cache.settings["over_order_enabled"] &&
            cache.settings["over_order_enabled"].toLowerCase() === 'true') {
            return true;
        }
        return false;
    };

    $scope.toggleCart = function() {
        $scope.data.showCart = !$scope.data.showCart;
    };

    /**
     * confirms the order and sets totals
     * @param order
     * @returns {*}
     */
    $scope.confirmOrder = function(order) {
        return stock2shop.orders.confirmOrder({
            channel_id: config.credentials.channels[0].id,
            body: {
                system_order_confirm: {
                    system_order: order,
                    params: {}
                }
            }
        }).then(function (data) {
            $scope.data.order = cart.setTotals(data.system_order_confirm.system_order);
            $scope.data.totals = $scope.data.order.has_taxes_incl ? $scope.data.order.totals_incl : $scope.data.order;
            $scope.data.line_totals = {};
            angular.forEach($scope.data.order.line_items, function(item) {
                $scope.data.line_totals[item.sku] = $scope.data.order.has_taxes_incl ? item.totals_incl : item;
            });

        })
    };

    $scope.confirmOrder(cart.getOrder())
}

angular.module('myApp')
    .controller('QuickOrderCtrl', ['$scope', 'stock2shop', 'elasticsearch', 'cache', 'Analytics', 'cart', 'utils', '$timeout', 'qty', 'config', 'price', '$location', QuickOrderCtrl]);
;'use strict';

function SearchCtrl($scope, $routeParams, utils, storeCache) {
    $scope.searchParams = {};
    $scope.searchParams.search = $routeParams.search || "";
    $scope.searchParams.collection = $routeParams.collection || "";
    $scope.searchParams.product_type = $routeParams.product_type || "";
    $scope.searchParams.vendor = $routeParams.vendor || "";

    $scope.product_meta = storeCache.get().product_meta;
    $scope.settings = storeCache.get().settings;

    $scope.changeRoute = function() {
        var params = {
            from_id: 0,
            // Force search even if params stay the same
            ts: new Date().getTime()
        };
        if ($scope.searchParams.search) {
            params.search = $scope.searchParams.search;
        }

        if ($scope.searchParams.collection) {
            params.collection = $scope.searchParams.collection;
        }
        if ($scope.searchParams.product_type) {
            params.product_type = $scope.searchParams.product_type;
        }
        if ($scope.searchParams.vendor) {
            params.vendor = $scope.searchParams.vendor;
        }

        var doSearch = true;
        var minSearchChars = 2;
        $scope.searchParams.search = $scope.searchParams.search.trim();
        if (
            $scope.searchParams.search.length > 0 &&
            $scope.searchParams.search.length < minSearchChars
        ) {
            $.SmartMessageBox({
                title: "Search must be at least " + minSearchChars + " characters",
                buttons: '[OK]'
            });
            doSearch = false;
        }

        if (doSearch) {
            utils.changeRoute("/products", params);
        }
    };
}

angular.module('myApp')
    .controller('SearchCtrl',
        ['$scope', '$routeParams', 'utils', 'storeCache', SearchCtrl]);;'use strict';

function TermsModalCtrl($scope, $modalInstance, settings) {
    $scope.settings = settings;

    $scope.cancel = function () {
        $modalInstance.dismiss(false);
    };

    $scope.accept = function () {
        $modalInstance.close(true);
    };

}

angular.module('myApp')
    .controller('TermsModalCtrl', ['$scope', '$modalInstance', 'settings', TermsModalCtrl]);;'use strict';

angular.module('myApp')
    .controller('MainCtrl', [
        '$scope', '$rootScope', '$location', '$routeParams', '$route',
        '$window', '$q', 'validation', 'cart', 'stock2shop', 'config',
        'auth', 'utils', 'storeCache', 'Analytics', 'elasticsearch',
        function ($scope, $rootScope, $location, $routeParams, $route,
                  $window, $q, validation, cart, stock2shop, config,
                  auth, utils, storeCache, analytics, elasticsearch) {

            // set clientID for analytics
            // this global variable is set on the index page.
            analytics.set('dimension1', gaClientID);

            // This used in ng-if on index page.
            // This ensures we have all data prepared before
            // Controllers are dynamically loaded (e.g. via ng-controller)
            // ng-if's cycle depth is 550 and ng-controller 600 I think
            $scope.pageReady = false;
            $scope.allow_bulk_order = true;
            $scope.allow_quick_order = true;

            // cache
            var promise = storeCache.set();
            if (promise) {
                promise.then(function(cache) {
                    $scope.settings = cache.settings;
                    $scope.settings.display_name = $scope.settings.display_name || "B2B Store";
                    $scope.allow_bulk_order = ($scope.settings.allow_bulk_order) ? utils.toBool($scope.settings.allow_bulk_order): true;
                    $scope.allow_quick_order = ($scope.settings.allow_quick_order) ? utils.toBool($scope.settings.allow_quick_order): true;

                    // ES product data setup
                    // ----------------------------------------

                    // Get data aggregations for search
                    // These form the layered navigation and are required
                    // Throughout the app.
                    // We have special channel meta "aggregations_display" for controlling
                    // how aggregations get displayed. This allows for setting
                    // the order in which aggregation is displayed and other ui features.
                    var aggs = getChannelAggs();
                    // var aggs_display = getChannelAggsDisplay();
                    elasticsearch.setAggregates(aggs);

                    // Set 'Search By SKU' feature depending on 'search_by_sku_default' channel meta
                    if (storeCache.get().settings.search_by_sku_default !== undefined &&
                        storeCache.get().settings.search_by_sku_default === "true") {
                        elasticsearch.setSearchBySku(true);
                    }

                    // Bind controllers data to the elastic search service data
                    // http://stackoverflow.com/questions/19744462/update-scope-value-when-service-data-is-changed
                    // Since main is like parent scope for the entire app
                    // All child controllers can reference $scope.es_data
                    $scope.es_data = elasticsearch.data;

                    // Fetch cart from database if nothing found in local storage
                    // We load the cart after the storeCache is set as there is a race condition between
                    // the cart and the storeCache. The cart needs the storeCache to be set.
                    if (
                        config.credentials &&
                        config.credentials.customer &&
                        config.credentials.customer.id
                    ) {
                        if (!cart.hasCookie(config.credentials.customer.id)) {
                            cart.loadCart();
                        }
                    }

                    // Good to go
                    $scope.pageReady = true;
                });
            }

            // menu options
            $scope.mobile = false;
            $scope.menu = "";

            // used for sidebar state (visible or not)
            $scope.sidebar = true;

            // used to hide main content when opening sidebar on mobile
            $scope.displayContent = true;

            // initial route is current location path
            $scope.route = $location.path();

            // validation
            $rootScope.validate = validation.validate;
            angular.forEach(validation.validationTypes(), function (typeData, type) {
                var capitalisedType = type.charAt(0).toUpperCase() + type.slice(1);
                $rootScope["valid" + capitalisedType] = validation.validate(type);
            });

            //if we are on mobile, close sidebar
            mobileSetup();

            // user setup
            if (config.credentials.token) {
                auth.addCredentialsWatcher(userSetup);
                userSetup();
            }

            // Listening for events on rootScope is confusing and fragile
            // in the same sense as using global variables to communicate state,
            // rather use pub/sub functions on the service.
            if (cart.order && cart.order.line_items) {
                $scope.order_lines = cart.order.line_items.length;
            }
            cart.subscribeCartUpdated(function() {
                // Used to display number of cart items in the top nav
                $scope.order_lines = cart.order.line_items.length;

                // persist the order to db
                cart.persistCart();
            });

            // On each route change
            $scope.$on('$routeChangeSuccess', function (event, current, previous) {

                //if we are on mobile, close sidebar
                mobileSetup();

                // scroll to top of page, using jquery
                $window.scrollTo(0, 0);
                //$scope.pageReady = true;
                $scope.displayContent = true;
            });

            $scope.openSidebar = function () {
                if ($scope.mobile) {
                    $scope.displayContent = false;
                }
                $scope.sidebar = true;
            };

            $scope.closeSidebar = function () {
                $scope.sidebar = false;
                $scope.displayContent = true;
            };

            $scope.closeMenu = function () {
                $scope.menu = "";
                $scope.query = "";
            };

            function mobileSetup() {
                if ($window.innerWidth < 992) {
                    $scope.mobile = true;
                    $scope.sidebar = false;
                } else {
                    $scope.sidebar = true;
                    $scope.mobile = false;
                }
            }

            function userSetup() {
                $scope.emailHash = utils.md5(config.credentials.email);
                $scope.name = config.credentials.name;
                $scope.surname = config.credentials.surname;
                $scope.client_name = config.credentials.client_name;
            }

            /**
             * Returns ES aggs setup for this channel
             * This is either the default aggregation (collection, type and vendor)
             * Or, this is a custom aggregation from channel meta
             */
            function getChannelAggs() {

                var aggs = {};
                if($scope.settings.aggregations) {
                    aggs = angular.fromJson($scope.settings.aggregations);
                } else {
                    aggs = {
                        "Categories" : {
                            "terms" : {
                                "field" : "collection",
                                "exclude" : "",
                                "order" : { "_term" : "asc" },
                                "size": 200
                            }
                        },
                        "Types" : {
                            "terms" : {
                                "field" : "product_type",
                                "exclude" : "",
                                "order" : { "_term" : "asc" },
                                "size": 200
                            }
                        },
                        "Brands" : {
                            "terms" : {
                                "field" : "vendor",
                                "exclude" : "",
                                "order" : { "_term" : "asc" },
                                "size": 200
                            }
                        }
                    };
                }
                return aggs;
            }

        }]);
;'use strict';

function AutomotiveFilterCtrl($scope, $routeParams, elasticsearch, storeCache, utils, route, stock2shop, config, $location) {

    var currentPath = route.getKey($location.path());
    if(currentPath !== "/products") {
        var query = elasticsearch.setFilterQuery();
        var aggsMap = elasticsearch.getAggregateMap();
        query = elasticsearch.setFilterAggs(query, elasticsearch.getAggregateQuery());
        var params = {
            "exclude_products": true
        };
        elasticsearch.ymmsearch(query).then(function() {
            utils.setAggregationsDisplay($scope.es_data.aggregations, aggsMap, $routeParams);
        });
    }

    $scope.filterDisplay = function(label) {
        // Based on the users settings, display casing
        var cache = storeCache.get();
        if(cache.settings.filter_text_case) {
            switch(cache.settings.filter_text_case) {
                case "upper_case":
                    return label.toString().toUpperCase();
                case "lower_case":
                    return label.toString().toLowerCase();
                case "capitalise":
                    return utils.capitalise(label);
            }
        }

        // default is capitalise
        return utils.capitalise(label);
    };

    /**
     * Show hide filter
     * @param filter
     */
    $scope.toggleFilter = function(filter) {
        angular.forEach($scope.es_data.aggregations, function(item) {
            if(item.key === filter) {
                item.open = (item.open)? false: true;
            }
        });
    };

    /**
     * Take the existing routeParams, add in new filter, append timestamp
     * Route to the product search page
     */
    $scope.applyFilter = function(filter_key, filter_value) {
        var params = {};
        if(filter_key && filter_value) {
            var aggs = elasticsearch.getAggregateMap();
            params = $routeParams;
            params[aggs[filter_key].field] = filter_value;
        }

        // pass through existing params
        if($routeParams.template) {
            params['template'] = $routeParams.template;
        }
        if($routeParams.limit) {
            params['limit'] = $routeParams.limit;
        }
        if($routeParams.sort_by) {
            params['sort_by'] = $routeParams.sort_by;
        }
        if($routeParams.sort_order) {
            params['sort_order'] = $routeParams.sort_order;
        }

        utils.changeRoute(route.get("/products"), params, true);
    };

}
angular.module('myApp')
    .controller('AutomotiveFilterCtrl',
        ['$scope', '$routeParams', 'elasticsearch', 'storeCache', 'utils', 'route', 'stock2shop', 'config', '$location', AutomotiveFilterCtrl]);;'use strict';

function AutomotiveProductsSearchCtrl(
    $scope, $location, $routeParams, price, utils, config,
    cart, cache, analytics, elasticsearch, route, qty) {

    $scope.data = {};

    analytics.trackPage($location.url(), 'Product List');
    $scope.pageReady = false;
    $scope.search = $routeParams.search || '';
    $scope.system_products = [];
    $scope.pagination = config.pagination;
    $scope.pagination.page = $routeParams.page || 1;
    $scope.pagination.limit = $routeParams.limit || 10;
    $scope.sort_by = $routeParams.sort_by || "_score";
    $scope.sort_order = $routeParams.sort_order || "desc";
    $scope.path = $location.path();
    $scope.hideOptionsOnListView = false;

    // list, grid, or both
    $scope.allowed_product_display_type = getDisplayType();

    // List of templates allowed
    $scope.templates = getAllowedDisplayTemplates();

    // default is grid
    $scope.template = getSelectedTemplate($scope.allowed_product_display_type);

    // default template for channel, if set
    if(cache.settings.product_template) {
        $scope.template = getSelectedTemplate(cache.settings.product_template);
    }

    // tax description
    if(cache.settings.price_display && cache.settings.price_display === "inclusive") {
        $scope.tax_description_incl = 'incl '
    } else {
        $scope.tax_description_incl = 'excl '
    }
    $scope.tax_description = cache.settings.tax_description || "tax";

    // User activated template
    if($routeParams.template) {
        $scope.template = getSelectedTemplate($routeParams.template);
    }

    // Hide options on list view
    if (
        cache.settings.hide_options_on_list_view &&
        cache.settings.hide_options_on_list_view.toLowerCase() === "true"
    ) {
        $scope.hideOptionsOnListView = true;
    }

    // Display product info if set
    var productInfo = {};
    if (cache.settings.product_info_display) {
        productInfo = JSON.parse(cache.settings.product_info_display);
    } else {
        productInfo = {
            "Brand": "vendor",
            "Category": "collection",
            "Type": "product_type"
        };
    }

    search();
    // constructor ends here....................................................

    function search() {
        // Build elastic search query
        var aggs = elasticsearch.getAggregateQuery();
        var aggsMap = elasticsearch.getAggregateMap();
        var productAggs = {};
        angular.forEach(aggs, function(value, key) {
            productAggs[key] = value;
        });

        // Build queries
        var payload = {};
        var query = elasticsearch.setFilterAggs({}, productAggs);
        angular.forEach($routeParams, function(value, key) {
            if(key === 'year' || key === 'make' || key === 'model') {
                payload[key] = value;
            } else {
                query = elasticsearch.setFilterTerm(query, key, value);
            }
        });
        if($routeParams['q']) {
            query = elasticsearch.setFilterQueryString(
                query,
                $routeParams['q']
            );
        }

        // product given
        if($routeParams['id']) {
            query = elasticsearch.setFilterID(query, $routeParams['id']);
            query = elasticsearch.setFilterSize(query, 1);
            query = elasticsearch.setFilterFrom(query, 0);
        } else {
            // Each route param gets set as a term filter on the query
            angular.forEach($routeParams, function(value, key) {
                if(key !== 'year' && key !== 'make' && key !== 'model') {
                    query = elasticsearch.setFilterTerm(query, key, value);
                }
            });

            // paging
            query = elasticsearch.setFilterSize(query, $scope.pagination.limit);
            query = elasticsearch.setFilterFrom(query, ($scope.pagination.page - 1) * $scope.pagination.limit);
        }

        // sort results
        query = elasticsearch.setFilterSort(query, $scope.sort_by, $scope.sort_order);

        // Do search
        elasticsearch.ymmsearch(query, payload).then(function() {
            $scope.system_products = $scope.es_data.system_products;
            $scope.pagination.total = $scope.es_data.hits.total;
            $scope.pagination.displaying = $scope.pagination.page * $scope.pagination.limit;
            if ($scope.es_data.hits.total < $scope.pagination.displaying) {
                $scope.pagination.displaying = $scope.es_data.hits.total;
            }
            utils.setAggregationsDisplay($scope.es_data.aggregations, aggsMap, $routeParams);

            angular.forEach($scope.system_products, function (product) {
                // price display
                var low = false;
                var high = false;
                var total_qty = 0;
                product.qty_in_cart = 0;
                angular.forEach(product.variants, function (variant) {
                    variant.display_price = utils.money(price.get(variant, product));
                    variant.display_tax_rate = price.getTaxRate(variant, product)
                    variant.display_tax = utils.money(price.getTax(variant.price, variant.display_tax_rate));
                    if (low > price.get(variant, product) || !low) {
                        low = price.get(variant, product);
                    }
                    if (high < price.get(variant, product) || !high) {
                        high = price.get(variant, product);
                    }
                    total_qty += variant.qty;

                    var variantInCart = cart.getVariant(variant.id);
                    if (variantInCart) {
                        product.qty_in_cart += parseInt(variantInCart.qty);
                        variant.qty_in_cart = parseInt(variantInCart.qty);
                    } else {
                        variant.qty_in_cart = 0;
                    }
                    variant.qty_to_add = qty.getDefaultItemQty(variant, product);
                    variant.multiples_of = qty.getMultiplesOf(variant, product);

                    variant.isSaleable = qty.isSaleable(variant, product, variant.qty_to_add);

                    // display options for selection
                    variant.display_option = variant.sku;
                    angular.forEach(product.options, function(option) {
                        variant.display_option +=  " - " + variant["option" + option.position];
                    });
                    // variant.display_option +=  " (" + variant.display_price + ")";

                });
                product.price_variance = (low !== high);
                product.min_price = utils.money(low);
                product.max_price = utils.money(high);
                product.total_qty = total_qty;
                product.availability = utils.stockLevels(total_qty);
                product.image_map = utils.createMap(product.images, 'id');

                //select first variant if only one variant
                if(product.variants.length === 1) {
                    product.selected_variant = product.variants[0];
                }

                // product info display
                product.info_display = {};
                var meta = utils.createMap(product.meta, 'key');
                angular.forEach(productInfo, function(value, key) {
                    if(value.indexOf("meta_") === 0) {
                        value = value.replace("meta_", "")
                        if(meta[value]) {
                            product.info_display[key] = meta[value].value;
                        } else {
                            product.info_display[key] = "n/a";
                        }

                    } else {
                        product.info_display[key] = product[value];
                    }
                });


                // Units available display
                if (
                    cache.settings.show_availability_units &&
                    cache.settings.show_availability_units.toLowerCase() === "true"
                ) {
                    $scope.data.showAvailabilityUnits = true;
                }
            });

            utils.focus('search');
            $scope.pageReady = true;
        });
    }

    $scope.objectKeys = function(obj){
        return Object.keys(obj);
    };

    $scope.isSaleable = function(product, variant, amount) {
        return qty.isSaleable(variant, product, amount);
    };

    $scope.inStockVariant = function (variant) {
        if(!variant) {
            return false;
        }
        if(variant.inventory_management === false) {
            return true;
        }
        return (variant.qty > 0);
    };

    $scope.overOrderEnabled = function(meta) {
        meta = meta || [];

        // Check if enabled on the product
        var meta_map = utils.createMap(meta, 'key');
        if (meta_map && meta_map.over_order_enabled) {
            return meta_map.over_order_enabled.value.toLowerCase() === 'true';
        }

        // Check if enabled on the channel
        return cache.settings["over_order_enabled"] &&
            cache.settings["over_order_enabled"].toLowerCase() === 'true';
    };

    $scope.hideAvailability = function () {
        return cache.settings.hide_availability_enabled &&
            cache.settings.hide_availability_enabled.toLowerCase() === 'true';
    };

    $scope.setTemplate = function(userTemplate) {
        $scope.template = userTemplate;
        $scope.pageChanged();
    };

    $scope.setSortOrder = function(userSort) {
        $scope.sort_order = userSort;
        $scope.pageChanged();
    };

    $scope.view = function (product_id) {
        $scope.pageReady = false;
        $location.path(route.get("/product") + '/' + product_id);
    };

    $scope.pageChanged = function() {
        var params = $routeParams;
        params["page"] = $scope.pagination.page;
        params["limit"] = $scope.pagination.limit;
        params["sort_by"] = $scope.sort_by;
        params["sort_order"] = $scope.sort_order;
        params["template"] = $scope.template;
        utils.changeRoute(route.get("/products"), params, true);
    };

    $scope.addToCart = function(product, variant) {
        var cartItem = cart.addItem(variant, product, variant.qty_to_add);
        if (cartItem.added) {
            $.bigBox({
                title: variant.qty_to_add + " x " + product.title,
                content: "<strong>Item(s) added to your cart</strong> <p>Checkout by clicking the cart icon on the top</p>",
                color: "#739E73",
                icon: "fa fa-check",
                timeout: 4000
            });
            variant.qty_in_cart = parseInt(variant.qty_to_add);

            // Analytics: track product detail
            analytics.addProduct(
                product.id,
                product.title,
                product.collection,
                product.vendor
            );
            analytics.trackDetail();
        } else {
            $.SmartMessageBox({
                title: "Item not added!",
                content: cartItem.error,
                buttons: '[OK]'
            });
        }
    };

    $scope.removeItemFromCart = function (item) {
        $.SmartMessageBox({
            title: "Removing item from cart",
            content: "Are you sure you want to remove " + item.sku + " from your cart?",
            buttons: '[No][Yes]'
        }, function (ButtonPressed) {
            if (ButtonPressed === "Yes") {
                cart.removeItem(item.id);
                $scope.$apply(function () {
                    $scope.order = cart.getOrder();

                    item.qty_in_cart = 0;
                });
            }
        });
    };

    function isValidDisplayType(type) {
        var allowedTypes = ['grid', 'list', 'both'];
        return allowedTypes.indexOf(type.toLowerCase()) !== -1;
    }

    function isValidTemplate(template) {
        var allowedTemplates = ['grid', 'list'];
        return allowedTemplates.indexOf(template.toLowerCase()) !== -1;
    }

    function getDisplayType() {
        var displayType = cache.settings.allowed_product_display_type || 'both';
        if (!isValidDisplayType(displayType)) {
            displayType = "both";
        }
        return displayType.toLowerCase();
    }

    function getAllowedDisplayTemplates() {
        var templates = {};
        if ($scope.allowed_product_display_type === 'both') {
            templates = {
                "grid": {
                    url: "partials/client/product-grid-wide.html?ts=" + new Date().getTime()
                },
                "list": {
                    url: "partials/client/product-list.html?ts=" + new Date().getTime()
                }
            };
        } else {
            var template_name = ($scope.allowed_product_display_type === 'grid')
                ? 'product-grid-wide' : 'product-list';
            templates[$scope.allowed_product_display_type] = {
                url: "partials/client/" + template_name + ".html?ts=" + new Date().getTime()
            };
        }
        return templates;
    }

    function getSelectedTemplate(template) {

        // If only a single display type is allowed, return that
        if ($scope.allowed_product_display_type !== 'both') {
            return $scope.allowed_product_display_type;
        }

        // If template is not valid, default to "grid"
        if (!isValidTemplate(template)) {
            return 'grid';
        }
        return template;
    }

    // Execute setTemplate function only after it has loaded
    $scope.setTemplate($scope.template);
}

angular.module('myApp')
    .controller('AutomotiveProductsSearchCtrl', ['$scope', '$location', '$routeParams',
        'price', 'utils', 'config', 'cart',
        'cache', 'Analytics', 'elasticsearch', 'route', 'qty', AutomotiveProductsSearchCtrl]);
;'use strict';

/**
 * http://stackoverflow.com/a/19204391/639133
 * The "ng-bind-html-unsafe" directive was removed in Angular v1.2
 * Sometimes we know HTML is safe, so don't worry about XSS.
 */
angular.module('myApp')
    .directive('ngBindHtmlUnsafe', [function() {
        return function(scope, element, attr) {
            element.addClass('ng-binding').data('$binding', attr.ngBindHtmlUnsafe);
            scope.$watch(attr.ngBindHtmlUnsafe, function ngBindHtmlUnsafeWatchAction(value) {
                element.html(value || '');
            });
        }
    }]);;'use strict';

/**
 * https://code.angularjs.org/1.1.5/docs/api/ng.$compile
 */
angular.module('myApp')
    .directive('compile', ['$compile' ,function($compile) {
        // directive factory creates a link function
        return function(scope, element, attrs) {
            scope.$watch(
                function(scope) {
                    // watch the 'compile' expression for changes
                    return scope.$eval(attrs.compile);
                },
                function(value) {
                    // when the 'compile' expression changes
                    // assign it into the current DOM
                    element.html(value);

                    // compile the new DOM and link it to the current
                    // scope.
                    // NOTE: we only compile .childNodes so that
                    // we don't get into infinite loop compiling ourselves
                    $compile(element.contents())(scope);
                }
            );
        };
    }]);;angular.module('myApp').directive('focusInvalid', function () {
    return {
        link: function (scope, elem) {
            // set up event handler on the form element
            elem.on('submit', function () {

                // find the first invalid element
                var firstInvalid = angular.element(elem[0].querySelector('.ng-invalid'));

                // if we find one, set focus
                if (firstInvalid) {
                    // Remove the ng-pristine class so the validation styling will kick in.
                    firstInvalid.removeClass("ng-pristine");
                    firstInvalid.addClass("ng-dirty");
                    firstInvalid.$dirty = true;

                    if(firstInvalid[0]) {
                        firstInvalid[0].focus();
                    }
                }
            });
        }
    };
});;angular.module('myApp').directive('imageLoader', function () {
    return {
        restrict: 'A',

        // Bind the DOM events to the scope
        link: function (scope, element, attrs) {

            // Check when change scope occurs
            scope.$watch(function () {
                if(!element[0].complete) {
                    element.siblings().show();
                    element.hide();
                }
            });

            // When new image is loaded
            element.bind('load', function () {
                element.siblings().hide();
                element.show();
            });
        }
    };
});;'use strict';

angular.module('myApp')
    .directive('loading', ['$http' , function($http) {
        return {
            restrict: 'A',
            link: function(scope, elm, attrs) {
                var option = attrs.loading;
                var isLoading = function() { return $http.pendingRequests.length > 0; };
                scope.$watch(
                    isLoading,
                    function(loading) {
                        if (loading) {
                            if(option=="show") {
                                elm.show();
                            } else {
                                elm.hide();
                            }

                        } else {
                            if(option=="show") {
                                elm.hide();
                            } else {
                                elm.show();
                            }

                        }
                    }
                );
            }
        };
    }]);;'use strict';

/* Filters */

angular.module('myApp.filters', [])

    .filter('boolean', function() {
        return function(input) {
            return (input) ? 'yes' : 'no';
        }
    });


;'use strict';

/*
 * TODO Angular $cookies do not allow the creation of persistent cookie!
 * See comments here:
 * http://docs.angularjs.org/api/ngCookies.$cookies
 * http://docs.angularjs.org/api/ngCookies.$cookieStore
 *
 * Just use jQuery Cookies plugin for now
 */

function auth(config, stock2shop) {
    var cookieName = "credentials-b2b";

    var credentialsWatchers = [];
    this.addCredentialsWatcher = function(callback) {
        credentialsWatchers.push(callback);
    };

    /**
     * The auth interceptor will
     * redirect to login if validToken returns 401.
     */
    this.validToken = function() {
        stock2shop.users.validToken();
    };

    /**
     * Create credentials object
     * @param system_user
     * @param version
     * @returns {{email: string, name: string, surname: string, token: string, username: string}}
     */
    this.createCredentials = function(system_user, version) {
        return {
            version: version,
            email: system_user.email,
            name: system_user.name,
            surname: system_user.surname,
            token: system_user.token,
            username: system_user.username,
            client_id: system_user.client_id,
            client_name: system_user.client_name,
            channels: system_user.channels,
            sources: system_user.sources,
            fulfillment_services: system_user.fulfillment_services,
            customer: system_user.customers[0]
        };
    };

    this.removeCookie = function() {
        console.info("Removing auth cookie: " + cookieName);
        $.removeCookie(cookieName, {path: "/"})
    };

    this.setCookie = function(credentials) {
        // Remove customer meta, it might be a lot of data.
        // Throwing too much JSON data at the cookie will corrupt it by
        // exceeding max length and users will not be able to login.
        // TODO we should only store version and token in cookie.
        if (credentials["customer"]) {
            if(credentials["customer"]["meta"]) {
                delete credentials["customer"]["meta"];
            }
            if(credentials["customer"]["addresses"]) {
                delete credentials["customer"]["addresses"];
            }
            if(credentials["customer"]["segments"]) {
                delete credentials["customer"]["segments"];
            }
            if(credentials["customer"]["user"] && credentials["customer"]["user"]["segments"]) {
                delete credentials["customer"]["user"]["segments"];
            }
        }
        $.cookie(cookieName, JSON.stringify(credentials), {expires: 365, path: "/"});
    };

    this.setCredentials = function(credentials) {
        config.credentials = credentials;
        angular.forEach(credentialsWatchers, function(credentialsWatcher) {
            credentialsWatcher(config.credentials);
        });
    };

    this.getCredentials = function() {
        // Try to load credentials from cookie.
        var cookie = $.cookie(cookieName);
        var credentials = undefined;
        if (cookie) {
            try {
                eval("credentials = " + cookie);
            } catch (e) {
            }
        }

        return credentials;
    };

    // Preventing redirecting or reloading twice.
    // This can happen because the interceptor might be called for multiple requests.
    // Cancelling requests would be elegant but adds a lot of complication.
    var redirecting = false;
    var reloading = false;

    this.redirectAfterLogin = function() {
        if (!redirecting) {
            var domain = config.domain.replace("/login.php", "");
            console.info("Sign in complete, redirecting to " + domain);
            window.open(domain + "/#", "_self");
            redirecting = true;
        }
    };

    this.redirectToLogin = function() {
        if (!redirecting) {
            console.info("Redirecting to login page...");
            window.open(config.domain + "/login", "_self");
            redirecting = true;
        }
    };

    this.redirectToMaintenance = function() {
        if (!redirecting) {
            console.info("Redirecting to maintenance page...");
            window.open(config.maintenanceDomain + "?maintenance=true", "_self");
            redirecting = true;
        }
    };

    this.reloadPage = function() {
        if (!reloading) {
            console.info("New version, reloading page...");
            window.location.reload();
            reloading = true;
        }
    };
}

angular.module('myApp')
    .service("auth", ["config", "stock2shop", auth]);
;// .............................................................................
// We use an interceptor to redirect to the login page.
// Don't put error handling logic in here if we might want to override it.
// The interceptor is always called regardless of whether
// we passed an error handler into the stock2shop service.

angular.module('myApp').factory("authInterceptor", ["$q", "config", "$injector",
    function($q, config, $injector) {
        return {
            response: function(result) {
                $injector.invoke(["auth", function(auth) {
                    var headers = result.headers();
                    var version = headers["x-b2b-version"];
                    var reloadPage = Boolean(config.credentials.version != undefined);
                    if (version && version != config.credentials.version) {
                        // Warning, don't use an alert here. It breaks stuff.
                        config.credentials.version = version;
                        auth.setCookie(config.credentials);
                        auth.setCredentials(config.credentials);
                        if (reloadPage) {
                            // Not really sure why we have to wrap this in an if-statement.
                            // Fixes a bug in IE where it redirects back to the login page after successful login.
                            auth.reloadPage();
                        }
                    }
                }]);

                return result;
            },
            responseError: function(result) {
                // We can optionally set a request header "ManualError".
                // If set the interceptor should not do any error handling.
                if (!Boolean(result.config.headers.ManualError)) {

                    if (result.status == 401) {
                        $injector.invoke(["auth", function(auth) {
                            auth.redirectToLogin();
                        }]);

                    } else if (result.status == 503) {
                        // 503 Service unavailable
                        $injector.invoke(["auth", function(auth) {
                            auth.redirectToMaintenance();
                        }]);
                    }
                }
                return $q.reject(result);
            }
        };
    }
]);

angular.module('myApp').config(function($httpProvider) {
    $httpProvider.interceptors.push('authInterceptor');
});;'use strict';

/**
 * http://stackoverflow.com/questions/19744462/update-scope-value-when-service-data-is-changed
 *
 */
function elasticsearch(stock2shop, config, utils, storeCache) {

    // The data shared between main.js controller, see stack overflow link above
    this.data = {
        "aggregations": [],
        "system_products": [],
        "phrase_prefix": false,
        "phrase_prefix_fields": ['variants.sku^3', 'tags.autocomplete']
    };

    // Aggregate query used in search
    var aggregateQuery = {};

    // Aggregation map
    var aggregateMap = {};

    // Field key map for aggregations
    var aggregateKeyMap = {};


    /**
     * Sets the aggregates query and terms to be used with each search
     * @param aggs
     */
    this.setAggregates = function (aggs) {
        aggregateMap = setAggregateQueryMap(aggs);
        aggregateQuery = setAggregateQuery(aggs);
        aggregateKeyMap = utils.createMap(aggregateMap, 'field');
    };

    /**
     * Sets the 'Search By SKU' search feature as active or inactive
     *
     * @param status
     */
    this.setSearchBySku = function(status) {
        this.data.phrase_prefix = status;
    };

    /**
     * Gets the aggregate query
     * @returns {{}}
     */
    this.getAggregateMap = function () {
        return aggregateMap;
    };

    /**
     * Gets the aggregate query
     * @returns {{}}
     */
    this.getAggregateQuery = function () {
        return aggregateQuery;
    };

    /**
     * Sets the filtered query
     * @returns {{}}
     */
    this.setFilterAggs = function (query, aggs) {
        var query = angular.extend(getFilterQuery(), query);
        query.aggs = aggs;
        return query;
    };

    /**
     * Sets the filtered query
     * @returns {{}}
     */
    this.setFilterQuery = function (query) {
        query = query || {};
        return angular.extend(getFilterQuery(), query);
    };

    /**
     * Sets the filtered query
     * @returns {{}}
     */
    this.setFilterSize = function (query, size) {
        query = query || {};
        query = angular.extend(getFilterQuery(), query);
        query.size = size;
        return query;
    };

    /**
     * Sets the filtered query
     * @returns {{}}
     */
    this.setFilterFrom = function (query, from) {
        query = query || {};
        query = angular.extend(getFilterQuery(), query);
        query.from = from;
        return query;
    };

    /**
     * Sets sort order for filter
     * @param query
     * @param sort
     * @param order
     * @returns {*}
     */
    this.setFilterSort = function (query, sort, order) {
        order = order || "desc";
        sort = sort || "_score";
        query = query || {};
        query = angular.extend(getFilterQuery(), query);
        query.sort = {};
        query.sort[sort] = {
            "order": order
        };
        return query;
    };

    this.setFilterID = function(query, id) {
        var filter = {};
        filter.term = {};
        filter.term["id"] = id;
        query.query.bool.filter.push(filter);
        return query;
    };

    /**
     * Sets a term for the query
     * Terms are only added if they have a corresponding agg
     * This is how the faceted navigation works
     * @returns {{}}
     */
    this.setFilterTerm = function (query, key, value) {
        query = query || {};
        query = angular.extend(getFilterQuery(), query);
        var filter = {};
        if (aggregateKeyMap[key]) {
            if (aggregateKeyMap[key].nested) {

                var nestedFilter ={
                    "nested": {
                        "path": "meta",
                        "query": {
                            "bool": {
                                "filter":[]
                            }
                        }
                    }
                };

                // nested aggregations
                // see search/queries/esSearchNestedFiltered.json
                // See https://github.com/stock2shop/app/issues/1495 for ref to changes
                // Set meta.key term
                var nestedFilterKey = {
                            "term": {
                                "meta.key": key
                            }
                        };
                nestedFilter.nested.query.bool.filter.push(nestedFilterKey);

                // Set meta.value term
                var nestedFilterValue = {
                    "term": {
                        "meta.value": value
                    }
                };
                nestedFilter.nested.query.bool.filter.push(nestedFilterValue);

                // Push nested to filter
                query.query.bool.filter.push(nestedFilter);
            } else {

                // normal aggregation
                filter.term = {};
                filter.term[aggregateKeyMap[key].field] = value;
                query.query.bool.filter.push(filter);
            }
        }
        return query;
    };

    this.setTerm = function(query, key, value) {
        query = angular.extend(getFilterQuery(), query);
        var filter = {};
        filter.term = {};
        filter.term[key] = value;
        query.query.bool.filter.push(filter);
        return query;
    };

    /**
     * Sets the filtered query
     * @returns {{}}
     */
    this.setFilterQueryString = function (query, q) {
        query = query || {};
        query = angular.extend(getFilterQuery(), query);
        var must = {
            "multi_match": {
                "fields": [
                    "variants.sku",
                    "variants.barcode",
                    "title",
                    "source_product_code",
                    "variants.source_variant_code",
                    "body_html",
                    "collection.english",
                    "product_type.english",
                    "meta.value",
                    "vendor.english"
                ],
                "query": q.toLowerCase(),
                "operator": "or"
            }
        };

        // if we are searching for sku (phrase prefix) we are only allowed to search
        // appropriate fields
        if(this.data.phrase_prefix) {
            must.multi_match.type = 'phrase_prefix';
            must.multi_match.fields = this.data.phrase_prefix_fields;
        } else {

            // Customise how fields are searched and weighted by client settings
            var settings = storeCache.get().settings;
            if(settings.elastic_query_fields) {
                must.multi_match.fields = settings.elastic_query_fields.split(",");
            }
        }
        query.query.bool.must.push(must);
        return query;
    };

    /**
     * Returns the suggest query
     * @param q
     * @returns {{suggest: {suggestions: {prefix: string, completion: {size: number, field: string, fuzzy: boolean, context: {channel_id: *}}}}}}
     */
    this.setSuggests = function (q, size) {
        size = size || 10;
        var response = {
            "query": {
                "bool": {
                    "must": [
                        {
                            "multi_match": {
                                "query": decodeURIComponent(q.toLowerCase()),
                                "fields": ["variants.sku^3", "title^3", "variants.barcode"],
                                "operator": "or"
                            }
                        }
                    ]
                }
            },
            "size": size,
            "from": 0,
            "_source": [
                "id",
                "title",
                "options",
                "variants.id",
                "variants.sku",
                "variants.option1",
                "variants.option2",
                "variants.option3"
            ],
            "sort": {
                "_score": {
                    "order": "desc"
                }
            }
        };
        if(this.data.phrase_prefix) {
            response.query.bool.must[0].multi_match.type = 'phrase_prefix';
            response.query.bool.must[0].multi_match.fields = this.data.phrase_prefix_fields;
            delete response.query.bool.must[0].multi_match.operator;

        } else {

            // Customise how fields are searched and weighted by client settings
            var settings = storeCache.get().settings;
            if(settings.elastic_suggest_fields) {
                response.query.bool.must[0].multi_match.fields = settings.elastic_suggest_fields.split(",");
            }
        }
        return response;
    };

    /**
     * Get suggestions
     *
     * @param query
     * @returns {*}
     */
    this.suggest = function (query, exclude_products) {
        var exclude = exclude_products !== false;
        return stock2shop.products.elasticSearchProducts(
            {
                body: query,
                exclude_products: exclude,
                channel_id: config.credentials.channels[0].id
            }
        ).then(function (data) {
                return data;
            }
        );
    };

    /**
     * Appends the aggregations and channel ID and processes search
     *
     * @param query
     * @returns {*}
     */
    this.search = function (query, params) {
        var self = this;
        var payload = params || {};
        payload.channel_id = config.credentials.channels[0].id;
        payload.body = query;
        return stock2shop.products.elasticSearchProducts(payload).then(function (result) {
            self.data.took = result.took;
            self.data.took_s2s = result.took_s2s;
            if(result.aggregations) {
                self.data.aggregations = utils.sortAggregations(aggregateMap, result.aggregations);
            }
            self.data.system_products = result.system_products;
            self.data.hits = result.hits;

            console.info("--------------------------------");
            console.info("search times: es", result.took);
            console.info("search times: s2s", result.took_s2s);
        });
    };

    /**
     * Appends the aggregations and channel ID and processes search
     *
     * @param query
     * @param params
     * @returns {*}
     */
    this.ymmsearch = function (query, params) {
        angular.forEach(aggregateMap, function(value, key) {
            if(value.field === 'year' || value.field === 'make' || value.field === 'model') {
                delete query.aggs[value.label];
            }
        });
        var self = this;
        var payload = params || {};
        payload.channel_id = config.credentials.channels[0].id;
        payload.body = query;
        return stock2shop.ymm.elasticSearchProducts(payload).then(function (result) {
            self.data.took = result.took;
            self.data.took_s2s = result.took_s2s;
            self.data.aggregations = utils.sortAggregations(aggregateMap, result.aggregations);
            self.data.system_products = result.system_products;
            self.data.hits = result.hits;

            console.info("--------------------------------");
            console.info("search times: es", result.took);
            console.info("search times: s2s", result.took_ymm);
        });
    };

    /**
     * The aggregate query is slightly different to the query in the channel meta
     * Channel meta query has extra properties for managing display.
     * Remove extra properties
     * @param aggs
     * @returns {{}}
     */
    var setAggregateQuery = function(aggs) {
        var query = {};
        angular.forEach(aggs, function (item, key) {
            query[key] = {};
            angular.copy(item, query[key]);
            delete query[key].requires;
            delete query[key].separator;
            delete query[key].hide_count;

            // for nested
            if(query[key].nested) {
                delete query[key].aggs[key].aggs[key].requires;
                delete query[key].aggs[key].aggs[key].separator;
                delete query[key].aggs[key].aggs[key].hide_count;
            }
        });
        return query;
    };

    /**
     * Maps the aggregated query to make building faceted navigation easier
     * This function flattens the nested and non nested agg query into a common format
     * @returns {}
     */
    var setAggregateQueryMap = function (aggs) {
        var map = {};
        var i =0;
        angular.forEach(aggs, function (item, key) {
            if (item.terms) {
                map[key] = {
                    "label": key,
                    "nested": false,
                    "key": false,
                    "value": false,
                    "path": false,
                    "requires": item.requires || false,
                    "separator": item.separator || false,
                    "field": item.terms.field,
                    "hide_count": item.hide_count || false
                };
            } else if (item.nested) {
                var filter_key = false;
                for (var property in item.aggs) {
                    filter_key = property;
                }

                // get the field value
                // var field = utils.searchProperty(item.aggs[filter_key], 'meta.key');
                var field = item.aggs[filter_key].filter.term['meta.key'];
                var requires = utils.searchProperty(item.aggs[filter_key], 'requires');
                var separator = utils.searchProperty(item.aggs[filter_key], 'separator');
                var hide_count = utils.searchProperty(item.aggs[filter_key], 'hide_count');
                map[key] = {
                    "label": key,
                    "nested": true,
                    "key": filter_key,
                    "value": field,
                    "path": item.nested.path,
                    "requires": requires || false,
                    "separator": separator || false,
                    "field": field,
                    "hide_count": hide_count
                };
            }
            map[key].sort_order = i;
            i++;
        });
        return map;
    };

    var getFilterQuery = function () {
        return {
            "query": {
                "bool": {
                    "must": [],
                    "filter": []
                }
            }
        };
    }

}

angular.module('myApp')
    .service("elasticsearch", ["stock2shop", "config", "utils", "storeCache", elasticsearch]);;
'use strict';

/**
 * Class to handle local storage
 * Local storage is config that is not saved on the server (e.g. customer meta).
 * This is for config such as layout view, number of products to list etc..
 *
 *
 */
function storage(config) {
    var self = this;

    var storagePrefix = "config";
    var storageKeys = [
        "limit",
        "sort_by",
        "sort_order",
        "template"
    ];

    function getName() {
        return storagePrefix + "-" + config.credentials.customer.id;
    }

    this.getStorage = function() {
        var configStorageName = getName();
        return JSON.parse(localStorage.getItem(configStorageName));
    };

    this.removeStorage = function () {
        var configStorageName = getName();
        localStorage.removeItem(configStorageName);
    };

    this.setStorage = function (data) {
        var configStorageName = getName();
        var allowedStorage = {};
        angular.forEach(storageKeys, function(key) {
            if(data[key]) {
                allowedStorage[key] = data[key];
            }
        });
        localStorage.setItem(configStorageName, JSON.stringify(allowedStorage));
    };


}

angular.module('myApp')
    .service("storage", ["config", storage]);;'use strict';

/**
 * Returns price depending on store settings
 *
 */
function price(storeCache, utils) {

    /**
     *
     * @param variant
     * @param product
     * @returns {number}
     */
    this.get = function (variant, product) {
        var settings = storeCache.get().settings;

        // default is excluding tax
        var includingTax = settings.price_inclusive || 'false';

        // default is exclusive
        var displayTax = settings.price_display || false;

        if (includingTax === 'true') {
            if (displayTax === 'inclusive') {
                return utils.round2Decimal(variant.price);
            } else {
                var tax_rate = this.getTaxRate(variant, product);
                return this.getExTax(variant.price, tax_rate);
            }
        } else {
            if (displayTax === 'inclusive') {
                var tax_rate = this.getTaxRate(variant, product);
                return this.getInclTax(variant.price, tax_rate);
            } else {
                return utils.round2Decimal(variant.price);
            }
        }
    };

    /**
     *
     * @param price
     * @param rate
     * @returns {number}
     */
    this.getExTax = function (price, rate) {
        var settings = storeCache.get().settings;
        var includingTax = settings.price_inclusive || 'false';
        if (includingTax === 'true') {
            var tax_multiplier = ((100 + rate) / 100);
            var tax = (price - (price / tax_multiplier));
            return utils.round2Decimal(price-tax);
        } else {
            return utils.round2Decimal(price);
        }
    };

    /**
     *
     * @param price
     * @param rate
     * @returns {number}
     */
    this.getInclTax = function (price, rate) {
        var settings = storeCache.get().settings;
        var includingTax = settings.price_inclusive || 'false';
        if (includingTax !== 'true') {
            var tax_multiplier = ((100 + rate) / 100);
            return utils.round2Decimal(price * tax_multiplier);
        } else {
            return utils.round2Decimal(price);
        }
    };

    /**
     *
     * @param price
     * @param rate
     * @returns {number}
     */
    this.getTax = function (price, rate) {
        var settings = storeCache.get().settings;
        var includingTax = settings.price_inclusive || 'false';
        var tax_multiplier = ((100 + rate) / 100);
        if (includingTax === 'true') {
            var tax = (price - (price / tax_multiplier));
            return utils.round2Decimal(tax);
        } else {
            var total = (price * tax_multiplier);
            return utils.round2Decimal(total - price);
        }
    };

    /**
     * Rate is calculated based on following hierarchy
     *
     * 1. variant.tax_rate
     * TODO we do not support this yet in app
     *
     * 2. product.meta.tax_rate
     * a product meta called tax_rate
     *
     * 3. settings.tax_rate
     * tax rate set for entire store or customer
     *
     * @param variant
     * @param product
     * @returns {number}
     */
    this.getTaxRate = function(variant, product) {

        // 1. TODO Tax rate on variant meta

        // 2. Tax rate on product meta
        if(product && product.meta) {
            for(var i = 0; i < product.meta.length; i++) {
                if(product.meta[i].key === "tax_rate") {
                    return parseFloat(product.meta[i].value);
                }
            }
        }

        // 3. Tax rate from settings
        var settings = storeCache.get().settings;
        if (settings.tax_rate && settings.tax_rate > 0) {
            return parseFloat(settings.tax_rate);
        } else {
            return 0;
        }
    }

}

angular.module('myApp')
    .service("price", ["storeCache", "utils", price]);;'use strict';

/**
 * Manages qty
 *
 */
function qty(storeCache, utils) {

    /**
     *
     * The increment the variant can be ordered in.
     * Hierarchy is:
     *
     * 1. settings.customer.meta.qty_multiples_of
     * Specific for customer
     *
     * 2. variant.meta.qty_multiples_of
     * TODO we do not support this yet in app
     *
     * 3. product.meta.qty_multiples_of
     * a product meta called qty_multiples_of
     *
     * 4. settings.qty_multiples_of
     * For entire store
     *
     * @param variant
     * @param product
     * @returns {number}
     */
    this.getMultiplesOf = function (variant, product) {
        var cache = storeCache.get();
        if(cache.customer.meta.qty_multiples_of) {
            var value = parseInt(cache.customer.meta.qty_multiples_of);
            if(value >= 1) {
                return value;
            }

        }
        if(product && product.meta) {
             var map = utils.createMap(product.meta, 'key');
             if(map['qty_multiples_of']) {
                 var value = parseInt(map['qty_multiples_of'].value);
                 if(value >= 1) {
                     return value;
                 }
             }
        }
        if (cache.settings.qty_multiples_of) {
            var value = parseInt(cache.settings.qty_multiples_of);
            if(value >= 1) {
                return value;
            }
        }
        return 1;
    };

    /**
     *
     * The minimum order qty for the variant .
     * Hierarchy is:
     *
     * 1. settings.customer.meta.minimum_order_qty
     * Specific for customer
     *
     * 2. variant.meta.minimum_order_qty
     * TODO we do not support this yet in app
     *
     * 3. product.meta.minimum_order_qty
     * a product meta called qty_multiples_of
     *
     * 4. settings.minimum_order_qty
     * For entire store
     * @param variant
     * @param product
     * @returns {number}
     */
    this.getMinimumOrderQty = function(variant, product) {
        var cache = storeCache.get();
        if(cache.customer.meta.minimum_order_qty) {
            var value = parseInt(cache.customer.meta.minimum_order_qty);
            if(value >= 1) {
                return value;
            }
        }
        if(product && product.meta) {
            var map = utils.createMap(product.meta, 'key');
            if(map['minimum_order_qty']) {
                var value = parseInt(map['minimum_order_qty'].value);
                if(value >= 1) {
                    return value;
                }
            }
        }
        if (cache.settings.minimum_order_qty) {
            var value = parseInt(cache.settings.minimum_order_qty);
            if(value >= 1) {
                return value;
            }
        }
        return 1;
    };

    /**
     * Calculate the minimum qty to be ordered for an item
     * @param variant
     * @param product
     * @returns {number}
     */
    this.getDefaultItemQty = function (variant, product) {
        var minOrderQty = this.getMinimumOrderQty(variant, product);
        var multiplesOf = this.getMultiplesOf(variant, product);
        var defaultItemQty = minOrderQty / multiplesOf;
        if (minOrderQty % multiplesOf !== 0) {
            defaultItemQty++;
        }
        defaultItemQty = parseInt(defaultItemQty) * multiplesOf;
        return defaultItemQty;
    };

    /**
     *
     * @param variant
     * @param product
     * @param amount_required
     * @returns {*}
     */
    this.validate = function(variant, product, amount_required) {

        // amount must positive integer
        if (isNaN(amount_required) || amount_required <= 0) {
            return {
                valid: false,
                error: "The value you have entered for qty is not valid"
            }
        }

        // "multiples of"
        if(!this.validMulitpleOf(variant, product, amount_required)) {
            return {
                valid: false,
                error: variant.sku + " must be ordered in multiples of " +
                    this.getMultiplesOf(variant, product) + " you have selected " + amount_required
            }
        }

        // amount correct
        if(!this.validAmount(variant, product, amount_required)) {
            return {
                valid: false,
                error: "Insufficient stock for sku " + variant.sku
            }
        }

        // minimum order qty
        if(!this.validMinimumOrder(variant, product, amount_required)) {
            return {
                valid: false,
                error: variant.sku + " has minimum order qty of " + this.getMinimumOrderQty(variant, product)
            }
        }

        // no error all good
        return {
            valid: true,
            error: ""
        };
    };

    /**
     *
     * @param variant
     * @param product
     * @param amount
     * @returns {boolean}
     */
    this.validMulitpleOf = function(variant, product, amount) {
        var divisor = this.getMultiplesOf(variant, product);
        return ((parseInt(amount) % divisor) === 0);
    };

    /**
     *
     * @param variant
     * @param product
     * @param amount
     * @returns {boolean}
     */
    this.validMinimumOrder = function(variant, product, amount) {
        var minimum = this.getMinimumOrderQty(variant, product);
        return (parseInt(amount) >= minimum);
    };


    /**
     *
     * @param variant
     * @param product
     * @param amount
     * @returns {boolean}
     */
    this.validAmount = function(variant, product, amount) {
        if (!this.canOverOrder(product.meta || [])) {
            if (amount > variant.qty && variant.inventory_management) {
                return false;
            }
        }
        return true;
    };

    this.canOverOrder = function (meta) {
        meta = meta || [];

        // Check if enabled on the product
        var meta_map = utils.createMap(meta, 'key');
        if (meta_map && meta_map.over_order_enabled) {
            return meta_map.over_order_enabled.value.toLowerCase() === 'true';
        }

        // Check if enabled on the channel
        var cache = storeCache.get();
        if (cache.settings["over_order_enabled"] &&
            cache.settings["over_order_enabled"].toLowerCase() === 'true') {
            return true;
        }
        return false;
    };

    this.isSaleable = function (variant, product, amount_required) {
        if (amount_required > 0) {
            // amount correct
            if (!this.validAmount(variant, product, amount_required)) {
                return false;
            }

            if (!this.canOverOrder(product.meta || []) && variant.inventory_management) {

                // multiples of - show nothing if available stock is less than qty_multiples_of
                if (variant.qty < this.validMulitpleOf(variant, product, amount_required)) {
                    return false;
                }

                // minimum order qty - show nothing if available stock is less than minimum_order_qty
                if (variant.qty < this.getMinimumOrderQty(variant, product)) {
                    return false;
                }
            }
        }
        return true;
    };

}

angular.module('myApp')
    .service("qty", ["storeCache", "utils", qty]);
;'use strict';

/**
 * Used for handling different routes based on industry
 *
 * @param storeCache
 */
function route(storeCache) {
    var routes = {
        "automotive": {
            "/products": "/industry/automotive/products"  // note the leading forward slash
        }
    };

    // Overrides routes with industry specific routes
    this.get = function(path) {
        var settings = storeCache.get().settings;
        if(settings.industry) {
            if(routes[settings.industry]) {
                path = routes[settings.industry][path] || path;
            }
        }
        return path;
    };

    // checks the path and returns the key
    this.getKey = function(path) {
        var settings = storeCache.get().settings;
        if(settings.industry) {
            angular.forEach(routes[settings.industry], function(value, key) {
                if(path == value) {
                    path = key;
                }
            });
        }
        return path;
    }
}

angular.module('myApp')
    .service("route", ["storeCache", route]);;// TODO The way this works has to be revised.
// For example, if the user is not logged in a bunch of errors was thrown,
// so I added "if (config.credentials.token)" to prevent these.
// Usually you won't see the errors in the console because auth.validToken
// redirects to the login page. Still, the way it works doesn't feel right.

'use strict';

/**
 * Cache for store
 * Q's all required ajax calls for config and setup info and caches the promise.
 * This is used as a resolve for the route provider to ensure each route has the cache data available before loading
 *
 */
function storeCache($q, stock2shop, config) {
    var self = this;

    this.store = false;
    this.queue = false;

    // returns a queue of promises, if cached returns cached queue
    this.set = function () {
        if (config.credentials.token) {
            if (!self.queue) {
                self.queue = $q.all([
                    stock2shop.channel_meta.getChannelMeta({
                        channel_id: config.credentials.channels[0].id
                    }),
                    stock2shop.customers.getCustomer(
                        {customer_id: config.credentials.customer.id}
                    )
                ]).then(function (data) {
                        var settings = {};
                        angular.forEach(data[0].system_channel_meta, function(meta) {
                            settings[meta.key] = meta.value;
                        });

                        // create map for customer meta
                        var meta = {};
                        angular.forEach(data[1].system_customer.meta, function(item) {
                            meta[item.key] = item.value;
                        });
                        data[1].system_customer.meta = meta;

                        self.store = {
                            //product_meta: data[0].system_product_meta,
                            settings: settings,
                            customer: data[1].system_customer
                        };

                        // Override channelmeta for tax based on customer address.
                        var customer = self.store.customer;
                        if(customer.addresses && customer.addresses.length > 0) {
                            if(customer.addresses[0]) {
                                customer.addresses[0].full_name =  customer.addresses[0].first_name + " " + customer.addresses[0].last_name;
                                customer.addresses[0].full_name = (customer.addresses[0].full_name === " ")? null: customer.addresses[0].full_name.trim();
                            }
                        }

                        // TAX settings
                        // if user has custom tax rate, use that
                        if(customer.meta.tax_rate) {
                            settings.tax_rate = customer.meta.tax_rate;
                        }
                        if(customer.meta.tax_rate_shipping) {
                            settings.tax_rate_shipping = customer.meta.tax_rate_shipping;
                        }
                        if (settings.tax_rate) {
                            settings.tax_rate = parseInt(settings.tax_rate);
                        } else {
                            settings.tax_rate = 0;
                        }
                        if(settings.tax_rate_shipping) {
                            settings.tax_rate_shipping = parseInt(settings.tax_rate_shipping);
                        } else {
                            settings.tax_rate_shipping = settings.tax_rate;
                        }
                        if(customer.meta.payment_methods) {
                            settings.payment_methods = customer.meta.payment_methods;
                        }
                        return self.store;
                    }
                );
            }
            return self.queue;
        }
        // Credentials not set, return empty promise
        return undefined;
    };

    this.get = function() {
        return self.store;
    };

    this.reset = function() {
        self.store = false;
        self.queue = false;
    };
}

angular.module('myApp')
    .service("storeCache", ["$q", "stock2shop", "config", storeCache]);;'use strict';


/*
 * Returns a url with version appended, depending on if production or development.
 * This requires global variables production and version to be set.
 */

function url(config) {

    // returns a url with version
    this.get = function(path) {
        if(config["production"]) {
            return path + "?v=" + config["buildVersion"];
        } else {
            return path + "?v=" + new Date().getTime();
        }
    }
}

angular.module('myApp')
    .service("url", ["config", url]);;'use strict';

/**
 * Use cookies to store cart contents
 *
 * the cart order model is the same as for Stock2Shop
 * See documentation for its structure.
 * https://app.stock2shop.com/docs/#!/orders/getOrder_get_4
 *
 */
function cart($filter, utils, storeCache, price, analytics, stock2shop, config, $q, qty) {
    var self = this;

    var cookiePrefix = "cart";
    var customerCookieName = cookiePrefix;
    var cookieData;
    var cookies = $.cookie();
    angular.forEach(cookies, function(value, key) {
        if (key.startsWith(cookiePrefix)) {
            // Set cart on localStorage
            localStorage.setItem(key, value);
            // Remove HTTP cookies
            $.removeCookie(key, {path: "/"});
        }
    });
    if (
        config.credentials &&
        config.credentials.customer &&
        config.credentials.customer.id
    ) {
        customerCookieName = cookiePrefix + "-" + config.credentials.customer.id;
        // Try to read sales rep cart
        cookieData = localStorage.getItem(customerCookieName);
        if (cookieData) {
            try {
                eval("this.order = " + cookieData);
            } catch (e) {
            }
        }
    }
    if (!this.order) {
        // Legacy cart is not name-spaced by customer_id
        cookieData = localStorage.getItem(cookiePrefix);
        if (cookieData) {
            try {
                eval("this.order = " + cookieData);
            } catch (e) {
            }
            localStorage.removeItem(cookiePrefix);
        }
    }
    if (!this.order) {
        // Default empty order
        this.order = {line_items: []};
    }

    // Calling $scope.$emit('cart:updated') from the controllers makes code
    // harder to read and creates extra work when writing controller logic.
    // Better to encapsulate this inside the service.
    var callbackCartUpdated = [];
    this.subscribeCartUpdated = function(callback) {
        callbackCartUpdated.push(callback);
    };
    this.cartUpdated = function() {
        angular.forEach(callbackCartUpdated, function(callback) {
            callback();
        });
    };

    /**
     * Ideally we should just return true or false from this function,
     * however seeing as it is being retrofitted to older code throwing
     * a stack-trace is useful for tracking down where it was called from.
     *
     * @param variant_id
     */
    this.checkVariantId = function(variant_id) {
        variant_id = variant_id || 0;
        if (isNaN(Number(variant_id)) || Number(variant_id) === 0) {
            // Check dev console for caller filename and line number.
            throw new Error("Invalid variant_id " + variant_id);
        }
    };

    this.resetOrder = function () {
        self.order = {line_items: []};
        self.removeCookie();
        self.cartUpdated();
    };

    /**
     *
     * @returns {*}
     */
    this.getOrder = function () {
        var config = storeCache.get();
        self.deDuplicateLineItems(self.order);
        var order = angular.copy(self.order);
        order.customer = {
            "id" : config.customer.channel_customer_code,
            "first_name": config.customer.first_name,
            "last_name": config.customer.last_name,
            "email": config.customer.email
        };
        order.client_id = order.customer.client_id;
        order.channel_id = order.customer.channel_id;
        order.has_taxes_incl = (config.settings.price_display || false) === 'inclusive';

        // self.calculateTotals(order, config.settings);
        return order;
    };

    /**
     *
     * @param variant_id
     * @returns {number}
     */
    this.getItemTax = function(variant_id) {
        for (var i = 0; i < self.order.line_items.length; i++) {
            if(self.order.line_items[i].variant_id === variant_id) {
                if(self.order.line_items[i].tax_lines[0]) {
                    return parseFloat(self.order.line_items[i].tax_lines[0].rate);
                }
                return 0;
            }
        }
    };

    // We do not allow duplicate lines per order.
    // If we have duplicate variant_id sum them together.
    this.deDuplicateLineItems = function(order) {
        var duplicates = [];
        var lines = {};
        angular.forEach(order.line_items, function(item, key) {
            if(lines[item.variant_id]) {
                lines[item.variant_id].qty += item.qty;
                duplicates.push(key);
            }
            lines[item.variant_id] = item;
        });
        angular.forEach(duplicates, function(key) {
            order.line_items.splice(key, 1);
        });
    };

    this.getVariant = function(variant_id) {
        self.checkVariantId(variant_id);
        var variant = false;
        if (self.order.line_items) {
            angular.forEach(self.order.line_items, function(line_item) {
                if (line_item.variant_id == variant_id) {
                    variant = line_item;
                }
            });
        }
        return variant;
    };

    /**
     *
     * @param item
     */
    this.updatePrice = function(item, tax_rate) {
        self.checkVariantId(item.variant_id);
        angular.forEach(self.order.line_items, function (existingItem) {
            if (existingItem.variant_id === item.variant_id) {
                existingItem.price = price.getExTax(item.price, tax_rate);
                existingItem.tax_lines = self.getItemTaxLines(item, tax_rate);
            }
        });
        self.setCookie(self.order);
        self.cartUpdated();
    };

    /**
     *
     * Changes the qty and saves the order
     *
     * @param item
     */
    this.updateQty = function (item) {
        self.checkVariantId(item.variant_id);
        angular.forEach(self.order.line_items, function (existingItem) {
            if (existingItem.variant_id === item.variant_id) {
                existingItem.qty = item.qty;
            }
        });
        self.setCookie(self.order);
        self.cartUpdated();
    };

    /**
     * accepts a stock2shop product and variant
     * assumes there is only one variant and uses the first variant.
     * Throws error if not valid
     * Returns true if added, false if not.
     *
     * @param variant
     * @param product
     * @param qty_to_add
     * @param [custom]
     * @returns {{added: boolean, message: string}}
     */
    this.addItem = function (variant, product, qty_to_add, custom) {

        // make sure the qty we are adding is allowed
        var qty_check = qty.validate(variant, product, qty_to_add);
        if(!qty_check.valid) {
            return {
                added: false,
                error: qty_check.error
            }
        }

        // Get tax rate
        var tax_rate = price.getTaxRate(variant, product);

        // Add item
        var item = {};
        item.sku = variant.sku;
        item.title = product.title;
        item.barcode = variant.barcode;
        item.grams = variant.grams;
        item.price = price.getExTax(variant.price, tax_rate);
        item.qty = parseInt(qty_to_add);
        item.product_id = variant.product_id;
        item.variant_id = variant.id;

        // Extend line item with custom data
        if (custom) {
            angular.extend(item, custom);
        }

        // set tax lines
        item.tax_lines = self.getItemTaxLines(item, tax_rate);

        // is this product already in the cart, then overwrite it
        var mustAdd = true;
        angular.forEach(self.order.line_items, function (existingItem) {
            if (existingItem.variant_id === item.variant_id) {
                existingItem.qty = item.qty;
                mustAdd = false;
            }
        });
        if (mustAdd) {
            self.order.line_items = self.order.line_items || [];
            self.order.line_items.push(item);

            // TODO Analytics should be actioned from the controllers
            // Analytics: track product add to cart
            analytics.addProduct(
                item.sku,
                item.title,
                '',
                '',
                item.variant_id,
                item.price,
                item.qty
            );
            analytics.trackCart('add');
        }

        // save cart
        self.setCookie(self.order);

        self.cartUpdated();

        return {
            added: true,
            error: ""
        };
    };

    /**
     *
     * @param variant_id
     */
    this.removeItem = function (variant_id) {
        self.order.line_items = $filter('filter')(self.order.line_items, {variant_id: '!' + variant_id});
        self.setCookie(self.order);
        self.cartUpdated();

        // Analytics: track product remove from cart
        analytics.addProduct(
            variant_id
        );
        analytics.trackCart('remove');
    };


    /**
     * Add items from an order to the cart and return a summary
     *
     * @param order_id
     * @param successCallback
     * @param failureCallback
     */
    this.reOrder = function(order_id, successCallback, failureCallback) {
        successCallback = successCallback || function() {};
        failureCallback = failureCallback || function() {};
        var response = {
            "total": 0,
            "added": [],
            "failed": []
        };
        stock2shop.orders.getOrder({order_id: order_id}).then(function(data) {
            var queue = [];
            response.total = data.system_order.line_items.length;
            angular.forEach(data.system_order.line_items, function(item) {
                var promise = stock2shop.products.getProduct({product_id: item.product_id})
                    .then(function(product) {
                        angular.forEach(product.system_product.variants, function(variant) {
                            if (item.variant_id === variant.id) {
                                var cartItem = self.addItem(variant, product.system_product, item.qty);
                                if(cartItem.added) {
                                    response.added.push(item);
                                } else {
                                    response.failed.push(item);
                                }
                            }
                        });
                    })
                    .catch(function () {return false;});
                queue.push(promise);
            });
            $q.all(queue).then(function() {
                successCallback(response);
            });
        }).catch(function() {
            failureCallback(response);
        });
    };

    /**
     * Set channel_order_code as given or
     * get next sequential channel_order_code from API.
     * @param channel_order_code
     * @param callback
     */
    this.setID = function (channel_order_code, callback) {
        if (channel_order_code) {
            self.order.id = channel_order_code;
        } else {
            stock2shop.orders.getNextOrderCode({
                channel_id: config.credentials.channels[0].id
            }).then(function(data) {
                self.order.id = data.system_order.channel_order_code;
                callback();
            });
        }
        self.setCookie(self.order);
        self.cartUpdated();
    };

    this.getItemTaxLines = function(item, tax_rate) {
        var settings = storeCache.get().settings;
        var tax_description = settings.tax_description || "TAX";
        var tax_code = (tax_rate > 0)? "taxed": "exempt";
        return [{
            "title": tax_description,
            "rate": tax_rate,
            "price": price.getTax(item.price, tax_rate),
            "code": tax_code
        }];

    };

    this.removeShippingLines = function (lines) {
        self.order.shipping_lines = lines || [];
        self.setCookie(self.order);
        self.cartUpdated();
    };

    this.setShippingLines = function (lines) {
        self.order.shipping_lines = lines || [];
        self.setCookie(self.order);
        self.cartUpdated();
    };

    this.setBillingAddress = function (address) {
        self.order.billing_address = address || {};
        self.setCookie(self.order);
        self.cartUpdated();
    };

    this.setShippingAddress = function (address) {
        self.order.shipping_address = address || {};
        self.setCookie(self.order);
        self.cartUpdated();
    };

    this.setInstruction = function (instruction) {
        self.order.instruction = instruction;
        self.setCookie(self.order);
        self.cartUpdated();
    };

    this.setCustomer = function (customer) {
        self.order.customer = customer;
        self.setCookie(self.order);
        self.cartUpdated();
    };

    this.setField = function (key, value) {
        self.order[key] = value;
        self.setCookie(self.order);
        self.cartUpdated();
    };

    this.setCheckoutFields = function (fields) {
        self.order.checkout_fields = fields;
        self.setCookie(self.order);
        self.cartUpdated();
    };

    this.setDiscount = function (discount) {
        self.order.total_discount = discount;
    };

    this.removeCookie = function () {
        console.info("Removing cart: " + customerCookieName);
        localStorage.removeItem(customerCookieName);
    };

    this.setCookie = function (order) {
        localStorage.setItem(customerCookieName, JSON.stringify(order));
    };

    this.setTotals = function(order) {
        var line_item_map = utils.createMap(order.line_items, 'variant_id');
        var settings = storeCache.get().settings;
        self.order.has_taxes_incl = order.has_taxes_incl;

        for(var i=0; i < self.order.line_items.length; i++) {
            self.order.line_items[i].tax = line_item_map[self.order.line_items[i].variant_id].tax;
            self.order.line_items[i].tax_per_unit = line_item_map[self.order.line_items[i].variant_id].tax_per_unit;
            self.order.line_items[i].sub_total = line_item_map[self.order.line_items[i].variant_id].sub_total;
            self.order.line_items[i].total = line_item_map[self.order.line_items[i].variant_id].total;
            self.order.line_items[i].totals_incl = line_item_map[self.order.line_items[i].variant_id].totals_incl;
            self.order.line_items[i].price = line_item_map[self.order.line_items[i].variant_id].price;
            self.order.line_items[i].price_incl = line_item_map[self.order.line_items[i].variant_id].price_incl;
        }
        self.order.shipping_total   = order.shipping_total;
        self.order.sub_total        = order.sub_total;
        self.order.tax              = order.tax;
        self.order.tax_description  = settings.tax_description || "TAX";
        self.order.total            = order.total;
        self.order.totals_incl = order.totals_incl;
        self.setCookie(self.order);
        return self.order;
    };

    this.setLineTitles = function(order) {
        var line_item_map = utils.createMap(order.line_items, 'variant_id');
        for(var i=0; i < self.order.line_items.length; i++) {
            self.order.line_items[i].title = line_item_map[self.order.line_items[i].variant_id].title;
        }
    };

    // Save cart items to database
    this.persistCart = function () {
        stock2shop.cart.saveCartFromOrder({
            body: self.getOrder()
        }).catch(function () {
            console.error("Cart save failed");
        });
    };

    // Load cart items from database
    this.loadCart = function () {
        var settings = storeCache.get().settings;
        var checkout_fields_cache = JSON.parse(settings.checkout_fields || '{}');
        stock2shop.cart.getCartAsOrder().then(function (data) {

            // Merge checkout fields from database with channel settings
            // ensuring that all properties are present.
            angular.forEach(checkout_fields_cache, function (val, key) {
                if (data.results.checkout_fields[key]) {
                    angular.forEach(val, function (v, k) {
                        if (!data.results.checkout_fields[key][k]) {
                            data.results.checkout_fields[key][k] = v;
                        }
                    });
                }
            });

            self.order = data.results;
            self.setCookie(self.order);
            self.cartUpdated();
        }).catch(function () {
            console.error("Cart load failed");
        });
    };

    this.hasCookie = function (customer_id) {
        return localStorage.getItem(cookiePrefix + "-" + customer_id) !== null;
    };
}

angular.module('myApp')
    .service("cart", ["$filter", "utils", "storeCache", "price", "Analytics", "stock2shop", "config", "$q", "qty", cart]);
;'use strict';

function validation() {
    var self = this;

    this.validationTypes = function() {
        return {
            number: {
                message: "Must be a valid number",
                valid: function(value) {
                    return !isNaN(Number(value));
                }
            }
        }
    };

    /**
     * Given a value check if it matches the specified type.
     * If no value is specified return the default validation message for the type.
     *
     * @param type
     * @param value
     */
    this.validate = function(type, value) {
        var types = self.validationTypes();

        // For one argument return the default validation message.
        if (arguments.length == 1) {
            if (types[type]) {
                return types[type].message;
            } else {
                // Unknown type return empty message.
                return "";
            }

        } else {
            // For two arguments validate the given value.
            if (types[type]) {
                return types[type].valid(value);
            } else {
                // Just return false for unknown type.
                return false;
            }
        }
    };
}

angular.module('myApp')
    .service("validation", validation);;'use strict';

function utils($location, storeCache, $timeout, $window, storage) {
    var self = this;

    // Add trim functions to string prototype if not available natively.
    // http://stackoverflow.com/a/498995/639133
    if (!String.prototype.trim) {
        String.prototype.trim=function(){return this.replace(/^\s+|\s+$/g, '');};
        String.prototype.ltrim=function(){return this.replace(/^\s+/,'');};
        String.prototype.rtrim=function(){return this.replace(/\s+$/,'');};
        String.prototype.fulltrim=function(){return this.replace(/(?:(?:^|\n)\s+|\s+(?:$|\n))/g,'').replace(/\s+/g,' ');};
    }

    // https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/String/startsWith
    if (!String.prototype.startsWith) {
        String.prototype.startsWith = function(searchString, position){
            position = position || 0;
            return this.substr(position, searchString.length) === searchString;
        };
    }

    /**
     * Returns a hash map of array of values keyed on the specified key.
     * @param {array} values
     * @param {string} key
     */
    this.createMap = function(values, key) {
        var map = {};
        angular.forEach(values, function(value) {
            map[value[key]] = value;
        });
        return map;
    };

    /**
     * Convert the given object to a JSON string and search inside it.
     * Return first match with padding as specified.
     *
     * @param {object} obj
     * @param {string} search
     * @param {number} [padding=10]
     */
    this.findMatch = function(obj, search, padding) {
        padding = padding || 30;
        var s = JSON.stringify(obj);
        var start = s.toLowerCase().indexOf(search.toLowerCase());
        if (start == -1) {
            return "No match";
        }
        var end = start + search.length;
        start = (start - padding > 0) ? start - padding : 0;
        end = (end + padding < s.length) ? end + padding : s.length;
        return s.substring(start, end);
    };

    /**
     * Focus on input by element id
     * http://stackoverflow.com/questions/25596399/set-element-focus-in-angular-way
     * @returns {Function}
     */
    this.focus = function(id) {
        // timeout makes sure that it is invoked after any other event has been triggered.
        // e.g. click events that need to run before the focus or
        // inputs elements that are in a disabled state but are enabled when those events
        // are triggered.
        $timeout(function() {
            var element = $window.document.getElementById(id);
            if(element)
                element.focus();
        });
    };

    /**
     * Wrap all instances of search inside text with <b> tag
     * @param {string} text
     * @param {string} search
     */
    this.wrapStrong = function(text, search) {
        var r = new RegExp(search, "ig");
        return text.replace(r, "<strong>" + search + "</strong>");
    };

    /**
     * Create a random string of given length
     * @param length
     * @returns {string}
     */
    this.randomString = function(length) {
        var mask = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        var result = '';
        for (var i = length; i > 0; --i) result += mask[Math.round(Math.random() * (mask.length - 1))];
        return result;
    };

    /**
     *
     * rounds number to 2 decimal places
     *
     * @param amount
     * @returns {number}
     */
    this.round2Decimal = function(amount) {
        amount -= 0;
        return (Math.round(amount * 100)) / 100;
    };

    /**
     * Convert the given amount (number or string) to a
     * currency string with prefix.
     * @param amount
     * @returns {string}
     */
    this.money = function (amount) {
        if (amount === undefined) {
            return ""
        }

        var cache = storeCache.get();
        var symbol = cache.customer.meta.currency || cache.settings.currency || "R";


//        amount -= 0;
//        amount = (Math.round(amount * 100)) / 100;
//        amount = (amount == Math.floor(amount)) ? amount + '.00' : (  (amount * 10 == Math.floor(amount * 10)) ? amount + '0' : amount);
//        return 'R' + amount;

        // Move minus symbol before "symbol" if self.currency() amount is less than 0
        var formatted_currency = self.currency(amount);
        if (amount < 0) {
            symbol = "-" + symbol;
            formatted_currency = formatted_currency.replace("-", "");
        }
        return symbol + formatted_currency;
    };

    /**
     * Convert the given amount (number or string) to a
     * currency string without prefix.
     * @param amount
     * @param [addSpaces]
     * @returns {string}
     */
    this.currency = function(amount, addSpaces) {
        addSpaces = (typeof addSpaces != "undefined")
            ? addSpaces
            : true;
        amount = Number(amount);
        if (isNaN(amount)) {
            return "0";
        }
        // Round second decimal.
        amount = Math.round(amount * 100) / 100;
        // Fix two decimal places.
        var fixed = amount.toFixed(2);
        // Add spaces for every three digits.
        if (addSpaces) {
            return fixed.replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1 ");
        }
        return fixed;
    };

    /**
     * Converts string into capitalised string
     * @param str
     * @returns {*}
     */
    this.capitalise = function(str) {
        return String(str).replace(
            /\w\S*/g,
            function(txt) {
                return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
            }
        );
    };


    this.objectLength = function(object) {
        var length = 0;
        for( var key in object ) {
            if( object.hasOwnProperty(key) ) {
                ++length;
            }
        }
        return length;
    };


    /**
     * Returns a string for the qty of a product
     * @param qty
     * @returns {string}
     */
    this.stockLevels = function(qty) {
        var cache = storeCache.get();

        // Set this to the corresponding channel meta value
        var qty_limit_upper = cache.settings["qty_limit_upper"] ? cache.settings["qty_limit_upper"] : 10;

        var string = '<span class="text-danger">out of stock</span>';
        if (qty < qty_limit_upper && qty > 0) {
            string = '<span class="text-success">' + qty + '</span>';

        } else if (qty >= qty_limit_upper) {
            string = '<span class="text-success">in stock</span>';
        }
        return string;
    };

    /**
     * Shuffles array and returns
     * Fisher–Yates shuffle
     * https://en.wikipedia.org/wiki/Fisher%E2%80%93Yates_shuffle
     *
     * @param array
     * @returns array
     */
    this.shuffleArray = function (array) {
        for (var i = array.length - 1; i > 0; i--) {
            var j = Math.floor(Math.random() * (i + 1));
            var temp = array[i];
            array[i] = array[j];
            array[j] = temp;
        }
        return array;
    };

    /**
     * Change the current route
     * @param route
     * @param [params]
     * @param save_params
     */
    this.changeRoute = function(route, params, save_params) {

        // Stored query params
        // These are overridden by storage settings
        var storageParams = storage.getStorage();
        if(storageParams) {
            params = angular.extend(storageParams, params);
        }
        if(save_params) {
            storage.setStorage(params);
        }

        var query = "";
        params = params || {};
        angular.forEach(params, function(value, key) {
            if (typeof value !== "undefined" && value !== null) {
                if (query.length === 0) {
                    query += "?";
                } else {
                    query += "&";
                }
                query += encodeURIComponent(key) + "=" + encodeURIComponent(value);
            }
        });
        $location.url(route + query);
    };

    /**
     *
     * Returns md5 hash of string
     * @param str
     * @returns {string}
     *
     * discuss at: http://phpjs.org/functions/md5/
     * original by: Webtoolkit.info (http://www.webtoolkit.info/)
     * improved by: Michael White (http://getsprink.com)
     * improved by: Jack
     * improved by: Kevin van Zonneveld (http://kevin.vanzonneveld.net)
     * input by: Brett Zamir (http://brett-zamir.me)
     * bugfixed by: Kevin van Zonneveld (http://kevin.vanzonneveld.net)
     * depends on: utf8_encode
     * example 1: md5('Kevin van Zonneveld');
     * returns 1: '6e658d4bfcb59cc13f96c14450ac40b9'
     *
     */
    this.md5 = function (str) {
        var xl;

        var rotateLeft = function (lValue, iShiftBits) {
            return (lValue << iShiftBits) | (lValue >>> (32 - iShiftBits));
        };

        var addUnsigned = function (lX, lY) {
            var lX4, lY4, lX8, lY8, lResult;
            lX8 = (lX & 0x80000000);
            lY8 = (lY & 0x80000000);
            lX4 = (lX & 0x40000000);
            lY4 = (lY & 0x40000000);
            lResult = (lX & 0x3FFFFFFF) + (lY & 0x3FFFFFFF);
            if (lX4 & lY4) {
                return (lResult ^ 0x80000000 ^ lX8 ^ lY8);
            }
            if (lX4 | lY4) {
                if (lResult & 0x40000000) {
                    return (lResult ^ 0xC0000000 ^ lX8 ^ lY8);
                } else {
                    return (lResult ^ 0x40000000 ^ lX8 ^ lY8);
                }
            } else {
                return (lResult ^ lX8 ^ lY8);
            }
        };

        var _F = function (x, y, z) {
            return (x & y) | ((~x) & z);
        };
        var _G = function (x, y, z) {
            return (x & z) | (y & (~z));
        };
        var _H = function (x, y, z) {
            return (x ^ y ^ z);
        };
        var _I = function (x, y, z) {
            return (y ^ (x | (~z)));
        };

        var _FF = function (a, b, c, d, x, s, ac) {
            a = addUnsigned(a, addUnsigned(addUnsigned(_F(b, c, d), x), ac));
            return addUnsigned(rotateLeft(a, s), b);
        };

        var _GG = function (a, b, c, d, x, s, ac) {
            a = addUnsigned(a, addUnsigned(addUnsigned(_G(b, c, d), x), ac));
            return addUnsigned(rotateLeft(a, s), b);
        };

        var _HH = function (a, b, c, d, x, s, ac) {
            a = addUnsigned(a, addUnsigned(addUnsigned(_H(b, c, d), x), ac));
            return addUnsigned(rotateLeft(a, s), b);
        };

        var _II = function (a, b, c, d, x, s, ac) {
            a = addUnsigned(a, addUnsigned(addUnsigned(_I(b, c, d), x), ac));
            return addUnsigned(rotateLeft(a, s), b);
        };

        var convertToWordArray = function (str) {
            var lWordCount;
            var lMessageLength = str.length;
            var lNumberOfWords_temp1 = lMessageLength + 8;
            var lNumberOfWords_temp2 = (lNumberOfWords_temp1 - (lNumberOfWords_temp1 % 64)) / 64;
            var lNumberOfWords = (lNumberOfWords_temp2 + 1) * 16;
            var lWordArray = new Array(lNumberOfWords - 1);
            var lBytePosition = 0;
            var lByteCount = 0;
            while (lByteCount < lMessageLength) {
                lWordCount = (lByteCount - (lByteCount % 4)) / 4;
                lBytePosition = (lByteCount % 4) * 8;
                lWordArray[lWordCount] = (lWordArray[lWordCount] | (str.charCodeAt(lByteCount) << lBytePosition));
                lByteCount++;
            }
            lWordCount = (lByteCount - (lByteCount % 4)) / 4;
            lBytePosition = (lByteCount % 4) * 8;
            lWordArray[lWordCount] = lWordArray[lWordCount] | (0x80 << lBytePosition);
            lWordArray[lNumberOfWords - 2] = lMessageLength << 3;
            lWordArray[lNumberOfWords - 1] = lMessageLength >>> 29;
            return lWordArray;
        };

        var wordToHex = function (lValue) {
            var wordToHexValue = '',
                wordToHexValue_temp = '',
                lByte, lCount;
            for (lCount = 0; lCount <= 3; lCount++) {
                lByte = (lValue >>> (lCount * 8)) & 255;
                wordToHexValue_temp = '0' + lByte.toString(16);
                wordToHexValue = wordToHexValue + wordToHexValue_temp.substr(wordToHexValue_temp.length - 2, 2);
            }
            return wordToHexValue;
        };

        var x = [],
            k, AA, BB, CC, DD, a, b, c, d, S11 = 7,
            S12 = 12,
            S13 = 17,
            S14 = 22,
            S21 = 5,
            S22 = 9,
            S23 = 14,
            S24 = 20,
            S31 = 4,
            S32 = 11,
            S33 = 16,
            S34 = 23,
            S41 = 6,
            S42 = 10,
            S43 = 15,
            S44 = 21;

//        str = utf8_encode(str);
        x = convertToWordArray(str);
        a = 0x67452301;
        b = 0xEFCDAB89;
        c = 0x98BADCFE;
        d = 0x10325476;

        xl = x.length;
        for (k = 0; k < xl; k += 16) {
            AA = a;
            BB = b;
            CC = c;
            DD = d;
            a = _FF(a, b, c, d, x[k + 0], S11, 0xD76AA478);
            d = _FF(d, a, b, c, x[k + 1], S12, 0xE8C7B756);
            c = _FF(c, d, a, b, x[k + 2], S13, 0x242070DB);
            b = _FF(b, c, d, a, x[k + 3], S14, 0xC1BDCEEE);
            a = _FF(a, b, c, d, x[k + 4], S11, 0xF57C0FAF);
            d = _FF(d, a, b, c, x[k + 5], S12, 0x4787C62A);
            c = _FF(c, d, a, b, x[k + 6], S13, 0xA8304613);
            b = _FF(b, c, d, a, x[k + 7], S14, 0xFD469501);
            a = _FF(a, b, c, d, x[k + 8], S11, 0x698098D8);
            d = _FF(d, a, b, c, x[k + 9], S12, 0x8B44F7AF);
            c = _FF(c, d, a, b, x[k + 10], S13, 0xFFFF5BB1);
            b = _FF(b, c, d, a, x[k + 11], S14, 0x895CD7BE);
            a = _FF(a, b, c, d, x[k + 12], S11, 0x6B901122);
            d = _FF(d, a, b, c, x[k + 13], S12, 0xFD987193);
            c = _FF(c, d, a, b, x[k + 14], S13, 0xA679438E);
            b = _FF(b, c, d, a, x[k + 15], S14, 0x49B40821);
            a = _GG(a, b, c, d, x[k + 1], S21, 0xF61E2562);
            d = _GG(d, a, b, c, x[k + 6], S22, 0xC040B340);
            c = _GG(c, d, a, b, x[k + 11], S23, 0x265E5A51);
            b = _GG(b, c, d, a, x[k + 0], S24, 0xE9B6C7AA);
            a = _GG(a, b, c, d, x[k + 5], S21, 0xD62F105D);
            d = _GG(d, a, b, c, x[k + 10], S22, 0x2441453);
            c = _GG(c, d, a, b, x[k + 15], S23, 0xD8A1E681);
            b = _GG(b, c, d, a, x[k + 4], S24, 0xE7D3FBC8);
            a = _GG(a, b, c, d, x[k + 9], S21, 0x21E1CDE6);
            d = _GG(d, a, b, c, x[k + 14], S22, 0xC33707D6);
            c = _GG(c, d, a, b, x[k + 3], S23, 0xF4D50D87);
            b = _GG(b, c, d, a, x[k + 8], S24, 0x455A14ED);
            a = _GG(a, b, c, d, x[k + 13], S21, 0xA9E3E905);
            d = _GG(d, a, b, c, x[k + 2], S22, 0xFCEFA3F8);
            c = _GG(c, d, a, b, x[k + 7], S23, 0x676F02D9);
            b = _GG(b, c, d, a, x[k + 12], S24, 0x8D2A4C8A);
            a = _HH(a, b, c, d, x[k + 5], S31, 0xFFFA3942);
            d = _HH(d, a, b, c, x[k + 8], S32, 0x8771F681);
            c = _HH(c, d, a, b, x[k + 11], S33, 0x6D9D6122);
            b = _HH(b, c, d, a, x[k + 14], S34, 0xFDE5380C);
            a = _HH(a, b, c, d, x[k + 1], S31, 0xA4BEEA44);
            d = _HH(d, a, b, c, x[k + 4], S32, 0x4BDECFA9);
            c = _HH(c, d, a, b, x[k + 7], S33, 0xF6BB4B60);
            b = _HH(b, c, d, a, x[k + 10], S34, 0xBEBFBC70);
            a = _HH(a, b, c, d, x[k + 13], S31, 0x289B7EC6);
            d = _HH(d, a, b, c, x[k + 0], S32, 0xEAA127FA);
            c = _HH(c, d, a, b, x[k + 3], S33, 0xD4EF3085);
            b = _HH(b, c, d, a, x[k + 6], S34, 0x4881D05);
            a = _HH(a, b, c, d, x[k + 9], S31, 0xD9D4D039);
            d = _HH(d, a, b, c, x[k + 12], S32, 0xE6DB99E5);
            c = _HH(c, d, a, b, x[k + 15], S33, 0x1FA27CF8);
            b = _HH(b, c, d, a, x[k + 2], S34, 0xC4AC5665);
            a = _II(a, b, c, d, x[k + 0], S41, 0xF4292244);
            d = _II(d, a, b, c, x[k + 7], S42, 0x432AFF97);
            c = _II(c, d, a, b, x[k + 14], S43, 0xAB9423A7);
            b = _II(b, c, d, a, x[k + 5], S44, 0xFC93A039);
            a = _II(a, b, c, d, x[k + 12], S41, 0x655B59C3);
            d = _II(d, a, b, c, x[k + 3], S42, 0x8F0CCC92);
            c = _II(c, d, a, b, x[k + 10], S43, 0xFFEFF47D);
            b = _II(b, c, d, a, x[k + 1], S44, 0x85845DD1);
            a = _II(a, b, c, d, x[k + 8], S41, 0x6FA87E4F);
            d = _II(d, a, b, c, x[k + 15], S42, 0xFE2CE6E0);
            c = _II(c, d, a, b, x[k + 6], S43, 0xA3014314);
            b = _II(b, c, d, a, x[k + 13], S44, 0x4E0811A1);
            a = _II(a, b, c, d, x[k + 4], S41, 0xF7537E82);
            d = _II(d, a, b, c, x[k + 11], S42, 0xBD3AF235);
            c = _II(c, d, a, b, x[k + 2], S43, 0x2AD7D2BB);
            b = _II(b, c, d, a, x[k + 9], S44, 0xEB86D391);
            a = addUnsigned(a, AA);
            b = addUnsigned(b, BB);
            c = addUnsigned(c, CC);
            d = addUnsigned(d, DD);
        }
        var temp = wordToHex(a) + wordToHex(b) + wordToHex(c) + wordToHex(d);
        return temp.toLowerCase();
    };


    // How can I get query string values in JavaScript?
    // http://stackoverflow.com/a/901144/639133
    this.getParameterByName = function(name, url) {
        if (!url) {
            url = window.location.href;
        }
        name = name.replace(/[\[\]]/g, "\\$&");
        var regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)"),
            results = regex.exec(url);
        if (!results) return null;
        if (!results[2]) return '';
        return decodeURIComponent(results[2].replace(/\+/g, " "));
    };

    /**
     * Elastic search will sort result.aggregations alphabetically.
     * Reconstruct using the same order as per elasticSearch.aggregateQuery
     * @param aggregations
     * @param result
     * @returns {Array|*}
     */
    this.sortAggregations = function(aggregations, result) {
        // Order of dictionary keys is not guaranteed
        // http://stackoverflow.com/a/5525820/639133
        var sorted = [];
        angular.forEach(aggregations, function(value, key) {
            if(value.field === 'year') {
                sorted.push({
                    key: key,
                    value: result['Year'],
                    requires: value.requires || false
                });
            } else if(value.field === 'make') {
                sorted.push({
                    key: key,
                    value: result['Make'],
                    requires: value.requires || false
                });
            } else if(value.field === 'model') {
                sorted.push({
                    key: key,
                    value: result['Model'],
                    requires: value.requires || false
                });
            } else {
                sorted.push({
                    key: key,
                    value: result[key],
                    requires: value.requires || false
                });
            }
        });

        return sorted
    };


    /**
     * Adds display property to aggregations such has load hide ad separate
     * @param aggregations
     * @param filters
     */
    this.setAggregationsDisplay = function(aggregations, map, filters) {
        angular.forEach(aggregations, function (agg) {
            if(map[agg.key]) {
                if (map[agg.key].requires && !filters[map[agg.key].requires]) {
                    agg.hide = true;
                }
                if (map[agg.key].separator) {
                    agg.separator = map[agg.key].separator;
                }
                if (map[agg.key].hide_count) {
                    agg.hide_count = map[agg.key].hide_count;
                }
            }
        });
    };

    /**
     * Recursive function to find the value of a property on a nested object
     * @param obj
     * @param query
     * @returns {*}
     */
    this.searchProperty = function(obj, query) {
        for (var key in obj) {
            var value = obj[key];
            if (key === query) {
                return value;
            }
            if (typeof value === 'object') {
                return this.searchProperty(value, query);
            }

        }
    };

    /**
     * Convert UTC date time to local date time
     * http://stackoverflow.com/a/18330682/639133
     * @param date
     * @returns {Date}
     */
    function convertUTCDateToLocalDate(date) {
        var newDate = new Date(date.getTime()+date.getTimezoneOffset()*60*1000);

        var offset = date.getTimezoneOffset() / 60;
        var hours = date.getHours();

        newDate.setHours(hours - offset);

        return newDate;
    }

    /**
     * Convert Date object into string YYYY-MM-DD format
     * @param dateObj
     * @param separator
     * @returns {string}
     */
    this.getYYYYMMDD = function(dateObj, separator) {
        if (dateObj instanceof Date && !isNaN(dateObj)) {
            separator = (typeof separator !== 'undefined') ? separator : '-';
            var day = dateObj.getDate();
            day = (day < 10) ? '0' + day : day; // add leading zero
            var month = dateObj.getMonth() + 1; // getMonth() returns the month index (0 to 11) of a date. January = 0, February = 1, etc.
            month = (month < 10) ? '0' + month : month; // add leading zero
            var year = dateObj.getFullYear();

            return year + separator + month + separator + day;
        }
        return 'not date';
    };

    /**
     * Return the local date of the given UTC date by default.
     *
     * @param utcDateString
     */
    this.getClientDate = function(utcDateString) {
        if (!utcDateString) {
            return "no date";
        }
        utcDateString = utcDateString.substr(0, 19);
        utcDateString = utcDateString.replace("T", " ");

        var matches = utcDateString.match(
            /(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})/);
        if (matches.length < 7) {
            return "not date";
        }

        // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date
        var localDate = convertUTCDateToLocalDate(new Date(
            Number(matches[1]),     // year
            Number(matches[2]) - 1, // month (zero based)
            Number(matches[3]),     // day
            Number(matches[4]),     // hours
            Number(matches[5]),     // minutes
            Number(matches[6]),     // seconds
            0                       // milliseconds
        ));

        return localDate.toString().substr(0,24);
    };

    this.toBool = function(string) {
        if (typeof string === "boolean"){
            return string;
        }
        if(typeof string === "string") {
            return (string.toLowerCase().trim() === "true");
        }
        if(typeof string === "number") {
            return (string === 1);
        }
        return false;
    }
}

angular.module('myApp')
    .service("utils", ["$location", "storeCache", "$timeout", "$window", "storage", utils]);

