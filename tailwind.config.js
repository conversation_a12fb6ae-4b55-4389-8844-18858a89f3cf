const colors = require('tailwindcss/colors');
/** @type {import('tailwindcss').Config} */
export default {
  darkMode: 'class',
  content: ['./src/**/*.svelte'],
  theme: {
    colors: {
      transparent: 'transparent',
      current: 'currentColor',
      black: colors.black,
      white: colors.white,
      gray: colors.zinc,
      brand: {
        brand: '#233347',
        action: '#ef4459',
        search: '#445162',
        notification: '#3b82f6',
        notificationTransparent: 'rgba(59, 130, 246, 0.1)',
        selected: 'rgba(59, 130, 246, 0.1)',
        selectedAction: '#CADDFD',
        actionTransparent: 'rgba(239, 68, 89, 0.1)',
        actionPink: '#FDECEE',
        warning: '#f5931c',
        warningTransparent: 'rgba(212, 121, 10, 0.1)',
        confirmation: '#0cb91b',
        confirmationTransparent: 'rgba(12, 185, 27, 0.1)',
        error: '#FC0C34',
        link: '#3b82f6'
      },
      neutral: {
        50: '#f8fafc',
        100: '#f1f5f9',
        200: '#e2e8f0',
        300: '#cbd5e1',
        400: '#94a3b8',
        500: '#64748b',
        600: '#475569',
        700: '#334155',
        800: '#1e293b',
        transparent800: 'rgba(30, 41, 59, 0.3)',
        900: '#0f172a'
      }
    },
    extend: {
      fontFamily: {
        sans: ['Public Sans', 'sans-serif'],
        mono: ['PT Mono', 'monospace']
      },
      /**
       * @see src/utils/classname.ts where we need flag these custom font-size values for twMerge to work correctly
       */
      fontSize: {
        smaller: '0.625rem',
        small: '0.688rem',
        regular: '0.813rem',
        big: '1.25rem'
      },
      keyframes: {
        shimmer: {
          '100%': {
            transform: 'translateX(100%)'
          }
        },
        indeterminate: {
          '0%': {
            transform: 'translateX(0) scaleX(0)'
          },
          '40%': {
            transform: 'translateX(0) scaleX(0.4)'
          },
          '100%': {
            transform: 'translateX(100%) scaleX(0.5)'
          }
        }
      },
      zIndex: {
        max: 999999999
      }
    }
  },
  plugins: [require('@tailwindcss/forms'), require('@tailwindcss/container-queries')]
};
