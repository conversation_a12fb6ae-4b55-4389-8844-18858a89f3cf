# stock2shop/app

App endpoints for use with console, b2b store, and v1 API


## Dev setup

You probably want to follow the instruction for [dev-setup](https://github.com/stock2shop/dev-setup)

However, if you're only going to be working on this repo, then follow the instructions below
```sh
# Clone
mkdir stock2shop
cd stock2shop
export S2S_PATH=$(pwd)
git clone https://github.com/stock2shop/dev-setup.git "$S2S_PATH/dev-setup"
git clone https://github.com/stock2shop/app.git "$S2S_PATH/app"
git clone https://github.com/stock2shop/b2b.git "$S2S_PATH/b2b"
git clone https://github.com/stock2shop/console.git "$S2S_PATH/console"
git clone https://github.com/stock2shop/sim.git "$S2S_PATH/sim"

# Install
cd "$S2S_PATH/app"
APP_DIR=$(pwd) ./make.sh install
```

Start services
```sh
${S2S_PATH}/app/up.sh
# Status
docker ps
```

Stop services
```sh
${S2S_PATH}/app/down.sh
```


## Testing

Run all the tests
```sh
cd "$S2S_PATH/app"
source dev.sh
./make.sh tests
```

For more details see `tests/README.md`

    
## PHP Storm

Setup remote interpreter

    Languages & Frameworks > PHP > CLI Interpreter > ...
    
    Name: s2s
    
    User name: root
    
    Authentication type: Key pair
    
    Private key file: /Users/<USER>/.ssh/id_rsa
    
    Path mapping: ${S2S_PATH} => /mnt/stock2shop
    
Override [remote_host](https://www.jetbrains.com/help/phpstorm/configuring-remote-interpreters.html)

    CLI Interpreter > Additional 
    
    Configuration options: -dxdebug.remote_host=host.stock2shop.test
    
Also create deployment and server with name `s2s` and same path mapping
    
    Build, Execution, Deployment > Deployment
    
    Languages & Frameworks > PHP > Servers
    
PHPUnit

    Languages & Frameworks > PHP > Test Frameworks
    
    + > PHPUnit by Remote Interpreter > s2s
    
    Path to phpunit.phar: /mnt/stock2shop/app/tests/phpunit-4.8.phar
    
    Default configuration file: /mnt/stock2shop/app/tests/phpunit.xml
    
Xdebug helper with Google Chrome,
env var set in Apache virtual host file

    `PHP_IDE_CONFIG="serverName=s2s"`

Dictionaries

    Editor > Spelling > Dictionaries
    
    Custom Dictionaries: ${S2S_PATH}/app/dictionaries/s2s.dic


## Restore database backup

Get last backup
```bash
cd ${S2S_PATH}/app
./scripts/stock2shop/getLastBackupByClient.sh key.pem ${S2S_PATH} 21 INCLUDE_PERSONAL_DATA
```

Connect to MySQL server as root user
```bash
cd ${S2S_PATH}
# See APP_DB_* env vars at link below
# https://github.com/stock2shop/dev-setup/blob/master/sample.config.dev.json
mysql --host=127.0.0.1 --port=3306 -u root -p --binary-mode
```

Create db and restore backup
```sql
drop database if exists stock2shop; create database stock2shop; use stock2shop; source stock2shop-21-ipd.sql;
```

Create user (if you haven't done this before)
```sql
create user 'stock2shop'@'%' identified by 'YOUR_PASSWORD';
grant all on stock2shop.* to 'stock2shop'@'%';
```

Update your config files
- `$config["db"]["password"]` in `${S2S_PATH}/app/www/v1/config.php`
- `APP_DB_PASS` in `${S2S_PATH}/go/api/config.dev.json`


## Xdebug

The `tests/up.sh` script will disable xdebug,
otherwise tests are slowed down significantly.
To toggle xdebug run the scripts like this
```sh
# Start
docker exec -it s2s /root/stock2shop/app/tests/xdebug.up.sh
# Stop
docker exec -it s2s /root/stock2shop/app/tests/xdebug.down.sh
```

    
## Remarks

The following links should now be working from the host
- [app.stock2shop](http://app.stock2shop.test/docs)
- [console.stock2shop](http://console.stock2shop.test/admin)
- [b2b.stock2shop](http://b2b.stock2shop.test/#/)
- [apache2](http://dev.stock2shop.test)
- [info.php](http://dev.stock2shop.test/info.php)
    
## About Apache rewrite rules
 
[into](http://httpd.apache.org/docs/current/rewrite/intro.html)

"The Pattern is a regular expression... 
matched against the URL-path of the incoming request"

    RewriteRule Pattern Substitution [flags]

[flag reference](https://httpd.apache.org/docs/current/rewrite/flags.html)

**END** not only the current round of rewrite processing,
but also prevents any subsequent rewrite processing from occurring 
in per-directory (htaccess) context
    
**NE** Do not escape special characters

**R=permanent** causes a HTTP redirect to be issued to the browser

[htaccess tester](http://htaccess.mwl.be/)

Using `Apache/2.4.18` the path for vhosts begins with `/`,
but using the htaccess tester there is no forward slash prefix

Test vhost setup

    cd app/apache/local && php test.php


## Setup xdebug

The setup script will configure xdebug on the VM,
but source files must still be mapped in PHP Storm 


## Console dev against production app

**TODO** Move `scripts/docker/i386/console` and instructions below to `stock2shop/console` repo. If an agency is doing dev on console like this they shouldn't have access to `stock2shop/app`. Same goes for `stock2shop/b2b`

Create console docker image and container

    docker stop s2s-console
    docker rm s2s-console
    docker image rm s2s-console
    
    docker build \
    -f ${S2S_PATH}/app/scripts/docker/i386/console/Dockerfile \
    -t s2s-console --rm \
    ${S2S_PATH}/app/scripts/docker/i386/console
    
    docker create --name=s2s-console \
    -p 80:80 -p 443:443 -p 22:22 \
    -it \
    -v ${S2S_PATH}:/mnt/stock2shop \
    --cap-add=NET_ADMIN --cap-add=NET_RAW \
    --env PHP_IDE_CONFIG="serverName=s2s" \
    s2s-console
    
Change `stock2shop/console/app/config.php` 
    
    $config["codegen"] = "https://app.stock2shop.com/v1/api-docs/codegen";
    $config["apiURL"] = "https://app.stock2shop.com/v1";
    
Start docker

    docker start s2s-console
    
Change hosts file

    sudo vi /etc/hosts
    # 127.0.0.1   console.stock2shop.com
    
[Login](https://console.stock2shop.com/console)

[Docker images can be shared](https://stackoverflow.com/a/23938978/639133),
also see [console README](https://github.com/stock2shop/console#s2s-console-docker-image)

    docker save -o s2s-console.tar s2s-console
    
    tar -cvzf s2s-console.tar.gz s2s-console.tar
    
    scp s2s-console.tar.gz <EMAIL>:/home/<USER>/s2s-console.tar.gz


# Create AMI for Stock2Shop 20.04
 
Keep anchors for issues linking to this README, setup instruction moved to [prod-setup](https://github.com/stock2shop/prod-setup)

## Stock2Shop Admin
## Stock2Shop App
## Stock2Shop Worker
## Stock2Shop Console

See [EC2 docs in prod-setup](https://github.com/stock2shop/prod-setup/blob/master/ec2/README.md)


# Stock2Shop Backup (Cloud backup server)

See [Linode docs in prod-setup](https://github.com/stock2shop/prod-setup/blob/master/linode/README.md)


# Elastic Search using [OpenDistro on AWS](https://eu-west-1.console.aws.amazon.com/es/home?region=eu-west-1)

See [ES docs in prod-setup](https://github.com/stock2shop/prod-setup/blob/master/es/README.md)

