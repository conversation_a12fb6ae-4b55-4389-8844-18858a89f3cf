import { derived, writable } from 'svelte/store';
import { configDefault } from '@/env/config';
import { cleanLS, getFromLS, setToLS } from '@/utils/localStorage';
import { envConfig, STORAGE_KEY, STORAGE_KEY_SEMI_PERSISTENT } from '@/constants';
import { setApiToken, setApiUrl } from '@/lib/api/apiConfigInstance';
import { isFieldNameMeta } from '@/utils/mapAggregationsToColumns';
import type { SystemUser } from '@/models/user/system-user';
import Diagnostics from '@/utils/diagnostics';
import mapColumns from '@/utils/mapColumns';
import { isFacetKeyMeta } from '@/utils/mapAggregationsToFacets';
import { CURRENT_STORAGE_VERSION } from '@/lib/constants';
import merge from 'lodash.mergewith';
import type { FacetWithIndex } from './es/types';
import type { Config, ElasticSearchConfig, Store, TableColumn } from './s2s/types';
import { ElasticSearchIndex } from './s2s/types';

let confPersistent = getFromLS(STORAGE_KEY);
let confSession = getFromLS(STORAGE_KEY_SEMI_PERSISTENT);

if (confPersistent?.version !== CURRENT_STORAGE_VERSION) {
  cleanLS(STORAGE_KEY);
  confPersistent = undefined;
}
if (confSession?.version !== CURRENT_STORAGE_VERSION) {
  cleanLS(STORAGE_KEY);
  confSession = undefined;
}

const conf: Config = {
  ...configDefault,
  persistent: merge({}, configDefault.persistent, confPersistent, (_, b) =>
    Array.isArray(b) ? b : undefined
  ),
  session: merge({}, configDefault.session, confSession, (_, b) =>
    Array.isArray(b) ? b : undefined
  )
};

// create custom store
const initial: Store = {
  config_persistent: conf.persistent,
  session: conf.session,
  config_system: conf.system
};

function createStore() {
  const { subscribe, update } = writable(initial);

  const setIndexColumns = (
    index: ElasticSearchIndex,
    columnIds: string[],
    newMetaColumns: TableColumn[] = []
  ) =>
    update((s) => {
      const entry: ElasticSearchConfig = s.config_persistent.elasticSearch[index];
      const metaFields = columnIds.filter((f) => isFieldNameMeta(f));
      const { metaColumns } = entry;
      const allColumns = [...metaColumns, ...newMetaColumns];
      const resultMetaColumns: TableColumn[] = [];
      metaFields.forEach((field) => {
        const col = allColumns.find((eachCol) => eachCol.field === field);
        if (col) {
          resultMetaColumns.push(col);
        } else {
          Diagnostics.error('Trying to add meta field without column definitions', field);
        }
      });
      entry.metaColumns = resultMetaColumns;
      entry.columnIds = columnIds;
      return s;
    });

  const setIndexFacets = (
    index: ElasticSearchIndex,
    facetKeys: string[],
    newMetaFacets: FacetWithIndex[] = []
  ) =>
    update((s) => {
      const entry = s.config_persistent.elasticSearch[index];
      const metaFields = facetKeys.filter((f) => isFacetKeyMeta(f));
      const { metaFacets } = entry;
      const allFacets = [...metaFacets, ...newMetaFacets];
      const resultMetaFacets: FacetWithIndex[] = [];
      metaFields.forEach((field) => {
        const col = allFacets.find((item) => item.key === field);
        if (col) {
          resultMetaFacets.push(col);
        } else {
          Diagnostics.error('Trying to add meta facet key without facet definitions', field);
        }
      });
      entry.metaFacets = resultMetaFacets;
      entry.facetKeys = facetKeys;
      return s;
    });

  return {
    subscribe,

    // Updates local token
    // TODO this should be set to a sameSite cookie, we should not use localStorage for this!
    // https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Set-Cookie/SameSite
    setToken: (newToken: string) =>
      update((s) => {
        const params = { ...s };
        params.session.apiToken = newToken;
        setToLS(STORAGE_KEY_SEMI_PERSISTENT, params.session);
        return params;
      }),

    resetToken: () =>
      update((s) => {
        const params = { ...s };
        delete params.session.apiToken;
        cleanLS(STORAGE_KEY_SEMI_PERSISTENT);
        return params;
      }),

    setUser: (user: SystemUser) =>
      update((s) => {
        const params = { ...s };
        params.session.user = user;
        setToLS(STORAGE_KEY_SEMI_PERSISTENT, params.session);
        return params;
      }),

    setProductColumns: (columnIds: string[], newMetaColumns: TableColumn[] = []) =>
      setIndexColumns(ElasticSearchIndex.PRODUCTS, columnIds, newMetaColumns),

    setOrderColumns: (columnIds: string[], newMetaColumns: TableColumn[] = []) =>
      setIndexColumns(ElasticSearchIndex.ORDERS, columnIds, newMetaColumns),

    setCustomerColumns: (columnIds: string[], newMetaColumns: TableColumn[] = []) =>
      setIndexColumns(ElasticSearchIndex.CUSTOMERS, columnIds, newMetaColumns),

    setProductFacets: (columnIds: string[], newMetaFacets: FacetWithIndex[] = []) =>
      setIndexFacets(ElasticSearchIndex.PRODUCTS, columnIds, newMetaFacets),

    setOrderFacets: (columnIds: string[], newMetaFacets: FacetWithIndex[] = []) =>
      setIndexFacets(ElasticSearchIndex.ORDERS, columnIds, newMetaFacets),

    setCustomerFacets: (columnIds: string[], newMetaFacets: FacetWithIndex[] = []) =>
      setIndexFacets(ElasticSearchIndex.CUSTOMERS, columnIds, newMetaFacets)
  };
}

export const s2sStore = createStore();
export const S2sStore = s2sStore;

// Subscribe to store, we can then write changes to localStorage
s2sStore.subscribe((s2s) => {
  setToLS(STORAGE_KEY, s2s.config_persistent);
});

export const configPersistent = derived(s2sStore, (s) => s.config_persistent);

export const configSession = derived(s2sStore, (s) => s.session);

/**
 * Since it's not changeable, should be replaced with a constant, not pollute dynamic store
 * @deprecated
 */
export const configSystem = derived(s2sStore, (s) => s.config_system);
export const ConfigSystem = derived(s2sStore, (s) => s.config_system);

// Token store
export const token = derived(s2sStore, (s) => s.session.apiToken);

// User store
export const userStore = derived(s2sStore, (s) => s.session.user);

setApiUrl(envConfig.apiURL);
setApiToken(conf.session.apiToken);

configSession.subscribe((c) => setApiToken(c.apiToken));

const mapFacets = (s: Store, index: ElasticSearchIndex): FacetWithIndex[] => {
  const persistent = s.config_persistent;
  const system = s.config_system;
  const { facetKeys, metaFacets } = persistent.elasticSearch[index];
  const { facets } = system.elasticSearch[index];
  return facetKeys
    .map((key) => {
      const staticFacet = facets.find((f) => f.key === key);
      const metaFacet = metaFacets.find((f) => f.key === key);
      return staticFacet || metaFacet;
    })
    .filter((f) => !!f);
};

export const productFacets = derived(s2sStore, (s) => mapFacets(s, ElasticSearchIndex.PRODUCTS));

export const orderFacets = derived(s2sStore, (s) => mapFacets(s, ElasticSearchIndex.ORDERS));

export const customerFacets = derived(s2sStore, (s) => mapFacets(s, ElasticSearchIndex.CUSTOMERS));

export const productColumns = derived(s2sStore, (s) =>
  mapColumns(ElasticSearchIndex.PRODUCTS, s.config_persistent, s.config_system)
);

export const orderColumns = derived(s2sStore, (s) =>
  mapColumns(ElasticSearchIndex.ORDERS, s.config_persistent, s.config_system)
);

export const customerOrderColumns = derived(s2sStore, (s) =>
  mapColumns(ElasticSearchIndex.CUSTOMER_ORDERS, s.config_persistent, s.config_system)
);

export const customerColumns = derived(s2sStore, (s) =>
  mapColumns(ElasticSearchIndex.CUSTOMERS, s.config_persistent, s.config_system)
);
