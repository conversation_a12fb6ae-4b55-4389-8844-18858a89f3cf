/**
 * Converts a field name to a name used by different components
 * e.g. Segments would use variants.sku instead of sku, channel rules could be different and so on.
 *
 */
export type FieldConvertor = {
  /**
   * Used for the query key, should be unique per converter
   */
  key: string;
  /**
   * converts a field name to a value used by different components
   */
  fieldToValue: (value: string) => string;
  /**
   * converts a value used by different components to a field name
   */
  valueToField: (value: string) => string;
  /**
   * list of fields that are not allowed to be used in conversion
   */
  ignoreFields: string[];
  /**
   * list of groups that are not allowed to be used in conversion
   */
  ignoreGroups: string[];
};

export const PRODUCT_FIELDS = [
  'source_product_code',
  'product_active',
  'title',
  'body_html',
  'collection',
  'product_type',
  'tags',
  'vendor'
];

const PRODUCT_DYNAMIC_FIELDS = ['meta'];

export const VARIANT_FIELDS = [
  'source_variant_code',
  'variant_active',
  'sku',
  'barcode',
  'price',
  'qty',
  'grams',
  'inventory_management'
];

/**
 * Default field convertor
 */
export const defaultFieldConvertor: FieldConvertor = {
  key: 'default',
  fieldToValue: (field: string) => field,
  valueToField: (value: string) => value,
  ignoreFields: [],
  ignoreGroups: []
};

/**
 * Initial segment convertor for initial launch,
 * does not support dynamic fields
 */
export const limitedSegmentProductConvertor: FieldConvertor = {
  key: 'limited-segment-product',
  fieldToValue: (field: string) => {
    if (VARIANT_FIELDS.includes(field)) {
      return `variants.${field}`;
    }
    return field;
  },
  valueToField: (value: string) => {
    return value.replace('variants.', '');
  },
  ignoreFields: [],
  ignoreGroups: ['price', 'qty', 'option', 'meta']
};

export const limitedSegmentCustomerConvertor: FieldConvertor = {
  key: 'limited-segment-customer',
  fieldToValue: (field: string) => field,
  valueToField: (value: string) => value,
  ignoreFields: ['password', 'password_confirmation', 'shipping_default'],
  //TODO we want to be able to support meta_* and its value (as a single KV)
  // in the future but this will require a backend change.
  // Currently we set it with meta.key and meta.value (in V1 console)
  // and we wont support editing/adding meta.key/meta.value in V2 console.
  ignoreGroups: ['meta']
};

/**
 * Segment convertors
 * @todo The logic for segments is flawed.
 * Leaving this here as an example but this needs to be reworked including backend changes.
 */
export const segmentProductConvertor: FieldConvertor = {
  key: 'segment-product',
  ignoreFields: [],
  ignoreGroups: [],
  fieldToValue: (field: string) => {
    if (VARIANT_FIELDS.includes(field)) {
      return `variants.${field}`;
    }
    // if the field starts with qty_ replace with variants.qty_availability_
    if (field.startsWith('qty_')) {
      return `variants.qty_availability_${field.replace('qty_', '')}`;
    }
    if (field.startsWith('price_')) {
      return `variants.price_tiers_${field.replace('price_', '')}`;
    }
    if (field.startsWith('meta_')) {
      return 'meta.key';
    }
    return field;
  },
  valueToField: (value: string) => {
    return value.replace('variants.', '');
  }
};

/**
 * Product Rule convertors
 */
export const productRuleConvertor: FieldConvertor = {
  key: 'product-rule',
  ignoreFields: [],
  ignoreGroups: ['price', 'qty', 'option'],
  fieldToValue: (field: string) => {
    if (PRODUCT_FIELDS.includes(field)) {
      return `product.${field}`;
    }
    if (PRODUCT_DYNAMIC_FIELDS.find((dynamicFieldPrefix) => field.startsWith(dynamicFieldPrefix))) {
      return `product.${field}`;
    }
    if (VARIANT_FIELDS.includes(field)) {
      return `variant.${field}`;
    }
    return field;
  },
  valueToField: (value: string) => {
    if (value.startsWith('variant.')) {
      return value.replace('variant.', '');
    }
    if (value.startsWith('product.')) {
      return value.replace('product.', '');
    }
    return value;
  }
};

/**
 * Faceted Navigation convertor for trade channel
 * navigation aggregations config
 */

/**
 * Elastic suggest fields convertor for trade channel
 * navigation aggregations config
 */
export const quickOrderColumnsFieldsConvertor: FieldConvertor = {
  key: 'quick-order-columns-fields',
  fieldToValue: (field: string) => {
    return field;
  },
  valueToField: (value: string) => {
    return value;
  },
  ignoreFields: [
    'price',
    'qty',
    'sku',
    'body_html',
    'inventory_management',
    'product_active',
    'variant_active'
  ],
  ignoreGroups: ['price', 'qty', 'option']
};

// Create priceFieldsConvertor which ignores all fields except for price, and all groups except for price and meta
export const priceFieldsConvertor: FieldConvertor = {
  key: 'price-fields',
  fieldToValue: (field: string) => {
    return field;
  },
  valueToField: (value: string) => {
    return value;
  },
  ignoreFields: [...PRODUCT_FIELDS, ...VARIANT_FIELDS.filter((field) => field !== 'price')],
  ignoreGroups: ['option', 'qty', 'meta']
};

export const qtyFieldsConvertor: FieldConvertor = {
  key: 'qty-fields',
  fieldToValue: (field: string) => {
    return field;
  },
  valueToField: (value: string) => {
    return value;
  },
  ignoreFields: [...PRODUCT_FIELDS, ...VARIANT_FIELDS.filter((field) => field !== 'qty')],
  ignoreGroups: ['option', 'price', 'meta']
};
