// See https://kit.svelte.dev/docs/types#app
// for information about these interfaces
declare global {
  namespace App {
    // interface Error {}
    // interface Locals {}
    // interface PageData {}
    // interface Platform {}
  }

  declare type Item = import('svelte-dnd-action').Item;
  declare type DndEvent<ItemType = Item> = import('svelte-dnd-action').DndEvent<ItemType>;

  declare namespace svelteHTML {
    interface HTMLAttributes<T> {
      // for our taphold action
      'on:taphold'?: CompositionEventHandler<T>;
      // for svelte-dnd-action
      'on:consider'?: (
        event: CustomEvent<DndEvent<ItemType>> & { target: EventTarget & T }
      ) => void;
      'on:finalize'?: (
        event: CustomEvent<DndEvent<ItemType>> & { target: EventTarget & T }
      ) => void;
    }
  }
}

export {};
