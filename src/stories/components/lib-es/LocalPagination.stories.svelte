<script context="module">
  export const meta = {
    title: 'components/lib-es/LocalPagination',
    component: LocalPagination,
    parameters: {
      layout: 'fullscreen'
    }
  };
</script>

<script>
  import { Story } from '@storybook/addon-svelte-csf';
  import LocalPagination from '@/lib/es/LocalPagination.svelte';

  const items = Array.from({ length: 30 }, (_, i) => i + 1);
  const prev = (prevPageItems) => {
    console.log('Prev items:', prevPageItems);
  };
  const next = (nextPageItems) => {
    console.log('Next items:', nextPageItems);
  };
</script>

<Story name="LocalPagination" id="local_pagination">
  <div class="container">
    <LocalPagination perPage={5} {items} {prev} {next} />
  </div>
</Story>

<style>
  .container {
    display: flex;
    justify-content: center;
    padding: 1rem;
    font-size: 18px;
  }
</style>
