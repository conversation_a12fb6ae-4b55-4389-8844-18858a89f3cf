<script context="module">
  export const meta = {
    title: 'components/lib-es/LocalSortHeader',
    component: LocalSortHeader,
    parameters: {
      layout: 'fullscreen'
    }
  };
</script>

<script>
  import { Story } from '@storybook/addon-svelte-csf';
  import LocalSortHeader from '@/lib/es/LocalSortHeader.svelte';

  let sortField = 'attribute';
  let sortBy = (type, order) => {
    sortField = type;
    alert(`Sorting by ${type} in ${order} order`);
  };
</script>

<Story name="LocalSortHeader" id="local_sort_header">
  <div class="container">
    <h2>Heading Text</h2>
    <LocalSortHeader {sortBy} {sortField} field={sortField} />
  </div>
</Story>

<style>
  .container {
    display: flex;
    align-items: center;
    padding: 1rem;
    font-size: 18px;
  }
</style>
