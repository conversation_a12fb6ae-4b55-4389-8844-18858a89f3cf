{"fields": [{"name": "source_product_code", "label": "Source Product Code", "description": "The unique identifier of the product on the source system"}, {"name": "source_variant_code", "label": "Source Variant Code", "description": "The unique identifier of the variant on the source system"}, {"name": "sku", "label": "SKU", "description": "Stock Keeping Unit - The unique identifier of the variant"}, {"name": "title", "label": "Product Title", "description": "Title for the product"}, {"name": "body_html", "label": "Description (HTML)", "description": "The long description of the product. May include Rich Text (HTML)."}, {"name": "collection", "label": "Collection", "description": "Product collection"}, {"name": "product_type", "label": "Type", "description": "Product type"}, {"name": "tags", "label": "Tags", "description": "List of keywords for the product"}, {"name": "vendor", "label": "<PERSON><PERSON><PERSON>", "description": "Product Vendor or Brand"}, {"name": "barcode", "label": "Barcode", "description": "Variant Barcode"}, {"name": "price", "label": "Price - De<PERSON>ult", "description": "Variants default price"}, {"name": "qty", "label": "Quantity - De<PERSON>ult", "description": "Variants default qty"}, {"name": "grams", "label": "Weight", "description": "Variant weight in grams"}, {"name": "inventory_management", "label": "Manage Inventory?", "description": "If false, oversell will be allowed for this variant"}], "dynamic_field_groups": [{"group": "meta", "label": "Custom Fields"}, {"group": "price", "label": "Pricing"}, {"group": "qty", "label": "Warehouse Qty"}, {"group": "option", "label": "Variant Options"}]}