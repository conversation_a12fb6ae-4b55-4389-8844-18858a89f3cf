{"config": {"channels": {"1": {"flags": [{"client_id": 21, "channel_id": 47, "source_id": 0, "table": "product", "column": "body_html", "description": "Description", "code": "system"}, {"client_id": 21, "channel_id": 47, "source_id": 0, "table": "product", "column": "collection", "description": "Collection", "code": "system"}, {"client_id": 21, "channel_id": 47, "source_id": 0, "table": "product", "column": "title", "description": "Title", "code": "system"}, {"client_id": 21, "channel_id": 47, "source_id": 0, "table": "product", "column": "options", "description": "Options", "code": "system"}, {"client_id": 21, "channel_id": 47, "source_id": 0, "table": "product", "column": "product_type", "description": "Type", "code": "system"}, {"client_id": 21, "channel_id": 47, "source_id": 0, "table": "product", "column": "tags", "description": "Tags", "code": "system"}, {"client_id": 21, "channel_id": 47, "source_id": 0, "table": "product", "column": "vendor", "description": "<PERSON><PERSON><PERSON>", "code": "system"}, {"client_id": 21, "channel_id": 47, "source_id": 0, "table": "variant", "column": "price", "description": "Price", "code": "system"}, {"client_id": 21, "channel_id": 47, "source_id": 0, "table": "variant", "column": "barcode", "description": "Barcode", "code": "system"}, {"client_id": 21, "channel_id": 47, "source_id": 0, "table": "variant", "column": "qty", "description": "QTY", "code": "system"}, {"client_id": 21, "channel_id": 47, "source_id": 0, "table": "variant", "column": "option1", "description": "Option 1", "code": "system"}, {"client_id": 21, "channel_id": 47, "source_id": 0, "table": "variant", "column": "option2", "description": "Option 2", "code": "system"}, {"client_id": 21, "channel_id": 47, "source_id": 0, "table": "variant", "column": "option3", "description": "Option 3", "code": "system"}, {"client_id": 21, "channel_id": 47, "source_id": 0, "table": "variant", "column": "grams", "description": "Grams", "code": "system"}, {"client_id": 21, "channel_id": 47, "source_id": 0, "table": "variant", "column": "inventory_management", "description": "Inventory Management", "code": "system"}, {"client_id": 21, "channel_id": 47, "source_id": 0, "table": "product", "column": "meta", "description": "Attributes", "code": "system"}, {"client_id": 21, "channel_id": 47, "source_id": 0, "table": "variant", "column": "sku", "description": "SKU", "code": "system"}], "rules": [], "sources": {"57": {"id": 57, "source_id": null, "description": "Sage One Demo", "client_id": 21, "type": "sageone", "sync_token": "2023-06-07T09:55:17.803", "active": true, "meta": [{"key": "create_order_enabled", "value": "true", "template_name": null}, {"key": "company_id", "value": "115214", "template_name": null}, {"key": "username", "value": "<EMAIL>", "template_name": null}, {"key": "password", "value": "juj-mash-yeV-1", "template_name": null}, {"key": "get_products_limit", "value": "100", "template_name": null}, {"key": "get_images_limit", "value": "1", "template_name": null}, {"key": "param_tax_code", "value": "2718926", "template_name": null}, {"key": "param_tax_exempt_code", "value": "0", "template_name": null}, {"key": "param_use_customer_address", "value": "false", "template_name": null}, {"key": "param_send_invoice_enabled", "value": "true", "template_name": null}, {"key": "product_field_map", "value": "{\n\t\"source\": {\n\t\t\"source_product_code\": \"{{TextUserField1}}\",\n\t\t\"product_active\": {{# YesNoUserField1}}true{{/ YesNoUserField1}}{{^ YesNoUserField1}}false{{/YesNoUserField1}}\n\t},\n\t\"product\": {\n\t\t\"collection\": \"{{Category.Description}}\",\n\t\t\"title\": \"{{{Description}}}\",\n\t\t\"product_type\": \"{{TextUserField2}}\",\n\t\t\"body_html\": \"\",\n\t\t\"tags\": \"\",\n\t\t\"vendor\": \"\",\n\t\t\"options\": [{\n\t\t\t\"value\": \"\",\n\t\t\t\"name\": \"Selection\",\n\t\t\t\"position\": 1\n\t\t}],\n\t\t\"variants\": {\n\t\t\t\"source_variant_code\": \"{{ID}}\",\n\t\t\t\"option1\": \"{{TextUserField3}}\",\n\t\t\t{{#QuantityReserved}}\t\n\t\t\t\"qty\": \"{{QuantityOnHand}}-{{QuantityReserved}}\",\n\t\t\t{{/QuantityReserved}}\n\t\t\t{{^QuantityReserved}}\n\t  \t\t\"qty\": \"{{QuantityOnHand}}\",\n\t \t    {{/QuantityReserved}}\n\t\t\t\"sku\": \"{{Code}}\",\n\t\t\t\"price\": \"{{PriceInclusive}}\",\n\t\t\t\"price_tiers\": [\n\t\t\t  {{#AdditionalItemPrices}} \n\t\t\t  \t\t{\n\t\t\t\t\t\t\"tier\": \"{{description}}\",\n\t\t\t\t\t\t\"price\": \"{{PriceInclusive}}\"\n\t\t\t\t\t} {{comma}} \n\t  \t\t\t\t{{/ AdditionalItemPrices}}\n\t\t\t\t\t  ],\t\t\t \t\t\t\t\t\n\t\t\t \"inventory_management\": true\t\t\n\t\t\t\t\t }\t \t\t\t\t  \n\t\t\t\t\t}\t}", "template_name": null}, {"key": "param_default_shipping_code", "value": "SHIP001", "template_name": null}, {"key": "param_get_images_enabled", "value": "true", "template_name": null}, {"key": "param_get_products_filter", "value": "Physical eq true and TextUserField1  ne ''", "template_name": null}, {"key": "cron_get_products_schedule", "value": "04 * * * *", "template_name": null}, {"key": "param_channel_order_code_enabled", "value": "false", "template_name": null}, {"key": "customer_reference_field", "value": "channel_order_code", "template_name": null}, {"key": "order_map", "value": "{\n{{^ params.use_customer_address}}\n  \"PostalAddress01\": \"{{system_order.billing_address.first_name}} {{system_order.billing_address.last_name}}\",\n  \"PostalAddress02\": \"{{system_order.billing_address.address1}} {{system_order.billing_address.address2}}\",\n  \"PostalAddress03\": \"{{system_order.billing_address.city}} {{system_order.billing_address.phone}}\",\n  \"PostalAddress04\": \"{{system_order.customer.email}}\",\n  \"DeliveryAddress01\": \"{{system_order.shipping_address.first_name}} {{system_order.shipping_address.last_name}}\",\n  \"DeliveryAddress02\": \"{{system_order.shipping_address.address1}} {{system_order.shipping_address.address2}}\",\n  \"DeliveryAddress03\": \"{{system_order.shipping_address.city}} {{system_order.shipping_address.phone}}\",\n  \"DeliveryAddress04\": \"{{system_order.customer.email}}\",\n{{/ params.use_customer_address}}\n\"message\": \"Bank: Standard Bank                                                                                                                 Branch: Sandton City                                                                                                                          Branch Code: 018105                                                                                                       Account Number: *********                                                                                                                        Please quote your invoice number as reference                                                                                  Notes: {{system_order.notes}}\"                                                           \n}", "template_name": null}, {"key": "sync_mode", "value": "pull", "template_name": null}, {"key": "queue_fetch_images", "value": "false", "template_name": null}, {"key": "param_sales_rep_id", "value": "25427", "template_name": null}, {"key": "param_create_customer_enabled", "value": "true", "template_name": null}, {"key": "order_created_date_now", "value": "false", "template_name": null}, {"key": "param_due_date_days", "value": "3", "template_name": null}, {"key": "param_negative_stock_disabled", "value": "false", "template_name": null}, {"key": "param_default_customer_code", "value": "Cash", "template_name": null}, {"key": "config_notes", "value": "<p>test2</p>", "template_name": null}, {"key": "cron_custom_ftp_processor_enabled", "value": "false", "template_name": null}, {"key": "cron_custom_ftp_processor_schedule", "value": "x 55 * * * *", "template_name": null}, {"key": "order_type", "value": "sales_order", "template_name": null}]}}, "id": 47, "description": "B2B Wholesale", "client_id": 21, "active": true, "type": "trade", "price_tier": "da<PERSON>", "qty_availability": null, "sync_token": null, "meta": [{"key": "login_redirect", "value": "https://b2b.stock2shop.com", "template_name": null}, {"key": "phone", "value": "086010101010", "template_name": null}, {"key": "address_line1", "value": "103 Sunny St", "template_name": null}, {"key": "address_line2", "value": "Sunnyville", "template_name": null}, {"key": "address_line3", "value": "Earth", "template_name": null}, {"key": "display_name", "value": "Sage One Demo", "template_name": null}, {"key": "tax_rate", "value": "15", "template_name": null}, {"key": "price_inclusive", "value": "true", "template_name": null}, {"key": "price_display", "value": "exclusive", "template_name": null}, {"key": "email", "value": "<EMAIL>", "template_name": null}, {"key": "welcome_html", "value": "<div class=\"row\">\n    <div class=\"col-xs-12 col-md-12\">\n        <a href=\"https://b2b.stock2shop.com/#/products\">\n            <img class=\"img-responsive\" src=\"https://docs.google.com/uc?export=view&id=18cOzEiRBLxBnQEoKQB7CVgTPW60HVlf2\">\n        </a>\n    </div>\n<!--\n    <div class=\"col-xs-12 col-md-12\">\n        <img class=\"img-responsive\" src=\"https://drive.google.com/uc?export=view&id=1h8Etbf_lRlSxUZaZTNzWwSE3fG0wyVWL\">\n    </div>\n-->\n</div>\n<div class=\"row s2s_image\">\n    <div class=\"col-xs-12 col-sm-6 col-md-4\">\n        <div class=\"s2s_image_block\">\n            <a href=\"https://b2b.stock2shop.com/#/products?collection=food%20and%20beverage\">\n                <img class=\"img-responsive\" src=\"https://docs.google.com/uc?export=view&id=1gyN-ccZsmR2pHM1Sh7C_LtWPTzWfC8sy\">\n            </a>\n        </div>\n    </div>\n    <div class=\"col-xs-12 col-sm-6 col-md-4\">\n        <div class=\"s2s_image_block\">\n            <a href=\"https://b2b.stock2shop.com/#/products?collection=toys%20and%20games\">\n                <img class=\"img-responsive\" src=\"https://docs.google.com/uc?export=view&id=1CSXdZlgWUdu-PS0ZcftHyJYnEue-0lu4\">\n            </a>\n        </div>\n    </div>\n    <div class=\"col-xs-12 col-sm-6 col-md-4\">\n        <div class=\"s2s_image_block\">\n            <a href=\"https://b2b.stock2shop.com/#/products?collection=health%20and%20beauty\">\n                <img class=\"img-responsive\" src=\"https://docs.google.com/uc?export=view&id=1B5qQMWrv9_G2z_qD0t8Eidxuf2lEu_90\">\n            </a>\n        </div>\n    </div>\n    <div class=\"col-xs-12 col-sm-6 col-md-4\">\n        <div class=\"s2s_image_block\">\n            <a href=\"https://b2b.stock2shop.com/#/products?collection=electronic\">\n                <img class=\"img-responsive\" src=\"https://docs.google.com/uc?export=view&id=1qg5swEhGnP3RFZ-kujFASQRkfn_MkyeC\">\n            </a>\n        </div>\n    </div>\n    <div class=\"col-xs-12 col-sm-6 col-md-4\">\n        <div class=\"s2s_image_block\">\n            <a href=\"https://b2b.stock2shop.com/#/products?collection=home%20and%20garden\">\n                <img class=\"img-responsive\" src=\"https://docs.google.com/uc?export=view&id=1Is01xGPZOrNi2P5RqLx_kEGlpYvQRAbc\">\n            </a>\n        </div>\n    </div>\n    <div class=\"col-xs-12 col-sm-6 col-md-4\">\n        <div class=\"s2s_image_block\">\n            <a href=\"https://b2b.stock2shop.com/#/products?collection=apparel\">\n                <img class=\"img-responsive\" src=\"https://docs.google.com/uc?export=view&id=1Rxg6PMvynMVm8ponYF2kWJkETDHTbsJs\">\n            </a>\n        </div>\n    </div>\n</div>\n<style class=\"ng-scope\">\n    .s_dashboard .content {\n        padding: 0px;\n    }\n\n    .s_dashboard .s2s_image {\n        padding: 5px 5px 5px 5px;\n        margin: 0px 0px 0px 0px;\n    }\n\n    .s_dashboard .s2s_image>[class*='col-'] {\n        padding: 0px 2px 2px 0px;\n    }\n\n    .s2s_image_block {\n        padding: 0 0px;\n        width: auto;\n    }\n    \n\n</style>", "template_name": null}, {"key": "over_order_enabled", "value": "true", "template_name": null}, {"key": "hide_availability_enabled", "value": "false", "template_name": null}, {"key": "channel_order_code_prefix", "value": "B2B", "template_name": null}, {"key": "queue_fulfill_order", "value": "true", "template_name": null}, {"key": "default_fulfillmentservice_id", "value": "8", "template_name": null}, {"key": "hmac_shared_secret", "value": "57016bf0c81426fb41e2988b077cc1f6", "template_name": null}, {"key": "send_customer_email", "value": "true", "template_name": null}, {"key": "param_collection", "value": "false", "template_name": null}, {"key": "show_availability_units", "value": "true", "template_name": null}, {"key": "checkout_fields", "value": "{\n  \"notes\": {\n    \"description\": \"Notes\",\n    \"type\": \"textarea\",\n    \"required\": false,\n    \"value\": \"Special instructions go here.\"\n  },\n  \"params.customer_reference\": {\n    \"description\": \"Customer Reference\",\n    \"type\": \"text\",\n    \"required\": true,\n    \"value\": \"Order Number:\"\n  },\n  \"params.delivery_date\": {\n        \"description\": \"Requested Delivery Date\",\n        \"type\": \"date\",\n        \"required\": false,\n        \"valid_after_days\": \"7\",\n        \"value\": \"\"\n      }\n}", "template_name": null}, {"key": "edit_shipping_address", "value": "false", "template_name": null}, {"key": "manage_customer_address", "value": "false", "template_name": null}, {"key": "edit_billing_address", "value": "false", "template_name": null}, {"key": "account_display", "value": "{\n  \"Credit Limit\":  \"20000\",\n  \"Balance\":  \"10000\",\n  \"Status\":  \"Active\"\n}", "template_name": null}, {"key": "shipping_methods", "value": "{\n\t\"Next Day\": {\n\t\t\"method\": \"flat_rate\",\n\t\t\"price\": 100,\n\t\t\"tax_lines\": [{\n\t\t\t\"rate\": 15,\n\t\t\t\"price\": 15,\n\t\t\t\"title\": \"VAT\",\n\t\t\t\"code\": \"taxed\"\n\t\t}]\n\t},\n\t\"Same day delivery\": {\n\t\t\"method\": \"flat_rate\",\n\t\t\"price\": 200,\n\t\t\"tax_lines\": [{\n\t\t\t\"rate\": 15,\n\t\t\t\"price\": 28,\n\t\t\t\"title\": \"VAT\",\n\t\t\t\"code\": \"taxed\"\n\t\t}]\n\t},\n\t\"Rate based on cart\": {\n\t\t\"method\": \"table_rate\",\n\t\t\"rates\": [{\n\t\t\t\t\"order_total\": 100,\n\t\t\t\t\"price\": 300,\n\t\t\t\t\"tax_lines\": [{\n\t\t\t\t\t\"rate\": 15,\n\t\t\t\t\t\"price\": 42,\n\t\t\t\t\t\"title\": \"VAT\",\n\t\t\t\t\t\"code\": \"taxed\"\n\t\t\t\t}]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"order_total\": 2000,\n\t\t\t\t\"price\": 200,\n\t\t\t\t\"tax_lines\": [{\n\t\t\t\t\t\"rate\": 15,\n\t\t\t\t\t\"price\": 28,\n\t\t\t\t\t\"title\": \"VAT\",\n\t\t\t\t\t\"code\": \"taxed\"\n\t\t\t\t}]\n\t\t\t}\n\t\t]\n\t}\n}", "template_name": null}, {"key": "display_shipping_address", "value": "false", "template_name": null}, {"key": "order_view_display", "value": "{\n  \"ORDER NO\": \"order.channel_order_code\",\n  \"SALES ORDER NO.\": \"order.sources[0].source_order_code\",\n  \"CUSTOMER REF\": \"order.params.customer_reference\"\n}", "template_name": null}, {"key": "search_by_sku_default", "value": "false", "template_name": null}, {"key": "param_order_type", "value": "sales_order", "template_name": null}, {"key": "param_delivery_date_days", "value": "3", "template_name": null}, {"key": "order_columns", "value": "{\n            \"ORDER NO.\": \"order.id\",\n            \"SALES ORDER NO.\": \"order.sources[0].source_order_code\",\n\"CUSTOMER REF\": \"order.meta.customer_reference\",\n\"ORDER STATUS\": \"order.status\",\n            \"DATE\": \"order.created\",\n            \"TOTAL\": \"order.total\"\n        }", "template_name": null}, {"key": "channel_order_code_sequence", "value": "1149", "template_name": null}, {"key": "payment_methods", "value": "[{\n\t\t\"method\": \"OnAccount\",\n\t\t\"description\": \"On Account\"\n\t},\n\t{\n\t\t\"method\": \"AdumoVirtual\",\n\t\t\"description\": \"Credit Card (Adumo)\",\n\t\t\"iss\": \"Merchant Name PTY\",\n\t\t\"auid\": \"4196B0B8-DB88-42E5-A06D-294A5E4DED87\",\n\t\t\"cuid\": \"9BA5008C-08EE-4286-A349-54AF91A621B0\",\n\t\t\"jwt_secret\": \"yglTxLCSMm7PEsfaMszAKf2LSRvM2qVW\",\n\t\t\"url_staging\": \"https://staging-apiv2.adumoonline.com/product/payment/v1/initialisevirtual\",\n\t\t\"url_production\": \"https://apiv2.adumoonline.com/product/payment/v1/initialisevirtual\",\n\t\t\"in_production\": false,\n\t\t\"currency\": \"zar\"\n\t},\n\n\t{\n\t\t\"method\": \"Stripe\",\n\t\t\"description\": \"Credit Card (Stripe)\",\n\t\t\"api_pk\": \"pk_test_76ovISW6bzckxoXoQoYaHnv400XUJi0u8s\",\n\t\t\"api_sk\": \"sk_test_eSfGmlOpmjRv5b1f6jKoD2Du00TAbUYxxy\",\n\t\t\"test_mode\": \"false\",\n\t\t\"currency\": \"usd\",\n\t\t\"currency_display\": \"$\"\n\t}\n]", "template_name": null}]}, "47": {"flags": [{"client_id": 21, "channel_id": 47, "source_id": 0, "table": "product", "column": "body_html", "description": "Description", "code": "system"}, {"client_id": 21, "channel_id": 47, "source_id": 0, "table": "product", "column": "collection", "description": "Collection", "code": "system"}, {"client_id": 21, "channel_id": 47, "source_id": 0, "table": "product", "column": "title", "description": "Title", "code": "system"}, {"client_id": 21, "channel_id": 47, "source_id": 0, "table": "product", "column": "options", "description": "Options", "code": "system"}, {"client_id": 21, "channel_id": 47, "source_id": 0, "table": "product", "column": "product_type", "description": "Type", "code": "system"}, {"client_id": 21, "channel_id": 47, "source_id": 0, "table": "product", "column": "tags", "description": "Tags", "code": "system"}, {"client_id": 21, "channel_id": 47, "source_id": 0, "table": "product", "column": "vendor", "description": "<PERSON><PERSON><PERSON>", "code": "system"}, {"client_id": 21, "channel_id": 47, "source_id": 0, "table": "variant", "column": "price", "description": "Price", "code": "system"}, {"client_id": 21, "channel_id": 47, "source_id": 0, "table": "variant", "column": "barcode", "description": "Barcode", "code": "system"}, {"client_id": 21, "channel_id": 47, "source_id": 0, "table": "variant", "column": "qty", "description": "QTY", "code": "system"}, {"client_id": 21, "channel_id": 47, "source_id": 0, "table": "variant", "column": "option1", "description": "Option 1", "code": "system"}, {"client_id": 21, "channel_id": 47, "source_id": 0, "table": "variant", "column": "option2", "description": "Option 2", "code": "system"}, {"client_id": 21, "channel_id": 47, "source_id": 0, "table": "variant", "column": "option3", "description": "Option 3", "code": "system"}, {"client_id": 21, "channel_id": 47, "source_id": 0, "table": "variant", "column": "grams", "description": "Grams", "code": "system"}, {"client_id": 21, "channel_id": 47, "source_id": 0, "table": "variant", "column": "inventory_management", "description": "Inventory Management", "code": "system"}, {"client_id": 21, "channel_id": 47, "source_id": 0, "table": "product", "column": "meta", "description": "Attributes", "code": "system"}, {"client_id": 21, "channel_id": 47, "source_id": 0, "table": "variant", "column": "sku", "description": "SKU", "code": "system"}], "rules": [], "sources": {"57": {"id": 57, "source_id": null, "description": "Sage One Demo", "client_id": 21, "type": "sageone", "sync_token": "2023-06-07T09:55:17.803", "active": true, "meta": [{"key": "create_order_enabled", "value": "true", "template_name": null}, {"key": "company_id", "value": "115214", "template_name": null}, {"key": "username", "value": "<EMAIL>", "template_name": null}, {"key": "password", "value": "juj-mash-yeV-1", "template_name": null}, {"key": "get_products_limit", "value": "100", "template_name": null}, {"key": "get_images_limit", "value": "1", "template_name": null}, {"key": "param_tax_code", "value": "2718926", "template_name": null}, {"key": "param_tax_exempt_code", "value": "0", "template_name": null}, {"key": "param_use_customer_address", "value": "false", "template_name": null}, {"key": "param_send_invoice_enabled", "value": "true", "template_name": null}, {"key": "product_field_map", "value": "{\n\t\"source\": {\n\t\t\"source_product_code\": \"{{TextUserField1}}\",\n\t\t\"product_active\": {{# YesNoUserField1}}true{{/ YesNoUserField1}}{{^ YesNoUserField1}}false{{/YesNoUserField1}}\n\t},\n\t\"product\": {\n\t\t\"collection\": \"{{Category.Description}}\",\n\t\t\"title\": \"{{{Description}}}\",\n\t\t\"product_type\": \"{{TextUserField2}}\",\n\t\t\"body_html\": \"\",\n\t\t\"tags\": \"\",\n\t\t\"vendor\": \"\",\n\t\t\"options\": [{\n\t\t\t\"value\": \"\",\n\t\t\t\"name\": \"Selection\",\n\t\t\t\"position\": 1\n\t\t}],\n\t\t\"variants\": {\n\t\t\t\"source_variant_code\": \"{{ID}}\",\n\t\t\t\"option1\": \"{{TextUserField3}}\",\n\t\t\t{{#QuantityReserved}}\t\n\t\t\t\"qty\": \"{{QuantityOnHand}}-{{QuantityReserved}}\",\n\t\t\t{{/QuantityReserved}}\n\t\t\t{{^QuantityReserved}}\n\t  \t\t\"qty\": \"{{QuantityOnHand}}\",\n\t \t    {{/QuantityReserved}}\n\t\t\t\"sku\": \"{{Code}}\",\n\t\t\t\"price\": \"{{PriceInclusive}}\",\n\t\t\t\"price_tiers\": [\n\t\t\t  {{#AdditionalItemPrices}} \n\t\t\t  \t\t{\n\t\t\t\t\t\t\"tier\": \"{{description}}\",\n\t\t\t\t\t\t\"price\": \"{{PriceInclusive}}\"\n\t\t\t\t\t} {{comma}} \n\t  \t\t\t\t{{/ AdditionalItemPrices}}\n\t\t\t\t\t  ],\t\t\t \t\t\t\t\t\n\t\t\t \"inventory_management\": true\t\t\n\t\t\t\t\t }\t \t\t\t\t  \n\t\t\t\t\t}\t}", "template_name": null}, {"key": "param_default_shipping_code", "value": "SHIP001", "template_name": null}, {"key": "param_get_images_enabled", "value": "true", "template_name": null}, {"key": "param_get_products_filter", "value": "Physical eq true and TextUserField1  ne ''", "template_name": null}, {"key": "cron_get_products_schedule", "value": "04 * * * *", "template_name": null}, {"key": "param_channel_order_code_enabled", "value": "false", "template_name": null}, {"key": "customer_reference_field", "value": "channel_order_code", "template_name": null}, {"key": "order_map", "value": "{\n{{^ params.use_customer_address}}\n  \"PostalAddress01\": \"{{system_order.billing_address.first_name}} {{system_order.billing_address.last_name}}\",\n  \"PostalAddress02\": \"{{system_order.billing_address.address1}} {{system_order.billing_address.address2}}\",\n  \"PostalAddress03\": \"{{system_order.billing_address.city}} {{system_order.billing_address.phone}}\",\n  \"PostalAddress04\": \"{{system_order.customer.email}}\",\n  \"DeliveryAddress01\": \"{{system_order.shipping_address.first_name}} {{system_order.shipping_address.last_name}}\",\n  \"DeliveryAddress02\": \"{{system_order.shipping_address.address1}} {{system_order.shipping_address.address2}}\",\n  \"DeliveryAddress03\": \"{{system_order.shipping_address.city}} {{system_order.shipping_address.phone}}\",\n  \"DeliveryAddress04\": \"{{system_order.customer.email}}\",\n{{/ params.use_customer_address}}\n\"message\": \"Bank: Standard Bank                                                                                                                 Branch: Sandton City                                                                                                                          Branch Code: 018105                                                                                                       Account Number: *********                                                                                                                        Please quote your invoice number as reference                                                                                  Notes: {{system_order.notes}}\"                                                           \n}", "template_name": null}, {"key": "sync_mode", "value": "pull", "template_name": null}, {"key": "queue_fetch_images", "value": "false", "template_name": null}, {"key": "param_sales_rep_id", "value": "25427", "template_name": null}, {"key": "param_create_customer_enabled", "value": "true", "template_name": null}, {"key": "order_created_date_now", "value": "false", "template_name": null}, {"key": "param_due_date_days", "value": "3", "template_name": null}, {"key": "param_negative_stock_disabled", "value": "false", "template_name": null}, {"key": "param_default_customer_code", "value": "Cash", "template_name": null}, {"key": "config_notes", "value": "<p>test2</p>", "template_name": null}, {"key": "cron_custom_ftp_processor_enabled", "value": "false", "template_name": null}, {"key": "cron_custom_ftp_processor_schedule", "value": "x 55 * * * *", "template_name": null}, {"key": "order_type", "value": "sales_order", "template_name": null}]}}, "id": 47, "description": "B2B Wholesale", "client_id": 21, "active": true, "type": "trade", "price_tier": null, "qty_availability": null, "sync_token": null, "meta": [{"key": "login_redirect", "value": "https://b2b.stock2shop.com", "template_name": null}, {"key": "phone", "value": "086010101010", "template_name": null}, {"key": "address_line1", "value": "103 Sunny St", "template_name": null}, {"key": "address_line2", "value": "Sunnyville", "template_name": null}, {"key": "address_line3", "value": "Earth", "template_name": null}, {"key": "display_name", "value": "Sage One Demo", "template_name": null}, {"key": "tax_rate", "value": "15", "template_name": null}, {"key": "price_inclusive", "value": "true", "template_name": null}, {"key": "price_display", "value": "exclusive", "template_name": null}, {"key": "email", "value": "<EMAIL>", "template_name": null}, {"key": "welcome_html", "value": "<div class=\"row\">\n    <div class=\"col-xs-12 col-md-12\">\n        <a href=\"https://b2b.stock2shop.com/#/products\">\n            <img class=\"img-responsive\" src=\"https://docs.google.com/uc?export=view&id=18cOzEiRBLxBnQEoKQB7CVgTPW60HVlf2\">\n        </a>\n    </div>\n<!--\n    <div class=\"col-xs-12 col-md-12\">\n        <img class=\"img-responsive\" src=\"https://drive.google.com/uc?export=view&id=1h8Etbf_lRlSxUZaZTNzWwSE3fG0wyVWL\">\n    </div>\n-->\n</div>\n<div class=\"row s2s_image\">\n    <div class=\"col-xs-12 col-sm-6 col-md-4\">\n        <div class=\"s2s_image_block\">\n            <a href=\"https://b2b.stock2shop.com/#/products?collection=food%20and%20beverage\">\n                <img class=\"img-responsive\" src=\"https://docs.google.com/uc?export=view&id=1gyN-ccZsmR2pHM1Sh7C_LtWPTzWfC8sy\">\n            </a>\n        </div>\n    </div>\n    <div class=\"col-xs-12 col-sm-6 col-md-4\">\n        <div class=\"s2s_image_block\">\n            <a href=\"https://b2b.stock2shop.com/#/products?collection=toys%20and%20games\">\n                <img class=\"img-responsive\" src=\"https://docs.google.com/uc?export=view&id=1CSXdZlgWUdu-PS0ZcftHyJYnEue-0lu4\">\n            </a>\n        </div>\n    </div>\n    <div class=\"col-xs-12 col-sm-6 col-md-4\">\n        <div class=\"s2s_image_block\">\n            <a href=\"https://b2b.stock2shop.com/#/products?collection=health%20and%20beauty\">\n                <img class=\"img-responsive\" src=\"https://docs.google.com/uc?export=view&id=1B5qQMWrv9_G2z_qD0t8Eidxuf2lEu_90\">\n            </a>\n        </div>\n    </div>\n    <div class=\"col-xs-12 col-sm-6 col-md-4\">\n        <div class=\"s2s_image_block\">\n            <a href=\"https://b2b.stock2shop.com/#/products?collection=electronic\">\n                <img class=\"img-responsive\" src=\"https://docs.google.com/uc?export=view&id=1qg5swEhGnP3RFZ-kujFASQRkfn_MkyeC\">\n            </a>\n        </div>\n    </div>\n    <div class=\"col-xs-12 col-sm-6 col-md-4\">\n        <div class=\"s2s_image_block\">\n            <a href=\"https://b2b.stock2shop.com/#/products?collection=home%20and%20garden\">\n                <img class=\"img-responsive\" src=\"https://docs.google.com/uc?export=view&id=1Is01xGPZOrNi2P5RqLx_kEGlpYvQRAbc\">\n            </a>\n        </div>\n    </div>\n    <div class=\"col-xs-12 col-sm-6 col-md-4\">\n        <div class=\"s2s_image_block\">\n            <a href=\"https://b2b.stock2shop.com/#/products?collection=apparel\">\n                <img class=\"img-responsive\" src=\"https://docs.google.com/uc?export=view&id=1Rxg6PMvynMVm8ponYF2kWJkETDHTbsJs\">\n            </a>\n        </div>\n    </div>\n</div>\n<style class=\"ng-scope\">\n    .s_dashboard .content {\n        padding: 0px;\n    }\n\n    .s_dashboard .s2s_image {\n        padding: 5px 5px 5px 5px;\n        margin: 0px 0px 0px 0px;\n    }\n\n    .s_dashboard .s2s_image>[class*='col-'] {\n        padding: 0px 2px 2px 0px;\n    }\n\n    .s2s_image_block {\n        padding: 0 0px;\n        width: auto;\n    }\n    \n\n</style>", "template_name": null}, {"key": "over_order_enabled", "value": "true", "template_name": null}, {"key": "hide_availability_enabled", "value": "false", "template_name": null}, {"key": "channel_order_code_prefix", "value": "B2B", "template_name": null}, {"key": "queue_fulfill_order", "value": "true", "template_name": null}, {"key": "default_fulfillmentservice_id", "value": "8", "template_name": null}, {"key": "hmac_shared_secret", "value": "57016bf0c81426fb41e2988b077cc1f6", "template_name": null}, {"key": "send_customer_email", "value": "true", "template_name": null}, {"key": "param_collection", "value": "false", "template_name": null}, {"key": "show_availability_units", "value": "true", "template_name": null}, {"key": "checkout_fields", "value": "{\n  \"notes\": {\n    \"description\": \"Notes\",\n    \"type\": \"textarea\",\n    \"required\": false,\n    \"value\": \"Special instructions go here.\"\n  },\n  \"params.customer_reference\": {\n    \"description\": \"Customer Reference\",\n    \"type\": \"text\",\n    \"required\": true,\n    \"value\": \"Order Number:\"\n  },\n  \"params.delivery_date\": {\n        \"description\": \"Requested Delivery Date\",\n        \"type\": \"date\",\n        \"required\": false,\n        \"valid_after_days\": \"7\",\n        \"value\": \"\"\n      }\n}", "template_name": null}, {"key": "edit_shipping_address", "value": "false", "template_name": null}, {"key": "manage_customer_address", "value": "false", "template_name": null}, {"key": "edit_billing_address", "value": "false", "template_name": null}, {"key": "account_display", "value": "{\n  \"Credit Limit\":  \"20000\",\n  \"Balance\":  \"10000\",\n  \"Status\":  \"Active\"\n}", "template_name": null}, {"key": "shipping_methods", "value": "{\n\t\"Next Day\": {\n\t\t\"method\": \"flat_rate\",\n\t\t\"price\": 100,\n\t\t\"tax_lines\": [{\n\t\t\t\"rate\": 15,\n\t\t\t\"price\": 15,\n\t\t\t\"title\": \"VAT\",\n\t\t\t\"code\": \"taxed\"\n\t\t}]\n\t},\n\t\"Same day delivery\": {\n\t\t\"method\": \"flat_rate\",\n\t\t\"price\": 200,\n\t\t\"tax_lines\": [{\n\t\t\t\"rate\": 15,\n\t\t\t\"price\": 28,\n\t\t\t\"title\": \"VAT\",\n\t\t\t\"code\": \"taxed\"\n\t\t}]\n\t},\n\t\"Rate based on cart\": {\n\t\t\"method\": \"table_rate\",\n\t\t\"rates\": [{\n\t\t\t\t\"order_total\": 100,\n\t\t\t\t\"price\": 300,\n\t\t\t\t\"tax_lines\": [{\n\t\t\t\t\t\"rate\": 15,\n\t\t\t\t\t\"price\": 42,\n\t\t\t\t\t\"title\": \"VAT\",\n\t\t\t\t\t\"code\": \"taxed\"\n\t\t\t\t}]\n\t\t\t},\n\t\t\t{\n\t\t\t\t\"order_total\": 2000,\n\t\t\t\t\"price\": 200,\n\t\t\t\t\"tax_lines\": [{\n\t\t\t\t\t\"rate\": 15,\n\t\t\t\t\t\"price\": 28,\n\t\t\t\t\t\"title\": \"VAT\",\n\t\t\t\t\t\"code\": \"taxed\"\n\t\t\t\t}]\n\t\t\t}\n\t\t]\n\t}\n}", "template_name": null}, {"key": "display_shipping_address", "value": "false", "template_name": null}, {"key": "order_view_display", "value": "{\n  \"ORDER NO\": \"order.channel_order_code\",\n  \"SALES ORDER NO.\": \"order.sources[0].source_order_code\",\n  \"CUSTOMER REF\": \"order.params.customer_reference\"\n}", "template_name": null}, {"key": "search_by_sku_default", "value": "false", "template_name": null}, {"key": "param_order_type", "value": "sales_order", "template_name": null}, {"key": "param_delivery_date_days", "value": "3", "template_name": null}, {"key": "order_columns", "value": "{\n            \"ORDER NO.\": \"order.id\",\n            \"SALES ORDER NO.\": \"order.sources[0].source_order_code\",\n\"CUSTOMER REF\": \"order.meta.customer_reference\",\n\"ORDER STATUS\": \"order.status\",\n            \"DATE\": \"order.created\",\n            \"TOTAL\": \"order.total\"\n        }", "template_name": null}, {"key": "channel_order_code_sequence", "value": "1149", "template_name": null}, {"key": "payment_methods", "value": "[{\n\t\t\"method\": \"OnAccount\",\n\t\t\"description\": \"On Account\"\n\t},\n\t{\n\t\t\"method\": \"AdumoVirtual\",\n\t\t\"description\": \"Credit Card (Adumo)\",\n\t\t\"iss\": \"Merchant Name PTY\",\n\t\t\"auid\": \"4196B0B8-DB88-42E5-A06D-294A5E4DED87\",\n\t\t\"cuid\": \"9BA5008C-08EE-4286-A349-54AF91A621B0\",\n\t\t\"jwt_secret\": \"yglTxLCSMm7PEsfaMszAKf2LSRvM2qVW\",\n\t\t\"url_staging\": \"https://staging-apiv2.adumoonline.com/product/payment/v1/initialisevirtual\",\n\t\t\"url_production\": \"https://apiv2.adumoonline.com/product/payment/v1/initialisevirtual\",\n\t\t\"in_production\": false,\n\t\t\"currency\": \"zar\"\n\t},\n\n\t{\n\t\t\"method\": \"Stripe\",\n\t\t\"description\": \"Credit Card (Stripe)\",\n\t\t\"api_pk\": \"pk_test_76ovISW6bzckxoXoQoYaHnv400XUJi0u8s\",\n\t\t\"api_sk\": \"sk_test_eSfGmlOpmjRv5b1f6jKoD2Du00TAbUYxxy\",\n\t\t\"test_mode\": \"false\",\n\t\t\"currency\": \"usd\",\n\t\t\"currency_display\": \"$\"\n\t}\n]", "template_name": null}]}, "197": {"flags": [{"client_id": 21, "channel_id": 197, "source_id": 0, "table": "product", "column": "body_html", "description": "Description", "code": "system"}, {"client_id": 21, "channel_id": 197, "source_id": 0, "table": "product", "column": "collection", "description": "Collection", "code": "system"}, {"client_id": 21, "channel_id": 197, "source_id": 0, "table": "product", "column": "title", "description": "Title", "code": "system"}, {"client_id": 21, "channel_id": 197, "source_id": 0, "table": "product", "column": "options", "description": "Options", "code": "channel"}, {"client_id": 21, "channel_id": 197, "source_id": 0, "table": "product", "column": "product_type", "description": "Type", "code": "channel"}, {"client_id": 21, "channel_id": 197, "source_id": 0, "table": "product", "column": "tags", "description": "Tags", "code": "system"}, {"client_id": 21, "channel_id": 197, "source_id": 0, "table": "product", "column": "vendor", "description": "<PERSON><PERSON><PERSON>", "code": "system"}, {"client_id": 21, "channel_id": 197, "source_id": 0, "table": "product", "column": "meta", "description": "Attributes", "code": "channel"}, {"client_id": 21, "channel_id": 197, "source_id": 0, "table": "variant", "column": "sku", "description": "SKU", "code": "system"}, {"client_id": 21, "channel_id": 197, "source_id": 0, "table": "variant", "column": "price", "description": "Price", "code": "system"}, {"client_id": 21, "channel_id": 197, "source_id": 0, "table": "variant", "column": "barcode", "description": "Barcode", "code": "channel"}, {"client_id": 21, "channel_id": 197, "source_id": 0, "table": "variant", "column": "qty", "description": "QTY", "code": "system"}, {"client_id": 21, "channel_id": 197, "source_id": 0, "table": "variant", "column": "option1", "description": "Option 1", "code": "system"}, {"client_id": 21, "channel_id": 197, "source_id": 0, "table": "variant", "column": "option2", "description": "Option 2", "code": "system"}, {"client_id": 21, "channel_id": 197, "source_id": 0, "table": "variant", "column": "option3", "description": "Option 3", "code": "system"}, {"client_id": 21, "channel_id": 197, "source_id": 0, "table": "variant", "column": "grams", "description": "Grams", "code": "channel"}, {"client_id": 21, "channel_id": 197, "source_id": 0, "table": "variant", "column": "inventory_management", "description": "Inventory Management", "code": "system"}], "rules": [], "sources": {"57": {"id": 57, "source_id": null, "description": "Sage One Demo", "client_id": 21, "type": "sageone", "sync_token": "2023-06-07T09:55:17.803", "active": true, "meta": [{"key": "create_order_enabled", "value": "true", "template_name": null}, {"key": "company_id", "value": "115214", "template_name": null}, {"key": "username", "value": "<EMAIL>", "template_name": null}, {"key": "password", "value": "juj-mash-yeV-1", "template_name": null}, {"key": "get_products_limit", "value": "100", "template_name": null}, {"key": "get_images_limit", "value": "1", "template_name": null}, {"key": "param_tax_code", "value": "2718926", "template_name": null}, {"key": "param_tax_exempt_code", "value": "0", "template_name": null}, {"key": "param_use_customer_address", "value": "false", "template_name": null}, {"key": "param_send_invoice_enabled", "value": "true", "template_name": null}, {"key": "product_field_map", "value": "{\n\t\"source\": {\n\t\t\"source_product_code\": \"{{TextUserField1}}\",\n\t\t\"product_active\": {{# YesNoUserField1}}true{{/ YesNoUserField1}}{{^ YesNoUserField1}}false{{/YesNoUserField1}}\n\t},\n\t\"product\": {\n\t\t\"collection\": \"{{Category.Description}}\",\n\t\t\"title\": \"{{{Description}}}\",\n\t\t\"product_type\": \"{{TextUserField2}}\",\n\t\t\"body_html\": \"\",\n\t\t\"tags\": \"\",\n\t\t\"vendor\": \"\",\n\t\t\"options\": [{\n\t\t\t\"value\": \"\",\n\t\t\t\"name\": \"Selection\",\n\t\t\t\"position\": 1\n\t\t}],\n\t\t\"variants\": {\n\t\t\t\"source_variant_code\": \"{{ID}}\",\n\t\t\t\"option1\": \"{{TextUserField3}}\",\n\t\t\t{{#QuantityReserved}}\t\n\t\t\t\"qty\": \"{{QuantityOnHand}}-{{QuantityReserved}}\",\n\t\t\t{{/QuantityReserved}}\n\t\t\t{{^QuantityReserved}}\n\t  \t\t\"qty\": \"{{QuantityOnHand}}\",\n\t \t    {{/QuantityReserved}}\n\t\t\t\"sku\": \"{{Code}}\",\n\t\t\t\"price\": \"{{PriceInclusive}}\",\n\t\t\t\"price_tiers\": [\n\t\t\t  {{#AdditionalItemPrices}} \n\t\t\t  \t\t{\n\t\t\t\t\t\t\"tier\": \"{{description}}\",\n\t\t\t\t\t\t\"price\": \"{{PriceInclusive}}\"\n\t\t\t\t\t} {{comma}} \n\t  \t\t\t\t{{/ AdditionalItemPrices}}\n\t\t\t\t\t  ],\t\t\t \t\t\t\t\t\n\t\t\t \"inventory_management\": true\t\t\n\t\t\t\t\t }\t \t\t\t\t  \n\t\t\t\t\t}\t}", "template_name": null}, {"key": "param_default_shipping_code", "value": "SHIP001", "template_name": null}, {"key": "param_get_images_enabled", "value": "true", "template_name": null}, {"key": "param_get_products_filter", "value": "Physical eq true and TextUserField1  ne ''", "template_name": null}, {"key": "cron_get_products_schedule", "value": "04 * * * *", "template_name": null}, {"key": "param_channel_order_code_enabled", "value": "false", "template_name": null}, {"key": "customer_reference_field", "value": "channel_order_code", "template_name": null}, {"key": "order_map", "value": "{\n{{^ params.use_customer_address}}\n  \"PostalAddress01\": \"{{system_order.billing_address.first_name}} {{system_order.billing_address.last_name}}\",\n  \"PostalAddress02\": \"{{system_order.billing_address.address1}} {{system_order.billing_address.address2}}\",\n  \"PostalAddress03\": \"{{system_order.billing_address.city}} {{system_order.billing_address.phone}}\",\n  \"PostalAddress04\": \"{{system_order.customer.email}}\",\n  \"DeliveryAddress01\": \"{{system_order.shipping_address.first_name}} {{system_order.shipping_address.last_name}}\",\n  \"DeliveryAddress02\": \"{{system_order.shipping_address.address1}} {{system_order.shipping_address.address2}}\",\n  \"DeliveryAddress03\": \"{{system_order.shipping_address.city}} {{system_order.shipping_address.phone}}\",\n  \"DeliveryAddress04\": \"{{system_order.customer.email}}\",\n{{/ params.use_customer_address}}\n\"message\": \"Bank: Standard Bank                                                                                                                 Branch: Sandton City                                                                                                                          Branch Code: 018105                                                                                                       Account Number: *********                                                                                                                        Please quote your invoice number as reference                                                                                  Notes: {{system_order.notes}}\"                                                           \n}", "template_name": null}, {"key": "sync_mode", "value": "pull", "template_name": null}, {"key": "queue_fetch_images", "value": "false", "template_name": null}, {"key": "param_sales_rep_id", "value": "25427", "template_name": null}, {"key": "param_create_customer_enabled", "value": "true", "template_name": null}, {"key": "order_created_date_now", "value": "false", "template_name": null}, {"key": "param_due_date_days", "value": "3", "template_name": null}, {"key": "param_negative_stock_disabled", "value": "false", "template_name": null}, {"key": "param_default_customer_code", "value": "Cash", "template_name": null}, {"key": "config_notes", "value": "<p>test2</p>", "template_name": null}, {"key": "cron_custom_ftp_processor_enabled", "value": "false", "template_name": null}, {"key": "cron_custom_ftp_processor_schedule", "value": "x 55 * * * *", "template_name": null}, {"key": "order_type", "value": "sales_order", "template_name": null}]}}, "id": 197, "description": "Stock2Shop Shopify", "client_id": 21, "active": true, "type": "shopify", "price_tier": null, "qty_availability": null, "sync_token": null, "meta": [{"key": "api_key", "value": "1323b776d87400377d31b86818eb1577", "template_name": null}, {"key": "channel_domain", "value": "stock2shop.myshopify.com", "template_name": null}, {"key": "password", "value": "19ba2e2c83295a9f773285cc138f49d7", "template_name": null}, {"key": "queue_fulfill_order", "value": "true", "template_name": null}, {"key": "add_all_orders", "value": "false", "template_name": null}, {"key": "default_fulfillmentservice_id", "value": "8", "template_name": null}, {"key": "notes_template", "value": "VAT: {{{customer.email}}} \nCompany: {{{shipping_first_name}}}", "template_name": null}, {"key": "delete_product_enabled", "value": "true", "template_name": null}, {"key": "auto_sync", "value": "true", "template_name": null}, {"key": "use_location_api", "value": "true", "template_name": null}, {"key": "location_id", "value": "9406152", "template_name": null}, {"key": "hide_product_enabled", "value": "false", "template_name": null}, {"key": "fulfillment_map", "value": "{\n  \"fulfillment\": {\n    \"tracking_number\": \"{{tracking_number}}\",\n    \"tracking_company\": \"{{tracking_company}}\",\n    \"tracking_url\": \"{{tracking_url}}\",\n    \"notify_customer\": true\n  }\n}", "template_name": null}, {"key": "compare_at_price", "value": "{{price_tiers.Was.price}}", "template_name": null}, {"key": "order_use_system_price", "value": "false", "template_name": null}, {"key": "check_order_items_linked", "value": "false", "template_name": null}, {"key": "location_map", "value": "{\n  \"35496919098\": \"{{# calculate}}{{qty_availability.jhb}}-1{{/ calculate}}\",\n  \"60914499642\": \"{{qty_availability.pta}}\"\n}", "template_name": null}, {"key": "pargo", "value": "true", "template_name": null}, {"key": "cost_price", "value": "{{price_tiers.Cost.price}}", "template_name": null}, {"key": "order_type", "value": "tax_invoice", "template_name": null}, {"key": "param_order_type", "value": "tax_invoice", "template_name": null}, {"key": "inventory_management", "value": "shopify", "template_name": null}, {"key": "config_notes", "value": "<p>Shopify Access Token:&nbsp;19ba2e2c83295a9f773285cc138f49d7</p>", "template_name": null}, {"key": "access_token", "value": "19ba2e2c83295a9f773285cc138f49d7", "template_name": null}, {"key": "api_version", "value": "2023-01", "template_name": null}]}, "1649": {"flags": [{"client_id": 21, "channel_id": 1649, "source_id": 0, "table": "product", "column": "body_html", "description": "Description", "code": "system"}, {"client_id": 21, "channel_id": 1649, "source_id": 0, "table": "product", "column": "collection", "description": "Collection", "code": "system"}, {"client_id": 21, "channel_id": 1649, "source_id": 0, "table": "product", "column": "title", "description": "Title", "code": "system"}, {"client_id": 21, "channel_id": 1649, "source_id": 0, "table": "product", "column": "options", "description": "Options", "code": "system"}, {"client_id": 21, "channel_id": 1649, "source_id": 0, "table": "product", "column": "product_type", "description": "Type", "code": "system"}, {"client_id": 21, "channel_id": 1649, "source_id": 0, "table": "product", "column": "tags", "description": "Tags", "code": "system"}, {"client_id": 21, "channel_id": 1649, "source_id": 0, "table": "product", "column": "vendor", "description": "<PERSON><PERSON><PERSON>", "code": "system"}, {"client_id": 21, "channel_id": 1649, "source_id": 0, "table": "product", "column": "meta", "description": "Attributes", "code": "system"}, {"client_id": 21, "channel_id": 1649, "source_id": 0, "table": "variant", "column": "sku", "description": "SKU", "code": "system"}, {"client_id": 21, "channel_id": 1649, "source_id": 0, "table": "variant", "column": "price", "description": "Price", "code": "system"}, {"client_id": 21, "channel_id": 1649, "source_id": 0, "table": "variant", "column": "barcode", "description": "Barcode", "code": "system"}, {"client_id": 21, "channel_id": 1649, "source_id": 0, "table": "variant", "column": "qty", "description": "QTY", "code": "system"}, {"client_id": 21, "channel_id": 1649, "source_id": 0, "table": "variant", "column": "option1", "description": "Option 1", "code": "system"}, {"client_id": 21, "channel_id": 1649, "source_id": 0, "table": "variant", "column": "option2", "description": "Option 2", "code": "system"}, {"client_id": 21, "channel_id": 1649, "source_id": 0, "table": "variant", "column": "option3", "description": "Option 3", "code": "system"}, {"client_id": 21, "channel_id": 1649, "source_id": 0, "table": "variant", "column": "grams", "description": "Grams", "code": "system"}, {"client_id": 21, "channel_id": 1649, "source_id": 0, "table": "variant", "column": "inventory_management", "description": "Inventory Management", "code": "system"}], "rules": [{"client_id": 21, "channel_id": 1649, "key": "variant.barcode", "value": "", "operator": "is not empty"}], "sources": [], "id": 1649, "description": "<PERSON><PERSON><PERSON> [Not use]", "client_id": 21, "active": true, "type": "mirakl", "price_tier": null, "qty_availability": null, "sync_token": "2022-01-18 13:04:11.856171", "meta": [{"key": "api_key", "value": "xxxx 67a24df1-17bb-41ca-b019-106877e15938", "template_name": null}, {"key": "sync_mode", "value": "bulk", "template_name": null}, {"key": "url", "value": "xxxx https://leroymerlinza-preprod.mirakl.net", "template_name": null}, {"key": "cron_sync_channel_bulk_schedule", "value": "37 * * * *", "template_name": null}, {"key": "offer_map", "value": "<offer>     <sku>{{variants.sku}}</sku>     <product-id>{{variants.barcode}}</product-id>     <product-id-type>EAN</product-id-type>     <description>Great Deal!</description>     <price>{{variants.price}}</price>     <discount-price>{{meta.mirakl_discount_price}}</discount-price>     <price-additional-info>Price including taxes</price-additional-info>     <quantity>{{variants.qty}}</quantity>     <state>11</state> </offer>", "template_name": null}]}, "1881": {"flags": [{"client_id": 21, "channel_id": 1881, "source_id": 0, "table": "product", "column": "body_html", "description": "Description", "code": "system"}, {"client_id": 21, "channel_id": 1881, "source_id": 0, "table": "product", "column": "collection", "description": "Collection", "code": "system"}, {"client_id": 21, "channel_id": 1881, "source_id": 0, "table": "product", "column": "title", "description": "Title", "code": "system"}, {"client_id": 21, "channel_id": 1881, "source_id": 0, "table": "product", "column": "options", "description": "Options", "code": "system"}, {"client_id": 21, "channel_id": 1881, "source_id": 0, "table": "product", "column": "product_type", "description": "Type", "code": "system"}, {"client_id": 21, "channel_id": 1881, "source_id": 0, "table": "product", "column": "tags", "description": "Tags", "code": "system"}, {"client_id": 21, "channel_id": 1881, "source_id": 0, "table": "product", "column": "vendor", "description": "<PERSON><PERSON><PERSON>", "code": "system"}, {"client_id": 21, "channel_id": 1881, "source_id": 0, "table": "product", "column": "meta", "description": "Attributes", "code": "system"}, {"client_id": 21, "channel_id": 1881, "source_id": 0, "table": "variant", "column": "sku", "description": "SKU", "code": "system"}, {"client_id": 21, "channel_id": 1881, "source_id": 0, "table": "variant", "column": "price", "description": "Price", "code": "system"}, {"client_id": 21, "channel_id": 1881, "source_id": 0, "table": "variant", "column": "barcode", "description": "Barcode", "code": "system"}, {"client_id": 21, "channel_id": 1881, "source_id": 0, "table": "variant", "column": "qty", "description": "QTY", "code": "system"}, {"client_id": 21, "channel_id": 1881, "source_id": 0, "table": "variant", "column": "option1", "description": "Option 1", "code": "system"}, {"client_id": 21, "channel_id": 1881, "source_id": 0, "table": "variant", "column": "option2", "description": "Option 2", "code": "system"}, {"client_id": 21, "channel_id": 1881, "source_id": 0, "table": "variant", "column": "option3", "description": "Option 3", "code": "system"}, {"client_id": 21, "channel_id": 1881, "source_id": 0, "table": "variant", "column": "grams", "description": "Grams", "code": "system"}, {"client_id": 21, "channel_id": 1881, "source_id": 0, "table": "variant", "column": "inventory_management", "description": "Inventory Management", "code": "system"}], "rules": [], "sources": [], "id": 1881, "description": "TEST B2B Trade Store [not in use]", "client_id": 21, "active": true, "type": "trade", "price_tier": null, "qty_availability": null, "sync_token": "0", "meta": [{"key": "account_display", "value": "{\n  \"Some Text\": \"abc\",\n  \"Balance\": \"meta_balance\",\n  \"Credit Limit\": \"meta_credit_limit\",\n  \"Sales Person\": \"meta_sales_person\"\n}", "template_name": null}, {"key": "address_line1", "value": "x", "template_name": null}, {"key": "address_line2", "value": "x", "template_name": null}, {"key": "address_line3", "value": "x", "template_name": null}, {"key": "aggregations", "value": "{ \t\"Department\": { \t\t\"nested\": { \t\t\t\"path\": \"meta\" \t\t}, \t\t\"aggs\": { \t\t\t\"Department\": { \t\t\t\t\"aggs\": { \t\t\t\t\t\"Department\": { \t\t\t\t\t\t\"terms\": { \t\t\t\t\t\t\t\"field\": \"meta.value\", \t\t\t\t\t\t\t\"exclude\": \"\", \t\t\t\t\t\t\t\"order\": { \t\t\t\t\t\t\t\t\"_term\": \"asc\" \t\t\t\t\t\t\t}, \t\t\t\t\t\t\t\"size\": 200 \t\t\t\t\t\t} \t\t\t\t\t} \t\t\t\t}, \t\t\t\t\"filter\": { \t\t\t\t\t\"term\": { \t\t\t\t\t\t\"meta.key\": \"department\" \t\t\t\t\t} \t\t\t\t} \t\t\t} \t\t} \t}, \t\"Collection\": { \t\t\"terms\": { \t\t\t\"field\": \"collection\", \t\t\t\"exclude\": \"\", \t\t\t\"order\": { \t\t\t\t\"_term\": \"asc\" \t\t\t}, \t\t\t\"size\": 200 \t\t} \t}, \t\"Product Type\": { \t\t\"terms\": { \t\t\t\"field\": \"product_type\", \t\t\t\"exclude\": \"\", \t\t\t\"order\": { \t\t\t\t\"_term\": \"asc\" \t\t\t}, \t\t\t\"size\": 200 \t\t} \t}, \t\"Brand\": { \t\t\"terms\": { \t\t\t\"field\": \"vendor\", \t\t\t\"exclude\": \"\", \t\t\t\"order\": { \t\t\t\t\"_term\": \"asc\" \t\t\t}, \t\t\t\"size\": 200 \t\t} \t}, \t\"Spare\": { \t\t\"nested\": { \t\t\t\"path\": \"meta\" \t\t}, \t\t\"aggs\": { \t\t\t\"Spare\": { \t\t\t\t\"aggs\": { \t\t\t\t\t\"Spare\": { \t\t\t\t\t\t\"terms\": { \t\t\t\t\t\t\t\"field\": \"meta.value\", \t\t\t\t\t\t\t\"exclude\": \"\", \t\t\t\t\t\t\t\"order\": { \t\t\t\t\t\t\t\t\"_term\": \"asc\" \t\t\t\t\t\t\t}, \t\t\t\t\t\t\t\"size\": 200 \t\t\t\t\t\t} \t\t\t\t\t} \t\t\t\t}, \t\t\t\t\"filter\": { \t\t\t\t\t\"term\": { \t\t\t\t\t\t\"meta.key\": \"spare\" \t\t\t\t\t} \t\t\t\t} \t\t\t} \t\t} \t} }", "template_name": null}, {"key": "checkout_fields", "value": "{ \t\"notes\": { \t\t\"description\": \"Notes\", \t\t\"type\": \"textarea\", \t\t\"required\": true, \t\t\"value\": \"\", \t\t\"min_chars\": 5 \t}, \t\"params.customer_reference\": { \t\t\"description\": \"Customer Reference\", \t\t\"type\": \"text\", \t\t\"required\": true, \t\t\"value\": \"\", \t\t\"min_chars\": 30, \t\t\"max_chars\": 50 \t}, \t\"params.default_warehouse\": { \t\t\"description\": \"Example Select\", \t\t\"required\": true, \t\t\"type\": \"dropdown\", \t\t\"value\": \"\", \t\t\"options\": [{ \t\t\t\"key\": \"Select Heading\", \t\t\t\"value\": \"\" \t\t}, { \t\t\t\"key\": \"Option 1\", \t\t\t\"value\": \"ABC\" \t\t}, { \t\t\t\"key\": \"Option 2\", \t\t\t\"value\": \"XYZ\" \t\t}] \t}, \t\"params.abc\": { \t\t\"description\": \"Example Text Field\", \t\t\"type\": \"text\", \t\t\"required\": true, \t\t\"value\": \"abc\", \t\t\"min_chars\": 10, \t\t\"max_chars\": 10 \t}, \t\"params.xyz\": { \t\t\"description\": \"Example Checkbox\", \t\t\"type\": \"checkbox\", \t\t\"required\": false, \t\t\"value\": true \t}, \t\"params.edf\": { \t\t\"description\": \"Example Text Area\", \t\t\"type\": \"textarea\", \t\t\"required\": false, \t\t\"value\": \"abc xyz edf\", \t\t\"max_chars\": 100 \t}, \t\"params.due_date\": { \t\t\"description\": \"Example date field\", \t\t\"type\": \"date\", \t\t\"required\": false, \t\t\"value\": \"\" \t} }", "template_name": null}, {"key": "display_name", "value": "xxx", "template_name": null}, {"key": "email", "value": "<EMAIL>", "template_name": null}, {"key": "hide_availability_enabled", "value": "false", "template_name": null}, {"key": "hide_tax", "value": "false", "template_name": null}, {"key": "over_order_enabled", "value": "false", "template_name": null}, {"key": "phone", "value": "0218135866", "template_name": null}, {"key": "price_display", "value": "exclusive", "template_name": null}, {"key": "price_inclusive", "value": "false", "template_name": null}, {"key": "product_info_display", "value": "{  \"Brand\": \"vendor\",  \"Category\": \"collection\",  \"Type\": \"product_type\"}", "template_name": null}, {"key": "queue_fulfill_order", "value": "false", "template_name": null}, {"key": "send_customer_email", "value": "true", "template_name": null}, {"key": "show_availability_units", "value": "true", "template_name": null}, {"key": "tax_description", "value": "VAT", "template_name": null}, {"key": "tax_rate", "value": "15", "template_name": null}, {"key": "tax_rate_shipping", "value": "15", "template_name": null}, {"key": "terms", "value": "x", "template_name": null}, {"key": "welcome_html", "value": "<div class=\"row\">\n    <div class=\"col-xs-12 col-md-12\">\n        <a href=\"https://b2b.stock2shop.com/#/products\">\n            <img class=\"img-responsive\" src=\"https://docs.google.com/uc?export=view&id=175k0v21ZJyqbXw2J2oXhPcLmvmRTvNxM\">\n        </a>\n    </div>\n<!--\n    <div class=\"col-xs-12 col-md-12\">\n        <img class=\"img-responsive\" src=\"https://drive.google.com/uc?export=view&id=1h8Etbf_lRlSxUZaZTNzWwSE3fG0wyVWL\">\n    </div>\n-->\n</div>\n<div class=\"row s2s_image\">\n    <div class=\"col-xs-12 col-sm-6 col-md-4\">\n        <div class=\"s2s_image_block\">\n            <a href=\"https://b2b.stock2shop.com/#/products?collection=food%20and%20beverage\">\n                <img class=\"img-responsive\" src=\"https://docs.google.com/uc?export=view&id=1dVzXNpLhN2LPJ50luqH0kFDgo7okhAiW\">\n            </a>\n        </div>\n    </div>\n    <div class=\"col-xs-12 col-sm-6 col-md-4\">\n        <div class=\"s2s_image_block\">\n            <a href=\"https://b2b.stock2shop.com/#/products?collection=toys%20and%20games\">\n                <img class=\"img-responsive\" src=\"https://docs.google.com/uc?export=view&id=1AhjPqsdKNvCxB6jycbdePr1IgZLBJsJJ\">\n            </a>\n        </div>\n    </div>\n    <div class=\"col-xs-12 col-sm-6 col-md-4\">\n        <div class=\"s2s_image_block\">\n            <a href=\"https://b2b.stock2shop.com/#/products?collection=health%20and%20beauty\">\n                <img class=\"img-responsive\" src=\"https://docs.google.com/uc?export=view&id=1cHpuhlfuw55PcmU3V63DmS2B9xl_AtNs\">\n            </a>\n        </div>\n    </div>\n    <div class=\"col-xs-12 col-sm-6 col-md-4\">\n        <div class=\"s2s_image_block\">\n            <a href=\"https://b2b.stock2shop.com/#/products?collection=electronic\">\n                <img class=\"img-responsive\" src=\"https://docs.google.com/uc?export=view&id=1YObsUM0Jz-hA-x5Z4-QIjufslyoRjAlT\">\n            </a>\n        </div>\n    </div>\n    <div class=\"col-xs-12 col-sm-6 col-md-4\">\n        <div class=\"s2s_image_block\">\n            <a href=\"https://b2b.stock2shop.com/#/products?collection=home%20and%20garden\">\n                <img class=\"img-responsive\" src=\"https://docs.google.com/uc?export=view&id=1h-I-x7CqN5qVLpwgONU5f3bDpDK95tcp\">\n            </a>\n        </div>\n    </div>\n    <div class=\"col-xs-12 col-sm-6 col-md-4\">\n        <div class=\"s2s_image_block\">\n            <a href=\"https://b2b.stock2shop.com/#/products?collection=apparel\">\n                <img class=\"img-responsive\" src=\"https://docs.google.com/uc?export=view&id=1o7L_uR8gWSNygIPNsf9zuvBBIq7Hxdte\">\n            </a>\n        </div>\n    </div>\n</div>\n<style class=\"ng-scope\">\n    .s_dashboard .content {\n        padding: 0px;\n    }\n\n    .s_dashboard .s2s_image {\n        padding: 25px 0px 0px 5px;\n        margin: 0px 0px 0px 0px;\n    }\n\n    .s_dashboard .s2s_image>[class*='col-'] {\n        padding: 0px 2px 0px 0px;\n    }\n\n    .s2s_image_block {\n        padding: 0 0px;\n    }\n    \n\n</style>", "template_name": null}]}, "2114": {"flags": [{"code": "system", "column": "body_html", "description": "Description", "id": 61864, "table": "product"}, {"code": "system", "column": "collection", "description": "Collection", "id": 61865, "table": "product"}, {"code": "system", "column": "title", "description": "Title", "id": 61866, "table": "product"}, {"code": "system", "column": "options", "description": "Options", "id": 61867, "table": "product"}, {"code": "system", "column": "product_type", "description": "Type", "id": 61868, "table": "product"}, {"code": "system", "column": "tags", "description": "Tags", "id": 61869, "table": "product"}, {"code": "system", "column": "vendor", "description": "<PERSON><PERSON><PERSON>", "id": 61870, "table": "product"}, {"code": "system", "column": "meta", "description": "Attributes", "id": 61871, "table": "product"}, {"code": "system", "column": "sku", "description": "SKU", "id": 61872, "table": "variant"}, {"code": "system", "column": "price", "description": "Price", "id": 61873, "table": "variant"}, {"code": "system", "column": "barcode", "description": "Barcode", "id": 61874, "table": "variant"}, {"code": "system", "column": "qty", "description": "QTY", "id": 61875, "table": "variant"}, {"code": "system", "column": "option1", "description": "Option 1", "id": 61876, "table": "variant"}, {"code": "system", "column": "option2", "description": "Option 2", "id": 61877, "table": "variant"}, {"code": "system", "column": "option3", "description": "Option 3", "id": 61878, "table": "variant"}, {"code": "system", "column": "grams", "description": "Grams", "id": 61879, "table": "variant"}, {"code": "system", "column": "inventory_management", "description": "Inventory Management", "id": 61880, "table": "variant"}], "meta": {"domain": {"id": 50853, "key": "domain", "value": "s2s-chris", "template_name": null}, "default_fulfillmentservice_id": {"id": 50854, "key": "default_fulfillmentservice_id", "value": "8", "template_name": null}, "product_allow_zero_price": {"id": 50855, "key": "product_allow_zero_price", "value": "false", "template_name": null}, "product_field_price": {"id": 50856, "key": "product_field_price", "value": "price", "template_name": null}, "queue_fulfill_order": {"id": 50857, "key": "queue_fulfill_order", "value": "true", "template_name": null}, "reduce_inventory_field": {"id": 50858, "key": "reduce_inventory_field", "value": "qty", "template_name": null}, "reduce_inventory_when_order_processing": {"id": 50859, "key": "reduce_inventory_when_order_processing", "value": "true", "template_name": null}}, "rules": [], "id": 2114, "description": "Shopify 2", "client_id": 21, "active": true, "type": "shopify2", "price_tier": null, "qty_availability": null, "sync_token": "0"}}, "client_id": 21, "meta": {"notifications": {"key": "notifications", "value": "{\"admin\":{\"name\":\"Stock2Shop Admin\",\"email\":\"<EMAIL>\"},\"user\":{\"name\":\"Stock2shop Admin;<PERSON>;chris\",\"email\":\"<EMAIL>;<EMAIL>;<EMAIL>\"}}", "template_name": null}, "cron_queue_blocking_enabled": {"key": "cron_queue_blocking_enabled", "value": "true", "template_name": null}, "cron_queue_non_blocking_enabled": {"key": "cron_queue_non_blocking_enabled", "value": "true", "template_name": null}, "debit_order": {"key": "debit_order", "value": "", "template_name": null}, "nda": {"key": "nda", "value": "", "template_name": null}, "requested_cancellation": {"key": "requested_cancellation", "value": "false", "template_name": null}, "estimate": {"key": "estimate", "value": "", "template_name": null}, "cron_queue_per_channel": {"key": "cron_queue_per_channel", "value": "true", "template_name": null}, "storage_code_enabled": {"key": "storage_code_enabled", "value": "true", "template_name": null}, "audit_enabled": {"key": "audit_enabled", "value": "true", "template_name": null}, "sales_rep": {"key": "sales_rep", "value": "20927", "template_name": null}, "technician": {"key": "technician", "value": "20927", "template_name": null}, "developer": {"key": "developer", "value": "20927", "template_name": null}, "apifact_log_stream_created": {"key": "apifact_log_stream_created", "value": "true", "template_name": null}, "business_type": {"key": "business_type", "value": "retail", "template_name": null}, "country": {"key": "country", "value": "ZA", "template_name": null}, "status": {"key": "status", "value": "demo", "template_name": null}}, "sources": {"57": {"flags": [{"client_id": 21, "channel_id": null, "source_id": 57, "table": "product", "column": "body_html", "description": "Description", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 57, "table": "product", "column": "collection", "description": "Collection", "code": "source"}, {"client_id": 21, "channel_id": null, "source_id": 57, "table": "product", "column": "title", "description": "Title", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 57, "table": "product", "column": "options", "description": "Options", "code": "source"}, {"client_id": 21, "channel_id": null, "source_id": 57, "table": "product", "column": "product_type", "description": "Type", "code": "source"}, {"client_id": 21, "channel_id": null, "source_id": 57, "table": "product", "column": "tags", "description": "Tags", "code": "source"}, {"client_id": 21, "channel_id": null, "source_id": 57, "table": "product", "column": "vendor", "description": "<PERSON><PERSON><PERSON>", "code": "source"}, {"client_id": 21, "channel_id": null, "source_id": 57, "table": "variant", "column": "price", "description": "Price", "code": "source"}, {"client_id": 21, "channel_id": null, "source_id": 57, "table": "variant", "column": "barcode", "description": "Barcode", "code": "source"}, {"client_id": 21, "channel_id": null, "source_id": 57, "table": "variant", "column": "qty", "description": "QTY", "code": "source"}, {"client_id": 21, "channel_id": null, "source_id": 57, "table": "variant", "column": "option1", "description": "Option 1", "code": "source"}, {"client_id": 21, "channel_id": null, "source_id": 57, "table": "variant", "column": "option2", "description": "Option 2", "code": "source"}, {"client_id": 21, "channel_id": null, "source_id": 57, "table": "variant", "column": "option3", "description": "Option 3", "code": "source"}, {"client_id": 21, "channel_id": null, "source_id": 57, "table": "variant", "column": "grams", "description": "Grams", "code": "source"}, {"client_id": 21, "channel_id": null, "source_id": 57, "table": "variant", "column": "inventory_management", "description": "Inventory Management", "code": "source"}, {"client_id": 21, "channel_id": null, "source_id": 57, "table": "product", "column": "meta", "description": "Attributes", "code": "source"}, {"client_id": 21, "channel_id": null, "source_id": 57, "table": "variant", "column": "sku", "description": "SKU", "code": "source"}], "id": 57, "source_id": null, "description": "Sage One Demo", "client_id": 21, "type": "sageone", "sync_token": "2023-06-07T09:55:17.803", "active": true, "meta": [{"key": "create_order_enabled", "value": "true", "template_name": null}, {"key": "company_id", "value": "115214", "template_name": null}, {"key": "username", "value": "<EMAIL>", "template_name": null}, {"key": "password", "value": "juj-mash-yeV-1", "template_name": null}, {"key": "get_products_limit", "value": "100", "template_name": null}, {"key": "get_images_limit", "value": "1", "template_name": null}, {"key": "param_tax_code", "value": "2718926", "template_name": null}, {"key": "param_tax_exempt_code", "value": "0", "template_name": null}, {"key": "param_use_customer_address", "value": "false", "template_name": null}, {"key": "param_send_invoice_enabled", "value": "true", "template_name": null}, {"key": "product_field_map", "value": "{\n\t\"source\": {\n\t\t\"source_product_code\": \"{{TextUserField1}}\",\n\t\t\"product_active\": {{# YesNoUserField1}}true{{/ YesNoUserField1}}{{^ YesNoUserField1}}false{{/YesNoUserField1}}\n\t},\n\t\"product\": {\n\t\t\"collection\": \"{{Category.Description}}\",\n\t\t\"title\": \"{{{Description}}}\",\n\t\t\"product_type\": \"{{TextUserField2}}\",\n\t\t\"body_html\": \"\",\n\t\t\"tags\": \"\",\n\t\t\"vendor\": \"\",\n\t\t\"options\": [{\n\t\t\t\"value\": \"\",\n\t\t\t\"name\": \"Selection\",\n\t\t\t\"position\": 1\n\t\t}],\n\t\t\"variants\": {\n\t\t\t\"source_variant_code\": \"{{ID}}\",\n\t\t\t\"option1\": \"{{TextUserField3}}\",\n\t\t\t{{#QuantityReserved}}\t\n\t\t\t\"qty\": \"{{QuantityOnHand}}-{{QuantityReserved}}\",\n\t\t\t{{/QuantityReserved}}\n\t\t\t{{^QuantityReserved}}\n\t  \t\t\"qty\": \"{{QuantityOnHand}}\",\n\t \t    {{/QuantityReserved}}\n\t\t\t\"sku\": \"{{Code}}\",\n\t\t\t\"price\": \"{{PriceInclusive}}\",\n\t\t\t\"price_tiers\": [\n\t\t\t  {{#AdditionalItemPrices}} \n\t\t\t  \t\t{\n\t\t\t\t\t\t\"tier\": \"{{description}}\",\n\t\t\t\t\t\t\"price\": \"{{PriceInclusive}}\"\n\t\t\t\t\t} {{comma}} \n\t  \t\t\t\t{{/ AdditionalItemPrices}}\n\t\t\t\t\t  ],\t\t\t \t\t\t\t\t\n\t\t\t \"inventory_management\": true\t\t\n\t\t\t\t\t }\t \t\t\t\t  \n\t\t\t\t\t}\t}", "template_name": null}, {"key": "param_default_shipping_code", "value": "SHIP001", "template_name": null}, {"key": "param_get_images_enabled", "value": "true", "template_name": null}, {"key": "param_get_products_filter", "value": "Physical eq true and TextUserField1  ne ''", "template_name": null}, {"key": "cron_get_products_schedule", "value": "04 * * * *", "template_name": null}, {"key": "param_channel_order_code_enabled", "value": "false", "template_name": null}, {"key": "customer_reference_field", "value": "channel_order_code", "template_name": null}, {"key": "order_map", "value": "{\n{{^ params.use_customer_address}}\n  \"PostalAddress01\": \"{{system_order.billing_address.first_name}} {{system_order.billing_address.last_name}}\",\n  \"PostalAddress02\": \"{{system_order.billing_address.address1}} {{system_order.billing_address.address2}}\",\n  \"PostalAddress03\": \"{{system_order.billing_address.city}} {{system_order.billing_address.phone}}\",\n  \"PostalAddress04\": \"{{system_order.customer.email}}\",\n  \"DeliveryAddress01\": \"{{system_order.shipping_address.first_name}} {{system_order.shipping_address.last_name}}\",\n  \"DeliveryAddress02\": \"{{system_order.shipping_address.address1}} {{system_order.shipping_address.address2}}\",\n  \"DeliveryAddress03\": \"{{system_order.shipping_address.city}} {{system_order.shipping_address.phone}}\",\n  \"DeliveryAddress04\": \"{{system_order.customer.email}}\",\n{{/ params.use_customer_address}}\n\"message\": \"Bank: Standard Bank                                                                                                                 Branch: Sandton City                                                                                                                          Branch Code: 018105                                                                                                       Account Number: *********                                                                                                                        Please quote your invoice number as reference                                                                                  Notes: {{system_order.notes}}\"                                                           \n}", "template_name": null}, {"key": "sync_mode", "value": "pull", "template_name": null}, {"key": "queue_fetch_images", "value": "false", "template_name": null}, {"key": "param_sales_rep_id", "value": "25427", "template_name": null}, {"key": "param_create_customer_enabled", "value": "true", "template_name": null}, {"key": "order_created_date_now", "value": "false", "template_name": null}, {"key": "param_due_date_days", "value": "3", "template_name": null}, {"key": "param_negative_stock_disabled", "value": "false", "template_name": null}, {"key": "param_default_customer_code", "value": "Cash", "template_name": null}, {"key": "config_notes", "value": "<p>test2</p>", "template_name": null}, {"key": "cron_custom_ftp_processor_enabled", "value": "false", "template_name": null}, {"key": "cron_custom_ftp_processor_schedule", "value": "x 55 * * * *", "template_name": null}, {"key": "order_type", "value": "sales_order", "template_name": null}]}, "137": {"flags": [{"client_id": 21, "channel_id": null, "source_id": 137, "table": "product", "column": "body_html", "description": "Description", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 137, "table": "product", "column": "collection", "description": "Collection", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 137, "table": "product", "column": "title", "description": "Title", "code": "source"}, {"client_id": 21, "channel_id": null, "source_id": 137, "table": "product", "column": "options", "description": "Options", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 137, "table": "product", "column": "product_type", "description": "Type", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 137, "table": "product", "column": "tags", "description": "Tags", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 137, "table": "product", "column": "vendor", "description": "<PERSON><PERSON><PERSON>", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 137, "table": "product", "column": "meta", "description": "Attributes", "code": "source"}, {"client_id": 21, "channel_id": null, "source_id": 137, "table": "variant", "column": "price", "description": "Price", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 137, "table": "variant", "column": "barcode", "description": "Barcode", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 137, "table": "variant", "column": "qty", "description": "QTY", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 137, "table": "variant", "column": "option1", "description": "Option 1", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 137, "table": "variant", "column": "option2", "description": "Option 2", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 137, "table": "variant", "column": "option3", "description": "Option 3", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 137, "table": "variant", "column": "grams", "description": "Grams", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 137, "table": "variant", "column": "inventory_management", "description": "Inventory Management", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 137, "table": "variant", "column": "sku", "description": "SKU", "code": "source"}], "id": 137, "source_id": 57, "description": "Google Sheet (Products Only)", "client_id": 21, "type": "flatfile", "sync_token": "50", "active": true, "meta": [{"key": "create_order_enabled", "value": "false", "template_name": null}, {"key": "get_images_limit", "value": "1", "template_name": null}, {"key": "get_images_enabled", "value": "false", "template_name": null}, {"key": "get_products_limit", "value": "100", "template_name": null}, {"key": "product_field_map", "value": "{\n    \"source\": {\n        \"source_product_code\": \"{{source_product_code}}\",\n        \"product_active\": \"true\"\n    },\n    \"product\": {\n        \"collection\": \"\",\n        \"title\": \"{{title}}\",\n        \"product_type\": \"\",\n        \"body_html\": \"{{body_html}}\",\n        \"tags\": \"\",\n        \"vendor\": \"\",\n        \"variants\": {},\n        \"meta\": [\n            {\n                \"key\": \"release_date\",\n                \"value\": \"{{meta_release_date}}\"\n            }\n        ]\n    }\n}", "template_name": null}, {"key": "transfer_protocol", "value": "url", "template_name": null}, {"key": "deliminator", "value": ",", "template_name": null}, {"key": "file_url", "value": "https://docs.google.com/feeds/download/spreadsheets/Export?key=1Ukp3LjynApCol8sdfxcxc0JiBFHnFXkao-0MlzoWpTQ&gid=1567129396&exportFormat=csv", "template_name": null}, {"key": "file_extension", "value": "csv", "template_name": null}, {"key": "google_sheet_header", "value": "product.collection,product.product_type,product.body_html, product.tags,product.vendor,product.title, product.meta_special_price,product.meta_age,product.meta_gender", "template_name": null}, {"key": "sync_mode", "value": "pull", "template_name": null}]}, "138": {"flags": [{"client_id": 21, "channel_id": null, "source_id": 138, "table": "product", "column": "body_html", "description": "Description", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 138, "table": "product", "column": "collection", "description": "Collection", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 138, "table": "product", "column": "title", "description": "Title", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 138, "table": "product", "column": "options", "description": "Options", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 138, "table": "product", "column": "product_type", "description": "Type", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 138, "table": "product", "column": "tags", "description": "Tags", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 138, "table": "product", "column": "vendor", "description": "<PERSON><PERSON><PERSON>", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 138, "table": "product", "column": "meta", "description": "Attributes", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 138, "table": "variant", "column": "price", "description": "Price", "code": "source"}, {"client_id": 21, "channel_id": null, "source_id": 138, "table": "variant", "column": "barcode", "description": "Barcode", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 138, "table": "variant", "column": "qty", "description": "QTY", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 138, "table": "variant", "column": "option1", "description": "Option 1", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 138, "table": "variant", "column": "option2", "description": "Option 2", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 138, "table": "variant", "column": "option3", "description": "Option 3", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 138, "table": "variant", "column": "grams", "description": "Grams", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 138, "table": "variant", "column": "inventory_management", "description": "Inventory Management", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 138, "table": "variant", "column": "sku", "description": "SKU", "code": "system"}], "id": 138, "source_id": 57, "description": "Google Sheet (Price)", "client_id": 21, "type": "flatfile", "sync_token": "38", "active": true, "meta": [{"key": "create_order_enabled", "value": "false", "template_name": null}, {"key": "get_images_limit", "value": "1", "template_name": null}, {"key": "get_images_enabled", "value": "false", "template_name": null}, {"key": "get_products_limit", "value": "100", "template_name": null}, {"key": "product_field_map", "value": "{\n    \"source\": {\n        \"source_product_code\": \"{{source_product_code}}\",\n        \"product_active\": \"true\"\n    },\n    \"product\": {\n        \"collection\": \"\",\n        \"title\": \"{{title}}\",\n        \"product_type\": \"\",\n        \"body_html\": \"\",\n        \"tags\": \"\",\n        \"vendor\": \"\",\n        \"options\": [\n            {\n                \"value\": \"\",\n                \"name\": \"\",\n                \"position\": 1\n            }\n        ],\n        \"variants\": {\n            \"source_variant_code\": \"{{source_variant_code}}\",\n            \"option1\": \"\",\n            \"qty\": \"\",\n            \"barcode\": \"{{barcode}}\",\n            \"grams\": \"\",\n            \"sku\": \"{{sku}}\",\n            \"price\": \"{{price}}\",\n            \"inventory_management\": \"true\",\n            \"price_tiers\": []\n        }\n    }\n}", "template_name": null}, {"key": "transfer_protocol", "value": "url", "template_name": null}, {"key": "deliminator", "value": ",", "template_name": null}, {"key": "file_url", "value": "https://docs.google.com/feeds/download/spreadsheets/Export?key=1-SBIA_Y_sNvPlO9vgz6Ai1F3WV84MrlirLZqqVb286U&gid=0&exportFormat=csv", "template_name": null}, {"key": "file_extension", "value": "csv", "template_name": null}, {"key": "google_sheet_header", "value": "product.title, variant.price", "template_name": null}, {"key": "sync_mode", "value": "pull", "template_name": null}]}, "140": {"flags": [{"client_id": 21, "channel_id": null, "source_id": 140, "table": "product", "column": "body_html", "description": "Description", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 140, "table": "product", "column": "collection", "description": "Collection", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 140, "table": "product", "column": "title", "description": "Title", "code": "source"}, {"client_id": 21, "channel_id": null, "source_id": 140, "table": "product", "column": "options", "description": "Options", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 140, "table": "product", "column": "product_type", "description": "Type", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 140, "table": "product", "column": "tags", "description": "Tags", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 140, "table": "product", "column": "vendor", "description": "<PERSON><PERSON><PERSON>", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 140, "table": "product", "column": "meta", "description": "Attributes", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 140, "table": "variant", "column": "price", "description": "Price", "code": "source"}, {"client_id": 21, "channel_id": null, "source_id": 140, "table": "variant", "column": "barcode", "description": "Barcode", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 140, "table": "variant", "column": "qty", "description": "QTY", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 140, "table": "variant", "column": "option1", "description": "Option 1", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 140, "table": "variant", "column": "option2", "description": "Option 2", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 140, "table": "variant", "column": "option3", "description": "Option 3", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 140, "table": "variant", "column": "grams", "description": "Grams", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 140, "table": "variant", "column": "inventory_management", "description": "Inventory Management", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 140, "table": "variant", "column": "sku", "description": "SKU", "code": "source"}], "id": 140, "source_id": 57, "description": "Google Sheet (Everything)", "client_id": 21, "type": "flatfile", "sync_token": "38", "active": true, "meta": [{"key": "create_order_enabled", "value": "false", "template_name": null}, {"key": "get_images_limit", "value": "1", "template_name": null}, {"key": "get_images_enabled", "value": "false", "template_name": null}, {"key": "get_products_limit", "value": "100", "template_name": null}, {"key": "product_field_map", "value": "{\n    \"source\": {\n        \"source_product_code\": \"{{source_product_code}}\",\n        \"product_active\": \"true\"\n    },\n    \"product\": {\n        \"collection\": \"\",\n        \"title\": \"{{title}}\",\n        \"product_type\": \"\",\n        \"body_html\": \"\",\n        \"tags\": \"\",\n        \"vendor\": \"\",\n        \"options\": [\n            {\n                \"value\": \"\",\n                \"name\": \"\",\n                \"position\": 1\n            }\n        ],\n        \"variants\": {\n            \"source_variant_code\": \"{{source_variant_code}}\",\n            \"option1\": \"\",\n            \"qty\": \"\",\n            \"barcode\": \"\",\n            \"grams\": \"\",\n            \"sku\": \"{{sku}}\",\n            \"price\": \"{{price}}\",\n            \"inventory_management\": \"true\",\n            \"price_tiers\": [\n                {\n                    \"tier\": \"Wholesale\",\n                    \"price\": \"{{price_Wholesale}}\"\n                }\n            ]\n        }\n    }\n}", "template_name": null}, {"key": "transfer_protocol", "value": "url", "template_name": null}, {"key": "deliminator", "value": ",", "template_name": null}, {"key": "file_extension", "value": "csv", "template_name": null}, {"key": "file_url", "value": "https://docs.google.com/feeds/download/spreadsheets/Export?exportFormat=csv&key=13t9RsysYuHWJnNcgSPb8v558dxr5fl3URq8SVQn0Js8&gid=0", "template_name": null}, {"key": "sync_mode", "value": "pull", "template_name": null}]}, "551": {"flags": [{"client_id": 21, "channel_id": null, "source_id": 551, "table": "product", "column": "body_html", "description": "Description", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 551, "table": "product", "column": "collection", "description": "Collection", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 551, "table": "product", "column": "title", "description": "Title", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 551, "table": "product", "column": "options", "description": "Options", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 551, "table": "product", "column": "product_type", "description": "Type", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 551, "table": "product", "column": "tags", "description": "Tags", "code": "source"}, {"client_id": 21, "channel_id": null, "source_id": 551, "table": "product", "column": "vendor", "description": "<PERSON><PERSON><PERSON>", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 551, "table": "product", "column": "meta", "description": "Attributes", "code": "source"}, {"client_id": 21, "channel_id": null, "source_id": 551, "table": "variant", "column": "sku", "description": "SKU", "code": "source"}, {"client_id": 21, "channel_id": null, "source_id": 551, "table": "variant", "column": "price", "description": "Price", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 551, "table": "variant", "column": "barcode", "description": "Barcode", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 551, "table": "variant", "column": "qty", "description": "QTY", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 551, "table": "variant", "column": "option1", "description": "Option 1", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 551, "table": "variant", "column": "option2", "description": "Option 2", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 551, "table": "variant", "column": "option3", "description": "Option 3", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 551, "table": "variant", "column": "grams", "description": "Grams", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 551, "table": "variant", "column": "inventory_management", "description": "Inventory Management", "code": "system"}], "id": 551, "source_id": 57, "description": "Test Image", "client_id": 21, "type": "flatfile", "sync_token": "24", "active": true, "meta": [{"key": "create_order_enabled", "value": "false", "template_name": null}, {"key": "get_images_limit", "value": "3", "template_name": null}, {"key": "get_images_enabled", "value": "true", "template_name": null}, {"key": "get_products_limit", "value": "100", "template_name": null}, {"key": "transfer_protocol", "value": "url", "template_name": null}, {"key": "file_extension", "value": "csv", "template_name": null}, {"key": "deliminator", "value": ",", "template_name": null}, {"key": "sync_mode", "value": "pull", "template_name": null}, {"key": "image_field_map", "value": "{\n  \"source_images\": [\n    {\n      \"source\": {\n        \"source_product_code\": \"{{source.source_product_code}}\",\n        \"source_id\": \"{{source.id}}\",\n        \"sync_token\": \"{{source.sync_token}}\"\n      },\n      \"image\": {\n        \"action\": \"I\",\n        \"image_id\": \"{{data.image1}}\",\n        \"meta\": {\n          \"thumbnail\": \"true\",\n          \"image\": \"true\",\n          \"small_image\": \"true\"\n        }\n      }\n    },\n    {\n      \"source\": {\n        \"source_product_code\": \"{{source.source_product_code}}\",\n        \"source_id\": \"{{source.id}}\",\n        \"sync_token\": \"{{source.sync_token}}\"\n      },\n      \"image\": {\n        \"action\": \"I\",\n        \"image_id\": \"{{data.image2}}\",\n        \"meta\": {\n          \"thumbnail\": \"false\",\n          \"image\": \"false\",\n          \"small_image\": \"false\"\n        }\n      }\n    },\n    {\n      \"source\": {\n        \"source_product_code\": \"{{source.source_product_code}}\",\n        \"source_id\": \"{{source.id}}\",\n        \"sync_token\": \"{{source.sync_token}}\"\n      },\n      \"image\": {\n        \"action\": \"I\",\n        \"image_id\": \"{{data.image3}}\",\n        \"meta\": {\n          \"thumbnail\": \"false\",\n          \"image\": \"false\",\n          \"small_image\": \"false\"\n        }\n      }\n    }\n  ]\n}", "template_name": null}, {"key": "file_url", "value": "https://docs.google.com/feeds/download/spreadsheets/Export?exportFormat=csv&key=108PO3IUHKw86yx9U0pILTlp6hbrv_ELW_IE9sA4eF7U&gid=0", "template_name": null}, {"key": "queue_fetch_images", "value": "true", "template_name": null}, {"key": "product_field_map", "value": "{\n\"source\": {\n\"source_product_code\": \"{{source_product_code}}\",\n\"product_active\": true\n},\n\"product\": {\n\"collection\": \"\",\n\"title\": \"{{title}}\",\n\"product_type\": \"\",\n\"body_html\": \"\",\n\"tags\": \"\",\n\"vendor\": \"\",\n\"options\": [\n{\n\"value\": \"\",\n\"name\": \"{{option1_name}}\",\n\"position\": 1\n}\n],\n\"meta\": [\n\n\t{\n\t\t\"key\": \"Image1\",\n\t\t\"value\": \"{{image1}}\"\n\t},\n    {\n    \"key\": \"Image2\",\n    \"value\": \"{{image2}}\"\n    },\n\n    {\n    \"key\": \"Image3\",\n    \"value\": \"{{image3}}\"\n    }\n\n],\n\"variants\": {\n\"source_variant_code\": \"{{source_variant_code}}\",\n\"option1\": \"{{option1_value}}\",\n\"qty\": \"\",\n\"barcode\": \"\",\n\"grams\": \"\",\n\"sku\": \"{{sku}}\",\n\"price\": \"\",\n\"inventory_management\": true,\n\"price_tiers\": []\n}\n}\n}", "template_name": null}]}, "715": {"flags": [{"client_id": 21, "channel_id": null, "source_id": 715, "table": "product", "column": "body_html", "description": "Description", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "product", "column": "collection", "description": "Collection", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "product", "column": "title", "description": "Title", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "product", "column": "options", "description": "Options", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "product", "column": "product_type", "description": "Type", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "product", "column": "tags", "description": "Tags", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "product", "column": "vendor", "description": "<PERSON><PERSON><PERSON>", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "product", "column": "meta", "description": "Attributes", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "variant", "column": "sku", "description": "SKU", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "variant", "column": "price", "description": "Price", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "variant", "column": "barcode", "description": "Barcode", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "variant", "column": "qty", "description": "QTY", "code": "source"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "variant", "column": "option1", "description": "Option 1", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "variant", "column": "option2", "description": "Option 2", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "variant", "column": "option3", "description": "Option 3", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "variant", "column": "grams", "description": "Grams", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "variant", "column": "inventory_management", "description": "Inventory Management", "code": "system"}], "id": 715, "source_id": 57, "description": "Test Product Sharing", "client_id": 21, "type": "flatfile", "sync_token": "0", "active": true, "meta": [{"key": "sync_mode", "value": "pull", "template_name": null}, {"key": "create_order_enabled", "value": "false", "template_name": null}]}, "1577": {"flags": [{"client_id": 21, "channel_id": null, "source_id": 715, "table": "product", "column": "body_html", "description": "Description", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "product", "column": "collection", "description": "Collection", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "product", "column": "title", "description": "Title", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "product", "column": "options", "description": "Options", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "product", "column": "product_type", "description": "Type", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "product", "column": "tags", "description": "Tags", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "product", "column": "vendor", "description": "<PERSON><PERSON><PERSON>", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "product", "column": "meta", "description": "Attributes", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "variant", "column": "sku", "description": "SKU", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "variant", "column": "price", "description": "Price", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "variant", "column": "barcode", "description": "Barcode", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "variant", "column": "qty", "description": "QTY", "code": "source"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "variant", "column": "option1", "description": "Option 1", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "variant", "column": "option2", "description": "Option 2", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "variant", "column": "option3", "description": "Option 3", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "variant", "column": "grams", "description": "Grams", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "variant", "column": "inventory_management", "description": "Inventory Management", "code": "system"}], "id": 715, "source_id": 57, "description": "Test Product Sharing", "client_id": 21, "type": "flatfile", "sync_token": "0", "active": true, "meta": [{"key": "sync_mode", "value": "pull", "template_name": null}, {"key": "create_order_enabled", "value": "false", "template_name": null}, {"key": "oauth2_is_authorised", "value": "false", "template_name": null}]}, "1578": {"flags": [{"client_id": 21, "channel_id": null, "source_id": 715, "table": "product", "column": "body_html", "description": "Description", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "product", "column": "collection", "description": "Collection", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "product", "column": "title", "description": "Title", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "product", "column": "options", "description": "Options", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "product", "column": "product_type", "description": "Type", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "product", "column": "tags", "description": "Tags", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "product", "column": "vendor", "description": "<PERSON><PERSON><PERSON>", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "product", "column": "meta", "description": "Attributes", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "variant", "column": "sku", "description": "SKU", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "variant", "column": "price", "description": "Price", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "variant", "column": "barcode", "description": "Barcode", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "variant", "column": "qty", "description": "QTY", "code": "source"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "variant", "column": "option1", "description": "Option 1", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "variant", "column": "option2", "description": "Option 2", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "variant", "column": "option3", "description": "Option 3", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "variant", "column": "grams", "description": "Grams", "code": "system"}, {"client_id": 21, "channel_id": null, "source_id": 715, "table": "variant", "column": "inventory_management", "description": "Inventory Management", "code": "system"}], "id": 715, "source_id": 57, "description": "Test Product Sharing", "client_id": 21, "type": "flatfile", "sync_token": "0", "active": true, "meta": [{"key": "sync_mode", "value": "pull", "template_name": null}, {"key": "create_order_enabled", "value": "false", "template_name": null}]}}, "fulfillmentservices": {"8": {"meta": {"api_username": {"id": 12, "key": "api_username", "value": "cPRRGunctY2cwiiYYt7wHRua2N7i1I67wktAcWEhKZ4=", "template_name": null}, "api_password": {"id": 13, "key": "api_password", "value": "SmS7hloRRvRasnO6yil9X4XWAIBfMtD61bN+IiYH+UA=", "template_name": null}, "api_url": {"id": 14, "key": "api_url", "value": "https://qa-storeapi.parcelninja.com/api/v1/", "template_name": null}, "delivery_rules": {"id": 648, "key": "delivery_rules", "value": "[\n          {\n            \"special_service_code\": \"0\",\n            \"delivery_quote_id\": \"0\",\n            \"field\": \"{{#shipping_lines}}{{title}}{{/shipping_lines}}\",\n            \"operator\": \"contains\",\n            \"value\": \"next Day\"\n          }\n        ]", "template_name": null}, "quote_id": {"id": 649, "key": "quote_id", "value": "1", "template_name": null}}, "workflow_configuration": null, "id": 8, "description": "Parcel <PERSON> Demo", "type": "parcelninja", "active": true, "client_id": 21, "is_warehouse": true}}}}