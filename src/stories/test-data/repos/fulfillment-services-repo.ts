import type FulfillmentService from '@/models/fulfillment-services/fulfillment-service';

export default class FulfillmentServicesRepo {
  async get(): Promise<FulfillmentService[]> {
    return [
      {
        id: 180,
        created: '2024-07-03 10:07:49',
        description: 'Parcel ninja',
        type: 'parcelninja',
        active: 1,
        modified: '2024-07-03 10:07:49',
        client_id: 1022,
        is_warehouse: 0
      }
    ];
  }
}
