import type { SystemChannelSource } from '@/models/channel-sources/channel-source';

export default class MockChannelSourcesRepo {
  basePath = 'channel_sources';

  // eslint-disable-next-line @typescript-eslint/require-await, class-methods-use-this, @typescript-eslint/no-unused-vars
  async get(sourceId: number): Promise<SystemChannelSource[]> {
    // Used for ChannelSources story
    return [
      {
        id: 1801,
        created: '2023-05-22 13:18:12.000000',
        channel_id: 57,
        source_id: 57,
        modified: '2023-05-22 13:24:25.000000',
        active: 1,
        status: null,
        client_id: '927'
      },
      {
        id: 1753,
        created: '2023-02-17 13:10:48.000000',
        channel_id: 1880,
        source_id: 1578,
        modified: '2023-02-17 13:11:22.000000',
        active: 1,
        status: 'syncing',
        client_id: '927'
      }
    ];
  }
}
