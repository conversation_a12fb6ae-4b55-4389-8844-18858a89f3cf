import type FulfillmentService from '@/models/fulfillment-services/fulfillment-service';

export default class MockFulfillmentServicesRepo {
  basePath = '';

  // eslint-disable-next-line @typescript-eslint/require-await, class-methods-use-this
  async get(): Promise<FulfillmentService[]> {
    return [
      {
        id: 170,
        created: '2023-04-03 13:39:26',
        description: 'KRS Parcelninja',
        type: 'parcelninja',
        active: 1,
        modified: '2023-04-03 13:39:26',
        client_id: 927,
        is_warehouse: 0
      }
    ];
  }
}
