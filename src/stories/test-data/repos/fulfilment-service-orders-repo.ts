import { post } from '@/lib/api/api';
import type { APIRequest } from '@/lib/api/types';
import type FulfillmentServiceOrderPost from '@/models/orders/fulfillment-service-order-post';
import type { ApiResult } from '../models/api-result';

export default class FulfillmentServiceOrdersRepo {
  basePath = 'fulfillment_service_orders';

  async fulfillOrder(
    fulfillmentServiceId: number,
    serviceOrder: FulfillmentServiceOrderPost
  ): Promise<ApiResult<boolean>> {
    return {
      error: '',
      result: true
    };
  }
}
