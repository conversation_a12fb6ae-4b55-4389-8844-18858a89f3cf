import { put, post, del } from '@/lib/api/api';
import type { ApiResult } from '@/models/api-result';
import type { APIRequest } from '@/lib/api/types';
import type Variant from '@/models/variant';
import type { ProductVariantWrapper } from '@/models/products/product-variant-wrapper';

export default class ProductVariantsRepo {
  basePath = 'products';

  path = (pid: number) => `${this.basePath}/${pid}/variants`;

  async post(variant: Variant): Promise<ApiResult<boolean>> {
    return {
      error: '',
      result: true
    };
  }

  async put(variant: Variant): Promise<ApiResult<Variant>> {
    return {
      error: '',
      result: true
    };
  }

  async del(variant: Variant): Promise<ApiResult<boolean>> {
    return {
      error: '',
      result: true
    };
  }
}
