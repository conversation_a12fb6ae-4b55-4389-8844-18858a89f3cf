/* eslint-disable class-methods-use-this */
/* eslint-disable @typescript-eslint/require-await */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import type { ApiResult } from '@/models/api-result';
import type SystemChannelCustomerDataWrapper from '@/models/channel-customers/channel-customer-data-wrapper';
import type { ChannelCustomerPostResponse } from '../../../models/channel-customers/channel-customer-post-response';

export default class MockChannelCustomersRepo {
  basePath = 'channel_customers';

  // eslint-disable-next-line @typescript-eslint/require-await, class-methods-use-this, @typescript-eslint/no-unused-vars
  async put(
    channelCustomer: SystemChannelCustomerDataWrapper,
    channelId: number,
    customerId: number
  ): Promise<ApiResult<boolean>> {
    return {
      error: '',
      result: true
    };
  }

  async post(
    channelCustomer: SystemChannelCustomerDataWrapper,
    channelId: number
  ): Promise<ApiResult<ChannelCustomerPostResponse>> {
    return {
      error: '',
      result: {
        customer: {
          id: 123
        }
      }
    };
  }
}
