/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/await-thenable */
/* eslint-disable @typescript-eslint/return-await */
/* eslint-disable class-methods-use-this */
/* eslint-disable @typescript-eslint/require-await */
import type { ApiResult } from '@/models/api-result';
import type { SystemMetaTemplateName } from '@/models/product-templates/meta-template-name';
import type { SystemMetaTemplateItem } from '@/models/product-templates/system-meta-template-item';
import type { SystemMetaTemplateItemDataWrapper } from '@/models/product-templates/system-meta-template-item-data-wrapper';
import type { TemplateWithItems } from '@/models/product-templates/template-with-items';
import productTemplatesWithItems from '@/stories/test-data/product-templates/product-templates.json';

export default class MockProductTemplatesRepo {
  basePath = 'meta_template';

  async getAll(): Promise<SystemMetaTemplateName[]> {
    return productTemplatesWithItems.map(({ template }) => template);
  }

  async allItems(): Promise<TemplateWithItems[]> {
    const templates = await this.getAll();
    const templateItems = await Promise.all(templates.map((template) => this.get(template.name)));

    return templates.map((template, index) => {
      const items = templateItems[index];
      return { template, items };
    });
  }

  async get(name: string): Promise<SystemMetaTemplateItem[]> {
    return productTemplatesWithItems.find((t) => t.template.name === name)?.items ?? [];
  }

  async post(
    templateItem: SystemMetaTemplateItem
  ): Promise<ApiResult<SystemMetaTemplateItemDataWrapper>> {
    return {
      error: '',
      result: {} // ToDo: Implement
    };
  }

  async put(
    templateItem: SystemMetaTemplateItem
  ): Promise<ApiResult<SystemMetaTemplateItemDataWrapper>> {
    return {
      error: '',
      result: {} // ToDo: Implement
    };
  }

  async renameTemplate(name: string, newName: string): Promise<ApiResult<boolean>> {
    return {
      error: '',
      result: true
    };
  }

  async delete(name: string): Promise<ApiResult<boolean>> {
    return {
      error: '',
      result: true
    };
  }

  async deleteItem(name: string, key: string): Promise<ApiResult<boolean>> {
    return {
      error: '',
      result: true
    };
  }
}
