import type { XeroTenantConnection } from '@/models/xero/tenant-connection';

export default class XeroTenantConnectionRepo {
  basePath = 'xero/connections';

  async get(sourceId: number): Promise<XeroTenantConnection[]> {
    const result = {
      results: [
        {
          tenantId: '1234',
          tenantType: 'ORGANISATION',
          tenantName: 'Demo Company (Global)'
        },
        {
          tenantId: '123456',
          tenantType: 'ORGANISATION',
          tenantName: 'Another Org'
        }
      ]
    };
    return result.results;
  }
}
