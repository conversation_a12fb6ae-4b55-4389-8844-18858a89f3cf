{"system_channel_meta_values": {"debug": {"stubs": {"name": "channelmeta_debug", "key": "stubs", "default": "", "description": "The set of stubs to use", "type": "string", "required": 0, "values": []}, "queue_fulfill_order": {"name": "channelmeta_debug", "key": "queue_fulfill_order", "default": "true", "description": "Queue fulfill_order item after adding order to source or if the source has add_order_disabled", "type": "string", "required": 0, "values": []}, "check_order_items_linked": {"name": "channelmeta_debug", "key": "check_order_items_linked", "default": "false", "description": "Ensures order items must have corresponding variant on S2S or the order fails.", "type": "string", "required": 0, "values": []}, "default_fulfillmentservice_id": {"name": "channelmeta_debug", "key": "default_fulfillmentservice_id", "default": "0", "description": "Default fulfillment service id", "type": "string", "required": 0, "values": []}, "hmac_shared_secret": {"name": "channelmeta_debug", "key": "hmac_shared_secret", "default": "", "description": "HMAC shared secret", "type": "string", "required": 0, "values": []}, "group_duplicate_order_items": {"name": "channelmeta_debug", "key": "group_duplicate_order_items", "default": "false", "description": "Group duplicate order items", "type": "string", "required": 0, "values": []}, "min_order_amount": {"name": "channelmeta_debug", "key": "min_order_amount", "default": "", "description": "Minimum order amount ex tax", "type": "string", "required": 0, "values": []}}, "export": {"cron_email_pricelist_schedule": {"name": "channelmeta_export", "key": "cron_email_pricelist_schedule", "default": "x 00 * * * *", "description": "Basic format is \"MM HH * * *\"", "type": "string", "required": 0, "values": []}, "field_map": {"name": "channelmeta_export", "key": "field_map", "default": "{\"product.title\":\"title\",\"product.collection\":\"collection\",\"variant.sku\":\"sku\",\"variant.price\":\"price\"}", "description": "JSON value as per POST /products/export", "type": "string", "required": 0, "values": []}, "email_pricelist_subject": {"name": "channelmeta_export", "key": "email_pricelist_subject", "default": "Stock2shop Pricelist Mailer", "description": "Subject for the pricelist email, note that the email does not have to include price.", "type": "string", "required": 0, "values": []}, "qty_limit_upper": {"name": "channelmeta_export", "key": "qty_limit_upper", "default": "50", "description": "Upper limit for channel qty", "type": "string", "required": 0, "values": []}, "send_customer_email": {"name": "channelmeta_export", "key": "send_customer_email", "default": "true", "description": "Send customer emails", "type": "string", "required": 0, "values": []}, "default_fulfillmentservice_id": {"name": "channelmeta_export", "key": "default_fulfillmentservice_id", "default": "0", "description": "Default fulfillment service id", "type": "string", "required": 0, "values": []}, "hmac_shared_secret": {"name": "channelmeta_export", "key": "hmac_shared_secret", "default": "", "description": "HMAC shared secret", "type": "string", "required": 0, "values": []}, "group_duplicate_order_items": {"name": "channelmeta_export", "key": "group_duplicate_order_items", "default": "false", "description": "Group duplicate order items", "type": "string", "required": 0, "values": []}}, "kalahari": {"ftp_port": {"name": "channelmeta_kalahari", "key": "ftp_port", "default": "", "description": "", "type": "string", "required": 0, "values": []}, "ftp_server": {"name": "channelmeta_kalahari", "key": "ftp_server", "default": "", "description": "", "type": "string", "required": 0, "values": []}, "ftp_user_name": {"name": "channelmeta_kalahari", "key": "ftp_user_name", "default": "", "description": "", "type": "string", "required": 0, "values": []}, "ftp_user_password": {"name": "channelmeta_kalahari", "key": "ftp_user_password", "default": "", "description": "", "type": "string", "required": 0, "values": []}, "ftp_file_name": {"name": "channelmeta_kalahari", "key": "ftp_file_name", "default": "ALCDAshleyStock.txt", "description": "Name of file to be sent", "type": "string", "required": 0, "values": []}, "cron_ftp_products_schedule": {"name": "channelmeta_kalahari", "key": "cron_ftp_products_schedule", "default": "x 00 * * * *", "description": "Basic format is \"MM HH * * *\"", "type": "string", "required": 0, "values": []}, "field_map": {"name": "channelmeta_kalahari", "key": "field_map", "default": "{\n\"field_map\": {\n\"variant.sku\": \"Product Code\",\n\"variant.barcode\": \"Barcode\",\n\"product.title\": \"Product Name\",\n\"variant.qty\": \"Stock Count\",\n\"variant.price\": \"Cost Price (ex VAT)\"\n}\n}", "description": "This is the field map used in products/export_products, first products are exported using this field map then they are FTPed", "type": "string", "required": 0, "values": []}, "qty_limit_upper": {"name": "channelmeta_kalahari", "key": "qty_limit_upper", "default": "50", "description": "Upper limit for channel qty", "type": "string", "required": 0, "values": []}, "send_customer_email": {"name": "channelmeta_kalahari", "key": "send_customer_email", "default": "true", "description": "Send customer emails", "type": "string", "required": 0, "values": []}, "default_fulfillmentservice_id": {"name": "channelmeta_kalahari", "key": "default_fulfillmentservice_id", "default": "0", "description": "Default fulfillment service id", "type": "string", "required": 0, "values": []}, "hmac_shared_secret": {"name": "channelmeta_kalahari", "key": "hmac_shared_secret", "default": "", "description": "HMAC shared secret", "type": "string", "required": 0, "values": []}, "group_duplicate_order_items": {"name": "channelmeta_kalahari", "key": "group_duplicate_order_items", "default": "false", "description": "Group duplicate order items", "type": "string", "required": 0, "values": []}}, "loot": {"ftp_port": {"name": "channelmeta_loot", "key": "ftp_port", "default": "", "description": "", "type": "string", "required": 0, "values": []}, "ftp_server": {"name": "channelmeta_loot", "key": "ftp_server", "default": "", "description": "", "type": "string", "required": 0, "values": []}, "ftp_user_name": {"name": "channelmeta_loot", "key": "ftp_user_name", "default": "", "description": "", "type": "string", "required": 0, "values": []}, "ftp_user_password": {"name": "channelmeta_loot", "key": "ftp_user_password", "default": "", "description": "", "type": "string", "required": 0, "values": []}, "ftp_file_name": {"name": "channelmeta_loot", "key": "ftp_file_name", "default": "test.csv", "description": "Name of file to be sent", "type": "string", "required": 0, "values": []}, "cron_ftp_products_schedule": {"name": "channelmeta_loot", "key": "cron_ftp_products_schedule", "default": "x 00 * * * *", "description": "Basic format is \"MM HH * * *\"", "type": "string", "required": 0, "values": []}, "field_map": {"name": "channelmeta_loot", "key": "field_map", "default": "{\n\"field_map\": {\n\"variant.sku\": \"ProductCode\",\n\"variant.barcode\": \"Barcode\",\n\"product.title\": \"Product Name\",\n\"variant.qty\": \"Qty Avail\",\n\"variant.price\": \"Price ex Vat\"\n}\n}", "description": "This is the field map used in products/export_products, first products are exported using this field map then they are FTPed", "type": "string", "required": 0, "values": []}, "qty_limit_upper": {"name": "channelmeta_loot", "key": "qty_limit_upper", "default": "50", "description": "Upper limit for channel qty", "type": "string", "required": 0, "values": []}, "send_customer_email": {"name": "channelmeta_loot", "key": "send_customer_email", "default": "true", "description": "Send customer emails", "type": "string", "required": 0, "values": []}, "default_fulfillmentservice_id": {"name": "channelmeta_loot", "key": "default_fulfillmentservice_id", "default": "0", "description": "Default fulfillment service id", "type": "string", "required": 0, "values": []}, "hmac_shared_secret": {"name": "channelmeta_loot", "key": "hmac_shared_secret", "default": "", "description": "HMAC shared secret", "type": "string", "required": 0, "values": []}, "group_duplicate_order_items": {"name": "channelmeta_loot", "key": "group_duplicate_order_items", "default": "false", "description": "Group duplicate order items", "type": "string", "required": 0, "values": []}}, "magento": {"attribute_set": {"name": "channelmeta_magento", "key": "attribute_set", "default": "64", "description": "", "type": "string", "required": 0, "values": []}, "queue_fulfill_order": {"name": "channelmeta_magento", "key": "queue_fulfill_order", "default": "true", "description": "Queue fulfill_order item after adding order to source", "type": "string", "required": 0, "values": []}, "qty_limit_upper": {"name": "channelmeta_magento", "key": "qty_limit_upper", "default": "50", "description": "Upper limit for channel qty", "type": "string", "required": 0, "values": []}, "root_category": {"name": "channelmeta_magento", "key": "root_category", "default": "3", "description": "", "type": "string", "required": 0, "values": []}, "send_customer_email": {"name": "channelmeta_magento", "key": "send_customer_email", "default": "true", "description": "Send customer emails", "type": "string", "required": 0, "values": []}, "soap_password": {"name": "channelmeta_magento", "key": "soap_password", "default": "", "description": "", "type": "string", "required": 0, "values": []}, "soap_url": {"name": "channelmeta_magento", "key": "soap_url", "default": "http://your_domain/api/v2_soap/?wsdl", "description": "", "type": "string", "required": 0, "values": []}, "soap_username": {"name": "channelmeta_magento", "key": "soap_username", "default": "", "description": "", "type": "string", "required": 0, "values": []}, "store_view": {"name": "channelmeta_magento", "key": "store_view", "default": "1", "description": "", "type": "string", "required": 0, "values": []}, "website_ids": {"name": "channelmeta_magento", "key": "website_ids", "default": "3", "description": "", "type": "string", "required": 0, "values": []}, "check_order_items_linked": {"name": "channelmeta_magento", "key": "check_order_items_linked", "default": "false", "description": "Ensures order items must have corresponding variant on S2S or the order fails.", "type": "string", "required": 1, "values": []}, "default_fulfillmentservice_id": {"name": "channelmeta_magento", "key": "default_fulfillmentservice_id", "default": "0", "description": "Default fulfillment service id", "type": "string", "required": 0, "values": []}, "hmac_shared_secret": {"name": "channelmeta_magento", "key": "hmac_shared_secret", "default": "", "description": "HMAC shared secret", "type": "string", "required": 0, "values": []}, "group_duplicate_order_items": {"name": "channelmeta_magento", "key": "group_duplicate_order_items", "default": "false", "description": "Group duplicate order items", "type": "string", "required": 0, "values": []}}, "magento2": {"pargo": {"name": "channelmeta_magento2", "key": "pargo", "default": "false", "description": "Set to true to enable Pargo shipping method for integration with ParcelNinja", "type": "string", "required": 0, "values": []}, "manage_stock_status": {"name": "channelmeta_magento2", "key": "manage_stock_status", "default": "false", "description": "Have the system determine a product's stock status. \"is_in_stock\" property must be omitted from maps when this feature is enabled. Disabled by default", "type": "string", "required": 0, "values": []}, "manage_visibility": {"name": "channelmeta_magento2", "key": "manage_visibility", "default": "false", "description": "If true we are allowed to change a products visibility. View API for more information about visibility https://docs.magento.com/user-guide/system/data-attributes-product.html", "type": "string", "required": 0, "values": []}, "queue_fulfill_order": {"name": "channelmeta_magento2", "key": "queue_fulfill_order", "default": "false", "description": "Queue fulfill_order item after adding order to source or if the source has add_order_disabled.", "type": "string", "required": 0, "values": []}, "username": {"name": "channelmeta_magento2", "key": "username", "default": "admin", "description": "Magento Admin username, must have full access", "type": "string", "required": 1, "values": []}, "check_order_items_linked": {"name": "channelmeta_magento2", "key": "check_order_items_linked", "default": "false", "description": "If false allows orders to be raised to source without having corresponding variant/SKU in S2S.(SKU code must exist in accounting system.)", "type": "string", "required": 0, "values": []}, "password": {"name": "channelmeta_magento2", "key": "password", "default": "*****", "description": "Magento Admin password", "type": "string", "required": 1, "values": []}, "log_enabled": {"name": "channelmeta_magento2", "key": "log_enabled", "default": "false", "description": "Log time requests take, only enable this during setup or for debugging. Find logs in S2S admin system.", "type": "string", "required": 0, "values": []}, "url": {"name": "channelmeta_magento2", "key": "url", "default": "http://demo.gomedia.co.za/rest/V1/", "description": "Magento2 REST endpoint, note the trailing forward slash. It is possible to to update only a specific store using \"rest/[store]/V1/\", read the Magento2 REST API docs for more on this: https://devdocs.magento.com/", "type": "string", "required": 1, "values": []}, "attribute_set_id": {"name": "channelmeta_magento2", "key": "attribute_set_id", "default": "4", "description": "Default attribute set id to assign attribute values to", "type": "string", "required": 1, "values": []}, "configurable_product_map": {"name": "channelmeta_magento2", "key": "configurable_product_map", "default": "{ \t\"product\": { \t\t\"sku\": \"{{id}}\", \t\t\"name\": \"{{title}}\", \t\t\"attribute_set_id\": \"{{params.attribute_set_id}}\", \t\t\"status\": 1, \t\t\"visibility\": 4, \t\t\"weight\": \"0.5\", \t\t\"extension_attributes\": { \t\t\t\"stock_item\": { \t\t\t\t\"is_in_stock\": true \t\t\t} \t\t}, \t\t\"custom_attributes\": [{ \t\t\t\t\"attribute_code\": \"description\", \t\t\t\t\"value\": \"{{{body_html}}}\" \t\t\t}, { \t\t\t\t\"attribute_code\": \"exmaple\", \t\t\t\t\"value\": \"{{meta.example}}\" \t\t\t}, \t\t\t{ \t\t\t\t\"attribute_code\": \"tax_class_id\", \t\t\t\t\"value\": \"2\" \t\t\t}, { \t\t\t\t\"attribute_code\": \"multi_select_test\", \t\t\t\t\"frontend_input\": \"multiselect\", \t\t\t\t\"default_frontend_label\": \"Multi Select Test\", \t\t\t\t\"value\": \"aaa,yyy,zzz\" \t\t\t} \t\t] \t} }", "description": "Map used to create a configurable product, read the docs for all available properties of a Magento product. https://devdocs.magento.com/swagger/index.html", "type": "string", "required": 0, "values": []}, "simple_product_map": {"name": "channelmeta_magento2", "key": "simple_product_map", "default": "{ \t\"product\": { \t\t\"sku\": \"{{variant.sku}}\", \t\t\"name\": \"{{{title}}}{{#variant.option1}}-{{.}}{{/variant.option1}}{{#variant.option2}}-{{.}}{{/variant.option2}}{{#variant.option3}}-{{.}}{{/variant.option3}}\", \t\t\"attribute_set_id\": \"{{params.attribute_set_id}}\", \t\t\"price\": \"{{variant.price}}\", \t\t\"status\": 1, \t\t\"visibility\": 1, \t\t\"tier_prices\": [{ \t\t\t\"customer_group_id\": 2, \t\t\t\"qty\": 1, \t\t\t\"value\": \"{{variant.price_tiers.wholesale}}\" \t\t}, { \t\t\t\"customer_group_id\": 3, \t\t\t\"qty\": 1, \t\t\t\"value\": \"{{variant.price_tiers.retail}}\" \t\t}], \t\t\"extension_attributes\": { \t\t\t\"stock_item\": { \t\t\t\t\"qty\": \"{{variant.qty}}\", \t\t\t\t\"is_in_stock\": true \t\t\t} \t\t}, \t\t\"custom_attributes\": [{ \t\t\t\t\t\"attribute_code\": \"cost\", \t\t\t\t\t\"value\": \"{{variant.price_tiers.cost}}\" \t\t\t\t}, { \t\t\t\t\t\"attribute_code\": \"special_price\", \t\t\t\t\t\"value\": \"{{meta.special_price}}\" \t\t\t\t}, { \t\t\t\t\t\"attribute_code\": \"special_from_date\", \t\t\t\t\t\"value\": \"2018-06-04\" \t\t\t\t}, { \t\t\t\t\t\"attribute_code\": \"special_to_date\", \t\t\t\t\t\"value\": \"2018-06-05\" \t\t\t\t}, { \t\t\t\t\t\"attribute_code\": \"tax_class_id\", \t\t\t\t\t\"value\": \"2\" \t\t\t\t} { \t\t\t\t\t{ \t\t\t\t\t\t#variant.option1 \t\t\t\t\t} \t\t\t\t}, { \t\t\t\t\t\"attribute_code\": \"{{variant.option1_name}}\", \t\t\t\t\t\"frontend_input\": \"select\", \t\t\t\t\t\"default_frontend_label\": \"Selection Test\", \t\t\t\t\t\"value\": \"{{variant.option1}}\" \t\t\t\t} { \t\t\t\t\t{ \t\t\t\t\t\t/variant.option1}}            ]          }        }", "description": "Map used to create a simple products, read the docs for all available properties of a Magento product. https://devdocs.magento.com/swagger/index.html Calculate function available. Values calculated that Equal '0' might cause Queue Errors", "type": "string", "required": 1, "values": []}, "category_map": {"name": "channelmeta_magento2", "key": "category_map", "default": "{   \"categories\": [     {       \"parent_id\": \"2\",       \"name\": \"Products\",       \"categories\": [         {           \"name\": \"{{collection}}\",           \"categories\": [             {               \"name\": \"{{product_type}}\"             }           ]         }       ]     },     {       \"parent_id\": \"2\",       \"name\": \"Brands\",       \"categories\": [         {           \"name\": \"{{vendor}}\"         }       ]     },     {       \"parent_id\": \"2\",       \"name\": \"Gender\",       \"categories\": [         {           \"name\": \"{{meta.gender}}\"         }       ]     }   ] }", "description": "Map used to manage category hierarchy, you can create any number of categories based on the given data", "type": "string", "required": 0, "values": []}, "manage_categories": {"name": "channelmeta_magento2", "key": "manage_categories", "default": "false", "description": "If set to true and category_map has been populated, categories will be created/updated by S2S.", "type": "string", "required": 0, "values": []}, "manage_images": {"name": "channelmeta_magento2", "key": "manage_images", "default": "false", "description": "If set to true, images will be added to configurable / simple products", "type": "string", "required": 0, "values": []}, "delete_products": {"name": "channelmeta_magento2", "key": "delete_products", "default": "false", "description": "If set to true, products will be deleted when required", "type": "string", "required": 0, "values": []}, "use_image_meta": {"name": "channelmeta_magento2", "key": "use_image_meta", "default": "false", "description": "If true and manage_images enabled we use the image meta to set thumbnail, small_image and image. The image meta must be set to \"thumbnail\" = \"true\" if you want to set thumbnail for the image etc.See image_field_map attribute on the sourcemeta_flatfile template.", "type": "string", "required": 0, "values": []}, "add_order_status": {"name": "channelmeta_magento2", "key": "add_order_status", "default": "processing", "description": "The Magento 2 order status to use for adding an order to source. There must be a corresponding add_order_status property on the order_map.", "type": "string", "required": 0, "values": []}, "group_duplicate_order_items": {"name": "channelmeta_magento2", "key": "group_duplicate_order_items", "default": "false", "description": "If false order line items will be as per Magento line items. Set to true to group duplicate line items from Magento.", "type": "string", "required": 0, "values": []}, "order_map": {"name": "channelmeta_magento2", "key": "order_map", "default": "{\n          \"system_order\": {\n            \"id\": \"{{entity_id}}\",\n            \"notes\": \"{{notes}}\",\n            \"add_order_status\": \"{{status}}\",\n            \"total_discount\": \"{{base_discount_amount}}\",\n            \"customer\": {\n              \"id\": \"{{customer.entity_id}}\",\n              \"last_name\": \"{{customer.lastname}}\",\n              \"first_name\": \"{{customer.firstname}}\",\n              \"email\": \"{{customer.email}}\"\n            },\n            \"billing_address\": {\n              \"first_name\": \"{{billing_address.firstname}}\",\n              \"last_name\": \"{{billing_address.lastname}}\",\n              \"company\": \"{{billing_address.company}}\",\n              \"phone\": \"{{billing_address.telephone}}\",\n              \"address1\": \"{{# json_escape}}{{billing_address.street}}{{/ json_escape}}\",\n              \"address2\": \"\",\n              \"city\": \"{{billing_address.city}}\",\n              \"zip\": \"{{billing_address.postcode}}\",\n              \"province\": \"{{billing_address.region}}\",\n              \"country\": \"{{billing_address.country_id}}\",\n              \"country_code\": \"{{billing_address.country_id}}\",\n              \"province_code\": \"\"\n            },\n            \"shipping_address\": {\n              \"first_name\": \"{{shipping_address.firstname}}\",\n              \"last_name\": \"{{shipping_address.lastname}}\",\n              \"company\": \"{{shipping_address.company}}\",\n              \"phone\": \"{{shipping_address.telephone}}\",\n              \"address1\": \"{{# json_escape}}{{shipping_address.street}}{{/ json_escape}}\",\n              \"address2\": \"\",\n              \"city\": \"{{shipping_address.city}}\",\n              \"zip\": \"{{shipping_address.postcode}}\",\n              \"province\": \"{{shipping_address.region}}\",\n              \"country\": \"{{shipping_address.country_id}}\",\n              \"country_code\": \"{{shipping_address.country_id}}\",\n              \"province_code\": \"\"\n            },\n            \"line_items\": [\n              {{# line_items}}\n              {\n                \"channel_product_code\": \"{{id}}\",\n                \"channel_variant_code\": \"{{id}}\",\n                \"price\": \"{{price}}\",\n                \"qty\": \"{{qty}}\",\n                \"sku\": \"{{sku}}\",\n                \"title\": \"{{name}}\",\n                \"total_discount\": \"{{# calculate}}{{price}}-{{price_with_discount}}{{/ calculate}}\",\n                \"tax_lines\": [\n                  {\n                    \"price\": \"{{# calculate}}{{price_with_discount_and_tax}}-{{price_with_discount}}{{/ calculate}}\",\n                    \"rate\": \"{{tax_rate}}\",\n                    \"title\": \"VAT\",\n                    \"code\": \"taxed\"\n                  }\n                ]\n              }{{^ last}},{{/ last}}\n              {{/ line_items}}\n            ],\n            \"shipping_lines\": [\n              {\n                \"price\": \"{{shipping_amount}}\",\n                \"qty\": 1,\n                \"title\": \"{{shipping_description}}\",\n                \"code\": \"ship\",\n                \"tax_lines\": [\n                  {\n                    \"rate\": 0,\n                    \"price\": 0,\n                    \"code\": \"taxed\",\n                    \"title\": \"VAT\"\n                  }\n                ]\n              }\n            ]\n          },\n          \"params\": {\n              \"currency\": \"{{base_currency_code}}\"\n          }\n        }", "description": "To be set if the default order_map needs to be adjusted.", "type": "string", "required": 0, "values": []}, "shipment_comment_map": {"name": "channelmeta_magento2", "key": "shipment_comment_map", "default": "{   \"entity\": {     \"is_customer_notified\": 0,     \"comment\": \"{{status}}\",     \"is_visible_on_front\": 0   } }", "description": "Configuration of the Shipment comments. entity_id is required as an order param - add entity_id to order_map", "type": "string", "required": 0, "values": []}, "order_ship_map": {"name": "channelmeta_magento2", "key": "order_ship_map", "default": "{   \"notify\": false,   \"appendComment\": true,   \"comment\": {     \"comment\": \"{{status}}\",     \"is_visible_on_front\": 0   },   \"tracks\": [     {       \"track_number\": \"{{tracking_number}}\",       \"title\": \"{{tracking_company}}\",       \"carrier_code\": \"{{tracking_company}}\"     }   ] }", "description": "Creates the shipment and updates tracking information from ParcelNinja. ParcelNinja webhook required.", "type": "string", "required": 0, "values": []}}, "magentocustomrest": {"notes_template": {"name": "channelmeta_magentocustomrest", "key": "notes_template", "default": "{{customer.special_instructions}}", "description": "Mustache template to be used to create the notes field", "type": "string", "required": 0, "values": []}, "use_image_meta": {"name": "channelmeta_magentocustomrest", "key": "use_image_meta", "default": "false", "description": "Use image meta to assign \"thumbnail\", \"small_image\", \"image\" values to specific images. The image meta must be set to have the key \"thumbnail\" and the value \"true\", etc.", "type": "string", "required": 0, "values": []}, "product_map_template": {"name": "channelmeta_magentocustomrest", "key": "product_map_template", "default": "{\n  \"attributes\": {\n    \"product_collection\": \"{{collection}}\",\n    \"dimensions\": \"{{meta.dimensions.value}}\",\n    \"weight\": \"{{# calculate}}{{variant.grams}} / 1000 {{/ calculate}}\",\n    \"grouped_qty\": \"{{# calculate}}{{variant.qty_availability.jhb.qty}} + {{variant.qty_availability.cpt.qty}}{{/ calculate}}\"\n  },\n  \"group_price\": [\n    {\n      \"cust_group\": 4,\n      \"price\": \"{{variant.price_tiers.wholesale.price}}\",\n      \"website_id\": 0\n    }\n  ]\n}", "description": "Product map to transform S2S product to Magento product. The following properties will be added automatically based on settings: type_id, attribute_set_id, status, visibility, tax_class_id. You can override these if required. The S2S product has the following structure:\n  {\n    \"options\": [],\n    \"images\": [],\n    \"variant\": {\n      \"price_tiers\": {},\n      \"qty_availability\": {},\n      \"image\": {}\n    }\n  }\n", "type": "string", "required": 0, "values": []}, "variant_map": {"name": "channelmeta_magentocustomrest", "key": "variant_map", "default": "{\n  \"weight\": {{variant.grams}},\n  \"price\": {{variant.price}},\n  \"group_price\":  [\n    {\n      \"cust_group\": 2,\n      \"price\": {{variant.price_tier_map.A.price}},\n      \"website_id\": 0\n    }\n  ],\n  \"stock_data\": {\n    \"qty\": {{variant.qty_availability_map.B.qty}},\n    \"is_in_stock\": {{variant.is_in_stock}},\n    \"use_config_manage_stock\": 0,\n    \"manage_stock\": 0\n  },\n  \"sku\": \"{{variant.sku}}\"\n}", "description": "Used for updating a magento simple product, see magento 1.x rest documentation for the model to use. To access S2S warehouses and pricing, see example using \"price_tier_map\" and \"qty_availability_map\" properties", "type": "string", "required": 0, "values": []}, "website_id": {"name": "channelmeta_magentocustomrest", "key": "website_id", "default": "1", "description": "Website ID in Magneto to link the products to", "type": "string", "required": 0, "values": []}, "tax_class_id": {"name": "channelmeta_magentocustomrest", "key": "tax_class_id", "default": "2", "description": "0 - None, 2 - taxable <PERSON><PERSON>, 4 - Shipping", "type": "string", "required": 0, "values": []}, "configurable_product_enabled": {"name": "channelmeta_magentocustomrest", "key": "configurable_product_enabled", "default": "true", "description": "Do we create configurable products for this store? Make sure options are correctly set for products", "type": "string", "required": 0, "values": []}, "configurable_sku_prefix": {"name": "channelmeta_magentocustomrest", "key": "configurable_sku_prefix", "default": "sku:", "description": "By default product code is used as the sku for the configurable product, this is sometimes the same as the sku, in which case we can prefix it to make it unique", "type": "string", "required": 0, "values": []}, "attribute_set": {"name": "channelmeta_magentocustomrest", "key": "attribute_set", "default": "4", "description": "default product attribute set ID in Magento.", "type": "string", "required": 0, "values": []}, "add_order_status": {"name": "channelmeta_magentocustomrest", "key": "add_order_status", "default": "processing", "description": "what status to send order on", "type": "string", "required": 0, "values": []}, "create_image_enabled": {"name": "channelmeta_magentocustomrest", "key": "create_image_enabled", "default": "false", "description": "", "type": "string", "required": 0, "values": []}, "create_product_enabled": {"name": "channelmeta_magentocustomrest", "key": "create_product_enabled", "default": "true", "description": "", "type": "string", "required": 1, "values": []}, "delete_image_enabled": {"name": "channelmeta_magentocustomrest", "key": "delete_image_enabled", "default": "false", "description": "", "type": "string", "required": 0, "values": []}, "delete_product_enabled": {"name": "channelmeta_magentocustomrest", "key": "delete_product_enabled", "default": "true", "description": "", "type": "string", "required": 0, "values": []}, "update_product_enabled": {"name": "channelmeta_magentocustomrest", "key": "update_product_enabled", "default": "false", "description": "", "type": "string", "required": 0, "values": []}, "manage_category_enabled": {"name": "channelmeta_magentocustomrest", "key": "manage_category_enabled", "default": "false", "description": "", "type": "string", "required": 0, "values": []}, "auto_sync": {"name": "channelmeta_magentocustomrest", "key": "auto_sync", "default": "true", "description": "This will automatically link products to existing products, rather than adding new ones, which it still does", "type": "string", "required": 0, "values": []}, "category_parent_id": {"name": "channelmeta_magentocustomrest", "key": "category_parent_id", "default": "2", "description": "", "type": "string", "required": 0, "values": []}, "category_is_anchor": {"name": "channelmeta_magentocustomrest", "key": "category_is_anchor", "default": "1", "description": "1 = true, 0 = false", "type": "string", "required": 0, "values": []}, "consumer_key": {"name": "channelmeta_magentocustomrest", "key": "consumer_key", "default": "", "description": "", "type": "string", "required": 0, "values": []}, "consumer_secret": {"name": "channelmeta_magentocustomrest", "key": "consumer_secret", "default": "", "description": "", "type": "string", "required": 0, "values": []}, "default_customer_group": {"name": "channelmeta_magentocustomrest", "key": "default_customer_group", "default": "1", "description": "the group new customers get assigned to", "type": "string", "required": 0, "values": []}, "param_use_customer_address": {"name": "channelmeta_magentocustomrest", "key": "param_use_customer_address", "default": "true", "description": "e.g. tell source to use this customers address", "type": "string", "required": 0, "values": []}, "queue_fulfill_order": {"name": "channelmeta_magentocustomrest", "key": "queue_fulfill_order", "default": "true", "description": "Queue fulfill_order item after adding order to source or if the source has add_order_disabled", "type": "string", "required": 0, "values": []}, "qty_limit_upper": {"name": "channelmeta_magentocustomrest", "key": "qty_limit_upper", "default": "50", "description": "Upper limit for channel qty", "type": "string", "required": 0, "values": []}, "secret": {"name": "channelmeta_magentocustomrest", "key": "secret", "default": "", "description": "", "type": "string", "required": 0, "values": []}, "send_customer_email": {"name": "channelmeta_magentocustomrest", "key": "send_customer_email", "default": "true", "description": "Send customer emails", "type": "string", "required": 0, "values": []}, "token": {"name": "channelmeta_magentocustomrest", "key": "token", "default": "", "description": "", "type": "string", "required": 0, "values": []}, "url": {"name": "channelmeta_magentocustomrest", "key": "url", "default": "", "description": "", "type": "string", "required": 0, "values": []}, "manage_status_disabled": {"name": "channelmeta_magentocustomrest", "key": "manage_status_disabled", "default": "false", "description": "Removes updating the status on Magento for a product when adding or creating product", "type": "string", "required": 0, "values": []}, "manage_visibility_disabled": {"name": "channelmeta_magentocustomrest", "key": "manage_visibility_disabled", "default": "false", "description": "Removes updating the visibility on Magento for a product when adding or creating product", "type": "string", "required": 0, "values": []}, "image_label_field": {"name": "channelmeta_magentocustomrest", "key": "image_label_field", "default": "option1", "description": "The field used to label an image, must be a variant field at this point", "type": "string", "required": 0, "values": []}, "set_param_[x]": {"name": "channelmeta_magentocustomrest", "key": "set_param_[x]", "default": "increment_id", "description": "This is used in the order transform. You can set any parameter you like from an order. e.g. key = set_param_external_order_number, value = id. This will create a param_external_order_number at the transform. Note: the properties on the order are from the original payload, i.e. before the transform has happened", "type": "string", "required": 0, "values": []}, "tax_after_discount": {"name": "channelmeta_magentocustomrest", "key": "tax_after_discount", "default": "false", "description": "If set to true, the tax portion will be deducted from the discount", "type": "string", "required": 0, "values": []}, "order_use_system_price": {"name": "channelmeta_magentocustomrest", "key": "order_use_system_price", "default": "false", "description": "Uses the price of the variant from S2S and not form Magento.", "type": "string", "required": 0, "values": []}, "order_use_system_price_tax": {"name": "channelmeta_magentocustomrest", "key": "order_use_system_price_tax", "default": "15", "description": "Tells us if S2S channel price needs to include TAX (e.g. 14 = 14% tax added to system price)", "type": "string", "required": 0, "values": []}, "order_code_prefix": {"name": "channelmeta_magentocustomrest", "key": "order_code_prefix", "default": "ORDER-", "description": "Prefix all orders with this", "type": "string", "required": 0, "values": []}, "ignore_stock_data": {"name": "channelmeta_magentocustomrest", "key": "ignore_stock_data", "default": "false", "description": "Default is false. When false and the variant has a qty, it will set the qty, and set the \"Stock Availability\" to \"In Stock\" ONLY if the qty > 0, and finally will uncheck \"Use Config Settings\" for the \"Manage Stock\" setting.", "type": "boolean", "required": 0, "values": []}, "ignore_manage_stock": {"name": "channelmeta_magentocustomrest", "key": "ignore_manage_stock", "default": "false", "description": "Default is false. When false it will either set the \"Manage Stock\" to be equal to the \"Manage Inventory\" value on the variant (if present), if \"Manage Inventory\" is not present on the S2S variant then it will set Magento's \"Manage Stock\" setting to \"Yes\".", "type": "boolean", "required": 0, "values": []}, "check_order_items_linked": {"name": "channelmeta_magentocustomrest", "key": "check_order_items_linked", "default": "false", "description": "Ensures order items must have corresponding variant on S2S or the order fails.", "type": "string", "required": 1, "values": []}, "default_fulfillmentservice_id": {"name": "channelmeta_magentocustomrest", "key": "default_fulfillmentservice_id", "default": "0", "description": "Default fulfillment service id", "type": "string", "required": 0, "values": []}, "hmac_shared_secret": {"name": "channelmeta_magentocustomrest", "key": "hmac_shared_secret", "default": "", "description": "HMAC shared secret", "type": "string", "required": 0, "values": []}, "group_duplicate_order_items": {"name": "channelmeta_magentocustomrest", "key": "group_duplicate_order_items", "default": "false", "description": "Group duplicate order items", "type": "string", "required": 0, "values": []}, "store_id": {"name": "channelmeta_magentocustomrest", "key": "store_id", "default": "0", "description": "Store to update, only use this for multi store setups", "type": "string", "required": 0, "values": []}}, "magentorest": {"attribute_set": {"name": "channelmeta_magentorest", "key": "attribute_set", "default": "4", "description": "default product attribute set.", "type": "string", "required": 0, "values": []}, "add_order_status": {"name": "channelmeta_magentorest", "key": "add_order_status", "default": "processing", "description": "what status to send order on", "type": "string", "required": 0, "values": []}, "can_add_image": {"name": "channelmeta_magentorest", "key": "can_add_image", "default": "false", "description": "", "type": "string", "required": 0, "values": []}, "can_add_product": {"name": "channelmeta_magentorest", "key": "can_add_product", "default": "true", "description": "", "type": "string", "required": 0, "values": []}, "can_delete_image": {"name": "channelmeta_magentorest", "key": "can_delete_image", "default": "false", "description": "", "type": "string", "required": 0, "values": []}, "can_delete_product": {"name": "channelmeta_magentorest", "key": "can_delete_product", "default": "true", "description": "", "type": "string", "required": 0, "values": []}, "customer_group_x": {"name": "channelmeta_magentorest", "key": "customer_group_x", "default": "1", "description": "list of customer groups. The price tier name should match the x in Magento", "type": "string", "required": 0, "values": []}, "consumer_key": {"name": "channelmeta_magentorest", "key": "consumer_key", "default": "", "description": "", "type": "string", "required": 0, "values": []}, "consumer_secret": {"name": "channelmeta_magentorest", "key": "consumer_secret", "default": "", "description": "", "type": "string", "required": 0, "values": []}, "can_update_product": {"name": "channelmeta_magentorest", "key": "can_update_product", "default": "false", "description": "", "type": "string", "required": 0, "values": []}, "default_customer_group": {"name": "channelmeta_magentorest", "key": "default_customer_group", "default": "1", "description": "the group new customers get assigned to", "type": "string", "required": 0, "values": []}, "magento_product_map": {"name": "channelmeta_magentorest", "key": "magento_product_map", "default": "{\n\"type_id\": \"simple\",\n\"visibility\": \"4\",\n\"stock_data.manage_stock\": \"1\",\n\"name\": \"product.title\",\n\"description\": \"product.body_html\"\n}", "description": "This will create a map for the magento product. Any S2S variable should be referenced like product.id.\nAny value that is no a S2S variable will be treated as a literal.", "type": "string", "required": 0, "values": []}, "queue_fulfill_order": {"name": "channelmeta_magentorest", "key": "queue_fulfill_order", "default": "true", "description": "Queue fulfill_order item after adding order to source", "type": "string", "required": 0, "values": []}, "qty_limit_upper": {"name": "channelmeta_magentorest", "key": "qty_limit_upper", "default": "50", "description": "Upper limit for channel qty", "type": "string", "required": 0, "values": []}, "secret": {"name": "channelmeta_magentorest", "key": "secret", "default": "", "description": "", "type": "string", "required": 0, "values": []}, "send_customer_email": {"name": "channelmeta_magentorest", "key": "send_customer_email", "default": "true", "description": "Send customer emails", "type": "string", "required": 0, "values": []}, "token": {"name": "channelmeta_magentorest", "key": "token", "default": "", "description": "", "type": "string", "required": 0, "values": []}, "url": {"name": "channelmeta_magentorest", "key": "url", "default": "", "description": "", "type": "string", "required": 0, "values": []}, "website_ids": {"name": "channelmeta_magentorest", "key": "website_ids", "default": "1", "description": "this can be comma separate list, not yet implemented", "type": "string", "required": 0, "values": []}, "default_fulfillmentservice_id": {"name": "channelmeta_magentorest", "key": "default_fulfillmentservice_id", "default": "0", "description": "Default fulfillment service id", "type": "string", "required": 0, "values": []}, "hmac_shared_secret": {"name": "channelmeta_magentorest", "key": "hmac_shared_secret", "default": "", "description": "HMAC shared secret", "type": "string", "required": 0, "values": []}, "group_duplicate_order_items": {"name": "channelmeta_magentorest", "key": "group_duplicate_order_items", "default": "false", "description": "Group duplicate order items", "type": "string", "required": 0, "values": []}}, "mirakl": {"sync_mode": {"name": "channelmeta_mirakl", "key": "sync_mode", "default": "bulk", "description": "Bulk mode means products will be synced on cron schedule with queue item \"sync_channel_bulk\", ignore or leave empty for \"instant\" sync (Bulk mode only supported for Mirakl)", "type": "string", "required": 1, "values": []}, "url": {"name": "channelmeta_mirakl", "key": "url", "default": "https://leroymerlinza-preprod.mirakl.net", "description": "API url, no trailing slash", "type": "string", "required": 1, "values": []}, "api_key": {"name": "channelmeta_mirakl", "key": "api_key", "default": "67a24df1-17bb-41ca-b019-106877e15938", "description": "API key for authentication", "type": "boolean", "required": 1, "values": []}, "sync": {"name": "channelmeta_mirakl", "key": "sync", "default": "", "description": "Possible values are: \"products\" or \"offers\". ignore or leave empty to sync both offers and products", "type": "string", "required": 0, "values": []}, "product_map": {"name": "channelmeta_mirakl", "key": "product_map", "default": "<product>\n          <attribute>\n            <code>category</code>\n            <value>716</value>\n          </attribute>\n          <attribute>\n            <code>shop_sku</code>\n            <value>{{variants.sku}}</value>\n          </attribute>\n          <attribute>\n            <code>name</code>\n            <value>{{title}}</value>\n          </attribute>\n          <attribute>\n            <code>att_06575</code>\n            <value>{{vendor}}</value>\n          </attribute>\n          <attribute>\n            <code>att_09914</code>\n            <value>{{meta.warranty}}</value>\n          </attribute>\n          <attribute>\n            <code>description</code>\n            <value>{{body_html}}</value>\n          </attribute>\n          <attribute>\n            <code>dtp_001</code>\n            <value>{{variants.grams}}</value>\n          </attribute>\n          <attribute>\n            <code>dtp_002</code>\n            <value>{{meta.width}}</value>\n          </attribute>\n          <attribute>\n            <code>dtp_004</code>\n            <value>{{meta.length}}</value>\n          </attribute>\n          <attribute>\n            <code>dtp_006</code>\n            <value>{{meta.height}}</value>\n          </attribute>\n          <attribute>\n            <code>ean</code>\n            <value>{{variants.barcode}}</value>\n          </attribute>\n          <attribute>\n            <code>media_1</code>\n            <value>{{images.0.src}}</value>\n          </attribute>\n          <attribute>\n            <code>media_2</code>\n            <value>{{images.1.src}}</value>\n          </attribute>\n          <attribute>\n            <code>media_3</code>\n            <value>{{images.2.src}}</value>\n          </attribute>\n          <attribute>\n            <code>media_4</code>\n            <value>{{images.3.src}}</value>\n          </attribute>\n          <attribute>\n            <code>media_5</code>\n            <value>{{images.4.src}}</value>\n          </attribute>\n          <attribute>\n            <code>short_description</code>\n            <value></value>\n          </attribute>\n          <attribute>\n            <code>size</code>\n            <value/>\n          </attribute>\n          <attribute>\n            <code>weight</code>\n            <value>{{# calculate}}{{variants.grams}}/1000{{/ calculate}}</value>\n          </attribute>\n          <attribute>\n            <code>color</code>\n            <value/>\n          </attribute>\n        </product>", "description": "A product is a set of data constituting the product itself, we find the description, pictures, features. Mustache template to define product xml sent to Mirakl. EAN must be included and a valid (13 digit alpha numeric) otherwise it will be ignored.", "type": "string", "required": 0, "values": []}, "offer_map": {"name": "channelmeta_mirakl", "key": "offer_map", "default": "<offer>\n            <sku>{{variants.sku}}</sku>\n            <product-id>{{variants.barcode}}</product-id>\n            <product-id-type>EAN</product-id-type>\n            <description>Great Deal!</description>\n            <price>{{variants.price}}</price>\n            <price-additional-info>Price including taxes</price-additional-info>\n            <quantity>{{variants.qty}}</quantity>\n            <state>11</state>\n        </offer>", "description": "An offer is a set of data that contains information related to the marketability of the product: the reference number, quantity, price, condition (new or used). Mustache template to define offer xml sent to Mirakl. EAN must be included and a valid (13 digit alpha numeric) otherwise it will be ignored", "type": "string", "required": 0, "values": []}, "cron_sync_channel_bulk_schedule": {"name": "channelmeta_mirakl", "key": "cron_sync_channel_bulk_schedule", "default": "x 23 * * * *", "description": "", "type": "string", "required": 0, "values": []}}, "shopify": {"fulfillment_map": {"name": "channelmeta_shopify", "key": "fulfillment_map", "default": "{ \t\"fulfillment\": { \t\t\"tracking_number\": \"{{tracking_number}}\", \t\t\"tracking_company\": \"{{tracking_company}}\", \t\t\"tracking_url\": \"{{tracking_url}}\", \t\t\"location_id\": *********, \t\t\"notify_customer\": true \t} }", "description": "Map used to transform the S2S fulfilment into the Shopify fulfilment, NB! Update location ID from channel setting and add to map, see this documentation https://help.shopify.com/en/api/reference/shipping-and-fulfillment/fulfillment#create", "type": "string", "required": 0, "values": []}, "set_param_[x]": {"name": "channelmeta_shopify", "key": "set_param_[x]", "default": "{{customer.tags}}", "description": "Create any order param using Mustache template on order data. See order meta solution for examples.", "type": "string", "required": 0, "values": []}, "delete_product_enabled": {"name": "channelmeta_shopify", "key": "delete_product_enabled", "default": "true", "description": "If set to true, products will be removed from the store. If set to anything else, products will not be deleted", "type": "string", "required": 0, "values": []}, "use_location_api": {"name": "channelmeta_shopify", "key": "use_location_api", "default": "true", "description": "Set to false for client that do not use the location based inventory.", "type": "string", "required": 1, "values": []}, "location_map": {"name": "channelmeta_shopify", "key": "location_map", "default": "{   \"shopify_location_id_1\": \"{{qty_availability.jhb}}\",   \"shopify_location_id_2\": \"{{qty_availability.cpt}}\" }", "description": "Keys are shopify location_id and value is mustache template. use_location_api must be set to true", "type": "string", "required": 0, "values": []}, "compare_at_price": {"name": "channelmeta_shopify", "key": "compare_at_price", "default": "{{price_tiers.Wholesale.price}}", "description": "Price tier to set compare_at_price on Shopify. Use {{price_tiers.*price_tier_key*.price}} make sure \".price\" is at the end. Read through solution for more information on the feature.", "type": "string", "required": 0, "values": []}, "api_key": {"name": "channelmeta_shopify", "key": "api_key", "default": "xxx", "description": "https://www.stock2shop.com/documentation/getting-started/shopify-install", "type": "string", "required": 1, "values": []}, "channel_domain": {"name": "channelmeta_shopify", "key": "channel_domain", "default": "xxx.myshopify.com", "description": "", "type": "string", "required": 1, "values": []}, "inventory_management": {"name": "channelmeta_shopify", "key": "inventory_management", "default": "shopify", "description": "Only set this when there are other apps set up to manage inventory on Shopify. Remember to remove setting when there is only a single app", "type": "string", "required": 0, "values": []}, "password": {"name": "channelmeta_shopify", "key": "password", "default": "****", "description": "", "type": "string", "required": 1, "values": []}, "queue_fulfill_order": {"name": "channelmeta_shopify", "key": "queue_fulfill_order", "default": "false", "description": "Queue fulfill_order item after adding order to source or if the source has add_order_disabled", "type": "string", "required": 0, "values": []}, "qty_limit_upper": {"name": "channelmeta_shopify", "key": "qty_limit_upper", "default": "50", "description": "Upper limit for channel qty. This is the maximum quantity that can be sent per SKU to the channel.", "type": "string", "required": 0, "values": []}, "block_image_enabled": {"name": "channelmeta_shopify", "key": "block_image_enabled", "default": "true", "description": "If false S2S creates images on Shopify. If true S2S cannot add or create images on Shopify.", "type": "string", "required": 0, "values": []}, "add_all_orders": {"name": "channelmeta_shopify", "key": "add_all_orders", "default": "false", "description": "If false only orders with paid status on Shopify will sync to the source. If true all orders are added to source regardless of order status.", "type": "string", "required": 0, "values": []}, "send_customer_email": {"name": "channelmeta_shopify", "key": "send_customer_email", "default": "false", "description": "Send customer order confirmation emails. Recommended to send order email directly from Shopify.", "type": "string", "required": 0, "values": []}, "hide_product_enabled": {"name": "channelmeta_shopify", "key": "hide_product_enabled", "default": "false", "description": "If false a product will be created and not hidden from the online store. If true a product is created but hidden from online store. This is useful when the product needs to be vetted before being published to the online store.", "type": "string", "required": 0, "values": []}, "ignore_category_enabled": {"name": "channelmeta_shopify", "key": "ignore_category_enabled", "default": "true", "description": "If true, the collection will not be created, updated or deleted on Shopify.If false product collections will be created on the online store based off the collection field in Stock2Shop.", "type": "string", "required": 0, "values": []}, "auto_sync": {"name": "channelmeta_shopify", "key": "auto_sync", "default": "true", "description": "If true, will check products on Shopify first and link them to system products. If product not found on Shopify it will create the product on Shopify from the system product. If false all system products will be created on Shopify regardless", "type": "string", "required": 0, "values": []}, "order_use_system_price": {"name": "channelmeta_shopify", "key": "order_use_system_price", "default": "false", "description": "If false, uses price from Shopify and If true, uses the price of the variant from S2S", "type": "string", "required": 0, "values": []}, "order_use_system_price_tax": {"name": "channelmeta_shopify", "key": "order_use_system_price_tax", "default": "15", "description": "Tells us if S2S channel price needs to include TAX (e.g. 15 = 15% tax added to system price)", "type": "string", "required": 0, "values": []}, "order_fixed_shipping_price": {"name": "channelmeta_shopify", "key": "order_fixed_shipping_price", "default": "", "description": "Fixed amount for shipping on the order (INCLUSIVE PRICE), if set Shopify shipping will be ignored", "type": "string", "required": 0, "values": []}, "order_fixed_shipping_tax": {"name": "channelmeta_shopify", "key": "order_fixed_shipping_tax", "default": "", "description": "Fixed rate (as a whole number, e.g. 15 = 15%) for shipping on the order and only required when fixed shipping price is set, if this meta is not included, no tax is charged", "type": "string", "required": 0, "values": []}, "order_use_system_price_tier": {"name": "channelmeta_shopify", "key": "order_use_system_price_tier", "default": "", "description": "Price tier to use only if \"order_use_system_price\" is set to true, if tier does not exist price will be 0.", "type": "string", "required": 0, "values": []}, "check_order_items_linked": {"name": "channelmeta_shopify", "key": "check_order_items_linked", "default": "false", "description": "If false allows orders to be raised to source without having corresponding variant/SKU in S2S.(SKU code must exist in accounting system.)", "type": "string", "required": 1, "values": []}, "pargo": {"name": "channelmeta_shopify", "key": "pargo", "default": "false", "description": "Set to true to enable Pargo shipping method for integration with ParcelNinja", "type": "string", "required": 0, "values": []}, "cost_price": {"name": "channelmeta_shopify", "key": "cost_price", "default": "{{price_tiers.Cost.price}}", "description": "Sync variant cost price.", "type": "string", "required": 0, "values": []}, "line_item_discounts": {"name": "channelmeta_shopify", "key": "line_item_discounts", "default": "false", "description": "If true Shopify orders will have discounts applied to line items and not to order total", "type": "string", "required": 0, "values": []}, "default_fulfillmentservice_id": {"name": "channelmeta_shopify", "key": "default_fulfillmentservice_id", "default": "", "description": "Default fulfilment service id to be used when creating fulfilment. Fulfilment service must be set up.", "type": "string", "required": 0, "values": []}, "hmac_shared_secret": {"name": "channelmeta_shopify", "key": "hmac_shared_secret", "default": "", "description": "HMAC shared secret  (Google for reference)", "type": "string", "required": 0, "values": []}, "group_duplicate_order_items": {"name": "channelmeta_shopify", "key": "group_duplicate_order_items", "default": "false", "description": "If false order line items will be as per Shopify line items. Set to true to group duplicate line items from Shopify.", "type": "string", "required": 0, "values": []}}, "takealot": {"group_duplicate_order_items": {"name": "channelmeta_takealot", "key": "group_duplicate_order_items", "default": "false", "description": "Use this if there are \"Duplicate line items are not allowed\" errors for orders", "type": "string", "required": 0, "values": []}, "check_order_items_linked": {"name": "channelmeta_takealot", "key": "check_order_items_linked", "default": "false", "description": "Ensures order items must have corresponding variant on S2S or the order fails.", "type": "string", "required": 1, "values": []}, "key": {"name": "channelmeta_takealot", "key": "key", "default": "*****", "description": "Takealot seller account key", "type": "string", "required": 1, "values": []}, "url": {"name": "channelmeta_takealot", "key": "url", "default": "https://seller-api.takealot.com/v2/", "description": "API url endpoint, ensure trailing forward slash", "type": "string", "required": 1, "values": []}, "offer_map": {"name": "channelmeta_takealot", "key": "offer_map", "default": "{             \"selling_price\": {{variant.price}},             \"rrp\": {{variant.price_tiers.Wholesale}},             \"leadtime_days\": {{meta.lead_time}},             \"leadtime_stock\": [                 {                     \"merchant_warehouse_id\": 0,                     \"quantity\": {{variant.qty}}                 }             ],             \"sku\": \"{{variant.sku}}\"         }", "description": "Template for creating an offer based on product and one variant, see example map on how to set properties. Consider hard coding leadtime to \"5\", which is the maximum. Note \"price\" must always be set. Warehouse ID should be hardcoded but can be added as meta eg: {{meta.warehouse_id}}", "type": "string", "required": 1, "values": []}, "gtin_map": {"name": "channelmeta_takealot", "key": "gtin_map", "default": "{{variant.barcode}}", "description": "GTIN, Global Trade Identification number (EAN-13 or ISBN-13 barcode). To create offers you must have a GTIN.", "type": "string", "required": 1, "values": []}, "order_map": {"name": "channelmeta_takealot", "key": "order_map", "default": "{ \t\"system_order\": { \t\t\"id\": \"{{sales.0.order_id}}\", \t\t\"instruction\": \"add_order\", \t\t\"total_discount\": \"\", \t\t\"created\": \"{{sales.0.order_date}}\", \t\t\"customer\": { \t\t\t\"id\": \"{{#generate_random}}8{{/generate_random}}\", \t\t\t\"first_name\": \"{{sales.0.first_name}}\", \t\t\t\"last_name\": \"{{sales.0.last_name}}\", \t\t\t\"email\": \"<EMAIL>\" \t\t}, \t\t\"billing_address\": {}, \t\t\"shipping_address\": {}, \t\t\"line_items\": [{{# sales}} { \t\t\t\"channel_product_code\": \"{{offer_id}}\", \t\t\t\"channel_variant_code\": \"{{offer_id}}\", \t\t\t\"price\": \"{{# calculate}}({{selling_price}}/{{quantity}})/1.15{{/ calculate}}\", \t\t\t\"qty\": \"{{quantity}}\", \t\t\t\"sku\": \"{{sku}}\", \t\t\t\"title\": \"{{# json_escape}}{{product_title}}{{/ json_escape}}\", \t\t\t\"total_discount\": \"\", \t\t\t\"tax_lines\": [{ \t\t\t\t\"price\": \"{{# calculate}}({{selling_price}}/{{quantity}})-(({{selling_price}}/{{quantity}})/1.15){{/ calculate}}\", \t\t\t\t\"rate\": \"15\", \t\t\t\t\"title\": \"VAT\", \t\t\t\t\"code\": \"taxed\" \t\t\t}] \t\t} {{^ last}}, {{/ last}} \t\t\t\t\t\t{{/ sales}}], \t\t\"shipping_lines\": [] \t}, \t\"params\": {} }", "description": "Used to transform takealot order to s2s order. The data transformed will be the result of view order https://seller-api.takealot.com/api-docs/#operation/view_sales We transform the returned sales information to include first_name, last_name and some other properties outlined in the default map.", "type": "string", "required": 1, "values": []}, "log_enabled": {"name": "channelmeta_takealot", "key": "log_enabled", "default": "false", "description": "Used for debugging, turn this off in production.", "type": "string", "required": 1, "values": []}}, "takealotretail": {"sync_mode": {"name": "channelmeta_takealotretail", "key": "sync_mode", "default": "bulk", "description": "Bulk mode means products will be synced on cron schedule with queue item \"sync_channel_bulk\", ignore or leave empty for \"instant\" sync", "type": "string", "required": 1, "values": []}, "url": {"name": "channelmeta_takealotretail", "key": "url", "default": "https://supplier-api.takealot.com/v1/", "description": "API url, no trailing slash, see https://supplier-api.takealot.com/api-docs", "type": "string", "required": 1, "values": []}, "api_key": {"name": "channelmeta_takealotretail", "key": "api_key", "default": "xxx", "description": "API key for authentication, see https://www.canva.com/design/DAE7-ACb0Lw/m8YGdy7jMS7BMRMrh9OlHQ/view", "type": "boolean", "required": 1, "values": []}, "product_map": {"name": "channelmeta_takealotretail", "key": "product_map", "default": "{\n           \"barcode\": \"{{variant.barcode}}\",\n           \"stock\": \"{{variant.qty}}\",\n           \"supplier_department_id\": \"{{meta.dept}}\"\n        }", "description": "Mustache template to define product payload. EAN must be included and a valid (13 digit alpha numeric) otherwise it will be ignored, see https://supplier-api.takealot.com/api-docs", "type": "string", "required": 1, "values": []}}, "trade": {"product_template": {"name": "channelmeta_trade", "key": "product_template", "default": "", "description": "Options are \"grid\" or \"list\" default is \"grid\"", "type": "string", "required": 0, "values": []}, "industry": {"name": "channelmeta_trade", "key": "industry", "default": "", "description": "Industry specific store, option is \"automotive\". Only use for year, make, and model.", "type": "string", "required": 0, "values": []}, "aggregations": {"name": "channelmeta_trade", "key": "aggregations", "default": "{ \t\"Department\": { \t\t\"nested\": { \t\t\t\"path\": \"meta\" \t\t}, \t\t\"aggs\": { \t\t\t\"Department\": { \t\t\t\t\"aggs\": { \t\t\t\t\t\"Department\": { \t\t\t\t\t\t\"terms\": { \t\t\t\t\t\t\t\"field\": \"meta.value\", \t\t\t\t\t\t\t\"exclude\": \"\", \t\t\t\t\t\t\t\"order\": { \t\t\t\t\t\t\t\t\"_term\": \"asc\" \t\t\t\t\t\t\t}, \t\t\t\t\t\t\t\"size\": 200 \t\t\t\t\t\t} \t\t\t\t\t} \t\t\t\t}, \t\t\t\t\"filter\": { \t\t\t\t\t\"term\": { \t\t\t\t\t\t\"meta.key\": \"department\" \t\t\t\t\t} \t\t\t\t} \t\t\t} \t\t} \t}, \t\"Collection\": { \t\t\"terms\": { \t\t\t\"field\": \"collection\", \t\t\t\"exclude\": \"\", \t\t\t\"order\": { \t\t\t\t\"_term\": \"asc\" \t\t\t}, \t\t\t\"size\": 200 \t\t} \t}, \t\"Product Type\": { \t\t\"terms\": { \t\t\t\"field\": \"product_type\", \t\t\t\"exclude\": \"\", \t\t\t\"order\": { \t\t\t\t\"_term\": \"asc\" \t\t\t}, \t\t\t\"size\": 200 \t\t} \t}, \t\"Brand\": { \t\t\"terms\": { \t\t\t\"field\": \"vendor\", \t\t\t\"exclude\": \"\", \t\t\t\"order\": { \t\t\t\t\"_term\": \"asc\" \t\t\t}, \t\t\t\"size\": 200 \t\t} \t}, \t\"Spare\": { \t\t\"nested\": { \t\t\t\"path\": \"meta\" \t\t}, \t\t\"aggs\": { \t\t\t\"Spare\": { \t\t\t\t\"aggs\": { \t\t\t\t\t\"Spare\": { \t\t\t\t\t\t\"terms\": { \t\t\t\t\t\t\t\"field\": \"meta.value\", \t\t\t\t\t\t\t\"exclude\": \"\", \t\t\t\t\t\t\t\"order\": { \t\t\t\t\t\t\t\t\"_term\": \"asc\" \t\t\t\t\t\t\t}, \t\t\t\t\t\t\t\"size\": 200 \t\t\t\t\t\t} \t\t\t\t\t} \t\t\t\t}, \t\t\t\t\"filter\": { \t\t\t\t\t\"term\": { \t\t\t\t\t\t\"meta.key\": \"spare\" \t\t\t\t\t} \t\t\t\t} \t\t\t} \t\t} \t} }", "description": "Elastic Search style aggregation used for layered navigation. Examples includes all types of fields in S2S. This is used for filtering products.", "type": "string", "required": 1, "values": []}, "domain_alias": {"name": "channelmeta_trade", "key": "domain_alias", "default": "https://b2b.mydomain.co.za", "description": "For clients using a custom domain. Used to redirect payment gateways like Adumo back to the custom domain. e.g. https://b2b.mydomain.co.za", "type": "string", "required": 0, "values": []}, "customer_warehouse_override": {"name": "channelmeta_trade", "key": "customer_warehouse_override", "default": "warehouse_code", "description": "Customer qty availability field determines order warehouse. This uses specific warehouse assigned to the customer when raising orders.", "type": "string", "required": 0, "values": []}, "option_sort_order": {"name": "channelmeta_trade", "key": "option_sort_order", "default": "{   \"{{option_name}}\": [\"{{option_value}}\", \"{{option_value}}\"],   \"Size\": [\"XS\", \"S\", \"M\", \"L\", \"XL\"] }", "description": "Used to sort variant options by option name. This is not case sensitive. For more info see https://github.com/stock2shop/b2b/pull/90", "type": "string", "required": 0, "values": []}, "product_info_display": {"name": "channelmeta_trade", "key": "product_info_display", "default": "{  \"Brand\": \"vendor\",  \"Category\": \"collection\",  \"Type\": \"product_type\"}", "description": "This is used to display information on the product listing page (Max 4 entries) JSON key value list, with the value being the stock2shop field. To add meta fields, use the value of \"meta_[meta field key]\"", "type": "string", "required": 1, "values": []}, "tax_rate_shipping": {"name": "channelmeta_trade", "key": "tax_rate_shipping", "default": "", "description": "Tax rate used for shipping, this can be overridden by customer meta", "type": "string", "required": 1, "values": []}, "tax_description": {"name": "channelmeta_trade", "key": "tax_description", "default": "VAT", "description": "Description of sales tax, e.g. GST or VAT", "type": "string", "required": 1, "values": []}, "currency": {"name": "channelmeta_trade", "key": "currency", "default": "R", "description": "Store currency, e.g. ZAR, $, USD etc... This will display as currency prefix in front of the price. Set this if currency for store is not in Rands. This can be added per customer as meta", "type": "string", "required": 0, "values": []}, "account_invoice": {"name": "channelmeta_trade", "key": "account_invoice", "default": "false", "description": "If true, the setting is used to create a link to download customer invoice. This requires custom development.", "type": "string", "required": 0, "values": []}, "account_statement": {"name": "channelmeta_trade", "key": "account_statement", "default": "false", "description": "If true, the setting is used to create a link to download customer statement. This requires custom development.", "type": "string", "required": 0, "values": []}, "filter_text_case": {"name": "channelmeta_trade", "key": "filter_text_case", "default": "capitalise", "description": "Change text casing of filter menu. Options are \"capitalise\", \"upper_case\", \"lower_case\"", "type": "string", "required": 0, "values": []}, "display_shipping_address": {"name": "channelmeta_trade", "key": "display_shipping_address", "default": "false", "description": "Display customers shipping address on checkout page.", "type": "boolean", "required": 0, "values": []}, "display_billing_address": {"name": "channelmeta_trade", "key": "display_billing_address", "default": "false", "description": "Display customers billing address on checkout page.", "type": "boolean", "required": 0, "values": []}, "checkout_fields": {"name": "channelmeta_trade", "key": "checkout_fields", "default": "{ \t\"notes\": { \t\t\"description\": \"Notes\", \t\t\"type\": \"textarea\", \t\t\"required\": true, \t\t\"value\": \"\", \t\t\"min_chars\": 5 \t}, \t\"params.customer_reference\": { \t\t\"description\": \"Customer Reference\", \t\t\"type\": \"text\", \t\t\"required\": true, \t\t\"value\": \"\", \t\t\"min_chars\": 30, \t\t\"max_chars\": 50 \t}, \t\"params.default_warehouse\": { \t\t\"description\": \"Example Select\", \t\t\"required\": true, \t\t\"type\": \"dropdown\", \t\t\"value\": \"\", \t\t\"options\": [{ \t\t\t\"key\": \"Select Heading\", \t\t\t\"value\": \"\" \t\t}, { \t\t\t\"key\": \"Option 1\", \t\t\t\"value\": \"ABC\" \t\t}, { \t\t\t\"key\": \"Option 2\", \t\t\t\"value\": \"XYZ\" \t\t}] \t}, \t\"params.abc\": { \t\t\"description\": \"Example Text Field\", \t\t\"type\": \"text\", \t\t\"required\": true, \t\t\"value\": \"abc\", \t\t\"min_chars\": 10, \t\t\"max_chars\": 10 \t}, \t\"params.xyz\": { \t\t\"description\": \"Example Checkbox\", \t\t\"type\": \"checkbox\", \t\t\"required\": false, \t\t\"value\": true \t}, \t\"params.edf\": { \t\t\"description\": \"Example Text Area\", \t\t\"type\": \"textarea\", \t\t\"required\": false, \t\t\"value\": \"abc xyz edf\", \t\t\"max_chars\": 100 \t}, \t\"params.due_date\": { \t\t\"description\": \"Example date field\", \t\t\"type\": \"date\", \t\t\"required\": false, \t\t\"value\": \"\" \t} }", "description": "List of fields to display at checkout. Fields can be required or not and can be set to order params or directly on the order, see example for default.", "type": "string", "required": 1, "values": []}, "logo": {"name": "channelmeta_trade", "key": "logo", "default": "", "description": "https: URL image link of logo (184px by 30px) to be displayed on the bottom left corner of the B2B trade store.", "type": "string", "required": 0, "values": []}, "elastic_suggest_fields": {"name": "channelmeta_trade", "key": "elastic_suggest_fields", "default": "variants.sku^3,title^3,variants.barcode", "description": "ElasticSearch fields to use for suggests (type ahead drop down on top navigation). See elastic search documentation for field weighting and other features", "type": "string", "required": 0, "values": []}, "hide_tax": {"name": "channelmeta_trade", "key": "hide_tax", "default": "false", "description": "If true, hides tax display throughout the site (Including cart). This does not affect the tax calculation in any way, only hides the tax display.", "type": "boolean", "required": 1, "values": []}, "elastic_query_fields": {"name": "channelmeta_trade", "key": "elastic_query_fields", "default": "variants.sku,variants.barcode,title,source_product_code,variants.source_variant_code,body_html,collection.english,product_type.english,meta.value,vendor.english", "description": "ElasticSearch fields to use for main search. See elastic search documentation for field weighting and other features", "type": "string", "required": 0, "values": []}, "account_display": {"name": "channelmeta_trade", "key": "account_display", "default": "{\n  \"Some Text\": \"abc\",\n  \"Balance\": \"meta_balance\",\n  \"Credit Limit\": \"meta_credit_limit\",\n  \"Sales Person\": \"meta_sales_person\"\n}", "description": "Map used to display additional information on the customers account page. Does not require the mustache template. Use meta_ to map customer attributes.", "type": "string", "required": 1, "values": []}, "param_notify_customer_order": {"name": "channelmeta_trade", "key": "param_notify_customer_order", "default": "", "description": "A semi-colon separated list of emails that will cc'd with the customer order confirmation email", "type": "string", "required": 0, "values": []}, "param_notify_add_source_order": {"name": "channelmeta_trade", "key": "param_notify_add_source_order", "default": "", "description": "A semi-colon separated list of emails that will receive the add source order email notification. Use this if you need to set the notification based on the customer attribute eg system_order.customer.meta.notify_email.value", "type": "string", "required": 0, "values": []}, "quick_order_columns": {"name": "channelmeta_trade", "key": "quick_order_columns", "default": "{\n            \"DESCRIPTION\": \"title\",\n            \"SOME NAME\": \"meta_[xyz]\"\n        }", "description": "Columns included on the quick order screen. Meta fields can be accessed as \"meta_[abc]\"", "type": "string", "required": 0, "values": []}, "address_line1": {"name": "channelmeta_trade", "key": "address_line1", "default": "", "description": "Store address line", "type": "string", "required": 1, "values": []}, "address_line2": {"name": "channelmeta_trade", "key": "address_line2", "default": "", "description": "Store address line 2", "type": "string", "required": 1, "values": []}, "address_line3": {"name": "channelmeta_trade", "key": "address_line3", "default": "", "description": "Store address line 3", "type": "string", "required": 1, "values": []}, "channel_order_code_prefix": {"name": "channelmeta_trade", "key": "channel_order_code_prefix", "default": "ORDER", "description": "Prefix applied to all orders", "type": "string", "required": 0, "values": []}, "display_name": {"name": "channelmeta_trade", "key": "display_name", "default": "", "description": "The name of the company displayed in the top left of the B2B Trade Store", "type": "string", "required": 1, "values": []}, "email": {"name": "channelmeta_trade", "key": "email", "default": "<EMAIL>", "description": "Email address to be displayed on the default terms and conditions.", "type": "string", "required": 1, "values": []}, "login_redirect": {"name": "channelmeta_trade", "key": "login_redirect", "default": "https://b2b.stock2shop.com", "description": "The URL link to redirect to on the customer invites and password resets emails. https://app.stock2shop.com/b2b would also work.", "type": "string", "required": 0, "values": []}, "queue_fulfill_order": {"name": "channelmeta_trade", "key": "queue_fulfill_order", "default": "false", "description": "Queue fulfill_order item after adding order to source or if the source has add_order_disabled", "type": "string", "required": 1, "values": []}, "qty_limit_upper": {"name": "channelmeta_trade", "key": "qty_limit_upper", "default": "50", "description": "Upper limit for channel qty - This will also restrict the qty orderable based on the value set here", "type": "string", "required": 0, "values": []}, "hide_availability_enabled": {"name": "channelmeta_trade", "key": "hide_availability_enabled", "default": "false", "description": "If true, hide In stock label and out of stock label from the customer.", "type": "string", "required": 1, "values": []}, "over_order_enabled": {"name": "channelmeta_trade", "key": "over_order_enabled", "default": "false", "description": "Allow customers to order products that are out of stock.", "type": "string", "required": 1, "values": []}, "phone": {"name": "channelmeta_trade", "key": "phone", "default": "", "description": "Client number on order view.", "type": "string", "required": 1, "values": []}, "price_display": {"name": "channelmeta_trade", "key": "price_display", "default": "exclusive", "description": "This is how pricing is displayed on the trade store.", "type": "string", "required": 1, "values": []}, "payment_methods": {"name": "channelmeta_trade", "key": "payment_methods", "default": "[{ \t\"method\": \"OnAccount\", \t\"description\": \"On Account\" }, { \t\"method\": \"AdumoVirtual\", \t\"description\": \"Credit Card (Adumo)\", \t\"iss\": \"Merchant Name PTY\", \t\"auid\": \"4196B0B8-DB88-42E5-A06D-294A5E4DED87\", \t\"cuid\": \"9BA5008C-08EE-4286-A349-54AF91A621B0\", \t\"jwt_secret\": \"yglTxLCSMm7PEsfaMszAKf2LSRvM2qVW\", \t\"url_staging\": \"https://staging-apiv2.adumoonline.com/product/payment/v1/initialisevirtual\", \t\"url_production\": \"https://apiv2.adumoonline.com/product/payment/v1/initialisevirtual\", \t\"in_production\": false, \t\"currency\": \"zar\" }, { \t\"method\": \"Stripe\", \t\"description\": \"Credit Card (Stripe)\", \t\"api_pk\": \"pk_test_76ovISW6bzckxoXoQoYaHnv400XUJi0u8s\", \t\"api_sk\": \"sk_test_eSfGmlOpmjRv5b1f6jKoD2Du00TAbUYxxy\", \t\"test_mode\": \"true\", \t\"currency\": \"usd\", \t\"currency_display\": \"$\" }]", "description": "Payment options available at checkout. This can be added per customer as meta. See Solutions.", "type": "string", "required": 0, "values": []}, "price_inclusive": {"name": "channelmeta_trade", "key": "price_inclusive", "default": "false", "description": "Is the price tier assigned to the trade store inclusive?", "type": "string", "required": 1, "values": []}, "send_customer_email": {"name": "channelmeta_trade", "key": "send_customer_email", "default": "true", "description": "Send customer emails", "type": "string", "required": 1, "values": []}, "tax_rate": {"name": "channelmeta_trade", "key": "tax_rate", "default": "15", "description": "Tax is calculated in the following hierarchy. 1. Does product have meta_tax_rate? 2. Does customer have meta_tax_rate 3. Does the channel have meta_tax_rate (this setting)", "type": "string", "required": 1, "values": []}, "terms": {"name": "channelmeta_trade", "key": "terms", "default": "", "description": "Terms HTML", "type": "string", "required": 1, "values": []}, "welcome_html": {"name": "channelmeta_trade", "key": "welcome_html", "default": "", "description": "Welcome HTML", "type": "string", "required": 1, "values": []}, "edit_shipping_address": {"name": "channelmeta_trade", "key": "edit_shipping_address", "default": "false", "description": "If true, then the customer can set their shipping address only for a specific order on checkout page.", "type": "boolean", "required": 0, "values": []}, "edit_billing_address": {"name": "channelmeta_trade", "key": "edit_billing_address", "default": "false", "description": "If true, then the customer can set their billing address only for a specific order on checkout page.", "type": "boolean", "required": 0, "values": []}, "manage_customer_address": {"name": "channelmeta_trade", "key": "manage_customer_address", "default": "false", "description": "If true, the customer can save addresses at checkout to use in the future", "type": "boolean", "required": 0, "values": []}, "qty_multiples_of": {"name": "channelmeta_trade", "key": "qty_multiples_of", "default": "1", "description": "The multiple of products must be ordered in. This is usually set on the actual product. e.g. You have a product thats unit of measure is 1 in the source, but they are sold in packs of 10, the products qty_multiples_of can be set to 10.", "type": "number", "required": 0, "values": []}, "minimum_order_qty": {"name": "channelmeta_trade", "key": "minimum_order_qty", "default": "1", "description": "The minimum qty each product must be ordered in. This is usually set on the actual product meta. See the solution https://stock2shop.freshdesk.com/a/solutions/articles/19000108765", "type": "number", "required": 0, "values": []}, "order_columns": {"name": "channelmeta_trade", "key": "order_columns", "default": "{\n            \"ORDER NO.\": \"order.id\",\n            \"SALES ORDER NO.\": \"order.sources[0].source_order_code\",\n            \"DATE\": \"order.created\",\n            \"TOTAL\": \"order.total\"\n        }", "description": "Columns included on the order list page. Meta fields can be accessed as \"meta.some_value\"", "type": "string", "required": 0, "values": []}, "order_view_display": {"name": "channelmeta_trade", "key": "order_view_display", "default": "{   \"ORDER NO\": \"order.channel_order_code\",   \"SALES ORDER NO.\": \"order.sources[0].source_order_code\",   \"CUSTOMER REF\": \"order.params.customer_reference\" }", "description": "Fields included on the order view page. Meta fields can be accessed as \"meta.some_value\"", "type": "string", "required": 0, "values": []}, "search_by_sku_default": {"name": "channelmeta_trade", "key": "search_by_sku_default", "default": "false", "description": "If set to true it will enable the 'Seach by SKU' feature by default", "type": "boolean", "required": 0, "values": []}, "show_availability_units": {"name": "channelmeta_trade", "key": "show_availability_units", "default": "true", "description": "Display units of stock available", "type": "string", "required": 1, "values": []}, "default_fulfillmentservice_id": {"name": "channelmeta_trade", "key": "default_fulfillmentservice_id", "default": "0", "description": "Default fulfilment service id to be used when creating fulfilment. Fulfilment service must be set up.", "type": "string", "required": 0, "values": []}, "hmac_shared_secret": {"name": "channelmeta_trade", "key": "hmac_shared_secret", "default": "", "description": "HMAC shared secret", "type": "string", "required": 0, "values": []}, "shipping_methods": {"name": "channelmeta_trade", "key": "shipping_methods", "default": "{ \t\"Next Day\": { \t\t\"method\": \"flat_rate\", \t\t\"price\": 100, \t\t\"tax_lines\": [{ \t\t\t\"rate\": 15, \t\t\t\"price\": 15, \t\t\t\"title\": \"VAT\", \t\t\t\"code\": \"taxed\" \t\t}] \t}, \t\"Same day delivery\": { \t\t\"method\": \"flat_rate\", \t\t\"price\": 200, \t\t\"tax_lines\": [{ \t\t\t\"rate\": 15, \t\t\t\"price\": 28, \t\t\t\"title\": \"VAT\", \t\t\t\"code\": \"taxed\" \t\t}] \t}, \t\"Rate based on cart\": { \t\t\"method\": \"table_rate\", \t\t\"rates\": [{ \t\t\t\"order_total\": 100, \t\t\t\"price\": 300, \t\t\t\"tax_lines\": [{ \t\t\t\t\"rate\": 15, \t\t\t\t\"price\": 42, \t\t\t\t\"title\": \"VAT\", \t\t\t\t\"code\": \"taxed\" \t\t\t}] \t\t}, { \t\t\t\"order_total\": 2000, \t\t\t\"price\": 200, \t\t\t\"tax_lines\": [{ \t\t\t\t\"rate\": 15, \t\t\t\t\"price\": 28, \t\t\t\t\"title\": \"VAT\", \t\t\t\t\"code\": \"taxed\" \t\t\t}] \t\t}] \t} }", "description": "Multiple shipping methods can be implemented for the customer to select. This can be added per customer as a meta. The format is JSON, with the key being the name displayed on the channel for each method", "type": "json", "required": 0, "values": []}, "group_duplicate_order_items": {"name": "channelmeta_trade", "key": "group_duplicate_order_items", "default": "false", "description": "If true, group duplicate line items from B2B Trade Store.If false order line items will be as per B2B Trade Store line items.", "type": "string", "required": 0, "values": []}, "channel_order_code_sequence": {"name": "channelmeta_trade", "key": "channel_order_code_sequence", "default": "0", "description": "Start number for the sequential_order_code", "type": "number", "required": 0, "values": []}, "min_order_amount": {"name": "channelmeta_trade", "key": "min_order_amount", "default": "", "description": "Minimum order amount ex tax (can only be a numeric value, if not in use remove setting)", "type": "string", "required": 0, "values": []}, "send_customer_email_from_name": {"name": "channelmeta_trade", "key": "send_customer_email_from_name", "default": "", "description": "The name that the customer emails are sent from", "type": "string", "required": 0, "values": []}, "send_customer_email_from": {"name": "channelmeta_trade", "key": "send_customer_email_from", "default": "", "description": "The email address that the customer emails are sent from, this must be verified on SES", "type": "string", "required": 0, "values": []}}, "usn": {"queue_fulfill_order": {"name": "channelmeta_usn", "key": "queue_fulfill_order", "default": "true", "description": "Queue fulfill_order item after adding order to source or if the source has add_order_disabled", "type": "string", "required": 0, "values": []}, "send_customer_email": {"name": "channelmeta_usn", "key": "send_customer_email", "default": "true", "description": "Send customer emails", "type": "string", "required": 0, "values": []}, "token": {"name": "channelmeta_usn", "key": "token", "default": "xyz", "description": "", "type": "string", "required": 0, "values": []}, "default_fulfillmentservice_id": {"name": "channelmeta_usn", "key": "default_fulfillmentservice_id", "default": "0", "description": "Default fulfillment service id", "type": "string", "required": 0, "values": []}, "hmac_shared_secret": {"name": "channelmeta_usn", "key": "hmac_shared_secret", "default": "", "description": "HMAC shared secret", "type": "string", "required": 0, "values": []}, "group_duplicate_order_items": {"name": "channelmeta_usn", "key": "group_duplicate_order_items", "default": "false", "description": "Group duplicate order items", "type": "string", "required": 0, "values": []}}, "woocommerce": {"order_link_items_on_sku": {"name": "channelmeta_woocommerce", "key": "order_link_items_on_sku", "default": "false", "description": "If set to true, all products and variants not linked to channel will be created on S2S and linked via SKU on new order.", "type": "string", "required": 0, "values": []}, "order_use_system_price": {"name": "channelmeta_woocommerce", "key": "order_use_system_price", "default": "false", "description": "If set to true, uses the price of the variant from S2S and not from Woocommerce.", "type": "string", "required": 0, "values": []}, "order_use_system_price_tier": {"name": "channelmeta_woocommerce", "key": "order_use_system_price_tier", "default": "", "description": "Price tier to use only if \"order_use_system_price\" is set to true, if price tier does not exist in S2S price will be 0", "type": "string", "required": 0, "values": []}, "order_use_system_price_tax": {"name": "channelmeta_woocommerce", "key": "order_use_system_price_tax", "default": "", "description": "Tells us if S2S channel price needs to include TAX (e.g. 15 = 15% tax added to system price). Use setting to override website tax.", "type": "string", "required": 0, "values": []}, "delete_product_enabled": {"name": "channelmeta_woocommerce", "key": "delete_product_enabled", "default": "true", "description": "If set to true, products will be deleted from the store. If set to anything else, products will not be deleted", "type": "string", "required": 0, "values": []}, "consumer_key": {"name": "channelmeta_woocommerce", "key": "consumer_key", "default": "xxx", "description": "Consumer key to access the API.", "type": "string", "required": 1, "values": []}, "consumer_secret": {"name": "channelmeta_woocommerce", "key": "consumer_secret", "default": "xxx", "description": "Consumer secret to access the API.", "type": "string", "required": 1, "values": []}, "api_url": {"name": "channelmeta_woocommerce", "key": "api_url", "default": "https://www.your-store.com/wc-api/v3", "description": "Must run over https on client domain! Test URL to confirm API set up.", "type": "string", "required": 1, "values": []}, "add_order_status": {"name": "channelmeta_woocommerce", "key": "add_order_status", "default": "processing", "description": "The woo commerce order status to use for adding an order to source.", "type": "string", "required": 0, "values": []}, "product_map": {"name": "channelmeta_woocommerce", "key": "product_map", "default": "{\n\"title\": \"{{product.title}}\",\n\"variations\": [\n{\n\"sku\": \"{{variant.sku}}\",\n\"regular_price\": \"{{variant.price}}\",\n\"stock_quantity\": \"{{variant.qty}}\"\n}\n]\n}", "description": "Product map overrides the basic product settings. To call price tier use the following \"{{variant.price_tier_MyPriceTier}}\". To call a warehouse use the following \"{{variant.qty_availability_MyWarehouse}}\" {{#calculate}} function not possible - Will set values to '0'", "type": "string", "required": 0, "values": []}, "create_product_enabled": {"name": "channelmeta_woocommerce", "key": "create_product_enabled", "default": "true", "description": "If set to true allow new products to be created. If false only products already on the channel will update - any that do not exist will be given a channel ID of '0'.", "type": "string", "required": 0, "values": []}, "create_product_status": {"name": "channelmeta_woocommerce", "key": "create_product_status", "default": "publish", "description": "If set, it will use this status only when creating a new product, default is publish", "type": "string", "required": 0, "values": []}, "create_image_enabled": {"name": "channelmeta_woocommerce", "key": "create_image_enabled", "default": "true", "description": "If set to true allows the creation of new images. If false images will not be created.", "type": "string", "required": 0, "values": []}, "delete_image_enabled": {"name": "channelmeta_woocommerce", "key": "delete_image_enabled", "default": "true", "description": "If set to true allows the deletion of images that S2S has created. If false images will not be deleted.", "type": "string", "required": 0, "values": []}, "manage_category_enabled": {"name": "channelmeta_woocommerce", "key": "manage_category_enabled", "default": "true", "description": "If set to true allows the creation of categories from the collection field in S2S. If false categories will not be created.Don't forget to set parent id", "type": "string", "required": 0, "values": []}, "category_parent_id": {"name": "channelmeta_woocommerce", "key": "category_parent_id", "default": "true", "description": "The Woocommerce category ID to use as parent for S2S.", "type": "string", "required": 0, "values": []}, "queue_fulfill_order": {"name": "channelmeta_woocommerce", "key": "queue_fulfill_order", "default": "false", "description": "Queue fulfil_order item after adding order to source or if the source has add_order_disabled", "type": "string", "required": 0, "values": []}, "authentication": {"name": "channelmeta_woocommerce", "key": "authentication", "default": "query_params", "description": "Options are basic or query_params, query_params passes the key and secret in the url over https, warning, server may log these. Only use basic authentication if query_params does not work.", "type": "string", "required": 1, "values": []}, "inventory_management": {"name": "channelmeta_woocommerce", "key": "inventory_management", "default": "true", "description": "If set to false, in_stock and managing_stock will be omitted when sending data to woo.", "type": "string", "required": 0, "values": []}, "order_code_field": {"name": "channelmeta_woocommerce", "key": "order_code_field", "default": "order_number", "description": "The Woocommerce field in the order webhook used for order number, if this is not set the id field is used", "type": "string", "required": 0, "values": []}, "order_code_prefix": {"name": "channelmeta_woocommerce", "key": "order_code_prefix", "default": "#", "description": "Prefix all orders with this", "type": "string", "required": 0, "values": []}, "default_fulfillmentservice_id": {"name": "channelmeta_woocommerce", "key": "default_fulfillmentservice_id", "default": "0", "description": "Default fulfilment service id. Make sure the fulfilment service is setup.", "type": "string", "required": 0, "values": []}, "hmac_shared_secret": {"name": "channelmeta_woocommerce", "key": "hmac_shared_secret", "default": "", "description": "HMAC shared secret.", "type": "string", "required": 0, "values": []}, "group_duplicate_order_items": {"name": "channelmeta_woocommerce", "key": "group_duplicate_order_items", "default": "false", "description": "If false order line items will be as per Woocommerce line items. Set to true to group duplicate line items from Woocommerce.", "type": "string", "required": 0, "values": []}, "order_shipping_method_name": {"name": "channelmeta_woocommerce", "key": "order_shipping_method_name", "default": "", "description": "Use this description when creating an order for shipping.", "type": "string", "required": 0, "values": []}, "bundles": {"name": "channelmeta_woocommerce", "key": "bundles", "default": "{   \"sku of bundle\": [     \"sku of item 1\",     \"sku of item 2\",     \"sku of item 3\"   ] }", "description": "JSON to define bundles, bundles need to exist on Woo. At this point in time we pass the price of R0 for each bundled item into the accounting system.", "type": "string", "required": 0, "values": []}}}}