{"system_channel_meta": [{"id": 44576, "client_id": 927, "channel_id": 1880, "key": "account_display", "value": "{\"Credit Limit\":\"20000\",\"Balance\":\"10000\",\"Status\":\"Active2\"}", "encrypt": false}, {"id": 44577, "client_id": 927, "channel_id": 1880, "key": "address_line1", "value": "103 Sunny", "encrypt": false}, {"id": 44578, "client_id": 927, "channel_id": 1880, "key": "address_line2", "value": "Sunnyville", "encrypt": false}, {"id": 44579, "client_id": 927, "channel_id": 1880, "key": "address_line3", "value": "Earth", "encrypt": false}, {"id": 44581, "client_id": 927, "channel_id": 1880, "key": "checkout_fields", "value": "{  \"notes\": {    \"description\": \"Notes\",    \"type\": \"textarea\",    \"required\": false,    \"value\": \"Special instructions go here.\"  },  \"params.customer_reference\": {    \"description\": \"Customer Reference\",    \"type\": \"text\",    \"required\": true,    \"value\": \"Order Number:\"  },  \"params.delivery_date\": {        \"description\": \"Requested Delivery Date\",        \"type\": \"date\",        \"required\": false,        \"valid_after_days\": \"7\",        \"value\": \"\"      }}", "encrypt": false}, {"id": 44582, "client_id": 927, "channel_id": 1880, "key": "display_name", "value": "KRS Sage One", "encrypt": false}, {"id": 44583, "client_id": 927, "channel_id": 1880, "key": "email", "value": "<EMAIL>", "encrypt": false}, {"id": 44584, "client_id": 927, "channel_id": 1880, "key": "hide_availability_enabled", "value": "false", "encrypt": false}, {"id": 44586, "client_id": 927, "channel_id": 1880, "key": "over_order_enabled", "value": "true", "encrypt": false}, {"id": 44587, "client_id": 927, "channel_id": 1880, "key": "phone", "value": "086010101010", "encrypt": false}, {"id": 44588, "client_id": 927, "channel_id": 1880, "key": "price_display", "value": "exclusive", "encrypt": false}, {"id": 44589, "client_id": 927, "channel_id": 1880, "key": "price_inclusive", "value": "false", "encrypt": false}, {"id": 44591, "client_id": 927, "channel_id": 1880, "key": "queue_fulfill_order", "value": "false", "encrypt": false}, {"id": 44592, "client_id": 927, "channel_id": 1880, "key": "send_customer_email", "value": "true", "encrypt": false}, {"id": 44593, "client_id": 927, "channel_id": 1880, "key": "show_availability_units", "value": "true", "encrypt": false}, {"id": 44595, "client_id": 927, "channel_id": 1880, "key": "tax_rate", "value": "15", "encrypt": false}, {"id": 44598, "client_id": 927, "channel_id": 1880, "key": "welcome_html", "value": "<div class=\"row\">    <div class=\"col-xs-12 col-md-12\">        <a href=\"https://b2b.stock2shop.com/#/products\">            <img class=\"img-responsive\" src=\"https://drive.google.com/uc?export=view&id=1sj4Q9NHv_qBx4HMI-gFXOnwBcfkKaZHQ\">        </a>    </div>    <div class=\"col-xs-12 col-md-12\">        <img class=\"img-responsive\" src=\"https://drive.google.com/uc?export=view&id=1h8Etbf_lRlSxUZaZTNzWwSE3fG0wyVWL\">    </div></div><div class=\"row s2s_image\">    <div class=\"col-xs-12 col-sm-6 col-md-4\">        <div class=\"s2s_image_block\">            <a href=\"https://b2b.stock2shop.com/#/products?collection=laptops\">                <img class=\"img-responsive\" src=\"https://drive.google.com/uc?export=view&id=1nNiT9DQr4jCjGUKlgrCxMtMBtVFvyij2\">            </a>        </div>    </div>    <div class=\"col-xs-12 col-sm-6 col-md-4\">        <div class=\"s2s_image_block\">            <a href=\"https://b2b.stock2shop.com/#/products?collection=cables\">                <img class=\"img-responsive\" src=\"https://drive.google.com/uc?export=view&id=1NYPXFqGEOmNOUApQ_gcnw7utX6VZG04L\">            </a>        </div>    </div>    <div class=\"col-xs-12 col-sm-6 col-md-4\">        <div class=\"s2s_image_block\">            <a href=\"https://b2b.stock2shop.com/#/products?collection=software\">                <img class=\"img-responsive\" src=\"https://drive.google.com/uc?export=view&id=1W7GPtujdN225G_iKUHZ7AB8ztiVV_CB3\">            </a>        </div>    </div>    <div class=\"col-xs-12 col-sm-6 col-md-4\">        <div class=\"s2s_image_block\">            <a href=\"https://b2b.stock2shop.com/#/products?collection=storage\">                <img class=\"img-responsive\" src=\"https://drive.google.com/uc?export=view&id=1I2TPi2mr9AoFNVcrz-F0SLmmtdGI397O\">            </a>        </div>    </div>    <div class=\"col-xs-12 col-sm-6 col-md-4\">        <div class=\"s2s_image_block\">            <a href=\"https://b2b.stock2shop.com/#/products?collection=cpus\">                <img class=\"img-responsive\" src=\"https://drive.google.com/uc?export=view&id=1yY3TnQIBFIRbWklriG_4ENj0wJU-FRbh\">            </a>        </div>    </div>    <div class=\"col-xs-12 col-sm-6 col-md-4\">        <div class=\"s2s_image_block\">            <a href=\"https://b2b.stock2shop.com/#/products?collection=desktops\">                <img class=\"img-responsive\" src=\"https://drive.google.com/uc?export=view&id=1lx8ewvUdnUyhYPsYIHIyIVH44YnTlqHa\">            </a>        </div>    </div>    <div class=\"col-xs-12 col-sm-6 col-md-4\">        <div class=\"s2s_image_block\">            <a href=\"https://b2b.stock2shop.com/#/products?collection=monitors\">                <img class=\"img-responsive\" src=\"https://drive.google.com/uc?export=view&id=1CLYxghG3WGsTdzXJSu5q49q6rsoArerX\">            </a>        </div>    </div>    <div class=\"col-xs-12 col-sm-6 col-md-4\">        <div class=\"s2s_image_block\">            <a href=\"https://b2b.stock2shop.com/#/products?collection=peripherals\">                <img class=\"img-responsive\" src=\"https://drive.google.com/uc?export=view&id=1A9AP26m9rF6mf88uTQbMF3hlYl0-WOSE\">            </a>        </div>    </div>    <div class=\"col-xs-12 col-sm-6 col-md-4\">        <div class=\"s2s_image_block\">            <a href=\"https://b2b.stock2shop.com/#/products?collection=headphones\">                <img class=\"img-responsive\" src=\"https://drive.google.com/uc?export=view&id=120jANJDuEoGUQHM15wmGAybeYum39bfu\">            </a>        </div>    </div></div><style class=\"ng-scope\">    .s_dashboard .content {        padding: 0px;    }    .s_dashboard .s2s_image {        padding: 25px 0px 0px 5px;        margin: 0px 0px 0px 0px;    }    .s_dashboard .s2s_image>[class*='col-'] {        padding: 0px 2px 0px 0px;    }    .s2s_image_block {        padding: 0 0px;    }</style>", "encrypt": false}, {"id": 44599, "client_id": 927, "channel_id": 1880, "key": "channel_order_code_prefix", "value": "KRS3", "encrypt": false}, {"id": 44601, "client_id": 927, "channel_id": 1880, "key": "channel_order_code_sequence", "value": "1083", "encrypt": false}, {"id": 44602, "client_id": 927, "channel_id": 1880, "key": "default_fulfillmentservice_id", "value": "170", "encrypt": false}, {"id": 44603, "client_id": 927, "channel_id": 1880, "key": "display_shipping_address", "value": "false", "encrypt": false}, {"id": 44604, "client_id": 927, "channel_id": 1880, "key": "edit_billing_address", "value": "false", "encrypt": false}, {"id": 44605, "client_id": 927, "channel_id": 1880, "key": "edit_shipping_address", "value": "false", "encrypt": false}, {"id": 44606, "client_id": 927, "channel_id": 1880, "key": "hmac_shared_secret", "value": "57016bf0c81426fb41e2988b077cc1f6", "encrypt": false}, {"id": 44607, "client_id": 927, "channel_id": 1880, "key": "login_redirect", "value": "TEST", "encrypt": false}, {"id": 44608, "client_id": 927, "channel_id": 1880, "key": "logo", "value": "https://drive.google.com/uc?export=view&id=1bLqMrBrAYTPTos0LARxm", "encrypt": false}, {"id": 44609, "client_id": 927, "channel_id": 1880, "key": "manage_customer_address", "value": "false", "encrypt": false}, {"id": 44610, "client_id": 927, "channel_id": 1880, "key": "order_columns", "value": "{            \"ORDER NO.\": \"order.id\",            \"SALES ORDER NO.\": \"order.sources[0].source_order_code\",\"CUSTOMER REF\": \"order.meta.customer_reference\",\"ORDER STATUS\": \"order.status\",            \"DATE\": \"order.created\",            \"TOTAL\": \"order.total\"        }", "encrypt": false}, {"id": 44611, "client_id": 927, "channel_id": 1880, "key": "order_view_display", "value": "{  \"ORDER NO\": \"order.channel_order_code\",  \"SALES ORDER NO.\": \"order.sources[0].source_order_code\",  \"CUSTOMER REF\": \"order.params.customer_reference\"}", "encrypt": false}, {"id": 44612, "client_id": 927, "channel_id": 1880, "key": "param_collection", "value": "false", "encrypt": false}, {"id": 44613, "client_id": 927, "channel_id": 1880, "key": "param_delivery_date_days", "value": "3", "encrypt": false}, {"id": 44615, "client_id": 927, "channel_id": 1880, "key": "payment_methods", "value": "[{\"method\":\"MygateVirtual\",\"description\":\"TEST3\",\"iss\":\"\",\"auid\":\"\",\"cuid\":\"\",\"jwt_secret\":\"\",\"url\":\"sdg\",\"url_staging\":\"\",\"url_production\":\"\",\"in_production\":false,\"currency\":\"\",\"api_pk\":\"\",\"api_sk\":\"\",\"test_mode\":false,\"currency_display\":\"\",\"merchant_id\":\"sdgsdg\",\"application_id\":\"sdgsdg\",\"mode\":\"wjtrj\"},{\"method\":\"OnAccount\",\"description\":\"PAYMENT CHANGES4\",\"iss\":\"\",\"auid\":\"\",\"cuid\":\"\",\"jwt_secret\":\"\",\"url\":\"\",\"url_staging\":\"\",\"url_production\":\"\",\"in_production\":false,\"currency\":\"\",\"api_pk\":\"\",\"api_sk\":\"\",\"test_mode\":false,\"currency_display\":\"\",\"merchant_id\":\"\",\"application_id\":\"\",\"mode\":\"\"}]", "encrypt": false}, {"id": 44617, "client_id": 927, "channel_id": 1880, "key": "shipping_methods", "value": "{\"Next Day\":{\"method\":\"flat_rate\",\"price\":100,\"tax_lines\":[{\"rate\":15,\"price\":15,\"title\":\"VAT\",\"code\":\"taxed\"}]},\"Same day delivery\":{\"method\":\"flat_rate\",\"price\":200,\"tax_lines\":[{\"rate\":15,\"price\":28,\"title\":\"VAT\",\"code\":\"taxed\"}]},\"Rate based on cart\":{\"method\":\"table_rate\",\"rates\":[{\"order_total\":100,\"price\":300,\"tax_lines\":[{\"rate\":15,\"price\":42,\"title\":\"VAT\",\"code\":\"taxed\"}]},{\"order_total\":20,\"price\":200,\"tax_lines\":[{\"rate\":15,\"price\":28,\"title\":\"VAT\",\"code\":\"taxed\"}]}]},\"JTESTFLAT\":{\"title\":\"JTESTFLAT\",\"method\":\"flat_rate\",\"price\":1000,\"tax_lines\":[{\"rate\":10,\"price\":15,\"title\":\"VAT\",\"code\":\"taxed\"}],\"rates\":[{}],\"prop\":\"JTESTFLAT\"},\"JTESTTABLE\":{\"title\":\"JTESTTABLE\",\"method\":\"table_rate\",\"price\":0,\"tax_lines\":[{\"rate\":0,\"price\":0,\"title\":\"\",\"code\":\"\"}],\"rates\":[{\"tax_lines\":[{\"rate\":10,\"price\":10,\"title\":\"VATT\",\"code\":\"taxed\"}],\"order_total\":1000,\"price\":1000}],\"prop\":\"JTESTTABLE\"}}", "encrypt": false}, {"id": 44618, "client_id": 927, "channel_id": 1880, "key": "param_order_type", "value": "sales_order", "encrypt": false}, {"id": 46076, "client_id": 927, "channel_id": 1880, "key": "option_sort_order", "value": "{\"Size\":[\"Small\",\"Medium\"],\"Colour\":[\"red\",\"green\",\"blue\"],\"e.g Size2\":\"e.g. XL,L,M,S,XS\"}", "encrypt": false}, {"id": 46334, "client_id": 927, "channel_id": 1880, "key": "domain_alias", "value": "TEST", "encrypt": false}, {"id": 46371, "client_id": 927, "channel_id": 1880, "key": "elastic_query_fields", "value": "variant.test^3,variant.normal,testvim^5", "encrypt": false}, {"id": 46409, "client_id": 927, "channel_id": 1880, "key": "search_facets", "value": "{\"A\":{\"field\":\"meta.material2\",\"hide_count\":false,\"requires\":\"\"},\"D\":{\"field\":\"D\",\"hide_count\":false,\"requires\":\"\"},\"B\":{\"field\":\"test.b\",\"hide_count\":false,\"requires\":\"A\"}}", "encrypt": false}, {"id": 46423, "client_id": 927, "channel_id": 1880, "key": "elastic_suggest_fields", "value": "suggest.field^5,another improtant^3", "encrypt": false}, {"id": 46509, "client_id": 927, "channel_id": 1880, "key": "product_template", "value": "list", "encrypt": false}, {"id": 46611, "client_id": 927, "channel_id": 1880, "key": "custom_user_invite_message_markdown_template", "value": "This is a test message updated.", "encrypt": false}]}