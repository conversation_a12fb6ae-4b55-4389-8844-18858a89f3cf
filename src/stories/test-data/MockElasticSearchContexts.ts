import { readable, writable } from 'svelte/store';
import { useElasticSearchContext } from '@/lib/es/context/useElasticSearchContext';
import type { SystemProductsResponse } from '@/lib/s2s/types';
import { ElasticSearchIndex } from '@/lib/s2s/types';
import esRoutes from '@/lib/esRoutes';
import { configLocal } from '@/env/config.local';
import type { SystemOrdersDataWrapper } from '../../models/orders/sytem-orders-data-wrapper';
import type SystemCustomer from '@/models/customer/customer';
// import type { IElasticContext } from '@/lib/ES/context/createElasticContext';

export function useMockProductsSearchContext() {
  const isFetching = writable(true);
  return useElasticSearchContext<SystemProductsResponse>({
    index: ElasticSearchIndex.PRODUCTS,
    request: { path: esRoutes.searchProducts },
    isFetching,
    facets: readable(configLocal.system.elasticSearch.products.facets),
    onInit: () => {
      // (context: IElasticContext<SystemProductsResponse>) => {
    },
    onRefetch: () => {
      isFetching.set(false);
    }
  });
}

export function useMockCustomerOrdersSearchContext() {
  const isFetching = writable(true);
  return useElasticSearchContext<SystemOrdersDataWrapper>({
    index: ElasticSearchIndex.ORDERS,
    request: { path: esRoutes.searchOrders },
    isFetching,
    facets: readable(configLocal.system.elasticSearch.customerOrders.facets),
    onInit: () => {
      // (context: IElasticContext<SystemProductsResponse>) => {
    },
    onRefetch: () => {
      isFetching.set(false);
    }
  });
}

export function useMockCustomersSearchContext() {
  const isFetching = writable(true);
  return useElasticSearchContext<SystemCustomer>({
    index: ElasticSearchIndex.CUSTOMERS,
    request: { path: esRoutes.searchCustomers },
    isFetching,
    facets: readable(configLocal.system.elasticSearch.customers.facets),
    onInit: () => {
      // (context: IElasticContext<SystemProductsResponse>) => {
    },
    onRefetch: () => {
      isFetching.set(false);
    }
  });
}
