{"params": {"customer_reference": "Order Number: 000001", "delivery_date": "2023-02-27T00:00:00.000Z", "payment_method": "OnAccount", "console_user_id": "242044", "coupon": "", "collection": "false", "delivery_date_days": "3", "order_type": "sales_order", "console_user_name": ""}, "sources": [{"source_id": 1423, "source_order_code": "SO0000170", "source_customer_code": "<PERSON>"}], "system_order": {"id": 3403907, "created": "2023-02-17 13:20:05.000000", "channel_order_code": "B2B0001068", "modified": "2023-02-17 13:37:23.000000", "channel_id": 1880, "client_id": 927, "customer_id": 5676648, "notes": "Test for KRS", "status": "ordered", "total_discount": 0, "ordered_date": null, "customer": {"id": 5676648, "created": "2023-02-17 13:08:37.000000", "channel_customer_code": "5676648", "modified": "2023-03-28 10:37:07.000000", "channel_id": 1880, "client_id": 927, "last_name": "Canell4", "first_name": "<PERSON>", "email": "joe", "accepts_marketing": 0, "active": 1, "hash": "a4919f34a6e022f908f35040251b9c9a", "addresses": [], "companyName": "", "send_email_invite": false}, "history": [{"id": 9327961, "created": "2023-02-17 13:20:05.000000", "modified": "2023-02-17 13:20:05.000000", "order_id": 3403907, "instruction": "sync_order", "client_id": "927", "storage_code": "927-1880-0/2023-02/17/13/20/04-*********-orderhistory-sync_order-3403907.json"}, {"id": 9327963, "created": "2023-02-17 13:20:05.000000", "modified": "2023-02-17 13:20:05.000000", "order_id": 3403907, "instruction": "unpaid_order", "client_id": "927", "storage_code": "927-1880-0/2023-02/17/13/20/04-*********-orderhistory-unpaid_order-3403907.json"}, {"id": 9327964, "created": "2023-02-17 13:20:05.000000", "modified": "2023-02-17 13:20:05.000000", "order_id": 3403907, "instruction": "provisionally_paid", "client_id": "927", "storage_code": "927-1880-0/2023-02/17/13/20/04-*********-orderhistory-unpaid_order-3403907.json"}], "billing_address": {"address1": "", "address2": "", "city": "", "company": "", "country": "", "first_name": "", "last_name": "", "phone": "", "province": "", "zip": "", "country_code": "", "province_code": "", "default": false}, "shipping_address": {"address1": "1 Hout Bay", "address2": "Beach Cresent", "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "phone": "0800 556 123", "province": "Western Cape", "zip": "9998", "country_code": "za", "province_code": "wc", "default": false}, "line_items": [{"id": ********, "created": "2023-02-17 13:20:05.000000", "barcode": null, "price": 2550, "qty": 1, "sku": "ACC/LOC", "modified": "2023-02-17 13:20:05.000000", "order_id": 3403907, "source_id": 1423, "variant_id": 5298897, "title": "Accounting Software (OEM)", "grams": null, "code": "item", "total_discount": 0, "client_id": "927", "product_id": 3163048, "source_variant_code": "4210162", "tax_lines": [{"id": ********, "created": "2023-02-17 13:20:05.000000", "price": 382.5, "rate": 15, "title": "TAX", "code": "taxed", "modified": "2023-02-17 13:20:05.000000", "orderitem_id": "********", "client_id": "927"}], "fulfillments": [], "sub_total": 2550, "tax": 382.5, "tax_per_unit": 382.5, "total": 2932.5, "sub_total_display": "2 550.00", "tax_display": "382.50", "tax_per_unit_display": "382.50", "total_display": "2 932.50", "price_display": "2 550.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": ********, "created": "2023-02-17 13:20:05.000000", "barcode": null, "price": 100, "qty": 1, "sku": null, "modified": "2023-02-17 13:20:05.000000", "order_id": 3403907, "source_id": null, "variant_id": null, "title": "Next Day", "grams": null, "code": "ship", "total_discount": 0, "client_id": "927", "product_id": null, "source_variant_code": null, "tax_lines": [{"id": 15479834, "created": "2023-02-17 13:20:05.000000", "price": 15, "rate": 15, "title": "VAT", "code": "taxed", "modified": "2023-02-17 13:20:05.000000", "orderitem_id": "********", "client_id": "927"}], "fulfillments": [], "sub_total": 100, "tax": 15, "tax_per_unit": 15, "total": 115, "sub_total_display": "100.00", "tax_display": "15.00", "tax_per_unit_display": "15.00", "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "fulfillments": [], "sub_total": 2550, "shipping_total": 100, "shipping_tax": 15, "tax": 397.5, "total": 3047.5, "sub_total_display": "2 550.00", "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "397.50", "total_display": "3 047.50", "total_discount_display": "0.00"}}