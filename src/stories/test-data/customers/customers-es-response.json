{"took": 76, "timed_out": false, "_shards": {"total": 5, "successful": 5, "skipped": 0, "failed": 0}, "hits": {"total": {"value": 9, "relation": "eq"}, "max_score": 0}, "aggregations": {"meta.key": {"doc_count": 15, "meta.key": {"doc_count": 15, "meta.key": {"doc_count_error_upper_bound": 0, "sum_other_doc_count": 5, "buckets": [{"key": "back_order", "doc_count": 1}, {"key": "credit_limit", "doc_count": 1}, {"key": "order_0|entity_product|key_brand|value_asd|type_discount", "doc_count": 1}, {"key": "order_0|entity_product|key_brand|value_gfd|type_discount", "doc_count": 1}, {"key": "order_0|entity_product|key_brand|value_ttt|type_discount", "doc_count": 1}, {"key": "order_0|entity_product|key_brand|value_yyy|type_discount", "doc_count": 1}, {"key": "order_0|entity_product|key_created|value_3|type_discount", "doc_count": 1}, {"key": "order_0|entity_product|key_price|value_123|type_discount", "doc_count": 1}, {"key": "order_0|entity_product|key_price|value_12|type_discount", "doc_count": 1}, {"key": "order_0|entity_product|key_price|value_12|type_fixed", "doc_count": 1}]}}}}, "request": {"url": "https://vpc-stock2shop-647vemf3m3u22fhny7f7dqjs4m.eu-west-1.es.amazonaws.com/customers/_search", "method": "POST", "body": {"aggs": {"meta.key": {"nested": {"path": "meta"}, "aggs": {"meta.key": {"aggs": {"meta.key": {"terms": {"field": "meta.key"}}}, "filter": {"bool": {"filter": [{"prefix": {"meta.key": ""}}]}}}}}}, "query": {"bool": {"filter": [{"term": {"client_id": 927}}], "must": [], "must_not": []}}, "from": 0, "size": 10, "sort": {"_score": {"order": "desc"}}}}, "system_customers": [{"id": 6263834, "created": "2023-05-15 10:11:11", "channel_customer_code": "6263834", "modified": "2023-05-15 10:11:11", "channel_id": 1880, "client_id": 927, "last_name": "Krstest5", "first_name": "Krstest5", "email": "<EMAIL>", "accepts_marketing": false, "active": true, "hash": "6f0d14e01a1114893c63e7f610d0ade9", "meta": [], "sources": [{"source_customer_code": "SCC1", "id": 1423, "created": "2023-02-17 12:05:58", "description": "KRS Sage One", "sync_token": "2023-02-17T10:27:14.567", "modified": "2023-02-17 12:55:15", "client_id": 927, "type": "sageone", "active": true}], "addresses": [], "user": {"id": 251522, "created": "2023-05-15 10:11:11", "name": "Krstest5", "surname": "Krstest5", "email": "<EMAIL>", "username": "krstest5", "modified": "2023-05-15 10:11:12", "client_id": 927, "active": true, "price_tier": "Wholesale", "qty_availability": null, "segments": []}}, {"id": 6249198, "created": "2023-05-12 11:24:40", "channel_customer_code": "6249198", "modified": "2023-05-12 11:24:40", "channel_id": 1880, "client_id": 927, "last_name": "v1", "first_name": "AddTest", "email": "<EMAIL>", "accepts_marketing": false, "active": true, "hash": "88256c784094940e5d69ac77ea13e7ff", "meta": [], "sources": [], "addresses": [], "user": {"id": 251491, "created": "2023-05-12 11:24:40", "name": "AddTest", "surname": "v1", "email": "<EMAIL>", "username": "addtest", "modified": "2023-05-12 11:24:40", "client_id": 927, "active": true, "price_tier": null, "qty_availability": null, "segments": []}}, {"id": 6249204, "created": "2023-05-12 11:25:48", "channel_customer_code": "6249204", "modified": "2023-05-18 11:00:44", "channel_id": 1880, "client_id": 927, "last_name": "LastName", "first_name": "AddTestv1", "email": "<EMAIL>", "accepts_marketing": false, "active": true, "hash": "3222eb2a3e99244bdd789107c504e479", "meta": [], "sources": [{"source_customer_code": "TEst1", "id": 1423, "created": "2023-02-17 12:05:58", "description": "KRS Sage One", "sync_token": "2023-02-17T10:27:14.567", "modified": "2023-02-17 12:55:15", "client_id": 927, "type": "sageone", "active": true}], "addresses": [], "user": {"id": 251492, "created": "2023-05-12 11:25:48", "name": "AddTestv1", "surname": "LastName", "email": "<EMAIL>", "username": "addtestv1", "modified": "2023-05-18 11:00:44", "client_id": 927, "active": true, "price_tier": "Wholesale", "qty_availability": null, "segments": []}}, {"id": 6112711, "created": "2023-04-18 08:28:19", "channel_customer_code": "6112711", "modified": "2023-04-18 13:52:05", "channel_id": 1880, "client_id": 927, "last_name": "<PERSON>", "first_name": "<PERSON>", "email": "<EMAIL>", "accepts_marketing": false, "active": true, "hash": "9da27917e0dee6ad68c693d334a1eebe", "meta": [], "sources": [{"source_customer_code": "<PERSON>", "id": 1423, "created": "2023-02-17 12:05:58", "description": "KRS Sage One", "sync_token": "2023-02-17T10:27:14.567", "modified": "2023-02-17 12:55:15", "client_id": 927, "type": "sageone", "active": true}], "addresses": [], "user": {"id": 245204, "created": "2023-04-18 08:28:19", "name": "<PERSON>", "surname": "<PERSON>", "email": "<EMAIL>", "username": "hampton", "modified": "2023-04-18 08:28:20", "client_id": 927, "active": true, "price_tier": null, "qty_availability": null, "segments": []}}, {"id": 6249287, "created": "2023-05-12 11:41:30", "channel_customer_code": "undefined", "modified": "2023-05-12 11:41:30", "channel_id": 1880, "client_id": 927, "last_name": "Addtestv2", "first_name": "Addtestv2", "email": "<EMAIL>", "accepts_marketing": false, "active": true, "hash": "71b3e32abb43da9f20bd69a59fb4ab08", "meta": [], "sources": [], "addresses": [], "user": {"id": 251494, "created": "2023-05-12 11:41:30", "name": "Addtestv2", "surname": "Addtestv2", "email": "<EMAIL>", "username": "addtestv2", "modified": "2023-05-12 11:41:30", "client_id": 927, "active": true, "price_tier": null, "qty_availability": null, "segments": []}}, {"id": 5676648, "created": "2023-02-17 13:08:37", "channel_customer_code": "5676648", "modified": "2023-05-18 07:42:57", "channel_id": 1880, "client_id": 927, "last_name": "Canell4", "first_name": "Adam3", "email": "joe", "accepts_marketing": false, "active": true, "hash": "04e86a3130f4d7cdb37557246b184f0f", "meta": [{"id": 2660477, "key": "back_order", "value": "true", "template_name": null, "created": "2023-02-17 13:09:17", "modified": "2023-05-18 07:42:56", "client_id": 927}, {"id": 2660478, "key": "credit_limit", "value": "1000000", "template_name": null, "created": "2023-02-17 13:09:29", "modified": "2023-05-18 07:42:56", "client_id": 927}, {"id": 2665923, "key": "test", "value": "5", "template_name": null, "created": "2023-03-14 08:12:29", "modified": "2023-05-18 07:42:57", "client_id": 927}, {"id": 2712885, "key": "order_0|entity_product|key_Price|value_12|type_discount", "value": "12", "template_name": null, "created": "2023-04-25 07:15:16", "modified": "2023-05-18 07:42:57", "client_id": 927}, {"id": 2729901, "key": "order_0|entity_product|key_Price|value_12|type_fixed", "value": "12", "template_name": null, "created": "2023-04-28 10:14:59", "modified": "2023-05-18 07:42:57", "client_id": 927}, {"id": 2755879, "key": "order_0|entity_product|key_Price|value_123|type_discount", "value": "23", "template_name": null, "created": "2023-05-08 11:16:50", "modified": "2023-05-18 07:42:57", "client_id": 927}, {"id": 2755905, "key": "order_0|entity_product|key_Created|value_3|type_discount", "value": "0", "template_name": null, "created": "2023-05-08 13:30:09", "modified": "2023-05-18 07:42:57", "client_id": 927}, {"id": 2758812, "key": "order_0|entity_product|key_Price|value_13|type_discount", "value": "13", "template_name": null, "created": "2023-05-16 06:33:19", "modified": "2023-05-18 07:42:57", "client_id": 927}, {"id": 2758884, "key": "order_0|entity_product|key_Price|value_67|type_discount", "value": "23", "template_name": null, "created": "2023-05-16 09:43:57", "modified": "2023-05-18 07:42:57", "client_id": 927}, {"id": 2758951, "key": "order_0|entity_product|key_Type|value_12|type_discount", "value": "12", "template_name": null, "created": "2023-05-16 13:42:43", "modified": "2023-05-18 07:42:57", "client_id": 927}, {"id": 2759033, "key": "order_0|entity_product|key_Brand|value_ASD|type_discount", "value": "12", "template_name": null, "created": "2023-05-17 07:27:57", "modified": "2023-05-18 07:42:56", "client_id": 927}, {"id": 2759080, "key": "order_0|entity_product|key_Brand|value_YYY|type_discount", "value": "12", "template_name": null, "created": "2023-05-17 10:06:40", "modified": "2023-05-18 07:42:56", "client_id": 927}, {"id": 2759097, "key": "order_0|entity_product|key_Brand|value_TTT|type_discount", "value": "45", "template_name": null, "created": "2023-05-17 11:35:24", "modified": "2023-05-18 07:42:56", "client_id": 927}, {"id": 2759318, "key": "order_0|entity_product|key_Brand|value_GFD|type_discount", "value": "12", "template_name": null, "created": "2023-05-18 07:43:28", "modified": "2023-05-18 07:43:28", "client_id": 927}], "sources": [{"source_customer_code": "<PERSON>", "id": 1423, "created": "2023-02-17 12:05:58", "description": "KRS Sage One", "sync_token": "2023-02-17T10:27:14.567", "modified": "2023-02-17 12:55:15", "client_id": 927, "type": "sageone", "active": true}], "addresses": [{"id": 591756, "address1": "70 Stock Road", "address2": "Shop Area", "city": "Cape Town", "company": "KRS", "country": "South Africa", "first_name": "<PERSON>", "last_name": "Engle", "phone": "************", "province": "Western Cape", "zip": "8000", "country_code": "ZA", "province_code": "WC", "created": "2023-03-02 15:36:05", "modified": "2023-05-18 07:42:56", "client_id": 927, "address_code": null, "type": "shipping", "default": true}, {"id": 591931, "address1": null, "address2": null, "city": "dsvsdvsdv", "company": null, "country": "South Africa", "first_name": null, "last_name": null, "phone": null, "province": null, "zip": "7764", "country_code": "South Africa", "province_code": null, "created": "2023-03-09 14:26:35", "modified": "2023-05-18 07:42:56", "client_id": 927, "address_code": "dsvdssd", "type": "shipping", "default": false}], "user": {"id": 242054, "created": "2023-02-17 13:08:37", "name": "Adam3", "surname": "Canell4", "email": "joe", "username": "adam961", "modified": "2023-05-18 07:42:57", "client_id": 927, "active": true, "price_tier": "Wholesale", "qty_availability": null, "segments": [{"created": "2023-03-22 14:35:48", "modified": "2023-05-04 11:56:35", "type": "products", "operator": "contains", "key": "body_html", "value": "T", "owner": "system", "user_id": 242054, "client_id": 927}, {"created": "2023-03-22 14:36:28", "modified": "2023-05-04 11:56:35", "type": "products", "operator": "equal", "key": "collection", "value": "2", "owner": "system", "user_id": 242054, "client_id": 927}, {"created": "2023-03-22 14:38:17", "modified": "2023-05-04 11:56:35", "type": "products", "operator": "contains", "key": "product_type", "value": "2", "owner": "system", "user_id": 242054, "client_id": 927}, {"created": "2023-03-22 14:39:35", "modified": "2023-05-04 11:56:35", "type": "products", "operator": "equal", "key": "title", "value": "bob", "owner": "system", "user_id": 242054, "client_id": 927}, {"created": "2023-03-22 14:40:19", "modified": "2023-05-04 11:56:35", "type": "products", "operator": "contains", "key": "variants.qty", "value": "3", "owner": "system", "user_id": 242054, "client_id": 927}]}}, {"id": 5915322, "created": "2023-03-23 10:26:17", "channel_customer_code": "5915322", "modified": "2023-04-11 19:22:09", "channel_id": 1880, "client_id": 927, "last_name": "<PERSON>", "first_name": "<PERSON>", "email": "<EMAIL>", "accepts_marketing": false, "active": true, "hash": "61df504bf90d0a14d1190f4b94cfd6a4", "meta": [{"id": 2676476, "key": "t", "value": "1", "template_name": null, "created": "2023-04-04 07:21:18", "modified": "2023-04-04 07:21:18", "client_id": 927}], "sources": [{"source_customer_code": "<PERSON>", "id": 1423, "created": "2023-02-17 12:05:58", "description": "KRS Sage One", "sync_token": "2023-02-17T10:27:14.567", "modified": "2023-02-17 12:55:15", "client_id": 927, "type": "sageone", "active": true}], "addresses": [], "user": {"id": 242840, "created": "2023-03-23 10:26:17", "name": "<PERSON>", "surname": "<PERSON>", "email": "<EMAIL>", "username": "john761", "modified": "2023-03-23 10:26:18", "client_id": 927, "active": true, "price_tier": null, "qty_availability": null, "segments": []}}, {"id": 6264404, "created": "2023-05-15 11:40:30", "channel_customer_code": "6264404", "modified": "2023-05-15 11:40:30", "channel_id": 1880, "client_id": 927, "last_name": "KRSTESTS6", "first_name": "KRSTESTS6", "email": "<EMAIL>", "accepts_marketing": false, "active": true, "hash": "122253865cdb4f3d29bba34c7ba4f13c", "meta": [], "sources": [], "addresses": [], "user": {"id": 251529, "created": "2023-05-15 11:40:30", "name": "KRSTESTS6", "surname": "KRSTESTS6", "email": "<EMAIL>", "username": "krstests6", "modified": "2023-05-15 11:40:30", "client_id": 927, "active": true, "price_tier": null, "qty_availability": null, "segments": []}}, {"id": 6263579, "created": "2023-05-15 09:25:34", "channel_customer_code": "6263579", "modified": "2023-05-15 09:25:34", "channel_id": 1880, "client_id": 927, "last_name": "KRSTest3", "first_name": "KRSTest3", "email": "<EMAIL>", "accepts_marketing": false, "active": true, "hash": "226b502a5e369d61e9be2d3c869ee271", "meta": [], "sources": [], "addresses": [], "user": {"id": 251521, "created": "2023-05-15 09:25:34", "name": "KRSTest3", "surname": "KRSTest3", "email": "<EMAIL>", "username": "krstest3", "modified": "2023-05-15 09:25:34", "client_id": 927, "active": true, "price_tier": null, "qty_availability": null, "segments": []}}], "took_s2s": 3}