{"took": 55, "timed_out": false, "_shards": {"total": 1, "successful": 1, "skipped": 0, "failed": 0}, "hits": {"total": 91, "max_score": 0, "hits": [{"_index": "orders----------2024-06-20-10-43-39", "_type": "_doc", "_id": "2888752", "_score": 0, "_source": {"id": 2888752, "created": "2022-09-20 12:37:11", "channel_order_code": "B2B0000999", "modified": "2022-09-20 12:37:18", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "Delivery by Friday", "status": "ordered", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 4500, "tax": 690, "total": 5290, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-14 09:31:06", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000030", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 12535411, "created": "2022-09-20 12:37:11", "barcode": null, "price": 1500, "qty": 3, "sku": "SPR/LOC", "modified": "2022-09-20 12:37:11", "source_id": 57, "variant_id": 67757, "title": "Spreadsheet Software V9 (OEM)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "TAX", "price": "225.00", "code": "taxed"}], "sub_total": 4500, "sub_total_display": "4 500.00", "tax": 675, "tax_display": "675.00", "tax_per_unit": 225, "tax_per_unit_display": "225.00", "total": 5175, "total_display": "5 175.00", "price_display": "1 500.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 12535412, "created": "2022-09-20 12:37:11", "barcode": null, "price": 100, "qty": 1, "modified": "2022-09-20 12:37:11", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "Order Number: PO1"}, {"key": "delivery_date", "value": null}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "238016"}, {"key": "coupon", "value": null}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_email", "value": "<EMAIL>"}, {"key": "console_user_name", "value": "<PERSON>"}], "shipping_address": {"address1": "", "address2": "", "city": "", "company": "", "country": "", "first_name": "", "last_name": "", "phone": "", "province": "", "zip": "", "country_code": "", "province_code": ""}, "history": [{"id": "7949534", "created": "2022-09-20 12:37:11", "modified": "2022-09-20 12:37:11", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-09/20/12/37/11-*********-orderhistory-sync_order-2888752.json"}, {"id": "7949535", "created": "2022-09-20 12:37:11", "modified": "2022-09-20 12:37:11", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-09/20/12/37/11-*********-orderhistory-unpaid_order-2888752.json"}, {"id": "7949536", "created": "2022-09-20 12:37:11", "modified": "2022-09-20 12:37:11", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-09/20/12/37/11-*********-orderhistory-unpaid_order-2888752.json"}], "sub_total_display": "4 500.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "690.00", "total_discount_display": "0.00", "total_display": "5 290.00", "_id": "2888752", "short_date": "<PERSON><PERSON> 2:37 PM", "channel_name": "Stock2Shop Demo - B2B Wholesale", "total_currency": "5290.00"}}, {"_index": "orders----------2024-06-20-10-43-39", "_type": "_doc", "_id": "2888796", "_score": 0, "_source": {"id": 2888796, "created": "2022-09-20 12:46:07", "channel_order_code": "B2B0001002", "modified": "2022-09-20 12:46:14", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "Delivery on Friday", "status": "ordered", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 1500, "tax": 240, "total": 1840, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-14 09:31:06", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000033", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 12535618, "created": "2022-09-20 12:46:07", "barcode": null, "price": 1500, "qty": 1, "sku": "SPR/LOC", "modified": "2022-09-20 12:46:07", "source_id": 57, "variant_id": 67757, "title": "Spreadsheet Software V9 (OEM)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "TAX", "price": "225.00", "code": "taxed"}], "sub_total": 1500, "sub_total_display": "1 500.00", "tax": 225, "tax_display": "225.00", "tax_per_unit": 225, "tax_per_unit_display": "225.00", "total": 1725, "total_display": "1 725.00", "price_display": "1 500.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 12535619, "created": "2022-09-20 12:46:07", "barcode": null, "price": 100, "qty": 1, "modified": "2022-09-20 12:46:07", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "Order Number: 123"}, {"key": "delivery_date", "value": null}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "238016"}, {"key": "coupon", "value": null}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_email", "value": "<EMAIL>"}, {"key": "console_user_name", "value": "<PERSON>"}], "shipping_address": {"address1": "", "address2": "", "city": "", "company": "", "country": "", "first_name": "", "last_name": "", "phone": "", "province": "", "zip": "", "country_code": "", "province_code": ""}, "history": [{"id": "7949651", "created": "2022-09-20 12:46:07", "modified": "2022-09-20 12:46:07", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-09/20/12/46/07-*********-orderhistory-sync_order-2888796.json"}, {"id": "7949652", "created": "2022-09-20 12:46:07", "modified": "2022-09-20 12:46:07", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-09/20/12/46/07-*********-orderhistory-unpaid_order-2888796.json"}, {"id": "7949653", "created": "2022-09-20 12:46:07", "modified": "2022-09-20 12:46:07", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-09/20/12/46/07-*********-orderhistory-unpaid_order-2888796.json"}], "sub_total_display": "1 500.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "240.00", "total_discount_display": "0.00", "total_display": "1 840.00", "_id": "2888796", "short_date": "<PERSON><PERSON> 2:46 PM", "channel_name": "Stock2Shop Demo - B2B Wholesale", "total_currency": "1840.00"}}, {"_index": "orders----------2024-06-20-10-43-39", "_type": "_doc", "_id": "2888870", "_score": 0, "_source": {"id": 2888870, "created": "2022-09-20 13:02:10", "channel_order_code": "B2B0001004", "modified": "2022-09-20 13:02:21", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "Special instructions go here.", "status": "shipping", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 100, "tax": 30, "total": 230, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-14 09:31:06", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000035", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 12535920, "created": "2022-09-20 13:02:10", "barcode": null, "price": 100, "qty": 1, "sku": "MOD/234", "modified": "2022-09-20 13:02:10", "source_id": 57, "variant_id": 67753, "title": "96K Modem (N/A)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "12535920", "fulfillment_id": "376322", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 12535921, "created": "2022-09-20 13:02:10", "barcode": null, "price": 100, "qty": 1, "modified": "2022-09-20 13:02:10", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "Order Number:"}, {"key": "delivery_date", "value": null}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "571"}, {"key": "coupon", "value": null}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_name", "value": null}], "shipping_address": {"id": "5648204", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-09-20 13:02:10", "modified": "2022-09-20 13:02:10", "client_id": "21"}, "billing_address": {"id": "5648205", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-09-20 13:02:10", "modified": "2022-09-20 13:02:10", "client_id": "21"}, "history": [{"id": "7949855", "created": "2022-09-20 13:02:10", "modified": "2022-09-20 13:02:10", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-09/20/13/02/10-*********-orderhistory-sync_order-2888870.json"}, {"id": "7949856", "created": "2022-09-20 13:02:10", "modified": "2022-09-20 13:02:10", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-09/20/13/02/10-*********-orderhistory-unpaid_order-2888870.json"}, {"id": "7949857", "created": "2022-09-20 13:02:10", "modified": "2022-09-20 13:02:10", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-09/20/13/02/10-*********-orderhistory-unpaid_order-2888870.json"}], "fulfillments": {"376322": {"id": "376322", "fulfillmentservice_order_code": "61848823", "tracking_company": "To be determined", "tracking_number": null, "tracking_url": null, "status": "Awaiting Stock", "state": "pending", "notes": "20220920130218 - 240: Awaiting Stock\n", "items": [{"orderitem_id": "12535920", "fulfillment_id": "376322", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}]}}, "sub_total_display": "100.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "30.00", "total_discount_display": "0.00", "total_display": "230.00", "_id": "2888870", "short_date": "<PERSON><PERSON> 3:02 PM", "channel_name": "Stock2Shop Demo - B2B Wholesale", "total_currency": "230.00"}}, {"_index": "orders----------2024-06-20-10-43-39", "_type": "_doc", "_id": "2888900", "_score": 0, "_source": {"id": 2888900, "created": "2022-09-20 13:08:03", "channel_order_code": "B2B0001006", "modified": "2022-09-20 13:08:10", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "Special instructions go here.", "status": "ordered", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 1500, "tax": 240, "total": 1840, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-14 09:31:06", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000037", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 12536010, "created": "2022-09-20 13:08:03", "barcode": null, "price": 1500, "qty": 1, "sku": "SPR/LOC", "modified": "2022-09-20 13:08:03", "source_id": 57, "variant_id": 67757, "title": "Spreadsheet Software V9 (OEM)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "TAX", "price": "225.00", "code": "taxed"}], "sub_total": 1500, "sub_total_display": "1 500.00", "tax": 225, "tax_display": "225.00", "tax_per_unit": 225, "tax_per_unit_display": "225.00", "total": 1725, "total_display": "1 725.00", "price_display": "1 500.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 12536012, "created": "2022-09-20 13:08:03", "barcode": null, "price": 100, "qty": 1, "modified": "2022-09-20 13:08:03", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "Order Number:"}, {"key": "delivery_date", "value": null}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "238016"}, {"key": "coupon", "value": null}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_email", "value": "<EMAIL>"}, {"key": "console_user_name", "value": "<PERSON>"}], "shipping_address": {"address1": "", "address2": "", "city": "", "company": "", "country": "", "first_name": "", "last_name": "", "phone": "", "province": "", "zip": "", "country_code": "", "province_code": ""}, "history": [{"id": "7949936", "created": "2022-09-20 13:08:03", "modified": "2022-09-20 13:08:03", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-09/20/13/08/03-*********-orderhistory-sync_order-2888900.json"}, {"id": "7949937", "created": "2022-09-20 13:08:03", "modified": "2022-09-20 13:08:03", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-09/20/13/08/03-*********-orderhistory-unpaid_order-2888900.json"}, {"id": "7949939", "created": "2022-09-20 13:08:03", "modified": "2022-09-20 13:08:03", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-09/20/13/08/03-*********-orderhistory-unpaid_order-2888900.json"}], "sub_total_display": "1 500.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "240.00", "total_discount_display": "0.00", "total_display": "1 840.00", "_id": "2888900", "short_date": "<PERSON><PERSON> 3:08 PM", "channel_name": "Stock2Shop Demo - B2B Wholesale", "total_currency": "1840.00"}}, {"_index": "orders----------2024-06-20-10-43-39", "_type": "_doc", "_id": "2889061", "_score": 0, "_source": {"id": 2889061, "created": "2022-09-20 13:43:11", "channel_order_code": "B2B0001008", "modified": "2022-09-20 13:43:27", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "Special instructions go here.", "status": "shipping", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 1500, "tax": 240, "total": 1840, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-14 09:31:06", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000039", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 12536630, "created": "2022-09-20 13:43:12", "barcode": null, "price": 1500, "qty": 1, "sku": "SPR/LOC", "modified": "2022-09-20 13:43:12", "source_id": 57, "variant_id": 67757, "title": "Spreadsheet Software V9 (OEM)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "12536630", "fulfillment_id": "376355", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "225.00", "code": "taxed"}], "sub_total": 1500, "sub_total_display": "1 500.00", "tax": 225, "tax_display": "225.00", "tax_per_unit": 225, "tax_per_unit_display": "225.00", "total": 1725, "total_display": "1 725.00", "price_display": "1 500.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 12536631, "created": "2022-09-20 13:43:12", "barcode": null, "price": 100, "qty": 1, "modified": "2022-09-20 13:43:12", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "Order Number:"}, {"key": "delivery_date", "value": null}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "238016"}, {"key": "coupon", "value": null}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_email", "value": "<EMAIL>"}, {"key": "console_user_name", "value": "<PERSON>"}], "shipping_address": {"id": "5648537", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-09-20 13:43:12", "modified": "2022-09-20 13:43:12", "client_id": "21"}, "billing_address": {"id": "5648538", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-09-20 13:43:12", "modified": "2022-09-20 13:43:12", "client_id": "21"}, "history": [{"id": "7950373", "created": "2022-09-20 13:43:11", "modified": "2022-09-20 13:43:11", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-09/20/13/43/11-*********-orderhistory-sync_order-2889061.json"}, {"id": "7950374", "created": "2022-09-20 13:43:11", "modified": "2022-09-20 13:43:12", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-09/20/13/43/11-*********-orderhistory-unpaid_order-2889061.json"}, {"id": "7950375", "created": "2022-09-20 13:43:12", "modified": "2022-09-20 13:43:12", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-09/20/13/43/11-*********-orderhistory-unpaid_order-2889061.json"}], "fulfillments": {"376355": {"id": "376355", "fulfillmentservice_order_code": "61848922", "tracking_company": "To be determined", "tracking_number": null, "tracking_url": null, "status": "Awaiting Stock", "state": "pending", "notes": "20220920134324 - 240: Awaiting Stock\n", "items": [{"orderitem_id": "12536630", "fulfillment_id": "376355", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}]}}, "sub_total_display": "1 500.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "240.00", "total_discount_display": "0.00", "total_display": "1 840.00", "_id": "2889061", "short_date": "<PERSON><PERSON> 3:43 PM", "channel_name": "Stock2Shop Demo - B2B Wholesale", "total_currency": "1840.00"}}, {"_index": "orders----------2024-06-20-10-43-39", "_type": "_doc", "_id": "2889084", "_score": 0, "_source": {"id": 2889084, "created": "2022-09-20 13:48:03", "channel_order_code": "B2B0001009", "modified": "2022-09-20 13:48:15", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "Special instructions go here.", "status": "shipping", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 1500, "tax": 240, "total": 1840, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-14 09:31:06", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000040", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 12536752, "created": "2022-09-20 13:48:03", "barcode": null, "price": 1500, "qty": 1, "sku": "SPR/LOC", "modified": "2022-09-20 13:48:03", "source_id": 57, "variant_id": 67757, "title": "Spreadsheet Software V9 (OEM)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "12536752", "fulfillment_id": "376359", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "225.00", "code": "taxed"}], "sub_total": 1500, "sub_total_display": "1 500.00", "tax": 225, "tax_display": "225.00", "tax_per_unit": 225, "tax_per_unit_display": "225.00", "total": 1725, "total_display": "1 725.00", "price_display": "1 500.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 12536753, "created": "2022-09-20 13:48:03", "barcode": null, "price": 100, "qty": 1, "modified": "2022-09-20 13:48:03", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "Order Number:"}, {"key": "delivery_date", "value": null}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "238016"}, {"key": "coupon", "value": null}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_email", "value": "<EMAIL>"}, {"key": "console_user_name", "value": "<PERSON>"}], "shipping_address": {"id": "5648573", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-09-20 13:48:03", "modified": "2022-09-20 13:48:03", "client_id": "21"}, "billing_address": {"id": "5648574", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-09-20 13:48:03", "modified": "2022-09-20 13:48:03", "client_id": "21"}, "history": [{"id": "7950436", "created": "2022-09-20 13:48:03", "modified": "2022-09-20 13:48:03", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-09/20/13/48/03-*********-orderhistory-sync_order-2889084.json"}, {"id": "7950437", "created": "2022-09-20 13:48:03", "modified": "2022-09-20 13:48:03", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-09/20/13/48/03-*********-orderhistory-unpaid_order-2889084.json"}, {"id": "7950438", "created": "2022-09-20 13:48:03", "modified": "2022-09-20 13:48:03", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-09/20/13/48/03-*********-orderhistory-unpaid_order-2889084.json"}], "fulfillments": {"376359": {"id": "376359", "fulfillmentservice_order_code": "61848928", "tracking_company": "To be determined", "tracking_number": null, "tracking_url": null, "status": "Awaiting Stock", "state": "pending", "notes": "20220920134811 - 240: Awaiting Stock\n", "items": [{"orderitem_id": "12536752", "fulfillment_id": "376359", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}]}}, "sub_total_display": "1 500.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "240.00", "total_discount_display": "0.00", "total_display": "1 840.00", "_id": "2889084", "short_date": "<PERSON><PERSON> 3:48 PM", "channel_name": "Stock2Shop Demo - B2B Wholesale", "total_currency": "1840.00"}}, {"_index": "orders----------2024-06-20-10-43-39", "_type": "_doc", "_id": "2921213", "_score": 0, "_source": {"id": 2921213, "created": "2022-09-30 14:54:03", "channel_order_code": "B2B0001013", "modified": "2022-09-30 14:54:17", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "testing for Grant", "status": "shipping", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 17300, "tax": 2610, "total": 20010, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-14 09:31:06", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000045", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 12685306, "created": "2022-09-30 14:54:03", "barcode": null, "price": 250, "qty": 2, "sku": "SPEAK/EAS", "modified": "2022-09-30 14:54:03", "source_id": 57, "variant_id": 67756, "title": "50W Speakers (N/A)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "12685306", "fulfillment_id": "382054", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "37.50", "code": "taxed"}], "sub_total": 500, "sub_total_display": "500.00", "tax": 75, "tax_display": "75.00", "tax_per_unit": 37.5, "tax_per_unit_display": "37.50", "total": 575, "total_display": "575.00", "price_display": "250.00", "total_discount_display": "0.00"}, {"id": 12685307, "created": "2022-09-30 14:54:03", "barcode": null, "price": 15000, "qty": 1, "sku": "ABC", "modified": "2022-09-30 14:54:03", "source_id": 57, "variant_id": 67777, "title": "Macbook Pro 15' Retina 2015 (512 ssd)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "12685307", "fulfillment_id": "382054", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "2250.00", "code": "taxed"}], "sub_total": 15000, "sub_total_display": "15 000.00", "tax": 2250, "tax_display": "2 250.00", "tax_per_unit": 2250, "tax_per_unit_display": "2 250.00", "total": 17250, "total_display": "17 250.00", "price_display": "15 000.00", "total_discount_display": "0.00"}, {"id": 12685308, "created": "2022-09-30 14:54:03", "barcode": null, "price": 900, "qty": 2, "sku": "DB/9/0", "modified": "2022-09-30 14:54:03", "source_id": 57, "variant_id": 67774, "title": "Database Software V9 (N/A)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "12685308", "fulfillment_id": "382054", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "135.00", "code": "taxed"}], "sub_total": 1800, "sub_total_display": "1 800.00", "tax": 270, "tax_display": "270.00", "tax_per_unit": 135, "tax_per_unit_display": "135.00", "total": 2070, "total_display": "2 070.00", "price_display": "900.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 12685309, "created": "2022-09-30 14:54:04", "barcode": null, "price": 100, "qty": 1, "modified": "2022-09-30 14:54:04", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "PO 123"}, {"key": "delivery_date", "value": null}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "238016"}, {"key": "coupon", "value": null}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_email", "value": "<EMAIL>"}, {"key": "console_user_name", "value": "<PERSON>"}], "shipping_address": {"id": "5706672", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-09-30 14:54:04", "modified": "2022-09-30 14:54:04", "client_id": "21"}, "billing_address": {"id": "5706673", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-09-30 14:54:04", "modified": "2022-09-30 14:54:04", "client_id": "21"}, "history": [{"id": "8036213", "created": "2022-09-30 14:54:03", "modified": "2022-09-30 14:54:03", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-09/30/14/54/03-*********-orderhistory-sync_order-2921213.json"}, {"id": "8036214", "created": "2022-09-30 14:54:03", "modified": "2022-09-30 14:54:03", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-09/30/14/54/03-*********-orderhistory-unpaid_order-2921213.json"}, {"id": "8036215", "created": "2022-09-30 14:54:04", "modified": "2022-09-30 14:54:04", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-09/30/14/54/03-*********-orderhistory-unpaid_order-2921213.json"}], "fulfillments": {"382054": {"id": "382054", "fulfillmentservice_order_code": "61865097", "tracking_company": "To be determined", "tracking_number": null, "tracking_url": null, "status": "Awaiting Stock", "state": "pending", "notes": "20220********* - 240: Awaiting Stock\n", "items": [{"orderitem_id": "12685306", "fulfillment_id": "382054", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}, {"orderitem_id": "12685307", "fulfillment_id": "382054", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}, {"orderitem_id": "12685308", "fulfillment_id": "382054", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}]}}, "sub_total_display": "17 300.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "2 610.00", "total_discount_display": "0.00", "total_display": "20 010.00", "_id": "2921213", "short_date": "Fri 4:54 PM", "channel_name": "Stock2Shop Demo - B2B Wholesale", "total_currency": "20010.00"}}, {"_index": "orders----------2024-06-20-10-43-39", "_type": "_doc", "_id": "2936814", "_score": 0, "_source": {"id": 2936814, "created": "2022-10-05 14:52:07", "channel_order_code": "B2B0001017", "modified": "2022-10-05 14:52:20", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "testing", "status": "shipping", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 32050, "tax": 4822.5, "total": 36972.5, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-14 09:31:06", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000049", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 12762395, "created": "2022-10-05 14:52:07", "barcode": null, "price": 250, "qty": 1, "sku": "SPEAK/EAS", "modified": "2022-10-05 14:52:07", "source_id": 57, "variant_id": 67756, "title": "50W Speakers (N/A)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "12762395", "fulfillment_id": "385216", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "37.50", "code": "taxed"}], "sub_total": 250, "sub_total_display": "250.00", "tax": 37.5, "tax_display": "37.50", "tax_per_unit": 37.5, "tax_per_unit_display": "37.50", "total": 287.5, "total_display": "287.50", "price_display": "250.00", "total_discount_display": "0.00"}, {"id": 12762396, "created": "2022-10-05 14:52:07", "barcode": null, "price": 900, "qty": 2, "sku": "DB/9/0", "modified": "2022-10-05 14:52:07", "source_id": 57, "variant_id": 67774, "title": "Database Software V9 (N/A)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "12762396", "fulfillment_id": "385216", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "135.00", "code": "taxed"}], "sub_total": 1800, "sub_total_display": "1 800.00", "tax": 270, "tax_display": "270.00", "tax_per_unit": 135, "tax_per_unit_display": "135.00", "total": 2070, "total_display": "2 070.00", "price_display": "900.00", "total_discount_display": "0.00"}, {"id": 12762397, "created": "2022-10-05 14:52:07", "barcode": null, "price": 15000, "qty": 2, "sku": "ABC", "modified": "2022-10-05 14:52:07", "source_id": 57, "variant_id": 67777, "title": "Macbook Pro 15' Retina 2015 (512 ssd)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "12762397", "fulfillment_id": "385216", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "2250.00", "code": "taxed"}], "sub_total": 30000, "sub_total_display": "30 000.00", "tax": 4500, "tax_display": "4 500.00", "tax_per_unit": 2250, "tax_per_unit_display": "2 250.00", "total": 34500, "total_display": "34 500.00", "price_display": "15 000.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 12762398, "created": "2022-10-05 14:52:07", "barcode": null, "price": 100, "qty": 1, "modified": "2022-10-05 14:52:07", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "PO-GM"}, {"key": "delivery_date", "value": null}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "238016"}, {"key": "coupon", "value": null}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_email", "value": "<EMAIL>"}, {"key": "console_user_name", "value": "<PERSON>"}], "shipping_address": {"id": "5735295", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-10-05 14:52:07", "modified": "2022-10-05 14:52:07", "client_id": "21"}, "billing_address": {"id": "5735296", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-10-05 14:52:07", "modified": "2022-10-05 14:52:07", "client_id": "21"}, "history": [{"id": "8077680", "created": "2022-10-05 14:52:07", "modified": "2022-10-05 14:52:07", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-10/05/14/52/07-*********-orderhistory-sync_order-2936814.json"}, {"id": "8077681", "created": "2022-10-05 14:52:07", "modified": "2022-10-05 14:52:07", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-10/05/14/52/07-*********-orderhistory-unpaid_order-2936814.json"}, {"id": "8077682", "created": "2022-10-05 14:52:07", "modified": "2022-10-05 14:52:07", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-10/05/14/52/07-*********-orderhistory-unpaid_order-2936814.json"}], "fulfillments": {"385216": {"id": "385216", "fulfillmentservice_order_code": "61872067", "tracking_company": "To be determined", "tracking_number": null, "tracking_url": null, "status": "Awaiting Stock", "state": "pending", "notes": "20221********* - 240: Awaiting Stock\n", "items": [{"orderitem_id": "12762395", "fulfillment_id": "385216", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}, {"orderitem_id": "12762396", "fulfillment_id": "385216", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}, {"orderitem_id": "12762397", "fulfillment_id": "385216", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}]}}, "sub_total_display": "32 050.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "4 822.50", "total_discount_display": "0.00", "total_display": "36 972.50", "_id": "2936814", "short_date": "Wed 4:52 PM", "channel_name": "Stock2Shop Demo - B2B Wholesale", "total_currency": "36972.50"}}, {"_index": "orders----------2024-06-20-10-43-39", "_type": "_doc", "_id": "2941974", "_score": 0, "_source": {"id": 2941974, "created": "2022-10-07 09:15:08", "channel_order_code": "B2B0001018", "modified": "2022-10-07 09:15:23", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "Special instructions go here.", "status": "shipping", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 30800, "tax": 4635, "total": 35535, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-14 09:31:06", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000050", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 12786557, "created": "2022-10-07 09:15:08", "barcode": null, "price": 800, "qty": 1, "sku": "DB/9/0", "modified": "2022-10-07 09:15:08", "source_id": 57, "variant_id": 67774, "title": "Database Software V9 (N/A)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "12786557", "fulfillment_id": "386137", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "120.00", "code": "taxed"}], "sub_total": 800, "sub_total_display": "800.00", "tax": 120, "tax_display": "120.00", "tax_per_unit": 120, "tax_per_unit_display": "120.00", "total": 920, "total_display": "920.00", "price_display": "800.00", "total_discount_display": "0.00"}, {"id": 12786558, "created": "2022-10-07 09:15:08", "barcode": null, "price": 10000, "qty": 3, "sku": "ABC", "modified": "2022-10-07 09:15:08", "source_id": 57, "variant_id": 67777, "title": "Macbook Pro 15' Retina 2015 (512 ssd)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "12786558", "fulfillment_id": "386137", "qty": "3", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "1500.00", "code": "taxed"}], "sub_total": 30000, "sub_total_display": "30 000.00", "tax": 4500, "tax_display": "4 500.00", "tax_per_unit": 1500, "tax_per_unit_display": "1 500.00", "total": 34500, "total_display": "34 500.00", "price_display": "10 000.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 12786559, "created": "2022-10-07 09:15:08", "barcode": null, "price": 100, "qty": 1, "modified": "2022-10-07 09:15:08", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "POS 23"}, {"key": "delivery_date", "value": null}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "238016"}, {"key": "coupon", "value": null}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_email", "value": "<EMAIL>"}, {"key": "console_user_name", "value": "<PERSON>"}], "shipping_address": {"id": "5744836", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-10-07 09:15:08", "modified": "2022-10-07 09:15:08", "client_id": "21"}, "billing_address": {"id": "5744837", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-10-07 09:15:08", "modified": "2022-10-07 09:15:08", "client_id": "21"}, "history": [{"id": "8091606", "created": "2022-10-07 09:15:08", "modified": "2022-10-07 09:15:08", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-10/07/09/15/08-*********-orderhistory-sync_order-2941974.json"}, {"id": "8091607", "created": "2022-10-07 09:15:08", "modified": "2022-10-07 09:15:08", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-10/07/09/15/08-*********-orderhistory-unpaid_order-2941974.json"}, {"id": "8091608", "created": "2022-10-07 09:15:08", "modified": "2022-10-07 09:15:08", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-10/07/09/15/08-*********-orderhistory-unpaid_order-2941974.json"}], "fulfillments": {"386137": {"id": "386137", "fulfillmentservice_order_code": "61874902", "tracking_company": "To be determined", "tracking_number": null, "tracking_url": null, "status": "Awaiting Stock", "state": "pending", "notes": "20221007091520 - 240: Awaiting Stock\n", "items": [{"orderitem_id": "12786557", "fulfillment_id": "386137", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}, {"orderitem_id": "12786558", "fulfillment_id": "386137", "qty": "3", "fulfilled_qty": "0", "status": "Awaiting stock"}]}}, "sub_total_display": "30 800.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "4 635.00", "total_discount_display": "0.00", "total_display": "35 535.00", "_id": "2941974", "short_date": "Fri 11:15 AM", "channel_name": "Stock2Shop Demo - B2B Wholesale", "total_currency": "35535.00"}}, {"_index": "orders----------2024-06-20-10-43-39", "_type": "_doc", "_id": "2959464", "_score": 0, "_source": {"id": 2959464, "created": "2022-10-13 07:04:16", "channel_order_code": "B2B0001020", "modified": "2022-10-13 07:04:48", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "Special instructions go here.", "status": "shipping", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 20800, "tax": 3135, "total": 24035, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-14 09:31:06", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000052", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 12869908, "created": "2022-10-13 07:04:16", "barcode": null, "price": 800, "qty": 1, "sku": "DB/9/0", "modified": "2022-10-13 07:04:16", "source_id": 57, "variant_id": 67774, "title": "Database Software V9 (N/A)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "12869908", "fulfillment_id": "389466", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "120.00", "code": "taxed"}], "sub_total": 800, "sub_total_display": "800.00", "tax": 120, "tax_display": "120.00", "tax_per_unit": 120, "tax_per_unit_display": "120.00", "total": 920, "total_display": "920.00", "price_display": "800.00", "total_discount_display": "0.00"}, {"id": 12869909, "created": "2022-10-13 07:04:16", "barcode": null, "price": 10000, "qty": 2, "sku": "ABC", "modified": "2022-10-13 07:04:16", "source_id": 57, "variant_id": 67777, "title": "Macbook Pro 15' Retina 2015 (512 ssd)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "12869909", "fulfillment_id": "389466", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "1500.00", "code": "taxed"}], "sub_total": 20000, "sub_total_display": "20 000.00", "tax": 3000, "tax_display": "3 000.00", "tax_per_unit": 1500, "tax_per_unit_display": "1 500.00", "total": 23000, "total_display": "23 000.00", "price_display": "10 000.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 12869910, "created": "2022-10-13 07:04:16", "barcode": null, "price": 100, "qty": 1, "modified": "2022-10-13 07:04:16", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "Order Number:"}, {"key": "delivery_date", "value": null}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "238016"}, {"key": "coupon", "value": null}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_email", "value": "<EMAIL>"}, {"key": "console_user_name", "value": "<PERSON>"}], "shipping_address": {"id": "5776486", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-10-13 07:04:16", "modified": "2022-10-13 07:04:16", "client_id": "21"}, "billing_address": {"id": "5776487", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-10-13 07:04:16", "modified": "2022-10-13 07:04:16", "client_id": "21"}, "history": [{"id": "8138112", "created": "2022-10-13 07:04:16", "modified": "2022-10-13 07:04:16", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-10/13/07/04/16-*********-orderhistory-sync_order-2959464.json"}, {"id": "8138113", "created": "2022-10-13 07:04:16", "modified": "2022-10-13 07:04:16", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-10/13/07/04/16-*********-orderhistory-unpaid_order-2959464.json"}, {"id": "8138114", "created": "2022-10-13 07:04:16", "modified": "2022-10-13 07:04:16", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-10/13/07/04/16-*********-orderhistory-unpaid_order-2959464.json"}], "fulfillments": {"389466": {"id": "389466", "fulfillmentservice_order_code": "61883020", "tracking_company": "To be determined", "tracking_number": null, "tracking_url": null, "status": "Awaiting Stock", "state": "pending", "notes": "20221013070444 - 240: Awaiting Stock\n", "items": [{"orderitem_id": "12869908", "fulfillment_id": "389466", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}, {"orderitem_id": "12869909", "fulfillment_id": "389466", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}]}}, "sub_total_display": "20 800.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "3 135.00", "total_discount_display": "0.00", "total_display": "24 035.00", "_id": "2959464", "short_date": "Thu 9:04 AM", "channel_name": "Stock2Shop Demo - B2B Wholesale", "total_currency": "24035.00"}}, {"_index": "orders----------2024-06-20-10-43-39", "_type": "_doc", "_id": "2960948", "_score": 0, "_source": {"id": 2960948, "created": "2022-10-13 12:17:03", "channel_order_code": "B2B0001021", "modified": "2022-10-13 12:17:18", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "Special instructions go here.", "status": "shipping", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 20000, "tax": 3015, "total": 23115, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-14 09:31:06", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000053", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 12877392, "created": "2022-10-13 12:17:03", "barcode": null, "price": 10000, "qty": 2, "sku": "ABC", "modified": "2022-10-13 12:17:03", "source_id": 57, "variant_id": 67777, "title": "Macbook Pro 15' Retina 2015 (512 ssd)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "12877392", "fulfillment_id": "389704", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "1500.00", "code": "taxed"}], "sub_total": 20000, "sub_total_display": "20 000.00", "tax": 3000, "tax_display": "3 000.00", "tax_per_unit": 1500, "tax_per_unit_display": "1 500.00", "total": 23000, "total_display": "23 000.00", "price_display": "10 000.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 12877393, "created": "2022-10-13 12:17:03", "barcode": null, "price": 100, "qty": 1, "modified": "2022-10-13 12:17:03", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "POASH"}, {"key": "delivery_date", "value": null}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "238016"}, {"key": "coupon", "value": null}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_email", "value": "<EMAIL>"}, {"key": "console_user_name", "value": "<PERSON>"}], "shipping_address": {"id": "5779167", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-10-13 12:17:03", "modified": "2022-10-13 12:17:03", "client_id": "21"}, "billing_address": {"id": "5779168", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-10-13 12:17:03", "modified": "2022-10-13 12:17:03", "client_id": "21"}, "history": [{"id": "8142064", "created": "2022-10-13 12:17:03", "modified": "2022-10-13 12:17:03", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-10/13/12/17/03-*********-orderhistory-sync_order-2960948.json"}, {"id": "8142065", "created": "2022-10-13 12:17:03", "modified": "2022-10-13 12:17:03", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-10/13/12/17/03-*********-orderhistory-unpaid_order-2960948.json"}, {"id": "8142066", "created": "2022-10-13 12:17:03", "modified": "2022-10-13 12:17:03", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-10/13/12/17/03-*********-orderhistory-unpaid_order-2960948.json"}], "fulfillments": {"389704": {"id": "389704", "fulfillmentservice_order_code": "61884086", "tracking_company": "To be determined", "tracking_number": null, "tracking_url": null, "status": "Awaiting Stock", "state": "pending", "notes": "20221013121714 - 240: Awaiting Stock\n", "items": [{"orderitem_id": "12877392", "fulfillment_id": "389704", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}]}}, "sub_total_display": "20 000.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "3 015.00", "total_discount_display": "0.00", "total_display": "23 115.00", "_id": "2960948", "short_date": "<PERSON>hu 2:17 PM", "channel_name": "Stock2Shop Demo - B2B Wholesale", "total_currency": "23115.00"}}, {"_index": "orders----------2024-06-20-10-43-39", "_type": "_doc", "_id": "2961088", "_score": 0, "_source": {"id": 2961088, "created": "2022-10-13 12:46:03", "channel_order_code": "B2B0001022", "modified": "2022-10-13 12:46:17", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "Special instructions go here.", "status": "shipping", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 20000, "tax": 3015, "total": 23115, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-14 09:31:06", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000054", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 12878044, "created": "2022-10-13 12:46:03", "barcode": null, "price": 10000, "qty": 2, "sku": "ABC", "modified": "2022-10-13 12:46:03", "source_id": 57, "variant_id": 67777, "title": "Macbook Pro 15' Retina 2015 (512 ssd)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "12878044", "fulfillment_id": "389728", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "1500.00", "code": "taxed"}], "sub_total": 20000, "sub_total_display": "20 000.00", "tax": 3000, "tax_display": "3 000.00", "tax_per_unit": 1500, "tax_per_unit_display": "1 500.00", "total": 23000, "total_display": "23 000.00", "price_display": "10 000.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 12878045, "created": "2022-10-13 12:46:03", "barcode": null, "price": 100, "qty": 1, "modified": "2022-10-13 12:46:03", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "Order Number:"}, {"key": "delivery_date", "value": null}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "238016"}, {"key": "coupon", "value": null}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_email", "value": "<EMAIL>"}, {"key": "console_user_name", "value": "<PERSON>"}], "shipping_address": {"id": "5779422", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-10-13 12:46:03", "modified": "2022-10-13 12:46:03", "client_id": "21"}, "billing_address": {"id": "5779423", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-10-13 12:46:03", "modified": "2022-10-13 12:46:03", "client_id": "21"}, "history": [{"id": "8142434", "created": "2022-10-13 12:46:03", "modified": "2022-10-13 12:46:03", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-10/13/12/46/03-*********-orderhistory-sync_order-2961088.json"}, {"id": "8142435", "created": "2022-10-13 12:46:03", "modified": "2022-10-13 12:46:03", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-10/13/12/46/03-*********-orderhistory-unpaid_order-2961088.json"}, {"id": "8142436", "created": "2022-10-13 12:46:03", "modified": "2022-10-13 12:46:03", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-10/13/12/46/03-*********-orderhistory-unpaid_order-2961088.json"}], "fulfillments": {"389728": {"id": "389728", "fulfillmentservice_order_code": "61884151", "tracking_company": "To be determined", "tracking_number": null, "tracking_url": null, "status": "Awaiting Stock", "state": "pending", "notes": "20221013124612 - 240: Awaiting Stock\n", "items": [{"orderitem_id": "12878044", "fulfillment_id": "389728", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}]}}, "sub_total_display": "20 000.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "3 015.00", "total_discount_display": "0.00", "total_display": "23 115.00", "_id": "2961088", "short_date": "<PERSON><PERSON> 2:46 PM", "channel_name": "Stock2Shop Demo - B2B Wholesale", "total_currency": "23115.00"}}, {"_index": "orders----------2024-06-20-10-43-39", "_type": "_doc", "_id": "2963625", "_score": 0, "_source": {"id": 2963625, "created": "2022-10-14 09:35:16", "channel_order_code": "B2B0001024", "modified": "2022-10-14 09:35:30", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "Special instructions go here.", "status": "shipping", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 20280, "tax": 3057, "total": 23437, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-14 09:31:06", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000056", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 12889135, "created": "2022-10-14 09:35:16", "barcode": null, "price": 140, "qty": 2, "sku": "SPEAK/EAS", "modified": "2022-10-14 09:35:16", "source_id": 57, "variant_id": 67756, "title": "50W Speakers (N/A)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "12889135", "fulfillment_id": "390157", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "21.00", "code": "taxed"}], "sub_total": 280, "sub_total_display": "280.00", "tax": 42, "tax_display": "42.00", "tax_per_unit": 21, "tax_per_unit_display": "21.00", "total": 322, "total_display": "322.00", "price_display": "140.00", "total_discount_display": "0.00"}, {"id": 12889136, "created": "2022-10-14 09:35:16", "barcode": null, "price": 10000, "qty": 2, "sku": "ABC", "modified": "2022-10-14 09:35:16", "source_id": 57, "variant_id": 67777, "title": "Macbook Pro 15' Retina 2015 (512 ssd)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "12889136", "fulfillment_id": "390157", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "1500.00", "code": "taxed"}], "sub_total": 20000, "sub_total_display": "20 000.00", "tax": 3000, "tax_display": "3 000.00", "tax_per_unit": 1500, "tax_per_unit_display": "1 500.00", "total": 23000, "total_display": "23 000.00", "price_display": "10 000.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 12889137, "created": "2022-10-14 09:35:16", "barcode": null, "price": 100, "qty": 1, "modified": "2022-10-14 09:35:16", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "Order Number:"}, {"key": "delivery_date", "value": null}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "238016"}, {"key": "coupon", "value": null}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_email", "value": "<EMAIL>"}, {"key": "console_user_name", "value": "<PERSON>"}], "shipping_address": {"id": "5784024", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-10-14 09:35:17", "modified": "2022-10-14 09:35:17", "client_id": "21"}, "billing_address": {"id": "5784025", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-10-14 09:35:17", "modified": "2022-10-14 09:35:17", "client_id": "21"}, "history": [{"id": "8149009", "created": "2022-10-14 09:35:16", "modified": "2022-10-14 09:35:16", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-10/14/09/35/16-*********-orderhistory-sync_order-2963625.json"}, {"id": "8149010", "created": "2022-10-14 09:35:16", "modified": "2022-10-14 09:35:16", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-10/14/09/35/16-*********-orderhistory-unpaid_order-2963625.json"}, {"id": "8149011", "created": "2022-10-14 09:35:17", "modified": "2022-10-14 09:35:17", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-10/14/09/35/16-*********-orderhistory-unpaid_order-2963625.json"}], "fulfillments": {"390157": {"id": "390157", "fulfillmentservice_order_code": "61885384", "tracking_company": "To be determined", "tracking_number": null, "tracking_url": null, "status": "Awaiting Stock", "state": "pending", "notes": "20221014093526 - 240: Awaiting Stock\n", "items": [{"orderitem_id": "12889135", "fulfillment_id": "390157", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}, {"orderitem_id": "12889136", "fulfillment_id": "390157", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}]}}, "sub_total_display": "20 280.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "3 057.00", "total_discount_display": "0.00", "total_display": "23 437.00", "_id": "2963625", "short_date": "Fri 11:35 AM", "channel_name": "Stock2Shop Demo - B2B Wholesale", "total_currency": "23437.00"}}, {"_index": "orders----------2024-06-20-10-43-39", "_type": "_doc", "_id": "3047899", "_score": 0, "_source": {"id": 3047899, "created": "2022-11-07 08:14:07", "channel_order_code": "B2B0001029", "modified": "2022-11-07 08:14:29", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "Please deliver before Friday", "status": "shipping", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 20280, "tax": 3057, "total": 23437, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-14 09:31:06", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000087", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 13288422, "created": "2022-11-07 08:14:07", "barcode": null, "price": 10000, "qty": 2, "sku": "ABC", "modified": "2022-11-07 08:14:07", "source_id": 57, "variant_id": 67777, "title": "Macbook Pro 15' Retina 2015 (512 ssd)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "13288422", "fulfillment_id": "406749", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "1500.00", "code": "taxed"}], "sub_total": 20000, "sub_total_display": "20 000.00", "tax": 3000, "tax_display": "3 000.00", "tax_per_unit": 1500, "tax_per_unit_display": "1 500.00", "total": 23000, "total_display": "23 000.00", "price_display": "10 000.00", "total_discount_display": "0.00"}, {"id": 13288423, "created": "2022-11-07 08:14:07", "barcode": null, "price": 140, "qty": 2, "sku": "SPEAK/EAS", "modified": "2022-11-07 08:14:07", "source_id": 57, "variant_id": 67756, "title": "50W Speakers (N/A)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "13288423", "fulfillment_id": "406749", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "21.00", "code": "taxed"}], "sub_total": 280, "sub_total_display": "280.00", "tax": 42, "tax_display": "42.00", "tax_per_unit": 21, "tax_per_unit_display": "21.00", "total": 322, "total_display": "322.00", "price_display": "140.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 13288424, "created": "2022-11-07 08:14:07", "barcode": null, "price": 100, "qty": 1, "modified": "2022-11-07 08:14:07", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "Order Number:"}, {"key": "delivery_date", "value": null}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "238016"}, {"key": "coupon", "value": null}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_email", "value": "<EMAIL>"}, {"key": "console_user_name", "value": "<PERSON>"}], "shipping_address": {"id": "5937030", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-11-07 08:14:07", "modified": "2022-11-07 08:14:07", "client_id": "21"}, "billing_address": {"id": "5937031", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-11-07 08:14:07", "modified": "2022-11-07 08:14:07", "client_id": "21"}, "history": [{"id": "8375875", "created": "2022-11-07 08:14:07", "modified": "2022-11-07 08:14:07", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-11/07/08/14/06-*********-orderhistory-sync_order-3047899.json"}, {"id": "8375876", "created": "2022-11-07 08:14:07", "modified": "2022-11-07 08:14:07", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-11/07/08/14/07-*********-orderhistory-unpaid_order-3047899.json"}, {"id": "8375877", "created": "2022-11-07 08:14:07", "modified": "2022-11-07 08:14:07", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-11/07/08/14/07-*********-orderhistory-unpaid_order-3047899.json"}], "fulfillments": {"406749": {"id": "406749", "fulfillmentservice_order_code": "61922745", "tracking_company": "To be determined", "tracking_number": null, "tracking_url": null, "status": "Awaiting Stock", "state": "pending", "notes": "20221107081425 - 240: Awaiting Stock\n", "items": [{"orderitem_id": "13288422", "fulfillment_id": "406749", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}, {"orderitem_id": "13288423", "fulfillment_id": "406749", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}]}}, "sub_total_display": "20 280.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "3 057.00", "total_discount_display": "0.00", "total_display": "23 437.00", "_id": "3047899", "short_date": "Mon 10:14 AM", "channel_name": "Stock2Shop Demo - B2B Wholesale", "total_currency": "23437.00"}}, {"_index": "orders----------2024-06-20-10-43-39", "_type": "_doc", "_id": "3048560", "_score": 0, "_source": {"id": 3048560, "created": "2022-11-07 09:49:05", "channel_order_code": "B2B0001032", "modified": "2022-11-07 09:49:19", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "delivery before Friday", "status": "shipping", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 30000, "tax": 4515, "total": 34615, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-14 09:31:06", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000090", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 13291884, "created": "2022-11-07 09:49:05", "barcode": null, "price": 10000, "qty": 3, "sku": "ABC", "modified": "2022-11-07 09:49:05", "source_id": 57, "variant_id": 67777, "title": "Macbook Pro 15' Retina 2015 (512 ssd)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "13291884", "fulfillment_id": "406862", "qty": "3", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "1500.00", "code": "taxed"}], "sub_total": 30000, "sub_total_display": "30 000.00", "tax": 4500, "tax_display": "4 500.00", "tax_per_unit": 1500, "tax_per_unit_display": "1 500.00", "total": 34500, "total_display": "34 500.00", "price_display": "10 000.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 13291885, "created": "2022-11-07 09:49:05", "barcode": null, "price": 100, "qty": 1, "modified": "2022-11-07 09:49:05", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "Order Number:"}, {"key": "delivery_date", "value": null}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "238016"}, {"key": "coupon", "value": null}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_email", "value": "<EMAIL>"}, {"key": "console_user_name", "value": "<PERSON>"}], "shipping_address": {"id": "5938236", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-11-07 09:49:05", "modified": "2022-11-07 09:49:05", "client_id": "21"}, "billing_address": {"id": "5938237", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-11-07 09:49:05", "modified": "2022-11-07 09:49:05", "client_id": "21"}, "history": [{"id": "8377653", "created": "2022-11-07 09:49:05", "modified": "2022-11-07 09:49:05", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-11/07/09/49/04-*********-orderhistory-sync_order-3048560.json"}, {"id": "8377654", "created": "2022-11-07 09:49:05", "modified": "2022-11-07 09:49:05", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-11/07/09/49/05-*********-orderhistory-unpaid_order-3048560.json"}, {"id": "8377655", "created": "2022-11-07 09:49:05", "modified": "2022-11-07 09:49:05", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-11/07/09/49/05-*********-orderhistory-unpaid_order-3048560.json"}], "fulfillments": {"406862": {"id": "406862", "fulfillmentservice_order_code": "61923123", "tracking_company": "To be determined", "tracking_number": null, "tracking_url": null, "status": "Awaiting Stock", "state": "pending", "notes": "20221107094915 - 240: Awaiting Stock\n", "items": [{"orderitem_id": "13291884", "fulfillment_id": "406862", "qty": "3", "fulfilled_qty": "0", "status": "Awaiting stock"}]}}, "sub_total_display": "30 000.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "4 515.00", "total_discount_display": "0.00", "total_display": "34 615.00", "_id": "3048560", "short_date": "Mon 11:49 AM", "channel_name": "Stock2Shop Demo - B2B Wholesale", "total_currency": "34615.00"}}, {"_index": "orders----------2024-06-20-10-43-39", "_type": "_doc", "_id": "3085379", "_score": 0, "_source": {"id": 3085379, "created": "2022-11-16 13:17:14", "channel_order_code": "B2B0001047", "modified": "2022-11-16 13:17:14", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "Special instructions go here.", "status": "processing", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 90, "tax": 28.5, "total": 218.5, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-14 09:31:06", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "line_items": [{"id": 13471127, "created": "2022-11-16 13:17:14", "barcode": null, "price": 90, "qty": 1, "sku": "MOD/234", "modified": "2022-11-16 13:17:14", "source_id": 57, "variant_id": 67753, "title": "96K Modem (N/A)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "TAX", "price": "13.50", "code": "taxed"}], "sub_total": 90, "sub_total_display": "90.00", "tax": 13.5, "tax_display": "13.50", "tax_per_unit": 13.5, "tax_per_unit_display": "13.50", "total": 103.5, "total_display": "103.50", "price_display": "90.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 13471128, "created": "2022-11-16 13:17:14", "barcode": null, "price": 100, "qty": 1, "modified": "2022-11-16 13:17:14", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "Order Number:"}, {"key": "delivery_date", "value": null}, {"key": "payment_method", "value": "AdumoVirtual"}, {"key": "console_user_id", "value": "571"}, {"key": "coupon", "value": null}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_name", "value": null}], "shipping_address": {"id": "6005874", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-11-16 13:17:14", "modified": "2022-11-16 13:17:14", "client_id": "21"}, "billing_address": {"id": "6005875", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-11-16 13:17:14", "modified": "2022-11-16 13:17:14", "client_id": "21"}, "history": [{"id": "8475593", "created": "2022-11-16 13:17:14", "modified": "2022-11-16 13:17:14", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-11/16/13/17/14-*********-orderhistory-sync_order-3085379.json"}, {"id": "8475594", "created": "2022-11-16 13:17:14", "modified": "2022-11-16 13:17:14", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-11/16/13/17/14-*********-orderhistory-unpaid_order-3085379.json"}], "sub_total_display": "90.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "28.50", "total_discount_display": "0.00", "total_display": "218.50", "_id": "3085379", "short_date": "Wed 3:17 PM", "channel_name": "Stock2Shop Demo - B2B Wholesale", "total_currency": "218.50"}}, {"_index": "orders----------2024-06-20-10-43-39", "_type": "_doc", "_id": "3113916", "_score": 0, "_source": {"id": 3113916, "created": "2022-11-23 09:15:05", "channel_order_code": "B2B0001052", "modified": "2022-11-23 09:15:18", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "Special instructions go here.", "status": "shipping", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 20090, "tax": 3028.5, "total": 23218.5, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-14 09:31:06", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000116", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 13606855, "created": "2022-11-23 09:15:05", "barcode": null, "price": 10000, "qty": 2, "sku": "ABC", "modified": "2022-11-23 09:15:05", "source_id": 57, "variant_id": 67777, "title": "Macbook Pro 15' Retina 2015 (512 ssd)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "13606855", "fulfillment_id": "420393", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "1500.00", "code": "taxed"}], "sub_total": 20000, "sub_total_display": "20 000.00", "tax": 3000, "tax_display": "3 000.00", "tax_per_unit": 1500, "tax_per_unit_display": "1 500.00", "total": 23000, "total_display": "23 000.00", "price_display": "10 000.00", "total_discount_display": "0.00"}, {"id": 13606856, "created": "2022-11-23 09:15:05", "barcode": null, "price": 90, "qty": 1, "sku": "MOD/234", "modified": "2022-11-23 09:15:05", "source_id": 57, "variant_id": 67753, "title": "96K Modem (N/A)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "13606856", "fulfillment_id": "420393", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "13.50", "code": "taxed"}], "sub_total": 90, "sub_total_display": "90.00", "tax": 13.5, "tax_display": "13.50", "tax_per_unit": 13.5, "tax_per_unit_display": "13.50", "total": 103.5, "total_display": "103.50", "price_display": "90.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 13606857, "created": "2022-11-23 09:15:05", "barcode": null, "price": 100, "qty": 1, "modified": "2022-11-23 09:15:05", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "Order Number:"}, {"key": "delivery_date", "value": null}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "238016"}, {"key": "coupon", "value": null}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_email", "value": "<EMAIL>"}, {"key": "console_user_name", "value": "<PERSON>"}], "shipping_address": {"id": "6058946", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-11-23 09:15:05", "modified": "2022-11-23 09:15:05", "client_id": "21"}, "billing_address": {"id": "6058947", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-11-23 09:15:05", "modified": "2022-11-23 09:15:05", "client_id": "21"}, "history": [{"id": "8552127", "created": "2022-11-23 09:15:05", "modified": "2022-11-23 09:15:05", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-11/23/09/15/05-*********-orderhistory-sync_order-3113916.json"}, {"id": "8552128", "created": "2022-11-23 09:15:05", "modified": "2022-11-23 09:15:05", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-11/23/09/15/05-*********-orderhistory-unpaid_order-3113916.json"}, {"id": "8552129", "created": "2022-11-23 09:15:05", "modified": "2022-11-23 09:15:05", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-11/23/09/15/05-*********-orderhistory-unpaid_order-3113916.json"}], "fulfillments": {"420393": {"id": "420393", "fulfillmentservice_order_code": "61952158", "tracking_company": "To be determined", "tracking_number": null, "tracking_url": null, "status": "Awaiting Stock", "state": "pending", "notes": "20221123091514 - 240: Awaiting Stock\n", "items": [{"orderitem_id": "13606855", "fulfillment_id": "420393", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}, {"orderitem_id": "13606856", "fulfillment_id": "420393", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}]}}, "sub_total_display": "20 090.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "3 028.50", "total_discount_display": "0.00", "total_display": "23 218.50", "_id": "3113916", "short_date": "Wed 11:15 AM", "channel_name": "Stock2Shop Demo - B2B Wholesale", "total_currency": "23218.50"}}, {"_index": "orders----------2024-06-20-10-43-39", "_type": "_doc", "_id": "3160109", "_score": 0, "_source": {"id": 3160109, "created": "2022-11-28 13:26:17", "channel_order_code": "B2B0001060", "modified": "2022-11-28 14:32:04", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "Special instructions go here.", "status": "shipping", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 10000, "tax": 1515, "total": 11615, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-14 09:31:06", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000124", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 13806204, "created": "2022-11-28 13:26:17", "barcode": null, "price": 10000, "qty": 1, "sku": "ABC", "modified": "2022-11-28 13:26:17", "source_id": 57, "variant_id": 67777, "title": "Macbook Pro 15' Retina 2015 (512 ssd)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "13806204", "fulfillment_id": "431576", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "1500.00", "code": "taxed"}], "sub_total": 10000, "sub_total_display": "10 000.00", "tax": 1500, "tax_display": "1 500.00", "tax_per_unit": 1500, "tax_per_unit_display": "1 500.00", "total": 11500, "total_display": "11 500.00", "price_display": "10 000.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 13806205, "created": "2022-11-28 13:26:17", "barcode": null, "price": 100, "qty": 1, "modified": "2022-11-28 13:26:17", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "Order Number:"}, {"key": "delivery_date", "value": null}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "238016"}, {"key": "coupon", "value": null}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_email", "value": "<EMAIL>"}, {"key": "console_user_name", "value": "<PERSON>"}], "shipping_address": {"id": "6145122", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-11-28 13:26:17", "modified": "2022-11-28 13:26:17", "client_id": "21"}, "billing_address": {"id": "6145123", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-11-28 13:26:17", "modified": "2022-11-28 13:26:17", "client_id": "21"}, "history": [{"id": "8679004", "created": "2022-11-28 13:26:17", "modified": "2022-11-28 13:26:17", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-11/28/13/26/16-*********-orderhistory-sync_order-3160109.json"}, {"id": "8679005", "created": "2022-11-28 13:26:17", "modified": "2022-11-28 13:26:17", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-11/28/13/26/16-*********-orderhistory-unpaid_order-3160109.json"}, {"id": "8679006", "created": "2022-11-28 13:26:17", "modified": "2022-11-28 13:26:17", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-11/28/13/26/16-*********-orderhistory-unpaid_order-3160109.json"}], "fulfillments": {"431576": {"id": "431576", "fulfillmentservice_order_code": "61974229", "tracking_company": "To be determined", "tracking_number": null, "tracking_url": null, "status": "Awaiting Stock", "state": "pending", "notes": "20221128143158 - 240: Awaiting Stock\n", "items": [{"orderitem_id": "13806204", "fulfillment_id": "431576", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}]}}, "sub_total_display": "10 000.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "1 515.00", "total_discount_display": "0.00", "total_display": "11 615.00", "_id": "3160109", "short_date": "Mon 3:26 PM", "channel_name": "Stock2Shop Demo - B2B Wholesale", "total_currency": "11615.00"}}, {"_index": "orders----------2024-06-20-10-43-39", "_type": "_doc", "_id": "3175183", "_score": 0, "_source": {"id": 3175183, "created": "2022-12-01 08:31:16", "channel_order_code": "B2B0001063", "modified": "2022-12-01 08:31:27", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "Special instructions go here.", "status": "shipping", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 10000, "tax": 1515, "total": 11615, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-14 09:31:06", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000127", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 13874564, "created": "2022-12-01 08:31:16", "barcode": null, "price": 10000, "qty": 1, "sku": "ABC", "modified": "2022-12-01 08:31:16", "source_id": 57, "variant_id": 67777, "title": "Macbook Pro 15' Retina 2015 (512 ssd)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "13874564", "fulfillment_id": "433188", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "1500.00", "code": "taxed"}], "sub_total": 10000, "sub_total_display": "10 000.00", "tax": 1500, "tax_display": "1 500.00", "tax_per_unit": 1500, "tax_per_unit_display": "1 500.00", "total": 11500, "total_display": "11 500.00", "price_display": "10 000.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 13874565, "created": "2022-12-01 08:31:16", "barcode": null, "price": 100, "qty": 1, "modified": "2022-12-01 08:31:16", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "Order Number:"}, {"key": "delivery_date", "value": null}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "238016"}, {"key": "coupon", "value": null}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_email", "value": "<EMAIL>"}, {"key": "console_user_name", "value": "<PERSON>"}], "shipping_address": {"id": "6173007", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-12-01 08:31:16", "modified": "2022-12-01 08:31:16", "client_id": "21"}, "billing_address": {"id": "6173008", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-12-01 08:31:16", "modified": "2022-12-01 08:31:16", "client_id": "21"}, "history": [{"id": "8719475", "created": "2022-12-01 08:31:16", "modified": "2022-12-01 08:31:16", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-12/01/08/31/16-*********-orderhistory-sync_order-3175183.json"}, {"id": "8719476", "created": "2022-12-01 08:31:16", "modified": "2022-12-01 08:31:16", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-12/01/08/31/16-*********-orderhistory-unpaid_order-3175183.json"}, {"id": "8719477", "created": "2022-12-01 08:31:16", "modified": "2022-12-01 08:31:16", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-12/01/08/31/16-*********-orderhistory-unpaid_order-3175183.json"}], "fulfillments": {"433188": {"id": "433188", "fulfillmentservice_order_code": "61980880", "tracking_company": "To be determined", "tracking_number": null, "tracking_url": null, "status": "Awaiting Stock", "state": "pending", "notes": "20221201083123 - 240: Awaiting Stock\n", "items": [{"orderitem_id": "13874564", "fulfillment_id": "433188", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}]}}, "sub_total_display": "10 000.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "1 515.00", "total_discount_display": "0.00", "total_display": "11 615.00", "_id": "3175183", "short_date": "Thu 10:31 AM", "channel_name": "Stock2Shop Demo - B2B Wholesale", "total_currency": "11615.00"}}, {"_index": "orders----------2024-06-20-10-43-39", "_type": "_doc", "_id": "3192218", "_score": 0, "_source": {"id": 3192218, "created": "2022-12-06 09:46:04", "channel_order_code": "B2B0001064", "modified": "2022-12-06 09:46:17", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "Special instructions go here.", "status": "shipping", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 30000, "tax": 4515, "total": 34615, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-14 09:31:06", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000128", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 13956434, "created": "2022-12-06 09:46:05", "barcode": null, "price": 10000, "qty": 3, "sku": "ABC", "modified": "2022-12-06 09:46:05", "source_id": 57, "variant_id": 67777, "title": "Macbook Pro 15' Retina 2015 (512 ssd)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "13956434", "fulfillment_id": "435751", "qty": "3", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "1500.00", "code": "taxed"}], "sub_total": 30000, "sub_total_display": "30 000.00", "tax": 4500, "tax_display": "4 500.00", "tax_per_unit": 1500, "tax_per_unit_display": "1 500.00", "total": 34500, "total_display": "34 500.00", "price_display": "10 000.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 13956435, "created": "2022-12-06 09:46:05", "barcode": null, "price": 100, "qty": 1, "modified": "2022-12-06 09:46:05", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "Order Number:"}, {"key": "delivery_date", "value": null}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "238016"}, {"key": "coupon", "value": null}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_email", "value": "<EMAIL>"}, {"key": "console_user_name", "value": "<PERSON>"}], "shipping_address": {"id": "6203574", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-12-06 09:46:05", "modified": "2022-12-06 09:46:05", "client_id": "21"}, "billing_address": {"id": "6203575", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-12-06 09:46:05", "modified": "2022-12-06 09:46:05", "client_id": "21"}, "history": [{"id": "8764309", "created": "2022-12-06 09:46:04", "modified": "2022-12-06 09:46:05", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-12/06/09/46/04-*********-orderhistory-sync_order-3192218.json"}, {"id": "8764310", "created": "2022-12-06 09:46:05", "modified": "2022-12-06 09:46:05", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-12/06/09/46/04-*********-orderhistory-unpaid_order-3192218.json"}, {"id": "8764311", "created": "2022-12-06 09:46:05", "modified": "2022-12-06 09:46:05", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-12/06/09/46/04-*********-orderhistory-unpaid_order-3192218.json"}], "fulfillments": {"435751": {"id": "435751", "fulfillmentservice_order_code": "61988148", "tracking_company": "To be determined", "tracking_number": null, "tracking_url": null, "status": "Awaiting Stock", "state": "pending", "notes": "20221206094613 - 240: Awaiting Stock\n", "items": [{"orderitem_id": "13956434", "fulfillment_id": "435751", "qty": "3", "fulfilled_qty": "0", "status": "Awaiting stock"}]}}, "sub_total_display": "30 000.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "4 515.00", "total_discount_display": "0.00", "total_display": "34 615.00", "_id": "3192218", "short_date": "<PERSON><PERSON> 11:46 AM", "channel_name": "Stock2Shop Demo - B2B Wholesale", "total_currency": "34615.00"}}]}, "request": {"url": "https://host.stock2shop.test:9200/orders/_search", "method": "POST", "body": {"aggs": {}, "query": {"bool": {"filter": [{"term": {"client_id": 21}}, {"term": {"customer_id": 4649847}}], "must": [], "must_not": []}}, "from": 0, "size": 20, "sort": {"_score": {"order": "desc"}}}}, "system_orders": [{"id": 2888752, "created": "2022-09-20 12:37:11", "channel_order_code": "B2B0000999", "modified": "2022-09-20 12:37:18", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "Delivery by Friday", "status": "ordered", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 4500, "tax": 690, "total": 5290, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-16 09:46:11", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000030", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 12535411, "created": "2022-09-20 12:37:11", "barcode": null, "price": 1500, "qty": 3, "sku": "SPR/LOC", "modified": "2022-09-20 12:37:11", "source_id": 57, "variant_id": 67757, "title": "Spreadsheet Software V9 (OEM)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "TAX", "price": "225.00", "code": "taxed"}], "sub_total": 4500, "sub_total_display": "4 500.00", "tax": 675, "tax_display": "675.00", "tax_per_unit": 225, "tax_per_unit_display": "225.00", "total": 5175, "total_display": "5 175.00", "price_display": "1 500.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 12535412, "created": "2022-09-20 12:37:11", "barcode": null, "price": 100, "qty": 1, "modified": "2022-09-20 12:37:11", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "Order Number: PO1"}, {"key": "delivery_date", "value": ""}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "238016"}, {"key": "coupon", "value": ""}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_email", "value": "<EMAIL>"}, {"key": "console_user_name", "value": "<PERSON>"}], "shipping_address": {"address1": "", "address2": "", "city": "", "company": "", "country": "", "first_name": "", "last_name": "", "phone": "", "province": "", "zip": "", "country_code": "", "province_code": ""}, "history": [{"id": "7949534", "created": "2022-09-20 12:37:11", "modified": "2022-09-20 12:37:11", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-09/20/12/37/11-*********-orderhistory-sync_order-2888752.json"}, {"id": "7949535", "created": "2022-09-20 12:37:11", "modified": "2022-09-20 12:37:11", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-09/20/12/37/11-*********-orderhistory-unpaid_order-2888752.json"}, {"id": "7949536", "created": "2022-09-20 12:37:11", "modified": "2022-09-20 12:37:11", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-09/20/12/37/11-*********-orderhistory-unpaid_order-2888752.json"}], "sub_total_display": "4 500.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "690.00", "total_discount_display": "0.00", "total_display": "5 290.00"}, {"id": 2888796, "created": "2022-09-20 12:46:07", "channel_order_code": "B2B0001002", "modified": "2022-09-20 12:46:14", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "Delivery on Friday", "status": "ordered", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 1500, "tax": 240, "total": 1840, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-16 09:46:11", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000033", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 12535618, "created": "2022-09-20 12:46:07", "barcode": null, "price": 1500, "qty": 1, "sku": "SPR/LOC", "modified": "2022-09-20 12:46:07", "source_id": 57, "variant_id": 67757, "title": "Spreadsheet Software V9 (OEM)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "TAX", "price": "225.00", "code": "taxed"}], "sub_total": 1500, "sub_total_display": "1 500.00", "tax": 225, "tax_display": "225.00", "tax_per_unit": 225, "tax_per_unit_display": "225.00", "total": 1725, "total_display": "1 725.00", "price_display": "1 500.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 12535619, "created": "2022-09-20 12:46:07", "barcode": null, "price": 100, "qty": 1, "modified": "2022-09-20 12:46:07", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "Order Number: 123"}, {"key": "delivery_date", "value": ""}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "238016"}, {"key": "coupon", "value": ""}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_email", "value": "<EMAIL>"}, {"key": "console_user_name", "value": "<PERSON>"}], "shipping_address": {"address1": "", "address2": "", "city": "", "company": "", "country": "", "first_name": "", "last_name": "", "phone": "", "province": "", "zip": "", "country_code": "", "province_code": ""}, "history": [{"id": "7949651", "created": "2022-09-20 12:46:07", "modified": "2022-09-20 12:46:07", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-09/20/12/46/07-*********-orderhistory-sync_order-2888796.json"}, {"id": "7949652", "created": "2022-09-20 12:46:07", "modified": "2022-09-20 12:46:07", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-09/20/12/46/07-*********-orderhistory-unpaid_order-2888796.json"}, {"id": "7949653", "created": "2022-09-20 12:46:07", "modified": "2022-09-20 12:46:07", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-09/20/12/46/07-*********-orderhistory-unpaid_order-2888796.json"}], "sub_total_display": "1 500.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "240.00", "total_discount_display": "0.00", "total_display": "1 840.00"}, {"id": 2888870, "created": "2022-09-20 13:02:10", "channel_order_code": "B2B0001004", "modified": "2022-09-20 13:02:21", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "Special instructions go here.", "status": "shipping", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 100, "tax": 30, "total": 230, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-16 09:46:11", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000035", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 12535920, "created": "2022-09-20 13:02:10", "barcode": null, "price": 100, "qty": 1, "sku": "MOD/234", "modified": "2022-09-20 13:02:10", "source_id": 57, "variant_id": 67753, "title": "96K Modem (N/A)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "12535920", "fulfillment_id": "376322", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 12535921, "created": "2022-09-20 13:02:10", "barcode": null, "price": 100, "qty": 1, "modified": "2022-09-20 13:02:10", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "Order Number:"}, {"key": "delivery_date", "value": ""}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "571"}, {"key": "coupon", "value": ""}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_name", "value": ""}], "shipping_address": {"id": "5648204", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-09-20 13:02:10", "modified": "2022-09-20 13:02:10", "client_id": "21"}, "billing_address": {"id": "5648205", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-09-20 13:02:10", "modified": "2022-09-20 13:02:10", "client_id": "21"}, "history": [{"id": "7949855", "created": "2022-09-20 13:02:10", "modified": "2022-09-20 13:02:10", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-09/20/13/02/10-*********-orderhistory-sync_order-2888870.json"}, {"id": "7949856", "created": "2022-09-20 13:02:10", "modified": "2022-09-20 13:02:10", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-09/20/13/02/10-*********-orderhistory-unpaid_order-2888870.json"}, {"id": "7949857", "created": "2022-09-20 13:02:10", "modified": "2022-09-20 13:02:10", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-09/20/13/02/10-*********-orderhistory-unpaid_order-2888870.json"}], "fulfillments": {"376322": {"id": "376322", "fulfillmentservice_order_code": "61848823", "tracking_company": "To be determined", "tracking_number": "", "tracking_url": "", "status": "Awaiting Stock", "state": "pending", "notes": "20220920130218 - 240: Awaiting Stock\n", "items": [{"orderitem_id": "12535920", "fulfillment_id": "376322", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}]}}, "sub_total_display": "100.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "30.00", "total_discount_display": "0.00", "total_display": "230.00"}, {"id": 2888900, "created": "2022-09-20 13:08:03", "channel_order_code": "B2B0001006", "modified": "2022-09-20 13:08:10", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "Special instructions go here.", "status": "ordered", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 1500, "tax": 240, "total": 1840, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-16 09:46:11", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000037", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 12536010, "created": "2022-09-20 13:08:03", "barcode": null, "price": 1500, "qty": 1, "sku": "SPR/LOC", "modified": "2022-09-20 13:08:03", "source_id": 57, "variant_id": 67757, "title": "Spreadsheet Software V9 (OEM)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "TAX", "price": "225.00", "code": "taxed"}], "sub_total": 1500, "sub_total_display": "1 500.00", "tax": 225, "tax_display": "225.00", "tax_per_unit": 225, "tax_per_unit_display": "225.00", "total": 1725, "total_display": "1 725.00", "price_display": "1 500.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 12536012, "created": "2022-09-20 13:08:03", "barcode": null, "price": 100, "qty": 1, "modified": "2022-09-20 13:08:03", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "Order Number:"}, {"key": "delivery_date", "value": ""}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "238016"}, {"key": "coupon", "value": ""}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_email", "value": "<EMAIL>"}, {"key": "console_user_name", "value": "<PERSON>"}], "shipping_address": {"address1": "", "address2": "", "city": "", "company": "", "country": "", "first_name": "", "last_name": "", "phone": "", "province": "", "zip": "", "country_code": "", "province_code": ""}, "history": [{"id": "7949936", "created": "2022-09-20 13:08:03", "modified": "2022-09-20 13:08:03", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-09/20/13/08/03-*********-orderhistory-sync_order-2888900.json"}, {"id": "7949937", "created": "2022-09-20 13:08:03", "modified": "2022-09-20 13:08:03", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-09/20/13/08/03-*********-orderhistory-unpaid_order-2888900.json"}, {"id": "7949939", "created": "2022-09-20 13:08:03", "modified": "2022-09-20 13:08:03", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-09/20/13/08/03-*********-orderhistory-unpaid_order-2888900.json"}], "sub_total_display": "1 500.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "240.00", "total_discount_display": "0.00", "total_display": "1 840.00"}, {"id": 2889061, "created": "2022-09-20 13:43:11", "channel_order_code": "B2B0001008", "modified": "2022-09-20 13:43:27", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "Special instructions go here.", "status": "shipping", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 1500, "tax": 240, "total": 1840, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-16 09:46:11", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000039", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 12536630, "created": "2022-09-20 13:43:12", "barcode": null, "price": 1500, "qty": 1, "sku": "SPR/LOC", "modified": "2022-09-20 13:43:12", "source_id": 57, "variant_id": 67757, "title": "Spreadsheet Software V9 (OEM)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "12536630", "fulfillment_id": "376355", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "225.00", "code": "taxed"}], "sub_total": 1500, "sub_total_display": "1 500.00", "tax": 225, "tax_display": "225.00", "tax_per_unit": 225, "tax_per_unit_display": "225.00", "total": 1725, "total_display": "1 725.00", "price_display": "1 500.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 12536631, "created": "2022-09-20 13:43:12", "barcode": null, "price": 100, "qty": 1, "modified": "2022-09-20 13:43:12", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "Order Number:"}, {"key": "delivery_date", "value": ""}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "238016"}, {"key": "coupon", "value": ""}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_email", "value": "<EMAIL>"}, {"key": "console_user_name", "value": "<PERSON>"}], "shipping_address": {"id": "5648537", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-09-20 13:43:12", "modified": "2022-09-20 13:43:12", "client_id": "21"}, "billing_address": {"id": "5648538", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-09-20 13:43:12", "modified": "2022-09-20 13:43:12", "client_id": "21"}, "history": [{"id": "7950373", "created": "2022-09-20 13:43:11", "modified": "2022-09-20 13:43:11", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-09/20/13/43/11-*********-orderhistory-sync_order-2889061.json"}, {"id": "7950374", "created": "2022-09-20 13:43:11", "modified": "2022-09-20 13:43:12", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-09/20/13/43/11-*********-orderhistory-unpaid_order-2889061.json"}, {"id": "7950375", "created": "2022-09-20 13:43:12", "modified": "2022-09-20 13:43:12", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-09/20/13/43/11-*********-orderhistory-unpaid_order-2889061.json"}], "fulfillments": {"376355": {"id": "376355", "fulfillmentservice_order_code": "61848922", "tracking_company": "To be determined", "tracking_number": "", "tracking_url": "", "status": "Awaiting Stock", "state": "pending", "notes": "20220920134324 - 240: Awaiting Stock\n", "items": [{"orderitem_id": "12536630", "fulfillment_id": "376355", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}]}}, "sub_total_display": "1 500.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "240.00", "total_discount_display": "0.00", "total_display": "1 840.00"}, {"id": 2889084, "created": "2022-09-20 13:48:03", "channel_order_code": "B2B0001009", "modified": "2022-09-20 13:48:15", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "Special instructions go here.", "status": "shipping", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 1500, "tax": 240, "total": 1840, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-16 09:46:11", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000040", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 12536752, "created": "2022-09-20 13:48:03", "barcode": null, "price": 1500, "qty": 1, "sku": "SPR/LOC", "modified": "2022-09-20 13:48:03", "source_id": 57, "variant_id": 67757, "title": "Spreadsheet Software V9 (OEM)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "12536752", "fulfillment_id": "376359", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "225.00", "code": "taxed"}], "sub_total": 1500, "sub_total_display": "1 500.00", "tax": 225, "tax_display": "225.00", "tax_per_unit": 225, "tax_per_unit_display": "225.00", "total": 1725, "total_display": "1 725.00", "price_display": "1 500.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 12536753, "created": "2022-09-20 13:48:03", "barcode": null, "price": 100, "qty": 1, "modified": "2022-09-20 13:48:03", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "Order Number:"}, {"key": "delivery_date", "value": ""}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "238016"}, {"key": "coupon", "value": ""}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_email", "value": "<EMAIL>"}, {"key": "console_user_name", "value": "<PERSON>"}], "shipping_address": {"id": "5648573", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-09-20 13:48:03", "modified": "2022-09-20 13:48:03", "client_id": "21"}, "billing_address": {"id": "5648574", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-09-20 13:48:03", "modified": "2022-09-20 13:48:03", "client_id": "21"}, "history": [{"id": "7950436", "created": "2022-09-20 13:48:03", "modified": "2022-09-20 13:48:03", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-09/20/13/48/03-*********-orderhistory-sync_order-2889084.json"}, {"id": "7950437", "created": "2022-09-20 13:48:03", "modified": "2022-09-20 13:48:03", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-09/20/13/48/03-*********-orderhistory-unpaid_order-2889084.json"}, {"id": "7950438", "created": "2022-09-20 13:48:03", "modified": "2022-09-20 13:48:03", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-09/20/13/48/03-*********-orderhistory-unpaid_order-2889084.json"}], "fulfillments": {"376359": {"id": "376359", "fulfillmentservice_order_code": "61848928", "tracking_company": "To be determined", "tracking_number": "", "tracking_url": "", "status": "Awaiting Stock", "state": "pending", "notes": "20220920134811 - 240: Awaiting Stock\n", "items": [{"orderitem_id": "12536752", "fulfillment_id": "376359", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}]}}, "sub_total_display": "1 500.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "240.00", "total_discount_display": "0.00", "total_display": "1 840.00"}, {"id": 2921213, "created": "2022-09-30 14:54:03", "channel_order_code": "B2B0001013", "modified": "2022-09-30 14:54:17", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "testing for Grant", "status": "shipping", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 17300, "tax": 2610, "total": 20010, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-16 09:46:11", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000045", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 12685306, "created": "2022-09-30 14:54:03", "barcode": null, "price": 250, "qty": 2, "sku": "SPEAK/EAS", "modified": "2022-09-30 14:54:03", "source_id": 57, "variant_id": 67756, "title": "50W Speakers (N/A)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "12685306", "fulfillment_id": "382054", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "37.50", "code": "taxed"}], "sub_total": 500, "sub_total_display": "500.00", "tax": 75, "tax_display": "75.00", "tax_per_unit": 37.5, "tax_per_unit_display": "37.50", "total": 575, "total_display": "575.00", "price_display": "250.00", "total_discount_display": "0.00"}, {"id": 12685307, "created": "2022-09-30 14:54:03", "barcode": null, "price": 15000, "qty": 1, "sku": "ABC", "modified": "2022-09-30 14:54:03", "source_id": 57, "variant_id": 67777, "title": "Macbook Pro 15' Retina 2015 (512 ssd)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "12685307", "fulfillment_id": "382054", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "2250.00", "code": "taxed"}], "sub_total": 15000, "sub_total_display": "15 000.00", "tax": 2250, "tax_display": "2 250.00", "tax_per_unit": 2250, "tax_per_unit_display": "2 250.00", "total": 17250, "total_display": "17 250.00", "price_display": "15 000.00", "total_discount_display": "0.00"}, {"id": 12685308, "created": "2022-09-30 14:54:03", "barcode": null, "price": 900, "qty": 2, "sku": "DB/9/0", "modified": "2022-09-30 14:54:03", "source_id": 57, "variant_id": 67774, "title": "Database Software V9 (N/A)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "12685308", "fulfillment_id": "382054", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "135.00", "code": "taxed"}], "sub_total": 1800, "sub_total_display": "1 800.00", "tax": 270, "tax_display": "270.00", "tax_per_unit": 135, "tax_per_unit_display": "135.00", "total": 2070, "total_display": "2 070.00", "price_display": "900.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 12685309, "created": "2022-09-30 14:54:04", "barcode": null, "price": 100, "qty": 1, "modified": "2022-09-30 14:54:04", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "PO 123"}, {"key": "delivery_date", "value": ""}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "238016"}, {"key": "coupon", "value": ""}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_email", "value": "<EMAIL>"}, {"key": "console_user_name", "value": "<PERSON>"}], "shipping_address": {"id": "5706672", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-09-30 14:54:04", "modified": "2022-09-30 14:54:04", "client_id": "21"}, "billing_address": {"id": "5706673", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-09-30 14:54:04", "modified": "2022-09-30 14:54:04", "client_id": "21"}, "history": [{"id": "8036213", "created": "2022-09-30 14:54:03", "modified": "2022-09-30 14:54:03", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-09/30/14/54/03-*********-orderhistory-sync_order-2921213.json"}, {"id": "8036214", "created": "2022-09-30 14:54:03", "modified": "2022-09-30 14:54:03", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-09/30/14/54/03-*********-orderhistory-unpaid_order-2921213.json"}, {"id": "8036215", "created": "2022-09-30 14:54:04", "modified": "2022-09-30 14:54:04", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-09/30/14/54/03-*********-orderhistory-unpaid_order-2921213.json"}], "fulfillments": {"382054": {"id": "382054", "fulfillmentservice_order_code": "61865097", "tracking_company": "To be determined", "tracking_number": "", "tracking_url": "", "status": "Awaiting Stock", "state": "pending", "notes": "20220********* - 240: Awaiting Stock\n", "items": [{"orderitem_id": "12685306", "fulfillment_id": "382054", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}, {"orderitem_id": "12685307", "fulfillment_id": "382054", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}, {"orderitem_id": "12685308", "fulfillment_id": "382054", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}]}}, "sub_total_display": "17 300.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "2 610.00", "total_discount_display": "0.00", "total_display": "20 010.00"}, {"id": 2936814, "created": "2022-10-05 14:52:07", "channel_order_code": "B2B0001017", "modified": "2022-10-05 14:52:20", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "testing", "status": "shipping", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 32050, "tax": 4822.5, "total": 36972.5, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-16 09:46:11", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000049", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 12762395, "created": "2022-10-05 14:52:07", "barcode": null, "price": 250, "qty": 1, "sku": "SPEAK/EAS", "modified": "2022-10-05 14:52:07", "source_id": 57, "variant_id": 67756, "title": "50W Speakers (N/A)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "12762395", "fulfillment_id": "385216", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "37.50", "code": "taxed"}], "sub_total": 250, "sub_total_display": "250.00", "tax": 37.5, "tax_display": "37.50", "tax_per_unit": 37.5, "tax_per_unit_display": "37.50", "total": 287.5, "total_display": "287.50", "price_display": "250.00", "total_discount_display": "0.00"}, {"id": 12762396, "created": "2022-10-05 14:52:07", "barcode": null, "price": 900, "qty": 2, "sku": "DB/9/0", "modified": "2022-10-05 14:52:07", "source_id": 57, "variant_id": 67774, "title": "Database Software V9 (N/A)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "12762396", "fulfillment_id": "385216", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "135.00", "code": "taxed"}], "sub_total": 1800, "sub_total_display": "1 800.00", "tax": 270, "tax_display": "270.00", "tax_per_unit": 135, "tax_per_unit_display": "135.00", "total": 2070, "total_display": "2 070.00", "price_display": "900.00", "total_discount_display": "0.00"}, {"id": 12762397, "created": "2022-10-05 14:52:07", "barcode": null, "price": 15000, "qty": 2, "sku": "ABC", "modified": "2022-10-05 14:52:07", "source_id": 57, "variant_id": 67777, "title": "Macbook Pro 15' Retina 2015 (512 ssd)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "12762397", "fulfillment_id": "385216", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "2250.00", "code": "taxed"}], "sub_total": 30000, "sub_total_display": "30 000.00", "tax": 4500, "tax_display": "4 500.00", "tax_per_unit": 2250, "tax_per_unit_display": "2 250.00", "total": 34500, "total_display": "34 500.00", "price_display": "15 000.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 12762398, "created": "2022-10-05 14:52:07", "barcode": null, "price": 100, "qty": 1, "modified": "2022-10-05 14:52:07", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "PO-GM"}, {"key": "delivery_date", "value": ""}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "238016"}, {"key": "coupon", "value": ""}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_email", "value": "<EMAIL>"}, {"key": "console_user_name", "value": "<PERSON>"}], "shipping_address": {"id": "5735295", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-10-05 14:52:07", "modified": "2022-10-05 14:52:07", "client_id": "21"}, "billing_address": {"id": "5735296", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-10-05 14:52:07", "modified": "2022-10-05 14:52:07", "client_id": "21"}, "history": [{"id": "8077680", "created": "2022-10-05 14:52:07", "modified": "2022-10-05 14:52:07", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-10/05/14/52/07-*********-orderhistory-sync_order-2936814.json"}, {"id": "8077681", "created": "2022-10-05 14:52:07", "modified": "2022-10-05 14:52:07", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-10/05/14/52/07-*********-orderhistory-unpaid_order-2936814.json"}, {"id": "8077682", "created": "2022-10-05 14:52:07", "modified": "2022-10-05 14:52:07", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-10/05/14/52/07-*********-orderhistory-unpaid_order-2936814.json"}], "fulfillments": {"385216": {"id": "385216", "fulfillmentservice_order_code": "61872067", "tracking_company": "To be determined", "tracking_number": "", "tracking_url": "", "status": "Awaiting Stock", "state": "pending", "notes": "20221********* - 240: Awaiting Stock\n", "items": [{"orderitem_id": "12762395", "fulfillment_id": "385216", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}, {"orderitem_id": "12762396", "fulfillment_id": "385216", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}, {"orderitem_id": "12762397", "fulfillment_id": "385216", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}]}}, "sub_total_display": "32 050.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "4 822.50", "total_discount_display": "0.00", "total_display": "36 972.50"}, {"id": 2941974, "created": "2022-10-07 09:15:08", "channel_order_code": "B2B0001018", "modified": "2022-10-07 09:15:23", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "Special instructions go here.", "status": "shipping", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 30800, "tax": 4635, "total": 35535, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-16 09:46:11", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000050", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 12786557, "created": "2022-10-07 09:15:08", "barcode": null, "price": 800, "qty": 1, "sku": "DB/9/0", "modified": "2022-10-07 09:15:08", "source_id": 57, "variant_id": 67774, "title": "Database Software V9 (N/A)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "12786557", "fulfillment_id": "386137", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "120.00", "code": "taxed"}], "sub_total": 800, "sub_total_display": "800.00", "tax": 120, "tax_display": "120.00", "tax_per_unit": 120, "tax_per_unit_display": "120.00", "total": 920, "total_display": "920.00", "price_display": "800.00", "total_discount_display": "0.00"}, {"id": 12786558, "created": "2022-10-07 09:15:08", "barcode": null, "price": 10000, "qty": 3, "sku": "ABC", "modified": "2022-10-07 09:15:08", "source_id": 57, "variant_id": 67777, "title": "Macbook Pro 15' Retina 2015 (512 ssd)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "12786558", "fulfillment_id": "386137", "qty": "3", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "1500.00", "code": "taxed"}], "sub_total": 30000, "sub_total_display": "30 000.00", "tax": 4500, "tax_display": "4 500.00", "tax_per_unit": 1500, "tax_per_unit_display": "1 500.00", "total": 34500, "total_display": "34 500.00", "price_display": "10 000.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 12786559, "created": "2022-10-07 09:15:08", "barcode": null, "price": 100, "qty": 1, "modified": "2022-10-07 09:15:08", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "POS 23"}, {"key": "delivery_date", "value": ""}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "238016"}, {"key": "coupon", "value": ""}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_email", "value": "<EMAIL>"}, {"key": "console_user_name", "value": "<PERSON>"}], "shipping_address": {"id": "5744836", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-10-07 09:15:08", "modified": "2022-10-07 09:15:08", "client_id": "21"}, "billing_address": {"id": "5744837", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-10-07 09:15:08", "modified": "2022-10-07 09:15:08", "client_id": "21"}, "history": [{"id": "8091606", "created": "2022-10-07 09:15:08", "modified": "2022-10-07 09:15:08", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-10/07/09/15/08-*********-orderhistory-sync_order-2941974.json"}, {"id": "8091607", "created": "2022-10-07 09:15:08", "modified": "2022-10-07 09:15:08", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-10/07/09/15/08-*********-orderhistory-unpaid_order-2941974.json"}, {"id": "8091608", "created": "2022-10-07 09:15:08", "modified": "2022-10-07 09:15:08", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-10/07/09/15/08-*********-orderhistory-unpaid_order-2941974.json"}], "fulfillments": {"386137": {"id": "386137", "fulfillmentservice_order_code": "61874902", "tracking_company": "To be determined", "tracking_number": "", "tracking_url": "", "status": "Awaiting Stock", "state": "pending", "notes": "20221007091520 - 240: Awaiting Stock\n", "items": [{"orderitem_id": "12786557", "fulfillment_id": "386137", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}, {"orderitem_id": "12786558", "fulfillment_id": "386137", "qty": "3", "fulfilled_qty": "0", "status": "Awaiting stock"}]}}, "sub_total_display": "30 800.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "4 635.00", "total_discount_display": "0.00", "total_display": "35 535.00"}, {"id": 2959464, "created": "2022-10-13 07:04:16", "channel_order_code": "B2B0001020", "modified": "2022-10-13 07:04:48", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "Special instructions go here.", "status": "shipping", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 20800, "tax": 3135, "total": 24035, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-16 09:46:11", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000052", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 12869908, "created": "2022-10-13 07:04:16", "barcode": null, "price": 800, "qty": 1, "sku": "DB/9/0", "modified": "2022-10-13 07:04:16", "source_id": 57, "variant_id": 67774, "title": "Database Software V9 (N/A)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "12869908", "fulfillment_id": "389466", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "120.00", "code": "taxed"}], "sub_total": 800, "sub_total_display": "800.00", "tax": 120, "tax_display": "120.00", "tax_per_unit": 120, "tax_per_unit_display": "120.00", "total": 920, "total_display": "920.00", "price_display": "800.00", "total_discount_display": "0.00"}, {"id": 12869909, "created": "2022-10-13 07:04:16", "barcode": null, "price": 10000, "qty": 2, "sku": "ABC", "modified": "2022-10-13 07:04:16", "source_id": 57, "variant_id": 67777, "title": "Macbook Pro 15' Retina 2015 (512 ssd)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "12869909", "fulfillment_id": "389466", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "1500.00", "code": "taxed"}], "sub_total": 20000, "sub_total_display": "20 000.00", "tax": 3000, "tax_display": "3 000.00", "tax_per_unit": 1500, "tax_per_unit_display": "1 500.00", "total": 23000, "total_display": "23 000.00", "price_display": "10 000.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 12869910, "created": "2022-10-13 07:04:16", "barcode": null, "price": 100, "qty": 1, "modified": "2022-10-13 07:04:16", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "Order Number:"}, {"key": "delivery_date", "value": ""}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "238016"}, {"key": "coupon", "value": ""}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_email", "value": "<EMAIL>"}, {"key": "console_user_name", "value": "<PERSON>"}], "shipping_address": {"id": "5776486", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-10-13 07:04:16", "modified": "2022-10-13 07:04:16", "client_id": "21"}, "billing_address": {"id": "5776487", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-10-13 07:04:16", "modified": "2022-10-13 07:04:16", "client_id": "21"}, "history": [{"id": "8138112", "created": "2022-10-13 07:04:16", "modified": "2022-10-13 07:04:16", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-10/13/07/04/16-*********-orderhistory-sync_order-2959464.json"}, {"id": "8138113", "created": "2022-10-13 07:04:16", "modified": "2022-10-13 07:04:16", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-10/13/07/04/16-*********-orderhistory-unpaid_order-2959464.json"}, {"id": "8138114", "created": "2022-10-13 07:04:16", "modified": "2022-10-13 07:04:16", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-10/13/07/04/16-*********-orderhistory-unpaid_order-2959464.json"}], "fulfillments": {"389466": {"id": "389466", "fulfillmentservice_order_code": "61883020", "tracking_company": "To be determined", "tracking_number": "", "tracking_url": "", "status": "Awaiting Stock", "state": "pending", "notes": "20221013070444 - 240: Awaiting Stock\n", "items": [{"orderitem_id": "12869908", "fulfillment_id": "389466", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}, {"orderitem_id": "12869909", "fulfillment_id": "389466", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}]}}, "sub_total_display": "20 800.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "3 135.00", "total_discount_display": "0.00", "total_display": "24 035.00"}, {"id": 2960948, "created": "2022-10-13 12:17:03", "channel_order_code": "B2B0001021", "modified": "2022-10-13 12:17:18", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "Special instructions go here.", "status": "shipping", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 20000, "tax": 3015, "total": 23115, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-16 09:46:11", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000053", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 12877392, "created": "2022-10-13 12:17:03", "barcode": null, "price": 10000, "qty": 2, "sku": "ABC", "modified": "2022-10-13 12:17:03", "source_id": 57, "variant_id": 67777, "title": "Macbook Pro 15' Retina 2015 (512 ssd)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "12877392", "fulfillment_id": "389704", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "1500.00", "code": "taxed"}], "sub_total": 20000, "sub_total_display": "20 000.00", "tax": 3000, "tax_display": "3 000.00", "tax_per_unit": 1500, "tax_per_unit_display": "1 500.00", "total": 23000, "total_display": "23 000.00", "price_display": "10 000.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 12877393, "created": "2022-10-13 12:17:03", "barcode": null, "price": 100, "qty": 1, "modified": "2022-10-13 12:17:03", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "POASH"}, {"key": "delivery_date", "value": ""}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "238016"}, {"key": "coupon", "value": ""}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_email", "value": "<EMAIL>"}, {"key": "console_user_name", "value": "<PERSON>"}], "shipping_address": {"id": "5779167", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-10-13 12:17:03", "modified": "2022-10-13 12:17:03", "client_id": "21"}, "billing_address": {"id": "5779168", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-10-13 12:17:03", "modified": "2022-10-13 12:17:03", "client_id": "21"}, "history": [{"id": "8142064", "created": "2022-10-13 12:17:03", "modified": "2022-10-13 12:17:03", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-10/13/12/17/03-*********-orderhistory-sync_order-2960948.json"}, {"id": "8142065", "created": "2022-10-13 12:17:03", "modified": "2022-10-13 12:17:03", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-10/13/12/17/03-*********-orderhistory-unpaid_order-2960948.json"}, {"id": "8142066", "created": "2022-10-13 12:17:03", "modified": "2022-10-13 12:17:03", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-10/13/12/17/03-*********-orderhistory-unpaid_order-2960948.json"}], "fulfillments": {"389704": {"id": "389704", "fulfillmentservice_order_code": "61884086", "tracking_company": "To be determined", "tracking_number": "", "tracking_url": "", "status": "Awaiting Stock", "state": "pending", "notes": "20221013121714 - 240: Awaiting Stock\n", "items": [{"orderitem_id": "12877392", "fulfillment_id": "389704", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}]}}, "sub_total_display": "20 000.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "3 015.00", "total_discount_display": "0.00", "total_display": "23 115.00"}, {"id": 2961088, "created": "2022-10-13 12:46:03", "channel_order_code": "B2B0001022", "modified": "2022-10-13 12:46:17", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "Special instructions go here.", "status": "shipping", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 20000, "tax": 3015, "total": 23115, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-16 09:46:11", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000054", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 12878044, "created": "2022-10-13 12:46:03", "barcode": null, "price": 10000, "qty": 2, "sku": "ABC", "modified": "2022-10-13 12:46:03", "source_id": 57, "variant_id": 67777, "title": "Macbook Pro 15' Retina 2015 (512 ssd)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "12878044", "fulfillment_id": "389728", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "1500.00", "code": "taxed"}], "sub_total": 20000, "sub_total_display": "20 000.00", "tax": 3000, "tax_display": "3 000.00", "tax_per_unit": 1500, "tax_per_unit_display": "1 500.00", "total": 23000, "total_display": "23 000.00", "price_display": "10 000.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 12878045, "created": "2022-10-13 12:46:03", "barcode": null, "price": 100, "qty": 1, "modified": "2022-10-13 12:46:03", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "Order Number:"}, {"key": "delivery_date", "value": ""}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "238016"}, {"key": "coupon", "value": ""}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_email", "value": "<EMAIL>"}, {"key": "console_user_name", "value": "<PERSON>"}], "shipping_address": {"id": "5779422", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-10-13 12:46:03", "modified": "2022-10-13 12:46:03", "client_id": "21"}, "billing_address": {"id": "5779423", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-10-13 12:46:03", "modified": "2022-10-13 12:46:03", "client_id": "21"}, "history": [{"id": "8142434", "created": "2022-10-13 12:46:03", "modified": "2022-10-13 12:46:03", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-10/13/12/46/03-*********-orderhistory-sync_order-2961088.json"}, {"id": "8142435", "created": "2022-10-13 12:46:03", "modified": "2022-10-13 12:46:03", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-10/13/12/46/03-*********-orderhistory-unpaid_order-2961088.json"}, {"id": "8142436", "created": "2022-10-13 12:46:03", "modified": "2022-10-13 12:46:03", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-10/13/12/46/03-*********-orderhistory-unpaid_order-2961088.json"}], "fulfillments": {"389728": {"id": "389728", "fulfillmentservice_order_code": "61884151", "tracking_company": "To be determined", "tracking_number": "", "tracking_url": "", "status": "Awaiting Stock", "state": "pending", "notes": "20221013124612 - 240: Awaiting Stock\n", "items": [{"orderitem_id": "12878044", "fulfillment_id": "389728", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}]}}, "sub_total_display": "20 000.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "3 015.00", "total_discount_display": "0.00", "total_display": "23 115.00"}, {"id": 2963625, "created": "2022-10-14 09:35:16", "channel_order_code": "B2B0001024", "modified": "2022-10-14 09:35:30", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "Special instructions go here.", "status": "shipping", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 20280, "tax": 3057, "total": 23437, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-16 09:46:11", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000056", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 12889135, "created": "2022-10-14 09:35:16", "barcode": null, "price": 140, "qty": 2, "sku": "SPEAK/EAS", "modified": "2022-10-14 09:35:16", "source_id": 57, "variant_id": 67756, "title": "50W Speakers (N/A)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "12889135", "fulfillment_id": "390157", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "21.00", "code": "taxed"}], "sub_total": 280, "sub_total_display": "280.00", "tax": 42, "tax_display": "42.00", "tax_per_unit": 21, "tax_per_unit_display": "21.00", "total": 322, "total_display": "322.00", "price_display": "140.00", "total_discount_display": "0.00"}, {"id": 12889136, "created": "2022-10-14 09:35:16", "barcode": null, "price": 10000, "qty": 2, "sku": "ABC", "modified": "2022-10-14 09:35:16", "source_id": 57, "variant_id": 67777, "title": "Macbook Pro 15' Retina 2015 (512 ssd)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "12889136", "fulfillment_id": "390157", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "1500.00", "code": "taxed"}], "sub_total": 20000, "sub_total_display": "20 000.00", "tax": 3000, "tax_display": "3 000.00", "tax_per_unit": 1500, "tax_per_unit_display": "1 500.00", "total": 23000, "total_display": "23 000.00", "price_display": "10 000.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 12889137, "created": "2022-10-14 09:35:16", "barcode": null, "price": 100, "qty": 1, "modified": "2022-10-14 09:35:16", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "Order Number:"}, {"key": "delivery_date", "value": ""}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "238016"}, {"key": "coupon", "value": ""}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_email", "value": "<EMAIL>"}, {"key": "console_user_name", "value": "<PERSON>"}], "shipping_address": {"id": "5784024", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-10-14 09:35:17", "modified": "2022-10-14 09:35:17", "client_id": "21"}, "billing_address": {"id": "5784025", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-10-14 09:35:17", "modified": "2022-10-14 09:35:17", "client_id": "21"}, "history": [{"id": "8149009", "created": "2022-10-14 09:35:16", "modified": "2022-10-14 09:35:16", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-10/14/09/35/16-*********-orderhistory-sync_order-2963625.json"}, {"id": "8149010", "created": "2022-10-14 09:35:16", "modified": "2022-10-14 09:35:16", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-10/14/09/35/16-*********-orderhistory-unpaid_order-2963625.json"}, {"id": "8149011", "created": "2022-10-14 09:35:17", "modified": "2022-10-14 09:35:17", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-10/14/09/35/16-*********-orderhistory-unpaid_order-2963625.json"}], "fulfillments": {"390157": {"id": "390157", "fulfillmentservice_order_code": "61885384", "tracking_company": "To be determined", "tracking_number": "", "tracking_url": "", "status": "Awaiting Stock", "state": "pending", "notes": "20221014093526 - 240: Awaiting Stock\n", "items": [{"orderitem_id": "12889135", "fulfillment_id": "390157", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}, {"orderitem_id": "12889136", "fulfillment_id": "390157", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}]}}, "sub_total_display": "20 280.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "3 057.00", "total_discount_display": "0.00", "total_display": "23 437.00"}, {"id": 3047899, "created": "2022-11-07 08:14:07", "channel_order_code": "B2B0001029", "modified": "2022-11-07 08:14:29", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "Please deliver before Friday", "status": "shipping", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 20280, "tax": 3057, "total": 23437, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-16 09:46:11", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000087", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 13288422, "created": "2022-11-07 08:14:07", "barcode": null, "price": 10000, "qty": 2, "sku": "ABC", "modified": "2022-11-07 08:14:07", "source_id": 57, "variant_id": 67777, "title": "Macbook Pro 15' Retina 2015 (512 ssd)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "13288422", "fulfillment_id": "406749", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "1500.00", "code": "taxed"}], "sub_total": 20000, "sub_total_display": "20 000.00", "tax": 3000, "tax_display": "3 000.00", "tax_per_unit": 1500, "tax_per_unit_display": "1 500.00", "total": 23000, "total_display": "23 000.00", "price_display": "10 000.00", "total_discount_display": "0.00"}, {"id": 13288423, "created": "2022-11-07 08:14:07", "barcode": null, "price": 140, "qty": 2, "sku": "SPEAK/EAS", "modified": "2022-11-07 08:14:07", "source_id": 57, "variant_id": 67756, "title": "50W Speakers (N/A)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "13288423", "fulfillment_id": "406749", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "21.00", "code": "taxed"}], "sub_total": 280, "sub_total_display": "280.00", "tax": 42, "tax_display": "42.00", "tax_per_unit": 21, "tax_per_unit_display": "21.00", "total": 322, "total_display": "322.00", "price_display": "140.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 13288424, "created": "2022-11-07 08:14:07", "barcode": null, "price": 100, "qty": 1, "modified": "2022-11-07 08:14:07", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "Order Number:"}, {"key": "delivery_date", "value": ""}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "238016"}, {"key": "coupon", "value": ""}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_email", "value": "<EMAIL>"}, {"key": "console_user_name", "value": "<PERSON>"}], "shipping_address": {"id": "5937030", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-11-07 08:14:07", "modified": "2022-11-07 08:14:07", "client_id": "21"}, "billing_address": {"id": "5937031", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-11-07 08:14:07", "modified": "2022-11-07 08:14:07", "client_id": "21"}, "history": [{"id": "8375875", "created": "2022-11-07 08:14:07", "modified": "2022-11-07 08:14:07", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-11/07/08/14/06-*********-orderhistory-sync_order-3047899.json"}, {"id": "8375876", "created": "2022-11-07 08:14:07", "modified": "2022-11-07 08:14:07", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-11/07/08/14/07-*********-orderhistory-unpaid_order-3047899.json"}, {"id": "8375877", "created": "2022-11-07 08:14:07", "modified": "2022-11-07 08:14:07", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-11/07/08/14/07-*********-orderhistory-unpaid_order-3047899.json"}], "fulfillments": {"406749": {"id": "406749", "fulfillmentservice_order_code": "61922745", "tracking_company": "To be determined", "tracking_number": "", "tracking_url": "", "status": "Awaiting Stock", "state": "pending", "notes": "20221107081425 - 240: Awaiting Stock\n", "items": [{"orderitem_id": "13288422", "fulfillment_id": "406749", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}, {"orderitem_id": "13288423", "fulfillment_id": "406749", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}]}}, "sub_total_display": "20 280.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "3 057.00", "total_discount_display": "0.00", "total_display": "23 437.00"}, {"id": 3048560, "created": "2022-11-07 09:49:05", "channel_order_code": "B2B0001032", "modified": "2022-11-07 09:49:19", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "delivery before Friday", "status": "shipping", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 30000, "tax": 4515, "total": 34615, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-16 09:46:11", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000090", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 13291884, "created": "2022-11-07 09:49:05", "barcode": null, "price": 10000, "qty": 3, "sku": "ABC", "modified": "2022-11-07 09:49:05", "source_id": 57, "variant_id": 67777, "title": "Macbook Pro 15' Retina 2015 (512 ssd)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "13291884", "fulfillment_id": "406862", "qty": "3", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "1500.00", "code": "taxed"}], "sub_total": 30000, "sub_total_display": "30 000.00", "tax": 4500, "tax_display": "4 500.00", "tax_per_unit": 1500, "tax_per_unit_display": "1 500.00", "total": 34500, "total_display": "34 500.00", "price_display": "10 000.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 13291885, "created": "2022-11-07 09:49:05", "barcode": null, "price": 100, "qty": 1, "modified": "2022-11-07 09:49:05", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "Order Number:"}, {"key": "delivery_date", "value": ""}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "238016"}, {"key": "coupon", "value": ""}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_email", "value": "<EMAIL>"}, {"key": "console_user_name", "value": "<PERSON>"}], "shipping_address": {"id": "5938236", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-11-07 09:49:05", "modified": "2022-11-07 09:49:05", "client_id": "21"}, "billing_address": {"id": "5938237", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-11-07 09:49:05", "modified": "2022-11-07 09:49:05", "client_id": "21"}, "history": [{"id": "8377653", "created": "2022-11-07 09:49:05", "modified": "2022-11-07 09:49:05", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-11/07/09/49/04-*********-orderhistory-sync_order-3048560.json"}, {"id": "8377654", "created": "2022-11-07 09:49:05", "modified": "2022-11-07 09:49:05", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-11/07/09/49/05-*********-orderhistory-unpaid_order-3048560.json"}, {"id": "8377655", "created": "2022-11-07 09:49:05", "modified": "2022-11-07 09:49:05", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-11/07/09/49/05-*********-orderhistory-unpaid_order-3048560.json"}], "fulfillments": {"406862": {"id": "406862", "fulfillmentservice_order_code": "61923123", "tracking_company": "To be determined", "tracking_number": "", "tracking_url": "", "status": "Awaiting Stock", "state": "pending", "notes": "20221107094915 - 240: Awaiting Stock\n", "items": [{"orderitem_id": "13291884", "fulfillment_id": "406862", "qty": "3", "fulfilled_qty": "0", "status": "Awaiting stock"}]}}, "sub_total_display": "30 000.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "4 515.00", "total_discount_display": "0.00", "total_display": "34 615.00"}, {"id": 3085379, "created": "2022-11-16 13:17:14", "channel_order_code": "B2B0001047", "modified": "2022-11-16 13:17:14", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "Special instructions go here.", "status": "processing", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 90, "tax": 28.5, "total": 218.5, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-16 09:46:11", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "line_items": [{"id": 13471127, "created": "2022-11-16 13:17:14", "barcode": null, "price": 90, "qty": 1, "sku": "MOD/234", "modified": "2022-11-16 13:17:14", "source_id": 57, "variant_id": 67753, "title": "96K Modem (N/A)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "TAX", "price": "13.50", "code": "taxed"}], "sub_total": 90, "sub_total_display": "90.00", "tax": 13.5, "tax_display": "13.50", "tax_per_unit": 13.5, "tax_per_unit_display": "13.50", "total": 103.5, "total_display": "103.50", "price_display": "90.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 13471128, "created": "2022-11-16 13:17:14", "barcode": null, "price": 100, "qty": 1, "modified": "2022-11-16 13:17:14", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "Order Number:"}, {"key": "delivery_date", "value": ""}, {"key": "payment_method", "value": "AdumoVirtual"}, {"key": "console_user_id", "value": "571"}, {"key": "coupon", "value": ""}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_name", "value": ""}], "shipping_address": {"id": "6005874", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-11-16 13:17:14", "modified": "2022-11-16 13:17:14", "client_id": "21"}, "billing_address": {"id": "6005875", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-11-16 13:17:14", "modified": "2022-11-16 13:17:14", "client_id": "21"}, "history": [{"id": "8475593", "created": "2022-11-16 13:17:14", "modified": "2022-11-16 13:17:14", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-11/16/13/17/14-*********-orderhistory-sync_order-3085379.json"}, {"id": "8475594", "created": "2022-11-16 13:17:14", "modified": "2022-11-16 13:17:14", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-11/16/13/17/14-*********-orderhistory-unpaid_order-3085379.json"}], "sub_total_display": "90.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "28.50", "total_discount_display": "0.00", "total_display": "218.50"}, {"id": 3113916, "created": "2022-11-23 09:15:05", "channel_order_code": "B2B0001052", "modified": "2022-11-23 09:15:18", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "Special instructions go here.", "status": "shipping", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 20090, "tax": 3028.5, "total": 23218.5, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-16 09:46:11", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000116", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 13606855, "created": "2022-11-23 09:15:05", "barcode": null, "price": 10000, "qty": 2, "sku": "ABC", "modified": "2022-11-23 09:15:05", "source_id": 57, "variant_id": 67777, "title": "Macbook Pro 15' Retina 2015 (512 ssd)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "13606855", "fulfillment_id": "420393", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "1500.00", "code": "taxed"}], "sub_total": 20000, "sub_total_display": "20 000.00", "tax": 3000, "tax_display": "3 000.00", "tax_per_unit": 1500, "tax_per_unit_display": "1 500.00", "total": 23000, "total_display": "23 000.00", "price_display": "10 000.00", "total_discount_display": "0.00"}, {"id": 13606856, "created": "2022-11-23 09:15:05", "barcode": null, "price": 90, "qty": 1, "sku": "MOD/234", "modified": "2022-11-23 09:15:05", "source_id": 57, "variant_id": 67753, "title": "96K Modem (N/A)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "13606856", "fulfillment_id": "420393", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "13.50", "code": "taxed"}], "sub_total": 90, "sub_total_display": "90.00", "tax": 13.5, "tax_display": "13.50", "tax_per_unit": 13.5, "tax_per_unit_display": "13.50", "total": 103.5, "total_display": "103.50", "price_display": "90.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 13606857, "created": "2022-11-23 09:15:05", "barcode": null, "price": 100, "qty": 1, "modified": "2022-11-23 09:15:05", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "Order Number:"}, {"key": "delivery_date", "value": ""}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "238016"}, {"key": "coupon", "value": ""}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_email", "value": "<EMAIL>"}, {"key": "console_user_name", "value": "<PERSON>"}], "shipping_address": {"id": "6058946", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-11-23 09:15:05", "modified": "2022-11-23 09:15:05", "client_id": "21"}, "billing_address": {"id": "6058947", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-11-23 09:15:05", "modified": "2022-11-23 09:15:05", "client_id": "21"}, "history": [{"id": "8552127", "created": "2022-11-23 09:15:05", "modified": "2022-11-23 09:15:05", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-11/23/09/15/05-*********-orderhistory-sync_order-3113916.json"}, {"id": "8552128", "created": "2022-11-23 09:15:05", "modified": "2022-11-23 09:15:05", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-11/23/09/15/05-*********-orderhistory-unpaid_order-3113916.json"}, {"id": "8552129", "created": "2022-11-23 09:15:05", "modified": "2022-11-23 09:15:05", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-11/23/09/15/05-*********-orderhistory-unpaid_order-3113916.json"}], "fulfillments": {"420393": {"id": "420393", "fulfillmentservice_order_code": "61952158", "tracking_company": "To be determined", "tracking_number": "", "tracking_url": "", "status": "Awaiting Stock", "state": "pending", "notes": "20221123091514 - 240: Awaiting Stock\n", "items": [{"orderitem_id": "13606855", "fulfillment_id": "420393", "qty": "2", "fulfilled_qty": "0", "status": "Awaiting stock"}, {"orderitem_id": "13606856", "fulfillment_id": "420393", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}]}}, "sub_total_display": "20 090.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "3 028.50", "total_discount_display": "0.00", "total_display": "23 218.50"}, {"id": 3160109, "created": "2022-11-28 13:26:17", "channel_order_code": "B2B0001060", "modified": "2022-11-28 14:32:04", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "Special instructions go here.", "status": "shipping", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 10000, "tax": 1515, "total": 11615, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-16 09:46:11", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000124", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 13806204, "created": "2022-11-28 13:26:17", "barcode": null, "price": 10000, "qty": 1, "sku": "ABC", "modified": "2022-11-28 13:26:17", "source_id": 57, "variant_id": 67777, "title": "Macbook Pro 15' Retina 2015 (512 ssd)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "13806204", "fulfillment_id": "431576", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "1500.00", "code": "taxed"}], "sub_total": 10000, "sub_total_display": "10 000.00", "tax": 1500, "tax_display": "1 500.00", "tax_per_unit": 1500, "tax_per_unit_display": "1 500.00", "total": 11500, "total_display": "11 500.00", "price_display": "10 000.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 13806205, "created": "2022-11-28 13:26:17", "barcode": null, "price": 100, "qty": 1, "modified": "2022-11-28 13:26:17", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "Order Number:"}, {"key": "delivery_date", "value": ""}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "238016"}, {"key": "coupon", "value": ""}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_email", "value": "<EMAIL>"}, {"key": "console_user_name", "value": "<PERSON>"}], "shipping_address": {"id": "6145122", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-11-28 13:26:17", "modified": "2022-11-28 13:26:17", "client_id": "21"}, "billing_address": {"id": "6145123", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-11-28 13:26:17", "modified": "2022-11-28 13:26:17", "client_id": "21"}, "history": [{"id": "8679004", "created": "2022-11-28 13:26:17", "modified": "2022-11-28 13:26:17", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-11/28/13/26/16-*********-orderhistory-sync_order-3160109.json"}, {"id": "8679005", "created": "2022-11-28 13:26:17", "modified": "2022-11-28 13:26:17", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-11/28/13/26/16-*********-orderhistory-unpaid_order-3160109.json"}, {"id": "8679006", "created": "2022-11-28 13:26:17", "modified": "2022-11-28 13:26:17", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-11/28/13/26/16-*********-orderhistory-unpaid_order-3160109.json"}], "fulfillments": {"431576": {"id": "431576", "fulfillmentservice_order_code": "61974229", "tracking_company": "To be determined", "tracking_number": "", "tracking_url": "", "status": "Awaiting Stock", "state": "pending", "notes": "20221128143158 - 240: Awaiting Stock\n", "items": [{"orderitem_id": "13806204", "fulfillment_id": "431576", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}]}}, "sub_total_display": "10 000.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "1 515.00", "total_discount_display": "0.00", "total_display": "11 615.00"}, {"id": 3175183, "created": "2022-12-01 08:31:16", "channel_order_code": "B2B0001063", "modified": "2022-12-01 08:31:27", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "Special instructions go here.", "status": "shipping", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 10000, "tax": 1515, "total": 11615, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-16 09:46:11", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000127", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 13874564, "created": "2022-12-01 08:31:16", "barcode": null, "price": 10000, "qty": 1, "sku": "ABC", "modified": "2022-12-01 08:31:16", "source_id": 57, "variant_id": 67777, "title": "Macbook Pro 15' Retina 2015 (512 ssd)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "13874564", "fulfillment_id": "433188", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "1500.00", "code": "taxed"}], "sub_total": 10000, "sub_total_display": "10 000.00", "tax": 1500, "tax_display": "1 500.00", "tax_per_unit": 1500, "tax_per_unit_display": "1 500.00", "total": 11500, "total_display": "11 500.00", "price_display": "10 000.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 13874565, "created": "2022-12-01 08:31:16", "barcode": null, "price": 100, "qty": 1, "modified": "2022-12-01 08:31:16", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "Order Number:"}, {"key": "delivery_date", "value": ""}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "238016"}, {"key": "coupon", "value": ""}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_email", "value": "<EMAIL>"}, {"key": "console_user_name", "value": "<PERSON>"}], "shipping_address": {"id": "6173007", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-12-01 08:31:16", "modified": "2022-12-01 08:31:16", "client_id": "21"}, "billing_address": {"id": "6173008", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-12-01 08:31:16", "modified": "2022-12-01 08:31:16", "client_id": "21"}, "history": [{"id": "8719475", "created": "2022-12-01 08:31:16", "modified": "2022-12-01 08:31:16", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-12/01/08/31/16-*********-orderhistory-sync_order-3175183.json"}, {"id": "8719476", "created": "2022-12-01 08:31:16", "modified": "2022-12-01 08:31:16", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-12/01/08/31/16-*********-orderhistory-unpaid_order-3175183.json"}, {"id": "8719477", "created": "2022-12-01 08:31:16", "modified": "2022-12-01 08:31:16", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-12/01/08/31/16-*********-orderhistory-unpaid_order-3175183.json"}], "fulfillments": {"433188": {"id": "433188", "fulfillmentservice_order_code": "61980880", "tracking_company": "To be determined", "tracking_number": "", "tracking_url": "", "status": "Awaiting Stock", "state": "pending", "notes": "20221201083123 - 240: Awaiting Stock\n", "items": [{"orderitem_id": "13874564", "fulfillment_id": "433188", "qty": "1", "fulfilled_qty": "0", "status": "Awaiting stock"}]}}, "sub_total_display": "10 000.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "1 515.00", "total_discount_display": "0.00", "total_display": "11 615.00"}, {"id": 3192218, "created": "2022-12-06 09:46:04", "channel_order_code": "B2B0001064", "modified": "2022-12-06 09:46:17", "channel_id": 47, "client_id": 21, "customer_id": 4649847, "notes": "Special instructions go here.", "status": "shipping", "total_discount": 0, "ordered_date": null, "has_taxes_incl": false, "total_discount_incl": null, "line_item_sub_total": 0, "line_item_tax": 0, "shipping_sub_total": 0, "shipping_tax": 15, "sub_total": 30000, "tax": 4515, "total": 34615, "customer": {"id": "4649847", "created": "2022-09-20 12:26:23", "channel_customer_code": "4649847", "modified": "2024-05-16 09:46:11", "channel_id": "47", "client_id": "21", "last_name": "<PERSON>", "first_name": "<PERSON>", "email": null, "accepts_marketing": "0", "active": "1", "hash": "fc8673047bb998ed60dc0472c75de7b6", "user_id": "238643", "name": null, "surname": null, "user": {"id": "238643", "name": null, "surname": null, "email": null}}, "sources": [{"source_id": "57", "source_order_code": "SO0000128", "source_customer_code": "<PERSON>"}], "line_items": [{"id": 13956434, "created": "2022-12-06 09:46:05", "barcode": null, "price": 10000, "qty": 3, "sku": "ABC", "modified": "2022-12-06 09:46:05", "source_id": 57, "variant_id": 67777, "title": "Macbook Pro 15' Retina 2015 (512 ssd)", "grams": 0, "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "fulfillments": [{"orderitem_id": "13956434", "fulfillment_id": "435751", "qty": "3", "fulfilled_qty": "0", "status": "Awaiting stock"}], "tax_lines": [{"rate": "15", "title": "TAX", "price": "1500.00", "code": "taxed"}], "sub_total": 30000, "sub_total_display": "30 000.00", "tax": 4500, "tax_display": "4 500.00", "tax_per_unit": 1500, "tax_per_unit_display": "1 500.00", "total": 34500, "total_display": "34 500.00", "price_display": "10 000.00", "total_discount_display": "0.00"}], "shipping_lines": [{"id": 13956435, "created": "2022-12-06 09:46:05", "barcode": null, "price": 100, "qty": 1, "modified": "2022-12-06 09:46:05", "title": "Next Day", "total_discount": 0, "client_id": "21", "price_incl": null, "total_discount_incl": null, "product_id": null, "source_variant_code": null, "tax_lines": [{"rate": "15", "title": "VAT", "price": "15.00", "code": "taxed"}], "sub_total": 100, "sub_total_display": "100.00", "tax": 15, "tax_display": "15.00", "tax_per_unit": 15, "tax_per_unit_display": "15.00", "total": 115, "total_display": "115.00", "price_display": "100.00", "total_discount_display": "0.00"}], "meta": [{"key": "customer_reference", "value": "Order Number:"}, {"key": "delivery_date", "value": ""}, {"key": "payment_method", "value": "OnAccount"}, {"key": "console_user_id", "value": "238016"}, {"key": "coupon", "value": ""}, {"key": "collection", "value": "false"}, {"key": "order_type", "value": "sales_order"}, {"key": "delivery_date_days", "value": "3"}, {"key": "console_user_email", "value": "<EMAIL>"}, {"key": "console_user_name", "value": "<PERSON>"}], "shipping_address": {"id": "6203574", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-12-06 09:46:05", "modified": "2022-12-06 09:46:05", "client_id": "21"}, "billing_address": {"id": "6203575", "address1": "G Street", "address2": null, "city": "Cape Town", "company": "Stock2Shop", "country": "South Africa", "first_name": "<PERSON>", "last_name": "<PERSON>", "phone": "**********", "province": "Western Cape", "zip": "7130", "country_code": "ZA", "province_code": "WP", "created": "2022-12-06 09:46:05", "modified": "2022-12-06 09:46:05", "client_id": "21"}, "history": [{"id": "8764309", "created": "2022-12-06 09:46:04", "modified": "2022-12-06 09:46:05", "instruction": "sync_order", "client_id": "21", "storage_code": "21-47-0/2022-12/06/09/46/04-*********-orderhistory-sync_order-3192218.json"}, {"id": "8764310", "created": "2022-12-06 09:46:05", "modified": "2022-12-06 09:46:05", "instruction": "unpaid_order", "client_id": "21", "storage_code": "21-47-0/2022-12/06/09/46/04-*********-orderhistory-unpaid_order-3192218.json"}, {"id": "8764311", "created": "2022-12-06 09:46:05", "modified": "2022-12-06 09:46:05", "instruction": "provisionally_paid", "client_id": "21", "storage_code": "21-47-0/2022-12/06/09/46/04-*********-orderhistory-unpaid_order-3192218.json"}], "fulfillments": {"435751": {"id": "435751", "fulfillmentservice_order_code": "61988148", "tracking_company": "To be determined", "tracking_number": "", "tracking_url": "", "status": "Awaiting Stock", "state": "pending", "notes": "20221206094613 - 240: Awaiting Stock\n", "items": [{"orderitem_id": "13956434", "fulfillment_id": "435751", "qty": "3", "fulfilled_qty": "0", "status": "Awaiting stock"}]}}, "sub_total_display": "30 000.00", "shipping_total": 100, "shipping_total_display": "100.00", "shipping_tax_display": "15.00", "tax_display": "4 515.00", "total_discount_display": "0.00", "total_display": "34 615.00"}], "took_s2s": 51}