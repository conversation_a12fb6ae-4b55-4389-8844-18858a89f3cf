{"system_source_meta_values": {"apifact": {"deactivate_productmeta_prefix_csv": {"name": "sourcemeta_apifact", "key": "deactivate_productmeta_prefix_csv", "default": "", "description": "Comma separated list of meta prefixes. All product meta matching these prefixes will be removed on every updateProduct sync and only the synced meta will remain", "type": "string", "required": 0, "values": []}, "create_order": {"name": "sourcemeta_apifact", "key": "create_order", "default": "", "description": "Route for creating an order", "type": "string", "required": 1, "values": []}, "get_images": {"name": "sourcemeta_apifact", "key": "get_images", "default": "", "description": "Route for fetching images", "type": "string", "required": 0, "values": []}, "get_images_limit": {"name": "sourcemeta_apifact", "key": "get_images_limit", "default": "2", "description": "Number of images to fetch at a time", "type": "string", "required": 1, "values": []}, "get_order": {"name": "sourcemeta_apifact", "key": "get_order", "default": "", "description": "Route for fetching an order", "type": "string", "required": 1, "values": []}, "get_products": {"name": "sourcemeta_apifact", "key": "get_products", "default": "", "description": "Route for fetching products", "type": "string", "required": 1, "values": []}, "get_product": {"name": "sourcemeta_apifact", "key": "get_product", "default": "", "description": "Route for fetching product", "type": "string", "required": 1, "values": []}, "get_products_limit": {"name": "sourcemeta_apifact", "key": "get_products_limit", "default": "20", "description": "Number of products to fetch at a time", "type": "string", "required": 1, "values": []}, "param_create_customer_enabled": {"name": "sourcemeta_apifact", "key": "param_create_customer_enabled", "default": "false", "description": "Create new customer if default not found", "type": "string", "required": 0, "values": []}, "param_default_customer_code": {"name": "sourcemeta_apifact", "key": "param_default_customer_code", "default": "", "description": "Use this if source_customer_code not found or given", "type": "string", "required": 1, "values": []}, "param_ignore_shipping_warehouse_code": {"name": "sourcemeta_apifact", "key": "param_ignore_shipping_warehouse_code", "default": "false", "description": "If warehouse tracking on inventory is disabled for shipping, this will need to be set to true. This is currently only used for creating orders", "type": "string", "required": 0, "values": []}, "param_shipping_code": {"name": "sourcemeta_apifact", "key": "param_shipping_code", "default": "", "description": "Inventory code for shipping line items", "type": "string", "required": 1, "values": []}, "param_skip_image_hash": {"name": "sourcemeta_apifact", "key": "param_skip_image_hash", "default": "", "description": "Do not check hash when fetching images", "type": "string", "required": 0, "values": []}, "param_test": {"name": "sourcemeta_apifact", "key": "param_test", "default": "", "description": "Use the test config", "type": "string", "required": 0, "values": []}, "param_use_channel_order_code": {"name": "sourcemeta_apifact", "key": "param_use_channel_order_code", "default": "false", "description": "Create source order with same code as channel", "type": "string", "required": 0, "values": []}, "param_use_customer_address": {"name": "sourcemeta_apifact", "key": "param_use_customer_address", "default": "false", "description": "Use address from source", "type": "string", "required": 0, "values": []}, "param_user_field_customer_[x]": {"name": "sourcemeta_apifact", "key": "param_user_field_customer_[x]", "default": "false", "description": "Use this to set user defined field values on customer", "type": "string", "required": 0, "values": []}, "queue_fetch_images": {"name": "sourcemeta_apifact", "key": "queue_fetch_images", "default": "false", "description": "Queue fetch_images item after processing sync_product", "type": "string", "required": 1, "values": []}, "tunnel_host": {"name": "sourcemeta_apifact", "key": "tunnel_host", "default": "", "description": "Hostname for source tunnel", "type": "string", "required": 1, "values": []}, "tunnel_password": {"name": "sourcemeta_apifact", "key": "tunnel_password", "default": "", "description": "Password for source tunnel", "type": "string", "required": 1, "values": []}, "tunnel_username": {"name": "sourcemeta_apifact", "key": "tunnel_username", "default": "", "description": "Username for source tunnel", "type": "string", "required": 1, "values": []}}, "apifact_evolution": {"param_lot_enabled": {"name": "sourcemeta_apifact_evolution", "key": "param_lot_enabled", "default": "false", "description": "Order setting: Enable the lots feature [dependancy: lot_warehouse_id, lot_status_ids]", "type": "string", "required": 0, "values": []}, "param_lot_warehouse_id": {"name": "sourcemeta_apifact_evolution", "key": "param_lot_warehouse_id", "default": "", "description": "Order setting: Set the warehouse where it should check for available lots", "type": "string", "required": 0, "values": []}, "param_lot_status_ids": {"name": "sourcemeta_apifact_evolution", "key": "param_lot_status_ids", "default": "2", "description": "Order setting: Comma separated list of lot status IDs (e.g. 1,2)", "type": "string", "required": 0, "values": []}, "param_default_tax_code_exempt_shipping": {"name": "sourcemeta_apifact_evolution", "key": "param_default_tax_code_exempt_shipping", "default": "", "description": "Order setting: Default tax code to use in the orders for shipping lines with tax line code 'exempt'", "type": "string", "required": 1, "values": []}, "param_confirm_qty": {"name": "sourcemeta_apifact_evolution", "key": "param_confirm_qty", "default": "false", "description": "Order setting: Set the line items qty to confirmed on a Sales Order", "type": "string", "required": 0, "values": []}, "param_confirm_qty_shipping": {"name": "sourcemeta_apifact_evolution", "key": "param_confirm_qty_shipping", "default": "false", "description": "Order setting: Set the shipping line qty to confirmed on a Sales Order", "type": "string", "required": 0, "values": []}, "param_warehouse_code": {"name": "sourcemeta_apifact_evolution", "key": "param_warehouse_code", "default": "", "description": "Order setting: Raise the order into this Warehouse (ID) when creating orders", "type": "string", "required": 1, "values": []}, "param_branch_code": {"name": "sourcemeta_apifact_evolution", "key": "param_branch_code", "default": "", "description": "Order setting: Raise the order into this Branch (ID) when creating orders", "type": "string", "required": 0, "values": []}, "param_use_line_item_uom": {"name": "sourcemeta_apifact_evolution", "key": "param_use_line_item_uom", "default": "false", "description": "Order setting: Set the line item uom base on what is stored in Evolution", "type": "string", "required": 0, "values": []}, "param_use_line_item_discounts": {"name": "sourcemeta_apifact_evolution", "key": "param_use_line_item_discounts", "default": "false", "description": "Order setting: Sets discounts by line_item and set total_discount to 0 (Line items must have discounts set)", "type": "string", "required": 0, "values": []}, "param_delivery_method": {"name": "sourcemeta_apifact_evolution", "key": "param_delivery_method", "default": "", "description": "Order setting: Set the delivery method for order - note if delivery method does not exist in source, order will fail", "type": "string", "required": 0, "values": []}, "param_ignore_warehouse_code": {"name": "sourcemeta_apifact_evolution", "key": "param_ignore_warehouse_code", "default": "false", "description": "Order setting: If warehouse tracking on inventory is disabled, this will need to be set to true. Evolution version  9+ requires a warehouse code", "type": "string", "required": 0, "values": []}, "create_order": {"name": "sourcemeta_apifact_evolution", "key": "create_order", "default": "/evolution/createOrder", "description": "", "type": "string", "required": 1, "values": []}, "get_order": {"name": "sourcemeta_apifact_evolution", "key": "get_order", "default": "/evolution/getOrder", "description": "", "type": "string", "required": 1, "values": []}, "get_product": {"name": "sourcemeta_apifact_evolution", "key": "get_product", "default": "/evolution/getProduct", "description": "", "type": "string", "required": 1, "values": []}, "get_products": {"name": "sourcemeta_apifact_evolution", "key": "get_products", "default": "/evolution/getProducts", "description": "", "type": "string", "required": 1, "values": []}, "get_products_limit": {"name": "sourcemeta_apifact_evolution", "key": "get_products_limit", "default": "500", "description": "", "type": "string", "required": 1, "values": []}, "param_default_tax_code": {"name": "sourcemeta_apifact_evolution", "key": "param_default_tax_code", "default": "", "description": "Order setting: Default tax code to use in the orders for line items with tax line code 'taxed'", "type": "string", "required": 1, "values": []}, "param_default_tax_code_exempt": {"name": "sourcemeta_apifact_evolution", "key": "param_default_tax_code_exempt", "default": "", "description": "Order setting: Default tax code to use in the orders for line items with tax line code 'exempt'", "type": "string", "required": 1, "values": []}, "param_default_tax_code_shipping": {"name": "sourcemeta_apifact_evolution", "key": "param_default_tax_code_shipping", "default": "", "description": "Order setting: Default tax code to use in the orders for shipping lines with tax line code 'taxed'", "type": "string", "required": 1, "values": []}, "param_new_customer_representative_code": {"name": "sourcemeta_apifact_evolution", "key": "param_new_customer_representative_code", "default": "", "description": "Order setting: Set this sales representative code when creating a new customer", "type": "string", "required": 0, "values": []}, "param_order_representative_code": {"name": "sourcemeta_apifact_evolution", "key": "param_order_representative_code", "default": "", "description": "Order setting: Set this sales representative code when creating orders", "type": "string", "required": 0, "values": []}, "param_order_project_code": {"name": "sourcemeta_apifact_evolution", "key": "param_order_project_code", "default": "", "description": "Order setting: Set this project code when creating orders", "type": "string", "required": 0, "values": []}, "param_new_customer_area_code": {"name": "sourcemeta_apifact_evolution", "key": "param_new_customer_area_code", "default": "", "description": "Order setting: Set this area code when creating new customer", "type": "string", "required": 0, "values": []}, "param_new_customer_group_code": {"name": "sourcemeta_apifact_evolution", "key": "param_new_customer_group_code", "default": "", "description": "Order setting: Use this group code when creating new customer", "type": "string", "required": 0, "values": []}, "param_new_customer_price_list_code": {"name": "sourcemeta_apifact_evolution", "key": "param_new_customer_price_list_code", "default": "", "description": "Order setting: Set this price list code when creating new customer", "type": "string", "required": 0, "values": []}, "param_contact_source_customer": {"name": "sourcemeta_apifact_evolution", "key": "param_contact_source_customer", "default": "false", "description": "If \"true\" then source_customer_code is made up of Account and idDelAddress. i,e. customers are contacts from Evolution. When order is raised, this will be raised on the account code", "type": "string", "required": 0, "values": []}, "param_process_invoice": {"name": "sourcemeta_apifact_evolution", "key": "param_process_invoice", "default": "false", "description": "Order setting: Creates invoice", "type": "string", "required": 0, "values": []}, "param_process_credit_note": {"name": "sourcemeta_apifact_evolution", "key": "param_process_credit_note", "default": "false", "description": "Order setting: Create credit note", "type": "string", "required": 0, "values": []}, "param_negative_stock_disabled": {"name": "sourcemeta_apifact_evolution", "key": "param_negative_stock_disabled", "default": "true", "description": "Order setting: Do not allow negative stock for this source when order is created, if set to true, stock must exist in Evolution warehouse or order will fail", "type": "string", "required": 0, "values": []}, "param_complete_credit_note": {"name": "sourcemeta_apifact_evolution", "key": "param_complete_credit_note", "default": "false", "description": "Order setting: Complete credit note", "type": "string", "required": 0, "values": []}, "param_complete_invoice": {"name": "sourcemeta_apifact_evolution", "key": "param_complete_invoice", "default": "false", "description": "Order setting: Completes invoice", "type": "string", "required": 0, "values": []}, "param_use_credit_note_discount": {"name": "sourcemeta_apifact_evolution", "key": "param_use_credit_note_discount", "default": "false", "description": "Order setting: Create credit note for discount when creating the order", "type": "string", "required": 0, "values": []}, "param_external_order_no": {"name": "sourcemeta_apifact_evolution", "key": "param_external_order_no", "default": "", "description": "Order setting: Could be channel_order_code or purchase order number when populating the external order number", "type": "string", "required": 0, "values": []}, "param_gl_discount_code": {"name": "sourcemeta_apifact_evolution", "key": "param_gl_discount_code", "default": "", "description": "Order setting: General ledger account code to use for discounts when raising orders", "type": "string", "required": 0, "values": []}, "param_gl_shipping_code": {"name": "sourcemeta_apifact_evolution", "key": "param_gl_shipping_code", "default": "", "description": "Order setting: General ledger account code to use for shipping lines when raising orders into Evolution", "type": "string", "required": 0, "values": []}, "param_gl_shipping_discount_code": {"name": "sourcemeta_apifact_evolution", "key": "param_gl_shipping_discount_code", "default": "", "description": "Order setting: General ledger account code to use for shipping discount lines when raising orders into Evolution", "type": "string", "required": 0, "values": []}, "param_order_document_type": {"name": "sourcemeta_apifact_evolution", "key": "param_order_document_type", "default": "Sales Order", "description": "Order setting: What order document type should be set when creating orders in Evolution. Options are \"Quotation, \" Sales Order\"", "type": "string", "required": 1, "values": []}, "param_due_date": {"name": "sourcemeta_apifact_evolution", "key": "param_due_date", "default": "", "description": "Order setting: Set the due date when creating the orders", "type": "string", "required": 0, "values": []}, "param_delivery_date": {"name": "sourcemeta_apifact_evolution", "key": "param_delivery_date", "default": "", "description": "Order setting: Set the delivery date when creating the orders, must be iso 8601 date format", "type": "string", "required": 0, "values": []}, "param_currency_code": {"name": "sourcemeta_apifact_evolution", "key": "param_currency_code", "default": "", "description": "Order setting: Set the currency code when creating the orders in Evolution (USD,EUR etc)", "type": "string", "required": 0, "values": []}, "param_use_channel_order_code": {"name": "sourcemeta_apifact_evolution", "key": "param_use_channel_order_code", "default": "false", "description": "Order setting: Create source order with same code as channel", "type": "string", "required": 0, "values": []}, "param_time_zone": {"name": "sourcemeta_apifact_evolution", "key": "param_time_zone", "default": "", "description": "Specify the timezone orders should use when the are raised in ERP. e.g. Africa/Johannesburg (https://en.wikipedia.org/wiki/List_of_tz_database_time_zones#List)", "type": "string", "required": 0, "values": []}, "param_ignore_shipping": {"name": "sourcemeta_apifact_evolution", "key": "param_ignore_shipping", "default": "false", "description": "Order setting: If set to true shipping lines will be ignored on the order", "type": "string", "required": 0, "values": []}, "param_user_field_order_[x]": {"name": "sourcemeta_apifact_evolution", "key": "param_user_field_order_[x]", "default": "", "description": "Order setting: Set a custom field on Evolution order, e.g. param_user_field_order_ucIDSOrdSpecialInstructions", "type": "string", "required": 0, "values": []}, "param_use_customer_address": {"name": "sourcemeta_apifact_evolution", "key": "param_use_customer_address", "default": "false", "description": "Use address from source", "type": "string", "required": 0, "values": []}, "param_process_reserved": {"name": "sourcemeta_apifact_evolution", "key": "param_process_reserved", "default": "false", "description": "Order setting: To reserve qty on Sales order. The order has to be saved and not processed to reserve stock and can only be applied to Sales Orders", "type": "string", "required": 0, "values": []}}, "apifact_iqretail": {"param_iq_company_number": {"name": "sourcemeta_apifact_iqretail", "key": "param_iq_company_number", "default": "", "description": "The company to post the orders to", "type": "string", "required": 1, "values": []}, "param_iq_terminal_number": {"name": "sourcemeta_apifact_iqretail", "key": "param_iq_terminal_number", "default": "", "description": "The terminal to post the orders to", "type": "string", "required": 1, "values": []}, "param_iq_user_number": {"name": "sourcemeta_apifact_iqretail", "key": "param_iq_user_number", "default": "", "description": "The iq user number", "type": "string", "required": 1, "values": []}, "param_iq_user_password": {"name": "sourcemeta_apifact_iqretail", "key": "param_iq_user_password", "default": "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "description": "The iq user api password (Generated using IQ API Test tool - IQEntApiTest.exe)", "type": "string", "required": 1, "values": []}, "param_sales_representative_number": {"name": "sourcemeta_apifact_iqretail", "key": "param_sales_representative_number", "default": "0", "description": "The sales rep for the orders", "type": "string", "required": 1, "values": []}, "create_order": {"name": "sourcemeta_apifact_iqretail", "key": "create_order", "default": "/iqretail/createOrder", "description": "", "type": "string", "required": 1, "values": []}, "get_product": {"name": "sourcemeta_apifact_iqretail", "key": "get_product", "default": "/iqretail/getProduct", "description": "", "type": "string", "required": 1, "values": []}, "get_order": {"name": "sourcemeta_apifact_iqretail", "key": "get_order", "default": "/iqretail/getOrder", "description": "", "type": "string", "required": 1, "values": []}, "get_products": {"name": "sourcemeta_apifact_iqretail", "key": "get_products", "default": "500", "description": "", "type": "string", "required": 1, "values": []}, "param_document_type": {"name": "sourcemeta_apifact_iqretail", "key": "param_document_type", "default": "Sales_Order", "description": "Set the document type when raising an order", "type": "dropdown", "required": 0, "values": []}, "dll_path": {"name": "sourcemeta_apifact_iqretail", "key": "dll_path", "default": "", "description": "Path to IQ Retail API - e.g. C:\\IQRetail\\IQEnterprise\\IQEntAPI.dll", "type": "string", "required": 0, "values": []}, "param_cashier_number": {"name": "sourcemeta_apifact_iqretail", "key": "param_cashier_number", "default": "", "description": "", "type": "string", "required": 0, "values": []}, "param_currency": {"name": "sourcemeta_apifact_iqretail", "key": "param_currency", "default": "", "description": "", "type": "string", "required": 0, "values": []}, "param_document_terms": {"name": "sourcemeta_apifact_iqretail", "key": "param_document_terms", "default": "", "description": "", "type": "string", "required": 0, "values": []}, "param_delivery_method": {"name": "sourcemeta_apifact_iqretail", "key": "param_delivery_method", "default": "", "description": "", "type": "string", "required": 0, "values": []}, "param_delivery_note_number": {"name": "sourcemeta_apifact_iqretail", "key": "param_delivery_note_number", "default": "", "description": "", "type": "string", "required": 0, "values": []}, "param_till_number": {"name": "sourcemeta_apifact_iqretail", "key": "param_till_number", "default": "", "description": "", "type": "string", "required": 0, "values": []}, "param_document_includes_vat": {"name": "sourcemeta_apifact_iqretail", "key": "param_document_includes_vat", "default": "", "description": "", "type": "string", "required": 0, "values": []}, "param_internal_order_number": {"name": "sourcemeta_apifact_iqretail", "key": "param_internal_order_number", "default": "", "description": "", "type": "string", "required": 0, "values": []}}, "apifact_navision": {"placeholder": {"name": "sourcemeta_apifact_navision", "key": "placeholder", "default": "", "description": "Placeholder for allowed types, do not use", "type": "string", "required": 0, "values": []}}, "apifact_odbc": {"create_order": {"name": "sourcemeta_apifact_odbc", "key": "create_order", "default": "/odbc/createOrder", "description": "", "type": "string", "required": 1, "values": []}, "get_order": {"name": "sourcemeta_apifact_odbc", "key": "get_order", "default": "/odbc/getOrder", "description": "", "type": "string", "required": 1, "values": []}, "get_product": {"name": "sourcemeta_apifact_odbc", "key": "get_product", "default": "/odbc/getProduct", "description": "", "type": "string", "required": 1, "values": []}, "get_products": {"name": "sourcemeta_apifact_odbc", "key": "get_products", "default": "/odbc/getProducts", "description": "", "type": "string", "required": 1, "values": []}, "get_products_limit": {"name": "sourcemeta_apifact_odbc", "key": "get_products_limit", "default": "500", "description": "", "type": "string", "required": 1, "values": []}}, "apifact_omniaccounts": {"placeholder": {"name": "sourcemeta_apifact_omniaccounts", "key": "placeholder", "default": "", "description": "Placeholder for allowed types, do not use", "type": "string", "required": 0, "values": []}, "param_line_item_discount": {"name": "sourcemeta_apifact_omniaccounts", "key": "param_line_item_discount", "default": "false", "description": "Order setting: When active then any order with a total discount will have this discount divided across all the line items excluding price (shipping lines are not included)", "type": "boolean", "required": 0, "values": []}, "param_default_tax_code": {"name": "sourcemeta_apifact_omniaccounts", "key": "param_default_tax_code", "default": "0", "description": "Order setting: Default tax code to use for line items with tax line code 'taxed' (Remove to use tax codes already set on customer in Omni)", "type": "string", "required": 0, "values": []}, "param_rep_code": {"name": "sourcemeta_apifact_omniaccounts", "key": "param_rep_code", "default": "", "description": "Order setting: Set the sales representative(name) when creating order", "type": "string", "required": 0, "values": []}, "param_default_tax_code_exempt": {"name": "sourcemeta_apifact_omniaccounts", "key": "param_default_tax_code_exempt", "default": "0", "description": "Order setting: Default tax code to use for line items with tax line code 'exempt' (Remove to use tax codes already set on customer in Omni)", "type": "string", "required": 0, "values": []}, "param_default_tax_code_shipping": {"name": "sourcemeta_apifact_omniaccounts", "key": "param_default_tax_code_shipping", "default": "0", "description": "Order setting:Default tax code to use for shipping lines with tax line code 'taxed' (Remove to use tax codes already set on customer in Omni)", "type": "string", "required": 0, "values": []}, "create_order": {"name": "sourcemeta_apifact_omniaccounts", "key": "create_order", "default": "/omniaccounts/createOrder", "description": "", "type": "string", "required": 1, "values": []}, "get_order": {"name": "sourcemeta_apifact_omniaccounts", "key": "get_order", "default": "/omniaccounts/getOrder", "description": "", "type": "string", "required": 1, "values": []}, "get_product": {"name": "sourcemeta_apifact_omniaccounts", "key": "get_product", "default": "/omniaccounts/getProduct", "description": "", "type": "string", "required": 1, "values": []}, "get_products": {"name": "sourcemeta_apifact_omniaccounts", "key": "get_products", "default": "/omniaccounts/getProducts", "description": "", "type": "string", "required": 1, "values": []}, "get_products_limit": {"name": "sourcemeta_apifact_omniaccounts", "key": "get_products_limit", "default": "500", "description": "", "type": "string", "required": 1, "values": []}, "param_warehouse_code": {"name": "sourcemeta_apifact_omniaccounts", "key": "param_warehouse_code", "default": "x", "description": "Order setting: Raise the order into this Warehouse (ID) when creating orders", "type": "string", "required": 1, "values": []}}, "apifact_partner": {"param_use_customer_payment_terms": {"name": "sourcemeta_apifact_partner", "key": "param_use_customer_payment_terms", "default": "false", "description": "Retrieves the payment terms (in months) of the customer on an order, to be then used to set the payment due date. By default the due date will be the last day of the month that the terms extend to. e.g. If creation date is 04/22 and the terms are 30 days, the due date will be set as 05/31", "type": "string", "required": 0, "values": []}, "param_create_customer_enabled": {"name": "sourcemeta_apifact_partner", "key": "param_create_customer_enabled", "default": "false", "description": "Create customers if source customer code does not exist in Partner", "type": "dropdown", "required": 0, "values": []}, "param_tax_type": {"name": "sourcemeta_apifact_partner", "key": "param_tax_type", "default": "01", "description": "Pastel Partner tax type to be used for orders marked as taxed", "type": "string", "required": 0, "values": []}, "param_multi_store": {"name": "sourcemeta_apifact_partner", "key": "param_multi_store", "default": "", "description": "Query or write to this store (001 if multi store module is not used)", "type": "string", "required": 0, "values": []}, "param_order_document_type": {"name": "sourcemeta_apifact_partner", "key": "param_order_document_type", "default": "Sales Order", "description": "Values can be: \"Sales Order\" | \"Quotation\" | \"Invoice\"", "type": "dropdown", "required": 1, "values": []}, "param_display_order_inc": {"name": "sourcemeta_apifact_partner", "key": "param_display_order_inc", "default": "false", "description": "if \"true\" invoice will display including (Will blank prices on Order in Pastel - Do not use)", "type": "string", "required": 0, "values": []}, "param_tax_type_exempt": {"name": "sourcemeta_apifact_partner", "key": "param_tax_type_exempt", "default": "", "description": "Pastel Partner tax type to be used for line items marked as exempt", "type": "string", "required": 0, "values": []}, "param_order_user_id": {"name": "sourcemeta_apifact_partner", "key": "param_order_user_id", "default": "-1", "description": "The user ID that will be used to process orders to Partner. -1 will default to the admin user", "type": "string", "required": 0, "values": []}, "param_use_customer_address": {"name": "sourcemeta_apifact_partner", "key": "param_use_customer_address", "default": "false", "description": "Use address from source", "type": "string", "required": 0, "values": []}, "param_ignore_due_date": {"name": "sourcemeta_apifact_partner", "key": "param_ignore_due_date", "default": "true", "description": "When set, Stock2Shop will not pass the date to Pastel as \"Closing date\"", "type": "string", "required": 0, "values": []}, "create_order": {"name": "sourcemeta_apifact_partner", "key": "create_order", "default": "/partner/createOrders", "description": "", "type": "string", "required": 0, "values": []}, "get_order": {"name": "sourcemeta_apifact_partner", "key": "get_order", "default": "/partner/getOrder", "description": "", "type": "string", "required": 0, "values": []}, "get_product": {"name": "sourcemeta_apifact_partner", "key": "get_product", "default": "/partner/getProduct", "description": "", "type": "string", "required": 0, "values": []}, "get_products": {"name": "sourcemeta_apifact_partner", "key": "get_products", "default": "/partner/getProducts", "description": "", "type": "string", "required": 0, "values": []}, "param_use_customer_currency": {"name": "sourcemeta_apifact_partner", "key": "param_use_customer_currency", "default": "false", "description": "Set to true to use customers currency from Pastel when raising an order", "type": "string", "required": 0, "values": []}, "param_exchange_rate": {"name": "sourcemeta_apifact_partner", "key": "param_exchange_rate", "default": "", "description": "Set the exchange rate (numeric value) when raising an order", "type": "string", "required": 0, "values": []}}, "apifact_sage300": {"param_location_code": {"name": "sourcemeta_apifact_sage300", "key": "param_location_code", "default": "", "description": "Location id of warehouse", "type": "string", "required": 0, "values": []}, "param_shipping_code": {"name": "sourcemeta_apifact_sage300", "key": "param_shipping_code", "default": "", "description": "Code used to pass shipping information as a line item on order (service item in Sage300)", "type": "string", "required": 0, "values": []}, "param_default_customer_code": {"name": "sourcemeta_apifact_sage300", "key": "param_default_customer_code", "default": "", "description": "Default customer code to send orders to", "type": "string", "required": 0, "values": []}, "param_username": {"name": "sourcemeta_apifact_sage300", "key": "param_username", "default": "", "description": "Username for raising orders through the API", "type": "string", "required": 0, "values": []}, "param_password": {"name": "sourcemeta_apifact_sage300", "key": "param_password", "default": "", "description": "Password to raise orders through the API", "type": "string", "required": 0, "values": []}}, "apifact_sapone": {"param_user_field_line_[x]": {"name": "sourcemeta_apifact_sapone", "key": "param_user_field_line_[x]", "default": "", "description": "Order setting: Set a custom field on SAPone order, e.g. param_user_field_line_U_OcrCode. This is applied on line items", "type": "string", "required": 0, "values": []}, "param_user_field_order_[x]": {"name": "sourcemeta_apifact_sapone", "key": "param_user_field_order_[x]", "default": "", "description": "Order setting:m_usSet a custom field on SAPone order, e.g. param_user_field_order_SpecialInstructions. This is applied on the order", "type": "string", "required": 0, "values": []}, "param_contact_source_customer": {"name": "sourcemeta_apifact_sapone", "key": "param_contact_source_customer", "default": "false", "description": "If \"true\" then source_customer_code is made up of card code and contact. i,e. customers are contacts from SAP. When order is raised, this will be raised on the the card code", "type": "string", "required": 0, "values": []}, "param_card_name": {"name": "sourcemeta_apifact_sapone", "key": "param_card_name", "default": "", "description": "Order setting: Add value to card name on order", "type": "string", "required": 0, "values": []}, "param_use_product_warehouse_code": {"name": "sourcemeta_apifact_sapone", "key": "param_use_product_warehouse_code", "default": "false", "description": "Order setting: add warehouse code based on default warehouse code of product", "type": "string", "required": 0, "values": []}, "param_warehouse_code": {"name": "sourcemeta_apifact_sapone", "key": "param_warehouse_code", "default": "", "description": "Order setting: Raise the order into this Warehouse (ID) when creating orders", "type": "string", "required": 1, "values": []}, "param_object_code": {"name": "sourcemeta_apifact_sapone", "key": "param_object_code", "default": "", "description": "", "type": "string", "required": 0, "values": []}, "param_limit": {"name": "sourcemeta_apifact_sapone", "key": "param_limit", "default": "", "description": "", "type": "string", "required": 0, "values": []}, "param_order_document_type": {"name": "sourcemeta_apifact_sapone", "key": "param_order_document_type", "default": "oOrders", "description": "\"oInvoices\" = Invoices ; \"oQuotations\" = Quotations ; \"oOrders\" = Sales Orders", "type": "string", "required": 1, "values": []}, "param_customer_reference": {"name": "sourcemeta_apifact_sapone", "key": "param_customer_reference", "default": "", "description": "", "type": "string", "required": 0, "values": []}, "param_currency": {"name": "sourcemeta_apifact_sapone", "key": "param_currency", "default": "USD", "description": "Sets a 3 character string that specifies the currency used in this document.", "type": "string", "required": 0, "values": []}, "param_address": {"name": "sourcemeta_apifact_sapone", "key": "param_address", "default": "", "description": "Populate \"Bill to\" field in SAPONE: eg. {{system_order.billing_address.address1}}, {{system_order.billing_address.address2}}, {{system_order.billing_address.city}}", "type": "string", "required": 0, "values": []}, "param_address2": {"name": "sourcemeta_apifact_sapone", "key": "param_address2", "default": "", "description": "Populate \"Ship to\" field in SAPONE: eg. {{system_order.shipping_address.address1}}, {{system_order.shipping_address.address2}}, {{system_order.shipping_address.city}}", "type": "string", "required": 0, "values": []}, "param_ignore_shipping": {"name": "sourcemeta_apifact_sapone", "key": "param_ignore_shipping", "default": "false", "description": "Order setting: If set to true shipping lines will be ignored on the order", "type": "string", "required": 0, "values": []}}, "apifact_syspro": {"param_warehouse_code": {"name": "sourcemeta_apifact_syspro", "key": "param_warehouse_code", "default": "", "description": "Order setting: Raise the order into this Warehouse (ID) when creating orders", "type": "string", "required": 1, "values": []}, "param_company_id": {"name": "sourcemeta_apifact_syspro", "key": "param_company_id", "default": "", "description": "Order setting: Company to raise orders into", "type": "string", "required": 1, "values": []}, "param_sales_rep_id": {"name": "sourcemeta_apifact_syspro", "key": "param_sales_rep_id", "default": "", "description": "Order setting: Set the sales rep on the order", "type": "string", "required": 0, "values": []}, "param_order_type": {"name": "sourcemeta_apifact_syspro", "key": "param_order_type", "default": "WO", "description": "Order setting: Set on order header, see Sypro example order (https://www.stock2shop.com/documentation/getting-started/syspro-installation/syspro-orders-via-enet-webservice)", "type": "string", "required": 0, "values": []}, "param_company_password": {"name": "sourcemeta_apifact_syspro", "key": "param_company_password", "default": "xxx", "description": "Order setting: Company password", "type": "string", "required": 0, "values": []}, "param_default_tax_code": {"name": "sourcemeta_apifact_syspro", "key": "param_default_tax_code", "default": "", "description": "Order setting: Default tax code to use in the orders for line items with tax line code 'taxed'", "type": "string", "required": 0, "values": []}, "param_default_tax_code_exempt": {"name": "sourcemeta_apifact_syspro", "key": "param_default_tax_code_exempt", "default": "", "description": "Order setting: Default tax code to use in the orders for line items with tax line code 'taxed'", "type": "string", "required": 0, "values": []}, "param_currency": {"name": "sourcemeta_apifact_syspro", "key": "param_currency", "default": "", "description": "Order setting: Set the currency code when creating the orders", "type": "string", "required": 0, "values": []}, "param_operator": {"name": "sourcemeta_apifact_syspro", "key": "param_operator", "default": "", "description": "Order setting: eNet service operator for logging in", "type": "string", "required": 1, "values": []}, "param_operator_password": {"name": "sourcemeta_apifact_syspro", "key": "param_operator_password", "default": "", "description": "Order setting:  eNet service operator password for logging in", "type": "string", "required": 1, "values": []}}, "debug": {"sync_mode": {"name": "sourcemeta_debug", "key": "sync_mode", "default": "pull", "description": "push or pull", "type": "string", "required": 1, "values": []}, "get_images_limit": {"name": "sourcemeta_debug", "key": "get_images_limit", "default": "1", "description": "Number of images to fetch at a time", "type": "string", "required": 1, "values": []}, "get_products_limit": {"name": "sourcemeta_debug", "key": "get_products_limit", "default": "1", "description": "Number of products to fetch at a time", "type": "string", "required": 1, "values": []}, "queue_fetch_images": {"name": "sourcemeta_debug", "key": "queue_fetch_images", "default": "false", "description": "Queue fetch_images item after processing sync_product", "type": "string", "required": 0, "values": []}, "stubs": {"name": "sourcemeta_debug", "key": "stubs", "default": "", "description": "The set of stubs to use", "type": "string", "required": 0, "values": []}}, "dolfin": {"shipping_code": {"name": "sourcemeta_dolfin", "key": "shipping_code", "default": "ABC", "description": "Shipping code used as line item on order. <PERSON><PERSON><PERSON> must have this sku setup already.", "type": "string", "required": 0, "values": []}, "product_request_map (v2)": {"name": "sourcemeta_dolfin", "key": "product_request_map (v2)", "default": "<soap:Envelope xmlns:soap=\"http://www.w3.org/2003/05/soap-envelope\" xmlns:tem=\"http://tempuri.org/\">     <soap:Header xmlns:wsa=\"http://www.w3.org/2005/08/addressing\">         <wsse:Security xmlns:wsse=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd\" xmlns:wsu=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd\">             <wsse:UsernameToken>                 <wsse:Username>STOCK2SHOP</wsse:Username>                 <wsse:Password Type=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText\">cZg4@kyapy@tq4Vw</wsse:Password>             </wsse:UsernameToken>         </wsse:Security>         <wsa:Action>http://tempuri.org/IEnquiryInterface/ProcessXML</wsa:Action>         <wsa:To>https://ffnsts.mychain.co.za/Argility.DolfinInterface.Webservices.FFN/EnquiryInterface.svc</wsa:To>     </soap:Header>     <soap:Body>         <tem:ProcessXML>             <tem:query>                 <Process>                     <TranHdr>                         <CompanyID>FFN</CompanyID>                         <StoreNumber>9007</StoreNumber>                         <UserName>STOCK2SHOP</UserName>                         <TranDate>{{date}}</TranDate>                         <TranTime>{{time}}</TranTime>                         <TranGUID>229EDCC7-9DDC-4CF6-A0AC-E977212E010F</TranGUID>                     </TranHdr>                     <DataRequest>                         <RequestType>S2S_Web_Products_v2</RequestType>                         <RequestDetails>                             <FromDateTime>{{sync_token}}</FromDateTime>                             <Rows>{{limit}}</Rows>                             <ProductList></ProductList>                             <WebEnabled>1</WebEnabled>                             <FinCompany>3</FinCompany>                         </RequestDetails>                     </DataRequest>                 </Process>             </tem:query>         </tem:ProcessXML>     </soap:Body> </soap:Envelope>", "description": "Example v2 SOAP Url endpoint used to request products. This version returns all financial companies associated with a product. Financial company can then be mapped as an meta for the purpose of creating channel rules, etc. Note: this also affects how the store array is structured for qty_availabilty and requires that the financial company also be called on the product map.", "type": "string", "required": 0, "values": []}, "product_map (v2)": {"name": "sourcemeta_dolfin", "key": "product_map (v2)", "default": "{     \"source\": {         \"source_product_code\": \"{{Prod}}\",         \"product_active\": \"{{# SellOnWeb}}true{{/ SellOnWeb}}{{^ SellOnWeb}}false{{/ SellOnWeb}}\",         \"sync_token\": \"{{AmendedOn}}\"     },     \"product\": {         \"title\": \"{{# WebName}}{{#json_escape}}{{WebName}}{{/json_escape}}{{/ WebName}}{{^ WebName}}Product Title{{/ WebName}}\",         \"vendor\": \"{{#json_escape}}{{Brand}}{{/json_escape}}\",         \"collection\": \"{{# MarkedDown}}SALE{{/ MarkedDown}}{{^ MarkedDown}}{{Category}}{{/ MarkedDown}}\",         \"product_type\": \"{{SubD}}\"                  {{# LifeStyleDescr}},             \"tags\": \"{{#json_escape}}{{LifeStyleDescr}}{{/json_escape}}\"         {{/ LifeStyleDescr}},                  \"body_html\": \"{{#json_escape}}{{WebFullDescr}}{{/json_escape}}\"         {{# Size}},         \"options\": [{             \"name\": \"Size\",             \"position\": \"1\"         }]         {{/ Size}},         \"variants\": {             \"source_variant_code\": \"{{SKU}}\",             \"qty\": \"{{qty.3.9007}}\",             \"price\": \"{{SP}}\",             \"price_tiers\":[             {                 \"tier\": \"PromoPrice\",                 \"price\": \"{{PromoPrice}}\"             },             {                 \"tier\": \"SP\",                 \"price\": \"{{SP}}\"             },             {                 \"tier\": \"SP2\",                 \"price\": \"{{SP2}}\"             },             {                 \"tier\": \"SP3\",                 \"price\": \"{{SP3}}\"             },             {                 \"tier\": \"SP4\",                 \"price\": \"{{SP4}}\"             }                     ],             \"sku\": \"{{SKU}}\"             {{# Size}},                 \"option1\": \"{{Size}}\"             {{/ Size}},             \"inventory_management\": true,             \"qty_availability\": [                 {                     \"description\": \"9007\",                     \"qty\": \"{{qty.3.9007}}\"                 },                 {                     \"description\": \"0176\",                     \"qty\": \"{{qty.1.0176}}\"                 }             ]         },         \"meta\": [{                 \"key\": \"PromoPrice\",                 \"value\": \"{{PromoPrice}}\"             },             {                 \"key\": \"PromoFromDate\",                 \"value\": \"{{PromoFromDate}}\"             },             {                 \"key\": \"PromoEndDate\",                 \"value\": \"{{PromoEndDate}}\"             }             {{# associated_fin}}             ,{                 \"key\": \"FinCompany_{{FinCompany}}\",                 \"value\": \"true\"             }            {{/ associated_fin}}         ]     } }", "description": "Transforms v2 Dolfin data into s2s product. See example for available properties. Carefully note the qty_availability array call syntax. To populate the financial companies associated with the product as meta, a method has been put in place to loop through all financial companies and return a value of true for those that contain SOH. This may be called via the {{# associated_fin}} check. Price value is not numeric, you need to use calculate function on all prices {{# calculate}} {{/ calculate}}", "type": "string", "required": 0, "values": []}, "product_request_map (v4)": {"name": "sourcemeta_dolfin", "key": "product_request_map (v4)", "default": "<soap:Envelope xmlns:soap=\"http://www.w3.org/2003/05/soap-envelope\" xmlns:tem=\"http://tempuri.org/\">     <soap:Header xmlns:wsa=\"http://www.w3.org/2005/08/addressing\">         <wsse:Security xmlns:wsse=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd\" xmlns:wsu=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd\">             <wsse:UsernameToken>                 <wsse:Username>MBDIGITAL</wsse:Username>                 <wsse:Password>xxxxxxxxxxx</wsse:Password>             </wsse:UsernameToken>         </wsse:Security>         <wsa:Action>http://tempuri.org/IEnquiryInterface/ProcessXML</wsa:Action>         <wsa:To>https://clcmbd.mychain.co.za/argility.dolfininterface.webservices.clc/enquiryinterface.svc</wsa:To>     </soap:Header>     <soap:Body>         <tem:ProcessXML>             <tem:query>                 <Process>                     <TranHdr>                         <CompanyID>CLC</CompanyID>                         <StoreNumber>0058</StoreNumber>                         <UserName>MBDIGITAL</UserName>                         <TranDate>{{date}}</TranDate>                         <TranTime>{{time}}</TranTime>                         <TranGUID>229EDCC7-9DDC-4CF6-A0AC-E977212E010F</TranGUID>                     </TranHdr>                     <DataRequest>                         <RequestType>S2S_Web_Products_v4</RequestType>                         <RequestDetails>                             <FromDateTime>{{sync_token}}</FromDateTime>                             <Rows>{{limit}}</Rows>                             <ProductList></ProductList>                             <WebEnabled>1</WebEnabled>                         </RequestDetails>                     </DataRequest>                 </Process>             </tem:query>         </tem:ProcessXML>     </soap:Body> </soap:Envelope>", "description": "Example v4 SOAP Url endpoint used to request products. This version returns all financial companies associated with a product, and includes variant-level pricing. Financial company can then be mapped as an meta for the purpose of creating channel rules, etc. Note: this also affects how the store array is structured for qty_availabilty and pricing, and requires that the financial company also be called on the product map.", "type": "string", "required": 0, "values": []}, "product_map (v4)": {"name": "sourcemeta_dolfin", "key": "product_map (v4)", "default": "{   \"source\": {     \"source_product_code\": \"{{Prod}}\",     \"product_active\": \"{{# SellOnWeb}}true{{/ SellOnWeb}}{{^ SellOnWeb}}false{{/ SellOnWeb}}\",     \"sync_token\": \"{{AmendedOn}}\"   },   \"product\": {     \"title\": \"{{# WebName}}{{#json_escape}}{{WebName}}{{/json_escape}}{{/ WebName}}{{^ WebName}}Product Title{{/ WebName}}\",     \"body_html\": \"{{#json_escape}}{{{WebFullDescr}}}{{/json_escape}}\",     \"collection\": \"{{DepD}}\",     \"product_type\": \"{{SubD}}\",     \"options\": [       {{# Size}}         {           \"name\": \"Size\",           \"position\": \"1\"         }       {{/ Size}}       {{# ColD}}         {{# Size}},{{/ Size}}         {           \"name\": \"Colour\",           {{# Size}}             \"position\": \"2\"           {{/ Size}}           {{^ Size}}             \"position\": \"1\"           {{/ Size}}         }       {{/ ColD}}     ],     \"variants\": {       \"source_variant_code\": \"{{SKU}}\",       \"qty\": \"{{qty.1.0058}}\",       \"price\": \"{{# calculate}}{{price.1.0058.SP}}/1.15{{/ calculate}}\",       \"price_tiers\": [         {           \"tier\": \"PromoPrice\",           \"price\": \"{{# calculate}}{{PromoPrice}}/1.15{{/ calculate}}\"         },         {           \"tier\": \"SP\",           \"price\": \"{{# calculate}}{{price.1.0058.SP}}/1.15{{/ calculate}}\"         },         {           \"tier\": \"SP2\",           \"price\": \"{{# calculate}}{{price.1.0058.SP2}}/1.15{{/ calculate}}\"         },         {           \"tier\": \"SP3\",           \"price\": \"{{# calculate}}{{price.1.0058.SP3}}/1.15{{/ calculate}}\"         },         {           \"tier\": \"SP4\",           \"price\": \"{{# calculate}}{{price.1.0058.SP4}}/1.15{{/ calculate}}\"         }       ],       \"sku\": \"{{SKU}}\",       {{# Size}}\"option1\": \"{{Size}}\",{{/ Size}}       {{# ColD}}         {{# Size}}\"option2\": \"{{ColD}}\",{{/ Size}}{{^ Size}}\"option1\": \"{{ColD}}\",{{/ Size}}       {{/ ColD}}       \"inventory_management\": true,       \"qty_availability\": [         {           \"description\": \"0058\",           \"qty\": \"{{qty.1.0058}}\"         },         {           \"description\": \"0058_qtyAvailable\",           \"qty\": \"{{qty_available.1.0058}}\"         }       ]     },     \"meta\": [       {         \"key\": \"PromoPrice\",         \"value\": \"{{# calculate}}{{PromoPrice}}/1.15{{/ calculate}}\"       },       {         \"key\": \"PromoFromDate\",         \"value\": \"{{PromoFromDate}}\"       },       {         \"key\": \"PromoEndDate\",         \"value\": \"{{PromoEndDate}}\"       }       {{# associated_fin}},         {           \"key\": \"FinCompany_{{FinCompany}}\",           \"value\": \"true\"         }       {{/ associated_fin}}     ]   } }", "description": "Transforms v4 Dolfin data into s2s product. See example for available properties. Carefully note the qty_availability and price list array call syntax. To call available stock instead of SOH, use the syntax of {{qty_available.finCom.store}}, e.g. {{qty_available.1.0058}} This will call a separate array that references the available stock. To populate the financial companies associated with the product as meta, a method has been put in place to loop through all financial companies and return a value of true for those that contain SOH. This may be called via the {{# associated_fin}} check. Price value is not numeric, you need to use calculate function on all prices {{# calculate}} {{/ calculate}}", "type": "string", "required": 0, "values": []}, "queue_fetch_images": {"name": "sourcemeta_dolfin", "key": "queue_fetch_images", "default": "false", "description": "", "type": "string", "required": 1, "values": []}, "sync_mode": {"name": "sourcemeta_dolfin", "key": "sync_mode", "default": "pull", "description": "push or pull", "type": "string", "required": 1, "values": []}, "get_products_limit": {"name": "sourcemeta_dolfin", "key": "get_products_limit", "default": "10", "description": "", "type": "string", "required": 1, "values": []}, "create_order_enabled": {"name": "sourcemeta_dolfin", "key": "create_order_enabled", "default": "false", "description": "", "type": "string", "required": 1, "values": []}, "cron_get_products_schedule": {"name": "sourcemeta_dolfin", "key": "cron_get_products_schedule", "default": "x 00 * * * *", "description": "Basic format is \"MM HH * * *\"", "type": "string", "required": 1, "values": []}, "product_request_url": {"name": "sourcemeta_dolfin", "key": "product_request_url", "default": "https://trpsksuatlab.mychain.co.za:8888/DolfinInterface.Webservices.TRP/EnquiryInterface.svc", "description": "SOAP Url endpoint used to request products", "type": "string", "required": 1, "values": []}, "product_request_action": {"name": "sourcemeta_dolfin", "key": "product_request_action", "default": "http://tempuri.org/IDolfinMessagingInterface/SourceEnquiry", "description": "SOAP Url action used to request products", "type": "string", "required": 1, "values": []}, "product_request_map": {"name": "sourcemeta_dolfin", "key": "product_request_map", "default": "<soap:Envelope xmlns:soap=\"http://www.w3.org/2003/05/soap-envelope\" xmlns:tem=\"http://tempuri.org/\">\n            <soap:Header xmlns:wsa=\"http://www.w3.org/2005/08/addressing\">\n                <wsse:Security xmlns:wsse=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd\" xmlns:wsu=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd\">\n                    <wsse:UsernameToken>\n                        <wsse:Username>STOCK2SHOP</wsse:Username>\n                        <wsse:Password>xxx</wsse:Password>\n                    </wsse:UsernameToken>\n                </wsse:Security>\n                <wsa:Action>http://tempuri.org/IEnquiryInterface/ProcessXML</wsa:Action>\n                <wsa:To>https://trpsksuatlab.mychain.co.za:8888/DolfinInterface.Webservices.TRP/EnquiryInterface.svc</wsa:To>\n            </soap:Header>\n            <soap:Body>\n                <tem:ProcessXML>\n                    <tem:query>\n                        <Process>\n                          <TranHdr>\n                            <CompanyID>TRP</CompanyID>\n                            <StoreNumber>0001</StoreNumber>\n                            <UserName>Stock2Shop</UserName>\n                            <TranDate>{{date}}</TranDate>\n                            <TranTime>{{time}}</TranTime>\n                            <TranGUID>229EDCC7-9DDC-4CF6-A0AC-E977212E010F</TranGUID>\n                          </TranHdr>\n                          <DataRequest>\n                            <RequestType>S2S_Web_Products</RequestType>\n                            <RequestDetails>\n                              <FromDateTime>{{sync_token}}</FromDateTime>\n                              <Rows>{{limit}}</Rows>\n                              <ProductList></ProductList>\n                            </RequestDetails>\n                          </DataRequest>\n                        </Process>\n                    </tem:query>\n                </tem:ProcessXML>\n            </soap:Body>\n        </soap:Envelope>", "description": "SOAP Url endpoint used to request products", "type": "string", "required": 1, "values": []}, "product_map": {"name": "sourcemeta_dolfin", "key": "product_map", "default": "{  \"source\": {    \"source_product_code\": \"{{Prod}}\",    \"product_active\": \"{{# SellOnWeb}}true{{/ SellOnWeb}}{{^ SellOnWeb}}false{{/ SellOnWeb}}\",    \"sync_token\": \"{{AmendedOn}}\"  },  \"product\": {    \"title\": \"{{# WebName}}{{#json_escape}}{{WebName}}{{/json_escape}}{{/ WebName}}{{^ WebName}}Product Title{{/ WebName}}\",    \"body_html\": \"{{#json_escape}}{{{WebFullDescr}}}{{/json_escape}}\",    \"collection\": \"{{DepD}}\",    \"product_type\": \"{{SubD}}\",   \"options\":[\t\t    {{# Size}}                {                    \"name\": \"Size\",                    \"position\": \"1\"                }            {{/ Size}}\t\t\t {{# ColD}}{{# Size}},{{/ Size}}\t\t\t   {                    \"name\": \"Colour\",\t\t\t\t\t{{# Size}}  \"position\": \"2\"{{/ Size}}{{^ Size}}\"position\": \"1\"{{/ Size}}                }\t\t\t  {{/ ColD}}              ],    \"variants\": {      \"source_variant_code\": \"{{SKU}}\",      \"qty\": \"{{qty.1.0058}}\",      \"price\": \"{{# calculate}}{{price.1.0058.SP}}/1.15{{/ calculate}}\",      \"price_tiers\": [{        \"tier\": \"PromoPrice\",        \"price\": \"{{# calculate}}{{PromoPrice}}/1.15{{/ calculate}}\"      },        {          \"tier\": \"SP\",          \"price\": \"{{# calculate}}{{price.1.0058.SP}}/1.15{{/ calculate}}\"        },        {          \"tier\": \"SP2\",          \"price\": \"{{# calculate}}{{price.1.0058.SP2}}/1.15{{/ calculate}}\"        },        {          \"tier\": \"SP3\",          \"price\": \"{{# calculate}}{{price.1.0058.SP3}}/1.15{{/ calculate}}\"        },        {          \"tier\": \"SP4\",          \"price\": \"{{# calculate}}{{price.1.0058.SP4}}/1.15{{/ calculate}}\"        }      ],      \"sku\": \"{{SKU}}\",     {{# Size}}         \t  \"option1\" : \"{{Size}}\",            {{/ Size}}\t\t\t {{# ColD}}\t\t\t\t\t  {{# Size}}  \"option2\": \"{{ColD}}\", {{/ Size}}{{^ Size}}\"option1\": \"{{ColD}}\",{{/ Size}}\t\t\t  {{/ ColD}}      \"inventory_management\": true,      \"qty_availability\": [{        \"description\": \"0058\",        \"qty\": \"{{qty.1.0058}}\"      }]    },    \"meta\": [{      \"key\": \"PromoPrice\",      \"value\": \"{{# calculate}}{{PromoPrice}}/1.15{{/ calculate}}\"    },      {        \"key\": \"PromoFromDate\",        \"value\": \"{{PromoFromDate}}\"      },      {        \"key\": \"PromoEndDate\",        \"value\": \"{{PromoEndDate}}\"      }      {{# associated_fin}},        {          \"key\": \"FinCompany_{{FinCompany}}\",          \"value\": \"true\"        }      {{/ associated_fin}}    ]  }}", "description": "Transforms Dolfin data into s2s product. See example for available properties. Confirm fields against XML feed from SOAP request. Price value is not numeric, you need to use calculate function on all prices {{# calculate}} {{/ calculate}}", "type": "string", "required": 1, "values": []}, "order_source_order_code_map": {"name": "sourcemeta_dolfin", "key": "order_source_order_code_map", "default": "{{system_order.id}}", "description": "The source order code to return, uses template to render results", "type": "string", "required": 1, "values": []}, "order_source_customer_code_map": {"name": "sourcemeta_dolfin", "key": "order_source_customer_code_map", "default": "099", "description": "The source customer code to return, can use template usually hard coded, i.e. all orders into one account", "type": "string", "required": 1, "values": []}, "order_process_url": {"name": "sourcemeta_dolfin", "key": "order_process_url", "default": "https://trpsksuatlab.mychain.co.za:8888/DolfinInterface.Webservices.TRP/DolfinMessagingInterface.svc", "description": "SOAP Url endpoint used to process orders", "type": "string", "required": 1, "values": []}, "order_process_action": {"name": "sourcemeta_dolfin", "key": "order_process_action", "default": "http://tempuri.org/IDolfinMessagingInterface/ProcessTransaction", "description": "SOAP Url action used to process order", "type": "string", "required": 1, "values": []}, "order_process_map": {"name": "sourcemeta_dolfin", "key": "order_process_map", "default": "<soap:Envelope xmlns:soap=\"http://www.w3.org/2003/05/soap-envelope\" xmlns:tem=\"http://tempuri.org/\">            <soap:Header xmlns:wsa=\"http://www.w3.org/2005/08/addressing\">                <wsse:Security xmlns:wsse=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd\" xmlns:wsu=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd\">                    <wsse:UsernameToken>                        <wsse:Username>STOCK2SHOP</wsse:Username>                        <wsse:Password>xxx</wsse:Password>                    </wsse:UsernameToken>                </wsse:Security>                <wsa:Action>http://tempuri.org/IDolfinMessagingInterface/ActionTransaction</wsa:Action>                <wsa:To>https://trpsksuatlab.mychain.co.za:8888/DolfinInterface.Webservices.TRP/DolfinMessagingInterface.svc</wsa:To>            </soap:Header>            <soap:Body>                <tem:ActionTransaction>                    <tem:companyId>TRP</tem:companyId>                    <tem:source>DolfinTranSourceWS</tem:source>                    <tem:xmlDoc><![CDATA[                        <CashSale>                          <Hdr>                            <TranHdr>                              <StoreNumber>0003</StoreNumber>                              <Terminal>6</Terminal>                              <TransactionNumber>{{system_order.id}}</TransactionNumber>                              <UserName>Stock2Shop</UserName>                              <OnlineAuthorisationUserName />                              <TranDate>{{params.date}}</TranDate>                              <TranTime>{{params.time}}</TranTime>                            </TranHdr>                            <TransactionType>CS</TransactionType>                            <CashierID>0015</CashierID>                            <EmployeeNumber />                            <LoyaltyCustomerNo />                            <DemographicHdr/>                            <RefundNote />                            <PostalCode />                            <SchoolClub />                            <MySchool />                            <DiscoveryMemNum />                            <DiscAuthCode />                            <ReasonName />                            <ReasonTel />                            <ReasonInfoID />                            <ReasonInfoVoucherNum />                            <ReasonInfoAdd1 />                            <ReasonInfoAdd2 />                            <ReasonInfoAdd3 />                            <ReasonInfoAdd4 />                            <ReasonInfoAdd5 />                            <OverrideUser />                            <DiscoveryHealthNumber />                            <DiscoveryInputType>0</DiscoveryInputType>                            <CSCustomerCellPhone />                            <CSCustomerEmail />                            <CSCustomerName />                            <CSCustomerSurname />                            <CSCustomerAccountNumber />                            <GiftReceiptNumber/>                          </Hdr>                          <Lines>                            {{# system_order.line_items}}                            <SaleLineData>                              <LineNumber>{{line_number}}</LineNumber>                              <SKUCode>{{sku}}</SKUCode>                              <SaleQty>{{qty}}</SaleQty>                              <SaleLineDirection>Sale</SaleLineDirection>                              <TransactionSubType>NLI</TransactionSubType>                              <NetAmtStoreInc>{{total_inc}}</NetAmtStoreInc>                              <TaxAmtStore>{{total_tax}}</TaxAmtStore>                              <DiscAmtStoreInc>0.00</DiscAmtStoreInc>                              <DiscountType>NA</DiscountType>                              <DiscountReasonCode />                              <ConditionalPromotionID>0</ConditionalPromotionID>                              <UnitPriceStoreInc>{{price_inc}}</UnitPriceStoreInc>                              <PriceOverrideReasonCode />                              <OverridePrice>{{price_inc}}</OverridePrice>                              <AssistantID>0015</AssistantID>                              <HamperSKUCode />                              <Reference1 />                              <Reference2 />                              <Reference3 />                              <Reference4 />                              <Reference5 />                              <ExchangeInd>0</ExchangeInd>                              <CSType>NA</CSType>                              <CSStoreNumber />                              <CSReferenceNumber />                              <TailorCode />                              <AdjustmentValue>0</AdjustmentValue>                              <AdjustmentType>NA</AdjustmentType>                              <ReferenceLineNumber>0</ReferenceLineNumber>                              <ReferenceLineType>NA</ReferenceLineType>                              <DiscountReference />                              <VoidReferenceLine>0</VoidReferenceLine>                              <JavaReferenceLineNumber>0</JavaReferenceLineNumber>                              <AltReferenceLineNumber>0</AltReferenceLineNumber>                              <ConcessionReference />                              <ConditionalPromoBucket />                              <TransactionDiscountAmtStoreInc>0</TransactionDiscountAmtStoreInc>                              <LineDiscountAmtStoreInc>0</LineDiscountAmtStoreInc>                              <GiftVoucher>                                <VoucherNumber />                                <STANNumber />                                <MerchantNumber />                                <IntCurrencyCode />                              </GiftVoucher>                              <QA>                                <QAData>                                  <QuestionID>0</QuestionID>                                  <Answer />                                </QAData>                              </QA>                              <SerialDetails>                                <SerialDetailData>                                  <SerialNumber />                                </SerialDetailData>                              </SerialDetails>                            </SaleLineData>                            {{/ system_order.line_items}}                          </Lines>                          <Discounts />                          <Tenders>                            <TenderData>                              <TenderTypeNo>Cash</TenderTypeNo>                              <TenderSubType>CT</TenderSubType>                              <Reference />                              <TenderDirection>Tender</TenderDirection>                              <TenderCurrency>RANDS</TenderCurrency>                              <TenderAmountCurr>{{system_order.total_inc}}</TenderAmountCurr>                              <TenderAmountStore>{{system_order.total_inc}}</TenderAmountStore>                              <ManualAuthorisationNo />                              <SpeedPointReference />                              <CartridgeNumber />                              <ContactData>                                <Title />                                <FirstName />                                <Surname />                                <TelW />                                <TelH />                                <Mobile />                                <IDNo />                                <Addr1 />                                <Addr2 />                                <Addr3 />                                <PCode />                              </ContactData>                              <Reference2 />                              <STANNumber />                              <MerchantNumber />                              <IntCurrencyCode />                              <EFTReferenceNumber />                              <CardReference />                              <CardType>0</CardType>                            </TenderData>                          </Tenders>                          <Totals>                            <TranTenderTotal>                              <LinesCount>{{system_order.line_count}}</LinesCount>                              <LinesValueInc>{{system_order.total_inc}}</LinesValueInc>                            </TranTenderTotal>                            <TranDetailTotal>                              <LinesCount>{{system_order.line_count}}</LinesCount>                              <LinesQty>{{system_order.qty_count}}</LinesQty>                              <LinesValueInc>{{system_order.total_inc}}</LinesValueInc>                            </TranDetailTotal>                          </Totals>                        </CashSale>]]>                 </tem:xmlDoc>                </tem:ActionTransaction>            </soap:Body>        </soap:Envelope>", "description": "XML SOAP payload for order.", "type": "string", "required": 1, "values": []}, "trading_summary_map": {"name": "sourcemeta_dolfin", "key": "trading_summary_map", "default": "<soap:Envelope xmlns:soap=\"http://www.w3.org/2003/05/soap-envelope\" xmlns:tem=\"http://tempuri.org/\">     <soap:Header xmlns:wsa=\"http://www.w3.org/2005/08/addressing\">         <wsse:Security xmlns:wsse=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd\" xmlns:wsu=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd\">             <wsse:UsernameToken>                 <wsse:Username>STOCK2SHOP</wsse:Username>                 <wsse:Password>xxx</wsse:Password>             </wsse:UsernameToken>         </wsse:Security>         <wsa:Action>http://tempuri.org/IDolfinMessagingInterface/ActionTransaction</wsa:Action>         <wsa:To>https://jamsts.mychain.co.za/Argility.DolfinInterface.Webservices.JAM/DolfinMessagingInterface.svc</wsa:To>     </soap:Header>     <soap:Body>         <tem:ActionTransaction>             <tem:companyId>JAM</tem:companyId>             <tem:source>DolfinTranSourceWS</tem:source>             <tem:xmlDoc>                 <![CDATA[                 <StoreDailyTradingSummary>                 <Hdr>                     <TranHdr>                         <StoreNumber>9161</StoreNumber>                         <Terminal>0</Terminal>                         <TransactionNumber>{{transaction_number}}</TransactionNumber>                         <UserName>s2s</UserName>                         <OnlineAuthorisationUserName/>                         <TranDate>{{created_date}}</TranDate>                         <TranTime>{{created_time}}</TranTime>                     </TranHdr>                 </Hdr>                 <Lines>                     <StoreDailyTradingSummaryLineData>                         <TranType>CashSale</TranType>                         <TranCount>{{order_count}}</TranCount>                         <TranValue>{{total}}</TranValue>                     </StoreDailyTradingSummaryLineData>                 </Lines>                 <Totals>                     <TranTenderTotal>                         <LinesCount>{{line_count}}</LinesCount>                         <LinesValueInc>{{total}}</LinesValueInc>                     </TranTenderTotal>                     <TranDetailTotal>                         <LinesCount>{{line_count}}</LinesCount>                         <LinesQty>{{qty_count}}</LinesQty>                         <LinesValueInc>{{total}}</LinesValueInc>                     </TranDetailTotal>                 </Totals>             </StoreDailyTradingSummary>                 ]]>             </tem:xmlDoc>         </tem:ActionTransaction>     </soap:Body> </soap:Envelope>", "description": "summary which is posted after each order to count the days transactions. note the example data structure", "type": "string", "required": 0, "values": []}, "checkpoint_map": {"name": "sourcemeta_dolfin", "key": "checkpoint_map", "default": "<soap:Envelope xmlns:soap=\"http://www.w3.org/2003/05/soap-envelope\" xmlns:tem=\"http://tempuri.org/\">     <soap:Header xmlns:wsa=\"http://www.w3.org/2005/08/addressing\">         <wsse:Security xmlns:wsse=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd\" xmlns:wsu=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd\">             <wsse:UsernameToken>                 <wsse:Username>STOCK2SHOP</wsse:Username>                 <wsse:Password>xxx</wsse:Password>             </wsse:UsernameToken>         </wsse:Security>         <wsa:Action>http://tempuri.org/IDolfinMessagingInterface/ActionTransaction</wsa:Action>         <wsa:To>https://jamstsuatlab.mychain.co.za:8888/DolfinInterface.Webservices.JAM/DolfinMessagingInterface.svc</wsa:To>     </soap:Header>     <soap:Body>         <tem:ActionTransaction>             <tem:companyId>JAM</tem:companyId>             <tem:source>DolfinTranSourceWS</tem:source>             <tem:xmlDoc>                 <![CDATA[                 <CheckPoint>   <Hdr>     <TranHdr>       <StoreNumber>9161</StoreNumber>       <Terminal>0</Terminal>       <TransactionNumber>{{params.jam_order_no}}</TransactionNumber>       <UserName>s2s</UserName>       <OnlineAuthorisationUserName/>       <TranDate>{{params.created_date}}</TranDate>       <TranTime>{{params.created_time}}</TranTime>     </TranHdr>   </Hdr>   <Lines>     <CheckPointLineData>       <TranType>CashSale</TranType>       <Terminal>6</Terminal>       <TransactionNumber>{{params.jam_order_no}}</TransactionNumber>     </CheckPointLineData>   </Lines>   <Totals>     <TranTenderTotal>       <LinesCount>1</LinesCount>       <LinesValueInc>0</LinesValueInc>     </TranTenderTotal>     <TranDetailTotal>       <LinesCount>0</LinesCount>       <LinesQty>0</LinesQty>       <LinesValueInc>0</LinesValueInc>     </TranDetailTotal>   </Totals> </CheckPoint>                 ]]>             </tem:xmlDoc>         </tem:ActionTransaction>     </soap:Body> </soap:Envelope>", "description": "Posts a checkpoint after each order", "type": "string", "required": 0, "values": []}}, "efinity": {"sync_mode": {"name": "sourcemeta_efinity", "key": "sync_mode", "default": "pull", "description": "push or pull", "type": "string", "required": 1, "values": []}, "url": {"name": "sourcemeta_efinity", "key": "url", "default": "http://sandbox.ecs24.co.za/api/", "description": "API endpoint url", "type": "string", "required": 1, "values": []}, "username": {"name": "sourcemeta_efinity", "key": "username", "default": "user123", "description": "API username", "type": "string", "required": 1, "values": []}, "password": {"name": "sourcemeta_efinity", "key": "password", "default": "****", "description": "API password", "type": "string", "required": 1, "values": []}, "product_code_key": {"name": "sourcemeta_efinity", "key": "product_code_key", "default": "sku", "description": "Values can be 'sku' or 'source_variant_code'. The field on the variant used to lookup source product code. Use this when you have a linked source with variance, since efinity does not understand variance", "type": "string", "required": 1, "values": []}, "cron_get_products_schedule": {"name": "sourcemeta_efinity", "key": "cron_get_products_schedule", "default": "x 00 * * * *", "description": "Basic format is \"MM HH * * *\"", "type": "string", "required": 0, "values": []}, "create_order_enabled": {"name": "sourcemeta_efinity", "key": "create_order_enabled", "default": "false", "description": "Allow creating order on source", "type": "string", "required": 1, "values": []}, "get_images_enabled": {"name": "sourcemeta_efinity", "key": "get_images_enabled", "default": "false", "description": "No images on this source", "type": "string", "required": 1, "values": []}, "get_products_limit": {"name": "sourcemeta_efinity", "key": "get_products_limit", "default": "100", "description": "Number of products to fetch at a time", "type": "string", "required": 1, "values": []}, "queue_fetch_images": {"name": "sourcemeta_efinity", "key": "queue_fetch_images", "default": "false", "description": "This source does not support images", "type": "string", "required": 1, "values": []}, "product_map": {"name": "sourcemeta_efinity", "key": "product_map", "default": "{\n\"source\": {\n\"product_active\": \"true\",\n\"source_product_code\": \"{{{productSku}}}\"\n},\n\"product\": {\n\"title\": \"{{{productName}}}\",\n\"variants\": {\n\"source_variant_code\": \"{{{productSku}}}\",\n\"sku\": \"{{{productSku}}}\",\n\"qty\": \"0\",\n\"qty_availability\": [\n{\n\"description\" : \"{{{warehouse}}}\",\n\"qty\" : \"{{availableQuantity}}\"\n}\n]\n}\n}\n}", "description": "Product map between S2S and efinity, uses mustache", "type": "string", "required": 1, "values": []}}, "flatfile": {"image_field_map": {"name": "sourcemeta_flatfile", "key": "image_field_map", "default": "{\n  \"source_images\": [\n    {\n      \"source\": {\n        \"source_product_code\": \"{{source.source_product_code}}\",\n        \"source_id\": \"{{source.id}}\",\n        \"sync_token\": \"{{source.sync_token}}\"\n      },\n      \"image\": {\n        \"action\": \"I\",\n        \"image_id\": \"{{data.image1}}\",\n        \"meta\": {\n          \"thumbnail\": \"true\",\n          \"image\": \"true\",\n          \"small_image\": \"true\"\n        }\n      }\n    }\n  ]\n}", "description": "Fieldmap used for creating images. See example map for object model", "type": "string", "required": 0, "values": []}, "sync_mode": {"name": "sourcemeta_flatfile", "key": "sync_mode", "default": "pull", "description": "push or pull", "type": "string", "required": 1, "values": []}, "order_template": {"name": "sourcemeta_flatfile", "key": "order_template", "default": "{{some template}}", "description": "Mustache JSON template used to transform order data. You can also use another format like csv - use a check for system_order.line_items to loop through line items eg {{# system_order.line_items}}{{sku}},{{qty}},{{price}}{{/ system_order.line_items}}. We can also set  tab as \\t and new line as \\n and/or \\r", "type": "string", "required": 0, "values": []}, "ftp_order_path": {"name": "sourcemeta_flatfile", "key": "ftp_order_path", "default": "{{system_order.id}}.txt", "description": "Mustache template which to calculate the full file path", "type": "string", "required": 0, "values": []}, "ftp_order_host": {"name": "sourcemeta_flatfile", "key": "ftp_order_host", "default": "ftp.stock2shop.com", "description": "Ftp host", "type": "string", "required": 0, "values": []}, "ftp_order_username": {"name": "sourcemeta_flatfile", "key": "ftp_order_username", "default": "user", "description": "FTP username", "type": "string", "required": 0, "values": []}, "ftp_order_password": {"name": "sourcemeta_flatfile", "key": "ftp_order_password", "default": "abc", "description": "FTP password", "type": "string", "required": 0, "values": []}, "ftp_order_port": {"name": "sourcemeta_flatfile", "key": "ftp_order_port", "default": "21", "description": "FTP port", "type": "string", "required": 0, "values": []}, "order_send_method": {"name": "sourcemeta_flatfile", "key": "order_send_method", "default": "ftp", "description": "What to do with the order, currently ftp and sftp supported", "type": "string", "required": 0, "values": []}, "csv_header": {"name": "sourcemeta_flatfile", "key": "csv_header", "default": "", "description": "Comma separated list of headers, if not included in the file (e.g. header1,header2). These must match the number of columns", "type": "string", "required": 0, "values": []}, "cron_get_products_schedule": {"name": "sourcemeta_flatfile", "key": "cron_get_products_schedule", "default": "x 00 * * * *", "description": "Basic format is \"MM HH * * *\"", "type": "string", "required": 1, "values": []}, "create_order_enabled": {"name": "sourcemeta_flatfile", "key": "create_order_enabled", "default": "false", "description": "Allow creating order on source", "type": "string", "required": 1, "values": []}, "get_images_limit": {"name": "sourcemeta_flatfile", "key": "get_images_limit", "default": "1", "description": "Number of images to fetch at a time", "type": "string", "required": 1, "values": []}, "get_images_enabled": {"name": "sourcemeta_flatfile", "key": "get_images_enabled", "default": "false", "description": "Fetch images for this source", "type": "string", "required": 1, "values": []}, "get_products_limit": {"name": "sourcemeta_flatfile", "key": "get_products_limit", "default": "100", "description": "Number of products to fetch at a time", "type": "string", "required": 1, "values": []}, "google_sheet_header": {"name": "sourcemeta_flatfile", "key": "google_sheet_header", "default": "", "description": "CSV list of headers to use. Example: product.title, variant.barcode", "type": "string", "required": 0, "values": []}, "product_field_map": {"name": "sourcemeta_flatfile", "key": "product_field_map", "default": "{\"source\": {\"source_product_code\": \"{{some_column}}\",\"product_active\": \"\"},\"product\": {\"collection\": \"\",\"title\": \"\",\"product_type\": \"\",\"body_html\": \"\",\"tags\": \"\",\"vendor\": \"\",\"options\": [{\"value\": \"\",\"name\": \"\",\"position\": 1}],\"variants\": {\"source_variant_code\": \"\",\"option1\": \"\",\"qty\": \"\",\"barcode\": \"\",\"grams\": \"\",\"sku\": \"\",\"price\": \"\",\"inventory_management\": \"true\",\"price_tiers\": []}}}", "description": "Field mapping between S2S and flat file, use handlebar tags for variables {{}}", "type": "string", "required": 1, "values": []}, "transfer_protocol": {"name": "sourcemeta_flatfile", "key": "transfer_protocol", "default": "", "description": "Type of transfer protocol, currently support \"ftp\", \"url\", \"sftp\"", "type": "string", "required": 1, "values": []}, "ftp_host": {"name": "sourcemeta_flatfile", "key": "ftp_host", "default": "", "description": "Host for ftp", "type": "string", "required": 0, "values": []}, "ftp_port": {"name": "sourcemeta_flatfile", "key": "ftp_port", "default": "", "description": "Port for ftp", "type": "string", "required": 0, "values": []}, "ftp_file_path": {"name": "sourcemeta_flatfile", "key": "ftp_file_path", "default": "", "description": "File to download, include full path from root and file name", "type": "string", "required": 0, "values": []}, "ftp_username": {"name": "sourcemeta_flatfile", "key": "ftp_username", "default": "", "description": "Username for transferring file", "type": "string", "required": 0, "values": []}, "ftp_password": {"name": "sourcemeta_flatfile", "key": "ftp_password", "default": "", "description": "Password for transferring file", "type": "string", "required": 0, "values": []}, "remove_file_enabled": {"name": "sourcemeta_flatfile", "key": "remove_file_enabled", "default": "true", "description": "Removes the file when complete (only ftp)", "type": "string", "required": 0, "values": []}, "file_extension": {"name": "sourcemeta_flatfile", "key": "file_extension", "default": "csv", "description": "Use this to specify the file extension if file_url doesn't end with .extension", "type": "string", "required": 1, "values": []}, "file_url": {"name": "sourcemeta_flatfile", "key": "file_url", "default": "", "description": "Url to download file from (only valid if transfer protocol is url) ", "type": "string", "required": 0, "values": []}, "deliminator": {"name": "sourcemeta_flatfile", "key": "deliminator", "default": ",", "description": "Type of deliminator used to separate values", "type": "string", "required": 1, "values": []}, "queue_fetch_images": {"name": "sourcemeta_flatfile", "key": "queue_fetch_images", "default": "false", "description": "Queue fetch_images item after processing sync_product", "type": "string", "required": 0, "values": []}, "xml_product_tag": {"name": "sourcemeta_flatfile", "key": "xml_product_tag", "default": "", "description": "", "type": "string", "required": 0, "values": []}, "xml_product_xslt": {"name": "sourcemeta_flatfile", "key": "xml_product_xslt", "default": "", "description": "", "type": "string", "required": 0, "values": []}, "xml_image_xslt": {"name": "sourcemeta_flatfile", "key": "xml_image_xslt", "default": "", "description": "", "type": "string", "required": 0, "values": []}, "flatfile_format": {"name": "sourcemeta_flatfile", "key": "flatfile_format", "default": "", "description": "xml csv", "type": "string", "required": 0, "values": []}}, "flowgear": {"sync_mode": {"name": "sourcemeta_flowgear", "key": "sync_mode", "default": "pull", "description": "push or pull", "type": "string", "required": 1, "values": []}, "create_order": {"name": "sourcemeta_flowgear", "key": "create_order", "default": "", "description": "", "type": "string", "required": 1, "values": []}, "get_images": {"name": "sourcemeta_flowgear", "key": "get_images", "default": "", "description": "", "type": "string", "required": 1, "values": []}, "get_images_limit": {"name": "sourcemeta_flowgear", "key": "get_images_limit", "default": "2", "description": "Number of images to fetch at a time", "type": "string", "required": 1, "values": []}, "get_products": {"name": "sourcemeta_flowgear", "key": "get_products", "default": "", "description": "", "type": "string", "required": 1, "values": []}, "get_products_limit": {"name": "sourcemeta_flowgear", "key": "get_products_limit", "default": "20", "description": "Number of products to fetch at a time", "type": "string", "required": 1, "values": []}, "profile": {"name": "sourcemeta_flowgear", "key": "profile", "default": "Production", "description": "", "type": "string", "required": 1, "values": []}, "queue_fetch_images": {"name": "sourcemeta_flowgear", "key": "queue_fetch_images", "default": "false", "description": "Queue fetch_images item after processing sync_product", "type": "string", "required": 0, "values": []}, "site": {"name": "sourcemeta_flowgear", "key": "site", "default": "", "description": "", "type": "string", "required": 1, "values": []}}, "isync": {"sync_mode": {"name": "sourcemeta_isync", "key": "sync_mode", "default": "pull", "description": "push or pull", "type": "string", "required": 1, "values": []}, "create_order_enabled": {"name": "sourcemeta_isync", "key": "create_order_enabled", "default": "true", "description": "Allow creating order on source", "type": "string", "required": 1, "values": []}, "cron_get_products_schedule": {"name": "sourcemeta_isync", "key": "cron_get_products_schedule", "default": "x 00 * * * *", "description": "Basic format is \"MM HH * * *\"", "type": "string", "required": 1, "values": []}, "param_customer_parent_id": {"name": "sourcemeta_isync", "key": "param_customer_parent_id", "default": "3015", "description": "", "type": "string", "required": 1, "values": []}, "get_images_limit": {"name": "sourcemeta_isync", "key": "get_images_limit", "default": "2", "description": "Number of images to fetch at a time", "type": "string", "required": 1, "values": []}, "get_products_limit": {"name": "sourcemeta_isync", "key": "get_products_limit", "default": "20", "description": "Number of products to fetch at a time", "type": "string", "required": 1, "values": []}, "username": {"name": "sourcemeta_isync", "key": "username", "default": "", "description": "", "type": "string", "required": 1, "values": []}, "password": {"name": "sourcemeta_isync", "key": "password", "default": "", "description": "", "type": "string", "required": 1, "values": []}, "url": {"name": "sourcemeta_isync", "key": "url", "default": "http://dctest01.isyncsa.com:81/UAT2EAI/MessageService.svc", "description": "", "type": "string", "required": 1, "values": []}, "product_field_map": {"name": "sourcemeta_isync", "key": "product_field_map", "default": "{\n\"source\": {\n\"source_product_code\": \"{{Style}}\",\n\"product_active\": \"{{WebSale}}\"\n},\n\"product\": {\n\"collection\": \"{{Product}}\",\n\"title\": \"{{JobDescription}}\",\n\"product_type\": \"{{ProductUD0}}\",\n\"body_html\": \"{{CustomField0}}\",\n\"tags\": \"{{CustomField1}}\",\n\"vendor\": \"{{VariableField3}}\",\n\"options\": [\n{\n\"value\": \"\",\n\"name\": \"Size\",\n\"position\": 1\n},\n{\n\"value\": \"\",\n\"name\": \"Colour\",\n\"position\": 1\n}\n],\n\"variants\": {\n\"source_variant_code\": \"{{SKU}}\",\n\"option1\": \"{{Size}}\",\n\"option2\": \"{{Colour}}\",\n\"qty\": \"{{AvailableUnits}}\",\n\"barcode\": \"\",\n\"grams\": \"\",\n\"sku\": \"{{SKU}}\",\n\"price\": \"{{RetailSellingPrice}}\",\n\"inventory_management\": \"true\",\n\"price_tiers\": [\n{\n\"tier\": \"Retail\",\n\"price\": \"{{RetailSellingPrice}}\"\n},\n{\n\"tier\": \"Wholesale\",\n\"price\": \"{{SellingPrice}}\"\n}\n]\n}\n}\n}", "description": "Field mapping between S2S and iSync, use '{{}}' signs to note variable, otherwise string will be treated as literal", "type": "string", "required": 1, "values": []}, "image_field_map": {"name": "sourcemeta_isync", "key": "image_field_map", "default": "[\n{\n\"image\": {\n\"image\": \"http://example.com/{{CustomField8}}\",\n\"image_id\": \"{{CustomField8}}\",\n\"action\": \"I\"\n},\n\"source\": {\n\"source_product_code\": \"{{JobProductID}}\",\n\"source_variant_code\": \"{{ProductStockID}}\"\n}\n},\n{\n\"image\": {\n\"image\": \"http://example.com/{{CustomField9}}\",\n\"image_id\": \"{{CustomField9}}\",\n\"action\": \"I\"\n},\n\"source\": {\n\"source_product_code\": \"{{JobProductID}}\",\n\"source_variant_code\": \"{{ProductStockID}}\"\n}\n}\n]", "description": "Field mapping between S2S and iSync for images, use '{{}}' signs to note variable, otherwise string will be treated as literal", "type": "string", "required": 0, "values": []}, "param_default_customer_code": {"name": "sourcemeta_isync", "key": "param_default_customer_code", "default": "", "description": "", "type": "string", "required": 0, "values": []}, "param_order_line_item": {"name": "sourcemeta_isync", "key": "param_order_line_item", "default": "ProductStockID", "description": "The iSync line item field to use when raising an order, options are ProductStockID (source_variant_code) or ThirdPartyBarcode (sku)", "type": "string", "required": 1, "values": []}, "queue_fetch_images": {"name": "sourcemeta_isync", "key": "queue_fetch_images", "default": "false", "description": "Queue fetch_images item after processing sync_product", "type": "string", "required": 0, "values": []}, "shipping_cn_type_id": {"name": "sourcemeta_isync", "key": "shipping_cn_type_id", "default": "4", "description": "CN Type ID used for shipping", "type": "string", "required": 0, "values": []}}, "json": {"order_template_0": {"name": "sourcemeta_json", "key": "order_template_0", "default": "mustache", "description": "Order template engine", "type": "string", "required": 1, "values": []}, "order_method_0": {"name": "sourcemeta_json", "key": "order_method_0", "default": "POST", "description": "(POST|GET|PUT|PATCH) request method", "type": "string", "required": 1, "values": []}, "order_url_0": {"name": "sourcemeta_json", "key": "order_url_0", "default": "http://ec2-176-34-142-52.eu-west-1.compute.amazonaws.com:8080/IQRetailRestAPI/v1/IQ_API_Submit_Document_Sales_Order", "description": "request url", "type": "string", "required": 1, "values": []}, "order_source_order_code_0": {"name": "sourcemeta_json", "key": "order_source_order_code_0", "default": "{   \"response_0\": {     \"iq_api_success\": {       \"iq_api_success_items\": [         [           {             \"fieldname\": \"IQ_Document_Number\",             \"data\": \"source_order_code\"           }         ]       ]     }     } }", "description": "JSON representing where to find the source_order_code in the response. You can include other properties that must be present n the response. The data returned is stored on a property called \"response_0\" if it is the first request and \"response_1\" if it is the second, and so on.", "type": "string", "required": 1, "values": []}, "order_map_0": {"name": "sourcemeta_json", "key": "order_map_0", "default": "{\n  \"IQ_API\": {\n    \"IQ_API_Submit_Document_Sales_Order\": {\n      \"IQ_Company_Number\": \"001\",\n      \"IQ_Terminal_Number\": 1,\n      \"IQ_User_Number\": 2,\n      \"IQ_User_Password\": \"{{password_hash}}\",\n      \"IQ_Instruction_Set\": [\n        {}\n      ],\n      \"IQ_AutoProcess\": {\n        \"IQ_AutoProcess_Type\": \"iqINV\",\n        \"IQ_Instruction_Set\": [\n          {\n            \"Instruction_Type\": \"apiitSubmitConfirmation\"\n          },\n          {\n            \"Instruction_Type\": \"apiitSubmitError\"\n          }\n        ]\n      },\n      \"IQ_Fallback\": {\n        \"IQ_Fallback_Type\": \"iqSOR\"\n      },\n      \"IQ_Submit_Data\": {\n        \"iq_root_json\": {\n          \"iq_identification_info\": {\n            \"company_store_id\": \"1\",\n            \"company_code\": \"001\",\n            \"company_name\": \"test\",\n            \"company_address1\": \"\",\n            \"company_address2\": \"\",\n            \"company_address3\": \"\",\n            \"company_address4\": \"\",\n            \"company_telephone1\": \"\",\n            \"company_telephone2\": \"\",\n            \"company_fax\": \"\",\n            \"company_email\": \"\",\n            \"company_tax\": \"\",\n            \"company_registration_Number\": \"\",\n            \"company_customs_Code\": \"\"\n          },\n          \"processing_documents\": [\n            {\n              \"export_class\": \"Sales_Order\",\n              \"document\": {\n                \"document_number\": \"SAL11\",\n                \"delivery_address_information\": [\n                  \"Address\",\n                  \"Address\",\n                  \"\",\n                  \"Address, Address\"\n                ],\n                \"email_address\": \"<EMAIL>\",\n                \"order_number\": \"ClientPONumber\",\n                \"delivery_method\": \"\",\n                \"delivery_note_number\": \"\",\n                \"total_vat\": 131.85,\n                \"discount_percentage\": 0,\n                \"discount_type\": \"Percentage\",\n                \"discount_amount\": 0,\n                \"long_description\": \"\",\n                \"document_total\": 1010.88,\n                \"total_number_of_items\": 125,\n                \"document_description\": \"Contact\",\n                \"print_layout\": 1,\n                \"warehouse\": \"\",\n                \"cashier_number\": 1,\n                \"till_number\": 500,\n                \"document_includes_vat\": true,\n                \"currency\": \"ZAR\",\n                \"currency_rate\": 1,\n                \"internal_order_number\": \"UNIO46216\",\n                \"store_department\": \"\",\n                \"document_terms\": \"Not Applicable\",\n                \"telephone_number\": \"12345\",\n                \"vat_number\": \"\",\n                \"postal_code\": \"Address\",\n                \"extra_charges_information\": [\n                  {\n                    \"extra_charge_description\": \"\",\n                    \"extra_charge_amount\": 0\n                  },\n                  {\n                    \"extra_charge_description\": \"\",\n                    \"extra_charge_amount\": 0\n                  },\n                  {\n                    \"extra_charge_description\": \"\",\n                    \"extra_charge_amount\": 0\n                  },\n                  {\n                    \"extra_charge_description\": \"\",\n                    \"extra_charge_amount\": 0\n                  }\n                ],\n                \"debtor_account\": \"CASH001\",\n                \"debtor_name\": \"\",\n                \"sales_representative_number\": 4,\n                \"order_information\": {\n                  \"order_date\": \"2020-06-05\",\n                  \"expected_date\": \"2020-06-26\",\n                  \"credit_approver_number\": 0\n                }\n              },\n              \"items\": [\n                {\n                  \"stock_code\": \"GTSH001\",\n                  \"stock_description\": \"\",\n                  \"comment\": \"QW_Vendor_Name: NETWORKS CENTREu001E\r\nQW_Vendor_Account: u001E\r\nQW_EstShipDate_Supplier: 1899-12-30u001E\r\nQW_EstShipDate: 2020-06-26u001E\r\nCustom Memo:2-3 Weeks\r\n\",\n                  \"quantity\": 1,\n                  \"volumetrics\": {},\n                  \"item_price_inclusive\": 8.0155\n                },\n                {\n                  \"stock_code\": \"DEL001\",\n                  \"stock_description\": \"\",\n                  \"comment\": \"QW_Vendor_Name: NETWORKS CENTREu001E\r\nQW_Vendor_Account: u001E\r\nQW_EstShipDate_Supplier: 1899-12-30u001E\r\nQW_EstShipDate: 1899-12-30u001E\r\nCustom Memo:\",\n                  \"quantity\": 1,\n                  \"volumetrics\": {},\n                  \"item_price_inclusive\": 11.4425\n                }\n              ]\n            }\n          ]\n        }\n      },\n      \"IQ_Overrides\": [\n        \"ideNegativeStock\",\n        \"ideInvalidDateRange\"\n      ]\n    }\n  }\n}", "description": "Order map for request", "type": "string", "required": 1, "values": []}, "get_products_limit": {"name": "sourcemeta_json", "key": "get_products_limit", "default": "100", "description": "Products fetched for each cycle. Fetches only 20 cycles.", "type": "string", "required": 1, "values": []}, "get_products_url": {"name": "sourcemeta_json", "key": "get_products_url", "default": "http://ec2-176-34-142-52.eu-west-1.compute.amazonaws.com:8080/IQRetailRestAPI/v1/IQ_API_Request_GenericSQL", "description": "Url for fetching products, this is render with Mustache. You have access to {{current_iteration}}, {{sync_token}}, {{limit}} and {{source_id}}", "type": "string", "required": 1, "values": []}, "get_products_body": {"name": "sourcemeta_json", "key": "get_products_body", "default": "{\n  \"IQ_API\": {\n    \"IQ_API_Request_GenericSQL\": {\n      \"IQ_Company_Number\": \"001\",\n      \"IQ_Terminal_Number\": 1,\n      \"IQ_User_Number\": 2,\n      \"IQ_User_Password\": \"{{meta.password_hash}}\",\n      \"IQ_SQL_Text\": \"select * from stock;\"\n    }\n  }\n}", "description": "POST body Mustache template. {{meta.x}} is source meta. {{params.x}} is the sync token and other params", "type": "string", "required": 0, "values": []}, "get_products_method": {"name": "sourcemeta_json", "key": "get_products_method", "default": "POST", "description": "(POST|GET) API request method ", "type": "string", "required": 1, "values": []}, "get_products_audit": {"name": "sourcemeta_json", "key": "get_products_audit", "default": "true", "description": "(true|false) if true, all products must be returned in one fetch. If true, you cannot use sync_token in your request.", "type": "string", "required": 1, "values": []}, "get_products_results_path": {"name": "sourcemeta_json", "key": "get_products_results_path", "default": "iq_api_result_data.records", "description": "Path showing where the array of products is in the response data", "type": "string", "required": 1, "values": []}, "get_products_map": {"name": "sourcemeta_json", "key": "get_products_map", "default": "{\n\t\"source\": {\n\t\t\"source_product_code\": \"{{data.code}}\",\n\t\t\"product_active\": \"true\",\n\t\t\"sync_token\": \"{{params.sync_token}}\"\n\t},\n\t\"product\": {\n\t\t\"title\": \"{{data.descript}}\",\n\t\t\"vendor\": \"\",\n\t\t\"options\": [],\n\t\t\"variants\": {\n\t\t\t\"active\": \"true\",\n\t\t\t\"source_variant_code\": \"{{data.code}}\",\n\t\t\t\"qty\": \"{{data.onhand}}\",\n\t\t\t\"price\": \"{{data.sellprice1}}\",\n\t\t\t\"sku\": \"{{data.code}}\",\n            \"inventory_management\": \"true\",\n\t\t\t\"price_tiers\": [],\n\t\t\t\"qty_availability\": []\n\t\t},\n\t\t\"meta\": []\n\t}\n}", "description": "Mustache map converting response to source product. {{data.x}} is the response data. {{params.x}} is the sync token and other params", "type": "string", "required": 1, "values": []}, "get_products_header": {"name": "sourcemeta_json", "key": "get_products_header", "default": "{   \"Accept\": \"application/json\",   \"Content-Type\": \"application/json\" }", "description": "Request Headers. JSON key values.", "type": "string", "required": 0, "values": []}, "get_products_paging": {"name": "sourcemeta_json", "key": "get_products_paging", "default": "false", "description": "Page through records all records on each fetch. This is for API's which do not support sync_token. If true, you must set get_products_audit=true", "type": "string", "required": 0, "values": []}, "order_source_customer_code_0": {"name": "sourcemeta_json", "key": "order_source_customer_code_0", "default": "{   \"response_0\": {     \"iq_api_success\": {       \"iq_api_success_items\": [         [           {             \"fieldname\": \"IQ_Customer_Number\",             \"data\": \"source_customer_code\"           }         ]       ]     }     } }", "description": "JSON representing where to find the source_customer_code in the response. You can include other properties that must be present n the response. The data returned is stored on a property called \"response_0\" if it is the first request and \"response_1\" if it is the second, and so on.", "type": "string", "required": 0, "values": []}, "order_header_0": {"name": "sourcemeta_json", "key": "order_header_0", "default": "{   \"Accept\": \"application/json\",   \"Content-Type\": \"application/json\" }", "description": "Request Headers. JSON key values.", "type": "string", "required": 0, "values": []}}, "parcelninja": {"sync_mode": {"name": "sourcemeta_parcelninja", "key": "sync_mode", "default": "pull", "description": "push or pull", "type": "string", "required": 1, "values": []}, "cron_get_products_schedule": {"name": "sourcemeta_parcelninja", "key": "cron_get_products_schedule", "default": "x 00 * * * *", "description": "Basic format is \"MM HH * * *\"", "type": "string", "required": 0, "values": []}, "username": {"name": "sourcemeta_parcelninja", "key": "username", "default": "***", "description": "PN API username", "type": "string", "required": 1, "values": []}, "password": {"name": "sourcemeta_parcelninja", "key": "password", "default": "2", "description": "PN API password", "type": "string", "required": 1, "values": []}, "get_products_limit": {"name": "sourcemeta_parcelninja", "key": "get_products_limit", "default": "500", "description": "Number of products to fetch at a time, 500 is default since this is the pn paging page size", "type": "string", "required": 1, "values": []}, "product_field_map": {"name": "sourcemeta_parcelninja", "key": "product_field_map", "default": "{ \t\"source\": { \t\t\"source_product_code\": \"{{itemNo}}\", \t\t\"product_active\": \"true\" \t}, \t\"product\": { \t\t\"title\": \"{{name}}\", \t\t\"variants\": { \t\t\t\"source_variant_code\": \"{{itemNo}}\", \t\t\t\"qty\": \"{{instock}}\", \t\t\t\"sku\": \"{{itemNo}}\", \t\t\t\"inventory_management\": true \t\t} \t} }", "description": "Field mapping between S2S and parcel ninja using handlebars. Source product code will be added in transform by default", "type": "string", "required": 1, "values": []}, "default_contact_number": {"name": "sourcemeta_parcelninja", "key": "default_contact_number", "default": "0821231234", "description": "Telephone number to use when raising an order if no cell number given.", "type": "string", "required": 0, "values": []}, "queue_fetch_images": {"name": "sourcemeta_parcelninja", "key": "queue_fetch_images", "default": "false", "description": "Queue fetch_images item after processing sync_product", "type": "string", "required": 0, "values": []}, "create_order_enabled": {"name": "sourcemeta_parcelninja", "key": "create_order_enabled", "default": "false", "description": "", "type": "string", "required": 1, "values": []}}, "sageintacct": {"endpoint_url": {"name": "sourcemeta_sageintacct", "key": "endpoint_url", "default": "https://api.intacct.com/ia/xml/xmlgw.phtml", "description": "API url", "type": "string", "required": 1, "values": []}, "sender_id": {"name": "sourcemeta_sageintacct", "key": "sender_id", "default": "x", "description": "", "type": "string", "required": 1, "values": []}, "sender_password": {"name": "sourcemeta_sageintacct", "key": "sender_password", "default": "x", "description": "", "type": "string", "required": 1, "values": []}, "company_id": {"name": "sourcemeta_sageintacct", "key": "company_id", "default": "x", "description": "", "type": "string", "required": 1, "values": []}, "user_id": {"name": "sourcemeta_sageintacct", "key": "user_id", "default": "x", "description": "", "type": "string", "required": 1, "values": []}, "user_password": {"name": "sourcemeta_sageintacct", "key": "user_password", "default": "x", "description": "", "type": "string", "required": 1, "values": []}, "create_customer_enabled": {"name": "sourcemeta_sageintacct", "key": "create_customer_enabled", "default": "true", "description": "if no source customer code given, customer is created", "type": "string", "required": 1, "values": []}, "get_products_limit": {"name": "sourcemeta_sageintacct", "key": "get_products_limit", "default": "100", "description": "", "type": "string", "required": 1, "values": []}, "product_query_filter_[key]": {"name": "sourcemeta_sageintacct", "key": "product_query_filter_[key]", "default": "value", "description": "Adds an \"equalto\" query filter", "type": "string", "required": 0, "values": []}, "product_query_fields": {"name": "sourcemeta_sageintacct", "key": "product_query_fields", "default": "value", "description": "Comma separated string of fields to return when fetching products", "type": "string", "required": 0, "values": []}, "order_create_field_[key]": {"name": "sourcemeta_sageintacct", "key": "order_create_field_[key]", "default": "value", "description": "Adds value to specific order field", "type": "string", "required": 0, "values": []}, "source_product_map": {"name": "sourcemeta_sageintacct", "key": "source_product_map", "default": "{\n              \"source\": {\n                \"source_product_code\": \"{{ITEMID}}\",\n                \"product_active\": \"{{WEBENABLED}}\",\n                \"sync_token\": \"{{WHENMODIFIED}}\"\n              },\n              \"product\": {\n                \"title\": \"{{WEBNAME}}\",\n                \"body_html\": \"{{WEBLONGDESC}}\",\n                \"collection\": \"{{CATEGORY}}\",\n                \"product_type\": \"{{SUBCATEGORY}}\",\n                \"options\":[\n                  {\n                    \"name\": \"Size\",\n                    \"position\": \"1\"\n                  },\n                  {\n                    \"name\": \"Colour\",\n                    \"position\": \"2\"\n                  }\n                ],\n                \"variants\": {\n                  \"source_variant_code\": \"{{ITEMID}}\",\n                  \"qty\": \"{{#calculate}}{{IONHAND}}-{{IONHOLD}}{{/calculate}}\",\n                  \"price\": \"{{BASEPRICE}}\",\n                  \"option1\" : \"{{# json_escape}}{{SIZE1}}{{/ json_escape}}\",\n                  \"option2\": \"{{# json_escape}}{{COLOR}}{{/ json_escape}}\",\n                  \"inventory_management\": true,\n                  \"grams\":\"{{NETWEIGHT}}\"\n                },\n                \"meta\":\n                [\n                  {\n                    \"key\": \"short_desc\",\n                    \"value\": \"{{WEBSHORTDESC}}\"\n                  },\n                  {\n                    \"key\": \"length\",\n                    \"value\": \"{{LENGTH}}\"\n                  },\n                  {\n                    \"key\": \"width\",\n                    \"value\": \"{{WIDTH}}\"\n                  }\n                ]\n              }\n            }", "description": "", "type": "string", "required": 0, "values": []}}, "sagelive": {"get_products_limit": {"name": "sourcemeta_sagelive", "key": "get_products_limit", "default": "100", "description": "", "type": "string", "required": 1, "values": []}, "create_order_enabled": {"name": "sourcemeta_sagelive", "key": "create_order_enabled", "default": "false", "description": "", "type": "string", "required": 1, "values": []}, "cron_get_products_schedule": {"name": "sourcemeta_sagelive", "key": "cron_get_products_schedule", "default": "x 00 * * * *", "description": "Basic format is \"MM HH * * *\"", "type": "string", "required": 0, "values": []}, "sync_mode": {"name": "sourcemeta_sagelive", "key": "sync_mode", "default": "pull", "description": "push or pull", "type": "string", "required": 1, "values": []}, "queue_fetch_images": {"name": "sourcemeta_sagelive", "key": "queue_fetch_images", "default": "false", "description": "Images not supported", "type": "string", "required": 1, "values": []}, "create_customer_enabled": {"name": "sourcemeta_sagelive", "key": "create_customer_enabled", "default": "false", "description": "Allowed to create new customer accounts", "type": "string", "required": 0, "values": []}, "access_token": {"name": "sourcemeta_sagelive", "key": "access_token", "default": "", "description": "Encrypted token generated from authentication", "type": "string", "required": 1, "values": []}, "consumer_key": {"name": "sourcemeta_sagelive", "key": "consumer_key", "default": "", "description": "oAuth consumer key", "type": "string", "required": 1, "values": []}, "consumer_secret": {"name": "sourcemeta_sagelive", "key": "consumer_secret", "default": "", "description": "oAuth consumer secret", "type": "string", "required": 1, "values": []}, "sync_price_token": {"name": "sourcemeta_sagelive", "key": "sync_price_token", "default": "2017-01-01T00:00:00+0000", "description": "Token used for checking changes in price", "type": "string", "required": 1, "values": []}, "sync_qty_token": {"name": "sourcemeta_sagelive", "key": "sync_qty_token", "default": "2017-01-01T00:00:00+0000", "description": "Token used for checking changes in qty", "type": "string", "required": 1, "values": []}, "product_query": {"name": "sourcemeta_sagelive", "key": "product_query", "default": "SELECT\n            SystemModstamp,\n            Id,\n            Name,\n            s2cor__Product__r.Name,\n            s2cor__Product__r.IsActive,\n            s2cor__Product__r.Id,\n            s2cor__Product__r.ProductCode,\n            s2cor__Product__r.Description,\n            s2cor__Product__r.s2cor__Product_Group__r.Name,\n            (\n              SELECT\n              s2cor__Tag__r.Name,\n              s2cor__Dimension__r.Name\n              FROM s2cor__Stock_Keeping_Unit_Tags__r\n            )\n            FROM s2cor__Sage_STK_Stock_Keeping_Unit__c ", "description": "Sales Force SOQL query for returning products, must return SystemModstamp and Id. Do not add where clause this is configured independently", "type": "string", "required": 1, "values": []}, "product_filter": {"name": "sourcemeta_sagelive", "key": "product_filter", "default": "s2cor__Product__r.s2cor__Stock_Item__c = true", "description": "Filter for products to be returned. This is the Sales Force SOQL query where clause", "type": "string", "required": 1, "values": []}, "product_template": {"name": "sourcemeta_sagelive", "key": "product_template", "default": "{\n  \"source\": {\n    \"source_product_code\": \"{{s2cor__Product__r.Id}}\",\n    \"product_active\": \"{{# s2cor__Product__r.IsActive}}true{{/ s2cor__Product__r.IsActive}}{{^ s2cor__Product__r.IsActive}}false{{/ s2cor__Product__r.IsActive}}\",\n    \"sync_token\": \"{{SystemModstamp}}\"\n  },\n  \"product\": {\n    \"collection\": \"{{s2cor__Product__r.s2cor__Product_Group__r.Name}}\",\n    \"title\": \"{{s2cor__Product__r.Name}}\",\n    \"product_type\": \"\",\n    \"body_html\": \"{{s2cor__Product__r.Description}}\",\n    \"tags\": \"\",\n    \"vendor\": \"\",\n    \"options\": [\n      {{# s2cor__Stock_Keeping_Unit_Tags__r.records.0}}\n      {\n        \"value\": \"\",\n        \"name\": \"{{s2cor__Dimension__r.Name}}\",\n        \"position\": 1\n      }\n      {{/ s2cor__Stock_Keeping_Unit_Tags__r.records.0}}\n      {{# s2cor__Stock_Keeping_Unit_Tags__r.records.1}}\n      ,{\n        \"value\": \"\",\n        \"name\": \"{{s2cor__Dimension__r.Name}}\",\n        \"position\": 2\n      }\n      {{/ s2cor__Stock_Keeping_Unit_Tags__r.records.1}}\n      {{# s2cor__Stock_Keeping_Unit_Tags__r.records.2}}\n      ,{\n        \"value\": \"\",\n        \"name\": \"{{s2cor__Dimension__r.Name}}\",\n        \"position\": 3\n      }\n      {{/ s2cor__Stock_Keeping_Unit_Tags__r.records.2}}\n    ],\n    \"variants\": {\n      \"source_variant_code\": \"{{Id}}\",\n      {{# s2cor__Stock_Keeping_Unit_Tags__r.records.0}}\n      \"option1\": \"{{s2cor__Stock_Keeping_Unit_Tags__r.records.0.s2cor__Tag__r.Name}}\",\n      {{/ s2cor__Stock_Keeping_Unit_Tags__r.records.0}}\n      {{# s2cor__Stock_Keeping_Unit_Tags__r.records.1}}\n      \"option2\": \"{{s2cor__Stock_Keeping_Unit_Tags__r.records.1.s2cor__Tag__r.Name}}\",\n      {{/ s2cor__Stock_Keeping_Unit_Tags__r.records.1}}\n      {{# s2cor__Stock_Keeping_Unit_Tags__r.records.2}}\n      \"option3\": \"{{s2cor__Stock_Keeping_Unit_Tags__r.records.2.s2cor__Tag__r.Name}}\",\n      {{/ s2cor__Stock_Keeping_Unit_Tags__r.records.2}}\n      \"qty\": \"{{s2cor__Stock_Balances__r.records.0.s2cor__Quantity__c}}\",\n      \"barcode\": \"\",\n      \"grams\": \"\",\n      \"sku\": \"{{Name}}\",\n      \"inventory_management\": \"TRUE\",\n      \"price\": \"{{calculated automatically, not set here}}\",\n      \"price_tiers\": \"{{calculated automatically, not set here}}\",\n      \"qty_availability\": \"{{calculated automatically, not set here}}\"\n    }\n  }\n}", "description": "Mustache template for transforming results from product query into S2S product, pricing and qty is added automatically and cannot be configured here. Source Variant and Product codes should not be changed.", "type": "string", "required": 1, "values": []}, "order_query": {"name": "sourcemeta_sagelive", "key": "order_query", "default": "", "description": "Sales Force composite queries for adding order", "type": "string", "required": 1, "values": []}, "order_template": {"name": "sourcemeta_sagelive", "key": "order_template", "default": "{\n          \"allOrNone\": true,\n          \"compositeRequest\": [\n            {\n              \"method\": \"POST\",\n              \"referenceId\": \"NewOrder\",\n              \"url\": \"/services/data/v42.0/sobjects/s2cor__Sage_INV_Trade_Document__c/\",\n              \"body\": {\n                \"s2cor__Account__c\": \"{{account.Id}}\",\n                \"s2cor__Trade_Document_Type__c\": \"a1w0O00000YvanJQAR\",\n                \"s2cor__BillingStreet__c\": \"{{system_order.billing_address.address1}} {{system_order.billing_address.address2}}\",\n                \"s2cor__BillingCity__c\": \"{{system_order.billing_address.city}}\",\n                \"s2cor__BillingState__c\": \"{{system_order.billing_address.province}}\",\n                \"s2cor__BillingPostalCode__c\": \"{{system_order.billing_address.zip}}\",\n                \"s2cor__BillingCountry__c\": \"{{system_order.billing_address.country}}\",\n                \"s2cor__ShippingStreet__c\": \"{{system_order.shipping_address.address1}} {{system_order.shipping_address.address2}}\",\n                \"s2cor__ShippingCity__c\": \"{{system_order.shipping_address.city}}\",\n                \"s2cor__ShippingState__c\": \"{{system_order.shipping_address.province}}\",\n                \"s2cor__ShippingPostalCode__c\": \"{{system_order.shipping_address.zip}}\",\n                \"s2cor__ShippingCountry__c\": \"{{system_order.shipping_address.country}}\",\n                \"s2cor__Description__c\": \"{{system_order.notes}}\",\n                \"s2cor__Reference__c\": \"{{system_order.channel_order_code}}\"\n              }\n            },\n            {{# system_order.line_items}}\n            {\n              \"method\": \"POST\",\n              \"referenceId\": \"NewLineItem{{id}}\",\n              \"url\": \"/services/data/v42.0/sobjects/s2cor__Sage_INV_Trade_Document_Item__c/\",\n              \"body\": {\n                \"s2cor__Quantity__c\": {{qty}},\n                \"s2cor__Unit_Price__c\": {{price}},\n                \"s2cor__Location_To__c\": \"a1z0O000002lMDOQA2\",\n                \"s2cor__Stock_Keeping_Unit__c\": \"{{source_variant_code}}\",\n                \"s2cor__Tax_Treatment__c\": \"a2A0O0000017xu8UAA\",\n                \"s2cor__Tax_Code__c\": \"a270O000001miEoQAI\",\n                \"s2cor__Product__c\": \"{{source_product_code}}\",\n                \"s2cor__Trade_Document__c\": \"@{NewOrder.id}\"\n              }\n            },\n            {{/ system_order.line_items}}\n            {{# system_order.shipping_lines}}\n            {\n              \"method\": \"POST\",\n              \"referenceId\": \"ShippingItem{{id}}\",\n              \"url\": \"/services/data/v42.0/sobjects/s2cor__Sage_INV_Trade_Document_Item__c/\",\n              \"body\": {\n                \"s2cor__Quantity__c\": 1,\n                \"s2cor__Unit_Price__c\": {{price}},\n                \"s2cor__Tax_Treatment__c\": \"a2A0O0000017xu8UAA\",\n                \"s2cor__Tax_Code__c\": \"a270O000001miEoQAI\",\n                \"s2cor__Product__c\": \"01t0O00000EWJHKQA5\",\n                \"s2cor__Trade_Document__c\": \"@{NewOrder.id}\"\n              }\n            },\n            {{/ system_order.shipping_lines}}\n            {\n              \"method\": \"GET\",\n              \"referenceId\": \"FetchOrder\",\n              \"url\": \"/services/data/v42.0/sobjects/s2cor__Sage_INV_Trade_Document__c/@{NewOrder.id}?fields=Name\"\n            }\n          ]\n        }", "description": "Mustache template for transforming order", "type": "string", "required": 1, "values": []}, "customer_template": {"name": "sourcemeta_sagelive", "key": "customer_template", "default": "{\n          \"allOrNone\": true,\n          \"compositeRequest\": [\n            {\n              \"method\": \"POST\",\n              \"referenceId\": \"NewAccount\",\n              \"url\": \"/services/data/v42.0/sobjects/Account\",\n              \"body\": {\n                \"Name\": \"{{system_order.billing_address.company}}\",\n                \"AccountNumber\": \"{{system_order.customer.id}}\",\n                \"BillingStreet\": \"{{system_order.billing_address.address1}} {{system_order.billing_address.address2}}\",\n                \"BillingCity\": \"{{system_order.billing_address.city}}\",\n                \"BillingState\": \"{{system_order.billing_address.province}}\",\n                \"BillingPostalCode\": \"{{system_order.billing_address.zip}}\",\n                \"BillingCountry\": \"{{system_order.billing_address.country}}\",\n                \"ShippingStreet\": \"{{system_order.shipping_address.address1}} {{system_order.shipping_address.address2}}\",\n                \"ShippingCity\": \"{{system_order.shipping_address.city}}\",\n                \"ShippingState\": \"{{system_order.shipping_address.province}}\",\n                \"ShippingPostalCode\": \"{{system_order.shipping_address.zip}}\",\n                \"ShippingCountry\": \"{{system_order.shipping_address.country}}\"\n              }\n            },\n            {\n              \"method\": \"POST\",\n              \"referenceId\": \"NewContact\",\n              \"url\": \"/services/data/v42.0/sobjects/Contact\",\n              \"body\": {\n                \"firstname\": \"{{system_order.customer.first_name}}\",\n                \"lastname\": \"{{system_order.customer.last_name}}\",\n                \"AccountId\": \"@{NewAccount.id}\",\n                \"Email\": \"{{system_order.customer.email}}\",\n                \"Phone\": \"{{system_order.billing_address.phone}}\"\n              }\n            },\n            {\n              \"method\": \"GET\",\n              \"referenceId\": \"FetchAccount\",\n              \"url\": \"/services/data/v42.0/sobjects/Account/@{NewAccount.id}?fields=Name,Id,AccountNumber\"\n            }\n          ]\n        }", "description": "Mustache template for composite query to create customer. It must have at least one FetchAccount referenceId that returns the customer account", "type": "string", "required": 1, "values": []}}, "sageone": {"order_created_date_now": {"name": "sourcemeta_sageone", "key": "order_created_date_now", "default": "true", "description": "Order created date will be set to now", "type": "string", "required": 0, "values": []}, "order_map": {"name": "sourcemeta_sageone", "key": "order_map", "default": "{   \"TaxReference\": \"{{sage_customer.TaxReference}}\",   \"DocumentNumber\": \"{{system_order.channel_order_code}}\",   \"Reference\": \"Literal Value\",   \"Message\": \"{{system_order.notes}}\",   \"Discount\": \"{{system_order.total_discount}}\",   \"PostalAddress01\": \"{{system_order.billing_address.first_name}} {{system_order.billing_address.last_name}}\",   \"PostalAddress02\": \"sample string 21\",   \"PostalAddress03\": \"sample string 22\",   \"PostalAddress04\": \"sample string 23\",   \"PostalAddress05\": \"sample string 24\",   \"DeliveryAddress01\": \"sample string 25\",   \"DeliveryAddress02\": \"sample string 26\",   \"DeliveryAddress03\": \"sample string 27\",   \"DeliveryAddress04\": \"sample string 28\",   \"DeliveryAddress05\": \"sample string 29\" }", "description": "Override order values using this map. The default values in this map are just an example, do not use them. The map should follow this format https://accounting.sageone.co.za/api/2.0.0/Help/Api/POST-TaxInvoice-Save_useSystemDocumentNumber. The order data used in the transform is the full S2S order, e.g. system_order.channel_order_code and sage customer e.g. sage_customer.TaxReference", "type": "string", "required": 0, "values": []}, "order_type": {"name": "sourcemeta_sageone", "key": "order_type", "default": "tax_invoice", "description": "Set this meta to specify one of the three order types to sync to this Sageone source: \"sales_order\", \"tax_invoice\", or \"quote\". When using sales_order, please note that you may specify the delivery date by setting the delivery_date_days meta.", "type": "string", "required": 0, "values": []}, "delivery_date_days": {"name": "sourcemeta_sageone", "key": "delivery_date_days", "default": "", "description": "(only when order_type = sales_order): Set the number of days to add to the created timestamp of the order in Stock2Shop.", "type": "string", "required": 0, "values": []}, "sync_mode": {"name": "sourcemeta_sageone", "key": "sync_mode", "default": "pull", "description": "push or pull", "type": "string", "required": 1, "values": []}, "param_multicurrency_enabled": {"name": "sourcemeta_sageone", "key": "param_multicurrency_enabled", "default": "false", "description": "Enable syncing multiple currencies using the multi currency module on Sage. param_currency_code will determine what currency to use.", "type": "dropdown", "required": 0, "values": []}, "param_currency_code": {"name": "sourcemeta_sageone", "key": "param_currency_code", "default": "", "description": "Set the currency code if multicurrency_enabled is true", "type": "string", "required": 0, "values": []}, "customer_map": {"name": "sourcemeta_sageone", "key": "customer_map", "default": "{\n  \"Name\": \"{{billing_address.company}}\"\n}", "description": "Map used to create new customers, see Sage One API docs", "type": "string", "required": 0, "values": []}, "cron_get_products_schedule": {"name": "sourcemeta_sageone", "key": "cron_get_products_schedule", "default": "x 00 * * * *", "description": "Basic format is \"MM HH * * *\"", "type": "string", "required": 0, "values": []}, "company_id": {"name": "sourcemeta_sageone", "key": "company_id", "default": "", "description": "SageOne company id (Can be found by using the following: https://app.stock2shop.com/sageone/setup/)", "type": "string", "required": 1, "values": []}, "create_order_enabled": {"name": "sourcemeta_sageone", "key": "create_order_enabled", "default": "false", "description": "Allow creating order on source", "type": "string", "required": 1, "values": []}, "get_images_limit": {"name": "sourcemeta_sageone", "key": "get_images_limit", "default": "1", "description": "Number of images to fetch at a time", "type": "string", "required": 1, "values": []}, "param_get_images_enabled": {"name": "sourcemeta_sageone", "key": "param_get_images_enabled", "default": "false", "description": "Fetch images for this source, only the following formats are supported:  \".jpg\" \".jpeg\" \".png\"", "type": "string", "required": 1, "values": []}, "get_products_limit": {"name": "sourcemeta_sageone", "key": "get_products_limit", "default": "100", "description": "Number of products to fetch at a time", "type": "string", "required": 1, "values": []}, "param_get_products_filter": {"name": "sourcemeta_sageone", "key": "param_get_products_filter", "default": "Physical eq true", "description": "OData protocol filter to be used with get products", "type": "string", "required": 1, "values": []}, "customer_reference_field": {"name": "sourcemeta_sageone", "key": "customer_reference_field", "default": "channel_order_code", "description": "The S2S order field to use when adding a customer reference to the order", "type": "string", "required": 0, "values": []}, "order_by_sku": {"name": "sourcemeta_sageone", "key": "order_by_sku", "default": "false", "description": "If true, we look up the sku on <PERSON> for each line item. Used if there is no previous link for this product", "type": "string", "required": 0, "values": []}, "param_negative_stock_disabled": {"name": "sourcemeta_sageone", "key": "param_negative_stock_disabled", "default": "false", "description": "Do not allow negative stock for this source, if set to true, stock must exist in source or order will fail", "type": "string", "required": 0, "values": []}, "param_due_date_days": {"name": "sourcemeta_sageone", "key": "param_due_date_days", "default": "30", "description": "This will set the due date to x days after the created date", "type": "string", "required": 0, "values": []}, "param_create_customer_enabled": {"name": "sourcemeta_sageone", "key": "param_create_customer_enabled", "default": "false", "description": "Allowed to add new customers to source when no default customer code is given", "type": "string", "required": 0, "values": []}, "param_channel_order_code_enabled": {"name": "sourcemeta_sageone", "key": "param_channel_order_code_enabled", "default": "false", "description": "Use the channel order code as the Invoice or Quote code on Sage One", "type": "string", "required": 0, "values": []}, "param_default_customer_code": {"name": "sourcemeta_sageone", "key": "param_default_customer_code", "default": "abc", "description": "default customer to use when no source customer code provided", "type": "string", "required": 0, "values": []}, "param_default_shipping_code": {"name": "sourcemeta_sageone", "key": "param_default_shipping_code", "default": "SHIP001", "description": "used when raising an order", "type": "string", "required": 0, "values": []}, "param_invoice_body": {"name": "sourcemeta_sageone", "key": "param_invoice_body", "default": "This invoice is for demo purposes only and must be ignored!", "description": "invoice body", "type": "string", "required": 0, "values": []}, "param_invoice_subject": {"name": "sourcemeta_sageone", "key": "param_invoice_subject", "default": "Stock2Shop Demo Invoice", "description": "invoice subject", "type": "string", "required": 0, "values": []}, "param_sales_rep_id": {"name": "sourcemeta_sageone", "key": "param_sales_rep_id", "default": "1234", "description": "used when raising an order", "type": "string", "required": 1, "values": []}, "param_send_invoice_enabled": {"name": "sourcemeta_sageone", "key": "param_send_invoice_enabled", "default": "true", "description": "Send invoice to customer straight away", "type": "string", "required": 0, "values": []}, "param_tax_code": {"name": "sourcemeta_sageone", "key": "param_tax_code", "default": "1234", "description": "used when raising an order, get id from API", "type": "string", "required": 1, "values": []}, "param_tax_exempt_code": {"name": "sourcemeta_sageone", "key": "param_tax_exempt_code", "default": "0", "description": "used when raising an order, default on pastel is 0", "type": "string", "required": 1, "values": []}, "param_use_customer_address": {"name": "sourcemeta_sageone", "key": "param_use_customer_address", "default": "true", "description": "use the customer address set in Pastel", "type": "string", "required": 0, "values": []}, "param_product_field_map": {"name": "sourcemeta_sageone", "key": "param_product_field_map", "default": "{\n\"source\": {\n\"source_product_code\": \"TextUserField1\",\n\"product_active\": \"Active\"\n},\n\"product\": {\n\"collection\": \"Category.Description\",\n\"title\": \"Description\",\n\"product_type\": \"TextUserField2\",\n\"body_html\": \"\",\n\"tags\": \"\",\n\"vendor\": \"\",\n\"options\": [\n{\n\"value\": \"\",\n\"name\": \"Options\",\n\"position\": 1\n}\n],\n\"variants\": {\n\"source_variant_code\": \"ID\",\n\"option1\": \"TextUserField3\",\n\"qty\": \"QuantityOnHand\",\n\"barcode\": \"\",\n\"grams\": \"\",\n\"sku\": \"Code\",\n\"price\": \"PriceExclusive\",\n\"price_tiers\": [],\n\"inventory_management\": true\n}\n}\n}", "description": "If you want to use ':' in the option name as the key value for options, do not include the product->options property. Changes made to Category names and Price Tiers on SageOne will only Update in Stock2Shop if the Sync token is set back and a fetch is performed.", "type": "string", "required": 1, "values": []}, "password": {"name": "sourcemeta_sageone", "key": "password", "default": "", "description": "SageOne password", "type": "string", "required": 1, "values": []}, "queue_fetch_images": {"name": "sourcemeta_sageone", "key": "queue_fetch_images", "default": "false", "description": "Queue fetch_images item after processing sync_product. Must be set to False in Used Config, if removed, it will be set to true by default.", "type": "string", "required": 1, "values": []}, "username": {"name": "sourcemeta_sageone", "key": "username", "default": "", "description": "Sage One username", "type": "string", "required": 1, "values": []}}, "xero": {"sync_mode": {"name": "sourcemeta_xero", "key": "sync_mode", "default": "pull", "description": "push or pull", "type": "string", "required": 1, "values": []}, "order_type": {"name": "sourcemeta_xero", "key": "order_type", "default": "", "description": "The order type you would like to raise in Xero. i.e Quote or Invoice (default)", "type": "string", "required": 0, "values": []}, "default_order_status": {"name": "sourcemeta_xero", "key": "default_order_status", "default": "DRAFT", "description": "The status the order should be raise as into Xero. e.g. DRAFT (default), SUBMITTED, AUTHORISED, etc.", "type": "string", "required": 0, "values": []}, "default_customer_code": {"name": "sourcemeta_xero", "key": "default_customer_code", "default": "", "description": "Default customer to use when no source customer code is provided", "type": "string", "required": 0, "values": []}, "create_customer_enabled": {"name": "sourcemeta_xero", "key": "create_customer_enabled", "default": "true", "description": "Are we allowed to create a customer when raising an order", "type": "string", "required": 0, "values": []}, "default_shipping_code": {"name": "sourcemeta_xero", "key": "default_shipping_code", "default": "", "description": "The shipping code to use when raising orders", "type": "string", "required": 0, "values": []}, "default_tax_code": {"name": "sourcemeta_xero", "key": "default_tax_code", "default": "", "description": "Default tax code to use for line items with tax line code of 'taxed'", "type": "string", "required": 1, "values": []}, "default_tax_code_exempt": {"name": "sourcemeta_xero", "key": "default_tax_code_exempt", "default": "NONE", "description": "Default tax code to use for line items with tax line code of 'exempt'", "type": "string", "required": 1, "values": []}, "default_tax_code_shipping": {"name": "sourcemeta_xero", "key": "default_tax_code_shipping", "default": "", "description": "Default tax code to use for shipping lines with tax line code 'taxed'", "type": "string", "required": 1, "values": []}, "default_tax_code_exempt_shipping": {"name": "sourcemeta_xero", "key": "default_tax_code_exempt_shipping", "default": "NONE", "description": "Default tax code to use for shipping lines with tax line code 'exempt'", "type": "string", "required": 1, "values": []}, "xero_quote_terms": {"name": "sourcemeta_xero", "key": "xero_quote_terms", "default": "", "description": "The terms used when creating a quote. Max 40 characters", "type": "string", "required": 0, "values": []}, "xero_quote_expiry_days": {"name": "sourcemeta_xero", "key": "xero_quote_expiry_days", "default": "", "description": "The number of day a quote is valid for. Default is zero.", "type": "number", "required": 0, "values": []}, "oauth2_refresh_token": {"name": "sourcemeta_xero", "key": "oauth2_refresh_token", "default": "x", "description": "The token used to request a new access token", "type": "string", "required": 1, "values": []}, "oauth2_access_token": {"name": "sourcemeta_xero", "key": "oauth2_access_token", "default": "x", "description": "The token used to call the API", "type": "string", "required": 1, "values": []}, "xero_tenant_id": {"name": "sourcemeta_xero", "key": "xero_tenant_id", "default": "x", "description": "The ID for the tenant that will be connected to", "type": "string", "required": 1, "values": []}, "order_map": {"name": "sourcemeta_xero", "key": "order_map", "default": "{   \"Type\": \"ACCREC\",   \"Contact\": {     \"ContactNumber\": \"{{system_order.customer.id}}\"   },   \"Reference\": \"{{params.customer_reference}}\",   \"Status\": \"DRAFT\",   \"Date\": \"{{system_order.created}}\",   \"DueDate\": \"{{system_order.created}}\",   \"LineAmountTypes\": \"Exclusive\",   \"LineItems\": [     {{#system_order.line_items}}     {{^first}},{{/first}}     {       \"Description\": \"{{title}}\",       \"Quantity\": \"{{qty}}\",       \"UnitAmount\": \"{{price}}\",       \"ItemCode\": \"{{sku}}\",       \"DiscountRate\": \"0\"     }     {{/system_order.line_items}}   ] }", "description": "Mapping between S2S order and Xero order. Customer (contact) can be created on the fly. The contact must have Name or ContactID or ContactNumber set, see xero documentation for invoices. https://developer.xero.com/documentation/api/invoices", "type": "string", "required": 0, "values": []}, "cron_get_products_schedule": {"name": "sourcemeta_xero", "key": "cron_get_products_schedule", "default": "x 00 * * * *", "description": "Basic format is \"MM HH * * *\"", "type": "string", "required": 0, "values": []}, "get_products_once": {"name": "sourcemeta_xero", "key": "get_products_once", "default": "true", "description": "Only fetch products once, do not page through products, Xero does not support paging products. This must be set to true for all Xero sources.", "type": "string", "required": 1, "values": []}, "create_order_enabled": {"name": "sourcemeta_xero", "key": "create_order_enabled", "default": "false", "description": "Allow creating order on source", "type": "string", "required": 1, "values": []}, "get_images_limit": {"name": "sourcemeta_xero", "key": "get_images_limit", "default": "1", "description": "Number of images to fetch at a time", "type": "string", "required": 1, "values": []}, "get_images_enabled": {"name": "sourcemeta_xero", "key": "get_images_enabled", "default": "false", "description": "Fetch images for this source", "type": "string", "required": 1, "values": []}, "get_products_limit": {"name": "sourcemeta_xero", "key": "get_products_limit", "default": "100", "description": "Number of products to fetch at a time", "type": "string", "required": 1, "values": []}, "product_field_map": {"name": "sourcemeta_xero", "key": "product_field_map", "default": "{   \"source\": {     \"source_product_code\": \"{{Code}}\",     \"product_active\": \"{{IsSold}}\"   },   \"product\": {     \"collection\": \"s\",     \"title\": \"{{Name}}\",     \"product_type\": \"\",     \"body_html\": \"{{Description}}\",     \"tags\": \"\",     \"vendor\": \"\",     \"options\": [],     \"meta\": [],     \"variants\": {       \"source_variant_code\": \"{{ItemID}}\",       \"qty\": \"{{QuantityOnHand}}\",       \"barcode\": \"\",       \"grams\": \"\",       \"sku\": \"{{Code}}\",       \"price\": \"{{SalesDetails.UnitPrice}}\",       \"inventory_management\": \"{{IsTrackedAsInventory}}\"     }   } }", "description": "Field mapping between S2S and flat file, use handlebar tags for variables {{}}", "type": "string", "required": 1, "values": []}, "product_where_clause": {"name": "sourcemeta_xero", "key": "product_where_clause", "default": "SalesDetails.UnitPrice>0", "description": "Where clause to limit products returned, see xero api docs", "type": "string", "required": 0, "values": []}, "queue_fetch_images": {"name": "sourcemeta_xero", "key": "queue_fetch_images", "default": "false", "description": "Queue fetch_images item after processing sync_product", "type": "string", "required": 0, "values": []}}}}