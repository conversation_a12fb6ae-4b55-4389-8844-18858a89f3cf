<script lang="ts">
  import { OptionInputType } from '@/components/types';
  import IconDelete from '@/components/Icons/IconDelete.svelte';
  import Btn from '@/components/form/Btn.svelte';
  import NumberInput from '@/components/Controls/NumberInput.svelte';

  export let inputs;

  function deleteFilter() {
    alert('delete filter');
  }
</script>

<div>
  {#each inputs as { name, type, value, id, unit }}
    <div class="mb-4 w-full last:mb-0">
      <div class="mb-2 text-regular leading-4 text-neutral-500">{name}</div>
      <div class="flex w-full">
        {#if type === OptionInputType.TEXT}
          <input
            type="text"
            class="block h-10 w-full rounded border border-neutral-200 p-3
            text-regular text-neutral-700 focus:border-neutral-200 focus:shadow-none focus:ring-0"
            {value}
          />
        {:else if type === OptionInputType.TEXTAREA}
          <textarea
            class="block h-24 w-full rounded border border-neutral-200 p-3
            text-regular text-neutral-700 focus:border-neutral-200 focus:shadow-none focus:ring-0"
            >{value}</textarea
          >
        {:else}
          <NumberInput {unit} {value} id={id || name} />
        {/if}
        <div class="ml-4 mt-2">
          <Btn
            size="small"
            variant="ghost"
            Prefix={IconDelete}
            iconSize="small"
            class="!text-neutral-800 hover:!bg-neutral-100"
            on:click={deleteFilter}
          />
        </div>
      </div>
    </div>
  {/each}
</div>
