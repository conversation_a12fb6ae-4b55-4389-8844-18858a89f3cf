<script lang="ts">
  import { type LogHit } from '@/models/logs/hits';
  import type { QueueInstructions } from '@/models/queue/instructions';
  import type { Config } from '@/models/config/config';
  import type { TableRow } from '@/components/Controls/Table/table';
  import LogTitle from '@/components/Logs/LogTitle.svelte';

  export let row: TableRow;
  $: hit = {
    _source: {
      origin: '/stock2shop-app',
      entity: row.search_mode,
      tags: [row.instruction],
      source_id: row.source_id,
      channel_id: row.channel_id,
      message: row.log_message
    }
  } as LogHit;
  export let instructions: QueueInstructions;
  export let config: Config;
</script>

<!--
@component Uses log title
-->
<LogTitle {hit} {instructions} {config} />
