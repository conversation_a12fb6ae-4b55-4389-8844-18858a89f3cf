<script lang="ts">
  import type { TableRow } from '@/components/Controls/Table/table';
  import {
    getLocalShortDayTime,
    getReadableStringFromNow,
    hasBeenXDays,
    getLocalCompactDateTime
  } from '$lib/date-utils';
  import Chip from '@/components/Controls/Tag/Chip.svelte';

  export let row: TableRow;

  const localDate = getLocalCompactDateTime(row.created as number | string, false);
  const localShortDayTime = getLocalShortDayTime(row.created as number | string);
  const timePassed = getReadableStringFromNow(row.created as number | string);
  const hasBeen3Days = hasBeenXDays(3, row.created as string);
</script>

<!--
@component
table cell to display short date
-->
<div class="flex items-center gap-1">
  <p class="leading-tracking-tight w-max">
    {hasBeen3Days ? localDate : localShortDayTime}
  </p>
  <Chip noShrink label={timePassed} />
</div>
