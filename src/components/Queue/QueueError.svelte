<script lang="ts">
  import type { QueueInstructions } from '@/models/queue/instructions';
  import type { QueueItems } from '@/models/queue/item';
  import QueueErrorTable from '@/components/Queue/QueueErrorTable.svelte';
  import type { Config } from '@/models/config/config';
  import IconRefresh from '@/components/Icons/IconRefresh.svelte';
  import { queueKeys } from '@/queries/queue.query';
  import { useQueryClient } from '@tanstack/svelte-query';
  import type { QueueSearchParams } from '@/models/queue/queue-search-params';
  import Button from '@/components/Controls/Button.svelte';
  import capitalize from 'lodash.capitalize';
  import { goto } from '$app/navigation';
  import SearchPageLayout from '@/components/Layout/SearchPageLayout.svelte';
  import TableSearchLayout from '@/components/Layout/TableSearchLayout.svelte';
  import Facets from '@/components/ES/Facets.svelte';
  import type { FilterTerms } from '@/models/es/filter';
  import { queueFacets } from '@/components/Queue';

  export let isLoading: boolean;
  export let failed: QueueItems | undefined;
  export let instructions: QueueInstructions | undefined;
  export let config: Config | undefined;
  export let searchParams: QueueSearchParams;
  export let filterTerms: FilterTerms;

  const queryClient = useQueryClient();
  $: invalidateQueries = () => {
    queryClient.invalidateQueries({ queryKey: queueKeys.items() });
  };

  $: {
    if (filterTerms.mode === 'processing') {
      goto('/queue');
    }
  }
</script>

<SearchPageLayout title="{capitalize(searchParams.mode)} Queue Errors">
  <!-- These svelte:fragments are not needed. slot="someSlotName" can be passed directly to the component. todo: check if anyone has a preference to do thing this way -->
  <svelte:fragment slot="sidebar">
    <Facets isLoading={false} facets={queueFacets} bind:filterTerms />
  </svelte:fragment>
  <svelte:fragment slot="header">
    <Button
      size="medium"
      variant="gray-outline"
      icon={IconRefresh}
      iconPosition="right"
      on:click={invalidateQueries}
    >
      Refresh
    </Button>
  </svelte:fragment>

  <svelte:fragment slot="content">
    <TableSearchLayout>
      <svelte:fragment slot="table">
        <QueueErrorTable {isLoading} {failed} {instructions} {config} bind:searchParams />
      </svelte:fragment>
    </TableSearchLayout>
  </svelte:fragment>
</SearchPageLayout>
