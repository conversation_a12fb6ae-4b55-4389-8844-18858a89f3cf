<script lang="ts">
  import type { TableProps } from '@/components/Controls/Table/table';
  import IconDelete from '@/components/Icons/IconDelete.svelte';
  import { getSelectedRows } from '@/components/Controls/Table/table';
  import { createQueueResetMutation, createQueueSkipMutation } from '@/mutations/queue.mutation';
  import IconRevert from '@/components/Icons/IconRevert.svelte';
  import { useQueryClient } from '@tanstack/svelte-query';
  import { queueKeys } from '@/queries/queue.query';
  import { reportKeys } from '@/queries/reports.query';
  import Button from '@/components/Controls/Button.svelte';
  import { confirmModal } from '@/components/Modal/modal';

  export let tableProps: TableProps;
  const queueSkipMutation = createQueueSkipMutation();
  const queueResetMutation = createQueueResetMutation();
  const queryClient = useQueryClient();

  const invalidateQueries = () => {
    queryClient.invalidateQueries({ queryKey: queueKeys.summary() });
    queryClient.invalidateQueries({ queryKey: queueKeys.items() });
    queryClient.invalidateQueries({ queryKey: reportKeys.blockedQueue() });
    queryClient.invalidateQueries({ queryKey: reportKeys.failedQueue() });
  };

  // select values
  $: selected = getSelectedRows(tableProps);

  const handleSkip = async () => {
    if (
      !(await confirmModal({
        title: 'Delete queue items',
        description: 'Are you sure you want to delete the selected queue items?',
        actionText: 'Delete'
      }))
    ) {
      return;
    }
    tableProps.isLoading = true;
    for await (const item of selected) {
      await $queueSkipMutation.mutateAsync(item.id as number);
    }
    invalidateQueries();
  };
  const handleRetry = async () => {
    if (
      !(await confirmModal({
        title: 'Retry running queue items',
        description: 'Are you sure you want to retry running the selected queue items?',
        actionText: 'Retry'
      }))
    ) {
      return;
    }
    tableProps.isLoading = true;
    for await (const item of selected) {
      await $queueResetMutation.mutateAsync(item.id as number);
    }
    invalidateQueries();
  };
</script>

<div class="flex gap-2">
  <div>{selected.length} items selected</div>
  <Button
    tooltip="Delete selected items"
    size="small"
    variant="gray"
    icon={IconDelete}
    on:click={handleSkip}
  />
  <Button
    tooltip="Re-process selected items"
    size="small"
    variant="gray"
    icon={IconRevert}
    on:click={handleRetry}
  />
</div>
