<script lang="ts">
  import Table from '@/components/Controls/Table/Table.svelte';
  import type { TableProps, TableRow } from '@/components/Controls/Table/table';
  import QueueErrorTableAction from '@/components/Queue/QueueErrorTableAction.svelte';
  import type { QueueItems } from '@/models/queue/item';
  import QueueErrorTableTitleCell from '@/components/Queue/QueueErrorTableTitleCell.svelte';
  import QueueErrorTableDateCell from '@/components/Queue/QueueErrorTableDateCell.svelte';
  import QueueErrorLogMessage from '@/components/Queue/QueueErrorLogMessage.svelte';
  import type { QueueInstructions } from '@/models/queue/instructions';
  import type { Config } from '@/models/config/config';
  import type { QueueSearchParams } from '@/models/queue/queue-search-params';
  import QueueModal from '@/components/Queue/QueueModal.svelte';

  export let isLoading: boolean;
  export let failed: QueueItems | undefined;
  export let instructions: QueueInstructions | undefined;
  export let config: Config | undefined;
  export let searchParams: QueueSearchParams;

  let selectedID: number | undefined;
  let openModal = false;

  const rowClickEventHandler = (row: TableRow) => {
    selectedID = row.id as number;
    openModal = true;
  };

  /**
   * Set url params from pagination values and redirect
   */
  const redirect = (props: TableProps) => {
    if (props.pagination) {
      searchParams.offset = (
        (props.pagination.pageNumber - 1) *
        props.pagination.pageSize
      ).toString();
      searchParams.limit = props.pagination.pageSize.toString();
    }
  };

  $: tableProps = <TableProps>{
    header: true,
    selectable: true,
    selectableComponent: QueueErrorTableAction,
    isLoading: isLoading,
    isLoadingSkeletonCount: 5,
    rowHover: true,
    rowPointer: true,
    rowKey: 'id',
    rows: [],
    columns: [
      {
        columnKey: 'created',
        label: 'Date',
        align: 'left',
        component: QueueErrorTableDateCell
      },
      {
        columnKey: 'title',
        label: 'Title',
        align: 'left',
        component: QueueErrorTableTitleCell,
        componentProps: {
          config: config,
          instructions: instructions
        }
      },
      {
        columnKey: 'log_message',
        label: 'Message',
        align: 'left',
        component: QueueErrorLogMessage
      }
    ],
    onPagination: redirect,
    onRowClick: rowClickEventHandler
  };

  // I suspect that this can be done once on line 41. we then wouldn't need to worry about the index of the columns if we ever change the order
  // set dynamic properties on tableProps
  $: if (config && instructions) {
    tableProps.isLoading = isLoading;
    tableProps.pagination = {
      pageNumber: Math.ceil(parseInt(searchParams.offset) / parseInt(searchParams.limit)) + 1,
      pageSize: parseInt(searchParams.limit),
      pageSizeOptions: [20, 50],
      total: failed?.results.total ?? 0
    };
    if (tableProps.columns) {
      tableProps.columns[1] = {
        ...tableProps.columns[1],
        component: QueueErrorTableTitleCell,
        componentProps: {
          config: config,
          instructions: instructions
        }
      };
    }
    if (failed) {
      tableProps.rows = failed.system_items.map((item) => item as unknown as TableRow);
    }
  }
</script>

<Table {tableProps} />

{#if selectedID && openModal}
  <QueueModal id={selectedID} bind:open={openModal} />
{/if}
