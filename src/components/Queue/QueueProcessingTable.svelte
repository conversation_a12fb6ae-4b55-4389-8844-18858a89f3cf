<script lang="ts">
  import type { QueueSummary } from '@/models/queue/summary';
  import Table from '@/components/Controls/Table/Table.svelte';
  import type { TableProps, TableRow } from '@/components/Controls/Table/table';
  import QueueProcessingTableAction from '@/components/Queue/QueueProcessingTableAction.svelte';
  import { getInstruction, type QueueInstructions } from '@/models/queue/instructions';
  import QueueProcessingTableDescriptionCell from '@/components/Queue/QueueProcessingTableDescriptionCell.svelte';
  import type { Config } from '@/models/config/config';

  export let isLoading: boolean;
  export let summary: QueueSummary | undefined;
  export let instructions: QueueInstructions | undefined;
  export let config: Config | undefined;

  const tableProps = <TableProps>{
    header: true,
    selectable: true,
    selectableComponent: QueueProcessingTableAction,
    isLoading: isLoading,
    isLoadingSkeletonCount: 5,
    rowKey: ['channel_description', 'instruction'],
    rows: [],
    columns: [
      {
        columnKey: 'description',
        label: 'Item',
        align: 'left'
      },
      {
        columnKey: 'count',
        label: 'Count',
        align: 'right'
      }
    ]
  };

  // set dynamic properties on tableProps
  $: if (config && instructions) {
    tableProps.isLoading = isLoading;
    if (tableProps?.columns) {
      tableProps.columns[0] = {
        ...tableProps.columns[0],
        component: QueueProcessingTableDescriptionCell,
        componentProps: {
          config: config,
          instructions: instructions
        }
      };
    }
    if (summary) {
      tableProps.rows =
        summary?.system_queue_summary.map((item) => {
          const row = item as unknown as TableRow;
          row.mode =
            getInstruction(instructions?.system_queue_instructions ?? [], item.instruction)?.mode ??
            '';
          return row;
        }) ?? [];
    }
  }
</script>

<Table {tableProps} />
