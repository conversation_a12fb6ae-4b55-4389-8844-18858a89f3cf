import { FACET_TYPE_RADIO } from '@/components/ES/Facets';
import type { Facet } from '@/models/es/facet';

export const queueFacets: Facet[] = [
  {
    key: 'mode',
    title: 'Status',
    type: FACET_TYPE_RADIO,
    options: [
      { label: 'Blocking Errors', value: 'blocking', default: true },
      { label: 'Non-blocking Errors', value: 'non-blocking' },
      { label: 'Processing', value: 'processing' }
    ]
  }
];
