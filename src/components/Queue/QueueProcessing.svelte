<script lang="ts">
  import type { QueueInstructions } from '@/models/queue/instructions';
  import type { Config } from '@/models/config/config';
  import IconRefresh from '@/components/Icons/IconRefresh.svelte';
  import { queueKeys } from '@/queries/queue.query';
  import { useQueryClient } from '@tanstack/svelte-query';
  import Button from '@/components/Controls/Button.svelte';
  import QueueProcessingTable from '@/components/Queue/QueueProcessingTable.svelte';
  import type { QueueSummary } from '@/models/queue/summary';
  import { goto } from '$app/navigation';
  import SearchPageLayout from '@/components/Layout/SearchPageLayout.svelte';
  import TableSearchLayout from '@/components/Layout/TableSearchLayout.svelte';
  import Facets from '@/components/ES/Facets.svelte';
  import type { FilterTerms } from '@/models/es/filter';
  import { queueFacets } from '@/components/Queue';

  export let isLoading: boolean;
  export let summary: QueueSummary | undefined;
  export let instructions: QueueInstructions | undefined;
  export let config: Config | undefined;

  const queryClient = useQueryClient();
  $: invalidateQueries = () => {
    queryClient.invalidateQueries({ queryKey: queueKeys.summary() });
  };

  let filterTerms: FilterTerms = { mode: 'processing' };

  $: {
    if (filterTerms.mode === 'blocking') {
      goto('/queue/error?mode=blocking');
    }
    if (filterTerms.mode === 'non-blocking') {
      goto('/queue/error?mode=non-blocking');
    }
  }
</script>

<SearchPageLayout title="Unprocessed Queue Items">
  <svelte:fragment slot="sidebar">
    <Facets isLoading={false} facets={queueFacets} bind:filterTerms />
  </svelte:fragment>
  <svelte:fragment slot="header">
    <Button
      size="medium"
      variant="gray-outline"
      icon={IconRefresh}
      iconPosition="right"
      on:click={invalidateQueries}
    >
      Refresh
    </Button>
  </svelte:fragment>
  <svelte:fragment slot="content">
    <TableSearchLayout>
      <svelte:fragment slot="table">
        <QueueProcessingTable {isLoading} {summary} {instructions} {config} />
      </svelte:fragment>
    </TableSearchLayout>
  </svelte:fragment>
</SearchPageLayout>
