<script lang="ts">
  import type { TableRow } from '@/components/Controls/Table/table';
  import type { QueueInstructions } from '@/models/queue/instructions';
  import type { Config } from '@/models/config/config';
  import { getSource, getChannel } from '@/models/config/config';

  export let row: TableRow;
  export let config: Config;
  export let instructions: QueueInstructions;

  // fetches description from a queue summary and replaces placeholders
  const getInstructionDescription = () => {
    const source =
      getSource(config, (row.source_id as number) ?? undefined)?.description ?? 'Source';
    const channel =
      getChannel(config, (row.channel_id as number) ?? undefined)?.description ?? 'Channel';
    let description = getDescription(row.instruction as string) ?? 'unknown';
    description = description.replace('{{source_description}}', source);
    description = description.replace('{{channel_description}}', channel);
    return description;
  };

  const getDescription = (instruction: string): string | undefined => {
    const results = instructions.system_queue_instructions.filter(
      (ins) => ins.instruction === instruction
    );
    if (results.length > 0) {
      return results[0].description;
    }
    return undefined;
  };
</script>

<!--
@component
table cell to display description based on instruction
-->
{getInstructionDescription()}
