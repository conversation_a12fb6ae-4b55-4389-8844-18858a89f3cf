<script lang="ts">
  import ModalInternal from '@/components/Modal/ModalInternal.svelte';
  import Icon from '@/components/Icons/Icon.svelte';
  import IconList from '@/components/Icons/IconList.svelte';
  import Skeleton from '@/components/Loader/Skeleton.svelte';
  import { createQueueItemsQuery } from '@/queries/queue.query';
  import ObjToJSON from '@/components/Link/ObjToJSON.svelte';

  export let open: boolean;
  export let id: number;

  $: item = createQueueItemsQuery({
    limit: '1',
    offset: '0',
    id: id.toString()
  });

  $: isLoading = $item.isFetching;
  $: title =
    !$item.data || $item.data.system_items.length < 1 ? '' : $item.data.system_items[0].instruction;
  $: obj = $item.data?.system_items[0] ?? undefined;
</script>

<!--
@component
Loads a queue item in a modal window.
-->
<ModalInternal bind:open position="right" size="full">
  <div slot="header-prefix">
    <div class="flex items-center gap-4">
      <Icon IconComponent={IconList} size="i-6" />
      {title}
    </div>
  </div>
  <div slot="body" class="rounded bg-neutral-100 text-neutral-500">
    {#if isLoading}
      <Skeleton shimmer class="h-10 w-full" />
    {:else}
      <pre class="whitespace-pre-wrap p-6"><ObjToJSON {obj} /></pre>
    {/if}
  </div>
</ModalInternal>
