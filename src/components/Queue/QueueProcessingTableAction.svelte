<script lang="ts">
  import type { TableProps } from '@/components/Controls/Table/table';
  import IconDelete from '@/components/Icons/IconDelete.svelte';
  import { getSelectedRows } from '@/components/Controls/Table/table';
  import type { QueueClearParams } from '@/models/queue/summary';
  import { createQueueMutation } from '@/mutations/queue.mutation';
  import { useQueryClient } from '@tanstack/svelte-query';
  import { queueKeys } from '@/queries/queue.query';
  import { reportKeys } from '@/queries/reports.query';
  import Button from '@/components/Controls/Button.svelte';
  import { confirmModal } from '@/components/Modal/modal';

  export let tableProps: TableProps;
  const queueMutation = createQueueMutation();
  const queryClient = useQueryClient();
  const invalidateQueries = () => {
    queryClient.invalidateQueries({ queryKey: queueKeys.summary() });
    queryClient.invalidateQueries({ queryKey: queueKeys.items() });
    queryClient.invalidateQueries({ queryKey: reportKeys.blockedQueue() });
    queryClient.invalidateQueries({ queryKey: reportKeys.failedQueue() });
  };

  // select values
  $: selected = getSelectedRows(tableProps);

  const handleDelete = async () => {
    if (
      !(await confirmModal({
        title: 'Delete queue items',
        description: 'Are you sure you want to delete the selected queue items?',
        actionText: 'Delete'
      }))
    ) {
      return;
    }

    tableProps.isLoading = true;

    // loop through all selected items and wait for each promise to resolve before the next iteration
    for await (const item of selected) {
      const param: QueueClearParams = {
        mode: item.mode
      } as QueueClearParams;
      if (item.instruction) {
        param.instruction = item.instruction as string;
      }
      if (item.channel_id) {
        param.channel_id = item.channel_id as number;
      }
      if (item.source_id) {
        param.source_id = item.source_id as number;
      }
      await $queueMutation.mutateAsync(param);
    }
    invalidateQueries();
  };
</script>

<div class="flex gap-2">
  <div>{selected.length} items selected</div>
  <div class="h-5 w-7">
    <Button
      tooltip="Delete selected items"
      size="small"
      variant="gray"
      icon={IconDelete}
      on:click={handleDelete}
    />
  </div>
</div>
