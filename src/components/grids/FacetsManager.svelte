<script lang="ts">
  import type { FacetWithIndex } from '@/lib/es/types';
  import FilterWrapper from '@/components/Filter/FilterWrapper.svelte';
  import Facet from '@/lib/es/Facet.svelte';
  import FiltersReset from '@/lib/es/FiltersReset.svelte';
  import { configPersistent, ConfigSystem } from '@/lib/s2sStore';
  import Btn from '@/components/form/Btn.svelte';
  import FilterBarHeader from '@/components/Filter/FilterBarHeader.svelte';
  import FacetConfigurator from '@/components/Configurators/FacetConfigurator/FacetConfigurator.svelte';
  import { flip } from 'svelte/animate';
  import clickOutside from '@/utils/actions/handleClickOutside';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';

  export let index: string;
  export let facetsArray: FacetWithIndex[];
  export let onSelected: (facetKeys: string[], newMetaFacets: FacetWithIndex[]) => void;

  let facetCollapsed: Record<string, boolean>;
  $: facetCollapsed = facetsArray
    .map((f) => f.key)
    .reduce((map, key) => ({ ...map, [key]: false }), {});
  $: areAllCollapsed = Object.values(facetCollapsed).reduce((a, b) => a && b, true);

  const toggleFacet = (facetKey) => {
    facetCollapsed = { ...facetCollapsed, [facetKey]: !facetCollapsed[facetKey] };
  };

  const onCollapseAll = () => {
    Object.keys(facetCollapsed).forEach((key) => {
      facetCollapsed[key] = !areAllCollapsed;
    });
    facetCollapsed = { ...facetCollapsed };
  };

  let showConfigurator = false;
  let container: HTMLElement;
  const toggleConfigurator = (e: CustomEvent<any>) => {
    e.preventDefault();
    e.stopImmediatePropagation();
    showConfigurator = !showConfigurator;
  };
  const hideContainer = () => {
    showConfigurator = false;
  };
</script>

<div
  class="duration- mr-6 h-full min-w-[260px] max-w-[260px] overflow-y-auto rounded bg-white shadow transition"
  class:grow={showConfigurator}
>
  <div bind:this={container}>
    <div use:clickOutside={hideContainer}>
      <FilterBarHeader {onCollapseAll} collapsed={areAllCollapsed} popupVisible={showConfigurator}>
        <FacetConfigurator
          slot="popup"
          {index}
          staticFacets={$ConfigSystem.elasticSearch[index].facets}
          selectedMetaFacets={$configPersistent.elasticSearch[index].metaFacets}
          selectedFacetKeys={$configPersistent.elasticSearch[index].facetKeys}
          {onSelected}
          onReset={hideContainer}
        />
        <FiltersReset slot="reset" class="ml-1 flex-1" {index}>
          <Btn class="w-full" size="small" variant="gray-outline">Reset Filter</Btn>
        </FiltersReset>
        <Btn
          slot="add"
          class="mr-1 flex-1"
          size="small"
          variant="outline"
          on:click={toggleConfigurator}>Add Filter</Btn
        >
      </FilterBarHeader>
    </div>
  </div>

  {#if facetsArray.length > 0}
    {#each facetsArray as facet (facet.key)}
      <div animate:flip={{ duration: 500 }}>
        <FilterWrapper
          onClick={() => toggleFacet(facet.key)}
          collapsed={facetCollapsed[facet.key]}
          label={facet.label}
        >
          {@const { index, key } = facet}
          <Facet {index} {key} />
        </FilterWrapper>
      </div>
    {/each}
  {:else}
    <div class="p-4">
      <InlineNotification type="info">No Filters Selected</InlineNotification>
    </div>
  {/if}
</div>

<style scoped>
  .grow {
    min-height: 440px;
  }
</style>
