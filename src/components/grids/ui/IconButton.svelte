<script lang="ts">
  import exclude from '@/components/exclude';
  import useForwardEvents from '@/components/useForwardEvents';
  import type { SvelteComponent } from 'svelte';

  export let Icon: typeof SvelteComponent;

  let baseRef: HTMLButtonElement;
  useForwardEvents(() => baseRef);

  let className = '';
  export { className as class };
</script>

<button
  bind:this={baseRef}
  {...exclude($$restProps, ['class'])}
  class="
  header-btn
  mr-3
  flex
  h-8
   items-center
   rounded
   border
   border-neutral-200
   bg-white
   px-2.5
   py-2
   text-regular
   font-bold
   leading-4 text-neutral-700
   {className}
"
>
  <span class="mr-1.5"><slot /></span>
  <svelte:component this={Icon} />
</button>
