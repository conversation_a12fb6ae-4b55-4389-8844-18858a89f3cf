<script>
  import { cn } from '@/utils/classname';

  let clazz = '';
  export { clazz as class };
</script>

<table class={cn('s2s-table w-full border-collapse', clazz)}>
  <thead>
    <slot name="thead" />
  </thead>
  <slot name="tbody" />
  <tfoot>
    <slot name="tfoot" />
  </tfoot>
</table>

<style lang="scss">
  // TODO: write styles here, this is a throw off draft
  .s2s-table {
    background: white;
    border: none;
    :global(thead th) {
      font-size: 11px;
      padding: 8px 8px;
      min-height: 48px;
      vertical-align: center;

      &:first-child {
        padding-left: 16px;
        padding-right: 16px;
      }
    }

    :global(tbody tr) {
      // TODO: read on how to make nested component sets without globals
      border-top: solid 1px #e2e8f0; // TODO: use proper color variable
    }

    :global(tbody td) {
      padding: 8px 8px;
      font-size: 13px;

      &:first-child {
        padding-left: 16px;
        padding-right: 16px;
      }
    }
  }
</style>
