<script lang="ts">
  import SortHeader from '@/lib/es/SortHeader.svelte';
  import type { TableColumn } from '@/lib/s2s/types';

  export let column: TableColumn;
  export let index: string;
  export let className = 'py-[5px] text-small font-bold text-neutral-800';
</script>

<div
  class={className}
  class:text-left={column.align === 'left'}
  class:text-right={column.align === 'right'}
  class:text-center={column.align === 'center'}
>
  {#if column.sortable}
    <SortHeader {index} field={column.sortField || column.field} defaultOrder={column.sortable}>
      {column.label}
    </SortHeader>
  {:else}
    {column.label}
  {/if}
</div>
