<script lang="ts">
  type T = $$Generic;
  import type { TableColumn } from '@/lib/s2s/types';
  import resolveColumnStringValue from '@/utils/resolveColumnStringValue';

  export let column: TableColumn;
  export let data: T;
</script>

{#if column.cellComponent}
  <div class={column.className ?? ''}>
    <svelte:component this={column.cellComponent} {data} />
  </div>
{:else}
  <div class="group relative">
    <div
      class="value overflow-hidden text-ellipsis {column.className ?? ''}"
      class:text-left={column.align === 'left'}
      class:text-right={column.align === 'right'}
      class:text-center={column.align === 'center'}
    >
      {resolveColumnStringValue(data, column)}
    </div>
    <div
      class="tooltip absolute bottom-5 left-1/2 z-30 hidden w-full max-w-[130px] -translate-x-1/2 rounded bg-brand-brand p-1 text-center text-smaller text-white group-hover:block"
    >
      {resolveColumnStringValue(data, column)}
    </div>
  </div>
{/if}

<style>
  .value {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }

  .tooltip:before {
    content: '';
    position: absolute;
    width: 8px;
    height: 8px;
    background-color: theme('colors.brand.brand');
    transform: translateX(-50%) rotate(135deg);
    top: calc(100% - 5px);
    left: 50%;
  }
</style>
