<script lang="ts">
  import type { Bucket } from '@/lib/es/types';
  import { createQuery } from '@tanstack/svelte-query';
  import { getContext } from 'svelte';

  export let data: Bucket;
  const dependencyContainer: any = getContext('source-bucket-dependencies');
  const sourcesData = createQuery({
    queryKey: ['sources-get'],
    queryFn: () => dependencyContainer.sourcesRepo.get()
  });
  $: sources = $sourcesData.data || [];
  $: sourceName =
    sources.find((c) => c.id.toString() == data.key)?.description ??
    ($sourcesData.isLoading ? '-' : `Source ${data.key}`);
</script>

<p>{sourceName}</p>
