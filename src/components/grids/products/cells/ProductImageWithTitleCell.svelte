<script lang="ts">
  import Image from '@/components/Controls/Image.svelte';
  import type { SystemProduct } from '@/models/products/product';
  import type Variant from '@/models/variant';

  export let product: SystemProduct;
  export let variant: Variant | undefined;
  $: options = variant
    ? [variant.option1, variant.option2, variant.option3].filter((o) => !!o).join(' / ')
    : undefined;

  $: variantImage = product.images.find((image) => image.id === variant?.image_id);
</script>

<div class="flex items-center">
  <div
    class="mr-4 h-12 max-h-[48px] min-h-[48px] w-12 min-w-[48px] max-w-[48px] overflow-hidden rounded"
  >
    <Image
      image={variantImage?.src_50x50 ? variantImage : product.images[0]}
      src="src_50x50"
      alt={product.title}
    />
  </div>
  <div class="max-w-[172px] text-regular leading-4 text-neutral-700">
    <div class="value overflow-hidden text-ellipsis font-bold">
      {product.title}
    </div>

    {#if options}
      <div class="value mt-1 overflow-hidden text-ellipsis">
        {options}
      </div>
    {/if}
  </div>
</div>
