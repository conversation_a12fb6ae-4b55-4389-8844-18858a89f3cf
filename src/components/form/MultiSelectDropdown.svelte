<script lang="ts">
  import Btn from '@/components/form/Btn.svelte';
  import type { TemplateDropdownItem } from '@/lib/s2s/types';
  import { fly } from 'svelte/transition';
  import handleClickOutside from '@/utils/actions/handleClickOutside';

  export let items = [];
  export let open = false;
  export let close = () => {
    open = false;
  };
  export let itemToggled = (item: TemplateDropdownItem) => {};
</script>

{#if open}
  <div
    transition:fly={{ y: -10, duration: 100 }}
    class="y-full fixed z-50 mb-1 w-64 border-2 border-gray-100 bg-white pb-2"
    use:handleClickOutside={close}
  >
    <div class="mt-1" />
    {#each items as item}
      <div class="ml-2 mt-1">
        <div
          class="flex cursor-pointer items-center justify-between rounded pb-1 pl-2 pr-1 pt-1.5 text-regular text-neutral-700"
        >
          <label class="inline-flex cursor-pointer">
            <input
              type="checkbox"
              checked={item.checked}
              on:change={() => itemToggled(item)}
              class="cursor-pointer rounded border border-neutral-300
          bg-white text-brand-notification checked:bg-brand-notification focus:ring-0 focus:ring-offset-0"
            />
            <span class="ml-2">{item.displayName}</span>
          </label>
        </div>
      </div>
    {/each}
    <div class="mt-2 border-t">
      <div
        class="ml-2 mt-2 flex cursor-pointer items-center justify-between rounded pb-1 pl-2 pr-1 pt-1.5 text-regular text-neutral-700"
      >
        <Btn variant="gray-outline" onClick={close} className="mt-4">Close</Btn>
      </div>
    </div>
  </div>
{/if}
