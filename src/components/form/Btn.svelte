<script lang="ts">
  import type { SvelteComponent } from 'svelte';
  import type { IconUnion } from '@/lib/s2s/icons';

  import exclude from '@/components/exclude';
  import useForwardEvents from '@/components/useForwardEvents';
  import type { Size } from '../types';
  import { cn } from '@/utils/classname';

  type SvelteComponentConstructor = new (...args: any[]) => SvelteComponent;

  export let Prefix: SvelteComponentConstructor | IconUnion | undefined = null;
  export let Suffix: SvelteComponentConstructor | IconUnion | undefined = null;

  export let disabled = false;
  export let size: Size = 'medium';
  export let iconSize: 'extra-small' | 'small' | 'medium' | 'large' | 'extra-large' | undefined =
    undefined;
  export let type: 'submit' | 'button' | 'reset' = 'button';
  export let variant: 'solid' | 'gray' | 'outline' | 'gray-outline' | 'ghost' = 'solid';

  export let onClick: (() => void) | ((parm: any) => void) = () => {};
  let baseRef: HTMLButtonElement;
  useForwardEvents(() => baseRef);

  let className = '';
  export { className as class };

  const onlyIcon = !$$slots.default && (Prefix || Suffix);

  const sizeClassMap = {
    small: 'text-small px-2 leading-4 h-6 btn-small', // TODO: migrate all h-* to min-h-*
    medium: 'text-regular px-2.5 leading-3 h-8 btn-medium',
    large: 'text-regular px-4 leading-4 h-10 btn-large'
  };

  const iconSizeClassMap = {
    small: 'h-6 w-6 btn-small', // TODO: migrate all h-* to min-h-*
    medium: 'h-8 w-8 btn-medium',
    large: 'h-10 w-10 btn-large'
  };

  const iconContainerSizeMap = {
    'extra-small': 'icon w-3.5 h-3.5',
    smaller: 'icon w-4 h-4',
    small: 'icon w-5 h-5',
    medium: 'icon w-6 h-6',
    large: 'icon w-8 h-8',
    'extra-large': 'icon w-10 h-10'
  };

  const variantClassMap = {
    solid: `
              text-white bg-brand-action border-brand-action
              disabled:bg-brand-action/10 disabled:border-none
    `,
    gray: `
              bg-neutral-100 text-neutral-700 border-neutral-100
              hover:bg-neutral-200 hover:border-neutral-200
              disabled:border-neutral-100 disabled:text-neutral-400 disabled:bg-neutral-100
    `,
    outline: `
              bg-white text-brand-action border-brand-action
              hover:bg-brand-action hover:text-white
              disabled:border-neutral-200 disabled:text-neutral-400 disabled:bg-white
    `,
    ghost: `
              bg-white text-brand-action border-white
              hover:bg-brand-action hover:text-white
              disabled:text-neutral-400 disabled:bg-white
    `,
    'gray-outline': `
              bg-white text-neutral-700 border-neutral-200
              hover:bg-neutral-100
              disabled:border-neutral-200 disabled:text-neutral-400
    `
  };

  const sizeClass = onlyIcon ? iconSizeClassMap[size] : sizeClassMap[size];
  const variantClass = variantClassMap[variant];
  const iconContainerClass = iconSize ? iconContainerSizeMap[iconSize] : '';
</script>

<button
  bind:this={baseRef}
  {disabled}
  {type}
  on:click={onClick}
  {...exclude($$restProps, ['class'])}
  class:rounded={!onlyIcon}
  class:rounded-full={onlyIcon}
  class={cn(
    sizeClass,
    variantClass,
    `btn-with-svg-icon
    flex
    items-center
     justify-center
     border
     font-bold`,
    className
  )}
>
  {#if Prefix}
    <div class={iconContainerClass}>
      <svelte:component this={Prefix} />
    </div>
  {/if}
  {#if $$slots.default}
    <span class:ml-1={!!Prefix} class:mr-1={!!Suffix}>
      <slot />
    </span>
  {/if}
  {#if Suffix}
    <div class={iconContainerClass}>
      <svelte:component this={Suffix} />
    </div>
  {/if}
</button>

<style>
  .btn-with-svg-icon:global(.btn-small) :global(svg) {
    width: 14px;
    height: 14px;
  }
  .btn-with-svg-icon:global(.btn-medium) :global(svg) {
    width: 18px;
    height: 18px;
  }
  .btn-with-svg-icon:global(.btn-large) :global(svg) {
    width: 20px;
    height: 20px;
  }
  .btn-with-svg-icon :global(.icon svg) {
    width: 100%;
    height: 100%;
  }
</style>
