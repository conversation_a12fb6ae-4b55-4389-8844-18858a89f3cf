<script context="module">
  export const meta = {
    title: 'components/form/MultiSelectDropdown',
    component: MultiSelectDropdown
  };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';
  import MultiSelectDropdown from '@/components/form/MultiSelectDropdown.svelte';
  import Btn from '@/components/form/Btn.svelte';
  import IconExpandArrow from '@/components/Icons/IconExpandArrow.svelte';
  import type { TemplateDropdownItem } from '@/lib/s2s/types';

  let templateDropdownItems = [
    { displayName: 'Option 1', checked: false },
    { displayName: 'Option 2', checked: true },
    { displayName: 'Option 3', checked: false }
  ];

  let dropdownSelectOpen = false;
  let toggleDropdownSelect = () => {
    dropdownSelectOpen = !dropdownSelectOpen;
  };

  let itemToggled = (item: TemplateDropdownItem) => {
    console.log(item);
  };
</script>

<Story name="MultiSelectDropdown" id="multi_select_dropdown">
  <div class="relative">
    <Btn size="medium" variant="gray" Suffix={IconExpandArrow} on:click={toggleDropdownSelect}
      >Select options
    </Btn>
    <MultiSelectDropdown
      items={templateDropdownItems}
      bind:open={dropdownSelectOpen}
      {itemToggled}
    />
  </div>
</Story>
