<script context="module">
  export const meta = {
    title: 'components/form/Btn',
    component: Btn
  };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';
  import Btn from '@/components/form/Btn.svelte';
  import IconDownload from '@/components/Icons/IconDownload.svelte';
</script>

<Story name="Small">
  <div class="flex flex-wrap gap-1 p-4">
    <Btn size="small" variant="solid">Solid</Btn>
    <Btn size="small" variant="solid" Prefix={IconDownload} />
    <Btn size="small" variant="solid" Prefix={IconDownload}>Solid Icon Left</Btn>
    <Btn size="small" variant="solid" Suffix={IconDownload}>Solid Icon Right</Btn>
    <Btn size="small" variant="outline">Outline</Btn>
    <Btn size="small" variant="outline" Prefix={IconDownload} />
    <Btn size="small" variant="outline" Prefix={IconDownload}>Outline Icon Left</Btn>
    <Btn size="small" variant="outline" Suffix={IconDownload}>Outline Icon Right</Btn>
    <Btn size="small" variant="gray">Gray</Btn>
    <Btn size="small" variant="gray" Prefix={IconDownload} />
    <Btn size="small" variant="gray" Prefix={IconDownload}>Gray Icon Left</Btn>
    <Btn size="small" variant="gray" Suffix={IconDownload}>Gray Icon Right</Btn>
    <Btn size="small" variant="gray-outline">GrayOutline</Btn>
    <Btn size="small" variant="gray-outline" Prefix={IconDownload} />
    <Btn size="small" variant="gray-outline" Prefix={IconDownload}>GrayOutline Icon Left</Btn>
    <Btn size="small" variant="gray-outline" Suffix={IconDownload}>GrayOutline Icon Right</Btn>
    <Btn size="small" variant="ghost">Ghost</Btn>
    <Btn size="small" variant="ghost" Prefix={IconDownload} />
    <Btn size="small" variant="ghost" Prefix={IconDownload}>Ghost Icon Left</Btn>
    <Btn size="small" variant="ghost" Suffix={IconDownload}>Ghost Icon Right</Btn>
    <Btn size="small" disabled variant="solid">Solid Disabled</Btn>
    <Btn size="small" disabled variant="solid" Prefix={IconDownload} />
    <Btn size="small" disabled variant="solid" Prefix={IconDownload}>Solid Disabled Icon Left</Btn>
    <Btn size="small" disabled variant="solid" Suffix={IconDownload}>Solid Disabled Icon Right</Btn>
    <Btn size="small" disabled variant="outline">Outline Disabled</Btn>
    <Btn size="small" disabled variant="outline" Prefix={IconDownload} />
    <Btn size="small" disabled variant="outline" Prefix={IconDownload}
      >Outline Disabled Icon Left</Btn
    >
    <Btn size="small" disabled variant="outline" Suffix={IconDownload}
      >Outline Disabled Icon Right</Btn
    >
    <Btn size="small" disabled variant="gray">Gray</Btn>
    <Btn size="small" disabled variant="gray" Prefix={IconDownload} />
    <Btn size="small" disabled variant="gray" Prefix={IconDownload}>Gray Disabled Icon Left</Btn>
    <Btn size="small" disabled variant="gray" Suffix={IconDownload}>Gray Disabled Icon Right</Btn>
    <Btn size="small" disabled variant="gray-outline">GrayOutline</Btn>
    <Btn size="small" disabled variant="gray-outline" Prefix={IconDownload} />
    <Btn size="small" disabled variant="gray-outline" Prefix={IconDownload}
      >GrayOutline Disabled Icon Left</Btn
    >
    <Btn size="small" disabled variant="gray-outline" Suffix={IconDownload}
      >GrayOutline Disabled Icon Right</Btn
    >
    <Btn size="small" disabled variant="ghost">Ghost</Btn>
    <Btn size="small" disabled variant="ghost" Prefix={IconDownload} />
    <Btn size="small" disabled variant="ghost" Prefix={IconDownload}>Ghost Disabled Icon Left</Btn>
    <Btn size="small" disabled variant="ghost" Suffix={IconDownload}>Ghost Disabled Icon Right</Btn>
  </div>
</Story>

<Story name="Medium">
  <div class="flex flex-wrap gap-1 p-4">
    <Btn size="medium" variant="solid">Solid</Btn>
    <Btn size="medium" variant="solid" Prefix={IconDownload} />
    <Btn size="medium" variant="solid" Prefix={IconDownload}>Solid Icon Left</Btn>
    <Btn size="medium" variant="solid" Suffix={IconDownload}>Solid Icon Right</Btn>
    <Btn size="medium" variant="outline">Outline</Btn>
    <Btn size="medium" variant="outline" Prefix={IconDownload} />
    <Btn size="medium" variant="outline" Prefix={IconDownload}>Outline Icon Left</Btn>
    <Btn size="medium" variant="outline" Suffix={IconDownload}>Outline Icon Right</Btn>
    <Btn size="medium" variant="gray">Gray</Btn>
    <Btn size="medium" variant="gray" Prefix={IconDownload} />
    <Btn size="medium" variant="gray" Prefix={IconDownload}>Gray Icon Left</Btn>
    <Btn size="medium" variant="gray" Suffix={IconDownload}>Gray Icon Right</Btn>
    <Btn size="medium" variant="gray-outline">GrayOutline</Btn>
    <Btn size="medium" variant="gray-outline" Prefix={IconDownload} />
    <Btn size="medium" variant="gray-outline" Prefix={IconDownload}>GrayOutline Icon Left</Btn>
    <Btn size="medium" variant="gray-outline" Suffix={IconDownload}>GrayOutline Icon Right</Btn>
    <Btn size="medium" variant="ghost">Ghost</Btn>
    <Btn size="medium" variant="ghost" Prefix={IconDownload} />
    <Btn size="medium" variant="ghost" Prefix={IconDownload}>Ghost Icon Left</Btn>
    <Btn size="medium" variant="ghost" Suffix={IconDownload}>Ghost Icon Right</Btn>
    <Btn size="medium" disabled variant="solid">Solid Disabled</Btn>
    <Btn size="medium" disabled variant="solid" Prefix={IconDownload} />
    <Btn size="medium" disabled variant="solid" Prefix={IconDownload}>Solid Disabled Icon Left</Btn>
    <Btn size="medium" disabled variant="solid" Suffix={IconDownload}>Solid Disabled Icon Right</Btn
    >
    <Btn size="medium" disabled variant="outline">Outline Disabled</Btn>
    <Btn size="medium" disabled variant="outline" Prefix={IconDownload} />
    <Btn size="medium" disabled variant="outline" Prefix={IconDownload}
      >Outline Disabled Icon Left</Btn
    >
    <Btn size="medium" disabled variant="outline" Suffix={IconDownload}
      >Outline Disabled Icon Right</Btn
    >
    <Btn size="medium" disabled variant="gray">Gray</Btn>
    <Btn size="medium" disabled variant="gray" Prefix={IconDownload} />
    <Btn size="medium" disabled variant="gray" Prefix={IconDownload}>Gray Disabled Icon Left</Btn>
    <Btn size="medium" disabled variant="gray" Suffix={IconDownload}>Gray Disabled Icon Right</Btn>
    <Btn size="medium" disabled variant="gray-outline">GrayOutline</Btn>
    <Btn size="medium" disabled variant="gray-outline" Prefix={IconDownload} />
    <Btn size="medium" disabled variant="gray-outline" Prefix={IconDownload}
      >GrayOutline Disabled Icon Left</Btn
    >
    <Btn size="medium" disabled variant="gray-outline" Suffix={IconDownload}
      >GrayOutline Disabled Icon Right</Btn
    >
    <Btn size="medium" disabled variant="ghost">Ghost</Btn>
    <Btn size="medium" disabled variant="ghost" Prefix={IconDownload} />
    <Btn size="medium" disabled variant="ghost" Prefix={IconDownload}>Ghost Disabled Icon Left</Btn>
    <Btn size="medium" disabled variant="ghost" Suffix={IconDownload}>Ghost Disabled Icon Right</Btn
    >
  </div>
</Story>

<Story name="Large">
  <div class="flex flex-wrap gap-1 p-4">
    <Btn size="large" variant="solid">Solid</Btn>
    <Btn size="large" variant="solid" Prefix={IconDownload} />
    <Btn size="large" variant="solid" Prefix={IconDownload}>Solid Icon Left</Btn>
    <Btn size="large" variant="solid" Suffix={IconDownload}>Solid Icon Right</Btn>
    <Btn size="large" variant="outline">Outline</Btn>
    <Btn size="large" variant="outline" Prefix={IconDownload} />
    <Btn size="large" variant="outline" Prefix={IconDownload}>Outline Icon Left</Btn>
    <Btn size="large" variant="outline" Suffix={IconDownload}>Outline Icon Right</Btn>
    <Btn size="large" variant="gray">Gray</Btn>
    <Btn size="large" variant="gray" Prefix={IconDownload} />
    <Btn size="large" variant="gray" Prefix={IconDownload}>Gray Icon Left</Btn>
    <Btn size="large" variant="gray" Suffix={IconDownload}>Gray Icon Right</Btn>
    <Btn size="large" variant="gray-outline">GrayOutline</Btn>
    <Btn size="large" variant="gray-outline" Prefix={IconDownload} />
    <Btn size="large" variant="gray-outline" Prefix={IconDownload}>GrayOutline Icon Left</Btn>
    <Btn size="large" variant="gray-outline" Suffix={IconDownload}>GrayOutline Icon Right</Btn>
    <Btn size="large" variant="ghost">Ghost</Btn>
    <Btn size="large" variant="ghost" Prefix={IconDownload} />
    <Btn size="large" variant="ghost" Prefix={IconDownload}>Ghost Icon Left</Btn>
    <Btn size="large" variant="ghost" Suffix={IconDownload}>Ghost Icon Right</Btn>
    <Btn size="large" disabled variant="solid">Solid Disabled</Btn>
    <Btn size="large" disabled variant="solid" Prefix={IconDownload} />
    <Btn size="large" disabled variant="solid" Prefix={IconDownload}>Solid Disabled Icon Left</Btn>
    <Btn size="large" disabled variant="solid" Suffix={IconDownload}>Solid Disabled Icon Right</Btn>
    <Btn size="large" disabled variant="outline">Outline Disabled</Btn>
    <Btn size="large" disabled variant="outline" Prefix={IconDownload} />
    <Btn size="large" disabled variant="outline" Prefix={IconDownload}
      >Outline Disabled Icon Left</Btn
    >
    <Btn size="large" disabled variant="outline" Suffix={IconDownload}
      >Outline Disabled Icon Right</Btn
    >
    <Btn size="large" disabled variant="gray">Gray</Btn>
    <Btn size="large" disabled variant="gray" Prefix={IconDownload} />
    <Btn size="large" disabled variant="gray" Prefix={IconDownload}>Gray Disabled Icon Left</Btn>
    <Btn size="large" disabled variant="gray" Suffix={IconDownload}>Gray Disabled Icon Right</Btn>
    <Btn size="large" disabled variant="gray-outline">GrayOutline</Btn>
    <Btn size="large" disabled variant="gray-outline" Prefix={IconDownload} />
    <Btn size="large" disabled variant="gray-outline" Prefix={IconDownload}
      >GrayOutline Disabled Icon Left</Btn
    >
    <Btn size="large" disabled variant="gray-outline" Suffix={IconDownload}
      >GrayOutline Disabled Icon Right</Btn
    >
    <Btn size="large" disabled variant="ghost">Ghost</Btn>
    <Btn size="large" disabled variant="ghost" Prefix={IconDownload} />
    <Btn size="large" disabled variant="ghost" Prefix={IconDownload}>Ghost Disabled Icon Left</Btn>
    <Btn size="large" disabled variant="ghost" Suffix={IconDownload}>Ghost Disabled Icon Right</Btn>
  </div>
</Story>
