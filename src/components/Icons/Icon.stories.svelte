<script context="module">
  import Icon from '@/components/Icons/Icon.svelte';

  export const meta = {
    title: 'components/Icons/Icon',
    component: Icon
  };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';
  import IconAdd from '@/components/Icons/IconAdd.svelte';
  import IconAddCircle from '@/components/Icons/IconAddCircle.svelte';
  import IconAlert from '@/components/Icons/IconAlert.svelte';
  import IconArrowDown from '@/components/Icons/IconArrowDown.svelte';
  import IconArrowRight from '@/components/Icons/IconArrowRight.svelte';
  import IconBack from '@/components/Icons/IconBack.svelte';
  import IconBox from '@/components/Icons/IconBox.svelte';
  import IconCamera from '@/components/Icons/IconCamera.svelte';
  import IconCheck from '@/components/Icons/IconCheck.svelte';
  import IconCheckCircle from '@/components/Icons/IconCheckCircle.svelte';
  import IconCheckout from '@/components/Icons/IconCheckout.svelte';
  import IconChevronDown from '@/components/Icons/IconChevronDown.svelte';
  import IconChevronUp from '@/components/Icons/IconChevronUp.svelte';
  import IconClose from '@/components/Icons/IconClose.svelte';
  import IconCloud from '@/components/Icons/IconCloud.svelte';
  import IconCollapseAll from '@/components/Icons/IconCollapseAll.svelte';
  import IconColumns from '@/components/Icons/IconColumns.svelte';
  import IconConnector from '@/components/Icons/IconConnector.svelte';
  import IconCustomer from '@/components/Icons/IconCustomer.svelte';
  import IconCustomers from '@/components/Icons/IconCustomers.svelte';
  import IconDatabase from '@/components/Icons/IconDatabase.svelte';
  import IconDelete from '@/components/Icons/IconDelete.svelte';
  import IconFlag from '@/components/Icons/IconFlag.svelte';
  import IconDownload from '@/components/Icons/IconDownload.svelte';
  import IconEdit from '@/components/Icons/IconEdit.svelte';
  import IconExpandAll from '@/components/Icons/IconExpandAll.svelte';
  import IconExpandArrow from '@/components/Icons/IconExpandArrow.svelte';
  import IconExternalLink from '@/components/Icons/IconExternalLink.svelte';
  import IconFile from '@/components/Icons/IconFile.svelte';
  import IconFilter from '@/components/Icons/IconFilter.svelte';
  import IconFulfill from '@/components/Icons/IconFulfill.svelte';
  import IconGrid from '@/components/Icons/IconGrid.svelte';
  import IconImage from '@/components/Icons/IconImage.svelte';
  import IconInfo from '@/components/Icons/IconInfo.svelte';
  import IconInventory from '@/components/Icons/IconInventory.svelte';
  import IconLayout from '@/components/Icons/IconLayout.svelte';
  import IconList from '@/components/Icons/IconList.svelte';
  import IconLock from '@/components/Icons/IconLock.svelte';
  import IconLogout from '@/components/Icons/IconLogout.svelte';
  import IconMail from '@/components/Icons/IconMail.svelte';
  import IconMapPin from '@/components/Icons/IconMapPin.svelte';
  import IconNavigation from '@/components/Icons/IconNavigation.svelte';
  import IconNotification from '@/components/Icons/IconNotification.svelte';
  import IconOrder from '@/components/Icons/IconOrder.svelte';
  import IconPayment from '@/components/Icons/IconPayment.svelte';
  import IconPricing from '@/components/Icons/IconPricing.svelte';
  import IconProduct from '@/components/Icons/IconProduct.svelte';
  import IconRefresh from '@/components/Icons/IconRefresh.svelte';
  import IconReport from '@/components/Icons/IconReport.svelte';
  import IconRevert from '@/components/Icons/IconRevert.svelte';
  import IconSage from '@/components/Icons/IconSage.svelte';
  import IconSearch from '@/components/Icons/IconSearch.svelte';
  import IconSegment from '@/components/Icons/IconSegment.svelte';
  import IconServer from '@/components/Icons/IconServer.svelte';
  import IconSetting from '@/components/Icons/IconSetting.svelte';
  import IconShow from '@/components/Icons/IconShow.svelte';
  import IconSource from '@/components/Icons/IconSource.svelte';
  import IconUnlock from '@/components/Icons/IconUnlock.svelte';
  import IconUpload from '@/components/Icons/IconUpload.svelte';
  import IconVariants from '@/components/Icons/IconVariants.svelte';
  import IconAlertCircle from '@/components/Icons/IconAlertCircle.svelte';
  import IconMenu from '@/components/Icons/IconMenu.svelte';
  import IconClockFading from '@/components/Icons/IconClockFading.svelte';

  let icons = [
    IconAdd,
    IconAddCircle,
    IconAlert,
    IconAlertCircle,
    IconArrowDown,
    IconArrowRight,
    IconBack,
    IconBox,
    IconCamera,
    IconCheck,
    IconCheckCircle,
    IconCheckout,
    IconChevronDown,
    IconChevronUp,
    IconClose,
    IconCloud,
    IconCollapseAll,
    IconColumns,
    IconConnector,
    IconCustomer,
    IconCustomers,
    IconDatabase,
    IconDelete,
    IconDownload,
    IconEdit,
    IconExpandAll,
    IconExpandArrow,
    IconExternalLink,
    IconFile,
    IconFilter,
    IconFlag,
    IconFulfill,
    IconGrid,
    IconImage,
    IconInfo,
    IconInventory,
    IconLayout,
    IconList,
    IconLock,
    IconLogout,
    IconMail,
    IconMapPin,
    IconNavigation,
    IconNotification,
    IconOrder,
    IconPayment,
    IconPricing,
    IconProduct,
    IconRefresh,
    IconReport,
    IconRevert,
    IconSage,
    IconSearch,
    IconSegment,
    IconServer,
    IconSetting,
    IconShow,
    IconSource,
    IconUnlock,
    IconUpload,
    IconVariants,
    IconMenu,
    IconClockFading
  ];
</script>

<Story name="Primary">
  <div id="Page" class="space-y-4">
    <h2>Size 10</h2>

    <div class="flex flex-wrap place-items-start items-center gap-1 pt-2">
      {#each icons as icon}
        <div title={icon.name} class="bg-gray-100">
          <Icon size="i-10" IconComponent={icon} />
        </div>
      {/each}
    </div>

    <h2>Size 6</h2>

    <div class="flex flex-wrap place-items-start items-center gap-1 pt-2">
      {#each icons as icon}
        <div title={icon.name} class="bg-gray-100 p-2">
          <Icon size="i-6" IconComponent={icon} />
        </div>
      {/each}
    </div>

    <h2>Size 4 blue</h2>

    <div class="flex flex-wrap place-items-start items-center gap-1 pt-2">
      {#each icons as icon}
        <div title={icon.name} style="color: blue" class="bg-gray-100 p-3">
          <Icon size="i-4" IconComponent={icon} />
        </div>
      {/each}
    </div>
  </div>
</Story>

<Story name="Icon Names">
  <h2>Names</h2>
  <div class="grid grid-cols-[auto_1fr] gap-4">
    {#each icons as icon}
      <div title={icon.name}>
        {icon.name}
      </div>
      <div title={icon.name} class="w-min bg-gray-100 p-3">
        <Icon size="i-10" IconComponent={icon} />
      </div>
    {/each}
  </div>
</Story>
