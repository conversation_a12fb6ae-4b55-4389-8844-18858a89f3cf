<script lang="ts">
  import type { ComponentType } from 'svelte';
  import type { IconSize } from '@/components/Controls/size';

  export let IconComponent: ComponentType;
  export let size: IconSize = 'i-8';

  // There is an odd naming convention "smallest, extra-small, smaller, small".
  // leaving it here until we have removed all references to it.
  const sizing: Record<IconSize, string> = {
    smallest: 'h-3.5 w-3.5',
    'extra-small': 'h-4 w-4',
    smaller: 'h-5 w-5',
    small: 'h-6 w-6',
    medium: 'h-8 w-8',
    large: 'h-10 w-10',

    // more meaningful names
    'i-3': 'h-3 w-3',
    'i-3.5': 'h-3.5 w-3.5',
    'i-4': 'h-4 w-4',
    'i-5': 'h-5 w-5',
    'i-6': 'h-6 w-6',
    'i-7': 'h-7 w-7',
    'i-8': 'h-8 w-8',
    'i-9': 'h-9 w-9',
    'i-10': 'h-10 w-10'
  };
</script>

<svelte:component this={IconComponent} size={sizing[size]} />
