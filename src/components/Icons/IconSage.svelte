<script lang="ts">
  export let size: string = '';
</script>

<svg
  class={size}
  width="24"
  height="24"
  viewBox="0 0 24 24"
  fill="currentColor"
  xmlns="http://www.w3.org/2000/svg"
>
  <path
    d="M3.47672 5.88623C2.06937 5.88623 1 6.95207 1 8.32003C1 9.81949 2.06937 10.3985 3.25509 10.7539C4.37709 11.1091 4.90902 11.5036 4.90902 12.2812C4.90902 13.0701 4.32169 13.624 3.55429 13.624C2.78412 13.624 2.11923 13.0701 2.11923 12.2169C2.11923 11.7441 2.34086 11.6238 2.34086 11.3609C2.34086 11.0588 2.13309 10.8349 1.79233 10.8349C1.40448 10.8349 1 11.428 1 12.27C1 13.6268 2.16079 14.6255 3.55429 14.6255C4.96166 14.6255 6.031 13.5597 6.031 12.1917C6.031 10.7035 4.96166 10.1132 3.76484 9.75793C2.65669 9.41664 2.122 9.00821 2.122 8.23051C2.122 7.45561 2.69547 6.90171 3.46287 6.90171C4.25797 6.90171 4.89794 7.4668 4.90902 8.2445C4.92287 8.59978 5.16942 8.80959 5.45754 8.80959C5.75674 8.80959 6.031 8.59978 6.01714 8.21653C5.99221 6.85975 4.85914 5.88623 3.47672 5.88623ZM9.13383 14.6115C9.75993 14.6115 10.242 14.4017 10.242 13.9793C10.242 13.6632 9.98158 13.4534 9.73225 13.4534C9.54939 13.4534 9.4192 13.5317 9.11997 13.5317C8.31104 13.5317 7.67386 12.8352 7.67386 12.0183C7.67386 11.1371 8.3249 10.4377 9.14768 10.4377C10.073 10.4377 10.6603 11.1483 10.6603 12.1749V14.0576C10.6603 14.3878 10.9207 14.6115 11.2338 14.6115C11.533 14.6115 11.7934 14.3878 11.7934 14.0576V12.1358C11.7934 10.558 10.7129 9.39986 9.17262 9.39986C7.73755 9.39986 6.55185 10.5188 6.55185 12.0183C6.55185 13.4674 7.72647 14.6115 9.13383 14.6115ZM14.8685 18.1392C16.2759 18.1392 17.3452 17.0733 17.3452 15.7054C17.3452 14.2059 16.2759 13.6268 15.0791 13.2716C13.9709 12.9163 13.4363 12.5218 13.4363 11.7441C13.4363 10.9664 14.0236 10.4014 14.7771 10.4014C15.5473 10.4014 16.2233 10.9552 16.2233 11.8085C16.2233 12.2841 16.0016 12.4015 16.0016 12.6645C16.0016 12.9666 16.2094 13.1904 16.5363 13.1904C16.9408 13.1904 17.3452 12.5974 17.3452 11.7553C17.3452 10.3985 16.1734 9.39986 14.7771 9.39986C13.3698 9.39986 12.3142 10.4657 12.3142 11.8336C12.3142 13.3219 13.3698 13.9122 14.5693 14.2675C15.6913 14.6088 16.2233 15.0172 16.2233 15.7949C16.2233 16.5838 15.6359 17.1237 14.8824 17.1237C14.0734 17.1237 13.4473 16.5726 13.4363 15.7809C13.4224 15.4256 13.162 15.227 12.8877 15.227C12.5885 15.227 12.3142 15.4256 12.3281 15.8061C12.3392 17.1796 13.4723 18.1392 14.8685 18.1392ZM20.5256 14.6115C21.2542 14.6115 21.8305 14.4017 22.2322 14.15C22.6228 13.901 22.778 13.6884 22.778 13.4534C22.778 13.2044 22.5951 12.9806 22.2571 12.9806C21.8526 12.9806 21.5285 13.5849 20.5367 13.5849C19.6641 13.5849 18.9715 12.8883 18.9715 12.0071C18.9715 11.1371 19.6364 10.4265 20.4702 10.4265C21.3706 10.4265 21.8526 11.0196 21.8526 11.4001C21.8526 11.5064 21.7751 11.5595 21.6698 11.5595H20.5755C20.2763 11.5595 20.0547 11.7581 20.0547 12.0211C20.0547 12.3092 20.2763 12.5218 20.5755 12.5218H22.0743C22.6477 12.5218 22.9996 12.2197 22.9996 11.705C22.9996 10.4153 21.9053 9.40266 20.4564 9.40266C19.0213 9.40266 17.8633 10.5608 17.8633 12.0071C17.8633 13.4534 19.0656 14.6115 20.5256 14.6115Z"
  />
</svg>
