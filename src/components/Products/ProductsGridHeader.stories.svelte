<script context="module">
  import ProductsGridHeader from '@/components/Products/ProductsGridHeader.svelte';

  export const meta = {
    title: 'components/Products/ProductsGridHeader',
    component: ProductsGridHeader
  };
</script>

<script lang="ts">
  import { Story, Template } from '@storybook/addon-svelte-csf';
  import { configLocal } from '@/env/config.local';
  import { useMockProductsSearchContext } from '@/stories/test-data/MockElasticSearchContexts';
  import PageCard from '@/components/Config/PageCard.svelte';

  const systemColumns = configLocal.system.elasticSearch.products.columns;
  useMockProductsSearchContext();
</script>

<!-- <Template let:args>
  <div
    class="table-heading items-center gap-x-8 border-b border-neutral-200 px-6 py-3 leading-4"
    style={`display: grid; grid-template-columns: 18px 260px 102px repeat(${
      args.columnsToShow?.length || 1
    }, 102px)`}
  >
    <ProductsGridHeader {...args} />
  </div>
</Template> -->

<Template let:args>
  <PageCard>
    <div class="w-full overflow-auto">
      <div
        class="grid"
        style="grid-template-columns:260px 102px repeat({args?.columnsToShow?.length ||
          1}, minmax(150px, 0.5fr))"
      >
        <ProductsGridHeader {...args} />
      </div>
      <div class="h-10 w-full">
        <!-- SPACER FOR SCROLLBAR -->
      </div>
    </div>
  </PageCard>
</Template>

<Story
  name="AllSystemColumns"
  args={{
    columnsToShow: systemColumns
  }}
/>

<!-- 
  This looks bad
  TODO: Look into neatening this up
-->
<Story
  name="AllSelected"
  args={{
    columnsToShow: systemColumns,
    allSelected: true
  }}
/>

<Story
  name="OneColumnSpecified"
  args={{
    columnsToShow: systemColumns.slice(0, 1)
  }}
/>

<Story name="NoColumnsSpecified" />
