<script lang="ts">
  import Btn from '@/components/form/Btn.svelte';
  import IconAddCircle from '@/components/Icons/IconAddCircle.svelte';
  import { createQuery, useQueryClient } from '@tanstack/svelte-query';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import RulesRepo from '@/repos/rules-repo';
  import ObjectVersionControl from '@/models/version-controlled';
  import IconClose from '@/components/Icons/IconClose.svelte';
  import CommaSeparatedInput from '@/components/Controls/Tag/CommaSeparatedInput.svelte';
  import Loader from '@/components/Loader/Loader.svelte';
  import ChannelsRepo from '@/repos/channels-repo';
  import { onDestroy } from 'svelte';
  import { createFieldsQuery, fieldsQueryKeys } from '@/queries/fields.query';
  import OperatorSelector from '@/components/Controls/Select/OperatorSelector.svelte';
  import FieldSelector from '@/components/Controls/Select/FieldSelector/FieldSelector.svelte';
  import { confirmOverrideModal } from '@/components/Modal/modal';
  import type { SystemRule } from '@/models/rules/rules';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';

  export let deletedRules: number[] = [];
  export let channelId: number;
  export let changesMade: boolean;
  const rulesRepo = new RulesRepo();
  const channelsRepo = new ChannelsRepo();

  const queryClient = useQueryClient();

  let filters: ObjectVersionControl<SystemRule>[] = [];
  let newFilters: SystemRule[] = [];
  const operators: string[] = [
    'equal',
    'not equal',
    'greater than',
    'less than',
    'contains',
    'not contains',
    'starts with',
    'ends with',
    'lookup',
    'is not empty'
  ];

  function addFilter() {
    newFilters = [...newFilters, { key: '', operator: '', value: '' } as SystemRule];
  }

  const channelData = createQuery({ queryKey: ['channelData'], queryFn: () => channelsRepo.get() });
  export const saveChanges = async () => {
    if (
      // eslint-disable-next-line no-restricted-globals
      filters.some((c) => c.isStale) &&
      !(await confirmOverrideModal('product filters'))
    ) {
      return;
    }

    const posts = newFilters.filter(validFilter).map((f) =>
      rulesRepo.post(channelId, {
        system_rule: { channel_id: channelId, ...f }
      })
    );

    const deletes = deletedRules.map((id) => rulesRepo.delete(id));

    const updates = filters
      .filter((f) => f.changesMade)
      .map((f) =>
        rulesRepo.update(f.baseServerVersion.id, {
          system_rule: { channel_id: channelId, ...f.workingCopy }
        })
      );

    const promises = [...posts, ...deletes, ...updates];

    await Promise.all(promises);

    filters = [];
    newFilters = [];
    deletedRules = [];

    await queryClient.invalidateQueries({ queryKey: ['rules', channelId] });
    await queryClient.invalidateQueries({ queryKey: fieldsQueryKeys.fields('product') });
  };

  const availableFieldsData = createFieldsQuery({ params: { entity: 'product' } });

  const rulesData = createQuery({
    queryKey: ['rules', channelId],
    queryFn: () => rulesRepo.get(channelId)
  });

  $: $rulesData.data?.forEach((rule) => {
    let existingRule = filters.find((f) => f.baseServerVersion.id == rule.id);
    if (existingRule) {
      existingRule.update(rule);
    } else {
      filters = [...filters, new ObjectVersionControl(rule)];
    }
  });

  $: availableFields = $availableFieldsData.data?.fields || [];

  $: isLoading = $availableFieldsData.isLoading || $rulesData.isLoading;

  $: changesMade =
    filters.some((f) => f.changesMade) || newFilters.some(validFilter) || deletedRules.length > 0;

  $: currentChannel = $channelData.data?.find((c) => c.id == channelId);

  $: isTradeStore = currentChannel?.type === 'trade';

  function validFilter(filter: SystemRule) {
    return filter.key && filter.operator && filter.value;
  }

  function deleteFilter(id: number) {
    deletedRules = [...deletedRules, id];
  }

  function deleteNewFilter(index: number) {
    newFilters = newFilters.filter((f, i) => i != index);
  }

  onDestroy(() => {
    changesMade = false;
  });
</script>

<!-- 

  This component should be refactored to use Button instead of Btn  

-->

{#if isTradeStore}
  <InlineNotification type="info"
    >Filter products which are sent to this channel. You can also 'segment' products by customer, so
    certain customers see specific products.</InlineNotification
  >
{:else}
  <InlineNotification type="info"
    >Filter products which are sent to this channel.</InlineNotification
  >
{/if}

<div class="my-6 text-smaller font-bold uppercase leading-4 tracking-widest text-brand-action">
  {#if filters.length || newFilters.length}
    Filters
  {/if}
</div>

{#if isLoading}
  <Loader />
{:else}
  {#each filters as filter}
    <!-- ToDo: Svelte 5 snippets -->
    {#if !deletedRules.includes(filter.baseServerVersion.id)}
      <div class="mb-4 flex items-center gap-2">
        <div class="w-1/3">
          <FieldSelector entity="product" bind:propValue={filter.workingCopy.key} />
        </div>
        <div class="w-1/3">
          <OperatorSelector bind:value={filter.workingCopy.operator} />
        </div>
        {#if filter.workingCopy.operator == 'lookup'}
          <div class="w-1/3">
            <CommaSeparatedInput hideHint bind:value={filter.workingCopy.value} />
          </div>
        {:else}
          <div class="w-1/3">
            <TextInput placeholder="Enter value" bind:value={filter.workingCopy.value} />
          </div>
        {/if}

        <Btn
          onClick={() => deleteFilter(filter.workingCopy.id)}
          Prefix={IconClose}
          variant="gray"
        />
      </div>
    {/if}
  {/each}
  <!-- ToDo: Svelte 5 snippets -->
  {#each newFilters as filter, i}
    <div class="mb-4 flex items-center gap-2">
      <div class="w-1/3">
        <FieldSelector entity="product" bind:propValue={filter.key} />
      </div>
      <div class="w-1/3">
        <OperatorSelector bind:value={filter.operator} />
      </div>
      {#if filter.operator == 'lookup'}
        <div class="w-1/3">
          <CommaSeparatedInput hideHint bind:value={filter.value} />
        </div>
      {:else}
        <div class="w-1/3">
          <TextInput placeholder="Enter value" bind:value={filter.value} />
        </div>
      {/if}

      <Btn onClick={() => deleteNewFilter(i)} Prefix={IconClose} variant="gray" />
    </div>
  {/each}

  <Btn size="medium" variant="gray" onClick={addFilter} Prefix={IconAddCircle}>Add Filter</Btn>
{/if}
