<script lang="ts">
  import type { SystemCustomer } from '@/models/customer/customer';
  import ObjectVersionControl from '@/models/version-controlled';
  import { createQuery } from '@tanstack/svelte-query';
  import Icon from '../Icons/Icon.svelte';
  import IconSage from '../Icons/IconSage.svelte';
  import IconSource from '../Icons/IconSource.svelte';
  import Loader from '../Loader/Loader.svelte';
  import { getContext } from 'svelte';
  import DataSyncRepo from '@/repos/data-sync-repo';
  import { getLocalDateTime } from '@/lib/date-utils';

  const dataSyncRepo = new DataSyncRepo();
  const customer = getContext<ObjectVersionControl<SystemCustomer>>('customer');

  const dataSyncLogsData = createQuery({
    queryKey: ['data-sync-logs-get', $customer.workingCopy.id.toString()],
    queryFn: () => dataSyncRepo.getDataSyncLogs($customer.workingCopy.id)
  });

  $: dataSyncLogs = $dataSyncLogsData.data || [];
</script>

<!-- @component
 STUBBED since the api is not available yet
 -->

{#if $dataSyncLogsData.isError}
  <h1>Failed to load data sync logs.</h1>
{:else if $dataSyncLogsData.isLoading}
  <Loader />
{:else}
  <div class="px-10 py-8">
    <h2>Data Sync Log</h2>
    <div class="items-center overflow-auto">
      <div
        class="flex h-12 w-full items-center justify-between border-b border-neutral-200 px-4 py-3 text-small font-bold text-neutral-500"
      >
        <span class="w-[40%]">Source</span>
        <span class="w-[40%]">Date</span>
        <span class="w-[20%]" />
      </div>
      {#each dataSyncLogs as dataSyncLog}
        <div
          class="flex h-12 w-full items-center justify-between border-b border-neutral-200 px-4 py-3"
        >
          <span class="w-[5%]">
            <div class="flex h-6 w-6 items-center justify-center rounded-full bg-neutral-100">
              <Icon IconComponent={IconSage} />
            </div></span
          >
          <span class="w-[35%]">{dataSyncLog.source}</span>
          <span class="w-[40%]">{getLocalDateTime(dataSyncLog.date)}</span>
          <span class="flex w-[20%] justify-end">
            <div
              class="flex h-5 w-5 cursor-pointer items-center justify-center rounded-full bg-neutral-100"
            >
              <Icon size="smallest" IconComponent={IconSource} />
            </div></span
          >
        </div>
      {/each}
    </div>
  </div>
{/if}
