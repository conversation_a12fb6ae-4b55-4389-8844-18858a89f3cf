<script lang="ts">
  import Search from '@/components/Controls/Search.svelte';
  import OrdersTable from '@/components/Orders/OrdersTable.svelte';
  import type { ESData } from '@/lib/es/types';
  import type { FilterTerms } from '@/models/es/filter';
  import type { OrderHits } from '@/models/orders/hits';
  import type { Config } from '@/models/config/config';
  import TableSearchLayout from '@/components/Layout/TableSearchLayout.svelte';

  export let data: ESData<OrderHits> | undefined;
  export let isLoading: boolean;
  export let config: Config | undefined;
  export let pagination: { from: number; size: number };

  // will be used in the future with Facets component
  export let filterTerms: FilterTerms;
  export let search: string | null;

  const onSearch = (s: string) => {
    search = s;
  };

  const clearSearch = () => {
    search = null;
  };
</script>

<!--
  @component
  Manages the layout of the orders page, currently only for a customer.
  TODO use this component for the /orders route by adding the Facets component
  
-->

<TableSearchLayout>
  <!--  Sidebar-->
  <!-- TODO add Facets component here to bind filterTerms for /orders route -->
  <svelte:fragment slot="search">
    <Search {onSearch} {clearSearch} search={search ?? undefined} placeholder="Search" />
  </svelte:fragment>
  <svelte:fragment slot="table">
    <OrdersTable
      total={data?.hits?.total}
      hits={data?.hits?.hits}
      {isLoading}
      {config}
      bind:pagination
    />
  </svelte:fragment>
</TableSearchLayout>
