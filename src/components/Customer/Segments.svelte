<script lang="ts">
  import type { SystemCustomer } from '@/models/customer/customer';
  import ObjectVersionControl from '@/models/version-controlled';
  import SegmentsRepo from '@/repos/segments-repo';

  import SegmentsList from '../Segments/SegmentsList.svelte';
  import { getContext } from 'svelte';

  const segmentsRepo = new SegmentsRepo();

  const customer = getContext<ObjectVersionControl<SystemCustomer>>('customer');
</script>

<div class="px-10 py-8">
  <SegmentsList
    segmentContext="customers"
    {segmentsRepo}
    userId={$customer.workingCopy.user.user_id}
  />
</div>
