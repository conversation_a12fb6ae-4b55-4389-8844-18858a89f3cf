<script lang="ts">
  import FormInputGroup from '@/components/Controls/Group/FormInputGroup.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import PromptModal from '@/components/Modal/PromptModal.svelte';
  import type { SystemCustomer } from '@/models/customer/customer';
  import CustomersRepo from '@/repos/customers-repo';
  import { queryClient } from '@/utils/client';
  import { toast } from 'svelte-sonner';

  export let open: boolean;
  export let customer: SystemCustomer;

  const customersRepo = new CustomersRepo();

  let newSourceCustomerCode = customer?.sources?.[0]?.source_customer_code || '';

  async function updateCustomer() {
    try {
      const toUpdate = JSON.parse(JSON.stringify(customer));
      const source = toUpdate.sources?.[0];
      if (!source) throw new Error('No source available to update');
      source.source_customer_code = newSourceCustomerCode;

      const response = await customersRepo.update({
        system_customer: { ...toUpdate } as SystemCustomer
      });
      if (!response.result) throw new Error('Failed to update customer');
    } catch (err) {
      throw err;
    }
  }

  async function updateSourceCustomerCode() {
    if (newSourceCustomerCode === customer?.sources?.[0].source_customer_code) {
      open = false;
      return;
    }

    if (!customer.sources?.[0]) {
      alert('No source available to update');
      return;
    }

    await toast.promise(await updateCustomer, {
      loading: 'Updating source customer code...',
      success: () => {
        queryClient.resetQueries();
        return 'Source customer code updated';
      },
      error: (err) => {
        queryClient.resetQueries();
        return `Error updating source customer code: ${err}`;
      }
    });
    open = false;
  }
</script>

<PromptModal
  title="Edit source customer code"
  size="narrow"
  position="center"
  bind:open
  actionClick={updateSourceCustomerCode}
  actionText="Update"
>
  <FormInputGroup>
    <TextInput label="Source customer code" bind:value={newSourceCustomerCode} />
  </FormInputGroup>
</PromptModal>
