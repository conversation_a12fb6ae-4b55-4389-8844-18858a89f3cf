<script lang="ts">
  /* eslint-disable  @typescript-eslint/no-unsafe-member-access */
  import { page } from '$app/stores';
  import Select from '@/components/Controls/Select/Select.svelte';
  import DynamicFieldSelector from '@/components/Controls/Select/FieldSelector/DynamicFieldSelector.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import Loader from '@/components/Loader/Loader.svelte';
  import type SystemCustomerUser from '@/models/customer-users.ts/customer-user';
  import type { SystemCustomer } from '@/models/customer/customer';
  import type { User } from '@/models/user';
  import type ObjectVersionControl from '@/models/version-controlled';
  import { createCustomerUsersQuery } from '@/queries/customerUsers.query';
  import ChannelsRepo from '@/repos/channels-repo';
  import SourcesRepo from '@/repos/sources-repo';
  import { createQuery } from '@tanstack/svelte-query';
  import { getContext } from 'svelte';
  import type { Readable } from 'svelte/store';
  import SubHeading from '@/components/Controls/Text/SubHeading.svelte';
  import { isNumber } from '@/utils/typeguards';

  const customer = getContext<ObjectVersionControl<SystemCustomer>>('customer');
  const customerUser = getContext<ObjectVersionControl<SystemCustomerUser>>('customerUser');
  const channelsRepo = new ChannelsRepo();
  const sourcesRepo = new SourcesRepo();

  const channelData = createQuery({
    queryKey: ['channels-get'],
    queryFn: () => channelsRepo.get()
  });
  const sourcesData = createQuery({ queryKey: ['sources-get'], queryFn: () => sourcesRepo.get() });

  $: customerUsersData = createCustomerUsersQuery(parseInt($page.params.id));

  $: channels = $channelData.data;

  $: sources = $sourcesData.data || [];
  $: hasData = !isLoading && channels && sources;
  $: isLoading = $channelData.isLoading || $sourcesData.isLoading || $customerUsersData.isLoading;
  $: systemChannel =
    (channels && channels.find((c) => c.id === customer.workingCopy?.channel_id)) || null;

  const derivedUser = getContext<Readable<User>>('derivedUser');
</script>

<div class="px-8 py-8">
  <SubHeading>Settings</SubHeading>
  {#if isLoading}
    <Loader />
  {/if}
  {#if hasData && $customer.workingCopy && $customer.baseServerVersion}
    <div class="flex flex-col gap-4">
      <Select
        label="Channel"
        options={channels?.map((v) => ({ value: v.id, label: v.description })) ?? []}
        disabled={isNumber($customer.baseServerVersion?.channel_id)}
        bind:value={$customer.workingCopy.channel_id}
        placeholder="Please select"
        required
      />

      {#if systemChannel && systemChannel.type !== 'trade'}
        <TextInput
          label="Customer ID on the channel"
          bind:value={$customer.workingCopy.channel_customer_code}
          required
        />
      {/if}

      {#if $derivedUser && $customerUser.workingCopy}
        <DynamicFieldSelector
          bind:value={$customerUser.workingCopy.price_tier}
          entity="product"
          group="price"
          title="Price Tier"
        />
        <DynamicFieldSelector
          bind:value={$customerUser.workingCopy.qty_availability}
          entity="product"
          group="qty"
          title="Warehouse"
        />
      {/if}

      {#if $customer.workingCopy?.sources?.[0]}
        <Select
          label="Source"
          bind:value={$customer.workingCopy.sources[0].source_id}
          disabled={typeof $customer.workingCopy.sources[0].source_id === 'number'
            ? $customer.workingCopy.sources[0].source_id > 0
            : false}
          options={sources.map((o) => ({ label: o.description, value: o.id }))}
          placeholder="Please select"
        />
        <TextInput
          label="Source Customer Code"
          bind:value={$customer.workingCopy.sources[0].source_customer_code}
          required
        />
      {/if}
    </div>
  {/if}
</div>
