<script lang="ts">
  import IconMapPin from '@/components/Icons/IconMapPin.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import type { Address } from '@/models/customer/address';
  import Select from '@/components/Controls/Select/Select.svelte';
  import { onDestroy } from 'svelte';
  import PromptModal from '@/components/Modal/PromptModal.svelte';
  import ModalInputGroup from '@/components/Controls/Group/ModalInputGroup.svelte';
  import InputWrapper from '@/components/Controls/Group/InputWrapper.svelte';
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';

  export let saveAction: () => void;
  export let address: Address;
  export let open = false;
  export let editing = false;

  const typeOptions = [
    { value: 'shipping', label: 'Shipping Address' },
    { value: 'billing', label: 'Billing Address' }
  ];

  onDestroy(() => {
    editing = false;
  });

  $: actionText = `${editing ? `Edit` : `Add`} Address`;
</script>

<PromptModal bind:open {actionText} title={actionText} actionClick={saveAction} icon={IconMapPin}>
  <InputWrapper>
    {#if address}
      <ModalInputGroup title="Settings">
        <Select
          label="Type"
          options={typeOptions}
          bind:value={address.type}
          placeholder="Please select"
          required
        />
        <TextInput
          label="Address Code"
          bind:value={address.address_code}
          info="From Source or Channel"
        />
        <Toggle label="Default Address" bind:checked={address.default} />
      </ModalInputGroup>

      <ModalInputGroup title="Address">
        <TextInput label="Address Line 1" bind:value={address.address1} />
        <TextInput label="Address Line 2" bind:value={address.address2} />
        <TextInput label="City" bind:value={address.city} />
        <TextInput label="Province" bind:value={address.province} />
        <TextInput label="Province Code" bind:value={address.province_code} />
        <TextInput label="Postal Code" bind:value={address.zip} />
        <TextInput label="Country" bind:value={address.country} />
        <TextInput label="Country Code" bind:value={address.country_code} />
      </ModalInputGroup>

      <ModalInputGroup title="Contact">
        <TextInput label="Contact Company" bind:value={address.company} />
        <TextInput label="Contact First Name" bind:value={address.first_name} />
        <TextInput label="Contact Last Name" bind:value={address.last_name} />
        <TextInput label="Contact Phone" bind:value={address.phone} />
      </ModalInputGroup>
    {/if}
  </InputWrapper>
</PromptModal>
