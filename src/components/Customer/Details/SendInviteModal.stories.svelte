<script context="module">
  import SendInviteModal from '@/components/Customer/Details/SendInviteModal.svelte';
  export const meta = {
    title: 'components/Customer/Details/SendInviteModal',
    component: SendInviteModal,
    parameters: {
      layout: 'fullscreen'
    }
  };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';
  import { onMount } from 'svelte';
  import type { SystemCustomer } from '@/models/customer/customer';
  import customersESResponse from '@/stories/test-data/customers/customers-es-response.json';
  import type SystemCustomersDataWrapper from '@/models/customer/customers-data-wrapper';
  import type { ESData } from '@/lib/es/types';

  let customers: SystemCustomer[] = [];

  const singleUserId = [251522];
  const userIds = [251522, 251491];

  onMount(async () => {
    let customersEs = (await customersESResponse) as SystemCustomersDataWrapper & ESData;
    customers = customersEs.system_customers;
  });

  function handleInviteResult(message: string): void {
    console.log('mock test');
  }
</script>

<Story name="Send_Invite_Modal">
  <div class="mr-6 h-full min-h-[900px] min-w-[260px] overflow-y-auto rounded bg-white shadow">
    {#if customers?.length}
      <SendInviteModal {customers} userIds={singleUserId} open={true} {handleInviteResult} />
    {/if}
  </div>
</Story>

<Story name="Bulk Send_Invite_Modal">
  <div class="mr-6 h-full min-h-[900px] min-w-[260px] overflow-y-auto rounded bg-white shadow">
    {#if customers?.length}
      <SendInviteModal {customers} {userIds} open={true} {handleInviteResult} />
    {/if}
  </div>
</Story>
