<script context="module">
  import AddressModal from '@/components/Customer/Details/AddressModal.svelte';

  export const meta = {
    title: 'components/Customer/Details/AddressModal',
    component: AddressModal,
    parameters: {
      layout: 'fullscreen'
    }
  };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';
  import type { Address } from '@/models/customer/address';

  let newAddress: Address = {};

  function addAddress(): void {
    console.log(newAddress);
  }
</script>

<Story name="Address_Modal">
  <div class="mr-6 h-full min-h-[900px] min-w-[260px] overflow-y-auto rounded bg-white shadow">
    <AddressModal bind:address={newAddress} open={true} saveAction={addAddress} />
  </div>
</Story>
