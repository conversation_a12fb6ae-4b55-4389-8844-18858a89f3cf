<script lang="ts">
  import Btn from '@/components/form/Btn.svelte';
  import IconAdd from '@/components/Icons/IconAdd.svelte';
  import type { SystemCustomer } from '@/models/customer/customer';
  import type { Address } from '@/models/customer/address';
  import type ObjectVersionControl from '@/models/version-controlled';
  import AddressModal from './AddressModal.svelte';
  import AddressList from './AddressList.svelte';
  import { getContext } from 'svelte';
  import cloneDeep from 'lodash.clonedeep';
  import { toast } from 'svelte-sonner';
  import SubHeading from '@/components/Controls/Text/SubHeading.svelte';

  const customer = getContext<ObjectVersionControl<SystemCustomer>>('customer');
  let addressToEdit: Address;
  let openModal: boolean;
  let editing = false;

  const saveCustomerChanges = getContext<() => Promise<void>>('saveCustomerChanges');

  const openAddAddressModal = () => {
    addressToEdit = {
      id: Math.round(Math.random() * 100000000), // Used to locally as delete ref (won't persist on server)
      customer_id: customer.workingCopy.id,
      default: !$customer.workingCopy.addresses?.length,
      client_id: $customer.workingCopy.client_id
    };
    openModal = true;
  };

  const editAddress = (address: Address) => {
    editing = true;
    addressToEdit = cloneDeep(address);
    openModal = true;
  };

  const saveAction = () => {
    if (!$customer.workingCopy) {
      throw Error('Unexpected state');
    }

    if (!Array.isArray($customer.workingCopy.addresses)) {
      $customer.workingCopy.addresses = [];
    }

    addressToEdit.address_code = (addressToEdit.address_code || '').trim();

    if (
      $customer.workingCopy.addresses.find(
        (address) =>
          (address.address_code || '').toLowerCase() ===
            addressToEdit.address_code?.toLowerCase() &&
          (editing ? address.id !== addressToEdit.id : true)
      )
    ) {
      return addressToEdit.address_code === ''
        ? toast.error(
            'There is already an address with an empty address code, only one of these is supported'
          )
        : toast.error('An address with this address code already exists');
    }

    if (editing) {
      const otherDefaultIndex = $customer.workingCopy.addresses?.findIndex(
        (address) =>
          address.default === true &&
          address.type === addressToEdit.type &&
          !(addressToEdit.id === address.id)
      );

      if (
        typeof otherDefaultIndex === 'number' &&
        otherDefaultIndex > -1 &&
        addressToEdit.default
      ) {
        $customer.workingCopy.addresses[otherDefaultIndex].default = false;
      }

      const index = $customer.workingCopy.addresses.findIndex(
        (address) => address.id === addressToEdit.id
      );

      $customer.workingCopy.addresses[index] = addressToEdit;
    } else {
      if (
        $customer.workingCopy.addresses.filter((add) => add.type === addressToEdit.type).length ===
          0 &&
        !addressToEdit.default
      ) {
        return toast.error(
          `There are no other ${addressToEdit.type} addresses. Please set this address as default`
        );
      }
      $customer.workingCopy.addresses = [...$customer.workingCopy.addresses, addressToEdit];
    }

    // This will save the customer with the new address to prevent
    // them from needing to click Save Changes additionally
    if (saveCustomerChanges) {
      toast.promise(saveCustomerChanges, {
        loading: `${editing ? `Updating` : `Adding`} address...`,
        error: (e) => (e as Error).message || `Failed to ${editing ? `update` : `add`} address`,
        success: `${editing ? `Updated` : `Added`} address`
      });
    }
    openModal = false;
  };
</script>

<div class="flex justify-between">
  <SubHeading>Addresses</SubHeading>
  <Btn
    onClick={openAddAddressModal}
    class="text-right"
    size="small"
    variant="outline"
    Prefix={IconAdd}>Add Address</Btn
  >
</div>
<div class="flex flex-col gap-4">
  <AddressList bind:addresses={$customer.workingCopy.addresses} type="shipping" {editAddress} />
  <AddressList bind:addresses={$customer.workingCopy.addresses} type="billing" {editAddress} />
</div>

{#if openModal}
  <AddressModal bind:address={addressToEdit} bind:open={openModal} {saveAction} bind:editing />
{/if}
