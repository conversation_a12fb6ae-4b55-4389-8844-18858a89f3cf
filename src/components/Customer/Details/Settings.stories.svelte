<script context="module">
  import Settings from '@/components/Customer/Details/Settings.svelte';
  export const meta = {
    title: 'components/Customers/Settings',
    component: Settings
  };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';
  import { onMount, setContext } from 'svelte';

  import type { SystemCustomer } from '@/models/customer/customer';
  import ObjectVersionControl from '@/models/version-controlled';
  import CustomersRepo from '@/repos/customers-repo';
  import { page } from '$app/stores';

  const customersRepo = new CustomersRepo();
  const customer = new ObjectVersionControl<SystemCustomer>({});

  setContext('customer', customer);

  $page.params.id = '1';

  onMount(async () => {
    const customerData = await customersRepo.get(parseInt($page.params.id));
    customer.update(customerData);
  });
</script>

<Story name="Customer_Settings">
  <div class="mr-6 h-full w-[260px] overflow-y-auto rounded bg-white shadow">
    {#if Object.keys($customer.workingCopy).length}
      <Settings />
    {/if}
  </div>
</Story>
