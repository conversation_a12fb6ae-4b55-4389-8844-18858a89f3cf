<script lang="ts">
  import TextInput from '@/components/Controls/TextInput.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { SystemCustomer } from '@/models/customer/customer';
  import UsersRepo from '@/repos/users-repo';
  import IconClose from '@/components/Icons/IconClose.svelte';
  import IconMail from '@/components/Icons/IconMail.svelte';
  import SendInviteModal from './SendInviteModal.svelte';
  import type { InlineNotificationModel } from '@/models/inline-notification';
  import { getContext } from 'svelte';
  import type ObjectVersionControl from '@/models/version-controlled';
  import Button from '@/components/Controls/Button.svelte';
  import FormInputGroup from '@/components/Controls/Group/FormInputGroup.svelte';
  import { createUserQuery } from '@/queries/user.query';
  import ChannelCustomersRepo from '@/repos/channel-customers-repo';
  import { createMutation } from '@tanstack/svelte-query';

  const usersRepo = new UsersRepo();
  const channelCustomersRepo = new ChannelCustomersRepo();

  const customer = getContext<ObjectVersionControl<SystemCustomer>>('customer');

  const updateChannelCustomerMutation = createMutation({
    mutationFn: ({
      customer,
      channelId,
      channelCustomerCode
    }: {
      customer: SystemCustomer;
      channelId: number;
      channelCustomerCode: number;
    }) => channelCustomersRepo.put({ customer }, channelId, channelCustomerCode)
  });

  $: userId = $customer.workingCopy?.user?.user_id;

  $: userData = createUserQuery(userId);

  $: user = $userData.data;

  let showNewPassword = false;
  let password = '';
  let passwordConfirmation = '';

  let inlineNotification: InlineNotificationModel | undefined = undefined;
  let inviteCustomerModalOpen = false;

  async function sendPasswordReset() {
    try {
      const result = await usersRepo.sendPasswordReset(user.username);
      notify(result.message, 'success');
    } catch (e) {
      notify(e instanceof Error ? e.message : 'Error sending password reset email', 'error');
    }
  }

  function setNewPassword() {
    password = '';
    passwordConfirmation = '';
    showNewPassword = true;
  }

  const handleInviteResult = async (result, userIds: number[]) => {
    const notificationMessage = result.result
      ? `An invite was sent to ${$customer.workingCopy.email}`
      : `Failed to send invite to ${$customer.workingCopy.email}`;

    notify(notificationMessage, result.result ? 'info' : 'error');
    inviteCustomerModalOpen = false;
  };

  function openInviteModal() {
    inviteCustomerModalOpen = true;
  }

  function notify(message: string, type: 'error' | 'success' | 'info') {
    inlineNotification = { message, type };
  }

  const handleUpdatePassword = async (e: SubmitEvent) => {
    try {
      e.preventDefault();
      e.stopPropagation();

      if (password !== passwordConfirmation) {
        notify('Passwords do not match', 'error');
        return;
      }

      await $updateChannelCustomerMutation.mutateAsync({
        customer: {
          ...$customer.workingCopy,
          password: password,
          send_email_invite: false
        },
        channelId: $customer.workingCopy.channel_id,
        channelCustomerCode: $customer.workingCopy.channel_customer_code
      });

      notify('The password has successfully been set.', 'success');

      showNewPassword = false;
      password = '';
      passwordConfirmation = '';
    } catch (e) {
      notify(e instanceof Error ? e.message : 'Error updating password', 'error');
    }
  };

  function cancel() {
    showNewPassword = false;
  }

  $: userIds = $customer.workingCopy?.user?.user_id ? [$customer.workingCopy?.user?.user_id] : [];

  $: customerArray = [$customer.workingCopy];
</script>

<FormInputGroup title="Contact Details">
  <TextInput
    label="First name"
    sideLabel={true}
    bind:value={$customer.workingCopy.first_name}
    required
  />
  <TextInput
    label="Last name"
    sideLabel={true}
    bind:value={$customer.workingCopy.last_name}
    required
  />
  <TextInput
    label="Email"
    type="email"
    sideLabel={true}
    bind:value={$customer.workingCopy.email}
    required
  />
  {#if user}
    <!-- We currently dont support editing usernames -->
    <TextInput label="Username" sideLabel={true} isDisable bind:value={user.username} />

    <div class="flex flex-row items-center">
      <div class="min-w-[150px]" />
      <div
        class=" flex w-full max-w-[528px] flex-grow flex-row items-center justify-start gap-2 rounded bg-white text-regular text-neutral-700"
      >
        <Button variant="gray" on:click={setNewPassword}>Set New Password</Button>
        <Button variant="gray" on:click={sendPasswordReset}>Send Password Reset</Button>
        <Button icon={IconMail} variant="gray" on:click={openInviteModal}>Send Invite</Button>
      </div>
    </div>
    {#if showNewPassword}
      <form on:submit={handleUpdatePassword}>
        <FormInputGroup>
          <div class=" w-[80%]">
            <TextInput
              label="New password"
              minLength={12}
              type="password"
              sideLabel={true}
              bind:value={password}
              required
            />
          </div>
          <div class="flex items-center justify-between gap-2">
            <div class="w-[80%]">
              <TextInput
                label="Confirm password"
                minLength={12}
                type="password"
                sideLabel={true}
                bind:value={passwordConfirmation}
                required
              />
            </div>

            <Button
              type="submit"
              size="large"
              variant="solid"
              disabled={$updateChannelCustomerMutation.isPending}>Save</Button
            >

            <Button size="medium" on:click={cancel} variant="gray" icon={IconClose} />
          </div>
        </FormInputGroup>
      </form>
    {/if}

    {#if inviteCustomerModalOpen}
      <SendInviteModal
        bind:open={inviteCustomerModalOpen}
        bind:customers={customerArray}
        bind:userIds
        {handleInviteResult}
      />
    {/if}
  {/if}
</FormInputGroup>

<div class="mt-6">
  {#if inlineNotification}
    <div class="mt-2">
      <InlineNotification type={inlineNotification.type}
        >{inlineNotification.message}</InlineNotification
      >
    </div>
  {/if}
</div>
