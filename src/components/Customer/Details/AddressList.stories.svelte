<script context="module">
  import AddressList from '@/components/Customer/Details/AddressList.svelte';

  export const meta = {
    title: 'components/Customer/Details/AddressList',
    component: AddressList
  };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';
  import type { Address } from '@/models/customer/address';
  import { onMount } from 'svelte';
  import customers2sResponse from '@/stories/test-data/customers/customer-response.json';
  import type { SystemCustomerDataWrapper } from '@/models/customer/customer-data-wrapper';

  let addresses: Address[];
  onMount(() => {
    addresses = (customers2sResponse as SystemCustomerDataWrapper).system_customer.addresses;
  });
</script>

<Story name="Shipping">
  <div class="mr-6 h-full min-w-[260px] overflow-y-auto rounded bg-white">
    <AddressList
      bind:addresses
      type="shipping"
      editAddress={() => {
        alert('This will trigger edit modal');
      }}
    />
  </div>
</Story>

<Story name="Billing">
  <div class="mr-6 h-full min-w-[260px] overflow-y-auto rounded bg-white">
    <AddressList
      bind:addresses
      type="billing"
      editAddress={() => {
        alert('This will trigger edit modal');
      }}
    />
  </div>
</Story>
