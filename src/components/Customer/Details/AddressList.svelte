<script lang="ts">
  import ActionHeading from '@/components/Controls/Text/ActionHeading.svelte';
  import IconEdit from '@/components/Icons/IconEdit.svelte';
  import Btn from '@/components/form/Btn.svelte';
  import type { Address } from '@/models/customer/address';

  export let addresses: Address[] | undefined;
  export let type: 'shipping' | 'billing';
  let customerAddresses: Address[];
  export let editAddress: (address: Address) => void;

  $: customerAddresses = addresses?.length
    ? addresses
        .filter((address) => address.type === type)
        .sort((x, y) => Number(y.default) - Number(x.default))
    : [];

  const handleDelete = (id: number) => {
    addresses = addresses?.filter((address) => address.id !== id);
  };
</script>

<div class="mb-4">
  <ActionHeading>{type.toUpperCase()} ADDRESSES</ActionHeading>
</div>

{#if !customerAddresses.length}
  <p>No existing addresses</p>
{/if}

<div>
  <div class=" flex flex-wrap gap-2 self-center rounded-[5px]">
    {#each customerAddresses as address}
      <div
        class="relative flex w-[220px] flex-col justify-between rounded border border-neutral-200 p-5"
      >
        <div>
          {#if address.default}
            <span
              class="absolute right-[11px] top-[18px] rounded bg-brand-action bg-opacity-10 px-[6px] py-[2px] text-small text-brand-action"
              >Default</span
            >
          {/if}
          {#if address.company}
            <p
              class="max-w-[136px] overflow-hidden text-ellipsis pb-4 text-regular font-bold text-neutral-700"
            >
              {address.company}
            </p>
          {/if}

          {#if address.address1}
            <p class="text-regular text-neutral-700">{address.address1}</p>
          {/if}

          {#if address.address2}
            <p class="text-regular text-neutral-700">{address.address2}</p>
          {/if}

          {#if address.city}
            <p class="text-regular text-neutral-700">{address.city}</p>
          {/if}

          {#if address.province}
            <p class="text-regular text-neutral-700">{address.province}</p>
          {/if}

          {#if address.province_code}
            <p class="text-regular text-neutral-700">{address.province_code}</p>
          {/if}

          {#if address.zip}
            <p class="text-regular text-neutral-700">{address.zip}</p>
          {/if}

          {#if address.country}
            <p class="text-regular text-neutral-700">{address.country}</p>
          {/if}

          {#if address.country_code}
            <p class="text-regular text-neutral-700">{address.country_code}</p>
          {/if}

          <div class="py-4">
            {#if address.first_name}
              <p class="text-regular text-neutral-700">{address.first_name} {address.last_name}</p>
            {/if}
            {#if address.phone}
              <p class="text-regular text-neutral-700">Tel: {address.phone}</p>
            {/if}
          </div>
        </div>
        <div class="flex w-full justify-between pt-2">
          <!-- ToDo: Delete addresses functionality will be added when the new endpoint is implemented  -->
          <!-- {#if !address.default}
            <Btn onClick={() => handleDelete(address.id)} size="small" Prefix={IconDelete} />
          {/if} -->

          <p class="label self-center">{address.address_code ?? ''}</p>

          <Btn onClick={() => editAddress(address)} size="small" Prefix={IconEdit} />
        </div>
      </div>
    {/each}
  </div>
</div>
