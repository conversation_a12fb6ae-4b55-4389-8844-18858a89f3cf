<script lang="ts">
  import IconMail from '@/components/Icons/IconMail.svelte';
  import type { SystemCustomer } from '@/models/customer/customer';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import ChannelsRepo from '@/repos/channels-repo';
  import UsersRepo from '@/repos/users-repo';
  import type InviteRequest from '@/models/user/invite-request';
  import { createQuery } from '@tanstack/svelte-query';
  import PromptModal from '@/components/Modal/PromptModal.svelte';
  import InputWrapper from '@/components/Controls/Group/InputWrapper.svelte';
  import ModalInputGroup from '@/components/Controls/Group/ModalInputGroup.svelte';
  import TextArea from '@/components/Controls/TextArea.svelte';

  export let open = false;
  export let userIds: number[] = [];
  export let handleInviteResult: any;
  export let customers: SystemCustomer[] = [];

  const channelsRepo = new ChannelsRepo();
  const usersRepo = new UsersRepo();

  let userMessage = '';
  let subject = '';
  let loading = false;

  $: channelData = createQuery({ queryKey: ['channels-get'], queryFn: () => channelsRepo.get() });

  $: channels = $channelData.data || [];

  $: channelMetaResult = createQuery({
    queryKey: ['channelMeta', channelId],
    queryFn: () => channelsRepo.getChannelMeta(channelId),
    enabled: !!channelId
  });

  $: template =
    $channelMetaResult.data?.find(
      (meta) => meta.key === 'custom_user_invite_message_markdown_template'
    )?.value || '';

  $: message = userMessage || template;

  $: channelId = channels.find((channel) => channel.type === 'trade' && channel.active === 1)?.id;

  $: customer =
    userIds?.length === 1
      ? customers.find((c) => c.user.user_id === userIds[0] || c.user.id === userIds[0])
      : null;

  $: isLoading = $channelMetaResult.isLoading || loading;

  $: isBulk = userIds?.length > 1;

  async function sendInvite() {
    loading = true;
    const request: InviteRequest = {
      subject_template: subject,
      message_template: message,
      user_ids: userIds
    };

    if (subject.length >= 50000 || message.length >= 50000) {
      return alert('Maximum message length is 50 000 characters.');
    }

    const result = await usersRepo.bulkInvite(channelId, request);
    loading = false;
    open = false;

    handleInviteResult(result, request.user_ids);
  }
</script>

<PromptModal
  title={`Invite ${isBulk ? `${userIds.length} customers` : customer?.first_name}`}
  bind:open
  disabled={isLoading}
  actionText="Send"
  icon={IconMail}
  actionClick={sendInvite}
>
  <InputWrapper>
    <ModalInputGroup hideSeparator>
      <InlineNotification type="info"
        >An email will be sent to
        <b>
          {isBulk ? `${userIds.length} customers` : `${customer?.first_name}`}</b
        ></InlineNotification
      >
      <TextInput label="Subject" required bind:value={subject} />
      <TextArea label="Add a custom message" rows={10} bind:value={message} />
    </ModalInputGroup>
  </InputWrapper>
</PromptModal>
