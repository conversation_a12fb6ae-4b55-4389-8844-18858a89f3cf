<script lang="ts">
  import Addresses from './Details/Addresses.svelte';
  import ContactDetails from './Details/ContactDetails.svelte';
  import Settings from './Details/Settings.svelte';
</script>

<div class="w-8/12 overflow-y-auto border-r bg-white">
  <div class="w-full border-b border-neutral-200 px-10 py-8">
    <ContactDetails />
  </div>

  <div class="px-[40px] py-[32px]">
    <Addresses />
  </div>
</div>
<div class="w-4/12 min-w-[300px] overflow-y-auto bg-white">
  <Settings />
</div>
