<script lang="ts">
  import SimpleCustomerDetail from '@/components/Customer/SimpleCustomerDetail.svelte';
  import UserCustomerDetail from '@/components/Customer/UserCustomerDetail.svelte';
  import type { User } from '@/models/user';

  import { getContext } from 'svelte';
  import type { Readable } from 'svelte/store';

  const derivedUser = getContext<Readable<User>>('derivedUser');
</script>

{#if $derivedUser}
  <UserCustomerDetail />
{:else}
  <SimpleCustomerDetail />
{/if}
