<script lang="ts">
  import type { SystemCustomer } from '@/models/customer/customer';
  import type ObjectVersionControl from '@/models/version-controlled';
  import ChannelsRepo from '@/repos/channels-repo';
  import { createQuery } from '@tanstack/svelte-query';
  import { getContext } from 'svelte';
  import Loader from '../Loader/Loader.svelte';
  import SourcesRepo from '@/repos/sources-repo';
  import { page } from '$app/stores';
  import type { SystemChannel } from '@/models/channels/system-channel';
  import { createCustomerUsersQuery } from '@/queries/customerUsers.query';
  import SubHeading from '@/components/Controls/Text/SubHeading.svelte';
  import Button from '@/components/Controls/Button.svelte';
  import IconEdit from '@/components/Icons/IconEdit.svelte';
  import UpdateSourceCustomerCodeModal from '@/components/Customer/UpdateSourceCustomerCodeModal.svelte';

  const channelsRepo = new ChannelsRepo();
  const sourcesRepo = new SourcesRepo();

  const channelData = createQuery({
    queryKey: ['channels-get'],
    queryFn: () => channelsRepo.get()
  });

  const sourcesData = createQuery({ queryKey: ['sources-get'], queryFn: () => sourcesRepo.get() });
  // ToDo: Double check understanding of customer users - since this seems to have price_tier for non-user customers
  $: customerUsersData = createCustomerUsersQuery(parseInt($page.params.id));

  const customer = getContext<ObjectVersionControl<SystemCustomer>>('customer');

  let channel: SystemChannel | undefined;
  let editSourceCustomerCodeModalOpen = false;
  $: channel = $channelData.data?.find((ch) => ch.id === $customer.workingCopy?.channel_id);
</script>

{#if $channelData.isLoading || $sourcesData.isLoading || !$customer.workingCopy}
  <Loader />
{:else}
  <div class="grid grid-cols-2">
    <div class="flex overflow-y-auto border-r">
      <div class="w-full border-neutral-200 px-10 py-8">
        <SubHeading>Details</SubHeading>
        <div class="relative mb-4 flex flex-col justify-between gap-2">
          {#if !$customer.workingCopy.first_name && !$customer.workingCopy.last_name && !$customer.workingCopy.email}
            <p class="label">No details available</p>
          {:else}
            {#if $customer.workingCopy?.first_name}
              <div class="flex w-full items-center gap-3">
                <p class="label w-40">First name</p>
                <p>{$customer.workingCopy.first_name}</p>
              </div>
            {/if}
            {#if $customer.workingCopy?.last_name}
              <div class="flex w-full items-center gap-3">
                <p class="label w-40">Last name</p>
                <p>{$customer.workingCopy.last_name}</p>
              </div>
            {/if}
            {#if $customer.workingCopy?.email}
              <div class="flex w-full items-center gap-3">
                <p class="label w-40">Email</p>
                <p>{$customer.workingCopy.email}</p>
              </div>
            {/if}
          {/if}
        </div>
      </div>
    </div>
    <div class="overflow-y-auto">
      {#if channel}
        <div class="w-full px-10 py-8">
          <SubHeading>Channel</SubHeading>
          <div class="flex w-full items-center gap-3">
            <p class="label w-40 leading-4">Name</p>
            <p>
              {channel.description}
            </p>
          </div>
          {#if $customer.workingCopy?.channel_customer_code}
            <div class="flex w-full items-center gap-3">
              <p class="label w-40 leading-4">ID on channel</p>
              <p>{$customer.workingCopy.channel_customer_code}</p>
            </div>
          {/if}
        </div>
      {/if}
      {#if $customer.workingCopy.sources?.[0]}
        <div class="flex w-full flex-col gap-3 border-t border-neutral-200 px-10 py-8">
          <SubHeading>Source</SubHeading>

          <div class="flex w-full items-center gap-3">
            <p class="label w-40 leading-4">Name</p>
            <p>
              {$sourcesData.data?.find(
                (s) => s.id === $customer.workingCopy?.sources?.[0].source_id
              )?.description}
            </p>
          </div>
          <div class="flex w-full items-center gap-3">
            <p class="label w-40 leading-4">Source customer code</p>
            <p class="flex-1">{$customer.workingCopy.sources[0].source_customer_code || ''}</p>
            <Button
              on:click={() => (editSourceCustomerCodeModalOpen = true)}
              icon={IconEdit}
              size="small"
              variant="gray"
            ></Button>
          </div>
        </div>
      {/if}
      {#if $customerUsersData?.data?.[0]?.price_tier}
        <div class="w-full border-t border-neutral-200 px-10 py-8">
          <SubHeading>Price Tier</SubHeading>
          <p>
            {$customerUsersData.data[0].price_tier}
          </p>
        </div>
      {/if}
    </div>
  </div>
{/if}

{#if editSourceCustomerCodeModalOpen && $customer.workingCopy}
  <UpdateSourceCustomerCodeModal
    bind:customer={$customer.workingCopy}
    bind:open={editSourceCustomerCodeModalOpen}
  />
{/if}
