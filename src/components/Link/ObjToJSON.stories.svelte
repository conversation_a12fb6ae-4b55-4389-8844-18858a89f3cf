<script context="module">
  import ObjToJSON from '@/components/Link/ObjToJSON.svelte';

  export const meta = {
    title: 'components/Link/ObjToJSON',
    component: ObjToJSON
  };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';

  let obj1 = {
    name: '<PERSON>',
    age: 30,
    city: 'New York'
  };
  let obj1Copy = obj1;

  const obj2 = {
    name: '<PERSON>',
    product_id: 123,
    product_ids: [123, 456],
    customer: {
      customer_id: 678
    },
    variants: [
      {
        sku: 'a'
      },
      {
        sku: 'b'
      }
    ],
    city: 'New York'
  };
</script>

<Story name="No Links">
  <div id="Page">
    <h2>Before</h2>
    <pre>{JSON.stringify(obj1Copy, null, 2)}</pre>
    <h2>After</h2>
    <pre><ObjToJSON obj={obj1} /></pre>
  </div>
</Story>

<Story name="Links">
  <div id="Page">
    <h2>Before</h2>
    <pre>{JSON.stringify(obj2, null, 2)}</pre>
    <h2>After</h2>
    <pre><ObjToJSON obj={obj2} /></pre>
  </div>
</Story>
