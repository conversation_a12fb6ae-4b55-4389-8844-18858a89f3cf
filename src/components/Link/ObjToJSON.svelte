<script lang="ts">
  import { createConfigQuery } from '@/queries/config.query';

  const configQuery = createConfigQuery();

  export let obj: unknown | undefined;

  const findReplaceList = [
    {
      find: 'channel_id',
      href: '/config/channels/{connectorType}/{value}'
    },
    {
      find: 'source_id',
      href: '/config/sources/{connectorType}/{value}'
    },
    {
      find: 'product_id',
      href: '/product/{value}/details'
    },
    {
      find: 'product_ids',
      href: '/product/{value}/details'
    },
    {
      find: 'customer_id',
      href: '/customer/{value}/details'
    },
    {
      find: 'customer_ids',
      href: '/customer/{value}/details'
    },
    {
      find: 'order_id',
      href: '/order/{value}/details'
    },
    {
      find: 'order_ids',
      href: '/order/{value}/details'
    }
  ];

  /**
   * Recursively go through obj and replace known properties with links.
   */
  const linkify = (obj): string => {
    if (!obj) {
      return '';
    }
    for (const key in obj) {
      if (typeof obj[key] === 'object') {
        if (Array.isArray(obj[key])) {
          if (typeof obj[key][0] === 'object') {
            for (let i = 0; i < obj[key].length; i++) {
              linkify(obj[key][i]);
            }
          } else {
            const res = [];
            for (let i = 0; i < obj[key].length; i++) {
              res.push(replaceKnownProperties(key, obj[key][i]));
            }
            obj[key] = res;
          }
        } else {
          linkify(obj[key]);
        }
      } else {
        obj[key] = replaceKnownProperties(key, obj[key]);
      }
    }
    return JSON.stringify(obj, null, 2);
  };

  /**
   * Replace known properties with links
   */
  $: replaceKnownProperties = (key: string, v: unknown): unknown => {
    for (const f of findReplaceList) {
      if (f.find === 'source_id' && key === f.find) {
        const source = $configQuery.data?.sources[v as string];
        if (!source) return v;
        const link = f.href.replace('{connectorType}', source.type).replace('{value}', v as string);
        return `<a title='${link}' class='text-brand-notification' href='${link}'>${v}</a>`;
      } else if (f.find === 'channel_id' && key === f.find) {
        const channel = $configQuery.data?.channels[v as string];
        if (!channel) return v;
        const link = f.href
          .replace('{connectorType}', channel.type)
          .replace('{value}', v as string);
        return `<a title='${link}' class='text-brand-notification' href='${link}'>${v}</a>`;
      } else if (key === f.find) {
        const link = f.href.replace('{value}', v as string);
        return `<a title='${link}' class='text-brand-notification' href='${link}'>${v}</a>`;
      }
    }
    return v;
  };

  $: linkified = linkify(obj);
</script>

<!--
@component
Recursively Creates links on json data for known properties
Warning! this binds html so this should only be used with trusted data
-->
{@html linkified}

<style>
  a {
    @apply text-brand-notification;
  }
</style>
