<script context="module">
  export const meta = {
    title: 'components/Header/IsActiveDropdown',
    component: IsActiveDropdown
  };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';
  import IsActiveDropdown from '@/components/Header/IsActiveDropdown.svelte';
</script>

<Story name="Active State">
  <IsActiveDropdown isActive={1} />
</Story>

<Story name="Inactive State">
  <IsActiveDropdown isActive={0} />
</Story>
