<script lang="ts">
  import { userStore } from '@/lib/s2sStore';
  import IconLogout from '@/components/Icons/IconLogout.svelte';
  import { fly } from 'svelte/transition';
  import handleClickOutside from '@/utils/actions/handleClickOutside';
  import IconCustomer from '@/components/Icons/IconCustomer.svelte';
  import Icon from '@/components/Icons/Icon.svelte';
  import { createConfigQuery } from '@/queries/config.query';
  import Skeleton from '@/components/Loader/Skeleton.svelte';
  import { createUserQuery } from '@/queries/user.query';
  import { handleLogout } from '@/utils/auth/logout';
  import { envConfig } from '@/constants';
  import IconBack from '@/components/Icons/IconBack.svelte';

  let showProfileMenu = false;
  let container: HTMLElement;

  // Toggle menu
  function toggleMenu() {
    showProfileMenu = !showProfileMenu;
  }

  const configQuery = createConfigQuery();
  $: userQuery = createUserQuery($userStore.id);

  $: clientName = $configQuery.data?.client_name;
</script>

<button
  class="relative ml-4 flex w-[180px] cursor-pointer rounded border border-brand-search px-2.5 py-1.5 text-left"
  on:click={() => toggleMenu()}
  bind:this={container}
  use:handleClickOutside={() => (showProfileMenu = false)}
>
  <div class="flex h-7 w-7 items-center justify-center rounded-full bg-brand-search">
    <IconCustomer />
  </div>
  <div class="ml-2.5 flex flex-col gap-1">
    {#if $configQuery.isLoading}
      <Skeleton class="h-[11px] w-16" shimmer />
    {:else}
      <p class="text-small font-bold leading-none text-white">{clientName}</p>
    {/if}
    {#if $userQuery.isLoading}
      <Skeleton class="h-3 w-10" shimmer />
    {:else}
      <p class="text-small leading-none text-neutral-300">
        {$userQuery?.data?.name ?? $userQuery.data?.username}
      </p>
    {/if}
  </div>

  {#if showProfileMenu}
    <div
      class="absolute left-0 top-full z-30 w-full rounded bg-white py-2 shadow"
      transition:fly={{ y: -2, duration: 100 }}
    >
      <a
        href={`${envConfig.consoleV1URL}/?action=login&token=${$userStore.token}`}
        target="_blank"
        class="flex items-center px-4 py-2 text-regular text-neutral-700 hover:bg-neutral-50"
      >
        <Icon IconComponent={IconBack} size="i-5" />
        <span class="ml-4">Old Console</span>
      </a>
      <a
        href="/settings/users/{$userStore.id}"
        class="flex items-center px-4 py-2 text-regular text-neutral-700 hover:bg-neutral-50"
      >
        <Icon IconComponent={IconCustomer} size="i-5" />
        <span class="ml-4">Profile</span>
      </a>
      <button
        on:click={() => {
          handleLogout({ showToast: true });
        }}
        class="flex w-full items-center px-4 py-2 text-regular text-neutral-700 hover:bg-neutral-50"
      >
        <Icon IconComponent={IconLogout} size="i-5" />

        <span class="ml-4">Logout</span>
      </button>
    </div>
  {/if}
</button>
