<script lang="ts">
  import { generateId } from '@/utils/generateId';

  export let isActive: number;
  export let id = generateId();
  export let onValueChanged: (e: Event) => Promise<void> = null;
  export let height = 'h-8';

  const options = [
    { id: 1, value: 'Active' },
    { id: 0, value: 'Inactive' }
  ];
</script>

<div class="relative flex items-center">
  <select
    {id}
    role="list"
    on:change={onValueChanged}
    class="h border-neutral-200 bg-transparent p-0 px-7 text-regular font-normal text-neutral-700 opacity-100 {height} w-[108px] rounded-[100px]"
    bind:value={isActive}
    on:click|stopPropagation={(e) => {}}
  >
    {#each options as option}
      <option value={option.id}>{option.value}</option>
    {/each}
  </select>
  <span
    class:bg-brand-action={!isActive}
    class:bg-brand-confirmation={isActive}
    class="absolute left-2.5 z-50 inline-block h-[12px] w-[12px] rounded-[50%] bg-brand-action"
  />
</div>
