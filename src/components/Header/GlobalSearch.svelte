<script lang="ts">
  import SearchProducts from '@/components/Header/SearchProducts.svelte';
  import SearchOrders from '@/components/Header/SearchOrders.svelte';
  import SearchCustomers from '@/components/Header/SearchCustomers.svelte';
  import IconSearch from '../Icons/IconSearch.svelte';
  import clickOutside from '@/utils/actions/handleClickOutside';
  import { fade, fly } from 'svelte/transition';
  import { shortcut, type ShortcutEventDetail } from '@svelte-put/shortcut';
  import { UAParser } from 'ua-parser-js';
  import Kbd from '../Kbd.svelte';
  import { cn } from '@/utils/classname';
  import Icon from '@/components/Icons/Icon.svelte';

  const uaParser = new UAParser();
  $: isMac = uaParser.getOS().name?.includes('Mac');

  let searchValue = '';
  let query = '';
  let isSearching = false;
  let timeout: number;
  const onFocus = () => {
    isSearching = true;
  };
  const onBlur = () => {
    isSearching = false;
    searchValue = '';
    query = '';
  };
  const typeahead = () => {
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => {
      query = searchValue;
    }, 300);
  };
  let searchInput: HTMLInputElement;

  const handleShortcutKeydown = (detail: ShortcutEventDetail) => {
    searchInput.focus();
  };

  function handleEscapeKeydown(detail: ShortcutEventDetail) {
    searchInput.blur();
    onBlur();
  }
</script>

<svelte:window
  use:shortcut={{
    trigger: [
      {
        key: 'k',
        modifier: ['ctrl', 'meta'],
        preventDefault: true,
        enabled: !isSearching,
        callback: handleShortcutKeydown
      },
      { key: 'Escape', preventDefault: true, enabled: isSearching, callback: handleEscapeKeydown }
    ]
  }}
/>

<div class="relative w-full max-w-[520px]">
  <div class="relative z-40">
    <input
      type="text"
      class="w-full rounded border-0 bg-brand-search py-2.5 pl-11 pr-4
        text-regular placeholder-neutral-200 transition-all duration-[400ms] focus:border-0 focus:bg-white focus:text-neutral-800 focus:placeholder-neutral-500
        focus:shadow-none focus:ring-0"
      class:rounded-b-none={query !== ''}
      placeholder="Search for products, customers or orders"
      bind:value={searchValue}
      on:input={typeahead}
      on:focus={onFocus}
      use:clickOutside={onBlur}
      bind:this={searchInput}
    />
    <div
      class={cn('absolute left-3 top-[10px] transition-colors duration-[400ms]', {
        'text-brand-brand': isSearching
      })}
    >
      <Icon IconComponent={IconSearch} size="i-6" />
    </div>
    {#if !isSearching}
      <div
        in:fade={{ duration: 400 }}
        class={cn('absolute right-3 top-3 flex gap-[2px] opacity-80', isMac && 'right-4')}
      >
        {#if !isMac}
          <Kbd class=" pr-[2px] text-xs">Ctrl</Kbd>
        {/if}
        <Kbd>
          {#if isMac}
            <div class=" font-serif text-xs">⌘</div>{/if}K
        </Kbd>
      </div>
    {/if}
  </div>
  {#if isSearching && query !== ''}
    <div
      class="search-results absolute left-0 top-full z-30 w-full rounded-b border
      border-t-0 border-neutral-200 bg-white text-neutral-800 shadow"
      transition:fly={{ y: -20, duration: 80 }}
    >
      <SearchProducts {query} />
      <SearchOrders {query} />
      <SearchCustomers {query} />
    </div>
  {/if}
</div>
