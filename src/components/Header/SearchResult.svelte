<script lang="ts">
  export let href: string;
  export let icon;
  export let title;
  export let code;
  export let description;
</script>

<a {href} class="search-group-item flex items-center px-4 py-1.5 hover:bg-neutral-50">
  <svelte:component this={icon} />
  <div class="ml-3.5 w-full leading-4">
    <div class="search-group-item-title text-regular font-bold text-neutral-800">{title}</div>
    <div class="text-small">
      <span class="mr-2 text-neutral-500">{code}</span>
      <span class="text-neutral-800">{description}</span>
    </div>
  </div>
</a>
