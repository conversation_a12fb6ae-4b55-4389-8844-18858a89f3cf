<script lang="ts">
  import { postProductsES } from '@/lib/s2s/products';
  import SearchResult from '@/components/Header/SearchResult.svelte';
  import type { DSL } from '@/lib/es/types';
  import createGlobalSearchRequest from '@/components/Header/utils/createGlobalSearchRequest';
  import debounce from 'just-debounce-it';
  import type { SystemProductsResponse } from '@/lib/s2s/types';
  import { createQuery } from '@tanstack/svelte-query';
  import IconProduct from '../Icons/IconProduct.svelte';
  import SkeletonText from '../Loader/SkeletonText.svelte';

  export let query = '';

  let debouncedQuery = '';

  const setDebouncedQuery = debounce((q: string) => {
    debouncedQuery = q;
  }, 100);

  $: setDebouncedQuery(query);

  const queryPrefixKey = 'global-search-products';
  let requestBody: DSL = createGlobalSearchRequest(query);

  $: data = createQuery({
    queryKey: [queryPrefixKey, requestBody],
    queryFn: () => postProductsES(requestBody)
  });

  let results: SystemProductsResponse;
  $: results = $data.data;
  $: isFetching = $data.isFetching;
  $: isLoading = $data.isLoading;
  $: if (debouncedQuery.length > 0) {
    requestBody = createGlobalSearchRequest(debouncedQuery);
  }
</script>

<div class="search-group border-t py-4">
  <div
    class="search-group-title mx-4 text-[10px] font-bold uppercase leading-4 tracking-widest text-neutral-500"
  >
    Products
  </div>

  <div>
    {#if isLoading}
      <div class="ml-4 mt-1 flex flex-col gap-1 opacity-50">
        <SkeletonText class="h-5" shimmer />
        <SkeletonText class="h-5" shimmer />
        <SkeletonText class="h-5" shimmer />
      </div>
    {:else}
      <div>
        {#if results.system_products && results.system_products.length > 0}
          {#each results.system_products as p (p.id)}
            <SearchResult
              icon={IconProduct}
              href="/product/{p.id}"
              title={p.title ?? ''}
              code="s2s: {p.id ?? 'none'} | source: {p.source_product_code ?? 'none'}"
              description="{p.variants.length} variants"
            />
          {/each}
          <div class="mt-2">
            <a
              href="/products/{query}"
              class="search-group-see-all ml-4 rounded border border-neutral-200 px-2
               py-1 text-small font-bold hover:bg-neutral-50">See all</a
            >
          </div>
        {:else}
          <div class="ml-4 text-small">No Results</div>
        {/if}
      </div>
    {/if}
  </div>
</div>
