<script lang="ts">
  import { goto } from '$app/navigation';
  import { cn } from '@/utils/classname';
  import { page } from '$app/stores';

  export let tabs: { name: string; key: string }[];
  export let baseUrl: string;

  $: pathSegments = $page.url.pathname.split('/');
</script>

<div class="sticky top-0 z-10 flex items-center border-b border-neutral-200 bg-white px-8 py-4">
  {#each tabs as { name, key }}
    <a
      href={`${baseUrl}/${key}`}
      class={cn(
        'bg-ne mr-1 flex h-9 w-[128px] items-center justify-center rounded text-regular font-bold leading-4 text-neutral-700 hover:bg-neutral-100',
        key === pathSegments[pathSegments.length - 1] && 'bg-neutral-100 text-brand-action'
      )}
    >
      {name}
    </a>
  {/each}
</div>
