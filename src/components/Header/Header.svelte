<script lang="ts">
  import User from './User.svelte';
  import GlobalSearch from './GlobalSearch.svelte';
  import AlertButton from '@/components/Alerts/AlertButton.svelte';
  import Icon from '@/components/Icons/Icon.svelte';
  import IconHelp from '@/components/Icons/IconHelp.svelte';
  import PromptModal from '../Modal/PromptModal.svelte';
  import HubspotForm from '../ExternalComponents/HubspotForm.svelte';

  import { dev } from '$app/environment';

  let hubspotModalOpen = false;
</script>

<header class="flex w-full bg-brand-brand px-4 py-2.5 text-gray-50">
  <div class="w-72 flex-none">
    <a href="/" class="flex h-full items-center">
      <img src="/s2s-logo.svg" alt="Logo" />
    </a>
  </div>
  <div class="flex grow justify-center">
    <GlobalSearch />
  </div>
  <div class="flex w-72 flex-none items-center justify-end">
    {#if !dev}
      <button on:click={() => (hubspotModalOpen = true)} class="relative p-2">
        <Icon IconComponent={IconHelp} size="i-6" />
      </button>
    {/if}
    <AlertButton />
    <User />
  </div>
</header>

{#if hubspotModalOpen && !dev}
  <PromptModal title="Log a ticket" bind:open={hubspotModalOpen} noAction>
    <HubspotForm bind:open={hubspotModalOpen} />
  </PromptModal>
{/if}
