<script context="module">
  import Tabs from '@/components/Header/Tabs.svelte';

  export const meta = {
    title: 'components/Header/Tabs',
    component: Tabs
  };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';

  const tabs = [
    { name: 'Details', key: 'details' },
    { name: 'Orders', key: 'orders' },
    { name: 'Attributes', key: 'attributes' },
    { name: 'Segments', key: 'segments' },
    { name: 'Data Sync', key: 'data-sync' },
    { name: 'Dynamic Pricing', key: 'dynamic-pricing' }
  ];
</script>

<Story name="Customer" id="tabs_customer">
  <div class="mr-6 h-full min-w-[260px] overflow-y-auto rounded bg-white shadow">
    <Tabs {tabs} baseUrl={''} />
  </div>
</Story>
