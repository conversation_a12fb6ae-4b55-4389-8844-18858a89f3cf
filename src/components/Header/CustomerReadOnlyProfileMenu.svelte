<script lang="ts">
  import IconLogout from '@/components/Icons/IconLogout.svelte';
  import { fly } from 'svelte/transition';
  import handleClickOutside from '@/utils/actions/handleClickOutside';
  import IconCustomer from '@/components/Icons/IconCustomer.svelte';
  import Icon from '@/components/Icons/Icon.svelte';
  import { handleLogout } from '@/utils/auth/logout';
  import { envConfig } from '@/constants';
  import IconBack from '@/components/Icons/IconBack.svelte';
  import { s2sStore } from '@/lib/s2sStore';
  import { get } from 'svelte/store';

  let showProfileMenu = false;
  let container: HTMLElement;

  const store = get(s2sStore);

  // Toggle menu
  function toggleMenu() {
    showProfileMenu = !showProfileMenu;
  }

  $: user = store.session.user;
</script>

<button
  class="relative ml-4 flex w-[180px] cursor-pointer rounded border border-brand-search px-2.5 py-1.5 text-left"
  on:click={() => toggleMenu()}
  bind:this={container}
  use:handleClickOutside={() => (showProfileMenu = false)}
>
  <div class="flex h-7 w-7 items-center justify-center rounded-full bg-brand-search">
    <IconCustomer />
  </div>
  <div class="ml-2.5 flex flex-col gap-1">
    <p class="text-small font-bold leading-none text-white">{user.client_name}</p>

    <p class="text-small leading-none text-neutral-300">
      {user.name ?? user?.username}
    </p>
  </div>

  {#if showProfileMenu}
    <div
      class="absolute left-0 top-full z-30 w-full rounded bg-white py-2 shadow"
      transition:fly={{ y: -2, duration: 100 }}
    >
      <a
        href={`${envConfig.consoleV1URL}/?action=login&token=${user.token}`}
        target="_blank"
        class="flex items-center px-4 py-2 text-regular text-neutral-700 hover:bg-neutral-50"
      >
        <Icon IconComponent={IconBack} size="i-5" />
        <span class="ml-4">Old Console</span>
      </a>
      <button
        on:click={() => {
          handleLogout({ showToast: true });
        }}
        class="flex w-full items-center px-4 py-2 text-regular text-neutral-700 hover:bg-neutral-50"
      >
        <Icon IconComponent={IconLogout} size="i-5" />

        <span class="ml-4">Logout</span>
      </button>
    </div>
  {/if}
</button>
