<script lang="ts">
  import { postSearchOrdersES } from '@/lib/s2s/orders';
  import SearchResult from '@/components/Header/SearchResult.svelte';
  import type { DSL } from '@/lib/es/types';
  import createGlobalSearchRequest from '@/components/Header/utils/createGlobalSearchRequest';
  import debounce from 'just-debounce-it';
  import type { SystemOrdersResponse } from '@/lib/s2s/types';
  import { createQuery } from '@tanstack/svelte-query';
  import IconOrder from '../Icons/IconOrder.svelte';
  import SkeletonText from '../Loader/SkeletonText.svelte';

  export let query = '';

  let debouncedQuery = '';

  const setDebouncedQuery = debounce((q: string) => {
    debouncedQuery = q;
  }, 100);

  $: setDebouncedQuery(query);

  const queryPrefixKey = 'global-search-orders';
  let requestBody: DSL = createGlobalSearchRequest(query);

  $: data = createQuery({
    queryKey: [queryPrefixKey, requestBody],
    queryFn: () => postSearchOrdersES(requestBody)
  });

  let results: SystemOrdersResponse;
  $: results = $data.data;
  $: isFetching = $data.isFetching;
  $: isLoading = $data.isLoading;
  $: if (debouncedQuery.length > 0) {
    requestBody = createGlobalSearchRequest(debouncedQuery);
  }
</script>

<div class="search-group border-t py-4">
  <div
    class="search-group-title mx-4 text-[10px] font-bold uppercase leading-4 tracking-widest text-neutral-500"
  >
    Orders
  </div>

  <div>
    {#if isLoading}
      <div class="ml-4 mt-1 flex flex-col gap-1 opacity-50">
        <SkeletonText class="h-5" shimmer />
        <SkeletonText class="h-5" shimmer />
        <SkeletonText class="h-5" shimmer />
      </div>
    {:else}
      <div>
        {#if results.system_orders && results.system_orders.length > 0}
          {#each results.system_orders as o (o.id)}
            <SearchResult
              icon={IconOrder}
              href="/order/{o.id}"
              title="{o.customer.first_name ? `${o.customer.first_name} ` : ''}{o.customer.last_name
                ? o.customer.last_name
                : ''}"
              code="s2s: {o.id ?? 'none'} | channel: {o.channel_order_code ?? 'none'}"
              description="Shipping Total: {o.shipping_total ?? 'none'} Total: {o.total ?? 'none'}"
            />
          {/each}
          <div class="mt-2">
            <a
              href="/orders/{query}"
              class="search-group-see-all ml-4 rounded border border-neutral-200 px-2 py-1 text-small font-bold hover:bg-neutral-50"
              >See all</a
            >
          </div>
        {:else}
          <div class="ml-4 text-small">No Results</div>
        {/if}
      </div>
    {/if}
  </div>
</div>
