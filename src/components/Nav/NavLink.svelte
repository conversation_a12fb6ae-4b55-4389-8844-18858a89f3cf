<script lang="ts">
  import { page } from '$app/stores';
  import pluralizeWord from '@/utils/pluralize';
  import Icon from '@/components/Icons/Icon.svelte';
  import type { Link } from '@/models/nav/link';

  export let link: Link;

  let setFixedWidth: boolean = false;

  $: hrefSegments = link.href.split('/');
  $: pathSegments = $page.url.pathname.split('/');

  const measureTextWidth = (value: string, font: string = 'Public Sans', fontSize = '0.813rem') => {
    const text = document.createElement('span');
    document.body.appendChild(text);

    text.style.font = font;
    text.style.fontSize = fontSize;
    text.style.height = 'auto';
    text.style.width = 'auto';
    text.style.position = 'absolute';
    text.style.left = '-9999px';
    text.style.whiteSpace = 'no-wrap';
    text.innerHTML = value;
    const width = Math.ceil(text.clientWidth);

    document.body.removeChild(text);

    return width;
  };

  const calculateMaxWidth = () => {
    let longestText =
      link?.subLinks?.reduce(
        (longest, { title }) => (title.length > longest.length ? title : longest),
        ''
      ) ?? '';

    if (measureTextWidth(longestText) > (link.maxWidthPx ?? 0)) {
      setFixedWidth = true;
    }
  };

  $: !!link.maxWidthPx && calculateMaxWidth();
  $: highlightNavItem =
    pathSegments[1] === hrefSegments[1] ||
    pluralizeWord(pathSegments[1]) === hrefSegments[hrefSegments.length - 1] ||
    (hrefSegments[1] === 'connectors' && pathSegments[1] === 'config') ||
    (link?.relatedLinks && link.relatedLinks.includes(pathSegments[1]));
</script>

<a
  href={link.href}
  class="nav-link group relative mb-1 block rounded p-2.5 hover:bg-neutral-100 hover:text-brand-action"
  class:bg-neutral-100={highlightNavItem}
  class:text-brand-action={highlightNavItem}
>
  <Icon IconComponent={link.icon} size="smaller" />

  <!-- 
    Static sublinks will grow with content.
    Dynamic sublinks will grow up to a max width, then wrap over multiple lines.
  -->
  <div class="absolute left-full top-0 z-20 hidden group-hover:block">
    <div
      class="nav-submenu ml-4 rounded bg-neutral-800 pb-2 pl-4 pr-4 pt-2 align-middle leading-4 text-white"
    >
      <div class="text-nowrap text-smaller font-bold uppercase">{link.title}</div>
    </div>
  </div>
</a>

<style>
  .nav-submenu:before {
    content: '';
    border-top: 12px solid theme('colors.neutral.800');
    border-left: 12px solid theme('colors.neutral.800');
    border-bottom-left-radius: 3px;
    transform: rotate(45deg);
    position: absolute;
    top: 12px;
    left: 10px;
  }
</style>
