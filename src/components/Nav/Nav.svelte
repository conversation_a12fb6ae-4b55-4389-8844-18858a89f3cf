<script lang="ts">
  import NavLink from './NavLink.svelte';
  import IconReport from '../Icons/IconReport.svelte';
  import IconProduct from '../Icons/IconProduct.svelte';
  import IconCustomers from '../Icons/IconCustomers.svelte';
  import IconOrder from '../Icons/IconOrder.svelte';
  import IconConnector from '../Icons/IconConnector.svelte';
  import IconSetting from '../Icons/IconSetting.svelte';
  import IconServer from '@/components/Icons/IconServer.svelte';
  import IconList from '@/components/Icons/IconList.svelte';
  import type { Link } from '@/models/nav/link';
  import IconAlert from '@/components/Icons/IconAlert.svelte';
  import IconFulfill from '@/components/Icons/IconFulfill.svelte';

  // TODO: Look into the comment below as I have not seen this anywhere...

  // This could be dynamically changed depending on user role.
  // It is possible this could be set in s2sStore.
  const links: Link[] = [
    {
      icon: IconReport,
      href: '/dashboard',
      title: 'Dashboard'
    },
    {
      icon: IconProduct,
      href: '/products',
      title: 'Products'
    },
    {
      icon: IconCustomers,
      href: '/customers',
      title: 'Customers'
    },
    {
      icon: IconOrder,
      href: '/orders',
      title: 'Orders'
    },
    {
      icon: IconFulfill,
      href: '/inbounds',
      title: 'Warehouse',
      relatedLinks: ['inbound', 'warehouse-stock-adjustments', 'warehouse-stock-adjustment']
    },
    {
      icon: IconConnector,
      href: '/connectors',
      title: 'Connectors'
    },
    {
      icon: IconServer,
      href: '/logs',
      title: 'Data Logs'
    },
    {
      icon: IconAlert,
      href: '/search/errors',
      title: 'Sync Errors'
    },
    {
      icon: IconList,
      href: '/queue/error',
      title: 'Queue'
    }
  ];
</script>

<nav class="flex h-full flex-col justify-between bg-white p-3 shadow">
  <div class="flex-none">
    {#each links as link}
      <NavLink {link} />
    {/each}
  </div>
  <div class="grow" />
  <div class="flex-none">
    <NavLink link={{ icon: IconSetting, href: '/settings/users', title: 'Settings' }} />
  </div>
</nav>
