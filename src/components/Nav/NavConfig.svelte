<script lang="ts">
  import Icon from '@/components/Icons/Icon.svelte';
  import type { ConfigNavLink } from '@/models/nav/configNav';
  import type { ConfigKey } from '@/models/config/config';

  export let links: ConfigNavLink[];
  export let configKey: ConfigKey;
</script>

<div class="flex-none overflow-y-auto rounded">
  <ul>
    {#each links as link}
      <li>
        <a
          href="/config/{configKey.kind}/{configKey.type}/{configKey.id}/{link.config}"
          class="flex w-full cursor-pointer items-center justify-between rounded px-4 py-2 text-regular {link.config ===
          configKey.config
            ? 'bg-neutral-100 text-brand-action'
            : 'hover:bg-neutral-100 hover:text-brand-action'}"
        >
          <div class="flex">
            <Icon size="smaller" IconComponent={link.Icon} />
            <span class="pl-2">{link.title}</span>
          </div>
        </a>
      </li>
    {/each}
  </ul>
</div>
