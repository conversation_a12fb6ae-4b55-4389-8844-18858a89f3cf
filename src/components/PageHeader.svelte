<script lang="ts">
  import IconBack from '@/components/Icons/IconBack.svelte';
  import TextInput from './Controls/TextInput.svelte';
  import Button from '@/components/Controls/Button.svelte';
  import { goto } from '$app/navigation';
  import { isString } from '@/utils/typeguards';
  import { cn } from '@/utils/classname';

  export let additionalClasses: string = '';
  export let title: string;
  /**
   * Will default to window.history.back() if
   * backUrl is not provided
   */
  export let hasBackButton = false;
  export let backUrl: string | undefined = undefined;
  export let sticky = false;
</script>

<div
  class={cn('mb-6 flex w-full items-center justify-between bg-neutral-50 xl:max-w-full', {
    'sticky top-0 z-10 mb-0 h-20 bg-neutral-50': sticky // ToDo sticky header styling is WIP
  })}
>
  <div class="flex w-4/5 items-center">
    {#if hasBackButton}
      <div class="mr-6">
        <Button
          size="large"
          variant="gray"
          on:click={() => (isString(backUrl) ? goto(backUrl) : () => window.history.back())}
          icon={IconBack}
        />
      </div>
    {/if}
    {#if false}
      <!-- DEPRECATED 
           This does fall within our designs
    -->
      <TextInput type="text" bind:value={title} />
    {:else}
      <h2 class={additionalClasses}>
        {title}
      </h2>
    {/if}
  </div>
  <!-- Additional content for the right side of header -->
  <div class="flex min-w-fit items-center gap-3">
    <slot />
  </div>
</div>
