<script lang="ts">
  import Icon from '../Icons/Icon.svelte';
  import RadioGroup from '../Controls/Switches/RadioGroup.svelte';
  import IconClose from '../Icons/IconClose.svelte';
  import TextInput from '../Controls/TextInput.svelte';
  import IconArrowRight from '../Icons/IconArrowRight.svelte';
  import FileSelector from './FileSelector.svelte';
  import { MB } from '@/constants';
  import Button from '@/components/Controls/Button.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';

  export let filesSelected: (selectedFiles: File[]) => void = null;
  export let selectedFiles: File[] | undefined = [];
  export let selectedFile: File | undefined = null;
  export let selectedField = 'variants.sku';
  export let findReplaceRules = [];
  export let onRulesChanged: (findReplaceRules: { find: string; replace: string }[]) => void;
  export let maxSize = MB * 5;

  let fileFormatError = false;

  const PRODUCT_IMAGE_GUIDE_URL = 'https://www.stock2shop.com/help/guides/product-images/';

  let findValue = '';
  let replaceValue = '';
  let uploadErrors = [];

  const fieldOptions = [
    {
      label: 'SKU',
      value: 'variant.sku'
    },
    {
      label: 'Source Product Code',
      value: 'product.source_product_code'
    },
    {
      label: 'Source Variant Code',
      value: 'variant.source_variant_code'
    }
  ];

  function addFindReplaceRule() {
    const newRule = {
      find: findValue,
      replace: replaceValue
    };
    findReplaceRules = [newRule, ...findReplaceRules];

    findValue = '';
    replaceValue = '';
    onRulesChanged(findReplaceRules);
  }

  function removeRule(rule: { find: string; replace: string }) {
    findReplaceRules = findReplaceRules.filter((r) => {
      return !(r.find === rule.find && r.replace === rule.replace);
    });

    onRulesChanged(findReplaceRules);
  }

  $: {
    selectedField;
    // Trigger this to run whenever selected field changes
    onRulesChanged(findReplaceRules);
  }
</script>

<div class="w-[360px] min-w-[360px] border-r border-neutral-200 bg-white p-8 text-neutral-500">
  <div class="mb-6">
    <InlineNotification type="info">
      <div class="flex items-center">
        <div>
          Find out more about Image Importing in our <a
            class="font-bold"
            href={PRODUCT_IMAGE_GUIDE_URL}
            target="_blank">Help Center.</a
          >
        </div>

        <a href={PRODUCT_IMAGE_GUIDE_URL} target="_blank">
          <Icon IconComponent={IconArrowRight} size="i-5" /></a
        >
      </div>
    </InlineNotification>
  </div>

  <!--info-->

  <!--step 1-->
  <span>1. Select the images you wish to upload</span>
  <div class="mb-8 mt-3">
    <FileSelector
      enableValidation={false}
      {filesSelected}
      bind:selectedFile
      bind:selectedFiles
      bind:fileFormatError
      {maxSize}
      allowMultiple={true}
      errors={uploadErrors}
    />
    {#each uploadErrors as error}
      <div class="text-red-500">{error}</div>
    {/each}
  </div>
  <!--step 1-->
  <!--step 2-->
  <span>2. Choose the field to match your filenames</span>
  <div class="mb-8 mt-3">
    <RadioGroup options={fieldOptions} bind:value={selectedField} />
  </div>
  <!--step 2-->
  <!--step 3-->
  <span>3. Add Find & Replace rules to swap out certain characters in your filenames</span>
  <div class="mb-8 mt-3">
    {#each findReplaceRules as rule}
      <div
        class="relative mb-3 flex items-start justify-between rounded bg-neutral-100 p-3 pr-5 text-neutral-500"
      >
        <div class="absolute right-3 top-3 cursor-pointer" on:click={() => removeRule(rule)}>
          <Icon IconComponent={IconClose} size="smallest" />
        </div>

        “{rule.find}” will be replaced with “{rule.replace}”
      </div>
    {/each}

    <div class="flex flex-col gap-2">
      <TextInput bind:value={findValue} className="mb-2" placeholder="Find" />
      <TextInput bind:value={replaceValue} className="mb-2" placeholder="Replace" />
    </div>
    <div class="mt-3">
      <Button size="medium" on:click={addFindReplaceRule} variant="outline">Add Rule</Button>
    </div>
  </div>
  <!--step 3-->
</div>
