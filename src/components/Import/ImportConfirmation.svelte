<script lang="ts">
  import Icon from '../Icons/Icon.svelte';
  import IconInfo from '../Icons/IconInfo.svelte';
  import IconRefresh from '../Icons/IconRefresh.svelte';
  import IconDelete from '../Icons/IconDelete.svelte';
  import type DryRunResults from '@/models/import/dry-run-results';
  import skipTake from '@/utils/skipTake';
  import Btn from '../form/Btn.svelte';
  import ImportsRepo from '@/repos/import-repo';
  import Loader from '../Loader/Loader.svelte';
  import { toast } from 'svelte-sonner';
  import { getContext } from 'svelte';
  import pluralizeWord from '@/utils/pluralize';
  import { conjunctionFormatter } from '@/utils/conjunctionFormatter';
  import IconProduct from '../Icons/IconProduct.svelte';
  import InlineNotification from '../Notification/InlineNotification.svelte';
  import { isDryRunCustomerResult, isDryRunProductResult } from '@/models/import/dry-run-results';
  import type { FieldEntity } from '@/models/fields/fields';
  import capitalize from 'lodash.capitalize';

  export let storageCode: string;
  export let fieldMap: { [key: string]: string };
  export let dryRunResults: DryRunResults;
  export let onConfirmBack: () => void;
  export let confirmationOpen: boolean;
  export let entity: FieldEntity;

  const importsRepo: ImportsRepo = new ImportsRepo();

  let takeAmount = 5;
  let loading = false;

  const confirmImport = async () => {
    await importsRepo.run(storageCode);
  };

  const onImportComplete = getContext<() => void>('onImportComplete');

  $: entityDisplayName = entity === 'product' ? 'products/variants' : pluralizeWord(entity);

  let addition: number;
  let deletion: number;
  let update: number;

  $: {
    if (
      isDryRunProductResult(dryRunResults.additions) &&
      isDryRunProductResult(dryRunResults.deletions) &&
      isDryRunProductResult(dryRunResults.updates)
    ) {
      addition = dryRunResults?.additions.products + dryRunResults?.additions.variants;
      deletion = dryRunResults?.deletions.products + dryRunResults?.deletions.variants;
      update = dryRunResults?.updates.products + dryRunResults?.updates.variants;
    }

    if (
      isDryRunCustomerResult(dryRunResults.additions) &&
      isDryRunCustomerResult(dryRunResults.deletions) &&
      isDryRunCustomerResult(dryRunResults.updates)
    ) {
      addition = dryRunResults?.additions.customers;
      deletion = dryRunResults?.deletions.customers;
      update = dryRunResults?.updates.customers;
    }
  }

  $: errorList = skipTake(Object.keys(dryRunResults?.errors), 0, takeAmount);
  $: errors = Object.keys(dryRunResults.errors)?.length;
  $: nothingToProcess = !Object.values({ addition, update, deletion, errors }).reduce(
    (acc, c) => acc + c,
    0
  );
  $: onlyErrors = !!errors && !(addition + deletion + update);

  const handleConfirmImport = async () => {
    confirmationOpen = false;
    if (onImportComplete) {
      onImportComplete();
    }

    const processSummaryItems = Object.entries({ addition, deletion, update }).reduce(
      (acc: string[], [k, v], i) => (v ? [...acc, `${v} ${v > 1 ? pluralizeWord(k) : k}`] : acc),
      []
    );
    const processSummary = processSummaryItems.length
      ? conjunctionFormatter.format(processSummaryItems)
      : 'nothing';

    toast.promise(confirmImport, {
      error: (e) => `Failed to import, please try again: ${e.message}`,
      loading: `Processing ${processSummary}`,
      success: `Successfully processed ${processSummary}`,
      duration: 10 * 1000
    });
  };
</script>

<div class="h-full w-full p-8">
  <h1 class="my-2 text-[15px] font-bold">Changes to process:</h1>
  <!-- ToDo: waiting processing count from dryRunResponse -->
  <div class="pb-2">
    <InlineNotification type={nothingToProcess ? 'success' : 'info'}
      >{nothingToProcess
        ? 'Everything is up to date'
        : 'There are items to process'}</InlineNotification
    >
  </div>

  {#if loading}
    <Loader />
  {:else}
    <div class="flex h-[38px] items-center justify-between border-b border-neutral-200">
      <div class="flex">
        <span class="text-brand-confirmation">
          <Icon IconComponent={IconProduct} size="smaller" />
        </span>

        <span class="ml-4"><b>{addition}</b> New {entityDisplayName}</span>
      </div>
    </div>

    <div class="flex h-[38px] items-center justify-between border-b border-neutral-200">
      <div class="flex">
        <span class="text-brand-notification">
          <Icon IconComponent={IconRefresh} size="smaller" />
        </span>

        <span class="ml-4"><b>{update}</b> Updates to existing {entityDisplayName}</span>
      </div>
    </div>

    <div class="flex h-[38px] items-center justify-between border-b border-neutral-200">
      <div class="flex">
        <span class="text-brand-warning">
          <Icon IconComponent={IconDelete} size="smaller" />
        </span>

        <span class="ml-4"><b>{deletion}</b> Deletions of {entityDisplayName}</span>
      </div>
    </div>

    <div class="flex h-[38px] items-center justify-between border-b border-neutral-200">
      <div class="flex">
        <span class="text-brand-action">
          <Icon IconComponent={IconInfo} colour="#ef4459" size="smaller" />
        </span>

        <span class="ml-4"><b>{errors}</b> {capitalize(entityDisplayName)} with errors</span>
      </div>
    </div>

    <div class="mt-6 h-[70%] overflow-y-auto">
      {#if errors}
        {#each errorList as error}
          <div class="mb-2 flex items-start rounded bg-brand-action/10 px-3 py-2 text-brand-action">
            <span class="mr-2 mt-1 text-brand-action">
              <Icon IconComponent={IconInfo} colour="#ef4459" size="smaller" />
            </span>
            <div>
              <p class="font-bold">{dryRunResults.errors[error]}</p>
              <p>Line {error}</p>
            </div>
          </div>
        {/each}
      {/if}

      {#if errorList.length == 5 && Object.keys(dryRunResults.errors)?.length > 5}
        <div
          class="flex h-8 w-fit cursor-pointer items-center rounded bg-neutral-100 p-3 font-bold"
          on:click={() => (takeAmount = 50)}
        >
          Load more
        </div>
      {/if}
      <div class="h-[170px]" />
    </div>
  {/if}

  <div
    class="absolute bottom-0 left-0 flex w-full gap-x-4 border-t border-neutral-200 bg-white px-6 py-4"
  >
    <Btn
      size="large"
      bind:disabled={loading}
      variant="gray"
      onClick={onConfirmBack}
      class={nothingToProcess || onlyErrors ? 'w-full' : 'w-1/2'}>Back</Btn
    >
    {#if !nothingToProcess && !onlyErrors}
      <Btn
        size="large"
        bind:disabled={loading}
        variant="solid"
        onClick={handleConfirmImport}
        class="w-1/2">Confirm Import</Btn
      >
    {/if}
  </div>
</div>
