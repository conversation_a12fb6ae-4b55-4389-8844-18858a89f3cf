<script context="module">
  export const meta = {
    title: 'components/Imports/ImportModal',
    component: ImportModal
  };
</script>

<script lang="ts">
  import { Story, Template } from '@storybook/addon-svelte-csf';
  import ImportModal from '@/components/Import/ImportModal.svelte';
  import { within } from '@storybook/test';

  const content = new Uint8Array(2131549);
  const invalidFile = new File([content], 'InvalidFile.png', {
    type: 'image/jpeg',
    lastModified: Date.now()
  });

  async function mountValidFile({ canvasElement }) {
    const canvas = within(canvasElement);
    const input = await canvas.findByTestId('test-file-input');
    mockSelectedFile(input, 'validfile.csv', 'text/csv', 2131549);
  }

  async function mountValidFileKB({ canvasElement }) {
    const canvas = within(canvasElement);
    const input = await canvas.findByTestId('test-file-input');
    mockSelectedFile(input, 'validfile.csv', 'text/csv', 3456);
  }

  async function mountInvalidFile({ canvasElement }) {
    const canvas = within(canvasElement);
    const input = await canvas.findByTestId('test-file-input');
    mockSelectedFile(input, 'invalidfile.png', 'image/jpeg', 2131549);
  }

  function mockSelectedFile(
    inputElement: HTMLElement,
    fileName: string,
    fileType: string,
    fileSize: number
  ) {
    const content = new Uint8Array(fileSize); // Create a Uint8Array with the desired file size
    const file = new File([content], fileName, { type: fileType, lastModified: Date.now() });
    Object.defineProperty(inputElement, 'files', { value: [file], writable: true });
    inputElement.dispatchEvent(new Event('change', { bubbles: true }));
  }
</script>

<Template let:args>
  <div class="table-body">
    <ImportModal open={true} href="#" {...args} />
  </div>
</Template>

<Story name="Default - Product" args={{ entity: 'product' }} />
<Story name="Default - Customer" args={{ entity: 'customer' }} />

<Story name="Selected File" play={mountValidFile} args={{ entity: 'product' }} />

<Story name="Selected File KB" play={mountValidFileKB} args={{ entity: 'product' }} />

<Story
  name="Invalid File"
  play={mountInvalidFile}
  args={{
    selectedFile: invalidFile,
    entity: 'product'
  }}
/>
