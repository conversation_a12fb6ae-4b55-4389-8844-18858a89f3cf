<script lang="ts">
  import type { ImportData } from '@/models/import/import-response';
  import CustomModal from '../Modal/CustomModal.svelte';
  import Btn from '../form/Btn.svelte';
  import IconBack from '@/components/Icons/IconBack.svelte';
  import IconInfo from '@/components/Icons/IconInfo.svelte';
  import Icon from '../Icons/Icon.svelte';
  import skipTake from '@/utils/skipTake';
  import ImportsRepo from '@/repos/import-repo';
  import ImportConfirmation from './ImportConfirmation.svelte';
  import Loader from '../Loader/Loader.svelte';
  import { fly } from 'svelte/transition';
  import clickOutside from '@/utils/actions/handleClickOutside';

  import { createFieldsQuery } from '@/queries/fields.query';

  import FieldSelector from '../Controls/Select/FieldSelector/FieldSelector.svelte';
  import { cn } from '@/utils/classname';
  import { createMutation } from '@tanstack/svelte-query';
  import InlineNotification from '../Notification/InlineNotification.svelte';
  import type { FieldEntity } from '@/models/fields/fields';
  import capitalize from 'lodash.capitalize';
  import pluralizeWord from '@/utils/pluralize';
  import Text from '../Controls/Text/Text.svelte';

  export let open = false;
  export let importResponseData: ImportData;
  export let entity: FieldEntity;
  export let previousModalOpen: boolean | undefined = undefined;

  const importsRepo: ImportsRepo = new ImportsRepo();

  const displayAmount = 4;

  type ProductImportMappingColumn = {
    csvProp: string;
    actualProp: string;
    active: boolean;
    index: number;
  };

  let pagingIndex = 0;
  let currentRange = '';
  let columns: ProductImportMappingColumn[] = [];
  let confirmationOpen = false;
  let fieldMap: { [key: string]: string };

  const availableFieldsData = createFieldsQuery({ params: { entity } });

  $: entityGroupPrefixes =
    $availableFieldsData.data?.dynamic_field_groups.map((e) => e.group) || [];

  $: availableFields = $availableFieldsData.data?.fields || [];

  $: {
    if (availableFields.length && !columns.length) {
      columns = Object.keys(importResponseData.column_map).map((c, index) => ({
        csvProp: c,
        actualProp: guessProp(c),
        active: true,
        index
      }));
    }
  }

  $: rows = importResponseData.first_few_rows?.filter((row) => row.length > 0);
  $: numberOfColumns = Object.keys(importResponseData.column_map).length;
  $: disableNext = displayAmount * (pagingIndex + 1) >= numberOfColumns;
  $: disablePrev = pagingIndex == 0;
  $: isLoading = $availableFieldsData.isLoading;

  $: {
    let startRange = Math.max(pagingIndex * displayAmount, 0) + 1;
    let endRange = Math.min(displayAmount * (pagingIndex + 1), numberOfColumns);
    currentRange = startRange === endRange ? startRange.toString() : `${startRange} - ${endRange}`;
  }

  function onConfirmBack() {
    confirmationOpen = false;
  }

  function nextPage() {
    if (disableNext) return;
    pagingIndex++;
  }

  function previousPage() {
    if (disablePrev) return;
    pagingIndex--;
  }

  function onBack() {
    open = false;
    confirmationOpen = false;
    if (typeof previousModalOpen === 'boolean') {
      previousModalOpen = true;
    }
  }

  const dryRunMutation = createMutation({
    mutationFn: () => {
      const fieldMap = columns.reduce((result, item) => {
        if (item.actualProp && item.csvProp && item.active) {
          result[item.actualProp] = item.csvProp;
        }
        return result;
      }, {});

      return importsRepo.dryRun(importResponseData.storage_code, fieldMap);
    }
  });

  function onNext() {
    confirmationOpen = true;
    $dryRunMutation.mutate();
  }

  function guessProp(c: string) {
    if (entityGroupPrefixes.includes(c.split('_')[0]) && c.split('_').length > 1) {
      return c;
    }
    const result = availableFields.find((a) => a.name.toLowerCase() === c.toLowerCase())?.name;
    if (result) return result;
    return '';
  }
</script>

<CustomModal bind:open onClose={onBack} class="overflow-y-hidden" enableClickOutside={false}>
  <div class="flex h-auto w-full justify-between" slot="title">
    <div class="flex items-center gap-4">
      <Btn Prefix={IconBack} size="small" iconSize="small" variant="gray" onClick={onBack} />
      <div class="flex items-center text-2xl font-bold text-neutral-800">
        Import {capitalize(pluralizeWord(entity))}
      </div>
    </div>
    <div class="flex items-center">
      <Btn onClick={onNext} size="medium" variant="solid">Next</Btn>
    </div>
  </div>
  <div slot="content" class="h-full overflow-hidden p-6 pt-0 text-regular text-neutral-700">
    {#if confirmationOpen}
      <div
        class="absolute right-0 top-0 z-20 h-full w-[427px] rounded bg-white shadow"
        transition:fly={{ x: 100, duration: 300 }}
        use:clickOutside={onConfirmBack}
      >
        {#if $dryRunMutation.isPending}
          <Loader />
        {:else if $dryRunMutation.isError}
          <div class="h-full w-full p-8">
            <h1 class="my-2 text-[15px] font-bold">Error processing:</h1>
            <InlineNotification type="error"
              >{$dryRunMutation.error?.message ?? 'Error processing'}</InlineNotification
            >

            <div
              class="absolute bottom-0 left-0 flex w-full gap-x-4 border-t border-neutral-200 bg-white px-6 py-4"
            >
              <Btn size="large" variant="gray" onClick={onConfirmBack} class={'w-full'}>Back</Btn>
            </div>
          </div>
        {:else if $dryRunMutation.data?.dry_run_results}
          <ImportConfirmation
            storageCode={importResponseData.storage_code}
            {fieldMap}
            {onConfirmBack}
            dryRunResults={$dryRunMutation.data.dry_run_results}
            bind:confirmationOpen
            {entity}
          />
        {/if}
      </div>
    {/if}

    <div
      class="my-3 flex items-start justify-between rounded bg-brand-notification/10 p-3 text-brand-notification"
    >
      <div class="flex items-center">
        <Icon IconComponent={IconInfo} colour="#3b82f6" size="small" />
        <p class="ml-2">Match the columns below. You can also select which columns to import.</p>
      </div>
      <div class="flex items-center">
        <b>{currentRange} &nbsp;</b> of {numberOfColumns} columns
      </div>
    </div>

    <div class="relative flex h-full w-full rounded">
      {#if !disablePrev}
        <div
          on:click={previousPage}
          class="flex h-5/6 w-8 cursor-pointer items-center justify-center rounded-l bg-neutral-500 text-white"
        >
          <Icon IconComponent={IconBack} size="smaller" />
        </div>
      {/if}
      {#if isLoading}
        <Loader />
      {:else}
        <div class="h-full w-full overflow-y-auto pb-40">
          <div
            style={`width: ${
              (skipTake(columns, pagingIndex * displayAmount, displayAmount).length / 4) * 100
            }%;
              grid-template-columns: repeat(${
                skipTake(columns, pagingIndex * displayAmount, displayAmount).length
              }, minmax(0, 1fr))`}
            class="grid w-full border border-neutral-200"
          >
            <!-- Table header -->
            {#each skipTake(columns, pagingIndex * displayAmount, displayAmount) as column (column.index)}
              <div
                class="sticky flex h-10 justify-between border-b border-r border-neutral-200 p-2 font-bold"
              >
                <Text tooltip={column.csvProp} class="truncate text-neutral-500">
                  {column.csvProp}
                </Text>
                <div>
                  <input
                    type="checkbox"
                    class="rounded border border-neutral-300 bg-white
            text-brand-notification checked:bg-brand-notification focus:ring-0 focus:ring-offset-0"
                    bind:checked={columns[column.index].active}
                  />
                </div>
              </div>
            {/each}

            {#each skipTake(columns, pagingIndex * displayAmount, displayAmount) as column (column.index)}
              <div
                class={cn('items-center border-b border-r border-neutral-200 p-2', {
                  locked: !columns[column.index].active
                })}
              >
                <FieldSelector
                  {entity}
                  bind:propValue={columns[column.index].actualProp}
                  colourIfSelected
                />
              </div>
            {/each}

            {#each rows as row, i}
              {#each skipTake(columns, pagingIndex * displayAmount, displayAmount) as column (column.index)}
                <div
                  class={cn(
                    'flex h-12 items-center truncate border-b border-r border-neutral-200 p-2',
                    { locked: !columns[column.index].active }
                  )}
                >
                  <span class="w-full truncate">
                    {row[importResponseData.column_map[column.csvProp]] ?? ''}</span
                  >
                </div>
              {/each}
            {/each}
          </div>
        </div>
      {/if}
      {#if !disableNext}
        <div
          on:click={nextPage}
          class="right-0 flex h-5/6 w-8 rotate-180 cursor-pointer items-center justify-center rounded-l bg-neutral-500 text-white"
        >
          <Icon IconComponent={IconBack} size="smaller" />
        </div>
      {/if}
    </div>
  </div>
</CustomModal>
