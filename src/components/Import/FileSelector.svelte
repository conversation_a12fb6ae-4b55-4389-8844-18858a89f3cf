<script lang="ts">
  import Icon from '../Icons/Icon.svelte';
  import Loader from '../Loader/Loader.svelte';
  import IconClose from '../Icons/IconClose.svelte';
  import IconCloud from '../Icons/IconCloud.svelte';
  import IconInfo from '../Icons/IconInfo.svelte';
  import IconUpload from '../Icons/IconUpload.svelte';
  import Skeleton from '../Loader/Skeleton.svelte';
  import { MB } from '@/constants';
  import Button from '../Controls/Button.svelte';

  export let fileAccept = '';
  export let fileFormatError = false;
  export let selectedFile: File | undefined = undefined;
  export let selectedFiles: File[] = [];
  export let allowMultiple = false;
  export let maxSize = MB * 5;
  export let errors: string[] = [];
  export let filesSelected: (selectedFiles: File[]) => void;
  export let enableValidation = true;
  export let type: 'full' | 'mini' = 'full';
  export let loading = false;

  let fileInput: HTMLInputElement | undefined;

  function reset() {
    selectedFile = undefined;
    selectedFiles = [];
    fileFormatError = false;
    loading = false;
  }

  function getFileSize(fileSize: number): string {
    if (fileSize) {
      const fileSizeInKB = fileSize / MB; // Convert bytes to kilobytes
      const fileSizeInMB = fileSizeInKB / MB; // Convert kilobytes to megabytes

      if (fileSizeInMB >= 1) {
        return `${fileSizeInMB.toFixed(2)} MB`;
      } else {
        return `${fileSizeInKB.toFixed(2)} KB`;
      }
    }

    return 'Error: File size is undefined or invalid';
  }

  function handleDragOver(event: DragEvent) {
    event.preventDefault();
  }

  function handleFileSelect(event: Event) {
    reset();
    const inputElement = event.target as HTMLInputElement;

    if (inputElement.files && inputElement.files.length > 0) {
      Array.from(inputElement.files).forEach((file) => {
        const valid = validateFile(file);
        if (valid) selectedFiles = [file, ...(selectedFiles ?? [])];
      });
      selectedFile = selectedFiles[0];
      filesSelected(selectedFiles);
    }
  }

  function handleDrop(event: DragEvent) {
    event.preventDefault();
    reset();
    const { files } = event.dataTransfer;

    if (files && files.length > 0) {
      Array.from(files).forEach((file) => {
        const valid = validateFile(file);
        if (valid) selectedFiles = [file, ...selectedFiles];
      });
    }
    selectedFile = selectedFiles[0];
    filesSelected(selectedFiles);
  }

  function getTotalFileSize(files: File[]) {
    if (!files.length) return 0;
    let totalSize = 0;
    files.forEach((file) => {
      totalSize += file.size;
    });

    return getFileSize(totalSize);
  }

  function validateFile(selectedFile: File) {
    if (!enableValidation) return true;

    if (selectedFiles.length === 0) {
      errors = [];
    }
    fileFormatError = false;

    if (
      !fileAccept
        .split(',')
        .find((t) => t.toLowerCase().trim() == selectedFile.type.toLowerCase().trim())
    ) {
      fileFormatError = true;
      errors = ['File format is not supported', ...errors];
    }

    if (selectedFile.size / MB > maxSize) {
      fileFormatError = true;
      errors = [`File size is too large. Max ${maxSize / MB}MB allowed`, ...errors];
    }
    return !fileFormatError;
  }
</script>

<!-- @component
File select component with two types:
 Full - Detailed UI which includes file summary
 Mini - Small dropzone-style ui

 This component handles the selection of files, 
 actual upload functionality is handled externally
 by either:
  1. uploading files on handleFileSelect
  2. binding selectedFile or selectedFiles (multiple)
  
  You can bind loading to display loading state 
  from within this component
 -->

<!-- Hidden input -->
<input
  data-testid="test-file-input"
  type="file"
  multiple={allowMultiple}
  id="fileInput"
  class="hidden"
  on:change={handleFileSelect}
  accept={fileAccept}
  bind:this={fileInput}
/>

{#if type === 'mini'}
  <button
    class="flex h-full w-full items-center justify-center overflow-hidden rounded border border-dashed border-neutral-400 bg-neutral-100 text-neutral-400"
    id="dropZone"
    on:dragover={handleDragOver}
    on:drop={handleDrop}
    on:click={() => fileInput?.click()}
    type="button"
  >
    {#if loading}
      <Skeleton class="flex h-full w-full items-center justify-center" shimmer>
        <div class="animate-bounce">
          <Icon size="extra-small" IconComponent={IconUpload} />
        </div>
      </Skeleton>
    {:else}
      <Icon size="extra-small" IconComponent={IconUpload} />
    {/if}
  </button>
{/if}

{#if type === 'full'}
  {#if selectedFile || selectedFiles?.length}
    <div
      class="flex h-20 w-full items-center justify-between border-2 border-dotted border-neutral-200 p-4"
    >
      {#if loading}
        <Loader />
      {:else}
        <div class="flex w-full justify-between">
          <div class="flex w-4/5 flex-col">
            <span class="truncate text-neutral-700"
              >{(selectedFiles?.length ? selectedFiles.length : 0) > 1
                ? 'Multiple files selected'
                : selectedFile?.name}</span
            >
            <span class="text-neutral-400">
              {(selectedFiles?.length ? selectedFiles.length : 0) > 1
                ? getTotalFileSize(selectedFiles)
                : getFileSize(selectedFile.size)}
            </span>
          </div>
          <button
            on:click={reset}
            class="flex h-4 w-4 cursor-pointer items-center justify-center rounded-full bg-neutral-900/10 text-white"
          >
            <Icon IconComponent={IconClose} size="smaller" />
          </button>
        </div>
      {/if}
    </div>
  {/if}

  {#if !selectedFile && !selectedFiles?.length}
    <div
      role="banner"
      id="dropZone"
      class="flex h-20 w-full flex-col items-center justify-center border-2 border-dotted border-neutral-200"
      on:dragover={handleDragOver}
      on:drop={handleDrop}
    >
      <p class="mb-2 text-neutral-700">Drag & drop here or</p>
      <Button size="small" variant="gray" icon={IconCloud} on:click={() => fileInput?.click()}>
        {allowMultiple ? 'Select files' : 'Select a file'}
      </Button>
    </div>
    {#each errors as error}
      <div class="mb-3 mt-4 flex items-center rounded bg-brand-action/10 p-3 text-brand-action">
        <Icon IconComponent={IconInfo} size="small" />
        <p class="ml-2">
          {error}
        </p>
      </div>
    {/each}
  {/if}
{/if}
