<script context="module">
  import ImportColumnMappingModal from '@/components/Import/ImportColumnMappingModal.svelte';

  export const meta = {
    title: 'components/Import/ImportColumnMappingModal',
    component: ImportColumnMappingModal,
    parameters: {
      chromatic: { delay: 1000 }
    }
  };
</script>

<script lang="ts">
  import { Story, Template } from '@storybook/addon-svelte-csf';
  import type ImportResponse from '@/models/import/import-response';
  import ImportsRepo from '@/repos/import-repo';
  import { onMount } from 'svelte';

  const importsRepo = new ImportsRepo(); //todo: Mock once realy API ready

  let importResponseData: ImportResponse;

  onMount(async () => {
    importResponseData = await importsRepo.upload(null, 'product', 0);
  });
</script>

<Template let:args>
  <div class="table-body">
    {#if importResponseData}
      <ImportColumnMappingModal open={true} {importResponseData} entity="product" />
    {/if}
  </div>
</Template>

<Story name="Product" />
