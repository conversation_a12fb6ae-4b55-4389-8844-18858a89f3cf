<script context="module">
  export const meta = {
    title: 'components/Import/ImportConfirmation',
    component: ImportConfirmation,
    parameters: {
      chromatic: { delay: 4000 }
    }
  };
</script>

<script lang="ts">
  import { Story, Template } from '@storybook/addon-svelte-csf';
  import { onMount } from 'svelte';
  import ImportsRepo from '@/repos/import-repo';
  import ImportConfirmation from '@/components/Import/ImportConfirmation.svelte';
  import type DryRunResults from '@/models/import/dry-run-results';
  import type { ImportData } from '@/models/import/import-response';

  const importsRepo = new ImportsRepo(); //todo: Mock once realy API ready

  let dryRunResults: DryRunResults;
  let importResponseData: ImportData;

  onMount(async () => {
    importResponseData = await importsRepo.upload(null, 'product', 0);
    const response = await importsRepo.dryRun('', null);
    dryRunResults = response.dry_run_results;
  });
</script>

<Template let:args>
  <div class="relative z-10 h-[1000px] w-[500px] rounded bg-white shadow">
    {#if dryRunResults}
      <ImportConfirmation
        storageCode="storagecode"
        {dryRunResults}
        confirmationOpen
        entity="product"
      />
    {/if}
  </div>
</Template>

<Story name="Product" />
