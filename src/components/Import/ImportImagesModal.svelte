<script lang="ts">
  import IconBack from '../Icons/IconBack.svelte';
  import CustomModal from '../Modal/CustomModal.svelte';
  import ImportImageSideBar from './ImportImageSideBar.svelte';
  import { usePostElasticSearch } from '@/lib/es/context/postElasticSearchContext';
  import { ElasticSearchIndex, type SystemProductsResponse } from '@/lib/s2s/types';
  import { useIsFetching, useQueryClient } from '@tanstack/svelte-query';
  import { useElasticSearchContext } from '@/lib/es/context/useElasticSearchContext';
  import esRoutes from '@/lib/esRoutes';
  import { productFacets } from '@/lib/s2sStore';
  import type { IElasticContext } from '@/lib/es/context/createElasticContext';
  import type { SystemProduct } from '@/models/products/product';
  import Btn from '../form/Btn.svelte';
  import IconDelete from '@/components/Icons/IconDelete.svelte';
  import UploadsRepo from '@/repos/uploads-repo';
  import IconClose from '../Icons/IconClose.svelte';
  import { getFileSizeString, getUploadTimeString, type FileStatus } from '@/lib/file-utils';
  import { MB } from '@/constants';

  export let open = false;
  export let maxSize = 5 * MB;

  const queryClient = useQueryClient();
  const index = ElasticSearchIndex.PRODUCTS;
  const isQueryFetching = useIsFetching([index]);

  let currentSearchTerm = '';
  let selectedField = 'variant.sku';
  let findReplaceRules: { find: string; replace: string }[] = [];
  let selectedFiles: File[] | undefined = [];
  let selectedFile: File | undefined = null;
  let fileAccept = 'image/jpeg, image/png';

  const {
    facetsArray,
    request,
    setSearch,
    isDataInvalidated,
    submitChanges,
    data: result,
    currentSearch
  } = useElasticSearchContext<SystemProductsResponse>({
    index,
    request: { path: esRoutes.searchProducts },
    isFetching: isQueryFetching,
    facets: productFacets,
    onInit: (context: IElasticContext<SystemProductsResponse>) => {
      context.setSearch(currentSearchTerm, [
        'variants.sku',
        'variants.source_variant_code',
        'source_product_code'
      ]);
    },
    onRefetch: () => {
      queryClient.invalidateQueries({ queryKey: [index] });
    }
  });

  const doPostElasticSearch = usePostElasticSearch<SystemProductsResponse>();

  let files: FileStatus[] = [];

  async function filesSelected(selectedFiles: File[]): Promise<void> {
    files = selectedFiles.map((file) => ({
      fileRef: file,
      size: getFileSizeString(file.size),
      name: file.name,
      status: 'Loading...',
      linkedProduct: 'Loading...',
      productId: '',
      searchQuery: file.name.substring(0, file.name.indexOf('.'))
    }));

    const queries = files.map((file) => findReplace(file.searchQuery, findReplaceRules));
    const products = await getElasticSearchProducts(queries);

    files = await Promise.all(files.map(async (file) => await updateFileStatus(file, products)));
  }

  async function updateFileStatus(
    file: FileStatus,
    products: SystemProduct[]
  ): Promise<FileStatus> {
    if (file.fileRef.size / MB > maxSize) {
      return {
        ...file,
        status: 'Too large',
        linkedProduct: `File size is too large. Max ${maxSize / MB}MB allowed`
      };
    }

    if (
      !fileAccept
        .split(',')
        .find((t) => t.toLowerCase().trim() == file.fileRef.type.toLowerCase().trim())
    ) {
      return {
        ...file,
        status: 'Unsupported',
        linkedProduct: 'File type is unsupported. Only .jpeg & .png allowed'
      };
    }

    let status = await getStatus(findReplace(file.searchQuery, findReplaceRules), products);
    return {
      ...file,
      status: status.status,
      linkedProduct: status.matchedItem,
      productId: status.productId
    };
  }

  async function getElasticSearchProducts(query: string[]): Promise<SystemProduct[]> {
    const result = await doPostElasticSearch($request.path, getRequestBody(query), $request.params);
    return result.system_products;
  }

  async function getStatus(query: string, products: SystemProduct[]): Promise<any> {
    const matchedItem = products.length > 0 ? findMatchedProduct(products, query) : null;
    return {
      status: matchedItem ? 'Matched' : 'No Match',
      matchedItem: matchedItem ? matchedItem?.title : 'Failed to match product, image ignored!',
      productId: matchedItem ? matchedItem?.id : null
    };
  }

  const findReplace = (
    val: string,
    rules: {
      find: string;
      replace: string;
    }[]
  ): string => {
    if (!rules.length) return val;

    const escapeRegExp = (str: string): string => {
      return str.replace(/([.?*+^$[\]\\(){}|-])/g, '\\$1');
    };

    rules.forEach(({ find, replace }) => {
      replace = replace || '';
      const regex = new RegExp(escapeRegExp(find), 'g');
      val = val.replace(regex, replace);
    });

    return val.toLowerCase();
  };

  function findMatchedProduct(products: SystemProduct[], searchTerm: string): SystemProduct {
    const fieldName = selectedField.startsWith('variant.')
      ? selectedField.replace('variant.', '')
      : selectedField.replace('product.', '');

    const isMatch = (product: SystemProduct) => {
      const fieldValue = selectedField.startsWith('variant.')
        ? product.variants?.some(
            (variant: any) => variant[fieldName]?.toLowerCase() === searchTerm.toLowerCase()
          )
        : product[fieldName]?.toLowerCase() === searchTerm.toLowerCase();
      return Boolean(fieldValue);
    };

    return products.find(isMatch);
  }

  function getRequestBody(query: string[]): any {
    return {
      query: {
        bool: {
          should: query.map((q) => ({
            multi_match: {
              query: q,
              fields: ['variants.sku', 'variants.source_variant_code', 'source_product_code']
            }
          }))
        }
      },
      size: 10
    };
  }

  async function applyRules(rules: { find: string; replace: string }[]) {
    findReplaceRules = rules;

    files = files.map((file) => ({
      ...file,
      status: 'Applying Rules...',
      linkedProduct: 'Applying Rules...'
    }));

    const queries = files.map((file) => findReplace(file.searchQuery, findReplaceRules));
    const products = await getElasticSearchProducts(queries);

    files = await Promise.all(files.map(async (file) => await updateFileStatus(file, products)));
  }

  function removeAll() {
    files = [];
    selectedFiles = [];
    selectedFile = null;
    hideSidebar = false;
    filesSelected;
  }

  let hideSidebar = false;
  let uploadTime = '';
  function uploadAll() {
    hideSidebar = true;
    uploadTime = getUploadTimeString();
    files.forEach((file, index) => {
      if (file.status === 'Matched') {
        const productId = file.productId;
        let uploadRepo = new UploadsRepo(
          `images/${productId}?format=json`,
          //handle progress
          (progress, cancelDelegate) => {
            files[index].status = 'Uploading';
            files[index].progress = progress;
            files[index].cancelDelegate = cancelDelegate;
          },
          //handle sucess
          () => {
            files[index].status = 'Completed';
          },
          //handle failure
          () => {
            files[index].status = 'Failed';
          }
        );

        uploadRepo.uploadFile(file.fileRef);
      }
    });
  }

  function cancelUpload(index: number) {
    files[index].cancelDelegate();
    files[index].status = 'Cancelled';
  }

  function removeFile(fileName: string) {
    files = files.filter((file) => file.name !== fileName);
  }

  function closeModal() {
    open = false;
    hideSidebar = false;
    files = [];
    selectedFiles = [];
    selectedFile = null;
  }

  $: disabledUpload =
    files.some((file) => file.status === 'Uploading') ||
    !files.some((file) => file.status === 'Matched');
</script>

<CustomModal bind:open height="h-5/6 max-h-[836px]">
  <div slot="title" class="w-full">
    <div class="flex w-full items-center justify-between">
      <div class="flex items-center">
        <Btn
          onClick={() => closeModal()}
          size="medium"
          variant="gray"
          Prefix={IconBack}
          iconSize="small"
          class="right-0 !text-neutral-700 hover:!bg-neutral-100"
        />
        <span class="ml-2 text-2xl font-bold text-neutral-800">Import Images</span>
      </div>
    </div>
  </div>
  <div slot="content" class=" h-full text-regular text-neutral-700">
    <div class="flex h-full">
      {#if !hideSidebar}
        <ImportImageSideBar
          {filesSelected}
          bind:selectedFiles
          bind:selectedFile
          bind:selectedField
          bind:findReplaceRules
          onRulesChanged={applyRules}
          {maxSize}
        />
      {/if}
      <div class="w-full p-8">
        {#if hideSidebar}
          <span class="text-neutral-500"> {uploadTime}</span>
        {:else}
          <span class="text-neutral-500">4. Review images to upload</span>
        {/if}
        <div
          style="display: grid; grid-template-columns: 145px  minmax(400px, 1fr) 100px 170px"
          class="mt-6 grid"
        >
          <div
            class="col-span-1 flex h-12 items-center border-b border-b-neutral-200 text-neutral-500"
          >
            <span class="ml-6 mr-8 text-small font-bold">Filename</span>
          </div>
          <div
            class="col-span-1 flex h-12 items-center border-b border-b-neutral-200 text-neutral-500"
          >
            <span class="text-small font-bold">Linked Product</span>
          </div>
          <div
            class="col-span-1 flex h-12 items-center border-b border-b-neutral-200 text-neutral-500"
          >
            <span class="text-right text-small font-bold">Size</span>
          </div>
          <div
            class="col-span-1 flex h-12 items-center border-b border-b-neutral-200 text-neutral-500"
          >
            <span class="text-right text-small font-bold">Status</span>
          </div>

          {#if !files.length}
            <p class="col-span-5 py-4 pl-6 italic">Please select files to upload.</p>
          {/if}

          {#each files as file, index}
            <div class="col-span-1 flex h-12 items-center border-b border-neutral-200">
              <span class="ml-6 mr-8 w-40 truncate font-bold">{file.name}</span>
            </div>
            <div class="col-span-1 flex h-12 items-center border-b border-neutral-200">
              <span class="truncate">{file.linkedProduct}</span>
            </div>
            <div class="col-span-1 flex h-12 items-center border-b border-neutral-200">
              <span class="text-right">{file.size}</span>
            </div>
            <div
              class="col-span-1 flex h-12 items-center justify-between border-b border-neutral-200"
            >
              {#if file.status === 'Matched'}
                <div
                  class="flex h-6 items-center rounded-l-full rounded-r-full bg-brand-notification/10 p-2 text-right text-brand-notification"
                >
                  <span class="mr-2 h-2 w-2 rounded-full bg-brand-notification" />
                  {file.status}
                </div>
              {:else if file.status === 'No Match'}
                <div
                  class="flex h-6 items-center rounded rounded-l-full rounded-r-full bg-brand-action/10 p-2 text-right text-brand-action"
                >
                  <span class="mr-2 h-2 w-2 rounded-full bg-brand-action" />
                  {file.status}
                </div>
              {:else if file.status === 'Too large'}
                <div
                  class="flex h-6 items-center rounded rounded-l-full rounded-r-full bg-brand-action/10 p-2 text-right text-brand-action"
                >
                  <span class="mr-2 h-2 w-2 rounded-full bg-brand-action" />
                  {file.status}
                </div>
              {:else if file.status === 'Unsupported'}
                <div
                  class="flex h-6 items-center rounded rounded-l-full rounded-r-full bg-brand-action/10 p-2 text-right text-brand-action"
                >
                  <span class="mr-2 h-2 w-2 rounded-full bg-brand-action" />
                  {file.status}
                </div>
              {:else if file.status === 'Uploading'}
                <div class="mr-2 w-full rounded-full bg-gray-300">
                  <div
                    class="h-2 rounded-full bg-brand-confirmation"
                    style="width: {file.progress}%"
                  />
                </div>
              {:else if file.status === 'Cancelled'}
                <div
                  class="flex h-6 items-center rounded rounded-l-full rounded-r-full bg-brand-warning/10 p-2 text-right text-brand-warning"
                >
                  <span class="mr-2 h-2 w-2 rounded-full bg-brand-warning" />
                  {file.status}
                </div>
              {:else if file.status === 'Completed'}
                <div
                  class="flex h-6 items-center rounded rounded-l-full rounded-r-full bg-brand-confirmation/10 p-2 text-right text-brand-confirmation"
                >
                  <span class="mr-2 h-2 w-2 rounded-full bg-brand-confirmation" />
                  {file.status}
                </div>
              {:else if file.status === 'Failed'}
                <div
                  class="flex h-6 items-center rounded rounded-l-full rounded-r-full bg-brand-action/10 p-2 text-right text-brand-action"
                >
                  <span class="mr-2 h-2 w-2 rounded-full bg-brand-action" />
                  {file.status}
                </div>
              {:else}
                <div class="text-right">Loading...</div>
              {/if}
              <div class="pr-6">
                {#if file.status == 'Uploading'}
                  <Btn
                    onClick={() => cancelUpload(index)}
                    size="small"
                    variant="gray"
                    Prefix={IconClose}
                    iconSize="extra-small"
                    class="!text-neutral-700 hover:!bg-neutral-100 "
                  />
                {:else if file.status != 'Completed' && file.status != 'Failed' && file.status != 'Cancelled'}
                  <Btn
                    onClick={() => removeFile(file.name)}
                    size="small"
                    variant="gray"
                    Prefix={IconDelete}
                    iconSize="extra-small"
                    class="!text-neutral-700 hover:!bg-neutral-100 "
                  />
                {/if}
              </div>
            </div>
          {/each}
        </div>
      </div>
    </div>
  </div>
  <div slot="btn" class="flex w-full justify-end">
    {#if true}
      <div class="flex">
        <Btn size="medium" onClick={removeAll} class="mr-4" variant="outline">Remove All</Btn>
        <Btn size="medium" disabled={disabledUpload} onClick={uploadAll} class="" variant="solid"
          >Upload All</Btn
        >
      </div>
    {/if}
  </div>
</CustomModal>
