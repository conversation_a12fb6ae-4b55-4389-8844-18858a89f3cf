<script lang="ts">
  import pluralizeWord from '@/utils/pluralize';
  import ImportsRepo from '@/repos/import-repo';
  import FileSelector from './FileSelector.svelte';
  import { getFileSizeString } from '@/lib/file-utils';
  import capitalize from 'lodash.capitalize';
  import Loader from '../Loader/Loader.svelte';
  import { createConfigQuery } from '@/queries/config.query';
  import type { Channel, Source } from '@/models/config/config';
  import type { ImportData } from '@/models/import/import-response';
  import type { FieldEntity } from '@/models/fields/fields';
  import InlineNotification from '../Notification/InlineNotification.svelte';
  import IconDownload from '../Icons/IconDownload.svelte';
  import Select from '../Controls/Select/Select.svelte';
  import { isNumber } from 'chart.js/helpers';
  import PromptModal from '@/components/Modal/PromptModal.svelte';

  export let open = true;
  export let entity: FieldEntity;
  export let uploadComplete: (importResponse: ImportData) => void;
  export let importRepo = new ImportsRepo();

  const configQuery = createConfigQuery();

  let files = [];
  async function filesSelected(selectedFiles: File[]): Promise<void> {
    files = selectedFiles.map((file) => ({
      fileRef: file,
      size: getFileSizeString(file.size),
      name: file.name,
      status: 'Loading...',
      linkedProduct: 'Loading...',
      productId: '',
      searchQuery: file.name.substring(0, file.name.indexOf('.'))
    }));
  }

  const fileAccept =
    'text/csv,  application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';

  let selectedFile: File | undefined = null;
  let loading = false;
  let fileFormatError = false;

  function closeModal() {
    open = false;
    selectedFile = null;
  }

  $: sources = Object.values($configQuery.data?.sources ?? {}).filter((source) => source.active);
  // Note: Can only import to trade channels
  $: tradeChannels = Object.values($configQuery.data?.channels ?? {}).filter(
    (channel) => channel.type === 'trade'
  );

  let defaultSourceId: number | undefined;
  let defaultChannelId: number | undefined;
  let selectedChannel: Channel | undefined;
  let selectedChannelId: number;
  let selectedSource: Source | undefined;
  let selectedSourceId: number;

  $: {
    if (typeof selectedChannelId === 'undefined' && isNumber(selectedChannel?.id)) {
      selectedChannelId = selectedChannel?.id;
    } else {
      selectedChannel = tradeChannels.find((channel) => channel.id === selectedChannelId);
    }
  }
  $: {
    if (typeof selectedSourceId === 'undefined' && isNumber(selectedSource?.id)) {
      selectedSourceId = selectedSource?.id;
    } else {
      selectedSource = sources.find((source) => source.id === selectedSourceId);
    }
  }

  $: {
    // Allow user to set if > 1 default (default will not have a parent `source_id` set)
    const defaultSources = sources.filter((source) => source.active && !source.source_id);
    defaultSourceId = defaultSources.length === 1 ? defaultSources[0].id : undefined;
  }

  $: {
    // Allow user to set if > 1 channel has been set up for the client
    defaultChannelId = tradeChannels.length === 1 ? tradeChannels[0].id : undefined;
  }

  async function importFile() {
    if (selectedFile) {
      loading = true;
      if (entity === 'customer') {
        const result = await importRepo.upload(selectedFile, entity, {
          channel_id: defaultChannelId ?? selectedChannel.id.toString(),
          source_id: defaultSourceId ?? selectedSource.id.toString()
        });
        uploadComplete(result);
      }

      if (entity == 'product') {
        const result = await importRepo.upload(selectedFile, entity, {
          source_id: defaultSourceId ?? selectedSource.id.toString()
        });
        uploadComplete(result);
      }
      closeModal();
      loading = false;
      4;
    }
  }

  $: isCustomerEntity = entity === 'customer';

  $: disabled =
    selectedFile == null ||
    (!defaultSourceId && !selectedSourceId) ||
    (isCustomerEntity && !defaultChannelId && !selectedChannelId) ||
    fileFormatError;

  $: noTradeChannels = isCustomerEntity && !tradeChannels.length;
</script>

<PromptModal
  title="Import {capitalize(pluralizeWord(entity))}"
  icon={IconDownload}
  position="center"
  size="narrow"
  bind:open
  actionText="Import"
  actionClick={importFile}
  {disabled}
>
  <div class="flex flex-col gap-4 text-regular text-neutral-700">
    {#if loading || $configQuery.isLoading}
      <div class="h-60">
        <Loader />
      </div>
    {:else if noTradeChannels}
      <InlineNotification type="error"
        >There aren't any available trade channels for importing customers.</InlineNotification
      >
    {:else}
      <p>
        You can bulk add and update {pluralizeWord(entity)} by importing a csv file. You can view a
        <a
          class="cursor-pointer font-bold text-brand-notification"
          target="_blank"
          href={entity === 'product'
            ? 'https://docs.google.com/spreadsheets/d/1IBkGXhNFQVejcTtH6GmT2SkjB5G2aZY8cPJhVXXB2qw'
            : 'https://docs.google.com/spreadsheets/d/136K5LWgrh0KH_0rrdhNTejND8-6pD1pTQU5fWRCpx2E'}
          >sample file</a
        >
        to see the best practice for file formatting.
      </p>
      <InlineNotification type="info">
        {#if entity === 'product'}
          You need to include the <b>Source Product Code</b> and <b>Source Variant Code</b> in your csv
          file.
        {:else if entity === 'customer'}
          You need to include the <b>Source Customer Code</b> in your csv file.
        {/if}
      </InlineNotification>

      <FileSelector {filesSelected} {fileAccept} bind:selectedFile bind:fileFormatError />

      {#if !defaultSourceId && !$configQuery.isLoading}
        <div class="mt-4">
          <Select
            label="Please select a source"
            options={sources.map((o) => ({ value: o.id, label: o.description }))}
            bind:value={selectedSourceId}
            required
          />
        </div>
      {/if}
      {#if isCustomerEntity}
        {#if !defaultChannelId && !$configQuery.isLoading}
          <div class="mt-4">
            <Select
              label="Please select a channel"
              options={tradeChannels.map((o) => ({ value: o.id, label: o.description }))}
              bind:value={selectedChannelId}
              required
            />
          </div>
        {/if}
      {/if}
    {/if}
  </div>
</PromptModal>
