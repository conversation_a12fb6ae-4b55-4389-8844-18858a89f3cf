<script context="module">
  export const meta = {
    title: 'components/Import/ImportImagesModal',
    component: ImportImagesModal
  };
</script>

<script lang="ts">
  import { Story, Template } from '@storybook/addon-svelte-csf';
  import ImportImagesModal from '@/components/Import/ImportImagesModal.svelte';
  import { setPostElasticSearch } from '@/lib/es/context/postElasticSearchContext';
  import productJson from '@/stories/test-data/products/es_products.json';
  import type { SystemProductsResponse } from '@/lib/s2s/types';
  import { within } from '@storybook/test';

  async function mountInValidFile({ canvasElement }) {
    const canvas = within(canvasElement);
    const input = await canvas.findByTestId('test-file-input');
    mockSelectedFile(input, 'validfile.csv', 'text/csv', 2131549);
  }

  async function mountToLargeFile({ canvasElement }) {
    const canvas = within(canvasElement);
    const input = await canvas.findByTestId('test-file-input');
    mockSelectedFile(input, 'validfile.csv', 'text/csv', 213154900);
  }

  async function mountValidFile({ canvasElement }) {
    const canvas = within(canvasElement);
    const input = await canvas.findByTestId('test-file-input');
    mockSelectedFile(input, 'validfile.png', 'image/png', 21315);
  }

  async function mountValidMatchFile({ canvasElement }) {
    const canvas = within(canvasElement);
    const input = await canvas.findByTestId('test-file-input');
    mockSelectedFile(input, 'key001.png', 'image/png', 21315);
  }

  function mockSelectedFile(
    inputElement: HTMLElement,
    fileName: string,
    fileType: string,
    fileSize: number
  ) {
    const content = new Uint8Array(fileSize); // Create a Uint8Array with the desired file size
    const file = new File([content], fileName, { type: fileType, lastModified: Date.now() });
    Object.defineProperty(inputElement, 'files', { value: [file], writable: true });
    inputElement.dispatchEvent(new Event('change', { bubbles: true }));
  }

  async function doPostElasticSearch() {
    return productJson as SystemProductsResponse;
  }
  setPostElasticSearch(doPostElasticSearch);
</script>

<Template>
  <ImportImagesModal open={true} />
</Template>

<Story name="Default" />

<Story name="Invalid File" play={mountInValidFile} />

<Story name="Too large File" play={mountToLargeFile} />

<Story name="Valid No match " play={mountValidFile} />

<Story name="Valid Match" play={mountValidMatchFile} />
