<script lang="ts">
  import { getInnerAgg, metaFieldsSearchRequest } from '@/lib/es/transforms';
  import { createQuery } from '@tanstack/svelte-query';
  import { postProductsES } from '@/lib/s2s/products';
  import debounce from 'just-debounce-it';
  import type { TableColumn } from '@/lib/s2s/types';
  import mapAggregationsToColumns from '@/utils/mapAggregationsToColumns';

  let body = metaFieldsSearchRequest('');
  let value = '';
  let debouncedValue = '';

  $: result = createQuery({
    queryKey: ['product-fields', debouncedValue],
    queryFn: () =>
      postProductsES(body, {
        exclude_products: true
      })
  });

  const updateSearch = () => {
    debouncedValue = value;
    body = metaFieldsSearchRequest(debouncedValue);
  };

  const handleInput = debounce(updateSearch, 100);

  let columns: TableColumn[];
  $: columns = mapAggregationsToColumns($result.data?.aggregations);
</script>

<!-- @component
 Example component - do delete
 -->

<input
  type="text"
  class="border-1 form-input rounded border-gray-700 bg-gray-50 p-2 dark:bg-gray-800"
  bind:value
  on:input={handleInput}
/>

<div class="flex flex-col" style="width: 600px">
  <div class="border-collapse border border-dashed border-gray-500 p-2">
    <div class="pb-2">Facets</div>
    <pre class="flex gap-2">
      {$result.isLoading}
      {JSON.stringify(columns, undefined, 2)}
    </pre>
  </div>
</div>
