<script lang="ts">
  import createIndexQuery from '@/lib/es/createIndexQuery';
  import esRoutes from '@/lib/esRoutes';
  import { facetsWithIndex } from '@/lib/es/transforms';
  import { useElasticSearchContext } from '@/lib/es/context/useElasticSearchContext';
  import type { SystemProductsResponse } from '@/lib/s2s/types';
  import type { IElasticContext } from '@/lib/es/context/createElasticContext';
  import postElasticSearch from '@/lib/es/api';
  import { useIsFetching, useQueryClient } from '@tanstack/svelte-query';
  import Facet from '@/lib/es/Facet.svelte';
  import type { FacetWithIndex } from '@/lib/es/types';
  import ESSearch from '@/lib/es/ESSearch.svelte';
  import Filters from '@/lib/es/Filters.svelte';
  import Hits from '@/lib/es/Hits.svelte';
  import FiltersReset from '@/lib/es/FiltersReset.svelte';
  import Sort from '@/lib/es/Sort.svelte';
  import Stats from '@/lib/es/Stats.svelte';
  import Pagination from '@/lib/es/Pagination.svelte';
  import { FacetType } from '$lib/es/types';
  import { writable } from 'svelte/store';

  const queryClient = useQueryClient();
  const index = 'global';
  const isQueryFetching = useIsFetching([index]);
  const isQueryLoading = writable(true);

  const fac: FacetWithIndex[] = facetsWithIndex(index, [
    {
      type: FacetType.Default,
      key: 'product_type',
      label: 'Facet',
      isSearchable: true,
      agg: {
        aggs: {
          product_type: {
            terms: {
              field: 'product_type'
            }
          }
        },
        filter: {
          bool: {
            filter: [
              {
                prefix: {
                  product_type: ''
                }
              }
            ]
          }
        }
      }
    },
    {
      type: FacetType.Default,
      key: 'collection',
      label: 'Facet',
      agg: {
        aggs: {
          collection: {
            terms: {
              field: 'collection'
            }
          }
        },
        filter: {
          bool: {
            filter: [
              {
                prefix: {
                  collection: ''
                }
              }
            ]
          }
        }
      }
    },
    {
      type: FacetType.Default,
      key: 'meta.key.test',
      label: 'Menu',
      agg: {
        nested: {
          path: 'meta'
        },
        aggs: {
          'meta.key.test': {
            aggs: {
              'meta.key.test': {
                terms: {
                  field: 'meta.value'
                }
              }
            },
            filter: {
              bool: {
                filter: [
                  {
                    term: {
                      'meta.key': 'test'
                    }
                  },
                  {
                    prefix: {
                      'meta.value': ''
                    }
                  }
                ]
              }
            }
          }
        }
      }
    }
  ]);

  const {
    facetsArray,
    request,
    store,
    submitChanges,
    data: result
  } = useElasticSearchContext<SystemProductsResponse>({
    index,
    request: { path: esRoutes.searchProducts },
    isFetching: isQueryFetching,
    isLoading: isQueryLoading,
    facets: writable(fac),
    onInit: (context: IElasticContext<SystemProductsResponse>) => {
      context.setSource(['title', 'id', 'variants.sku']);
      context.setSortFields(['modified', 'id']);
      context.setSort('modified', 'desc');
      context.submitChanges();
    },
    onRefetch: () => {
      queryClient.invalidateQueries({ queryKey: [index] });
    }
  });

  const data_key = {
    id: 'ID',
    title: 'Title'
  };

  const data = createIndexQuery(index, () => {
    if ($request.body && $request.path) {
      return postElasticSearch($request.path, $request.body, {
        exclude_products: true
      });
    }
  });

  $: $isQueryLoading = $data.isLoading || !$result.system_products;
</script>

<div class="flex flex-col" style="width: 600px">
  <div class="border-collapse border border-dashed border-gray-500 p-2">
    <div class="pb-2">Global search</div>
    <ESSearch {index} placeholder="Search" />
  </div>

  <div class="border-collapse border border-dashed border-gray-500 p-2">
    <div class="pb-2">Filters</div>
    <div class="flex gap-2">
      <FiltersReset {index} />
      <Filters {index} />
    </div>
  </div>

  <div class="border-collapse border border-dashed border-gray-500 p-2">
    <div class="pb-2">Facets</div>
    <div class="flex gap-2">
      {#each $facetsArray as f (f.key)}
        <div class="rounded bg-white">
          <Facet {...f} />
        </div>
      {/each}
    </div>
  </div>

  <div class="border-collapse border border-dashed border-gray-500 p-2">
    <div class="inline-flex gap-2">
      <div>
        <Sort {index} />
      </div>
      <div class="inline-block h-full align-middle">
        <Stats {index} />
      </div>
    </div>
  </div>
  <div class="border-collapse border border-dashed border-gray-500 p-2">
    <div class="pb-2">Hits</div>
    <Pagination {index} />
    <div class="text-gray-400">
      <Hits {index} {data_key} />
    </div>
  </div>
</div>
