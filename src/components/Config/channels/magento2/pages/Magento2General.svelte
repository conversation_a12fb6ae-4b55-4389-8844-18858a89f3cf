<script lang="ts">
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import ConfigToggle from '@/components/Config/shared/cards/ConfigToggle.svelte';
  import AttributeSetIdCard from '@/components/Config/channels/magento2/cards/AttributeSetIdCard.svelte';
  import { DELETE_PRODUCTS, MANAGE_IMAGES, MANAGE_VISIBILITY } from '@/components/Config/constants';

  export let workingCopy: ChannelWorkingCopy;
</script>

<PageWrapper>
  <!-- Products -->
  <ConfigToggle
    bind:workingCopy
    metaKey={MANAGE_VISIBILITY}
    title="Manage Visibility"
    label="Enable"
    description="Enable to allow Stock2Shop to update a product's visibility. See the Magento 2 <a class='underline font-bold' href='https://docs.magento.com/user-guide/system/data-attributes-product.html' target='_blank'>API documentation</a> for details on visibility settings."
  />
  <ConfigToggle
    bind:workingCopy
    metaKey={DELETE_PRODUCTS}
    title="Delete Products"
    label="Allow Product Deletions on {workingCopy.description}?"
    description="Enable to let Stock2Shop automatically delete products from your store when they are removed from Stock2Shop."
  />

  <!-- Images -->
  <ConfigToggle
    bind:workingCopy
    metaKey={MANAGE_IMAGES}
    title="Manage Images"
    label="Allow Image Management on {workingCopy.description}?"
    description="Enable to allow Stock2Shop to automatically handle product images — adding, updating, and removing them when necessary."
  />

  <!-- Categories -->
  <!--

  Commenting this out for now since, if a user enables this setting, they would nen need to populate the category_map.
  This should be done by one of our technical team members, as a user wouldn't know how to do this.

  <ConfigToggle
    bind:workingCopy
    metaKey="manage_categories"
    title="Manage Categories"
    label="Manage Categories"
    description="When enabled and 'category_map' is populated, Stock2Shop will create or update categories accordingly."
  /> -->

  <AttributeSetIdCard bind:workingCopy />
</PageWrapper>
