<script lang="ts">
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import OrderFromDate from '@/components/Config/shared/cards/OrderFromDate.svelte';
  import OrderingDefaultCustomerCode from '@/components/Config/channels/shared/cards/OrderingDefaultCustomerCode.svelte';
  import UsePriceFromS2S from '@/components/Config/channels/shared/cards/OrderingUsePriceFromS2S.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import AddOrderStatusCard from '@/components/Config/channels/magento2/cards/AddOrderStatusCard.svelte';
  import GroupDuplicateOrderItems from '@/components/Config/shared/cards/GroupDuplicateOrderItems.svelte';

  export let workingCopy: ChannelWorkingCopy;
</script>

<PageWrapper>
  <AddOrderStatusCard bind:workingCopy />
  <GroupDuplicateOrderItems bind:workingCopy />
  <OrderFromDate bind:workingCopy />
  <OrderingDefaultCustomerCode bind:workingCopy />
  <UsePriceFromS2S bind:workingCopy />
</PageWrapper>
