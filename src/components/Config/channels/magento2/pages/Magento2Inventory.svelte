<script lang="ts">
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import StockCalculator from '@/components/Config/channels/shared/cards/InventoryStockCalculator.svelte';
  import StoreWideQty from '@/components/Config/channels/shared/cards/StoreWideQty.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import ConfigToggle from '@/components/Config/shared/cards/ConfigToggle.svelte';
  import InventoryStock from '@/components/Config/shared/cards/InventoryStock.svelte';
  import { MANAGE_STOCK_STATUS } from '@/components/Config/constants';

  export let workingCopy: ChannelWorkingCopy;
</script>

<PageWrapper>
  <StockCalculator bind:workingCopy />
  <StoreWideQty name="Quantity Upper Limit" metaKey="qty_limit_upper" bind:workingCopy>
    <p>
      <strong>Upper limit for channel quantity.</strong> This value will also restrict the quantity available
      for order.
    </p>
    <br />
  </StoreWideQty>
  <ConfigToggle
    bind:workingCopy
    metaKey={MANAGE_STOCK_STATUS}
    title="Manage Stock Status"
    label="Manage Stock Status"
    description="When enabled, the system will automatically determine a product's stock status. The 'is_in_stock' property should be omitted from maps. This feature is disabled by default."
  />
  <InventoryStock bind:workingCopy />
</PageWrapper>
