<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import Secret from '@/components/Config/shared/components/Secret.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import { PASSWORD, USERNAME } from '@/components/Config/constants';

  export let workingCopy: ChannelWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [USERNAME, PASSWORD]);
</script>

<PageCard title="API Credentials">
  <InlineNotification type="info">
    <p>This is your username and password that you use to log into your Magento store.</p>
  </InlineNotification>
  <div class="max-w-max">
    <TextInput sideLabel label="Username" bind:value={workingCopy.meta[USERNAME].value} />
  </div>
  <Secret keyLabel="Password" key={PASSWORD} connectorKind="channel" connectorId={workingCopy.id} />
</PageCard>
