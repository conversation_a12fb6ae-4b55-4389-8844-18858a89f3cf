<script lang="ts">
  import { ATTRIBUTE_SET_ID } from '@/components/Config/constants';
  import PageCard from '@/components/Config/PageCard.svelte';
  import NumberInput from '@/components/Controls/NumberInput.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: ChannelWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [ATTRIBUTE_SET_ID]);
</script>

<PageCard title="Attribute Set ID">
  <InlineNotification type="info">
    Default attribute set id to assign attribute values to. Defaults to ID '4' if left empty. The ID
    can be found in the Magento 2 admin panel under <strong
      >Stores > Attributes > Attribute Set</strong
    >.
  </InlineNotification>
  <div class="max-w-max">
    <NumberInput sideLabel label="ID" bind:value={workingCopy.meta[ATTRIBUTE_SET_ID].value} />
  </div>
</PageCard>
