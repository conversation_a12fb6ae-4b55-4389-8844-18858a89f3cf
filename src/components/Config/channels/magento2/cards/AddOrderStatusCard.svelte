<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import Select from '@/components/Controls/Select/Select.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import { ADD_ORDER_STATUS } from '@/components/Config/constants';

  export let workingCopy: ChannelWorkingCopy;

  const options = [
    { label: 'Pending', value: 'pending' },
    { label: 'Pending Payment', value: 'pending_payment' },
    { label: 'Processing', value: 'processing' },
    { label: 'Complete', value: 'complete' }
  ];

  $: setDefaultConfigMeta(workingCopy, [ADD_ORDER_STATUS]);
</script>

<PageCard title="Add Order Status">
  <InlineNotification type="info">
    <p>
      Choose the order status to use in {workingCopy.description} when adding an order to the source.
    </p>
    <!--
      TODO: I think the following information does need to be conveyed to the client in some way: "There must be a corresponding `add_order_status` property on the 'order_map'"
    -->
  </InlineNotification>
  <div class="max-w-96">
    <Select
      label="Status"
      sideLabel
      bind:value={workingCopy.meta[ADD_ORDER_STATUS].value}
      {options}
    />
  </div>
</PageCard>
