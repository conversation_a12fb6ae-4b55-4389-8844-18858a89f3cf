<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import { URL } from '@/components/Config/constants';

  export let workingCopy: ChannelWorkingCopy;

  const API_VERSION = 'V1';
  const API_URI_SUFFIX = '/rest/';
  const API_URI_SUFFIX_FULL = API_URI_SUFFIX + API_VERSION + '/';

  let store_name: string = extractStoreName(workingCopy.meta[URL].value);
  let domain_name: string = extractDomain(workingCopy.meta[URL].value);

  $: {
    workingCopy.meta[URL].value = domain_name;
    if (workingCopy.meta[URL] && workingCopy.meta[URL].value) {
      // Extract the domain name from the URL and remove the uri
      const domain = extractDomain(workingCopy.meta[URL].value);
      const uri = store_name
        ? API_URI_SUFFIX + store_name + '/' + API_VERSION + '/'
        : API_URI_SUFFIX_FULL;
      workingCopy.meta[URL].value = `${domain}${uri}`;
    }
    setDefaultConfigMeta(workingCopy, [URL]);
  }

  /**
   * Extracts the domain from the URL.
   * e.g. 'https://fun-shirts.com/rest/english/V1' -> 'https://fun-shirts.com'
   * @param url
   */
  function extractDomain(url: string): string {
    const parts = url.split(API_URI_SUFFIX);
    return parts[0];
  }

  /**
   * Extracts the store name from the URL.
   * e.g. 'https://fun-shirts.com/rest/english/V1' -> 'english'
   * @param url
   */
  function extractStoreName(url: string): string {
    if (!url.includes(API_URI_SUFFIX_FULL)) {
      const parts = url.split(API_URI_SUFFIX);

      // Remove the API version from the store name (case insensitive)
      const regEx = new RegExp('/V1/', 'ig');
      const regEx2 = new RegExp('/V1', 'ig');
      if (parts.length > 1) {
        const store = parts[1].replace(regEx, '');
        return store.replace(regEx2, '');
      }
    }
    return '';
  }
</script>

<PageCard title="API Domain">
  <InlineNotification type="info">
    <p>
      Enter the Magento 2 REST endpoint, including the trailing forward slash. To update a specific
      store, use 'rest/[store]/V1/'.
    </p>
    <p>
      For more details, refer to the <a
        class="font-bold underline"
        href="https://devdocs.magento.com/"
        target="_blank">Magento 2 REST API documentation</a
      >
    </p>
  </InlineNotification>
  <TextInput label="URL" placeholder="e.g. https://fun-shirts.com" bind:value={domain_name} />

  <InlineNotification type="info">
    <p>
      You can optionally specify your store name, store ID, or 'all' based on your Magento 2 update
      requirements. For most integrations, leaving this field empty is sufficient.
    </p>
    <p>
      If you're unsure whether to enter anything, please contact our <a
        href="mailto:<EMAIL>"
        class="font-bold underline">support team.</a
      >
    </p>
    <br />
    <p>
      <strong>Note:</strong> The store name is included in the API URL during updates, so only use it
      if necessary.
    </p>
  </InlineNotification>
  <TextInput label="Store name" placeholder="e.g. english" bind:value={store_name} />
</PageCard>
