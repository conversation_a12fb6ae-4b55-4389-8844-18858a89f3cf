<script lang="ts">
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import ConnectionDomain from '../cards/ConnectionDomain.svelte';
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import SecretCard from '@/components/Config/shared/cards/SecretCard.svelte';
  import { API_KEY } from '@/components/Config/constants';

  export let workingCopy: ChannelWorkingCopy;
</script>

<PageWrapper>
  <ConnectionDomain bind:workingCopy />
  <SecretCard
    info="Provide your Mirakl API key to authenticate and securely link Stock2Shop to your account."
    title="API Key"
    connectorKind="channel"
    connectorId={workingCopy.id}
    key={API_KEY}
  />
</PageWrapper>
