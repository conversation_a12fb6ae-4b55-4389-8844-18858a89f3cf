<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import { URL } from '@/components/Config/constants';

  export let workingCopy: ChannelWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [URL]);
</script>

<PageCard title="Domain">
  <InlineNotification type="info">
    Enter your Mirakl domain URL without a trailing slash.
  </InlineNotification>
  <TextInput label="Domain" bind:value={workingCopy.meta[URL].value} />
</PageCard>
