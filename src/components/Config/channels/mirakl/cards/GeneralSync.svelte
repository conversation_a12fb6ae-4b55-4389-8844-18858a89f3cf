<script lang="ts">
  import { SYNC } from '@/components/Config/constants';
  import PageCard from '@/components/Config/PageCard.svelte';
  import Select from '@/components/Controls/Select/Select.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: ChannelWorkingCopy;

  const options = [
    { value: '', label: 'Both' },
    { value: 'offers', label: 'Offers' },
    { value: 'products', label: 'Products' }
  ];

  $: setDefaultConfigMeta(workingCopy, [SYNC]);
</script>

<PageCard title="Sync Preference">
  <InlineNotification type="info">
    Stock2Shop allows you to sync products, offers, or both, based on your preferences.
  </InlineNotification>
  <div class="max-w-96">
    <Select sideLabel label="Sync Preference" {options} bind:value={workingCopy.meta[SYNC].value} />
  </div>
</PageCard>
