<script lang="ts">
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import Domain from '@/components/Config/channels/woocommerce/cards/ConnectionDomain.svelte';
  import AuthenticationType from '@/components/Config/channels/woocommerce/cards/AuthenticationTypeCard.svelte';
  import CredentialsCard from '@/components/Config/channels/woocommerce/cards/CredentialsCard.svelte';

  export let workingCopy: ChannelWorkingCopy;
</script>

<PageWrapper>
  <Domain bind:workingCopy />
  <CredentialsCard bind:workingCopy />
  <AuthenticationType bind:workingCopy />
</PageWrapper>
