<script lang="ts">
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import ConfigToggle from '@/components/Config/shared/cards/ConfigToggle.svelte';
  import CreateProductStatusCard from '@/components/Config/channels/woocommerce/cards/CreateProductStatusCard.svelte';
  import {
    MANAGE_CATEGORY_ENABLED,
    CREATE_PRODUCTS_ENABLED,
    DELETE_PRODUCTS_ENABLED,
    CREATE_IMAGE_ENABLED,
    DELETE_IMAGE_ENABLED
  } from '@/components/Config/constants';

  export let workingCopy: ChannelWorkingCopy;

  let showCategoryId: boolean = false;

  $: showCategoryId = workingCopy.meta[MANAGE_CATEGORY_ENABLED]
    ? workingCopy.meta[MANAGE_CATEGORY_ENABLED].value === 'true'
    : false;
</script>

<PageWrapper>
  <!-- Products -->
  <CreateProductStatusCard bind:workingCopy />
  <ConfigToggle
    bind:workingCopy
    metaKey={CREATE_PRODUCTS_ENABLED}
    title="Create Products"
    description="<p>Enable this option to allow the creation of new products on {workingCopy.description}. When disabled, only updates will occur for existing products on the {workingCopy.description}.</p>"
    label="Create Products?"
  />
  <ConfigToggle
    bind:workingCopy
    metaKey={DELETE_PRODUCTS_ENABLED}
    title="Delete Products"
    description="<p>Enable this option to give Stock2Shop permission to remove products from {workingCopy.description} after they have been removed from Stock2Shop.</p>"
    label="Delete Products?"
  />

  <!-- Images -->
  <ConfigToggle
    bind:workingCopy
    metaKey={CREATE_IMAGE_ENABLED}
    title="Create Images"
    description="Enable this option to create new images on {workingCopy.description}."
    label="Create Images?"
  />

  <ConfigToggle
    bind:workingCopy
    metaKey={DELETE_IMAGE_ENABLED}
    title="Delete Images"
    description="Enable this option to give Stock2Shop permission to remove images from {workingCopy.description} after they have been removed from Stock2Shop."
    label="Delete Images?"
  />
</PageWrapper>
