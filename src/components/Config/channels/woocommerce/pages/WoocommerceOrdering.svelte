<script lang="ts">
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import OrderFromDate from '@/components/Config/shared/cards/OrderFromDate.svelte';
  import ParamDefaultCustomerCodeCard from '@/components/Config/channels/shared/cards/OrderingDefaultCustomerCode.svelte';
  import OrderingUsePriceFromS2S from '@/components/Config/channels/shared/cards/OrderingUsePriceFromS2S.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import AddOrderStatusCard from '@/components/Config/channels/woocommerce/cards/AddOrderStatusCard.svelte';
  import OrderCodeFieldCard from '@/components/Config/channels/woocommerce/cards/OrderCodeFieldCard.svelte';
  import ShippingTitleCard from '@/components/Config/channels/woocommerce/cards/ShippingTitleCard.svelte';
  import ConfigToggle from '@/components/Config/shared/cards/ConfigToggle.svelte';
  import GroupDuplicateOrderItems from '@/components/Config/shared/cards/GroupDuplicateOrderItems.svelte';
  import { API_WEBHOOK_VERSION } from '@/components/Config/constants';

  export let workingCopy: ChannelWorkingCopy;
</script>

<PageWrapper>
  <AddOrderStatusCard bind:workingCopy />
  <OrderCodeFieldCard bind:workingCopy />
  <GroupDuplicateOrderItems bind:workingCopy />
  <ConfigToggle
    bind:workingCopy
    metaKey={API_WEBHOOK_VERSION}
    title="API Webhook Version"
    label="Enable v3 Latest"
    description="<p>By default, the legacy v3 webhook is used. You can also enable the latest v3 webhook. If you do so, make sure that the webhook version in your WooCommerce settings is set to 'v3 latest'.</p>"
  />
  <OrderFromDate bind:workingCopy />
  <ParamDefaultCustomerCodeCard bind:workingCopy />
  <OrderingUsePriceFromS2S bind:workingCopy />
  <ShippingTitleCard bind:workingCopy />
</PageWrapper>
