<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import Select from '@/components/Controls/Select/Select.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import { CREATE_PRODUCT_STATUS } from '@/components/Config/constants';

  export let workingCopy: ChannelWorkingCopy;

  const options = [
    { label: 'Draft', value: 'draft' },
    { label: 'Publish', value: 'publish' }
  ];

  $: setDefaultConfigMeta(workingCopy, [CREATE_PRODUCT_STATUS]);
</script>

<PageCard title="Create Product Status">
  <InlineNotification type="info">
    <p>Choose the status for new products. If not set, 'publish' will be used by default.</p>
  </InlineNotification>
  <div class="max-w-96">
    <Select
      label="Status"
      sideLabel
      bind:value={workingCopy.meta[CREATE_PRODUCT_STATUS].value}
      {options}
    />
  </div>
</PageCard>
