<script lang="ts">
  import { ORDER_SHIPPING_METHOD_NAME } from '@/components/Config/constants';
  import PageCard from '@/components/Config/PageCard.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: ChannelWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [ORDER_SHIPPING_METHOD_NAME]);
</script>

<PageCard title="Shipping Title">
  <InlineNotification type="info">
    <p>
      Stock2Shop allows you to replace the shipping method title from this channel with a custom
      title before the order is added to Stock2Shop. This can be useful if there is specific
      terminology used within your business.
    </p>
  </InlineNotification>
  <div class="max-w-max">
    <TextInput
      sideLabel
      label="Custom title"
      bind:value={workingCopy.meta[ORDER_SHIPPING_METHOD_NAME].value}
    />
  </div>
</PageCard>
