<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import Select from '@/components/Controls/Select/Select.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import { ORDER_CODE_FIELD } from '@/components/Config/constants';

  export let workingCopy: ChannelWorkingCopy;

  const options = [
    { label: 'Order ID', value: 'id' },
    { label: 'Order Number', value: 'order_number' }
  ];

  $: setDefaultConfigMeta(workingCopy, [ORDER_CODE_FIELD]);
</script>

<PageCard title="Order Code Field">
  <InlineNotification type="info">
    <p>
      Specify the WooCommerce field from the order webhook to be used as the order number. If not
      set, the Order ID will be used by default.
    </p>
  </InlineNotification>
  <div class="max-w-96">
    <Select
      label="Field"
      sideLabel
      bind:value={workingCopy.meta[ORDER_CODE_FIELD].value}
      {options}
    />
  </div>
</PageCard>
