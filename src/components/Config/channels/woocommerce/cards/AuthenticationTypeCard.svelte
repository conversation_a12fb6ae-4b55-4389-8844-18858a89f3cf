<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import Select from '@/components/Controls/Select/Select.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import { AUTHENTICATION } from '@/components/Config/constants';

  export let workingCopy: ChannelWorkingCopy;

  const options = [
    { label: 'Query Params', value: 'query_params' },
    { label: 'Basic', value: 'basic' },
    { label: 'Oauth', value: 'oauth1' }
  ];

  $: setDefaultConfigMeta(workingCopy, [AUTHENTICATION]);
</script>

<PageCard title="Authentication Type">
  <InlineNotification type="info">
    <p>Query Params is the default authentication type.</p>
    <p>Only use Basic Authentication if Query Params does not work.</p>
    <br />
    <p>
      <strong>Warning</strong>: The server may log these values.
    </p>
  </InlineNotification>
  <div class="max-w-96">
    <Select
      label="Authentication Type"
      sideLabel
      bind:value={workingCopy.meta[AUTHENTICATION].value}
      {options}
    />
  </div>
</PageCard>
