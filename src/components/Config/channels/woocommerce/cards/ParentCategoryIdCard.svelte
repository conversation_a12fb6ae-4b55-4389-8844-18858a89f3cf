<script lang="ts">
  import { CATEGORY_PARENT_ID } from '@/components/Config/constants';
  import PageCard from '@/components/Config/PageCard.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: ChannelWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [CATEGORY_PARENT_ID]);
</script>

<PageCard title="Parent Category ID">
  <InlineNotification type="info">
    <p>Since you've enabled Stock2Shop to manage categories, you'll need a Parent Category ID</p>
    <p>This is the Woocommerce category ID to use as parent for Stock2Shop.</p>
  </InlineNotification>
  <div class="max-w-max">
    <TextInput
      sideLabel
      label="Category ID"
      bind:value={workingCopy.meta[CATEGORY_PARENT_ID].value}
    />
  </div>
</PageCard>
