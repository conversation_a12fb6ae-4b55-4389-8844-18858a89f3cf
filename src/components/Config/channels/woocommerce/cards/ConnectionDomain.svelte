<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';
  import { API_URL } from '@/components/Config/constants';

  export let workingCopy: ChannelWorkingCopy;

  const LEGACY_URI = '/wc-api/v3';
  const LATEST_URI = '/wp-json/wc/v3';

  let domain_name: string = extractDomain(workingCopy.meta[API_URL].value);
  let latest_api_enabled: boolean = workingCopy.meta[API_URL].value.includes(LATEST_URI);

  $: {
    workingCopy.meta[API_URL].value = domain_name;
    if (workingCopy.meta[API_URL] && workingCopy.meta[API_URL].value) {
      // Extract the domain name from the URL and remove the protocol and uri
      const domain = extractDomain(workingCopy.meta[API_URL].value);

      // Update the API URL based according to whether the latest API is enabled or not
      workingCopy.meta[API_URL].value = `${domain}${latest_api_enabled ? LATEST_URI : LEGACY_URI}`;
    }
    setDefaultConfigMeta(workingCopy, [API_URL]);
  }

  function extractDomain(url: string): string {
    return url.replace(LATEST_URI, '').replace(LEGACY_URI, '');
  }
</script>

<PageCard title="API Domain">
  <InlineNotification type="info">
    <p>The domain of your Woocommerce store, which must be secured with HTTPS.</p>
  </InlineNotification>
  <TextInput label="Domain" placeholder="e.g. https://fun-shirts.com" bind:value={domain_name} />

  <InlineNotification type="info">
    <p>
      You can choose to use the latest WooCommerce API version (v3 Latest). By default, legacy v3 is
      used.
    </p>
  </InlineNotification>

  <div class="max-w-max">
    <Toggle label="Enable v3 Latest" bind:checked={latest_api_enabled} noLabelBorder />
  </div>
</PageCard>
