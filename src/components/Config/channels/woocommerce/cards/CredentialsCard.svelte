<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import Secret from '@/components/Config/shared/components/Secret.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import { CONSUMER_KEY, CONSUMER_SECRET } from '@/components/Config/constants';

  export let workingCopy: ChannelWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [CONSUMER_KEY, CONSUMER_SECRET]);
</script>

<PageCard title="API Credentials">
  <InlineNotification type="info">
    <p>To obtain your WooCommerce API credentials, follow the steps below:</p>
    <br />
    <ol class="list-inside list-decimal">
      <li>Log in to your WooCommerce store.</li>
      <li>Go to WooCommerce > Settings > Advanced > REST API.</li>
      <li>Click on the Add Key button.</li>
      <li>Enter a description for the key, select the user, and set the permissions.</li>
      <li>Click on the Generate API Key button.</li>
      <li>Copy the Consumer Key and Consumer Secret and paste them below.</li>
    </ol>
  </InlineNotification>
  <Secret
    keyLabel="Consumer Key"
    key={CONSUMER_KEY}
    connectorKind="channel"
    connectorId={workingCopy.id}
  />
  <Secret
    keyLabel="Consumer Secret"
    key={CONSUMER_SECRET}
    connectorKind="channel"
    connectorId={workingCopy.id}
  />
</PageCard>
