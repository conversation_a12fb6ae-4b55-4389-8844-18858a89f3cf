<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import Select from '@/components/Controls/Select/Select.svelte';

  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import { ADD_ORDER_STATUS } from '@/components/Config/constants';

  export let workingCopy: ChannelWorkingCopy;

  const options = [
    { label: 'Pending', value: 'pending' },
    { label: 'Processing', value: 'processing' },
    { label: 'On Hold', value: 'on-hold' },
    { label: 'Completed', value: 'completed' }
  ];

  $: setDefaultConfigMeta(workingCopy, [ADD_ORDER_STATUS]);
</script>

<PageCard title="Add Order Status">
  <InlineNotification type="info">
    Specify the WooCommerce order status to use when adding an order to the source.
  </InlineNotification>
  <div class="max-w-96">
    <Select
      label="Status"
      sideLabel
      bind:value={workingCopy.meta[ADD_ORDER_STATUS].value}
      {options}
    />
  </div>
</PageCard>
