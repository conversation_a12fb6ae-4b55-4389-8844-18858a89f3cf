<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import Button from '@/components/Controls/Button.svelte';
  import { confirmModal } from '@/components/Modal/modal';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { SystemChannelSource } from '@/models/channel-sources/channel-source';
  import { createChannelSyncSourceMutation } from '@/mutations/channels.mutation';
  import { createPollChannelSourceQuery } from '@/queries/channel-sources.query';
  import { createConfigQuery } from '@/queries/config.query';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { isNumber } from '@/utils/typeguards';

  export let workingCopy: ChannelWorkingCopy;
  export let channelSource: SystemChannelSource;

  const configQuery = createConfigQuery();

  $: channelSourcesQuery = createPollChannelSourceQuery({
    channelId: workingCopy.id,
    sourceId: channelSource.source_id,
    enabled: isNumber(workingCopy.id)
  });

  $: channelSourceDescription = $configQuery.data?.sources[channelSource.source_id]?.description;

  $: isSyncing = channelSource?.status === 'syncing';

  const channelSyncSourceMutation = createChannelSyncSourceMutation({
    onSuccess: async () => {
      $channelSourcesQuery.refetch();
    }
  });

  const handleSync = async () => {
    if (
      await confirmModal({
        title: `Sync Data Source`,
        description: `Are you sure you want to sync this source to your channel?`
      })
    ) {
      if (isNumber(channelSource?.channel_id) && isNumber(channelSource?.source_id)) {
        $channelSyncSourceMutation.mutate({
          channelId: channelSource?.channel_id,
          sources: [channelSource?.source_id]
        });
      }
    }
  };
</script>

<PageCard title="Sync Channel ({channelSourceDescription})">
  <InlineNotification type="info">
    <p>
      Your online platform where products are published—in this case, the {workingCopy.description} channel.
    </p>
    <br />
    <p>Click 'Run Full Re-sync' to update products based on your configuration.</p>
    <br />
    <p>
      Please note that this process may take some time, depending on the channel, so we appreciate
      your patience.
    </p>
  </InlineNotification>
  <InlineNotification type="warning">
    <p>Your products may currently be syncing.</p>
    <p>
      Please make sure your <a class="font-bold underline" href="/queue">queue</a> is empty before initiating
      a full re-sync.
    </p>
  </InlineNotification>
  {#if $channelSyncSourceMutation.isError}
    <InlineNotification type="error">
      {$channelSyncSourceMutation.error instanceof Error
        ? $channelSyncSourceMutation.error.message
        : 'Error running full resync'}
    </InlineNotification>
  {/if}
  <div>
    <Button
      variant="outline"
      size="large"
      disabled={$channelSyncSourceMutation.isPending || isSyncing}
      on:click={handleSync}
    >
      {$channelSyncSourceMutation.isPending || isSyncing
        ? 'Running Full Re-sync'
        : 'Run Full Re-sync'}
    </Button>
  </div>
</PageCard>
