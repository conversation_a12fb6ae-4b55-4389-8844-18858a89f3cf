<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import Chip from '@/components/Controls/Tag/Chip.svelte';
  import Button from '@/components/Controls/Button.svelte';
  import { createGroupFieldsQuery } from '@/queries/fields.query';
  import DynamicFieldSelector from '@/components/Controls/Select/FieldSelector/DynamicFieldSelector.svelte';

  export let workingCopy: ChannelWorkingCopy;

  let newWarehouse: string | undefined;
  let warehouses = workingCopy.qty_availability?.split('+') ?? [];

  const fieldsData = createGroupFieldsQuery({
    entity: 'product',
    group: 'qty'
  });

  $: noWarehouses = $fieldsData.data?.fields.length === 0;

  const onAdd = () => {
    if (newWarehouse) {
      warehouses.push(newWarehouse);
      warehouses = warehouses;
      workingCopy.qty_availability = warehouses.join('+');
      newWarehouse = undefined;
    }
  };

  const handleDelete = (warehouse?: string) => {
    if (!warehouse) return;
    warehouses = warehouses.filter((w) => w !== warehouse);
    workingCopy.qty_availability = warehouses.join('+');
  };
</script>

<PageCard title="Stock Calculator">
  <InlineNotification type="info">
    <p>
      Select the warehouse(s) that manage your {workingCopy.description} stock quantities. If multiple
      warehouses are chosen, their quantities will be combined.
    </p>
  </InlineNotification>
  {#if noWarehouses}
    <InlineNotification type="warning">
      Currently there are no additional warehouses set for any products.
    </InlineNotification>
  {:else}
    <div class="flex gap-2">
      <div class="w-48">
        <DynamicFieldSelector
          bind:value={newWarehouse}
          entity="product"
          group="qty"
          placeholder="Add a warehouse"
          canClear
          excludeValues={warehouses}
          handleClear={() => (newWarehouse = undefined)}
        />
      </div>

      <Button variant="outline" on:click={onAdd} size="large">Add</Button>
    </div>
    <div class="flex flex-wrap items-center gap-1">
      <p class="label">Stock =</p>
      {#if warehouses.length}
        {#each warehouses as warehouse, i}
          <Chip {handleDelete} type="info" variant="tag" label={warehouse} />
          {#if i < warehouses.length - 1}
            <p class="label">+</p>{/if}
        {/each}
      {:else}Default{/if}
    </div>
    {#if !warehouses.length}
      <InlineNotification type="warning">
        You have not assigned any warehouses to this channel. This means the default stock level
        will be used.
      </InlineNotification>
    {/if}
  {/if}
</PageCard>
