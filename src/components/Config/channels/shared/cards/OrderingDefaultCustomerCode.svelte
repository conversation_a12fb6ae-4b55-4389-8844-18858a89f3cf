<script lang="ts">
  import { PARAM_DEFAULT_CUSTOMER_CODE } from '@/components/Config/constants';
  import PageCard from '@/components/Config/PageCard.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: ChannelWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [PARAM_DEFAULT_CUSTOMER_CODE]);
</script>

<PageCard title="Default Customer Code">
  <InlineNotification type="info">
    <p>Specify the debtor's account you want to use as the default for orders from this channel.</p>
  </InlineNotification>
  <div class="max-w-max">
    <TextInput
      sideLabel
      label="Default Customer Code"
      bind:value={workingCopy.meta[PARAM_DEFAULT_CUSTOMER_CODE].value}
    />
  </div>
</PageCard>
