<script lang="ts">
  import {
    QUEUE_FULFILL_ORDER,
    DEFAULT_FULFILLMENTSERVICE_ID
  } from '@/components/Config/constants';
  import PageCard from '@/components/Config/PageCard.svelte';
  import Label from '@/components/Controls/Label.svelte';
  import Select from '@/components/Controls/Select/Select.svelte';
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { Config } from '@/models/config/config';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import { createLabel } from '@melt-ui/svelte';

  export let workingCopy: ChannelWorkingCopy;
  export let fulfillmentServices: Config['fulfillmentservices'];

  $: setDefaultConfigMeta(workingCopy, [QUEUE_FULFILL_ORDER, DEFAULT_FULFILLMENTSERVICE_ID]);

  $: {
    if (
      fulfillmentServices.length === 1 &&
      workingCopy.meta[QUEUE_FULFILL_ORDER]?.value === 'true'
    ) {
      workingCopy.meta[DEFAULT_FULFILLMENTSERVICE_ID].value = fulfillmentServices[0].id.toString();
    }
  }

  const {
    elements: { root: labelInternal }
  } = createLabel();
</script>

<PageCard title="Fulfill orders">
  <InlineNotification type="info">
    <p>
      Automatically fulfill paid orders from this channel using
      {#if fulfillmentServices.length > 1}
        one of your fulfillment services
      {:else}
        your fulfillment service
      {/if} your fulfillment service
    </p>
    <p>
      This means orders will be picked, packed, and shipped automatically once payment is received.
    </p>
  </InlineNotification>

  <div class="grid grid-cols-[auto_1fr] items-center gap-x-4 gap-y-6">
    {#if fulfillmentServices.length > 1}
      <div class="w-[150px]">
        <Label side meltAction={labelInternal} title="Fulfillment Enabled" />
      </div>
      <div class="w-full max-w-80">
        <Select
          options={[
            ...fulfillmentServices.map((f) => ({ value: f.id.toString(), label: f.description }))
          ]}
          bind:value={workingCopy.meta[DEFAULT_FULFILLMENTSERVICE_ID].value}
        />
      </div>
    {/if}
    <div class="w-[150px]">
      <Label side meltAction={labelInternal} title="Enable Fulfillment?" id="fulfillment_enabled" />
    </div>
    <Toggle
      bind:checked={workingCopy.meta[QUEUE_FULFILL_ORDER].value}
      type="string"
      noLabelBorder
      id="fulfillment_enabled"
    />
  </div>
</PageCard>
