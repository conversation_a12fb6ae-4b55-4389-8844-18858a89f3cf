<script lang="ts" context="module">
  export type ProductRuleItem = {
    id?: number;
    title: string;
    operator: keyof typeof operatorMap;
    value: string;
  };
</script>

<script lang="ts">
  import { type SystemRule } from '@/models/rules/rules';
  import { productRuleConvertor } from '@/models/fields/fieldConvertors';
  import { operatorMap } from '@/components/Controls/Select/OperatorSelector.svelte';
  import PageCard from '@/components/Config/PageCard.svelte';
  import type { RulesWorkingCopy } from '@/stores/rulesVersionedObject';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import AddProductRule from '@/components/Config/shared/AddProductRule.svelte';
  import { type TableItem } from '@/components/Config/shared/components/ConfigTable.svelte';
  import KeyOperatorValueRow from '@/components/Config/shared/components/KeyOperatorValueRow.svelte';

  export let workingCopy: RulesWorkingCopy;

  let duplicateRule = false;
  let duplicateRuleTimeout: ReturnType<typeof setTimeout>;

  $: if (duplicateRule) {
    duplicateRuleTimeout = setTimeout(() => {
      duplicateRule = false;
    }, 5000);
  }

  const addRule = (key: string, value: string, operator: string) => {
    duplicateRule = false;

    const existingRule = workingCopy.rules.find(
      (r) => r.key === key && r.value === value && r.operator === operator
    );

    if (existingRule) {
      clearTimeout(duplicateRuleTimeout);
      duplicateRule = true;
      return false;
    }

    workingCopy.rules = workingCopy.rules.concat({
      key: key,
      operator: operator,
      value: value,
      channel_id: workingCopy.channel_id
    } as SystemRule);

    return true;
  };

  const onDelete = (productRule: ProductRuleItem) => {
    workingCopy.rules = workingCopy.rules.filter(
      (rule) =>
        !(
          rule.key === productRuleConvertor.fieldToValue(productRule.title) &&
          rule.operator === productRule.operator &&
          rule.value === productRule.value
        )
    );
  };

  let items: TableItem<ProductRuleItem>[];
  $: items =
    workingCopy.rules.map((rule) => {
      return {
        id: rule.id,
        key: rule.key,
        title: productRuleConvertor.valueToField(rule.key),
        operator: rule.operator as keyof typeof operatorMap,
        value: rule.value
      };
    }) ?? [];
</script>

<PageCard title="Filters">
  <InlineNotification type="info">
    <p>
      You can define criteria for products to sync with this channel. Products that don't meet these
      criteria will be excluded from the sync.
    </p>
    {#if workingCopy.type === 'trade'}
      <p>
        You can also 'segment' products by customer, so certain customers see specific products.
      </p>
    {/if}
  </InlineNotification>

  <div>
    {#if items.length > 0}
      {#each items as { key, title, operator, value } (key + operator + value)}
        <KeyOperatorValueRow
          key={title}
          {operator}
          {value}
          on:delete={() => onDelete({ title, operator, value })}
        />
      {/each}
    {/if}
  </div>
  <AddProductRule onAdd={addRule} />

  {#if duplicateRule}
    <InlineNotification type="error">
      <p>Rule already exists</p>
    </InlineNotification>
  {/if}
</PageCard>
