<script lang="ts">
  import ConfigTogglableNumber from '@/components/Config/shared/cards/ConfigTogglableNumber.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';

  export let workingCopy: ChannelWorkingCopy;
  export let name: string;
  export let metaKey: string;
</script>

<!--
  @component shared component for setting and unsetting store-wide quantity settings.
  Description is added as the default slot.
 -->

<ConfigTogglableNumber
  bind:workingCopy
  {metaKey}
  toggleLabel="Set store wide {name}?"
  inputLabel={name}
  {name}
>
  <slot />
  <p>
    To specify a quantity limit for an individual product, create a
    <strong>product attribute</strong>
    with the key '{metaKey}'.
  </p>
</ConfigTogglableNumber>
