<script lang="ts">
  import {
    ORDER_SET_SYSTEM_PRICE,
    ORDER_USE_SYSTEM_PRICE_TIER,
    ORDER_USE_SYSTEM_PRICE_TIER_INCL
  } from '@/components/Config/constants';
  import PageCard from '@/components/Config/PageCard.svelte';
  import DynamicFieldSelector from '@/components/Controls/Select/FieldSelector/DynamicFieldSelector.svelte';
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: ChannelWorkingCopy;

  // just incase we're processing orders inclusive of tax

  $: setDefaultConfigMeta(workingCopy, [
    ORDER_SET_SYSTEM_PRICE,
    ORDER_USE_SYSTEM_PRICE_TIER,
    ORDER_USE_SYSTEM_PRICE_TIER_INCL
  ]);
</script>

<PageCard title="Use price from Stock2Shop">
  <InlineNotification type="info">
    <p>
      If enabled, the line item price for each order will be sourced from Stock2Shop, overriding the
      price on the original order. This is useful for drop-ship channels that sell your products at
      a different price than the one you want to invoice.
    </p>
  </InlineNotification>

  <div class="max-w-max">
    <Toggle
      bind:checked={workingCopy.meta[ORDER_SET_SYSTEM_PRICE].value}
      type="string"
      label="Use price from Stock2Shop?"
      noLabelBorder
    />
  </div>
  {#if workingCopy.meta[ORDER_SET_SYSTEM_PRICE].value === 'true'}
    <div class="max-w-max">
      <DynamicFieldSelector
        bind:value={workingCopy.meta[ORDER_USE_SYSTEM_PRICE_TIER].value}
        entity="product"
        group="price"
        title="Price Tier"
        sideLabel
        placeholder="Default"
        clearValue=""
        canClear
      />
    </div>
    <div class="max-w-max">
      <DynamicFieldSelector
        bind:value={workingCopy.meta[ORDER_USE_SYSTEM_PRICE_TIER_INCL].value}
        entity="product"
        group="price"
        title="Price Tier (Tax incl)"
        sideLabel
        placeholder="Default"
        clearValue=""
        canClear
      />
    </div>
  {/if}
</PageCard>
