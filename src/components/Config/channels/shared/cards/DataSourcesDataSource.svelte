<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import Button from '@/components/Controls/Button.svelte';
  import { confirmModal } from '@/components/Modal/modal';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { Source } from '@/models/config/config';
  import {
    createPostChannelSourceMutation,
    createUpdateChannelSourceMutation
  } from '@/mutations/channel-sources.mutation';
  import { createPollChannelSourceQuery } from '@/queries/channel-sources.query';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';

  import { isNumber } from '@/utils/typeguards';
  import capitalize from 'lodash.capitalize';

  export let workingCopy: ChannelWorkingCopy;
  export let source: Source;

  const updateChannelSourceMutation = createUpdateChannelSourceMutation({
    onSuccess: async () => {
      $channelSourcesQuery.refetch();
    }
  });

  const createChannelSourceMutation = createPostChannelSourceMutation({
    onSuccess: async () => {
      $channelSourcesQuery.refetch();
    }
  });

  $: channelSourcesQuery = createPollChannelSourceQuery({
    channelId: workingCopy.id,
    sourceId: source.id,
    enabled: isNumber(workingCopy.id)
  });

  $: channelSource = $channelSourcesQuery?.data?.find(
    (channelSource) => channelSource.source_id === source.id
  );

  $: channelSourceDescription = source.description;

  $: isActive = !!channelSource?.active;
  $: toggleVerb = isActive ? 'deactivate' : 'activate';
  $: isSyncing = channelSource?.status === 'syncing';

  const toggleActive = async () => {
    if (
      await confirmModal({
        title: `${capitalize(toggleVerb)} Data Source`,
        description: `Are you sure you want to ${toggleVerb} this data source?`,
        actionText: capitalize(toggleVerb)
      })
    ) {
      if (channelSource) {
        $updateChannelSourceMutation.mutate({ ...channelSource, active: isActive ? 0 : 1 });
      } else {
        $createChannelSourceMutation.mutate({
          channel_id: workingCopy.id,
          source_id: source.id
        });
      }
    }
  };
</script>

<PageCard title="Data Source ({channelSourceDescription})">
  <InlineNotification type="info">
    <p>
      The system that supplies data (e.g., stock quantities, prices) to your channel through
      Stock2Shop.
    </p>
  </InlineNotification>
  <div>
    <Button
      size="large"
      disabled={$updateChannelSourceMutation.isPending ||
        $createChannelSourceMutation.isPending ||
        isSyncing}
      tooltip={isSyncing ? 'Channel is syncing' : undefined}
      on:click={toggleActive}
      variant="outline"
    >
      {capitalize(toggleVerb)}
    </Button>
  </div>
</PageCard>
