<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import Select from '@/components/Controls/Select/Select.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { createGroupFieldsQuery } from '@/queries/fields.query';

  export let workingCopy: ChannelWorkingCopy;

  $: fieldsData = createGroupFieldsQuery({
    entity: 'product',
    group: 'price'
  });

  const defaultOption = { label: 'Default', value: null };

  $: options = [
    defaultOption,
    ...($fieldsData.data?.fields.map((f) => {
      const fieldWithoutPrefix = f.name.replace(`price_`, '');
      return { label: fieldWithoutPrefix, value: fieldWithoutPrefix };
    }) ?? [])
  ];
</script>

<PageCard title="Set Channel Price">
  <InlineNotification type="info">
    <p>Select which price list (tier) should sync with this channel.</p>
  </InlineNotification>
  <div class="max-w-96">
    <Select
      label="Channel price"
      sideLabel
      optionsLoading={$fieldsData.isLoading}
      bind:value={workingCopy.price_tier}
      {options}
    />
  </div>
</PageCard>
