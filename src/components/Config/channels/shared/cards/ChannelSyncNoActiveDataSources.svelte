<script lang="ts">
  import { page } from '$app/stores';
  import PageCard from '@/components/Config/PageCard.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';

  export let id: number;
</script>

<PageCard title="No Active Data Sources">
  <InlineNotification type="error">
    No active data sources found for this channel. Enable one <a
      class="font-bold"
      href="/config/{$page.params.kind}/{$page.params.type}/{id}/data-sources">here</a
    >.
  </InlineNotification>
</PageCard>
