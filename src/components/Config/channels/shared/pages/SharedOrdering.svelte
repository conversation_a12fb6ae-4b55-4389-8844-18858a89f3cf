<script lang="ts">
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import OrderFromDate from '@/components/Config/shared/cards/OrderFromDate.svelte';
  import DefaultCustomerCode from '@/components/Config/channels/shared/cards/OrderingDefaultCustomerCode.svelte';
  import UsePriceFromS2S from '@/components/Config/channels/shared/cards/OrderingUsePriceFromS2S.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';

  export let workingCopy: ChannelWorkingCopy;
</script>

<!-- @component
 Config page for ordering on channels where we don't need to configure anything specific.
-->

<PageWrapper>
  <OrderFromDate bind:workingCopy />
  <DefaultCustomerCode bind:workingCopy />
  <UsePriceFromS2S bind:workingCopy />
</PageWrapper>
