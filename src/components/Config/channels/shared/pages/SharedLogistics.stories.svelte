<script context="module" lang="ts">
  import FulfillmentsPage from '@/components/Config/channels/shared/pages/SharedLogistics.svelte';
  import { Story, Template } from '@storybook/addon-svelte-csf';

  export const meta = {
    component: FulfillmentsPage
  };
</script>

<script lang="ts">
  import StorybookWrapper from '@/components/StorybookWrapper.svelte';

  const baseWorkingCopy = {
    id: 999,
    created: '2023-02-17 12:06:12.000000',
    description: 'NOT A REAL CHANNEL',
    modified: '2023-02-17 13:10:48.000000',
    client_id: 999,
    active: 1,
    type: 'TO_BE_DECIDED',
    price_tier: '',
    qty_availability: null,
    sync_token: '0'
  };
</script>

<!-- See FulfillOrderComponent story to see additional states,
  since the fulfillmentServices list is defined in a mock repo 
  we cant mock additional states at this level  -->

<Template let:args>
  <StorybookWrapper>
    <FulfillmentsPage
      workingCopy={{
        ...baseWorkingCopy,
        isChannel: true,
        description: 'My Channel',
        meta: args.meta
      }}
    />
  </StorybookWrapper>
</Template>

<Story
  name="None set"
  args={{
    meta: {}
  }}
/>

<Story
  name="Set"
  args={{
    meta: {
      queue_fulfill_order: {
        key: 'queue_fulfill_order',
        value: 'true'
      },
      default_fulfillmentservice_id: {
        key: 'default_fulfillmentservice_id',
        value: '180'
      }
    }
  }}
/>
