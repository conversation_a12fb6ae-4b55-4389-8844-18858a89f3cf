<script lang="ts">
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import StockCalculator from '@/components/Config/channels/shared/cards/InventoryStockCalculator.svelte';
  import StoreWideQty from '@/components/Config/channels/shared/cards/StoreWideQty.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import InventoryStock from '@/components/Config/shared/cards/InventoryStock.svelte';
  import { QTY_LIMIT_UPPER } from '@/components/Config/constants';

  export let workingCopy: ChannelWorkingCopy;
</script>

<PageWrapper>
  <StockCalculator bind:workingCopy />
  <StoreWideQty name="Quantity Upper Limit" metaKey={QTY_LIMIT_UPPER} bind:workingCopy>
    <p>
      Set the upper limit for the channel quantity. This will also restrict the quantity available
      for ordering.
    </p>
    <br />
  </StoreWideQty>
  <InventoryStock bind:workingCopy />
</PageWrapper>
