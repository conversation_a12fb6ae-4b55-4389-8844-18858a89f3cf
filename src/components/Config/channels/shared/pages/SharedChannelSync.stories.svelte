<script context="module" lang="ts">
  import SharedChannelSync from '@/components/Config/channels/shared/pages/SharedChannelSync.svelte';
  import { Story, Template } from '@storybook/addon-svelte-csf';

  export const meta = {
    component: SharedChannelSync
  };
</script>

<script lang="ts">
  import StorybookWrapper from '@/components/StorybookWrapper.svelte';

  const baseWorkingCopy = {
    id: 999,
    created: '2023-02-17 12:06:12.000000',
    description: 'NOT A REAL CHANNEL',
    modified: '2023-02-17 13:10:48.000000',
    client_id: 999,
    active: 1,
    type: 'TO_BE_DECIDED',
    price_tier: '',
    qty_availability: null,
    sync_token: '0'
  };
</script>

<Template let:args>
  <StorybookWrapper>
    <!-- See mock channel-sources-repo for various states -->
    <SharedChannelSync
      workingCopy={{
        ...baseWorkingCopy,
        isChannel: true,
        id: 57,
        meta: args.meta ?? {}
      }}
    />
  </StorybookWrapper>
</Template>

<Story name="Various" />
