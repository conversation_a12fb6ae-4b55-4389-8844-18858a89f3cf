<script context="module" lang="ts">
  import SharedInventory from '@/components/Config/channels/shared/pages/SharedInventory.svelte';
  import { Story, Template } from '@storybook/addon-svelte-csf';

  export const meta = {
    component: SharedInventory
  };
</script>

<script lang="ts">
  const baseWorkingCopy = {
    id: 999,
    created: '2023-02-17 12:06:12.000000',
    description: 'NOT A REAL CHANNEL',
    modified: '2023-02-17 13:10:48.000000',
    client_id: 999,
    active: 1,
    type: 'TO_BE_DECIDED',
    price_tier: '',
    qty_availability: null,
    sync_token: '0'
  };
</script>

<Template let:args>
  <SharedInventory
    workingCopy={{
      ...baseWorkingCopy,
      isChannel: true,
      qty_availability: args.qty_availability,
      meta: args.meta
    }}
  />
</Template>

<Story
  name="None set"
  args={{
    qty_availability: undefined,
    meta: {}
  }}
/>

<Story
  name="Qty availability set & no limit"
  args={{
    qty_availability: 'Durban+Cape Town',
    meta: {}
  }}
/>

<Story
  name="Qty upper limit set & limit set"
  args={{
    qty_availability: 'Cape Town',
    meta: {
      qty_limit_upper: {
        value: '20'
      }
    }
  }}
/>
