<script context="module" lang="ts">
  import SharedPricing from '@/components/Config/channels/shared/pages/SharedPricing.svelte';
  import { Story, Template } from '@storybook/addon-svelte-csf';

  export const meta = {
    component: SharedPricing
  };
</script>

<script lang="ts">
  const baseWorkingCopy = {
    id: 999,
    created: '2023-02-17 12:06:12.000000',
    description: 'NOT A REAL CHANNEL',
    modified: '2023-02-17 13:10:48.000000',
    client_id: 999,
    active: 1,
    type: 'TO_BE_DECIDED',
    price_tier: '',
    qty_availability: null,
    sync_token: '0'
  };
</script>

<Template let:args>
  <SharedPricing
    workingCopy={{
      ...baseWorkingCopy,
      isChannel: true,
      price_tier: args.price_tier,
      meta: {}
    }}
  />
</Template>

<Story
  name="None set"
  args={{
    price_tier: undefined
  }}
/>

<Story
  name="Default"
  args={{
    price_tier: null
  }}
/>

<Story
  name="Wholesale"
  args={{
    price_tier: 'Wholesale'
  }}
/>

<Story
  name="Retail"
  args={{
    price_tier: 'Retail'
  }}
/>
