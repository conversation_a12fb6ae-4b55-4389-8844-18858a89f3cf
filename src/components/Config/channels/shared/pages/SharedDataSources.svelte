<script lang="ts">
  import DataSource from '@/components/Config/channels/shared/cards/DataSourcesDataSource.svelte';
  import NoChannelSources from '@/components/Config/channels/shared/cards/DataSourcesNoChannelSources.svelte';
  import PageCardSkeleton from '@/components/Config/PageCardSkeleton.svelte';
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import { createConfigQuery } from '@/queries/config.query';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';

  export let workingCopy: ChannelWorkingCopy;

  const configQuery = createConfigQuery();
  /**
   * Only allow non-linked 'master' source
   */
  $: sources = Object.values($configQuery.data?.sources ?? {}).filter(
    (source) => !source?.source_id
  );
</script>

<PageWrapper>
  {#if $configQuery.isLoading}
    <PageCardSkeleton />
  {:else if sources?.length}
    {#each sources as source (source.id)}
      <DataSource bind:workingCopy {source} />
    {/each}
  {:else}
    <NoChannelSources clientName={$configQuery.data?.client_name} />
  {/if}
</PageWrapper>
