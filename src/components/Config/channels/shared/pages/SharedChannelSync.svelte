<script lang="ts">
  import SyncChannel from '@/components/Config/channels/shared/cards/ChannelSyncSyncChannel.svelte';
  import NoActiveDataSources from '@/components/Config/channels/shared/cards/ChannelSyncNoActiveDataSources.svelte';
  import PageCardSkeleton from '@/components/Config/PageCardSkeleton.svelte';
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import { createChannelSourceQuery } from '@/queries/channel-sources.query';
  import { createConfigQuery } from '@/queries/config.query';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';

  export let workingCopy: ChannelWorkingCopy;

  const configQuery = createConfigQuery();
  const channelSourcesQuery = createChannelSourceQuery({ channelId: workingCopy.id });

  $: activeChannelSources = $channelSourcesQuery?.data?.filter(
    (channelSource) => channelSource.active === 1
  );
</script>

<PageWrapper>
  {#if $channelSourcesQuery.isLoading || $configQuery.isLoading}
    <PageCardSkeleton />
  {:else if activeChannelSources?.length}
    {#each activeChannelSources as channelSource (channelSource.id)}
      <SyncChannel bind:workingCopy {channelSource} />
    {/each}
  {:else}
    <NoActiveDataSources id={workingCopy.id} />
  {/if}
</PageWrapper>
