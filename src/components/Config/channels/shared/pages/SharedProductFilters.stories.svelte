<script context="module" lang="ts">
  import SharedProductFilters from '@/components/Config/channels/shared/pages/SharedProductFilters.svelte';
  import { Story, Template } from '@storybook/addon-svelte-csf';

  export const meta = {
    component: SharedProductFilters
  };
</script>

<script lang="ts">
</script>

<Template let:args>
  <!-- See mock channel-sources-repo for various states -->
  <SharedProductFilters workingCopy={{ channel_id: 47, rules: args.rules, type: args.type }} />
</Template>

<Story name="No rules - trade" args={{ rules: [], type: 'trade' }} />
<Story name="No rules - none trade" args={{ rules: [], type: 'shopify' }} />
<Story
  name="Rules"
  args={{
    rules: [
      {
        key: 'product.source_product_code',
        operator: 'equal',
        value: '123',
        channel_id: 47
      },
      {
        key: 'variant.sku',
        operator: 'contains',
        value: 'PRODUCT',
        channel_id: 47
      },
      {
        key: 'variant.grams',
        operator: 'equal',
        value: '3',
        channel_id: 47
      },
      {
        key: 'product.meta_width_cm',
        operator: 'equal',
        value: '1',
        channel_id: 47
      },
      {
        key: 'product.title',
        operator: 'contains',
        value: 'new',
        channel_id: 47
      },
      {
        key: 'product.tags',
        operator: 'lookup',
        value: 'Limited Edition,New',
        channel_id: 47
      }
    ],
    type: 'trade'
  }}
/>
