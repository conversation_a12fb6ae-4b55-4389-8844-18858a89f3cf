<script lang="ts">
  import type { TableItem } from '@/components/Config/shared/components/ConfigTable.svelte';
  import Chip from '@/components/Controls/Tag/Chip.svelte';
  import {
    PAYMENT_METHOD_OPTIONS,
    type PaymentMethod
  } from '@/models/config/channels/trade/payment/methods';

  export let listItem: TableItem<PaymentMethod>;
</script>

<div class="flex items-center gap-2">
  <span>{listItem.key}</span>
  <Chip type="info" label={PAYMENT_METHOD_OPTIONS[listItem.method]} />
</div>
