<script lang="ts">
  import {
    CHECKOUT_FIELD_TYPE_OPTIONS,
    type CheckoutField,
    type CheckoutFieldTypeAttributes,
    type RadioOrDropDownCheckoutField
  } from '@/components/Config/channels/trade/cards/checkoutFields';
  import AddOrEditCheckoutFieldModal from '@/components/Config/channels/trade/components/AddOrEditCheckoutFieldModal.svelte';
  import Button from '@/components/Controls/Button.svelte';
  import Select from '@/components/Controls/Select/Select.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import { createEventDispatcher } from 'svelte';

  const dispatch = createEventDispatcher();

  const BASE_CHECKOUT_FIELDS: CheckoutField = {
    key: '',
    description: '',
    type: '',
    required: false
  };

  export let allKeys: string[] = [];

  let type: CheckoutFieldTypeAttributes = '';
  let newField: Partial<RadioOrDropDownCheckoutField> = {};
  let open = false;

  $: selectedOption = CHECKOUT_FIELD_TYPE_OPTIONS.find((option) => option.value === type);
  $: newField = open === false ? { ...BASE_CHECKOUT_FIELDS } : newField;

  function openAddFieldModal() {
    newField = { ...BASE_CHECKOUT_FIELDS, type };
    open = true;
  }

  function addField() {
    dispatch('addField', newField);
    open = false;
  }
</script>

<InlineNotification type="info">
  <p>
    List of fields to display at checkout. You can use this to collect additional custom information
    on the checkout page.
  </p>
</InlineNotification>

<div class="flex flex-1 gap-4">
  <div class="flex-1">
    <Select
      placeholder="Select an input type"
      bind:value={type}
      options={CHECKOUT_FIELD_TYPE_OPTIONS}
    />
  </div>
  <Button on:click={() => openAddFieldModal()} disabled={type === ''} size="large">Add</Button>
</div>

{#if open}
  <AddOrEditCheckoutFieldModal
    bind:open
    {type}
    {allKeys}
    bind:field={newField}
    label={selectedOption?.label}
    {addField}
  />
{/if}
