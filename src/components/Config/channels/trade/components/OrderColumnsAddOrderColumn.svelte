<script lang="ts">
  import type { TableItem } from '@/components/Config/shared/components/ConfigTable.svelte';
  import type { OrderColumnItem } from '@/components/Config/channels/trade/cards/OrderingOrderColumns.svelte';
  import Button from '@/components/Controls/Button.svelte';
  import Select from '@/components/Controls/Select/Select.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import { ORDER_COLUMNS } from '@/models/config/channels/trade/ordering/columns';

  export let onAdd: (item: TableItem<OrderColumnItem>) => void;

  let label = '';
  let key: string | undefined;

  const handleAdd = () => {
    if (!key) return;
    onAdd({ key, label });
    key = undefined;
    label = '';
  };

  const options = ORDER_COLUMNS.map((column) => ({
    label: column.label,
    value: column.value
  }));
</script>

<!-- @component
 Used for OrderColumns and OrderViewDisplay
 -->

<div class="flex w-full flex-row gap-2">
  <div class="w-2/4">
    <TextInput placeholder="Label" bind:value={label} />
  </div>
  <div class="w-3/4">
    <Select {options} bind:value={key} />
  </div>

  <Button variant="gray" disabled={!key?.length || !label?.length} on:click={handleAdd} size="large"
    >Add</Button
  >
</div>
