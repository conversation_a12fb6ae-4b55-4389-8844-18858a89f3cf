<script lang="ts">
  import type {
    DropdownRadioOption,
    RadioOrDropDownCheckoutField
  } from '@/components/Config/channels/trade/cards/checkoutFields';
  import Button from '@/components/Controls/Button.svelte';
  import FormInputGroup from '@/components/Controls/Group/FormInputGroup.svelte';
  import Select, { type ListOption } from '@/components/Controls/Select/Select.svelte';
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import IconDelete from '@/components/Icons/IconDelete.svelte';
  import { onMount } from 'svelte';

  export let field: Partial<RadioOrDropDownCheckoutField>;
  export let isEditing: boolean = false;
  export let type: 'radio' | 'dropdown';

  let newOption = { value: '', key: '' };

  $: fieldOptionsAsDropdownOptions = field.options?.map(({ key, value }) => ({
    label: key,
    value
  })) as ListOption[];

  function removeOption(key: string) {
    field.options = field.options?.filter((option) => option.key !== key);
  }

  function addOption() {
    field.options = [...(field.options as DropdownRadioOption[]), newOption as DropdownRadioOption];
    newOption = { value: '', key: '' };
  }

  onMount(() => {
    if (!isEditing) {
      field.type = type;
    }
    if (!field.options) {
      field.options = [];
    }
  });
</script>

<FormInputGroup>
  <TextInput required label="Key" isDisable={isEditing} bind:value={field.key} />
  <TextInput required label="Description" bind:value={field.description} />
  {#if field.required !== undefined}
    <Toggle label="Required" bind:checked={field.required} />
  {/if}
  <!-- Add option form goes here. -->
  <div>
    <p class="text pb-2 text-neutral-500">Add option</p>
    <div class="flex gap-4">
      <!-- 
        This will confuse you...
        Notice that:
          [a] the input with the placeholder of key is bound to newOption.value
          [b] the input with placeholder of Label is bound to newOption.key
        I hate it too, but it's intentional...
      -->
      <TextInput placeholder="Key" bind:value={newOption.value} />
      <TextInput placeholder="Label" bind:value={newOption.key} />
      <Button
        type="button"
        size="large"
        on:click={addOption}
        disabled={newOption.key === '' || newOption.value === ''}>Add</Button
      >
    </div>
  </div>
  {#if field.options && field?.options.length > 0}
    <div>
      <div
        class="grid grid-cols-[1fr_1fr_24px] items-center gap-4 border-b border-neutral-200 p-2 text-neutral-500"
      >
        <p>Key:</p>
        <p>Label:</p>
      </div>
      {#each field?.options as option}
        <div
          class="grid grid-cols-[1fr_1fr_24px] items-center gap-4 border-b border-neutral-200 p-2 text-neutral-500"
        >
          <p>{option.value}</p>
          <p>{option.key}</p>
          <Button
            icon={IconDelete}
            variant="gray"
            size="small"
            on:click={() => removeOption(option.key || '')}
          />
        </div>
      {/each}
    </div>
    <Select
      label="Default option"
      placeholder="Pick a default selected option"
      options={fieldOptionsAsDropdownOptions}
      bind:value={field.value}
    />
  {/if}
</FormInputGroup>
