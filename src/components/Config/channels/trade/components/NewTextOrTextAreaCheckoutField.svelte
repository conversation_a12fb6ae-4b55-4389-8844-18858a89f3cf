<script lang="ts">
  import type { TextOrTextAreaCheckoutField } from '@/components/Config/channels/trade/cards/checkoutFields';
  import FormInputGroup from '@/components/Controls/Group/FormInputGroup.svelte';
  import NumberInput from '@/components/Controls/NumberInput.svelte';
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import { onMount } from 'svelte';

  export let field: Partial<TextOrTextAreaCheckoutField>;
  export let isEditing: boolean = false;
  export let type: 'text' | 'textarea';

  onMount(() => {
    if (!isEditing) {
      field.type = type;
      field.min_chars = 10;
      field.max_chars = 20;
      field.value = '';
    }
  });
</script>

<FormInputGroup>
  <TextInput label="Key" isDisable={isEditing} bind:value={field.key} />
  <TextInput label="Description" bind:value={field.description} />
  {#if field.required !== undefined}
    <Toggle label="Required" bind:checked={field.required} />
  {/if}
  <TextInput label="Default Value" bind:value={field.value} />
  <InlineNotification type="info">
    <p>You can specify a minimum and maximum number of characters for this field.</p>
  </InlineNotification>
  <NumberInput label="Minimum characters" bind:value={field.min_chars} bind:max={field.max_chars} />
  <NumberInput label="Maximum characters" bind:value={field.max_chars} bind:min={field.min_chars} />
  {#if field.value?.length && field.max_chars && field.value?.length > field.max_chars}
    <InlineNotification type="warning">
      <p>
        The length of the default value for this checkout field exceeds the maximum character length
        that you've specified.
      </p>
    </InlineNotification>
  {/if}
</FormInputGroup>
