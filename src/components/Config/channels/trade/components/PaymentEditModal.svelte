<script lang="ts">
  import TextInput from '@/components/Controls/TextInput.svelte';
  import IconPayment from '@/components/Icons/IconPayment.svelte';
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';
  import PromptModal from '@/components/Modal/PromptModal.svelte';
  import InputWrapper from '@/components/Controls/Group/InputWrapper.svelte';
  import ModalInputGroup from '@/components/Controls/Group/ModalInputGroup.svelte';
  import type { PaymentMethod } from '@/models/config/channels/trade/payment/methods';

  export let open = false;
  /**
   * Payment Method to edit, dont bind.
   *
   * Take the edited method from the onSave callback
   */
  export let paymentMethod: PaymentMethod;
  export let onSave: (methodToEdit: PaymentMethod) => void;
</script>

<PromptModal
  bind:open
  title="Edit {paymentMethod.method}"
  actionText="Save"
  icon={IconPayment}
  actionClick={() => onSave(paymentMethod)}
>
  <InputWrapper>
    <ModalInputGroup hideSeparator>
      {#if paymentMethod.method == 'OnAccount'}
        <TextInput type="text" label="Description" bind:value={paymentMethod.description} />
      {/if}

      {#if paymentMethod.method == 'AdumoVirtual'}
        <TextInput type="text" label="Description" bind:value={paymentMethod.description} />
        <TextInput type="text" label="ISS" bind:value={paymentMethod.iss} />
        <TextInput type="text" label="AUID" bind:value={paymentMethod.auid} />
        <TextInput type="text" label="CUID" bind:value={paymentMethod.cuid} />
        <TextInput type="text" label="JWT secret" bind:value={paymentMethod.jwt_secret} />
        <TextInput type="text" label="URL Staging" bind:value={paymentMethod.url_staging} />
        <TextInput type="text" label="URL Production" bind:value={paymentMethod.url_production} />
        <Toggle label="In Production" bind:checked={paymentMethod.in_production} />
        <TextInput type="text" label="Currency" bind:value={paymentMethod.currency} />
      {/if}

      {#if paymentMethod.method == 'Stripe'}
        <TextInput type="text" label="Description" bind:value={paymentMethod.description} />
        <TextInput type="text" label="API PK" bind:value={paymentMethod.api_pk} />
        <TextInput type="text" label="API SK" bind:value={paymentMethod.api_sk} />
        <Toggle label="Test Mode" bind:checked={paymentMethod.test_mode} />
        <TextInput type="text" label="Currency" bind:value={paymentMethod.currency} />
        <TextInput
          type="text"
          label="Currency Display"
          bind:value={paymentMethod.currency_display}
        />
      {/if}

      {#if paymentMethod.method == 'MygateVirtual'}
        <TextInput type="text" label="Description" bind:value={paymentMethod.description} />
        <TextInput type="text" label="URL" bind:value={paymentMethod.url} />
        <TextInput type="text" label="Merchant ID" bind:value={paymentMethod.merchant_id} />
        <TextInput type="text" label="Application ID" bind:value={paymentMethod.application_id} />
        <TextInput type="text" label="Mode" bind:value={paymentMethod.mode} />
      {/if}
      <div class="mb-2" />
    </ModalInputGroup>
  </InputWrapper>
</PromptModal>
