<script lang="ts">
  import Button from '@/components/Controls/Button.svelte';
  import PaymentMethodSelector from '@/components/Controls/Select/PaymentMethodSelector.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import {
    generatePaymentMethod,
    type PaymentMethod,
    type PaymentMethodOption
  } from '@/models/config/channels/trade/payment/methods';

  export let onAdd: (paymentMethod: PaymentMethod) => void;

  const handleAdd = () => {
    if (!description || !methodOption) return;
    const newPm = generatePaymentMethod(methodOption, description);
    onAdd(newPm);
    description = '';
    methodOption = undefined;
  };

  let description: string = '';
  let methodOption: PaymentMethodOption | undefined = undefined;
</script>

<div class="grid grid-cols-[1fr_1fr_auto] gap-2">
  <TextInput bind:value={description} placeholder="Description" />
  <PaymentMethodSelector bind:method={methodOption} />
  <Button disabled={!description || !methodOption} on:click={handleAdd} size="large">Add</Button>
</div>
