<script lang="ts">
  import FacetedNavigationFieldHandler, {
    type Field
  } from '@/components/Config/channels/trade/utils/facidedNavigationFieldHandler';
  import Button from '@/components/Controls/Button.svelte';
  import Select from '@/components/Controls/Select/Select.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import {
    generateNestedFacetedNavigationAggregation,
    generateSimpleFacetedNavigationAggregation,
    type NestedFacetedNavigationAggregation,
    type SimpleFacetedNavigationAggregation
  } from '@/models/config/channels/trade/navigation/aggregations';
  import { onMount } from 'svelte';

  export let onAdd: (
    label: string,
    agg: SimpleFacetedNavigationAggregation | NestedFacetedNavigationAggregation
  ) => void;

  const facetedNavigationFieldHandler = new FacetedNavigationFieldHandler();

  const handleAdd = () => {
    const dynamicPrefix = 'meta_';
    const isDynamic = key.startsWith(dynamicPrefix);
    if (isDynamic) {
      onAdd(
        label,
        generateNestedFacetedNavigationAggregation(label, key.substring(dynamicPrefix.length))
      );
    } else {
      onAdd(label, generateSimpleFacetedNavigationAggregation(key));
    }
    label = '';
  };

  let label = '';
  let key = '';
  let allFields: Field[];
  let options = [];

  $: options = allFields ? allFields.map(({ name, label }) => ({ value: name, label })) : [];

  onMount(async () => (allFields = await facetedNavigationFieldHandler.fetchAggregatedFields()));
</script>

<div class="grid w-full grid-cols-[2fr_3fr_auto] gap-2">
  <TextInput placeholder="Label" bind:value={label} />
  {#if options}
    <Select {options} bind:value={key} />
  {/if}
  <Button variant="gray" disabled={!key?.length || !label?.length} on:click={handleAdd} size="large"
    >Add</Button
  >
</div>
