<script lang="ts">
  import type { TableItem } from '@/components/Config/shared/components/ConfigTable.svelte';
  import type { OrderColumnItem } from '@/components/Config/channels/trade/cards/OrderingOrderColumns.svelte';
  import Button from '@/components/Controls/Button.svelte';
  import FieldSelect from '@/components/Controls/Select/FieldSelector/FieldSelect.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import { quickOrderColumnsFieldsConvertor } from '@/models/fields/fieldConvertors';

  export let onAdd: (item: TableItem<OrderColumnItem>) => void;

  let fieldSelectComponent: FieldSelect;

  const handleAdd = () => {
    onAdd({ key, label });
    fieldSelectComponent.clear();
    label = '';
  };

  let label = '';
  let key: string;
</script>

<div class="flex w-full flex-row gap-2">
  <div class="w-2/4">
    <TextInput placeholder="Label" bind:value={label} />
  </div>
  <div class="w-3/4">
    <FieldSelect
      bind:value={key}
      convertor={quickOrderColumnsFieldsConvertor}
      bind:this={fieldSelectComponent}
      entity="product"
    />
  </div>

  <Button variant="gray" disabled={!key?.length || !label?.length} on:click={handleAdd} size="large"
    >Add</Button
  >
</div>
