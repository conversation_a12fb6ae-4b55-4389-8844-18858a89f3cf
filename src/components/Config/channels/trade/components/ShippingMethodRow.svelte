<script lang="ts">
  import type { FormattedMethodObject } from '@/components/Config/channels/trade/cards/LogisticsShippingMethods.svelte';
  import ShippingMethodEditModal from '@/components/Connectors/ConfigItems/Channel/Forms/ShippingMethodEditModal.svelte';
  import Button from '@/components/Controls/Button.svelte';
  import Select from '@/components/Controls/Select/Select.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import IconDelete from '@/components/Icons/IconDelete.svelte';
  import IconEdit from '@/components/Icons/IconEdit.svelte';

  const methodOptions = [
    { value: 'flat_rate', label: 'Flat Rate' },
    { value: 'table_rate', label: 'Table Rate' }
  ];

  export let shippingMethod: FormattedMethodObject = {
    name: '',
    method: 'flat_rate',
    price: 0,
    tax_lines: [],
    rates: []
  };

  export let removeMethod: (name: string) => void;

  let editMethodModalOpen = false;

  function editMethod() {
    editMethodModalOpen = true;
  }
</script>

{#if editMethodModalOpen}
  <ShippingMethodEditModal bind:open={editMethodModalOpen} bind:shippingMethod />
{/if}

<div class="grid grid-cols-[1fr_1fr_auto_auto] items-center gap-4 border-b py-2">
  <TextInput bind:value={shippingMethod.name} placeholder="Shipping method name" />
  <Select bind:value={shippingMethod.method} options={methodOptions} />
  <Button
    variant="gray"
    icon={IconDelete}
    size="small"
    on:click={() => removeMethod(shippingMethod.name)}
  />
  <Button variant="gray" icon={IconEdit} size="small" on:click={editMethod} />
</div>
