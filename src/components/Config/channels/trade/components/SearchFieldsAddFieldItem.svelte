<script lang="ts">
  import Button from '@/components/Controls/Button.svelte';
  import WeightSelector from '@/components/Controls/Select/WeightSelector.svelte';
  import type { Field } from '@/models/fields/fields';
  import Select from '@/components/Controls/Select/Select.svelte';
  import { ELASTIC_SEARCH_INDEX_FIELDS } from '@/components/Config/channels/trade/utils/elasticFieldHandler';

  // PROPS
  export let onAdd: (field: { key: string; value: string }, weight?: number) => void;
  export let additionalFields: Field[] = [];

  // DROPDOWN OPTIONS
  let options: any = [
    ...ELASTIC_SEARCH_INDEX_FIELDS.map(({ key, label }) => ({ key, value: label })),
    ...additionalFields.map(({ name, label }) => ({ key: name, value: label }))
  ];

  const handleAdd = () => {
    const selectedOption = options.find(
      (option: { value: string }) => option.value === selectedField
    );
    onAdd(selectedOption, weight);
    weight = undefined;
    selectedField = '';
  };

  let selectedField = '';
  let weight: number | undefined;
</script>

<div class="grid w-full grid-cols-[2fr_3fr_auto] gap-2">
  <Select bind:value={selectedField} {options} />
  <WeightSelector bind:weight />
  <Button disabled={!selectedField || weight === undefined} on:click={handleAdd} size="large"
    >Add</Button
  >
</div>
