<script lang="ts">
  import type { TableItem } from '@/components/Config/shared/components/ConfigTable.svelte';
  import type { FacetedNavItem } from '@/components/Config/channels/trade/cards/NavigationProductFacetedNavigation.svelte';
  import Chip from '@/components/Controls/Tag/Chip.svelte';

  export let listItem: TableItem<FacetedNavItem>;
</script>

<div class="flex items-center gap-2">
  <span>{listItem.label}</span>
  <span class="font-bold text-brand-notification">{listItem.field}</span>
  {#if listItem.chip}
    <Chip label="Meta" type="info" />
  {/if}
</div>
