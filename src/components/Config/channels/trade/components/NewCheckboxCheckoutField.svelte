<script lang="ts">
  import type { CheckboxCheckoutField } from '@/components/Config/channels/trade/cards/checkoutFields';
  import FormInputGroup from '@/components/Controls/Group/FormInputGroup.svelte';
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import { onMount } from 'svelte';

  export let field: Partial<CheckboxCheckoutField>;
  export let isEditing: boolean = false;

  const type = 'checkbox';

  onMount(() => {
    if (!isEditing) {
      field.type = type;
      field.checked_by_default = false;
    }
  });
</script>

<FormInputGroup>
  <TextInput label="Key" isDisable={isEditing} bind:value={field.key} />
  <TextInput label="Description" bind:value={field.description} />
  {#if field.required !== undefined}
    <Toggle label="Required" bind:checked={field.required} />
  {/if}
  {#if field.checked_by_default !== undefined}
    <Toggle label="Checked by default" bind:checked={field.checked_by_default} />
  {/if}
</FormInputGroup>
