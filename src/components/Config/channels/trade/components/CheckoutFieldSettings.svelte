<script lang="ts">
  import NumberInput from '@/components/Controls/NumberInput.svelte';
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';

  import { onMount } from 'svelte';
  import Button from '@/components/Controls/Button.svelte';
  import Select, { type ListOption } from '@/components/Controls/Select/Select.svelte';
  import type {
    CheckboxCheckoutField,
    CheckoutFieldTypeAttributes,
    DateCheckoutField,
    DropdownRadioOption,
    RadioOrDropDownCheckoutField,
    TextOrTextAreaCheckoutField
  } from '@/components/Config/channels/trade/cards/checkoutFields';

  export let newField:
    | RadioOrDropDownCheckoutField
    | TextOrTextAreaCheckoutField
    | CheckboxCheckoutField
    | DateCheckoutField;
  export let allKeys: string[] = [];
  export let isEditing: boolean;
  export let type: CheckoutFieldTypeAttributes;

  let newOption: { key: string; value: string } = { value: '', key: '' };
  let addOptionsValidationErrors = '';
  let keyIsDisabled = typeof newField.key === 'string' && newField.key.length > 0;
  let minChars = newField.min_chars as number;
  let maxChars = newField.max_chars as number;

  $: newField.min_chars = minChars;
  $: newField.max_chars = maxChars;

  $: newFieldOptionsAsDropdownOptions = newField.options?.map(({ key, value }) => ({
    label: key,
    value
  })) as ListOption[];

  onMount(() => {
    newField.required !== true ? (newField.required = false) : (newField.required = true);
    newField.type = type;
    if (type === 'radio' || type === 'dropdown') {
      newField.options = newField.options ? newField.options : [];
      newField.value = '';
    }
  });

  function addOption() {
    if (!validateNewOption()) return;
    newField.options = [
      ...(newField.options as DropdownRadioOption[]),
      newOption as DropdownRadioOption
    ];
    newOption = { value: '', key: '' };
  }

  function removeOption(key: string) {
    if (key === '') return;
    newField.options = [
      ...(newField.options as DropdownRadioOption[]).filter((option) => option.key !== key)
    ];
  }

  function validateNewOption() {
    addOptionsValidationErrors = '';
    if (newField.options?.findIndex((option) => option.key === newOption.key) === -1) return true;
    addOptionsValidationErrors = 'Key already exists';
    return false;
  }
</script>

<div class="space-y-4">
  <TextInput label="Key" required isDisable={keyIsDisabled} bind:value={newField.key} />
  {#if allKeys.includes(newField.key) && !isEditing}
    <p class="text-brand-warning">This key already exists</p>
  {/if}
  <TextInput bind:value={newField.description} label="Description" required />
  <Toggle label="Required" bind:checked={newField.required} />
  {#if type === 'text' || type === 'textarea'}
    <TextInput bind:value={newField.default} label="Default" />
    <InlineNotification type="info">
      <div>Specify a Minimum and Maximum length, if required</div>
    </InlineNotification>
    <NumberInput bind:value={minChars} hideSpinner label="Min" />
    <NumberInput bind:value={maxChars} hideSpinner label="Max" />
  {:else if type === 'checkbox' && typeof newField.checked_by_default === 'boolean'}
    <Toggle label="Checked by default" bind:checked={newField.checked_by_default} />
  {:else if type === 'date' && typeof newField.valid_after_days === 'string'}
    <TextInput bind:value={newField.valid_after_days} label="Days to pass from current date" />
  {:else if type === 'radio' || type === 'dropdown'}
    <p>Options:</p>
    {#if newField.options && newField?.options.length > 0}
      <div class="grid grid-cols-3 items-center gap-4">
        <p>Option key:</p>
        <p>Option label:</p>
      </div>
      {#each newField?.options as option}
        <div class="grid grid-cols-3 items-center gap-4">
          <p>{option.value}</p>
          <p>{option.key}</p>
          <Button on:click={() => removeOption(option.key || '')}>Delete</Button>
        </div>
      {/each}
      <Select
        placeholder="Pick a default selected option"
        options={newFieldOptionsAsDropdownOptions}
        bind:value={newField.value}
      />
    {:else}
      <InlineNotification type="info">
        <p>Add option</p>
      </InlineNotification>
    {/if}

    <div>
      <p class="pb-2">Add option</p>
      <div class="flex gap-4">
        <!-- 

          This will confuse you...

          Notice that:
            [a] the input with the placeholder of key is bound to newOption.value
            [b] the input with placeholder of Label is bound to newOption.key
          
          I hate it too, but it's intentional...
        
        -->
        <TextInput placeholder="Key" bind:value={newOption.value} />
        <TextInput placeholder="Label" bind:value={newOption.key} />
        <Button
          type="button"
          size="large"
          on:click={addOption}
          disabled={newOption.key === '' || newOption.value === ''}>Add</Button
        >
      </div>
      {#if addOptionsValidationErrors}
        <p class="text-brand-warning">{addOptionsValidationErrors}</p>
      {/if}
    </div>
  {/if}
</div>
