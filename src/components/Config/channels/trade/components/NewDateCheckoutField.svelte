<script lang="ts">
  import type { DateCheckoutField } from '@/components/Config/channels/trade/cards/checkoutFields';
  import FormInputGroup from '@/components/Controls/Group/FormInputGroup.svelte';
  import NumberInput from '@/components/Controls/NumberInput.svelte';
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import { onMount } from 'svelte';

  export let field: Partial<DateCheckoutField>;
  export let isEditing: boolean = false;
  const type = 'date';

  onMount(() => {
    if (!isEditing) {
      field.type = type;
      field.valid_after_days = 0;
    }
  });
</script>

<FormInputGroup>
  <TextInput label="Key" isDisable={isEditing} bind:value={field.key} />
  <TextInput label="Description" bind:value={field.description} />
  {#if field.required !== undefined}
    <Toggle label="Required" bind:checked={field.required} />
  {/if}
  <InlineNotification type="info">
    <p>
      You can set the number of days that needs to pass between the date of the checkout and the the
      earliest date available for the user to select.
    </p>
  </InlineNotification>
  <NumberInput label="Valid after days" bind:value={field.valid_after_days} />
</FormInputGroup>
