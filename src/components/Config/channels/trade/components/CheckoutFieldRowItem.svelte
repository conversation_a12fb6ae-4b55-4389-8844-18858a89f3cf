<script lang="ts">
  import type { CheckoutField } from '@/components/Config/channels/trade/cards/checkoutFields';
  import Chip from '@/components/Controls/Tag/Chip.svelte';

  export let listItem: CheckoutField;
</script>

<div class="flex items-center gap-2">
  <!-- 
    Sometimes Items will have been set elsewhere, for example the old console.  
    In this case, there might have been no validation preventing them from adding items with out some required properties  
    So when we get them, they may be mission some properties. 
    In this case I've chosen to render a Chip asking them to set the property. 
  -->
  {#if !listItem.description}
    <Chip type="warning" label="Please set label" />
  {:else}
    <span class="font-bold text-neutral-500">{listItem.description}</span>
  {/if}

  {#if !listItem.type}
    <Chip type="warning" label="Please set type" />
  {:else}
    <span class="font-bold uppercase text-brand-notification">{listItem.type}</span>
  {/if}

  {#if listItem.required}
    <Chip type="info" label="Required" />
  {/if}
</div>
