<script lang="ts">
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import NewCheckboxCheckoutField from '@/components/Config/channels/trade/components/NewCheckboxCheckoutField.svelte';
  import NewDateCheckoutField from '@/components/Config/channels/trade/components/NewDateCheckoutField.svelte';
  import NewRadioOrDropdownCheckoutField from '@/components/Config/channels/trade/components/NewRadioOrDropdownCheckoutField.svelte';
  import NewTextOrTextAreaCheckoutField from '@/components/Config/channels/trade/components/NewTextOrTextAreaCheckoutField.svelte';
  import PromptModal from '@/components/Modal/PromptModal.svelte';
  import {
    type RadioOrDropDownCheckoutField,
    type CheckboxCheckoutField,
    type TextOrTextAreaCheckoutField,
    type DateCheckoutField,
    type CheckoutFieldTypeAttributes
  } from '@/components/Config/channels/trade/cards/checkoutFields';
  import { createEventDispatcher, onMount } from 'svelte';

  const dispatch = createEventDispatcher();

  export let open: boolean;
  export let label: string = '';
  export let addField: () => void = () => {};
  export let allKeys: string[] = [];
  export let type: CheckoutFieldTypeAttributes = '';
  export let isEditing: boolean = false;
  export let field: Partial<
    | RadioOrDropDownCheckoutField
    | TextOrTextAreaCheckoutField
    | CheckboxCheckoutField
    | DateCheckoutField
  >;

  function handleSubmit() {
    if (isEditing) return dispatch('edit', field);
    addField();
  }

  onMount(() => {
    type = isEditing ? (field.type as CheckoutFieldTypeAttributes) : type;
  });
</script>

<PromptModal
  bind:open
  title={`Add a ${label} input`}
  position="right"
  size="narrow"
  actionClick={handleSubmit}
  actionText={isEditing ? 'Save' : 'Add'}
  disabled={field.key === '' || allKeys.includes(field.key ?? '')}
>
  <div class="space-y-4">
    {#if type === 'radio' || type === 'dropdown'}
      <NewRadioOrDropdownCheckoutField bind:field {type} {isEditing} />
    {:else if type === 'checkbox'}
      <NewCheckboxCheckoutField bind:field {isEditing} />
    {:else if type === 'date'}
      <NewDateCheckoutField bind:field {isEditing} />
    {:else if type === 'text' || type === 'textarea'}
      <NewTextOrTextAreaCheckoutField bind:field {type} {isEditing} />
    {/if}
    {#if field.key && allKeys.includes(field.key)}
      <InlineNotification type="warning">
        <p>Key already exists</p>
      </InlineNotification>
    {/if}
  </div>
</PromptModal>
