import FieldsRepo from '@/repos/fields-repo';

const fieldsRepo = new FieldsRepo();

export type Field = {
  name: string;
  label: string;
  description: string;
  is_variant_field: boolean;
};

type DynamicFieldGroup = {
  group: string;
  label: string;
};

type Fields = {
  fields: Field[];
  dynamic_field_groups: DynamicFieldGroup[];
};

const FIELDS: Fields = {
  fields: [
    {
      name: 'collection',
      label: 'Collection',
      description: 'Product collection',
      is_variant_field: false
    },
    {
      name: 'product_type',
      label: 'Type',
      description: 'Product type',
      is_variant_field: false
    },
    {
      name: 'tags',
      label: 'Tags',
      description: 'List of keywords for the product',
      is_variant_field: false
    },
    {
      name: 'vendor',
      label: 'Vendor',
      description: 'Product Vendor or Brand',
      is_variant_field: false
    }
  ],
  dynamic_field_groups: [
    {
      group: 'meta',
      label: 'Custom Fields'
    }
    // {
    //   group: 'price',
    //   label: 'Pricing'
    // },
    // {
    //   group: 'qty',
    //   label: 'Warehouse Qty'
    // },
    // {
    //   group: 'option',
    //   label: 'Variant Options'
    // }
  ]
};

export default class FacetedNavigationFieldHandler {
  private async fetchFieldGroup(group: string): Promise<Field[]> {
    const data = await fieldsRepo.getGroupFields('product', group);
    return data.fields as Field[];
  }

  private async fetchDynamicFieldGroups(): Promise<Field[]> {
    const [CustomFields /*Pricing , WarehouseQty, Variant Options*/] = await Promise.all(
      FIELDS.dynamic_field_groups.map(async (dynamicFieldGroup) => {
        return this.fetchFieldGroup(dynamicFieldGroup.group);
      })
    );
    return [...CustomFields];
  }

  async fetchAggregatedFields(): Promise<Field[]> {
    // If for some reason the API request to get the dynamic fields fail, then just return the static fields
    try {
      const dynamicFields = await this.fetchDynamicFieldGroups();
      return [...FIELDS.fields, ...dynamicFields];
    } catch (err) {
      // Perhaps we should log this error to let the user know that not all fields are available ???
      return [...FIELDS.fields];
    }
  }
}
