/*

  This function is a helper, used to convert a string which represents an ElasticQuery field to a human readable string and vice versa.

  An example of an elastic query field string is: "tags.autocomplete^3"
  We convert this string to an object that looks like this: { field: "Tags", weight: "Important" } 
  which our UI will display like this "Tags Important" 

  to summarize: "tags.autocomplete^3" => { field: "Tags", weight: "Important" } => "Tags Important" and vice versa

*/

type ElasticQueryField = {
  key: string;
  label: string;
};

export type ElasticQueryFieldDisplayObject = {
  field: string;
  weight: string;
};

const WEIGHT_MAP: { [key: string]: string } = {
  undefined: 'Normal',
  '3': 'Important',
  '5': 'Very important'
};

const REVERSE_WEIGHT_MAP: { [key: string]: string } = {
  Normal: 'undefined',
  Important: '3',
  'Very important': '5'
};

export const ELASTIC_SEARCH_INDEX_FIELDS: ElasticQueryField[] = [
  {
    key: 'source_product_code',
    label: 'Source Product Code'
  },
  {
    key: 'title',
    label: 'Title'
  },
  {
    key: 'body_html',
    label: 'Body HTML'
  },
  {
    key: 'collection.english',
    label: 'Collection'
  },
  {
    key: 'product_type.english',
    label: 'Product Type'
  },
  {
    key: 'tags.autocomplete',
    label: 'Tags'
  },
  {
    key: 'vendor.english',
    label: 'Vendor'
  },
  {
    key: 'variants.source_variant_code',
    label: 'Source Variant Code'
  },
  {
    key: 'variant.sku',
    label: 'SKU'
  },
  {
    key: 'variants.barcode',
    label: 'Barcode'
  },
  {
    key: 'meta.value',
    label: 'Meta Value'
  }
];

export default class ElasticQueryFieldHandler {
  availableFields: ElasticQueryField[] = [];

  constructor() {
    this.availableFields = [...ELASTIC_SEARCH_INDEX_FIELDS];
  }

  convertStringToElasticQueryField(string: string): ElasticQueryFieldDisplayObject {
    const [key, weight] = string.split('^');
    const weightLabel = WEIGHT_MAP[weight] || 'Normal';
    const label = this.availableFields.find((field) => field.key === key)?.label as string;
    return { field: label, weight: weightLabel };
  }

  convertElasticQueryFieldToString({ field, weight }: ElasticQueryFieldDisplayObject): string {
    const theField = this.availableFields.find((availableField) => availableField.label === field);
    const weightValue = REVERSE_WEIGHT_MAP[weight as string];
    if (weightValue === 'undefined' || weightValue === undefined) return theField?.key as string;
    return theField?.key + '^' + weightValue;
  }

  convertWeightValueToLabel(value: number): string {
    return WEIGHT_MAP[value];
  }
}
