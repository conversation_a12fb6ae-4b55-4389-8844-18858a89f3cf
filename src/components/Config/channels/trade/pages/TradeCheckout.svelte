<script lang="ts">
  import CheckoutCheckoutFields from '@/components/Config/channels/trade/cards/CheckoutCheckoutFields.svelte';
  import CheckoutCreditLimit from '@/components/Config/channels/trade/cards/CheckoutCreditLimit.svelte';
  // COMPONENTS
  // import PageCard from '@/components/Config/PageCard.svelte';
  // import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  // import CheckoutCheckoutFields from '../cards/CheckoutCheckoutFields.svelte';
  // TYPES
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  // PROPS
  export let workingCopy: ChannelWorkingCopy;
</script>

<div class="space-y-4">
  <CheckoutCheckoutFields bind:workingCopy />
  <CheckoutCreditLimit bind:workingCopy />
  <!-- 
    All these components need to be turned into components and moved to the cards sub folder
   Leaving them here acts as my todo list
  -->

  <!--

  <PageCard title="Display Billing address">
    <InlineNotification type="info">
      <div>Display customers billing address on checkout page.</div>
    </InlineNotification>
    <div class=" flex items-center gap-4">
      <span class="label leading-6 text-neutral-700">NOT CONNECTED</span>
    </div>
  </PageCard>

  <PageCard title="Edit Billing address">
    <InlineNotification type="info">
      <div>Display customers billing address on checkout page.</div>
    </InlineNotification>
    <div class=" flex items-center gap-4">
      <span class="label leading-6 text-neutral-700">NOT CONNECTED</span>
    </div>
  </PageCard>

  <PageCard title="Display Shipping address">
    <InlineNotification type="info">
      <div>Display customers billing address on checkout page.</div>
    </InlineNotification>
    <div class=" flex items-center gap-4">
      <span class="label leading-6 text-neutral-700">NOT CONNECTED</span>
    </div>
  </PageCard>

  <PageCard title="Edit Shipping address">
    <InlineNotification type="info">
      <div>Display customers billing address on checkout page.</div>
    </InlineNotification>
    <div class=" flex items-center gap-4">
      <span class="label leading-6 text-neutral-700">NOT CONNECTED</span>
    </div>
  </PageCard>
  -->
</div>
