<script lang="ts">
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import CustomerDisplayFields from '@/components/Config/channels/trade/cards/CustomersCustomerDisplayFields.svelte';
  import ConfigToggle from '@/components/Config/shared/cards/ConfigToggle.svelte';

  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { MANAGE_CUSTOMER_ADDRESS } from '@/components/Config/constants';

  export let workingCopy: ChannelWorkingCopy;
</script>

<PageWrapper>
  <CustomerDisplayFields bind:workingCopy />
  <ConfigToggle
    bind:workingCopy
    label="Enable"
    title="Manage Addresses"
    metaKey={MANAGE_CUSTOMER_ADDRESS}
    description="<p>Let customers manage their addresses.</p><p>By turning this on, customers can manage a list of their addresses when they checkout</p>"
  />
</PageWrapper>
