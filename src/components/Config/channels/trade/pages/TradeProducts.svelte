<script lang="ts">
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import ConfigToggle from '@/components/Config/shared/cards/ConfigToggle.svelte';

  export let workingCopy: ChannelWorkingCopy;
</script>

<PageWrapper>
  <!-- Products -->
  <ConfigToggle
    bind:workingCopy
    metaKey="hide_options_on_list_view"
    title="Hide Option Labels on List View"
    label="Enable"
    description="By default, the option labels are displayed next to the SKU on the list view. Enabling this option will hide the option labels."
  />
</PageWrapper>
