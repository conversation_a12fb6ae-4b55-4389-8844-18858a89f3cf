<script context="module" lang="ts">
  import TradePayment from '@/components/Config/channels/trade/pages/TradePayment.svelte';
  import { Story, Template } from '@storybook/addon-svelte-csf';
  import { userEvent, within } from '@storybook/test';

  export const meta = {
    component: TradePayment
  };
</script>

<script lang="ts">
  async function waitFor(seconds: number) {
    await new Promise((resolve) => setTimeout(resolve, seconds * 1000));
  }

  async function openModal({ canvasElement }: { canvasElement: HTMLElement }) {
    const canvas = within(canvasElement);
    const btn = await canvas.findByTestId('table-edit');
    await userEvent.click(btn);
    await waitFor(0.5);
  }

  const baseWorkingCopy = {
    id: 999,
    created: '2023-02-17 12:06:12.000000',
    description: 'NOT A REAL CHANNEL',
    modified: '2023-02-17 13:10:48.000000',
    client_id: 999,
    active: 1,
    type: 'TO_BE_DECIDED',
    price_tier: '',
    qty_availability: null,
    sync_token: '0'
  };
</script>

<Template let:args>
  <TradePayment
    workingCopy={{
      ...baseWorkingCopy,
      isChannel: true,
      id: 57,
      description: 'My Channel',
      meta: args.meta
    }}
  />
</Template>

<Story
  name="No payment methods set"
  args={{
    meta: {}
  }}
/>

<Story
  name="Methods set"
  args={{
    meta: {
      payment_methods: {
        value:
          '[{"description":"On Account","method":"OnAccount","currency":"","merchant_id":"","application_id":"","mode":"","in_production":false,"test_mode":false},{"description":"Stripe","method":"Stripe","currency":"","merchant_id":"","application_id":"","mode":"","in_production":false,"test_mode":false}]'
      }
    }
  }}
/>

<Story
  name="Edit: OnAccount method"
  play={async (ctx) => {
    await openModal(ctx);
  }}
  args={{
    meta: {
      payment_methods: {
        value:
          '[{"description":"On Account","method":"OnAccount","currency":"","merchant_id":"","application_id":"","mode":"","in_production":false,"test_mode":false}]'
      }
    }
  }}
/>

<Story
  name="Edit: AdumoVirtual method"
  play={async (ctx) => {
    await openModal(ctx);
  }}
  args={{
    meta: {
      payment_methods: {
        value:
          '[{"description":"Adumo Virtual","method":"AdumoVirtual","currency":"","merchant_id":"","application_id":"","mode":"","in_production":false,"test_mode":false}]'
      }
    }
  }}
/>

<Story
  name="Edit: Stripe method"
  play={async (ctx) => {
    await openModal(ctx);
  }}
  args={{
    meta: {
      payment_methods: {
        value:
          '[{"description":"Stripe","method":"Stripe","currency":"","merchant_id":"","application_id":"","mode":"","in_production":false,"test_mode":false}]'
      }
    }
  }}
/>

<Story
  name="Edit: MygateVirtual method"
  play={async (ctx) => {
    await openModal(ctx);
  }}
  args={{
    meta: {
      payment_methods: {
        value:
          '[{"description":"Mygate Virtual","method":"MygateVirtual","currency":"","merchant_id":"","application_id":"","mode":"","in_production":false,"test_mode":false}]'
      }
    }
  }}
/>
