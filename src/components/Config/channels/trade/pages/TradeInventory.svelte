<script lang="ts">
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import StockCalculator from '@/components/Config/channels/shared/cards/InventoryStockCalculator.svelte';
  import StoreWideQty from '@/components/Config/channels/shared/cards/StoreWideQty.svelte';
  import ConfigToggle from '@/components/Config/shared/cards/ConfigToggle.svelte';
  import InventoryStock from '@/components/Config/shared/cards/InventoryStock.svelte';

  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import {
    QTY_MULTIPLES_OF,
    QTY_UPPER_LIMIT,
    MINIMUM_ORDER_QTY,
    HIDE_AVAILABILITY_ENABLED,
    SHOW_AVAILABILITY_UNITS,
    OVER_ORDER_ENABLED
  } from '@/components/Config/constants';

  export let workingCopy: ChannelWorkingCopy;
</script>

<PageWrapper>
  <StockCalculator bind:workingCopy />
  <StoreWideQty name="Purchase Quantity Increments" metaKey={QTY_MULTIPLES_OF} bind:workingCopy>
    <p>
      <strong>The increments that products must be ordered in.</strong>
      You can set the increments for ordering products for all products in your store.
    </p>
    <br />
    <p>
      This is typically defined on the product itself. For example, if a product is listed with a
      unit of measure of 1 but sold in packs of 10, you can set the product's 'qty_multiples_of'
      attribute to 10.
    </p>
    <br />
  </StoreWideQty>
  <StoreWideQty name="Quantity Upper Limit" metaKey={QTY_UPPER_LIMIT} bind:workingCopy>
    <p>
      You can set the <strong>upper limit for the channel quantity</strong>. This will also restrict
      the quantity available for ordering.
    </p>
    <br />
  </StoreWideQty>
  <StoreWideQty name="Minimum Order Quantity" metaKey={MINIMUM_ORDER_QTY} bind:workingCopy>
    <p>
      This is <strong>the minimum quantity required for each product to be ordered.</strong>
    </p>
    <br />
  </StoreWideQty>
  <ConfigToggle
    bind:workingCopy
    metaKey={HIDE_AVAILABILITY_ENABLED}
    title="Hide availability"
    description="When enabled, this will hide the 'In stock' and 'Out of stock' labels from the customer."
    label="Hide Availability"
  />
  <ConfigToggle
    bind:workingCopy
    metaKey={SHOW_AVAILABILITY_UNITS}
    title="Show availability units"
    description="When enabled, this will display the number of units of stock available to your users."
    label="Show availability units"
  />
  <ConfigToggle
    bind:workingCopy
    metaKey={OVER_ORDER_ENABLED}
    title="Over-ordering"
    description="Allow customers to place orders for products that are out of stock."
    label="Allow over-ordering"
  />
  <InventoryStock bind:workingCopy />
</PageWrapper>
