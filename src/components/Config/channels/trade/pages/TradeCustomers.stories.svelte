<script context="module" lang="ts">
  import TradeCustomers from '@/components/Config/channels/trade/pages/TradeCustomers.svelte';
  import { Story, Template } from '@storybook/addon-svelte-csf';

  export const meta = {
    component: TradeCustomers
  };
</script>

<script lang="ts">
  const baseWorkingCopy = {
    id: 999,
    created: '2023-02-17 12:06:12.000000',
    description: 'NOT A REAL CHANNEL',
    modified: '2023-02-17 13:10:48.000000',
    client_id: 999,
    active: 1,
    type: 'TO_BE_DECIDED',
    price_tier: '',
    qty_availability: null,
    sync_token: '0'
  };
</script>

<Template let:args>
  <TradeCustomers
    workingCopy={{
      ...baseWorkingCopy,
      isChannel: true,
      id: 57,
      description: 'My Channel',
      meta: args.meta
    }}
  />
</Template>

<Story
  name="None set"
  args={{
    meta: { account_display: { value: undefined } }
  }}
/>

<Story
  name="All set"
  args={{
    meta: {
      account_display: {
        value:
          '{"Discount":"discount","Group":"group","Payment Method":"payment_method","Sales rep":"sales_rep"}'
      },
      manage_customer_address: {
        value: 'true'
      }
    }
  }}
/>
