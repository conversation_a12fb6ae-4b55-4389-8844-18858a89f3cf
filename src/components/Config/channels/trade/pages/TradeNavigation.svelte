<script lang="ts">
  import ProductFacetedNavigation from '@/components/Config/channels/trade/cards/NavigationProductFacetedNavigation.svelte';
  import SearchFields from '@/components/Config/channels/trade/cards/NavigationSearchFields.svelte';
  import SuggestFields from '@/components/Config/channels/trade/cards/NavigationSuggestFields.svelte';
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import SearchBySkuDefault from '@/components/Config/channels/trade/cards/NavigationSearchBySkuDefault.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  export let workingCopy: ChannelWorkingCopy;
</script>

<PageWrapper>
  <ProductFacetedNavigation bind:workingCopy />
  <SearchFields bind:workingCopy />
  <SuggestFields bind:workingCopy />
  <SearchBySkuDefault bind:workingCopy />
</PageWrapper>
