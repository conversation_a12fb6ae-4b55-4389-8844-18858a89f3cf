<script context="module" lang="ts">
  import Navigation from '@/components/Config/channels/trade/pages/TradeNavigation.svelte';
  import { Story, Template } from '@storybook/addon-svelte-csf';

  export const meta = {
    component: Navigation
  };
</script>

<script lang="ts">
  const baseWorkingCopy = {
    id: 999,
    created: '2023-02-17 12:06:12.000000',
    description: 'NOT A REAL CHANNEL',
    modified: '2023-02-17 13:10:48.000000',
    client_id: 999,
    active: 1,
    type: 'TO_BE_DECIDED',
    price_tier: '',
    qty_availability: null,
    sync_token: '0'
  };
</script>

<Template let:args>
  <Navigation
    workingCopy={{
      ...baseWorkingCopy,
      isChannel: true,
      id: 57,
      description: 'My Channel',
      meta: args.meta
    }}
  />
</Template>

<Story
  name="None set (with defaults shown)"
  args={{
    meta: {}
  }}
/>

<Story
  name="All set"
  args={{
    meta: {
      aggregations: {
        value:
          '{"Categories":{"terms":{"field":"collection","exclude":"","order":{"_term":"asc"},"size":200}},"Types":{"terms":{"field":"product_type","exclude":"","order":{"_term":"asc"},"size":200}},"Tags":{"terms":{"field":"tags","exclude":"","order":{"_term":"asc"},"size":10}},"Brands":{"terms":{"field":"vendor","exclude":"","order":{"_term":"asc"},"size":200}},"Attributes":{"terms":{"field":"meta.value.english","exclude":"","order":{"_term":"asc"},"size":10}}}'
      },
      elastic_query_fields: {
        value:
          'source_product_code,title,body_html,collection.english,product_type.english,tags.autocomplete,vendor.english,variants.source_variant_code,variant.sku,variants.barcode'
      },
      elastic_suggest_fields: {
        value:
          'source_product_code,title,body_html,collection.english,product_type.english,tags.autocomplete,vendor.english,variants.source_variant_code,variant.sku,variants.barcode'
      },
      search_by_sku_default: {
        value: 'true'
      }
    }
  }}
/>

<Story
  name="All set, default aggregations"
  args={{
    meta: {
      elastic_query_fields: {
        value:
          'source_product_code,title,body_html,collection.english,product_type.english,tags.autocomplete,vendor.english,variants.source_variant_code,variant.sku,variants.barcode'
      },
      elastic_suggest_fields: {
        value:
          'source_product_code,title,body_html,collection.english,product_type.english,tags.autocomplete,vendor.english,variants.source_variant_code,variant.sku,variants.barcode'
      },
      search_by_sku_default: {
        value: 'true'
      }
    }
  }}
/>
