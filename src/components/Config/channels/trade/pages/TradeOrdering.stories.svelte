<script context="module" lang="ts">
  import TradeOrdering from '@/components/Config/channels/trade/pages/TradeOrdering.svelte';
  import { Story, Template } from '@storybook/addon-svelte-csf';

  export const meta = {
    component: TradeOrdering
  };
</script>

<script lang="ts">
  const baseWorkingCopy = {
    id: 999,
    created: '2023-02-17 12:06:12.000000',
    description: 'NOT A REAL CHANNEL',
    modified: '2023-02-17 13:10:48.000000',
    client_id: 999,
    active: 1,
    type: 'TO_BE_DECIDED',
    price_tier: '',
    qty_availability: null,
    sync_token: '0'
  };
</script>

<Template let:args>
  <TradeOrdering
    workingCopy={{
      ...baseWorkingCopy,
      isChannel: true,
      id: 57,
      description: 'My Channel',
      meta: args.meta
    }}
  />
</Template>

<Story
  name="None set (with defaults shown)"
  args={{
    meta: {}
  }}
/>

<Story
  name="All set"
  args={{
    meta: {
      order_columns: {
        value:
          '{"SALES ORDER NO.":"order.sources[0].source_order_code","TOTAL":"order.total","STATUS":"order.status","DATE":"order.created","CUSTOMER FIRST NAME":"order.customer.first_name"}'
      },
      quick_order_columns: {
        value:
          '{"DESCRIPTION":"title","PRODUCT CODE":"source_product_code","WIDTH":"meta_width_cm","TYPE":"product_type"}'
      },
      channel_order_code_prefix: {
        value: 'TRADE_ORDER_'
      },
      order_view_display: {
        value:
          '{"ORDER NO":"order.channel_order_code","SALES ORDER NO.":"order.sources[0].source_order_code","TOTAL":"order.total"}'
      },
      channel_order_code_sequence: {
        value: '100'
      },
      allow_bulk_order: {
        value: 'true'
      },
      allow_quick_order: {
        value: 'true'
      },
      order_from_date: {
        value: '2024-07-11T10:00:00.000Z'
      },
      param_default_customer_code: {
        value: '123'
      },
      order_set_system_price: {
        value: 'true'
      },
      order_use_system_price_tier: {
        value: 'Wholesale'
      },
      order_use_system_price_tier_incl: {
        value: 'Retail'
      },
      min_order_amount: {
        value: '10'
      }
    }
  }}
/>

<Story
  name="All except price from S2S"
  args={{
    meta: {
      order_columns: {
        value:
          '{"SALES ORDER NO.":"order.sources[0].source_order_code","TOTAL":"order.total","STATUS":"order.status","DATE":"order.created","CUSTOMER FIRST NAME":"order.customer.first_name"}'
      },
      quick_order_columns: {
        value:
          '{"DESCRIPTION":"title","PRODUCT CODE":"source_product_code","WIDTH":"meta_width_cm","TYPE":"product_type"}'
      },
      channel_order_code_prefix: {
        value: 'TRADE_ORDER_'
      },
      order_view_display: {
        value:
          '{"ORDER NO":"order.channel_order_code","SALES ORDER NO.":"order.sources[0].source_order_code","TOTAL":"order.total"}'
      },
      channel_order_code_sequence: {
        value: '100'
      },
      allow_bulk_order: {
        value: 'true'
      },
      allow_quick_order: {
        value: 'true'
      },
      order_from_date: {
        value: '2024-07-11T10:00:00.000Z'
      },
      param_default_customer_code: {
        value: '123'
      },
      order_set_system_price: {
        value: 'false'
      },
      order_use_system_price_tier: {
        value: 'Wholesale'
      },
      order_use_system_price_tier_incl: {
        value: 'Retail'
      },
      min_order_amount: {
        value: '10'
      }
    }
  }}
/>
