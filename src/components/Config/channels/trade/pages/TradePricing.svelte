<script lang="ts">
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import Pricing from '@/components/Config/channels/shared/cards/ChannelPricing.svelte';
  import PriceDisplay from '@/components/Config/channels/trade/cards/PricingPriceDisplay.svelte';
  import PriceInclusive from '@/components/Config/channels/trade/cards/PricingPriceInclusive.svelte';
  import PricingTaxRate from '@/components/Config/channels/trade/cards/PricingTaxRate.svelte';
  export let workingCopy: ChannelWorkingCopy;
</script>

<PageWrapper>
  <Pricing bind:workingCopy />
  <PriceInclusive bind:workingCopy />
  <PriceDisplay bind:workingCopy />
  <PricingTaxRate bind:workingCopy />
</PageWrapper>
