<script lang="ts">
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import OrderColumns from '@/components/Config/channels/trade/cards/OrderingOrderColumns.svelte';
  import QuickOrderColumns from '@/components/Config/channels/trade/cards/OrderingQuickOrderColumns.svelte';
  import ConfigToggle from '@/components/Config/shared/cards/ConfigToggle.svelte';
  import ConfigTogglableNumber from '@/components/Config/shared/cards/ConfigTogglableNumber.svelte';
  import OrderViewDisplay from '@/components/Config/channels/trade/cards/OrderingOrderViewDisplay.svelte';
  import OrderCodePrefix from '@/components/Config/channels/trade/cards/OrderingOrderCodePrefix.svelte';
  import ChannelOrderCodeSequence from '@/components/Config/channels/trade/cards/OrderingChannelOrderCodeSequence.svelte';
  import OrderFromDate from '@/components/Config/shared/cards/OrderFromDate.svelte';
  import UsePriceFromS2S from '@/components/Config/channels/shared/cards/OrderingUsePriceFromS2S.svelte';
  import DefaultCustomerCode from '@/components/Config/channels/shared/cards/OrderingDefaultCustomerCode.svelte';

  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import {
    MIN_ORDER_AMOUNT,
    ALLOW_BULK_ORDER,
    ALLOW_QUICK_ORDER
  } from '@/components/Config/constants';

  export let workingCopy: ChannelWorkingCopy;
</script>

<PageWrapper>
  <OrderColumns bind:workingCopy />

  <QuickOrderColumns bind:workingCopy />

  <ConfigTogglableNumber
    bind:workingCopy
    toggleLabel="Enable Minimum Order Amount"
    inputLabel="Minimum Order Amount"
    metaKey={MIN_ORDER_AMOUNT}
    name="Minimum order amount"
    inputType="currency"
  >
    Minimum order amount exl. tax.
  </ConfigTogglableNumber>

  <OrderCodePrefix bind:workingCopy />

  <OrderViewDisplay bind:workingCopy />

  <ChannelOrderCodeSequence bind:workingCopy />

  <ConfigToggle
    bind:workingCopy
    metaKey={ALLOW_BULK_ORDER}
    title="Allow bulk order"
    description="Show or hide bulk order feature."
    label="Enable bulk order"
  />

  <ConfigToggle
    bind:workingCopy
    metaKey={ALLOW_QUICK_ORDER}
    title="Allow quick order"
    description="Show or hide quick order feature."
    label="Enable quick order"
  />

  <OrderFromDate bind:workingCopy />

  <DefaultCustomerCode bind:workingCopy />

  <UsePriceFromS2S bind:workingCopy />
</PageWrapper>
