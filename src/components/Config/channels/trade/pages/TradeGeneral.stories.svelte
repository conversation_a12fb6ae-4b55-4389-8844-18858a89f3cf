<script context="module" lang="ts">
  import TradeGeneral from '@/components/Config/channels/trade/pages/TradeGeneral.svelte';
  import { Story, Template } from '@storybook/addon-svelte-csf';

  export const meta = {
    component: TradeGeneral
  };
</script>

<script lang="ts">
  const baseWorkingCopy = {
    id: 999,
    created: '2023-02-17 12:06:12.000000',
    description: 'NOT A REAL CHANNEL',
    modified: '2023-02-17 13:10:48.000000',
    client_id: 999,
    active: 1,
    type: 'TO_BE_DECIDED',
    price_tier: '',
    qty_availability: null,
    sync_token: '0'
  };
</script>

<Template let:args>
  <div class="max-w-[864px]">
    <TradeGeneral
      workingCopy={{
        ...baseWorkingCopy,
        type: 'trade',
        meta: args.meta,
        isChannel: true
      }}
    />
  </div>
</Template>

<Story
  name="None set"
  args={{
    meta: {}
  }}
/>

<Story
  name="All values set"
  args={{
    meta: {
      domain_alias: {
        value: 'b2b.example.com'
      },
      login_redirect: {
        value: 'b2b.example.com'
      },
      display_name: {
        value: 'B2B Store'
      },
      email: {
        value: '<EMAIL>'
      },
      phone: {
        value: '123456789'
      },
      address_line1: {
        value: '94 Road street'
      },
      address_line2: {
        value: 'Glutenberg'
      },
      address_line3: {
        value: 'Cape City'
      }
    }
  }}
/>
