<script lang="ts">
  import GeneralAddress from '@/components/Config/channels/trade/cards/GeneralAddress.svelte';
  import GeneralName from '@/components/Config/channels/trade/cards/GeneralName.svelte';
  import GeneralDomain from '@/components/Config/channels/trade/cards/GeneralDomain.svelte';
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  export let workingCopy: ChannelWorkingCopy;
</script>

<PageWrapper>
  <GeneralDomain bind:workingCopy />
  <GeneralName bind:workingCopy />
  <GeneralAddress bind:workingCopy />
</PageWrapper>
