<script lang="ts">
  import PageCardSkeleton from '@/components/Config/PageCardSkeleton.svelte';
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import FulfillOrder from '@/components/Config/channels/shared/cards/LogisticsFulfillOrder.svelte';
  import { hasFulfillmentService } from '@/models/config/config';
  import { createConfigQuery } from '@/queries/config.query';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import LogisticsNoFulfillmentServices from '@/components/Config/channels/shared/cards/LogisticsNoFulfillmentServices.svelte';
  import LogisticsShippingMethods from '@/components/Config/channels/trade/cards/LogisticsShippingMethods.svelte';
  import LogisticsShippingTaxRate from '@/components/Config/channels/trade/cards/LogisticsShippingTaxRate.svelte';

  export let workingCopy: ChannelWorkingCopy;

  const configQuery = createConfigQuery();

  $: fulfillmentServices = Object.values($configQuery.data?.fulfillmentservices || {}) || [];
</script>

<PageWrapper>
  {#if $configQuery.isLoading || !$configQuery.data}
    <PageCardSkeleton />
  {:else if hasFulfillmentService($configQuery.data) || fulfillmentServices?.length}
    <FulfillOrder {fulfillmentServices} bind:workingCopy />
  {:else}
    <LogisticsNoFulfillmentServices />
  {/if}
  <LogisticsShippingTaxRate bind:workingCopy />
  <LogisticsShippingMethods bind:workingCopy />
</PageWrapper>
