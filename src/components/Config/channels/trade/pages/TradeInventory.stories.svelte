<script context="module" lang="ts">
  import TradeInventory from '@/components/Config/channels/trade/pages/TradeInventory.svelte';
  import { Story, Template } from '@storybook/addon-svelte-csf';

  export const meta = {
    component: TradeInventory
  };
</script>

<script lang="ts">
  const baseWorkingCopy = {
    id: 999,
    created: '2023-02-17 12:06:12.000000',
    description: 'NOT A REAL CHANNEL',
    modified: '2023-02-17 13:10:48.000000',
    client_id: 999,
    active: 1,
    type: 'TO_BE_DECIDED',
    price_tier: '',
    qty_availability: null,
    sync_token: '0'
  };
</script>

<Template let:args>
  <!-- See mock channel-sources-repo for various states -->
  <TradeInventory
    workingCopy={{
      ...baseWorkingCopy,
      isChannel: true,
      id: 57,
      description: 'My Channel',
      meta: args.meta,
      qty_availability: args.qty_availability
    }}
  />
</Template>

<Story
  name="None set"
  args={{
    meta: {}
  }}
/>

<Story
  name="All set"
  args={{
    qty_availability: 'Cape Town+Durban',
    meta: {
      hide_availability_enabled: {
        value: 'true'
      },
      show_availability_units: {
        value: 'true'
      },
      over_order_enabled: {
        value: 'true'
      },
      qty_multiples_of: {
        value: '2'
      },
      qty_limit_upper: {
        value: '10'
      },
      minimum_order_qty: {
        value: '3'
      }
    }
  }}
/>
