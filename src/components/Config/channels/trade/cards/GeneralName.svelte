<script lang="ts">
  import { DISPLAY_NAME, EMAIL, PHONE } from '@/components/Config/constants';
  import PageCard from '@/components/Config/PageCard.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: ChannelWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [DISPLAY_NAME, EMAIL, PHONE]);
</script>

<PageCard title="Store Details">
  <div class="flex max-w-96 flex-col gap-4">
    <TextInput sideLabel label="Display Name" bind:value={workingCopy.meta[DISPLAY_NAME].value} />
    <TextInput sideLabel label="Email Address" bind:value={workingCopy.meta[EMAIL].value} />
    <TextInput sideLabel label="Phone" bind:value={workingCopy.meta[PHONE].value} />
  </div>
</PageCard>
