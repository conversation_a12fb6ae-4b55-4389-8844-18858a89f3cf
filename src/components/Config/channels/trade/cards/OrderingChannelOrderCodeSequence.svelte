<script lang="ts">
  import { CHANNEL_ORDER_CODE_SEQUENCE } from '@/components/Config/constants';
  import PageCard from '@/components/Config/PageCard.svelte';
  import NumberInput from '@/components/Controls/NumberInput.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: ChannelWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [CHANNEL_ORDER_CODE_SEQUENCE]);
</script>

<PageCard title="Channel Order Code Sequence">
  <InlineNotification type="info">
    <p>Start number for the sequential_order_code</p>
  </InlineNotification>
  <div class="w-1/2">
    <NumberInput
      label="Order code start number"
      sideLabel
      bind:value={workingCopy.meta[CHANNEL_ORDER_CODE_SEQUENCE].value}
    />
  </div>
</PageCard>
