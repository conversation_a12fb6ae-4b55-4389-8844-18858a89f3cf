<script lang="ts" context="module">
  export type FacetedNavItem = { label: string; field: string; chip?: string };
</script>

<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import {
    isNestedFacetedNavigationAggregation,
    isSimpleFacetedNavigationAggregation,
    productFacetedNavigationAggregationsFromList,
    productFacetedNavigationAggregationsToList,
    type NestedFacetedNavigationAggregation,
    type ProductFacetedNavigationAggregations,
    type ProductFacetedNavigationAggregationsListItem,
    type SimpleFacetedNavigationAggregation
  } from '@/models/config/channels/trade/navigation/aggregations';
  import AddFacetedNavItem from '@/components/Config/channels/trade/components/ProductFacetedNavigationAddFacetedNavItem.svelte';
  import ConfigTable, {
    type TableItem
  } from '@/components/Config/shared/components/ConfigTable.svelte';
  import FacetedNavItemContent from '@/components/Config/channels/trade/components/ProductFacitedNavigationFacetedNavItemContent.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { AGGREGATIONS } from '@/components/Config/constants';

  export let workingCopy: ChannelWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [AGGREGATIONS]);

  // dont mutate aggs
  let aggs: ProductFacetedNavigationAggregationsListItem[];
  $: aggs = productFacetedNavigationAggregationsToList(
    JSON.parse(workingCopy.meta[AGGREGATIONS].value) as ProductFacetedNavigationAggregations
  );

  let items: TableItem<FacetedNavItem>[];
  $: items = aggs.map((agg) => {
    let field: string = '';
    let isNested = false;
    if (isNestedFacetedNavigationAggregation(agg)) {
      field = agg.aggs[agg.key].filter.term['meta.key'];
      isNested = true;
    } else if (isSimpleFacetedNavigationAggregation(agg)) {
      field = agg.terms.field;
    }
    return { key: agg.key, label: agg.key, field, chip: isNested ? 'Meta' : undefined };
  });

  const onDelete = (key: string) => {
    const newAggs = aggs.filter((aggs) => aggs.key !== key);
    workingCopy.meta[AGGREGATIONS].value = JSON.stringify(
      productFacetedNavigationAggregationsFromList(newAggs)
    );
  };

  const onAdd = (
    label: string,
    agg: SimpleFacetedNavigationAggregation | NestedFacetedNavigationAggregation
  ) => {
    let newAggs: ProductFacetedNavigationAggregationsListItem[] = [];
    const newAggField = isNestedFacetedNavigationAggregation(agg)
      ? agg.aggs[label].filter.term['meta.key']
      : agg.terms.field;

    const existingAgg = items.find((item) => item.field === newAggField);

    if (existingAgg) {
      newAggs = aggs.map((oldAgg) => {
        if (oldAgg.key === existingAgg.key) {
          return { key: label, ...agg };
        }
        return oldAgg;
      });
    } else {
      newAggs = [...aggs, { key: label, ...agg }];
    }

    workingCopy.meta[AGGREGATIONS].value = JSON.stringify(
      productFacetedNavigationAggregationsFromList(newAggs)
    );
  };

  const onReorder = (newList: TableItem<FacetedNavItem>[]) => {
    const reorderedAggs = newList.map(
      (item) =>
        aggs.find((agg) => agg.key === item.key) as ProductFacetedNavigationAggregationsListItem
    );
    const newAggs = productFacetedNavigationAggregationsFromList(reorderedAggs);
    workingCopy.meta[AGGREGATIONS].value = JSON.stringify(newAggs);
  };
</script>

<PageCard title="Product Faceted Navigation">
  <InlineNotification type="info">
    <p>
      Faceted navigation settings allow you to <strong>customize the filters</strong> (like Manufacturer,
      Size, Color) that customers use to sort and find products on your Trade store. By adding or adjusting
      these attributes, you enhance the shopping experience, making it easier for customers to find what
      they are looking for. Properly configured filters can lead to improved customer satisfaction and
      potentially higher sales.
    </p>
  </InlineNotification>

  <ConfigTable displayComponent={FacetedNavItemContent} dnd {items} {onReorder} {onDelete}>
    <AddFacetedNavItem {onAdd} />
  </ConfigTable>
</PageCard>
