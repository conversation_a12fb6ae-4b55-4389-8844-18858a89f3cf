<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { ADDRESS_LINE_1, ADDRESS_LINE_2, ADDRESS_LINE_3 } from '@/components/Config/constants';

  export let workingCopy: ChannelWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [ADDRESS_LINE_1, ADDRESS_LINE_2, ADDRESS_LINE_3]);
</script>

<PageCard title="Company Address">
  <InlineNotification type="info">
    <p>This is the address that will appear on your order documents.</p>
  </InlineNotification>
  <div class="flex max-w-96 flex-col gap-4">
    <TextInput
      sideLabel
      label="Address Line 1"
      bind:value={workingCopy.meta[ADDRESS_LINE_1].value}
    />
    <TextInput
      sideLabel
      label="Address Line 2"
      bind:value={workingCopy.meta[ADDRESS_LINE_2].value}
    />
    <TextInput
      sideLabel
      label="Address Line 3"
      bind:value={workingCopy.meta[ADDRESS_LINE_3].value}
    />
  </div>
</PageCard>
