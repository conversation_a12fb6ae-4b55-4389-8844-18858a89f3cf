<script lang="ts">
  import {
    CHECKOUT_DISPLAY_CREDIT_LIMIT,
    CHECKOUT_CREDIT_LIMIT_EXCEEDED_CONFIRMATION_MESSAGE
  } from '@/components/Config/constants';
  import PageCard from '@/components/Config/PageCard.svelte';
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';
  import TextArea from '@/components/Controls/TextArea.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: ChannelWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [
    CHECKOUT_DISPLAY_CREDIT_LIMIT,
    CHECKOUT_CREDIT_LIMIT_EXCEEDED_CONFIRMATION_MESSAGE
  ]);
</script>

<PageCard title="Credit Limit">
  <InlineNotification type="info">
    <p>
      You can show customer's their credit limit at checkout along with the after purchase value.
      You can also set a custom message to be shown when the customer exceeds their credit limit.
    </p>
    <br />
    <p>
      <strong>
        Note that credit limit will only be shown at checkout if the customer has a credit limit
        meta set. This can be added per customer.
      </strong>
    </p>
  </InlineNotification>
  <div class="w-max">
    <Toggle
      label="Show credit limit"
      noLabelBorder
      bind:checked={workingCopy.meta[CHECKOUT_DISPLAY_CREDIT_LIMIT].value}
      type="string"
    />
  </div>
  {#if workingCopy.meta[CHECKOUT_DISPLAY_CREDIT_LIMIT].value === 'true'}
    <TextArea
      label="Credit Limit Exceeded Confirmation Message"
      bind:value={workingCopy.meta[CHECKOUT_CREDIT_LIMIT_EXCEEDED_CONFIRMATION_MESSAGE].value}
      rows={3}
    />
  {/if}
</PageCard>
