<script lang="ts">
  import { DOMAIN_ALIAS, LOGIN_REDIRECT } from '@/components/Config/constants';
  import PageCard from '@/components/Config/PageCard.svelte';
  import Icon from '@/components/Icons/Icon.svelte';
  import IconFlag from '@/components/Icons/IconFlag.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';

  export let workingCopy: ChannelWorkingCopy;

  const DEFAULT_DOMAIN = 'https://b2b.stock2shop.com';

  let domain = workingCopy.meta[DOMAIN_ALIAS]?.value ?? DEFAULT_DOMAIN;
  domain = workingCopy.meta[LOGIN_REDIRECT]?.value ?? domain;
</script>

<PageCard title="Website Domain">
  <InlineNotification type="info">
    <p>
      If you need a custom domain, please <a
        href="mailto:<EMAIL>"
        title="mail support"
        class="font-bold underline">send us a request.</a
      >
    </p>
    <br />
    <p>
      Note that there are costs associated with using a custom domain, and specific
      <a
        class="font-bold underline"
        href="https://www.stock2shop.com/help/guides/custom-domain/"
        target="_blank"
        title="steps to setup custom domain">steps</a
      >
      must be completed.
    </p>
  </InlineNotification>
  <div class="flex gap-2 leading-4">
    <Icon IconComponent={IconFlag} size="i-4" />
    <span>Current Domain: </span>
    <a class="underline" target="_blank" href={domain}>{domain.replace('https://', '')}</a>
  </div>
</PageCard>
