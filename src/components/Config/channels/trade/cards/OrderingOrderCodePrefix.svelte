<script lang="ts">
  import { CHANNEL_ORDER_CODE_PREFIX } from '@/components/Config/constants';

  import PageCard from '@/components/Config/PageCard.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: ChannelWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [CHANNEL_ORDER_CODE_PREFIX]);
</script>

<PageCard title="Order Code Prefix">
  <InlineNotification type="info">
    <p>Prefix applied to all orders.</p>
  </InlineNotification>
  <div class="max-w-max">
    <TextInput
      sideLabel
      label="Order Prefix"
      bind:value={workingCopy.meta[CHANNEL_ORDER_CODE_PREFIX].value}
    />
  </div>
</PageCard>
