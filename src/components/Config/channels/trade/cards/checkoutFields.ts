export type DropdownRadioOption = {
  key?: string;
  value: CheckoutFieldTypeAttributes;
  label: string;
};

export type CheckoutField = {
  key: string;
  description: string;
  type: CheckoutFieldTypeAttributes;
  required: boolean;
};

export type RadioOrDropDownCheckoutField = {
  options: DropdownRadioOption[];
  value: string;
} & CheckoutField;

export type TextOrTextAreaCheckoutField = {
  default: string;
  min_chars: number;
  max_chars: number;
  value: string;
} & CheckoutField;

export type CheckboxCheckoutField = {
  checked_by_default: boolean;
} & CheckoutField;

export type DateCheckoutField = {
  valid_after_days: number;
} & CheckoutField;

export type OldConfigCheckoutFields = {
  [key: string]: Record<string, unknown>;
};

export type CheckoutFieldTypeAttributes =
  | 'text'
  | 'textarea'
  | 'dropdown'
  | 'checkbox'
  | 'radio'
  | 'date'
  | '';

export const CHECKOUT_FIELD_TYPE_OPTIONS: DropdownRadioOption[] = [
  { value: 'text', label: 'Text' },
  { value: 'textarea', label: 'Text Area' },
  { value: 'dropdown', label: 'Dropdown' },
  { value: 'checkbox', label: 'Checkbox' },
  { value: 'radio', label: 'Radio' },
  { value: 'date', label: 'Date' }
];
