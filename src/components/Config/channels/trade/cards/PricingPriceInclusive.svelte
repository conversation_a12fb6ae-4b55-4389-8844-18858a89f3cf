<script lang="ts">
  import { PRICE_INCLUSIVE } from '@/components/Config/constants';
  import PageCard from '@/components/Config/PageCard.svelte';
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: ChannelWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [PRICE_INCLUSIVE]);
</script>

<PageCard title="Is the Price List Tax Inclusive?">
  <InlineNotification type="info">
    <p>
      Turn this on if the price list selected above includes tax in its prices. For example, if a
      product sells for $100 including 15% tax, the price list should show $100.
    </p>
    <br />
    <p>
      Turn this off if the price list excludes tax. In the same example, the price list should show
      $86.96 ($100 ÷ 1.15).
    </p>
  </InlineNotification>
  <div class="w-max">
    <Toggle
      label="Inclusive"
      bind:checked={workingCopy.meta[PRICE_INCLUSIVE].value}
      type="string"
      noLabelBorder
    />
  </div>
</PageCard>
