<script lang="ts">
  import { type TableItem } from '@/components/Config/shared/components/ConfigTable.svelte';
  import PageCard from '@/components/Config/PageCard.svelte';
  import AddFieldItem from '@/components/Config/channels/trade/components/SearchFieldsAddFieldItem.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import ElasticQueryFieldHandler, {
    type ElasticQueryFieldDisplayObject
  } from '@/components/Config/channels/trade/utils/elasticFieldHandler';
  import KeyOperatorValueRow from '@/components/Config/shared/components/KeyOperatorValueRow.svelte';
  import { ELASTIC_SUGGEST_FIELDS } from '@/components/Config/constants';

  export let workingCopy: ChannelWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [ELASTIC_SUGGEST_FIELDS]);

  let fields: ElasticQueryFieldDisplayObject[];

  const elasticQueryFieldHandler = new ElasticQueryFieldHandler();

  $: if (workingCopy.meta[ELASTIC_SUGGEST_FIELDS].value !== '') {
    fields = workingCopy.meta[ELASTIC_SUGGEST_FIELDS].value
      .split(',')
      .map((presetField) => elasticQueryFieldHandler.convertStringToElasticQueryField(presetField));
  } else {
    fields = [];
  }

  const onDelete = (fieldLabel: string) => {
    const newFields = [...fields].filter((field) => field.field !== fieldLabel);
    if (newFields.length === 0) return (workingCopy.meta[ELASTIC_SUGGEST_FIELDS].value = '');
    workingCopy.meta[ELASTIC_SUGGEST_FIELDS].value = newFields
      .map((field) => elasticQueryFieldHandler.convertElasticQueryFieldToString(field))
      .join(',');
  };

  const onAdd = (field: { key: string; value: string }, weight: number = 0) => {
    const newFields = [...fields];
    // update field if it already exists
    const existingFieldIndex = fields.findIndex(
      (existingField) => existingField.field === field.value
    );
    if (existingFieldIndex === -1) {
      newFields.push({
        field: field.value,
        weight: elasticQueryFieldHandler.convertWeightValueToLabel(weight)
      });
    } else {
      newFields[existingFieldIndex] = {
        field: field.value,
        weight: elasticQueryFieldHandler.convertWeightValueToLabel(weight)
      };
    }
    workingCopy.meta[ELASTIC_SUGGEST_FIELDS].value = newFields
      .map((field) => elasticQueryFieldHandler.convertElasticQueryFieldToString(field))
      .join(',');
  };

  let items: TableItem<{ weight: string }>[];
  $: items = fields.map(({ field, weight }) => ({
    key: field,
    weight: weight
  }));
</script>

<PageCard title="Suggest Fields">
  <InlineNotification type="info">
    <p>
      Configure the product fields that appear as suggestions in the dropdown while the user is
      typing in the search bar on your trade store.
    </p>
  </InlineNotification>

  <div>
    {#if items.length > 0}
      {#each items as { key, weight } (key)}
        <KeyOperatorValueRow {key} operator={weight} on:delete={() => onDelete(key)} />
      {/each}
    {/if}
  </div>
  <AddFieldItem {onAdd} />
</PageCard>
