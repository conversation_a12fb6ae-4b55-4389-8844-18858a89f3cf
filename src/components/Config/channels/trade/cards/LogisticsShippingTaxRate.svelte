<script lang="ts">
  import { TAX_RATE_SHIPPING } from '@/components/Config/constants';
  import PageCard from '@/components/Config/PageCard.svelte';
  import NumberInput from '@/components/Controls/NumberInput.svelte';
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: ChannelWorkingCopy;

  let userWantsToSetShippingTaxRate =
    workingCopy.meta[TAX_RATE_SHIPPING]?.value !== undefined &&
    workingCopy.meta[TAX_RATE_SHIPPING]?.value !== null &&
    workingCopy.meta[TAX_RATE_SHIPPING]?.value !== '';

  $: if (!userWantsToSetShippingTaxRate) {
    setDefaultConfigMeta(workingCopy, [TAX_RATE_SHIPPING]);
    workingCopy.meta[TAX_RATE_SHIPPING].value = '';
  }
</script>

<PageCard title="Shipping tax rate">
  <InlineNotification type="info">
    <p>
      Set the tax rate applied to shipping for this channel. This rate overrides the general
      tax_rate for shipping charges and can be further overridden by a meta_tax_rate_shipping
      defined at the customer level, which takes precedence.
    </p>
  </InlineNotification>
  <div class="w-max space-y-4">
    <Toggle
      noLabelBorder
      label="Set Shipping tax rate"
      bind:checked={userWantsToSetShippingTaxRate}
      type="boolean"
    />
    {#if userWantsToSetShippingTaxRate}
      <NumberInput required bind:value={workingCopy.meta[TAX_RATE_SHIPPING].value} />
    {/if}
  </div>
</PageCard>
