<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  export let workingCopy: ChannelWorkingCopy;
</script>

<PageCard title="Shipping Methods">
  <InlineNotification type="info">
    <div>
      Create custom shipping methods to add shipping types and costs to checkout. Customers can have
      their own Shipping methods applied.
    </div>
  </InlineNotification>
  <div class="flex items-center gap-4">
    <span class="label leading-6 text-neutral-700"> shipping method </span>
  </div>
</PageCard>
