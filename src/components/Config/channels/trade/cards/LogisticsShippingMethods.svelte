<script lang="ts" context="module">
  export type UnformattedMethodObject = {
    [key: string]: {
      method: 'flat_rate' | 'table_rate';
      price: number;
      tax_lines: TaxLine[];
      rates: Rate[];
    };
  };

  export type FormattedMethodObject = {
    name: string;
    method: 'flat_rate' | 'table_rate';
    price: number;
    tax_lines: TaxLine[];
    rates: Rate[];
  };

  export type Rate = {
    tax_lines: TaxLine[];
    order_total: number;
    price: number;
  };
</script>

<script lang="ts">
  import IconAddCircle from '@/components/Icons/IconAddCircle.svelte';
  import Button from '@/components/Controls/Button.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import PageCard from '@/components/Config/PageCard.svelte';
  import { SHIPPING_METHODS } from '@/components/Config/constants';
  import type { TaxLine } from '@/models/orders/tax-line';
  import ShippingMethodRow from '@/components/Config/channels/trade/components/ShippingMethodRow.svelte';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: ChannelWorkingCopy;

  let shippingMethods = JSON.parse(workingCopy.meta[SHIPPING_METHODS]?.value || '{}');

  $: methodNames = shippingMethodsArray.map(({ name }) => name) || [];
  $: hasDuplicates = new Set(methodNames).size !== methodNames.length;

  function convertMethodsObjectToArray(methodsObject: UnformattedMethodObject) {
    return Object.entries(methodsObject).map(([key, value]) => {
      return { name: key, ...value };
    });
  }

  function convertMethodsArrayToObject(
    methodsArray: FormattedMethodObject[]
  ): UnformattedMethodObject {
    return methodsArray.reduce((acc: Record<string, Partial<FormattedMethodObject>>, method) => {
      const { name, rates, ...rest } = method;
      if (rest.method === 'table_rate') {
        acc[name] = { ...rest, rates };
      } else {
        acc[name] = rest;
      }
      return acc as UnformattedMethodObject;
    }, {});
  }

  let shippingMethodsArray = convertMethodsObjectToArray(
    JSON.parse(workingCopy.meta[SHIPPING_METHODS]?.value || '{}')
  );

  function addShippingMethod() {
    const newMethod = {
      name: '',
      method: 'flat_rate',
      price: 0,
      tax_lines: [
        {
          rate: 0,
          price: 0,
          title: '',
          code: ''
        }
      ],
      rates: [{}]
    };
    // @ts-expect-error
    shippingMethodsArray = [...shippingMethodsArray, newMethod];
  }

  function removeMethod(methodName: string) {
    shippingMethodsArray = shippingMethodsArray.filter((method) => method.name !== methodName);
  }

  $: setDefaultConfigMeta(workingCopy, [SHIPPING_METHODS]);

  $: if (workingCopy.meta[SHIPPING_METHODS])
    workingCopy.meta[SHIPPING_METHODS].value = JSON.stringify(
      convertMethodsArrayToObject(shippingMethodsArray)
    );

  $: shippingMethodsArray.forEach((method) => {
    if (method.method === 'table_rate' && !method.rates) {
      method.rates = [];
    }
  });
</script>

<PageCard title="Shipping Methods">
  <div class="w-full space-y-6">
    <InlineNotification type="info">
      <p>Shipping methods available to your customers.</p>
    </InlineNotification>

    {#if shippingMethods}
      <div>
        {#each shippingMethodsArray as shippingMethod}
          <ShippingMethodRow {removeMethod} bind:shippingMethod />
        {/each}
      </div>
    {/if}
    <div class="flex w-full justify-end py-2">
      <Button
        variant="gray"
        icon={IconAddCircle}
        on:click={() => addShippingMethod()}
        disabled={methodNames.some((key) => key === '') || hasDuplicates}
      >
        Add Shipping Method
      </Button>
    </div>
    {#if hasDuplicates}
      <InlineNotification type="warning">
        <p>Shipping method names must be unique.</p>
      </InlineNotification>
    {/if}
  </div>
</PageCard>
