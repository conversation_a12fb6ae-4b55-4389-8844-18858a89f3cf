<script lang="ts">
  import { TAX_RATE } from '@/components/Config/constants';
  import PageCard from '@/components/Config/PageCard.svelte';
  import NumberInput from '@/components/Controls/NumberInput.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: ChannelWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [TAX_RATE]);
</script>

<PageCard title="Tax rate">
  <InlineNotification type="info">
    <p>
      Set the tax rate applied to this channel. This rate can be overridden by a meta_tax_rate
      defined at the customer or product level, with the product-level rate taking precedence.
    </p>
  </InlineNotification>
  <div class="w-max">
    <NumberInput bind:value={workingCopy.meta[TAX_RATE].value} />
  </div>
</PageCard>
