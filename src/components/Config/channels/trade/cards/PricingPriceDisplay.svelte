<script lang="ts">
  import { PRICE_DISPLAY } from '@/components/Config/constants';
  import PageCard from '@/components/Config/PageCard.svelte';
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: ChannelWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [PRICE_DISPLAY]);

  let selected: boolean = workingCopy.meta[PRICE_DISPLAY].value === 'inclusive';

  $: {
    if (selected) {
      workingCopy.meta[PRICE_DISPLAY].value = 'inclusive';
    } else {
      workingCopy.meta[PRICE_DISPLAY].value = 'exclusive';
    }
  }
</script>

<PageCard title="Display prices with tax included?">
  <InlineNotification type="info">
    <p>
      Choose whether prices on this channel should be shown with tax included — even if your price
      list excludes tax.
    </p>
  </InlineNotification>
  <div class="w-max">
    <Toggle label="Inclusive" type="boolean" noLabelBorder bind:checked={selected} />
  </div>
</PageCard>
