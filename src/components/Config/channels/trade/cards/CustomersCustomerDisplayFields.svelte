<script lang="ts">
  import DynamicFieldSelector from '@/components/Controls/Select/FieldSelector/DynamicFieldSelector.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import Button from '@/components/Controls/Button.svelte';
  import IconAddCircle from '@/components/Icons/IconAddCircle.svelte';
  import PageCard from '@/components/Config/PageCard.svelte';
  import { sortObjectByKey } from '@/utils/sortObjectByKey';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import KeyOperatorValueRow from '@/components/Config/shared/components/KeyOperatorValueRow.svelte';
  import { ACCOUNT_DISPLAY } from '@/components/Config/constants';

  $: setDefaultConfigMeta(workingCopy, [ACCOUNT_DISPLAY]);

  export let workingCopy: ChannelWorkingCopy;

  let key: string | undefined;
  let value: string | undefined;
  let fields: Record<string, unknown> = {};

  $: disabled = !key || !value;

  $: if (workingCopy.meta[ACCOUNT_DISPLAY]) {
    const jsonString = workingCopy.meta[ACCOUNT_DISPLAY]?.value ?? '{}';
    fields = sortObjectByKey(JSON.parse(jsonString));
  }

  const deleteHandler = (key: string) => {
    delete fields[key];
    workingCopy.meta[ACCOUNT_DISPLAY].value = JSON.stringify(fields);
  };
  const addHandler = () => {
    fields[key as string] = value;
    key = undefined;
    value = undefined;
    workingCopy.meta[ACCOUNT_DISPLAY].value = JSON.stringify(sortObjectByKey(fields));
  };
</script>

<PageCard title="Custom fields">
  <InlineNotification type="info"
    >Display custom fields on the customers account page.</InlineNotification
  >
  <div>
    {#if fields}
      {#each Object.entries(fields) as [key, value] (key)}
        <KeyOperatorValueRow
          {key}
          operator="Displays"
          value={typeof value === 'string' ? value : JSON.stringify(value)}
          on:delete={() => deleteHandler(key)}
        />
      {/each}
    {/if}
  </div>

  <div class="flex gap-2">
    <TextInput bind:value={key} placeholder="Title" />
    <div class="w-48">
      <DynamicFieldSelector
        bind:value
        placeholder="Select Custom Field"
        entity="customer"
        group="meta"
        addPrefix
      />
    </div>

    <div class="w-36">
      <Button {disabled} variant="gray" icon={IconAddCircle} size="large" on:click={addHandler}
        >Add
      </Button>
    </div>
  </div>
</PageCard>
