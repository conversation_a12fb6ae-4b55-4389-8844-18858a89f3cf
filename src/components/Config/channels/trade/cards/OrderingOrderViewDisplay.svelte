<script lang="ts">
  import { type TableItem } from '@/components/Config/shared/components/ConfigTable.svelte';
  import PageCard from '@/components/Config/PageCard.svelte';
  import AddOrderColumn from '@/components/Config/channels/trade/components/OrderColumnsAddOrderColumn.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import KeyOperatorValueRow from '@/components/Config/shared/components/KeyOperatorValueRow.svelte';
  import { getOperatorLabelFromValue } from '@/models/config/channels/trade/ordering/columns';
  import type { OrderColumnItem } from '@/components/Config/channels/trade/cards/OrderingOrderColumns.svelte';
  import { ORDER_VIEW_DISPLAY } from '@/components/Config/constants';

  export let workingCopy: ChannelWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [ORDER_VIEW_DISPLAY]);

  export const orderColumnsToList = (
    columns: Record<string, string>
  ): { key: string; label: string }[] => {
    return Object.entries(columns).map(([label, key]) => ({ key, label }));
  };

  export const orderColumnsFromList = (
    columns: TableItem<OrderColumnItem>[]
  ): Record<string, string> => {
    const o: Record<string, string> = {};
    columns.forEach((v) => {
      const { key, label } = v;
      o[label] = key;
    });
    return o;
  };

  let columns: TableItem<OrderColumnItem>[];
  $: columns = orderColumnsToList(JSON.parse(workingCopy.meta[ORDER_VIEW_DISPLAY].value));

  const onDelete = (key: string) => {
    const newAggs = columns.filter((column) => column.key !== key);
    workingCopy.meta[ORDER_VIEW_DISPLAY].value = JSON.stringify(orderColumnsFromList(newAggs));
  };

  const onAdd = (item: TableItem<OrderColumnItem>) => {
    let newItems: TableItem<OrderColumnItem>[];

    const existingItem = columns.find((column) => column.key === item.key);

    if (existingItem) {
      newItems = columns.map((column) => (column.key === item.key ? item : column));
    } else {
      newItems = [...columns, item];
    }

    workingCopy.meta[ORDER_VIEW_DISPLAY].value = JSON.stringify(orderColumnsFromList(newItems));
  };
</script>

<PageCard title="Order View Display">
  <InlineNotification type="info">
    <p>Fields included on the order view page.</p>
  </InlineNotification>
  <div>
    {#if columns.length > 0}
      {#each columns as { key, label } (key)}
        <KeyOperatorValueRow
          key={label}
          operator={getOperatorLabelFromValue(key)}
          on:delete={() => onDelete(key)}
        />
      {/each}
    {/if}
  </div>
  <AddOrderColumn {onAdd} />
</PageCard>
