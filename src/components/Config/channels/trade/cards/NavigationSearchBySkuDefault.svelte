<script lang="ts">
  import { SEARCH_BY_SKU_DEFAULT } from '@/components/Config/constants';
  import PageCard from '@/components/Config/PageCard.svelte';
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: ChannelWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [SEARCH_BY_SKU_DEFAULT]);
</script>

<!-- TODO: this can just be a ConfigToggle wherever it is used -->
<PageCard title="Search by SKU checked by default">
  <InlineNotification type="info">
    <p>
      When enabled, this option will automatically check the 'Search by SKU' box adjacent to the
      search bar.
    </p>
  </InlineNotification>
  <div class="max-w-max">
    <Toggle
      label="Enable"
      bind:checked={workingCopy.meta[SEARCH_BY_SKU_DEFAULT].value}
      type="string"
      noLabelBorder
    />
  </div>
</PageCard>
