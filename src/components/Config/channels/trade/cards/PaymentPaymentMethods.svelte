<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import ConfigTable, {
    tableItemsToItems,
    type TableItem
  } from '@/components/Config/shared/components/ConfigTable.svelte';
  import PaymentMethodItemContent from '@/components/Config/channels/trade/components/PaymentMethodItemContent.svelte';
  import AddPaymentMethodItem from '@/components/Config/channels/trade/components/PaymentAddPaymentMethodItem.svelte';
  import {
    PAYMENT_METHOD_OPTIONS,
    type PaymentMethod
  } from '@/models/config/channels/trade/payment/methods';
  import PaymentEditModal from '@/components/Config/channels/trade/components/PaymentEditModal.svelte';
  import clone from 'lodash.clone';
  import { confirmModal } from '@/components/Modal/modal';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { PAYMENT_METHODS } from '@/components/Config/constants';

  export let workingCopy: ChannelWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [PAYMENT_METHODS]);

  let paymentMethods: PaymentMethod[];
  $: paymentMethods = JSON.parse(workingCopy.meta[PAYMENT_METHODS].value);

  const onDelete = (key: string) => {
    workingCopy.meta[PAYMENT_METHODS].value = JSON.stringify(
      paymentMethods.filter((pm) => pm.description !== key)
    );
  };

  let editModalOpen = false;
  let methodToEdit: PaymentMethod;
  const onEdit = (key: string) => {
    editModalOpen = true;
    const method = clone(paymentMethods.find((pm) => pm.description === key));
    if (method) {
      methodToEdit = method;
    }
  };

  const onEditSave = (updatedMethod: PaymentMethod) => {
    workingCopy.meta[PAYMENT_METHODS].value = JSON.stringify(
      paymentMethods.map((pm) =>
        pm.description === updatedMethod.description ? updatedMethod : pm
      )
    );
    editModalOpen = false;
  };

  const onAdd = async (paymentMethod: PaymentMethod) => {
    const existing = paymentMethods.find((pm) => pm.method === paymentMethod.method);
    if (existing) {
      if (
        await confirmModal({
          title: 'Duplicate Payment Method',
          description: `A payment method of the type '${PAYMENT_METHOD_OPTIONS[paymentMethod.method]}' already exists. Would you like to override it?`,
          actionText: 'Override'
        })
      ) {
        workingCopy.meta[PAYMENT_METHODS].value = JSON.stringify(
          paymentMethods.map((pm) => (pm.method === paymentMethod.method ? paymentMethod : pm))
        );
      }
    } else {
      workingCopy.meta[PAYMENT_METHODS].value = JSON.stringify([...paymentMethods, paymentMethod]);
    }
  };

  const onReorder = (newItems: TableItem<PaymentMethod>[]) => {
    workingCopy.meta[PAYMENT_METHODS].value = JSON.stringify(tableItemsToItems(newItems));
  };

  let items: TableItem<PaymentMethod>[];
  $: items = paymentMethods.map((pm) => ({ key: pm.description, ...pm }));
</script>

{#if editModalOpen && methodToEdit}
  <PaymentEditModal paymentMethod={methodToEdit} bind:open={editModalOpen} onSave={onEditSave} />
{/if}

<PageCard title="Payment Methods">
  <InlineNotification type="info">
    <p>
      <strong>Default payment methods available to your customers.</strong> Note: Each Customer can also
      be assigned their own payment methods. Example: 'Customer A' might be 'on account' and 'Customer
      B' may be cash only (credit card)
    </p>
  </InlineNotification>
  <ConfigTable
    displayComponent={PaymentMethodItemContent}
    {items}
    {onDelete}
    {onReorder}
    {onEdit}
    dnd
  >
    <AddPaymentMethodItem {onAdd} />
  </ConfigTable>
</PageCard>
