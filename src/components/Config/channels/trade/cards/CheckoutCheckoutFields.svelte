<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import ConfigTable from '@/components/Config/shared/components/ConfigTable.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import CheckoutFieldRowItem from '@/components/Config/channels/trade/components/CheckoutFieldRowItem.svelte';
  import { onMount } from 'svelte';
  import {
    type CheckoutField,
    type OldConfigCheckoutFields
  } from '@/components/Config/channels/trade/cards/checkoutFields';
  import AddCheckoutField from '@/components/Config/channels/trade/components/AddCheckoutField.svelte';
  import AddOrEditCheckoutFieldModal from '@/components/Config/channels/trade/components/AddOrEditCheckoutFieldModal.svelte';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import { CHECKOUT_FIELDS } from '@/components/Config/constants';

  export let workingCopy: ChannelWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [CHECKOUT_FIELDS]);

  let editing = false;
  let fieldToEdit: Partial<CheckoutField> = {};
  let checkoutFields: CheckoutField[] = [];

  onMount(() => {
    if (!workingCopy['meta'][CHECKOUT_FIELDS]) return (checkoutFields = []);
    let initialCheckoutFields = JSON.parse(
      workingCopy['meta'][CHECKOUT_FIELDS].value || '{}'
    ) as OldConfigCheckoutFields;
    checkoutFields = format(initialCheckoutFields);
  });

  $: allKeys = checkoutFields.map((field) => field.key);

  function onEdit(key: CheckoutField['key']) {
    fieldToEdit = checkoutFields.find((field) => field.key === key) as CheckoutField;
    editing = true;
  }

  function onReorder(items: CheckoutField[]) {
    checkoutFields = [...items];
    update();
  }

  function onDelete(key: string) {
    const tempFields = checkoutFields.filter((field) => field.key !== key);
    checkoutFields = [...tempFields];
    update();
  }

  function update() {
    workingCopy['meta']['checkout_fields'].value =
      checkoutFields.length > 0 ? JSON.stringify(deformat(checkoutFields)) : '';
  }

  function format(unformattedCheckoutFields: OldConfigCheckoutFields): CheckoutField[] {
    return Object.entries(unformattedCheckoutFields).map((field) => {
      const [fieldKey, fieldProperties] = field;
      return { key: fieldKey, ...fieldProperties } as CheckoutField;
    });
  }

  function deformat(formattedCheckoutFields: CheckoutField[]): OldConfigCheckoutFields {
    const checkoutFieldsReformatted = {} as OldConfigCheckoutFields;
    formattedCheckoutFields.forEach((field) => {
      const { key, ...otherAttributes } = field;
      checkoutFieldsReformatted[field['key']] = otherAttributes;
    });
    console.log('checkoutFieldsReformatted', checkoutFieldsReformatted);
    return checkoutFieldsReformatted;
  }

  function addNewField(event: CustomEvent) {
    const newField = event.detail;
    if (checkoutFields.some((field) => field.key === newField.key)) {
      alert('Key already exists');
      return;
    }
    checkoutFields = [...checkoutFields, newField];
    update();
  }

  function editField(event: CustomEvent) {
    const editedField = event.detail;
    const index = checkoutFields.findIndex((field) => field.key === editedField.key);
    if (index !== -1) {
      checkoutFields[index] = { ...editedField };
      update();
    }
    editing = false;
    fieldToEdit = {};
  }
</script>

<PageCard title="Checkout Fields">
  <AddCheckoutField bind:allKeys on:addField={addNewField} />
  {#if checkoutFields.length > 0}
    <ConfigTable
      dnd
      items={checkoutFields}
      {onReorder}
      {onDelete}
      {onEdit}
      displayComponent={CheckoutFieldRowItem}
    />
  {/if}
</PageCard>

{#if editing}
  <AddOrEditCheckoutFieldModal
    isEditing
    bind:open={editing}
    label={fieldToEdit?.type}
    field={fieldToEdit}
    on:edit={editField}
  />
{/if}
