<script lang="ts">
  import ConnectionApiKey from '@/components/Config/channels/takealotretail/cards/ConnectionAPIKey.svelte';
  import ConnectionUrl from '@/components/Config/channels/takealotretail/cards/ConnectionUrl.svelte';
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';

  export let workingCopy: ChannelWorkingCopy;
</script>

<PageWrapper>
  <ConnectionUrl bind:workingCopy />
  <ConnectionApiKey bind:workingCopy />
</PageWrapper>
