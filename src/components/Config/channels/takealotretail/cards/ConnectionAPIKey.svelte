<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import Secret from '@/components/Config/shared/components/Secret.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { API_KEY } from '@/components/Config/constants';

  export let workingCopy: ChannelWorkingCopy;
</script>

<PageCard title="API Key">
  <InlineNotification type="info">
    <p>To connect Stock2Shop to your Takealot Retail account, you'll need an API key.</p>
    <p>
      If you're unsure how to find your API key, you can find some information over <a
        class="font-bold underline"
        href="https://www.stock2shop.com/help/setup/takealot/"
        target="_blank">here</a
      >.
    </p>
  </InlineNotification>
  <Secret keyLabel="API Key" key={API_KEY} connectorId={workingCopy.id} connectorKind="channel" />
</PageCard>
