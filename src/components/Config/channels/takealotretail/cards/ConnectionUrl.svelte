<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import Select from '@/components/Controls/Select/Select.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import { URL } from '@/components/Config/constants';

  export let workingCopy: ChannelWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [URL]);

  const OPTIONS = [
    {
      value: 'https://supplier-api.takealot.com/v1/',
      label: 'V1 API'
    }
  ];
</script>

<PageCard title="URL">
  <InlineNotification type="info">
    Select the Stock2Shop Takealot Retail API version you want to use.
  </InlineNotification>
  <div class="max-w-[331px]">
    <Select
      sideLabel
      label="API Version"
      options={OPTIONS}
      bind:value={workingCopy.meta[URL].value}
    />
  </div>
</PageCard>
