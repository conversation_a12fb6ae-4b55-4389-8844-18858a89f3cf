<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import Button from '@/components/Controls/Button.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import KeyOperatorValueRow from '@/components/Config/shared/components/KeyOperatorValueRow.svelte';
  import FieldSelect from '@/components/Controls/Select/FieldSelector/FieldSelect.svelte';
  import { defaultFieldConvertor } from '@/models/fields/fieldConvertors';
  import { SHOPIFY_PRODUCT_META_ } from '@/components/Config/constants';

  export let workingCopy: ChannelWorkingCopy;

  let namespaceAndKey = '';
  let meta = '';
  let fieldSelectComponent: FieldSelect;

  $: buttonIsDisabled = !namespaceAndKey || !meta || !namespaceAndKey.includes('.');

  function add() {
    if (!namespaceAndKey.includes('.')) return;

    workingCopy.meta[SHOPIFY_PRODUCT_META_ + namespaceAndKey] = {
      key: SHOPIFY_PRODUCT_META_ + namespaceAndKey,
      value: meta
    };

    workingCopy.meta = workingCopy.meta;

    namespaceAndKey = '';
    meta = '';
    fieldSelectComponent.clear();
  }

  function onDelete(key: string) {
    delete workingCopy.meta[key];
    workingCopy.meta = workingCopy.meta;
  }

  function filterShopifyProductMeta(allMeta: ChannelWorkingCopy['meta']) {
    return Object.entries(allMeta).filter(([key]) => key.startsWith(SHOPIFY_PRODUCT_META_));
  }

  function extractNamespaceAndKeyFromMetaKey(metaKey: string) {
    const [key, namespace] = metaKey.replace(SHOPIFY_PRODUCT_META_, '').split('.');
    return { key, namespace };
  }
</script>

<PageCard title="Custom Product Meta">
  <InlineNotification type="info">
    <p>
      Shopify enables you to add custom metadata to your products, which can be configured in
      Stock2Shop for updates. Please note that this feature only supports updating existing
      metadata, not creating new entries.
    </p>
    <br />
    <p>How to create metafield definition on Shopify:</p>
    <br />
    <ol class="list-inside list-decimal">
      <li>From your Shopify admin, go to Settings > Custom data.</li>
      <li>In the Metafield definitions section, click "Products".</li>
      <li>Click the "Add definition" button in the top right corner.</li>
      <li>Give your Metafield a name and description.</li>
      <li>
        Set the type to be "Single line text". <strong
          >Note that Stock2Shop only supports sending data to single line text metafields.</strong
        >
      </li>
      <li>Click Save.</li>
      <li>
        A namespace and key will have been generated which you can copy and paste in the field
        below.
      </li>
    </ol>
    <br />
    <p>
      You can also find more information about Shopify's custom meta <a
        href="https://help.shopify.com/en/manual/custom-data/metafields/using-metafields"
        target="_blank"
        class="font-bold underline">here.</a
      >
    </p>
  </InlineNotification>
  <div>
    {#each filterShopifyProductMeta(workingCopy.meta) as [metaKey, metaValue]}
      {@const { namespace, key } = extractNamespaceAndKeyFromMetaKey(metaValue.key)}
      <KeyOperatorValueRow
        {key}
        operator={namespace}
        value={metaValue.value}
        on:delete={() => onDelete(metaKey)}
      />
    {/each}
  </div>
  <div class="grid grid-cols-[1fr_1fr_auto] gap-4">
    <TextInput bind:value={namespaceAndKey} placeholder="Namespace and key" />
    <FieldSelect
      bind:this={fieldSelectComponent}
      bind:value={meta}
      convertor={defaultFieldConvertor}
      entity="product"
    />
    <Button on:click={add} type="button" size="large" variant="gray" disabled={buttonIsDisabled}>
      Add
    </Button>
  </div>
  {#if namespaceAndKey !== '' && !namespaceAndKey.includes('.')}
    <InlineNotification type="warning">
      <p>Namespace and key must be in the format namespace.key</p>
    </InlineNotification>
  {/if}
</PageCard>
