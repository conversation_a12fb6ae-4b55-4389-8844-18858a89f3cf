<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import Select from '@/components/Controls/Select/Select.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import { URL } from '@/components/Config/constants';

  export let workingCopy: ChannelWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [URL]);

  const OPTIONS = [
    {
      value: 'https://seller-api.takealot.com/v2/',
      label: 'V2 API'
    }
  ];
</script>

<PageCard title="API Version">
  <InlineNotification type="info">
    Select the Takealot API version you want to use.
  </InlineNotification>
  <div class="max-w-[331px]">
    <Select
      sideLabel
      label="API Version"
      options={OPTIONS}
      bind:value={workingCopy.meta[URL].value}
    />
  </div>
</PageCard>
