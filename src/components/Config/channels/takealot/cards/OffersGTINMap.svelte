<script lang="ts">
  import { GTIN_MAP } from '@/components/Config/constants';
  import PageCard from '@/components/Config/PageCard.svelte';
  import Select from '@/components/Controls/Select/Select.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: ChannelWorkingCopy;

  setDefaultConfigMeta(workingCopy, [GTIN_MAP]);

  const FIELD_OPTIONS = [
    {
      value: '{{variant.barcode}}',
      label: 'Barcode'
    },
    {
      value: '{{variant.sku}}',
      label: 'SKU'
    }
  ];
</script>

<PageCard title="GTIN Map">
  <InlineNotification type="info">
    <p>To create offers, a GTIN (Global Trade Identification Number) is required.</p>
    <p>Map the GTIN field to the corresponding field in your feed that contains the GTIN.</p>
  </InlineNotification>

  <div class="max-w-96">
    <Select
      sideLabel
      label="Field"
      options={FIELD_OPTIONS}
      bind:value={workingCopy.meta[GTIN_MAP].value}
    />
  </div>
</PageCard>
