<script lang="ts">
  import ConnectionUrl from '@/components/Config/channels/takealot/cards/ConnectionUrl.svelte';
  import { API_KEY } from '@/components/Config/constants';
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import SecretCard from '@/components/Config/shared/cards/SecretCard.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: ChannelWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [API_KEY]);
</script>

<PageWrapper>
  <ConnectionUrl bind:workingCopy />

  <SecretCard
    title="API Key"
    key={API_KEY}
    connectorKind="channel"
    connectorId={workingCopy.id}
    info="This is the API key for your Takealot seller account."
  />
</PageWrapper>
