<script lang="ts">
  import { IDENTIFIER_FIELD, IDENTIFIER_TYPE } from '@/components/Config/constants';
  import PageCard from '@/components/Config/PageCard.svelte';
  import Select from '@/components/Controls/Select/Select.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import capitalize from 'lodash.capitalize';

  export let workingCopy: ChannelWorkingCopy;

  const IDENTIFIER_TYPE_OPTIONS = ['ASIN', 'EAN', 'GTIN', 'ISBN', 'JAN', 'MINSAN', 'SKU', 'UPC'];
  const IDENTIFIER_FIELD_OPTIONS = ['barcode', 'sku'];

  $: setDefaultConfigMeta(workingCopy, [IDENTIFIER_TYPE, IDENTIFIER_FIELD]);
</script>

<PageCard title="Identifier">
  <InlineNotification type="info">
    <p>
      The Amazon Product Identifier is a unique code assigned to each product listed on the
      marketplace. Amazon supports various identifier types depending on the product type and other
      factors. Here, you can map a product field in Stock2Shop to a specific identifier type to link
      it to Amazon listings. Select the appropriate product identifier from the dropdown and match
      it to the corresponding Stock2Shop product field.
    </p>
  </InlineNotification>
  <div class="max-w-[331px]">
    <Select
      label="Type"
      options={IDENTIFIER_TYPE_OPTIONS.map((o) => ({ value: o, label: o }))}
      bind:value={workingCopy.meta[IDENTIFIER_TYPE].value}
      sideLabel
    />
  </div>
  <div class="max-w-[331px]">
    <Select
      label="Field"
      options={IDENTIFIER_FIELD_OPTIONS.map((o) => ({ value: o, label: capitalize(o) }))}
      bind:value={workingCopy.meta[IDENTIFIER_FIELD].value}
      sideLabel
    />
  </div>
</PageCard>
