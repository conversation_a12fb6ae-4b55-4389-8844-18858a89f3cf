<script lang="ts">
  import { DELETE_OFFER_ENABLED } from '@/components/Config/constants';
  import PageCard from '@/components/Config/PageCard.svelte';
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: ChannelWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [DELETE_OFFER_ENABLED]);
</script>

<PageCard title="Delete Offers">
  <InlineNotification type="info">
    Enable to allow Stock2Shop to remove offers from Amazon Marketplace.
  </InlineNotification>
  <div class="max-w-max">
    <Toggle
      label="Delete Offers?"
      bind:checked={workingCopy.meta[DELETE_OFFER_ENABLED].value}
      type="string"
      noLabelBorder
    />
  </div>
</PageCard>
