<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';
  import Skeleton from '@/components/Loader/Skeleton.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import capitalize from 'lodash.capitalize';
  import { toast } from 'svelte-sonner';
  import { confirmModal } from '@/components/Modal/modal';
  import { HTTPError } from '@/lib/api/api';
  import {
    createDeleteConnectorWebhookMutation,
    createPutConnectorWebhookMutation
  } from '@/mutations/connectors.mutation';
  import { createConnectorHasWebhookQuery } from '@/queries/connectors.query';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { writable } from 'svelte/store';

  export let workingCopy: ChannelWorkingCopy;
  const enableOrdering = writable(false);

  const connectorHasWebhookQuery = createConnectorHasWebhookQuery({
    connectorId: workingCopy.id,
    connectorKind: 'channel'
  });

  const putConnectorWebhookMutation = createPutConnectorWebhookMutation({
    onError: () => {
      $enableOrdering = false;
    }
  });

  const deleteConnectorWebhookMutation = createDeleteConnectorWebhookMutation({
    onError: () => {
      $enableOrdering = true;
    }
  });

  $: {
    if (!$connectorHasWebhookQuery.isLoading) {
      if ($connectorHasWebhookQuery.isSuccess) {
        $enableOrdering = $connectorHasWebhookQuery.data;
      }
    }
  }

  const onChange = async (newValue: boolean) => {
    const verb = newValue ? 'enable' : 'disable';
    if (
      !(await confirmModal({
        title: `${capitalize(verb)} Ordering`,
        description: `Are you sure you want to ${verb} ordering?`,
        actionText: capitalize(verb)
      }))
    ) {
      return ($enableOrdering = !newValue);
    }

    if (newValue) {
      toast.promise(
        $putConnectorWebhookMutation.mutateAsync({
          connectorId: workingCopy.id,
          connectorKind: 'channel'
        }),
        {
          loading: 'Enabling ordering...',
          success: 'Ordering enabled',
          error: (e) => (e instanceof HTTPError ? e.message : 'Failed to enable ordering')
        }
      );
    } else {
      toast.promise(
        $deleteConnectorWebhookMutation.mutateAsync({
          connectorId: workingCopy.id,
          connectorKind: 'channel'
        }),
        {
          loading: 'Disabling ordering...',
          success: 'Ordering disabled',
          error: (e) => (e instanceof HTTPError ? e.message : 'Failed to disable ordering')
        }
      );
    }
  };
</script>

<PageCard title="Enable Ordering">
  <InlineNotification type="info">
    <p>
      Enable this option to allow Stock2Shop to receive and process orders from Amazon. Orders with
      an 'unshipped' status will be treated as paid and sent to your source, if configured.
    </p>
  </InlineNotification>
  {#if $connectorHasWebhookQuery.isLoading}
    <Skeleton shimmer class="h-10 w-full" />
  {:else if $connectorHasWebhookQuery.isError}
    <InlineNotification type="error">
      <p>
        <strong>
          Please check that you've authorized Stock2Shop to communicate with your Amazon reseller
          account
        </strong>
      </p>
    </InlineNotification>
  {:else}
    <div class="max-w-max">
      <Toggle
        bind:checked={$enableOrdering}
        label="Enable ordering"
        noLabelBorder
        {onChange}
        disabled={$deleteConnectorWebhookMutation.isPending ||
          $putConnectorWebhookMutation.isPending}
      />
    </div>
  {/if}
</PageCard>
