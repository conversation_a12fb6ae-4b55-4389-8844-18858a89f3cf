<script lang="ts">
  import { SYNC_PRICE, SYNC_STOCK } from '@/components/Config/constants';
  import PageCard from '@/components/Config/PageCard.svelte';
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: ChannelWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [SYNC_PRICE, SYNC_STOCK]);
</script>

<PageCard title="Offer Sync">
  <div class="flex gap-2">
    <div class="flex flex-1 flex-col gap-3">
      <InlineNotification type="info">
        <div class=" my-auto">
          Enable to automatically sync pricing updates with to Amazon Marketplace.
        </div>
      </InlineNotification>
      <div class="max-w-max">
        <Toggle
          label="Sync Price"
          bind:checked={workingCopy.meta[SYNC_PRICE].value}
          type="string"
          noLabelBorder
        />
      </div>
    </div>
    <div class="flex flex-1 flex-col gap-3">
      <InlineNotification type="info">
        <div class=" my-auto">
          Enable to automatically sync stock quantity updates to Amazon Marketplace.
        </div>
      </InlineNotification>
      <div class="max-w-max">
        <Toggle
          label="Sync Stock"
          bind:checked={workingCopy.meta[SYNC_STOCK].value}
          type="string"
          noLabelBorder
        />
      </div>
    </div>
  </div>
</PageCard>
