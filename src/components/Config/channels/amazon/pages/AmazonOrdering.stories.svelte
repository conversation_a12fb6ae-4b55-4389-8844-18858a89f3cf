<script context="module" lang="ts">
  import { Template, Story } from '@storybook/addon-svelte-csf';
  import AmazonOrdering from './AmazonOrdering.svelte';

  export const meta = {
    component: AmazonOrdering
  };
</script>

<Template let:args>
  <AmazonOrdering
    workingCopy={{
      ...args
    }}
  />
</Template>

<Story
  name="Default"
  args={{
    meta: {}
  }}
/>

<Story
  name="Order from date set"
  args={{
    meta: {
      order_from_date: {
        key: 'order_from_date',
        value: '2024-07-01T10:00:00.000Z'
      }
    }
  }}
/>

<Story
  name="Default customer code set"
  args={{
    meta: {
      param_default_customer_code: {
        key: 'param_default_customer_code',
        value: 'TEST_CODE'
      }
    }
  }}
/>

<Story
  name="Enable price from stock 2 shop Toggle:On"
  args={{
    meta: {
      order_set_system_price: {
        key: 'order_set_system_price',
        value: 'true'
      }
    }
  }}
/>

<Story
  name="Price Tier Selected"
  args={{
    meta: {
      order_set_system_price: {
        key: 'order_set_system_price',
        value: 'true'
      },
      order_use_system_price_tier: {
        key: 'order_use_system_price_tier',
        value: 'RRP'
      }
    }
  }}
/>

<Story
  name="Price Tier(Tax incl) Selected"
  args={{
    meta: {
      order_set_system_price: {
        key: 'order_set_system_price',
        value: 'true'
      },
      order_use_system_price_tier_incl: {
        key: 'order_use_system_price_tier_incl',
        value: 'retail'
      }
    }
  }}
/>
