<script context="module" lang="ts">
  import { Template, Story } from '@storybook/addon-svelte-csf';
  import AmazonProductDiscovery from './AmazonProductDiscovery.svelte';

  export const meta = {
    component: AmazonProductDiscovery
  };

  // More accurate data to be added later
  const baseWorkingCopy = {
    id: 999,
    created: '2023-02-17 12:06:12.000000',
    description: 'NOT A REAL CHANNEL',
    modified: '2023-02-17 13:10:48.000000',
    client_id: 999,
    active: 1,
    type: 'TO_BE_DECIDED',
    price_tier: '',
    qty_availability: null,
    sync_token: '0'
  };
</script>

<Template let:args>
  <AmazonProductDiscovery
    workingCopy={{
      ...baseWorkingCopy,
      isChannel: true,
      meta: {}
    }}
  />
</Template>

<!-- What other states does this component have? -->
<!-- Could we get a test Amazon store set up to properly document this config? -->

<Story name="Default" args={{}} />
