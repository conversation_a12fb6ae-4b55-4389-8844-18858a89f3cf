<script context="module" lang="ts">
  import AmazonConnection from './AmazonConnection.svelte';
  import { Story, Template } from '@storybook/addon-svelte-csf';

  export const meta = {
    component: AmazonConnection
  };
</script>

<Template let:args>
  <AmazonConnection
    workingCopy={{
      type: 'amazon',
      ...args
    }}
  />
</Template>

<Story
  name="Unauthorised"
  args={{
    meta: {
      oauth2_is_authorised: {
        value: 'false'
      }
    }
  }}
/>

<Story
  name="Authorised"
  args={{
    meta: {
      oauth2_is_authorised: {
        value: 'true'
      }
    }
  }}
/>
