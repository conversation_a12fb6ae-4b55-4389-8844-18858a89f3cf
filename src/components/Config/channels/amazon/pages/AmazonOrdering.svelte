<script lang="ts">
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import OrderFromDate from '@/components/Config/shared/cards/OrderFromDate.svelte';
  import DefaultCustomerCode from '@/components/Config/channels/shared/cards/OrderingDefaultCustomerCode.svelte';
  import UsePriceFromS2S from '@/components/Config/channels/shared/cards/OrderingUsePriceFromS2S.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import OrderingEnableOrdering from '@/components/Config/channels/amazon/cards/OrderingEnableOrdering.svelte';

  export let workingCopy: ChannelWorkingCopy;
</script>

<PageWrapper>
  <OrderingEnableOrdering bind:workingCopy />
  <OrderFromDate bind:workingCopy />
  <DefaultCustomerCode bind:workingCopy />
  <UsePriceFromS2S bind:workingCopy />
</PageWrapper>
