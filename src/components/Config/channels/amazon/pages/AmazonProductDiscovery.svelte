<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import Button from '@/components/Controls/Button.svelte';
  import { confirmModal } from '@/components/Modal/modal';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { QueueSummaryItem } from '@/models/queue/summary';
  import { AMAZON_SEARCH_INSTRUCTION } from '@/models/tasks/tasks';
  import { createPostTaskMutation } from '@/mutations/tasks.mutation';
  import { createQueueSummaryQuery } from '@/queries/queue.query';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';

  export let workingCopy: ChannelWorkingCopy;

  const postTaskMutation = createPostTaskMutation({
    onSettled: () => {
      $queueSummaryQuery.refetch();
    }
  });

  const evaluateRunning = (systemQueueSummary?: QueueSummaryItem[]) =>
    systemQueueSummary?.find((summary) => summary.instruction === AMAZON_SEARCH_INSTRUCTION);

  const queueSummaryQuery = createQueueSummaryQuery({
    refetchInterval: ({ state }) =>
      evaluateRunning(state.data?.system_queue_summary) ? RETRY_DELAY : false
  });

  const RETRY_DELAY = 2000;

  const postTask = async () => {
    if (
      await confirmModal({
        title: `Run Product Discovery`,
        description: `Are you sure you want to search Amazon's catalog using your product identifiers?`,
        actionText: 'Run Discovery'
      })
    ) {
      $postTaskMutation.mutate({
        instruction: AMAZON_SEARCH_INSTRUCTION,
        payload: {
          connector_id: workingCopy.id
        }
      });
    }
  };

  $: isRunning = !!evaluateRunning($queueSummaryQuery.data?.system_queue_summary);
</script>

<PageWrapper>
  <PageCard title="Product Discovery">
    <InlineNotification type="info">
      <p>
        Ensure the correct <a
          class="font-bold underline"
          href="/config/channels/amazon/{workingCopy.id}/offers">Product Identifier type</a
        > is set and that each of your products has the appropriate code.
      </p>
      <p>
        Then click 'Run Discovery' to check which of your products are already listed on Amazon
        Marketplace.
      </p>
      <p>
        Stock2Shop will retrieve the Product Type for each found product and update the product's
        attributes accordingly.
      </p>
    </InlineNotification>
    <InlineNotification type="warning">
      This process involves searching through Amazon's catalog and may take some time, so please be
      patient.
    </InlineNotification>
    <div class=" flex gap-2">
      <Button
        size="large"
        disabled={$postTaskMutation.isPending || $queueSummaryQuery.isFetching || isRunning}
        on:click={postTask}
        variant="outline"
        >{isRunning ? 'Discovery Running' : 'Run Discovery'}
      </Button>
      <a
        href="/search/products/field/meta_amz_product_type?completeness=complete&from=0&modifiedFromTo=&size=50&title=Products+matched+to+Amazon"
      >
        <Button size="large" variant="outline">View sellable products</Button>
      </a>
    </div>
  </PageCard>
</PageWrapper>
