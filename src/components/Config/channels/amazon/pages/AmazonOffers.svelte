<script lang="ts">
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import DeleteOffer from '@/components/Config/channels/amazon/cards/OffersDeleteOffer.svelte';
  import Identifier from '@/components/Config/channels/amazon/cards/OffersIdentifier.svelte';
  import OfferSync from '@/components/Config/channels/amazon/cards/OffersOfferSync.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';

  export let workingCopy: ChannelWorkingCopy;
</script>

<PageWrapper>
  <Identifier bind:workingCopy />
  <OfferSync bind:workingCopy />
  <DeleteOffer bind:workingCopy />
</PageWrapper>
