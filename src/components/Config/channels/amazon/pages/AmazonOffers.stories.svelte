<script context="module" lang="ts">
  import { Template, Story } from '@storybook/addon-svelte-csf';
  import AmazonOffers from './AmazonOffers.svelte';

  export const meta = {
    component: AmazonOffers
  };
</script>

<Template let:args>
  <AmazonOffers
    workingCopy={{
      ...args
    }}
  />
</Template>

<Story
  name="Default"
  args={{
    meta: {}
  }}
/>

<Story
  name="Type Selected"
  args={{
    meta: {
      identifier_type: {
        value: 'ASIN'
      }
    }
  }}
/>

<Story
  name="Field Selected"
  args={{
    meta: {
      identifier_field: {
        value: 'barcode'
      }
    }
  }}
/>

<Story
  name="Sync Price Toggle:On"
  args={{
    meta: {
      sync_price: {
        value: 'true'
      }
    }
  }}
/>

<Story
  name="Sync Stock Toggle:On"
  args={{
    meta: {
      sync_stock: {
        value: 'true'
      }
    }
  }}
/>

<Story
  name="Delete Offer Toggle:On"
  args={{
    meta: {
      delete_offer_enabled: {
        value: 'true'
      }
    }
  }}
/>
