<script lang="ts">
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import PriceFieldMap from '@/components/Config/channels/shopify2/cards/PricingPriceFieldMap.svelte';
  import AllowZeroPrice from '@/components/Config/channels/shopify2/cards/PricingAllowZeroPrice.svelte';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import {
    PRODUCT_FIELD_COMPARE_AT_PRICE,
    PRODUCT_FIELD_PRICE
  } from '@/components/Config/constants';

  export let workingCopy: ChannelWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [PRODUCT_FIELD_PRICE, PRODUCT_FIELD_COMPARE_AT_PRICE]);
</script>

<PageWrapper>
  <PriceFieldMap
    metaKey={PRODUCT_FIELD_PRICE}
    bind:workingCopy
    title="Product Price Field"
    description="Select which Stock2Shop product field should update the price in Shopify."
  />
  {#if workingCopy.meta[PRODUCT_FIELD_PRICE]?.value}
    <AllowZeroPrice bind:workingCopy />
  {/if}
  <PriceFieldMap
    metaKey={PRODUCT_FIELD_COMPARE_AT_PRICE}
    bind:workingCopy
    title="Product Compare at Price Field"
    description="Specify the product field that will be used to update the 'Compare at Price' on Shopify."
  />
</PageWrapper>
