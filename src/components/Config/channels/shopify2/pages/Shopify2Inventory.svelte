<script lang="ts">
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import StockCalculator from '@/components/Config/channels/shared/cards/InventoryStockCalculator.svelte';
  import StoreWideQty from '@/components/Config/channels/shared/cards/StoreWideQty.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import ProductLocation from '@/components/Config/channels/shopify2/cards/InventoryProductLocation.svelte';
  import InventoryStock from '@/components/Config/shared/cards/InventoryStock.svelte';

  export let workingCopy: ChannelWorkingCopy;

  let atleastOneLocationSelected: false;
</script>

<PageWrapper>
  <ProductLocation bind:workingCopy bind:atleastOneLocationSelected />
  {#if atleastOneLocationSelected}
    <StockCalculator bind:workingCopy />
    <StoreWideQty name="Quantity Upper Limit" metaKey="qty_limit_upper" bind:workingCopy>
      <strong>Upper limit for channel quantity.</strong> This value will also restrict the quantity orderable.
    </StoreWideQty>
  {/if}
  <InventoryStock bind:workingCopy />
</PageWrapper>
