<script lang="ts">
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import PageCard from '@/components/Config/PageCard.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import CreditYard from '@/components/Config/channels/shopify2/cards/AppsCreditYard.svelte';

  export let workingCopy: ChannelWorkingCopy;
</script>

<PageWrapper>
  <PageCard title="App Settings">
    <InlineNotification type="info">
      <p>
        App settings are configurations for specific Shopify-supported applications. To access and
        use these settings, ensure the application is enabled and properly set up within Shopify.
      </p>
    </InlineNotification>
  </PageCard>
  <!-- No need to bind because we aren't changing any meta -->
  <CreditYard {workingCopy} />
</PageWrapper>
