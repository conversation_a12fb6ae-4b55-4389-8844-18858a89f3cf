<script lang="ts">
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import OrderFromDate from '@/components/Config/shared/cards/OrderFromDate.svelte';

  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';

  import DefaultCustomerCode from '@/components/Config/channels/shared/cards/OrderingDefaultCustomerCode.svelte';
  import UsePriceFromS2S from '@/components/Config/channels/shared/cards/OrderingUsePriceFromS2S.svelte';
  import EnableOrdering from '@/components/Config/channels/shopify2/cards/OrderingEnableOrdering.svelte';

  export let workingCopy: ChannelWorkingCopy;

  let orderingEnabled = false;

  function handleChangeEnableOrdering(event: CustomEvent) {
    orderingEnabled = event.detail;
  }
</script>

<PageWrapper>
  <EnableOrdering bind:workingCopy on:toggle-ordering={handleChangeEnableOrdering} />
  {#if orderingEnabled}
    <OrderFromDate bind:workingCopy />
    <DefaultCustomerCode bind:workingCopy />
    <UsePriceFromS2S bind:workingCopy />
  {/if}
</PageWrapper>
