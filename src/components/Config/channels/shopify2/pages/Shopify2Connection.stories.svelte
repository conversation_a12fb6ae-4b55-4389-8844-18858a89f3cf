<script context="module" lang="ts">
  import Shopify2Connection from '@/components/Config/channels/shopify2/pages/Shopify2Connection.svelte';
  import { Story, Template } from '@storybook/addon-svelte-csf';

  export const meta = {
    component: Shopify2Connection
  };
</script>

<script lang="ts">
  import StorybookWrapper from '@/components/StorybookWrapper.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';

  const baseWorkingCopy = {
    id: 2116,
    created: '2024-11-01 07:57:46.000000',
    description: 'Spoify(REFUNDS)',
    modified: '2024-11-11 14:37:38.000000',
    client_id: 21,
    active: 1,
    type: 'shopify2',
    price_tier: 'RRP',
    qty_availability: null,
    sync_token: '0',
    isChannel: true,
    meta: {
      domain: {
        id: 50864,
        key: 'domain',
        value: 'https://yusuf-s2s.myshopify.com/',
        template_name: null
      },
      queue_fulfill_order: {
        id: 54645,
        key: 'queue_fulfill_order',
        value: 'true',
        template_name: null
      },
      default_fulfillmentservice_id: {
        id: 54646,
        key: 'default_fulfillmentservice_id',
        value: '181',
        template_name: null
      },
      product_field_location_123: {
        id: 54647,
        key: 'product_field_location_123',
        value: 'qty',
        template_name: null
      }
    }
  } as ChannelWorkingCopy;
</script>

<Template>
  <StorybookWrapper>
    <!-- See mock channel-sources-repo for various states -->
    <Shopify2Connection workingCopy={baseWorkingCopy} />
  </StorybookWrapper>
</Template>

<Story name="Connection Page" />
