<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import { priceFieldsConvertor } from '@/models/fields/fieldConvertors';
  import FieldSelect from '@/components/Controls/Select/FieldSelector/FieldSelect.svelte';

  export let metaKey: string;
  export let workingCopy: ChannelWorkingCopy;
  export let title: string;
  export let description: string;
  export let label: string = '';

  $: setDefaultConfigMeta(workingCopy, [metaKey]);
</script>

<PageCard {title}>
  <InlineNotification type="info">
    {description}
  </InlineNotification>
  <div class="max-w-[181px]">
    <FieldSelect
      placeholder="Do not update"
      {label}
      bind:value={workingCopy.meta[metaKey].value}
      convertor={priceFieldsConvertor}
      entity="product"
    />
  </div>
</PageCard>
