<script lang="ts">
  import { PRODUCT_ALLOW_ZERO_PRICE } from '@/components/Config/constants';
  import PageCard from '@/components/Config/PageCard.svelte';
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: ChannelWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [PRODUCT_ALLOW_ZERO_PRICE]);
</script>

<PageCard title="Allow Zero Price Updates">
  <InlineNotification type="info">
    <p>Allow price updates on Shopify, even if the price is set to zero.</p>
  </InlineNotification>

  <div class="max-w-max">
    <Toggle
      bind:checked={workingCopy.meta[PRODUCT_ALLOW_ZERO_PRICE].value}
      type="string"
      label="Allow Zero Price Updates?"
      noLabelBorder
    />
  </div>
</PageCard>
