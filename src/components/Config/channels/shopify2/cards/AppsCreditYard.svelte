<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import Secret from '@/components/Config/shared/components/Secret.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import type { Secret as SecretType } from '@/models/secrets';
  import { CREDITS_YARD_MERCHANT_API_KEY } from '@/components/Config/constants';

  export let workingCopy: ChannelWorkingCopy;

  const CONNECTOR_KIND: SecretType['connector_kind'] = 'channel';
</script>

<PageCard title="Credits Yard">
  <InlineNotification type="info">
    <p>
      CreditsYard is a store credit management system designed to manage customer returns and
      facilitate store credit usage, encouraging repurchases and retaining funds within the
      business.
    </p>
    <p class="pt-3">
      You can find information on how to find your API key <a
        href="https://creditsyard.helpyard.io/article/custom-api"
        class="underline"><strong>here</strong></a
      >
    </p>
  </InlineNotification>
  <Secret
    keyLabel="API Key"
    key={CREDITS_YARD_MERCHANT_API_KEY}
    connectorKind={CONNECTOR_KIND}
    connectorId={workingCopy.id}
  />
</PageCard>
