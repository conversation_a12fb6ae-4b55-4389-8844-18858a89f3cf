<script lang="ts">
  import TextInput from '@/components/Controls/TextInput.svelte';
  import Button from '@/components/Controls/Button.svelte';
  import PageCard from '@/components/Config/PageCard.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import { qtyFieldsConvertor } from '@/models/fields/fieldConvertors';
  import FieldSelect from '@/components/Controls/Select/FieldSelector/FieldSelect.svelte';
  import type { Meta } from '@/models/meta';
  import KeyOperatorValueRow from '@/components/Config/shared/components/KeyOperatorValueRow.svelte';
  import { PRODUCT_FIELD_LOCATION_ } from '@/components/Config/constants';

  export let workingCopy: ChannelWorkingCopy;
  export let atleastOneLocationSelected: boolean;

  let locations: Meta[] = loadLocations();
  let key: string;
  let value: string;
  let fieldSelectComponent: FieldSelect;

  $: disabled = !key || !value;
  $: atleastOneLocationSelected = locations.length > 0;

  $: {
    // If there are no locations, we need to trigger the setDefaultConfigMeta function
    if (locations.length === 0) workingCopy.meta = workingCopy.meta;
    locations.forEach((location: Meta) => {
      setDefaultConfigMeta(workingCopy, [PRODUCT_FIELD_LOCATION_ + location.key]);
      workingCopy.meta[PRODUCT_FIELD_LOCATION_ + location.key].value = location.value;
    });
  }

  const deleteHandler = (key: string) => {
    delete workingCopy.meta[key];
    locations = [...locations.filter((location) => PRODUCT_FIELD_LOCATION_ + location.key !== key)];
  };

  const addHandler = () => {
    locations = [
      ...locations,
      {
        key: key as string,
        value: value as string
      }
    ];
    fieldSelectComponent.clear();
    key = '';
    value = '';
  };

  function loadLocations() {
    return Object.entries(workingCopy.meta)
      .filter(([key]) => key.startsWith(PRODUCT_FIELD_LOCATION_))
      .map(([key, value]) => {
        return {
          key: key.replace(PRODUCT_FIELD_LOCATION_, ''),
          value: value.value
        };
      });
  }
</script>

<PageCard title="Update Stock Levels">
  <InlineNotification type="info">
    <p>
      To correctly configure your Shopify integration we need the Shopify Location ID for your stock
      location. When you create a Shopify shop you will always have at least one Stock location.
    </p>
    <p>
      For more info about locations <a
        target="_blank"
        class="font-bold underline"
        href="https://help.shopify.com/en/manual/fulfillment/setup/locations/setting-up-locations"
        >here</a
      >
    </p>
  </InlineNotification>
  <!-- ToDo this needs to be refactored into our ConfigTable component  -->
  <div>
    {#if locations.length > 0}
      {#each locations as { key, value } (key)}
        <KeyOperatorValueRow
          label="Location"
          {key}
          operator="uses"
          {value}
          on:delete={() => deleteHandler(PRODUCT_FIELD_LOCATION_ + key)}
        />
      {/each}
    {/if}
  </div>
  <div class="flex gap-2">
    <TextInput bind:value={key} placeholder="Shopify Location ID" />
    <div class="w-48">
      <FieldSelect
        bind:this={fieldSelectComponent}
        bind:value
        convertor={qtyFieldsConvertor}
        entity="product"
      />
    </div>

    <div class="w-36">
      <Button {disabled} variant="gray" size="large" on:click={addHandler}>Add</Button>
    </div>
  </div>
</PageCard>
