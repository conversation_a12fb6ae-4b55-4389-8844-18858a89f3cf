<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';
  import Skeleton from '@/components/Loader/Skeleton.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import capitalize from 'lodash.capitalize';
  import { toast } from 'svelte-sonner';
  import { confirmModal } from '@/components/Modal/modal';
  import { HTTPError } from '@/lib/api/api';
  import {
    createDeleteConnectorWebhookMutation,
    createPutConnectorWebhookMutation
  } from '@/mutations/connectors.mutation';
  import { createConnectorHasWebhookQuery } from '@/queries/connectors.query';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { writable } from 'svelte/store';
  import { createEventDispatcher } from 'svelte';

  export let workingCopy: ChannelWorkingCopy;

  const enableOrdering = writable(false);
  const dispatch = createEventDispatcher();

  const connectorHasWebhookQuery = createConnectorHasWebhookQuery({
    connectorId: workingCopy.id,
    connectorKind: 'channel'
  });

  const putConnectorWebhookMutation = createPutConnectorWebhookMutation({
    onError: () => {
      $enableOrdering = false;
    },
    onSettled: () => {
      dispatch('toggle-ordering', $enableOrdering);
    }
  });

  const deleteConnectorWebhookMutation = createDeleteConnectorWebhookMutation({
    onError: () => {
      $enableOrdering = true;
    },
    onSettled: () => {
      dispatch('toggle-ordering', $enableOrdering);
    }
  });

  $: {
    if (!$connectorHasWebhookQuery.isLoading && $connectorHasWebhookQuery.isSuccess) {
      $enableOrdering = $connectorHasWebhookQuery.data;
    }
  }

  async function handleChangeEnableOrdering(newValue: boolean) {
    const verb = newValue ? 'enable' : 'disable';

    const confirmed = await confirmModal({
      title: `${capitalize(verb)} Ordering`,
      description: `Are you sure you want to ${verb} ordering?`,
      actionText: capitalize(verb)
    });

    if (!confirmed) return ($enableOrdering = !newValue);

    if (newValue) {
      // ADD THE WEBHOOK
      toast.promise(
        $putConnectorWebhookMutation.mutateAsync({
          connectorId: workingCopy.id,
          connectorKind: 'channel'
        }),
        {
          loading: 'Enabling ordering...',
          success: () => {
            return 'Ordering enabled';
          },
          error: (e) => (e instanceof HTTPError ? e.message : 'Failed to enable ordering')
        }
      );
    } else {
      // DELETE THE WEBHOOK
      toast.promise(
        $deleteConnectorWebhookMutation.mutateAsync({
          connectorId: workingCopy.id,
          connectorKind: 'channel'
        }),
        {
          loading: 'Disabling ordering...',
          success: () => {
            dispatch('toggle-ordering', $enableOrdering);
            return 'Ordering disabled';
          },
          error: (e) => (e instanceof HTTPError ? e.message : 'Failed to disable ordering')
        }
      );
    }
  }

  function convertErrorToReadableMessage(errorMessage: string): string {
    if (errorMessage) {
      if (errorMessage.includes('Access token')) {
        return 'You need to enable access through the "Connection" setting before you can enable ordering';
      } else if (errorMessage.includes('No fields configured')) {
        return 'You need to configure at least one field that should be updated under the "Pricing" setting';
      } else if (errorMessage.includes('Invalid API key')) {
        return 'Invalid Access Token. Please confirm the Access Token entered under the "Connection" setting is correct';
      }
    }
    return 'Failed to load ordering configuration status';
  }
</script>

<PageCard title="Enable Ordering">
  <InlineNotification type="info">
    <p>Toggle to allow Stock2shop to receive orders from Shopify.</p>
  </InlineNotification>
  {#if $connectorHasWebhookQuery.isLoading}
    <Skeleton shimmer class="h-10 w-full" />
  {:else if $connectorHasWebhookQuery.isError}
    <InlineNotification type="error">
      {convertErrorToReadableMessage($connectorHasWebhookQuery.error?.message)}
    </InlineNotification>
  {:else}
    <div class="max-w-max">
      <Toggle
        label="Enable ordering"
        noLabelBorder
        bind:checked={$enableOrdering}
        onChange={handleChangeEnableOrdering}
      />
    </div>
  {/if}
</PageCard>
