<script lang="ts">
  import { DOMAIN } from '@/components/Config/constants';
  import PageCard from '@/components/Config/PageCard.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: ChannelWorkingCopy;
  $: setDefaultConfigMeta(workingCopy, [DOMAIN]);
</script>

<PageCard title="Domain">
  <InlineNotification type="info">
    <p>
      Provide your Shopify store’s domain (e.g., for fun-shirts.myshopify.com, enter 'fun-shirts').
    </p>
  </InlineNotification>
  <TextInput placeholder="fun-shirts" bind:value={workingCopy.meta[DOMAIN].value} />
</PageCard>
