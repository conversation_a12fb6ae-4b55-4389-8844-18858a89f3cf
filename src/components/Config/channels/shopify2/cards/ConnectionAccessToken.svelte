<script lang="ts">
  import { ACCESS_TOKEN } from '@/components/Config/constants';
  import PageCard from '@/components/Config/PageCard.svelte';
  import Secret from '@/components/Config/shared/components/Secret.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';

  export let workingCopy: ChannelWorkingCopy;
</script>

<PageCard title="Access Token">
  <InlineNotification type="info">
    <p>
      The access token is used to authenticate with the Shopify API. To generate an access token,
      follow the instructions in the Shopify documentation <a
        href="https://www.shopify.com/za/partners/blog/17056443-how-to-generate-a-shopify-api-token"
        target="_blank"
        class="font-bold underline">here</a
      >.
    </p>
    <p class="my-1"><strong>Access scopes required:</strong></p>
    <ul>
      <li>read_inventory</li>
      <li>write_inventory</li>
      <li>read_all_orders</li>
      <li>read_orders</li>
      <li>write_products</li>
      <li>write_assigned_fulfillment_orders</li>
      <li>write_merchant_managed_fulfillment_orders</li>
      <li>write_third_party_fulfillment_orders</li>
      <li></li>
      <li>fulfill_and_ship_orders</li>
    </ul>
  </InlineNotification>
  <Secret
    keyLabel="Access Token"
    key={ACCESS_TOKEN}
    connectorKind="channel"
    connectorId={workingCopy.id}
  />
</PageCard>
