## config

Config components update properties on the config model.
For example, channel rules or channel meta.

Config has a type (channels, sources, fulfillments), a connector (trade, xero etc), an ID
and a config which is the group of things being edited (e.g. product rules, customer display etc).

The page hands each page component a workingCopy of its versioned object.
They only need to modify the working copy and then the page will handle the rest.
