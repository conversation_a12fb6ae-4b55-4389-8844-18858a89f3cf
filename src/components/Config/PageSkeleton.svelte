<script lang="ts">
  import type { ConfigNavLink } from '@/models/nav/configNav';
  import Skeleton from '@/components/Loader/Skeleton.svelte';
  import NavConfig from '@/components/Nav/NavConfig.svelte';
  import type { ConfigKey } from '@/models/config/config';
  import IconBack from '@/components/Icons/IconBack.svelte';
  import Button from '@/components/Controls/Button.svelte';
  import PageCardSkeleton from '@/components/Config/PageCardSkeleton.svelte';
  import PageWrapper from '@/components/Config/PageWrapper.svelte';

  export let links: ConfigNavLink[];
  export let configKey: ConfigKey;
</script>

<!--
@component
Loading for config page
-->
<div class="mb-6 flex w-full items-center justify-between xl:max-w-full">
  <div class="flex w-4/5 items-center gap-2">
    <Button size="large" variant="gray" on:click={() => window.history.back()} icon={IconBack} />
    <Skeleton shimmer class="h-10 w-36" />
  </div>
  <div class="flex min-w-fit items-center gap-3">
    <Button size="large" variant="ghost" disabled>Cancel</Button>
    <Button size="large" disabled>Save Changes</Button>
  </div>
</div>

<div class="flex h-full gap-4">
  <div class="h-min w-64 min-w-[260px] rounded bg-white p-6 shadow">
    <NavConfig {configKey} {links} />
  </div>

  <PageWrapper>
    <PageCardSkeleton />
  </PageWrapper>
</div>
