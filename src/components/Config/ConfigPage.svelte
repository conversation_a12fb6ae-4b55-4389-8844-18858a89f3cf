<script lang="ts">
  import { AUTH_CONFIG, CONFIG_NAV } from '@/models/nav/configNav';
  import PageHeader from '@/components/PageHeader.svelte';
  import NavConfig from '@/components/Nav/NavConfig.svelte';
  import { createVersionedObjectMutation } from '@/mutations/versioned-object.mutation';
  import Button from '@/components/Controls/Button.svelte';
  import LabelStatusNotification from '@/components/Controls/LabelStatusNotification.svelte';
  import IconCheckCircle from '@/components/Icons/IconCheckCircle.svelte';
  import PageSkeleton from '@/components/Config/PageSkeleton.svelte';
  import { checkConfigNeedsAuth, type ConfigKey } from '@/models/config/config';
  import { queryClient } from '@/utils/client';
  import { createConfigQuery } from '@/queries/config.query';
  import {
    createVersionedObjectQuery,
    versionedObjectQueryKeys
  } from '@/queries/versioned-object.query';
  import { safeNavigationCheck } from '@/utils/navigation';
  import { writable } from 'svelte/store';
  import { setContext } from 'svelte';
  import { goto } from '$app/navigation';
  import { SITE_TITLE } from '@/constants';

  // This page is destroyed and remounted each time the config key changes.

  // Key allows us to reference which part of config we are editing
  // e.g. channel or source and which config we are editing e.g. rules or meta
  export let configKey: ConfigKey;

  // The links for the connector and the current link the page is on
  const links = CONFIG_NAV[configKey.kind][configKey.type];
  const link = links.filter((link) => link.config === configKey.config)[0];

  // Versioned object store
  // This store extends the version controlled class and adds
  // in an override for the save and getServerData methods
  // Always fetch server data for the store if it has not been fetch before.
  // This means pages will always have workingCopy.
  const versionedObjectStore = link.makeVersionedObjectStore(configKey);

  // invalidate the query to force it to refetch each time the page is loaded.
  queryClient.resetQueries({
    queryKey: versionedObjectQueryKeys.get(versionedObjectStore.getQueryKey())
  });
  const query = createVersionedObjectQuery(versionedObjectStore);

  // Page title will be description from source or channel
  // Fetch config for get the title
  // This does mean that config is fetched twice.
  const configQuery = createConfigQuery();
  $: pageTitle = $configQuery.data
    ? `${link.title} (${$configQuery.data[configKey.kind][configKey.id]['description']})`
    : link.title;

  // The mutation on the versioned object will call the save method.
  // This will run whatever custom logic found in the save method.
  $: mutation = createVersionedObjectMutation(versionedObjectStore);
  $: handleSave = async () => {
    // Runs the versioned objects save method
    $mutation.mutate();

    // In this case the versioned object query is the same as the config query, lets refresh that too.
    // This does mean an additional call to the server, but it ensures the data is up-to-date.
    await queryClient.invalidateQueries({ queryKey: ['config'] });
  };

  // Place save handler in context for the rare occasion that
  // a child component needs to trigger a save.
  const handleSaveStore = writable(handleSave);
  $: handleSaveStore.set(handleSave);
  setContext('handleSave', handleSaveStore);

  // Reverts the store back by refreshing.
  $: handleRevert = () => {
    location.reload();
    // STUBBED - this currently wont trigger components to update back
    // to their initial state so using full window reload until we can look at this in #821
    // $versionedObjectStore.revertChanges();
  };

  // Lock the form when saving or loading new data
  $: locked = $mutation.isPending;

  // show/hide the save / cancel buttons
  $: disabled = !$versionedObjectStore.changesMade || locked;

  const changesMade = writable(false);
  $: $changesMade = $versionedObjectStore.changesMade;
  safeNavigationCheck(changesMade, () => {});

  $: isLoading =
    $configQuery.isLoading ||
    $query.isLoading ||
    typeof $versionedObjectStore.workingCopy === 'undefined';
  $: needsAuth = !isLoading && checkConfigNeedsAuth($versionedObjectStore.workingCopy);

  $: {
    if (needsAuth) {
      goto(AUTH_CONFIG);
    }
  }
</script>

<svelte:head>
  <title>{pageTitle} | {SITE_TITLE}</title>
</svelte:head>

{#if isLoading}
  <PageSkeleton {links} {configKey} />
{:else}
  <form class="group" on:submit|preventDefault={handleSave}>
    <PageHeader title={pageTitle} hasBackButton={true} backUrl="/connectors">
      {#if $mutation.isPending}
        <LabelStatusNotification status="info" size="medium">
          Saving Configuration...
        </LabelStatusNotification>
      {/if}
      {#if $mutation.isSuccess && $mutation.data}
        <LabelStatusNotification
          status={$mutation.data.success ? 'success' : 'error'}
          icon={IconCheckCircle}
          size="medium"
        >
          {$mutation.data.message}
        </LabelStatusNotification>
      {/if}
      {#if $mutation.isError && $mutation.error}
        <LabelStatusNotification status="error" size="medium">
          {$mutation.error.message}
        </LabelStatusNotification>
      {/if}
      <Button size="large" variant="ghost" {disabled} on:click={handleRevert}>Cancel</Button>
      <Button size="large" {disabled} type="submit">Save Changes</Button>
    </PageHeader>
    <div class="flex gap-4">
      {#if !needsAuth}
        <div class="sticky top-0 h-min min-w-[260px] rounded bg-white p-6 shadow">
          <NavConfig {configKey} {links} />
        </div>
      {/if}
      <svelte:component
        this={link.Component}
        bind:workingCopy={$versionedObjectStore.workingCopy}
      />
    </div>
  </form>
{/if}
