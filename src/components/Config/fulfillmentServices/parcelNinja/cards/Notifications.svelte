<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import ComboboxInternal from '@/components/Controls/Select/Combobox/ComboboxInternal.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { FulfillmentServiceWorkingCopy } from '@/stores/fulfillmentServiceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import { onMount } from 'svelte';

  export let workingCopy: FulfillmentServiceWorkingCopy;

  const STATUS_NOTIFICATION_STATUS_CODES_KEY =
    'notify_pn_status_codes' as keyof FulfillmentServiceWorkingCopy['meta'];

  const STATUS_CODE_OPTIONS = [
    { value: 'Awaiting stock', name: 'Awaiting stock' },
    { value: 'In picking queue', name: 'In picking queue' },
    { value: 'Ready to be picked', name: 'Ready to be picked' },
    { value: 'In packing queue', name: 'In packing queue' },
    { value: 'Ready to be packed', name: 'Ready to be packed' },
    { value: 'Awaiting courier pickup', name: 'Awaiting courier pickup' },
    { value: 'Dispatched with courier', name: 'Dispatched with courier' },
    { value: 'Delivered', name: 'Delivered' },
    { value: 'Awaiting collection', name: 'Awaiting collection' },
    { value: 'Collected', name: 'Collected' },
    { value: 'Unable to deliver', name: 'Unable to deliver' },
    { value: 'Order on hold', name: 'Order on hold' },
    { value: 'Unsuccessful', name: 'Unsuccessful' },
    { value: 'Courier Collection Unsuccessful', name: 'Courier Collection Unsuccessful' },
    { value: 'Courier Delivery Unsuccessful', name: 'Courier Delivery Unsuccessful' }
  ];

  let selectedStatusCodes: string[];

  onMount(() => {
    if (
      !workingCopy.meta[STATUS_NOTIFICATION_STATUS_CODES_KEY] ||
      !workingCopy.meta[STATUS_NOTIFICATION_STATUS_CODES_KEY].value
    ) {
      selectedStatusCodes = [];
    } else {
      selectedStatusCodes = workingCopy.meta[STATUS_NOTIFICATION_STATUS_CODES_KEY].value.split(',');
    }
  });

  $: if (selectedStatusCodes)
    workingCopy.meta[STATUS_NOTIFICATION_STATUS_CODES_KEY].value = selectedStatusCodes.join(',');

  $: setDefaultConfigMeta(workingCopy, [STATUS_NOTIFICATION_STATUS_CODES_KEY]);
</script>

<PageCard title="Notification Settings">
  <InlineNotification type="info">
    <p>
      Webhooks are created for all major events in the Parcelninja process. Each event has a status
      code. You can select status codes for which you would like to receive email notifications.
    </p>
    <p>
      You can find more information on status codes and which events they correspond to <a
        class="font-bold underline"
        href="https://parcelninja.docs.apiary.io/#reference/webhooks"
        target="_blank">here.</a
      >
    </p>
  </InlineNotification>
  <ComboboxInternal
    label="Status Codes"
    sideLabel
    canAdd
    multiple
    options={STATUS_CODE_OPTIONS}
    placeholder="Select status codes"
    bind:valueArray={selectedStatusCodes}
  />
</PageCard>
