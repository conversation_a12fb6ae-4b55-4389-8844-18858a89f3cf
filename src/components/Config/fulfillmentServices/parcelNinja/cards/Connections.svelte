<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import Secret from '@/components/Config/shared/components/Secret.svelte';
  import Select from '@/components/Controls/Select/Select.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { FulfillmentServiceWorkingCopy } from '@/stores/fulfillmentServiceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: FulfillmentServiceWorkingCopy;

  const META_API_URL = 'api_url' as keyof FulfillmentServiceWorkingCopy['meta'];
  const META_API_USERNAME = 'api_username' as keyof FulfillmentServiceWorkingCopy['meta'];
  const META_API_PASSWORD = 'api_password' as keyof FulfillmentServiceWorkingCopy['meta'];

  const API_OPTIONS = [
    {
      label: 'Parcel Ninja',
      value: 'https://qa-storeapi.parcelninja.com/api/v1/'
    },
    {
      label: 'DP World',
      value: 'https://ecom-sfs.dpworld.com/dpconnect/mw-service/api/ext/v1/s2s/api/v1/'
    }
  ];

  $: setDefaultConfigMeta(workingCopy, [META_API_URL]);
</script>

<PageCard title="API config">
  <InlineNotification type="info">
    <p>
      In order for Stock2Shop to communicate with your your ParcelNinja fulfillment service, we need
      your API username and password. You can find information of how to find these details <a
        class="font-bold underline"
        href="https://www.stock2shop.com/help/setup/parcelninja/"
        target="_blank">here.</a
      >
    </p>
  </InlineNotification>
  <Secret
    key={META_API_USERNAME}
    keyLabel="API Username"
    connectorKind="fulfillmentservice"
    connectorId={workingCopy.fulfillmentservice_id}
  />

  <Secret
    key={META_API_PASSWORD}
    keyLabel="API Password"
    connectorKind="fulfillmentservice"
    connectorId={workingCopy.fulfillmentservice_id}
  />

  <div class="max-w-96">
    <Select
      required
      label="API"
      sideLabel
      options={API_OPTIONS}
      bind:value={workingCopy.meta[META_API_URL].value}
    />
  </div>
</PageCard>
