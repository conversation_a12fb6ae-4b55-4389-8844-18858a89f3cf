<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import Select from '@/components/Controls/Select/Select.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { FulfillmentServiceWorkingCopy } from '@/stores/fulfillmentServiceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: FulfillmentServiceWorkingCopy;

  const META_FOR_COLLECTION = 'collection' as keyof FulfillmentServiceWorkingCopy['meta'];

  const METHOD_OPTIONS = [
    { label: 'Delivery', value: 'false' },
    { label: 'Collection', value: 'true' }
  ];

  $: setDefaultConfigMeta(workingCopy, [META_FOR_COLLECTION]);
</script>

<PageCard title="Collection">
  <InlineNotification type="info">
    <p>
      Select the default delivery method to use. In most cases, this will be 'Delivery,' where
      parcels are sent directly to your customer. Alternatively, you can select 'Collection,' which
      requires the customer to pick up their parcel from the ParcelNinja warehouse.
    </p>
    <br />
    <p>
      <strong>Note:</strong> orders which have this property set will override this setting
    </p>
  </InlineNotification>
  <div class="max-w-96">
    <Select
      required
      label="Method"
      sideLabel
      options={METHOD_OPTIONS}
      bind:value={workingCopy.meta[META_FOR_COLLECTION].value}
    />
  </div>
</PageCard>
