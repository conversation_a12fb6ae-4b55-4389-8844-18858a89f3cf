<script context="module" lang="ts">
  import FulfillmentServicesCollection from '@/components/Config/fulfillmentServices/parcelNinja/pages/FulfillmentServicesCollection.svelte';
  import { Story, Template } from '@storybook/addon-svelte-csf';
  export const meta = {
    component: FulfillmentServicesCollection
  };
</script>

<script lang="ts">
  import StorybookWrapper from '@/components/StorybookWrapper.svelte';
  import type { FulfillmentServiceWorkingCopy } from '@/stores/fulfillmentServiceMetaVersionedObject';
  const workingCopy = {
    fulfillmentservice_id: 8,
    type: 'parcelninja',
    meta: {
      api_username: {
        id: 12,
        key: 'api_username',
        value: 'cPRRGunctY2cwiiYYt7wHRua2N7i1I67wktAcWEhKZ4=',
        template_name: null
      },
      api_password: {
        id: 13,
        key: 'api_password',
        value: 'SmS7hloRRvRasnO6yil9X4XWAIBfMtD61bN+IiYH+UA=',
        template_name: null
      },
      api_url: {
        id: 14,
        key: 'api_url',
        value: 'https://qa-storeapi.parcelninja.com/api/v1/',
        template_name: null
      },
      delivery_rules: {
        id: 648,
        key: 'delivery_rules',
        value:
          '[\n          {\n            "special_service_code": "0",\n            "delivery_quote_id": "0",\n            "field": "{{#shipping_lines}}{{title}}{{/shipping_lines}}",\n            "operator": "contains",\n            "value": "next Day"\n          }\n        ]',
        template_name: null
      },
      quote_id: {
        id: 649,
        key: 'quote_id',
        value: '1',
        template_name: null
      }
    },
    description: 'Parcel Ninja Demo'
  } as FulfillmentServiceWorkingCopy;
</script>

<Template>
  <StorybookWrapper>
    <!-- See mock channel-sources-repo for various states -->
    <FulfillmentServicesCollection {workingCopy} configKey="" />
  </StorybookWrapper>
</Template>

<Story name="Connection Page" />
