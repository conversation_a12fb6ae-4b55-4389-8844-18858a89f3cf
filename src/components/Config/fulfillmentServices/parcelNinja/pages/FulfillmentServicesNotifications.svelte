<script lang="ts">
  import Notifications from '@/components/Config/fulfillmentServices/parcelNinja/cards/Notifications.svelte';
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import type { FulfillmentServiceWorkingCopy } from '@/stores/fulfillmentServiceMetaVersionedObject';

  export let workingCopy: FulfillmentServiceWorkingCopy;
  export let configKey;
</script>

<PageWrapper>
  <Notifications bind:workingCopy />
</PageWrapper>
