<script lang="ts">
  import type { FulfillmentServiceWorkingCopy } from '@/stores/fulfillmentServiceMetaVersionedObject';
  import Connections from '@/components/Config/fulfillmentServices/parcelNinja/cards/Connections.svelte';
  import PageWrapper from '@/components/Config/PageWrapper.svelte';

  export let workingCopy: FulfillmentServiceWorkingCopy;
  export let configKey;
</script>

<PageWrapper>
  <Connections bind:workingCopy />
</PageWrapper>
