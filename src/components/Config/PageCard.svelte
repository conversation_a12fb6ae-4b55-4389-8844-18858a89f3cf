<script lang="ts">
  export let title: string | undefined = undefined;
  export let withPadding: boolean = true;
  export let noGap = false;
</script>

<!--
@component
Card inside a page
-->
<div
  class="flex w-full flex-col rounded bg-white p-6 text-regular text-neutral-700 shadow"
  class:p-6={withPadding}
  class:gap-6={!noGap}
>
  {#if title}
    <div
      class="w-full text-smaller font-bold uppercase leading-4 tracking-widest text-brand-action"
    >
      {title}
    </div>
  {/if}
  <slot />
</div>
