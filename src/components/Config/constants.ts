export const SYNC_STOCK = 'sync_stock';

export const ADD_ORDER_STATUS = 'add_order_status';

export const ADDRESS_LINE_1 = 'address_line_1';

export const ADDRESS_LINE_2 = 'address_line_2';

export const ADDRESS_LINE_3 = 'address_line_3';

export const AGGREGATIONS = 'aggregations';

export const ALLOW_BULK_ORDER = 'allow_bulk_order';

export const ALLOW_QUICK_ORDER = 'allow_quick_order';

export const API_KEY = 'api_key';

export const API_URL = 'api_url';

export const API_WEBHOOK_VERSION = 'api_webhook_version';

export const ACCOUNT_DISPLAY = 'account_display';

export const ACCESS_TOKEN = 'access_token';

export const ATTRIBUTE_SET_ID = 'attribute_set_id';

export const AUTHENTICATION = 'authentication';

export const CATEGORY_PARENT_ID = 'category_parent_id';

export const CHANNEL_ORDER_CODE_PREFIX = 'channel_order_code_prefix';

export const CHANNEL_ORDER_CODE_SEQUENCE = 'channel_order_code_sequence';

export const CHECKOUT_FIELDS = 'checkout_fields';

export const CHECKOUT_DISPLAY_CREDIT_LIMIT = 'checkout_display_credit_limit';

export const CHECKOUT_CREDIT_LIMIT_EXCEEDED_CONFIRMATION_MESSAGE =
  'checkout_credit_limit_exceeded_confirmation_message';

export const CONSUMER_KEY = 'consumer_key';

export const CONSUMER_SECRET = 'consumer_secret';

export const CREATE_CUSTOMER_ENABLED = 'create_customer_enabled';

export const CREATE_IMAGE_ENABLED = 'create_image_enabled';

export const CREATE_ORDER_ENABLED = 'create_order_enabled';

export const CREATE_PRODUCT_STATUS = 'create_product_status';

export const CREATE_PRODUCTS_ENABLED = 'create_products_enabled';

export const CREDITS_YARD_MERCHANT_API_KEY = 'credits_yard_merchant_api_key';

export const CRON_GET_PRODUCTS_SCHEDULE = 'cron_get_products_schedule';

export const DEFAULT_CUSTOMER_CODE = 'default_customer_code';

export const DEFAULT_FULFILLMENTSERVICE_ID = 'default_fulfillmentservice_id';

export const DEFAULT_ORDER_STATUS = 'default_order_status';

export const DEFAULT_SHIPPING_CODE = 'default_shipping_code';

export const DELETE_IMAGE_ENABLED = 'delete_image_enabled';

export const DELETE_OFFER_ENABLED = 'delete_offer_enabled';

export const DELETE_PRODUCTS = 'delete_products';

export const DELETE_PRODUCTS_ENABLED = 'delete_products_enabled';

export const DEACTIVATE_PRODUCTMETA_PREFIX_CSV = 'deactivate_productmeta_prefix_csv';

export const DISPLAY_NAME = 'display_name';

export const DOMAIN = 'domain';

export const DOMAIN_ALIAS = 'domain_alias';

export const EMAIL = 'email';

export const ELASTIC_QUERY_FIELDS = 'elastic_query_fields';

export const ELASTIC_SUGGEST_FIELDS = 'elastic_suggest_fields';

export const GTIN_MAP = 'gtin_map';

export const HIDE_AVAILABILITY_ENABLED = 'hide_availability_enabled';

export const IDENTIFIER_FIELD = 'identifier_field';

export const IDENTIFIER_TYPE = 'identifier_type';

export const LOGIN_REDIRECT = 'login_redirect';

export const MANAGE_CATEGORY_ENABLED = 'manage_category_enabled';

export const MANAGE_CUSTOMER_ADDRESS = 'manage_customer_address';

export const MANAGE_IMAGES = 'manage_images';

export const MANAGE_STOCK_STATUS = 'manage_stock_status';

export const MANAGE_VISIBILITY = 'manage_visibility';

export const MINIMUM_ORDER_QTY = 'minimum_order_qty';

export const MIN_ORDER_AMOUNT = 'min_order_amount';

export const OAUTH2_IS_AUTHORISED = 'oauth2_is_authorised';

export const ORDER_CODE_FIELD = 'order_code_field';

export const ORDER_COLUMNS = 'order_columns';
export const ORDER_DELIVERY_DATE = 'order_delivery_date';

export const ORDER_FROM_DATE = 'order_from_date';

export const ORDER_SET_SYSTEM_PRICE = 'order_set_system_price';

export const ORDER_SHIPPING_METHOD_NAME = 'order_shipping_method_name';

export const ORDER_TYPE = 'order_type';

export const ORDER_USE_SYSTEM_PRICE_TIER = 'order_use_system_price_tier';

export const ORDER_USE_SYSTEM_PRICE_TIER_INCL = 'order_use_system_price_tier_incl';

export const ORDER_VIEW_DISPLAY = 'order_view_display';

export const OVER_ORDER_ENABLED = 'over_order_enabled';

export const PARAM_BRANCH_CODE = 'param_branch_code';

export const PARAM_CHECK_CREDIT_LIMIT = 'param_check_credit_limit';

export const PARAM_COMPANY_ID = 'param_company_id';
export const PARAM_COMPLETE_CREDIT_NOTE = 'param_complete_credit_note';
export const PARAM_COMPANY_PASSWORD = 'param_company_password';

export const PARAM_COMPLETE_INVOICE = 'param_complete_invoice';

export const PARAM_CONTACT_SOURCE_CUSTOMER = 'param_contact_source_customer';

export const PARAM_CREATE_CUSTOMER_ENABLED = 'param_create_customer_enabled';
export const PARAM_CURRENCY = 'param_currency';

export const PARAM_DEFAULT_CUSTOMER_CODE = 'param_default_customer_code';

export const PARAM_DEFAULT_TAX_CODE = 'param_default_tax_code';

export const PARAM_DELIVERY_METHOD = 'param_delivery_method';
export const PARAM_DUE_DATE_DAYS = 'param_due_date_days';

export const PARAM_GL_DISCOUNT_CODE = 'param_gl_discount_code';

export const PARAM_GL_SHIPPING_CODE = 'param_gl_shipping_code';

export const PARAM_GL_SHIPPING_DISCOUNT_CODE = 'param_gl_shipping_discount_code';

export const PARAM_IGNORE_DUE_DATE = 'param_ignore_due_date';

export const PARAM_IGNORE_SHIPPING = 'param_ignore_shipping';

export const PARAM_IGNORE_WAREHOUSE_CODE = 'param_ignore_warehouse_code';

export const PARAM_LOCATION_CODE = 'param_location_code';
export const PARAM_LOT_ENABLED = 'param_lot_enabled';

export const PARAM_LOT_STATUS_IDS = 'param_lot_status_ids';

export const PARAM_LOT_WAREHOUSE_ID = 'param_lot_warehouse_id';

export const PARAM_NEGATIVE_STOCK_DISABLED = 'param_negative_stock_disabled';

export const PARAM_NEW_CUSTOMER_AREA_CODE = 'param_new_customer_area_code';

export const PARAM_NEW_CUSTOMER_GROUP_CODE = 'param_new_customer_group_code';

export const PARAM_NEW_CUSTOMER_PRICE_LIST_CODE = 'param_new_customer_price_list_code';

export const PARAM_NEW_CUSTOMER_REPRESENTATIVE_CODE = 'param_new_customer_representative_code';

export const PARAM_OPERATOR = 'param_operator';
export const PARAM_OPERATOR_PASSWORD = 'param_operator_password';
export const PARAM_ORDER_DOCUMENT_TYPE = 'param_order_document_type';
export const PARAM_ORDER_TYPE = 'param_order_type';

export const PARAM_ORDER_PROJECT_CODE = 'param_order_project_code';

export const PARAM_ORDER_REPRESENTATIVE_CODE = 'param_order_representative_code';

export const PARAM_PROCESS_CREDIT_NOTE = 'param_process_credit_note';

export const PARAM_PROCESS_INVOICE = 'param_process_invoice';

export const PARAM_PROCESS_RESERVED = 'param_process_reserved';

export const PARAM_SALES_REP_ID = 'param_sales_rep_id';
export const PARAM_SHIPPING_CODE = 'param_shipping_code';

export const PARAM_SKIP_IMAGE_HASH = 'param_skip_image_hash';

export const PARAM_TAX_TYPE = 'param_tax_type';

export const PARAM_TAX_TYPE_EXEMPT = 'param_tax_type_exempt';

export const PARAM_USE_CHANNEL_ORDER_CODE = 'param_use_channel_order_code';

export const PARAM_USE_CREDIT_NOTE_DISCOUNT = 'param_use_credit_note_discount';

export const PARAM_USE_CUSTOMER_ADDRESS = 'param_use_customer_address';

export const PARAM_USE_CUSTOMER_CURRENCY = 'param_use_customer_currency';

export const PARAM_USE_CUSTOMER_PAYMENT_TERMS = 'param_use_customer_payment_terms';

export const PARAM_USE_LINE_ITEM_UOM = 'param_use_line_item_uom';

export const PARAM_WAREHOUSE_CODE = 'param_warehouse_code';

export const PASSWORD = 'password';

export const PAYMENT_METHODS = 'payment_methods';

export const PHONE = 'phone';

export const PRICE_DISPLAY = 'price_display';

export const PRICE_INCLUSIVE = 'price_inclusive';

export const PRICE_TIER = 'price_tier';

export const PRODUCT_ALLOW_ZERO_PRICE = 'product_allow_zero_price';

export const PRODUCT_FIELD_COMPARE_AT_PRICE = 'product_field_compare_at_price';

export const PRODUCT_FIELD_LOCATION_ = 'product_field_location_';

export const PRODUCT_FIELD_PRICE = 'product_field_price';

export const PRODUCT_INACTIVE_KEYWORD = 'product_inactive_keyword';

export const QTY_LIMIT_UPPER = 'qty_limit_upper';

export const QTY_MULTIPLES_OF = 'qty_multiples_of';

export const QTY_UPPER_LIMIT = 'qty_upper_limit';

export const QUEUE_FULFILL_ORDER = 'queue_fulfill_order';

export const QUICK_ORDER_COLUMNS = 'quick_order_columns';

export const REDUCE_INVENTORY_FIELD = 'reduce_inventory_field';

export const REDUCE_INVENTORY_WHEN_ORDER_PROCESSING = 'reduce_inventory_when_order_processing';

export const SEARCH_BY_SKU_DEFAULT = 'search_by_sku_default';

export const SHIPPING_METHODS = 'shipping_methods';

export const SHOPIFY_PRODUCT_META_ = 'shopify_product_meta_';

export const SHOW_AVAILABILITY_UNITS = 'show_availability_units';

export const SYNC = 'sync';

export const SYNC_PRICE = 'sync_price';

export const SYNC_PRODUCTS_ENABLED = 'sync_products_enabled';

export const TAX_RATE = 'tax_rate';

export const TAX_RATE_SHIPPING = 'tax_rate_shipping';

export const TERMS = 'terms';

export const URL = 'url';

export const USERNAME = 'username';

export const XERO_QUOTE_EXPIRY_DAYS = 'xero_quote_expiry_days';

export const XERO_QUOTE_TERMS = 'xero_quote_terms';

export const XERO_TENANT_ID = 'xero_tenant_id';
