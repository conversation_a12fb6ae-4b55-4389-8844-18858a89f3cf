<script context="module">
  import ConfigPage from '@/components/Config/ConfigPage.svelte';
  import { Story } from '@storybook/addon-svelte-csf';
  export const meta = {
    title: 'components/Config/ConfigPage',
    component: ConfigPage
  };
</script>

<script lang="ts">
</script>

<!-- @component
   A few stories to test ConfigPage, see */pages stories for page stories
 -->

<!-- Sources -->

<!-- Xero -->
<Story name="Source: Xero: Unauthorised">
  <ConfigPage
    configKey={{
      id: 1577,
      type: 'xero',
      kind: 'sources',
      config: 'connection',
      description: 'Test Product Sharing'
    }}
  />
</Story>

<Story name="Source: Xero: Connection">
  <ConfigPage
    configKey={{
      id: 1578,
      type: 'xero',
      kind: 'sources',
      config: 'connection'
    }}
  />
</Story>

<Story name="Source: Xero: Products">
  <ConfigPage
    configKey={{
      id: 1578,
      type: 'xero',
      kind: 'sources',
      config: 'products'
    }}
  />
</Story>

<Story name="Source: Xero: Orders">
  <ConfigPage
    configKey={{
      id: 1578,
      type: 'xero',
      kind: 'sources',
      config: 'ordering'
    }}
  />
</Story>

<Story name="Source: Xero: Customers">
  <ConfigPage
    configKey={{
      id: 1578,
      type: 'xero',
      kind: 'sources',
      config: 'customers'
    }}
  />
</Story>
