<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: ChannelWorkingCopy | SourceWorkingCopy;
  export let metaKey: string;
  export let title: string;
  export let description: string = '';
  export let label: string;

  $: setDefaultConfigMeta(workingCopy, [metaKey]);
</script>

<!-- @component
 Component for all of our toggle configurations.
 -->

<PageCard {title}>
  {#if description}
    <InlineNotification type="info">{@html description}</InlineNotification>
  {/if}
  <div class="max-w-max">
    <Toggle {label} bind:checked={workingCopy.meta[metaKey].value} type="string" noLabelBorder />
  </div>
</PageCard>
