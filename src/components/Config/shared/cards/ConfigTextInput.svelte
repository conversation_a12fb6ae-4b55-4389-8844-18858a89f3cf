<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: ChannelWorkingCopy | SourceWorkingCopy;
  export let metaKey: string;
  export let title: string;
  export let description: string = '';
  export let label: string = '';
  export let sideLabel = false;

  $: setDefaultConfigMeta(workingCopy, [metaKey]);
</script>

<!-- @component
   Component for all of our toggle configurations.
   -->

<PageCard {title}>
  {#if description}
    <InlineNotification type="info">{description}</InlineNotification>
  {/if}
  <div class="max-w-max">
    <TextInput {label} {sideLabel} bind:value={workingCopy.meta[metaKey].value} />
  </div>
</PageCard>
