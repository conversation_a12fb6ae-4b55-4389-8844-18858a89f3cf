<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import { CREATE_ORDER_ENABLED } from '@/components/Config/constants';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';

  export let workingCopy: SourceWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [CREATE_ORDER_ENABLED]);
</script>

<PageCard title="Create order">
  <InlineNotification type="info">
    Allow Stock2Shop to create orders received from channels on {workingCopy.description}.
  </InlineNotification>
  <div class="max-w-max">
    <Toggle
      label="Create Order?"
      bind:checked={workingCopy.meta[CREATE_ORDER_ENABLED].value}
      type="string"
      noLabelBorder
    />
  </div>
</PageCard>
