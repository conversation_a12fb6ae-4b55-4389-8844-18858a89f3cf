<script lang="ts">
  import {
    REDUCE_INVENTORY_FIELD,
    REDUCE_INVENTORY_WHEN_ORDER_PROCESSING
  } from '@/components/Config/constants';
  import PageCard from '@/components/Config/PageCard.svelte';
  import DynamicFieldSelector from '@/components/Controls/Select/FieldSelector/DynamicFieldSelector.svelte';
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: ChannelWorkingCopy | SourceWorkingCopy;

  // set initial dropdown value based on the current meta value
  let selectedWarehouse = workingCopy.meta[REDUCE_INVENTORY_FIELD]?.value.replace('qty_', '') || '';

  $: {
    setDefaultConfigMeta(workingCopy, [
      REDUCE_INVENTORY_WHEN_ORDER_PROCESSING,
      REDUCE_INVENTORY_FIELD
    ]);
    if (workingCopy.meta[REDUCE_INVENTORY_WHEN_ORDER_PROCESSING].value === 'false') {
      workingCopy.meta[REDUCE_INVENTORY_FIELD].value = '';
    } else {
      // This is an ugly hack, but to implement something nice requires refactoring the dynamic field selector component which is a large component used in many places.
      // Perhaps the solution to to build DynamicFieldDropdown which is a simpler version of the dynamic field selector with less functionality.
      workingCopy.meta[REDUCE_INVENTORY_FIELD].value =
        selectedWarehouse !== 'qty' ? `qty_${selectedWarehouse}` : 'qty';
    }
  }
</script>

<PageCard title="Reduce Stock">
  <InlineNotification type="info">
    <p>
      Enable this option to automatically reduce stock levels in Stock2Shop when an order is
      received and marked as paid. Once updated, all connected channels will reflect the new stock
      levels.
    </p>
  </InlineNotification>
  <div class="max-w-max">
    <Toggle
      label="Enable"
      noLabelBorder
      type="string"
      bind:checked={workingCopy.meta[REDUCE_INVENTORY_WHEN_ORDER_PROCESSING].value}
    />
  </div>
  {#if workingCopy.meta[REDUCE_INVENTORY_WHEN_ORDER_PROCESSING].value === 'true'}
    <InlineNotification type="info">
      Choose the warehouse from which Stock2Shop should reduce stock.
    </InlineNotification>
    <div class="max-w-96">
      <DynamicFieldSelector
        entity="product"
        group="qty"
        sideLabel
        bind:value={selectedWarehouse}
        title="Warehouse"
        required
        canClear
        defaultOption={{
          name: 'Default',
          value: 'qty'
        }}
      />
    </div>
  {/if}
</PageCard>
