<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import ConfigToggleGroup from '@/components/Config/shared/components/ConfigToggleGroup.svelte';
  import NumberInput from '@/components/Controls/NumberInput.svelte';
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: ChannelWorkingCopy;
  export let metaKey: string;
  export let toggleLabel: string;
  export let inputLabel: string;
  export let name: string;
  export let inputType: 'increment' | 'currency' = 'increment';

  let enabled = !!workingCopy.meta[metaKey];

  $: {
    if (!enabled) {
      delete workingCopy.meta[metaKey];
    } else {
      setDefaultConfigMeta(workingCopy, [metaKey]);
    }
    workingCopy.meta = workingCopy.meta;
  }
</script>

<!-- 
      @component shared component for setting and unsetting togglable quantity settings. 
      Description is added as the default slot. 
-->

<PageCard title={name}>
  <InlineNotification type="info">
    <slot />
  </InlineNotification>
  <ConfigToggleGroup>
    <div class="w-1/2 max-w-max">
      <Toggle label={toggleLabel} bind:checked={enabled} noLabelBorder />
    </div>
    <div class="w-1/2">
      {#if enabled}
        <NumberInput
          hideSpinner={inputType === 'currency'}
          decimals={inputType === 'currency'}
          step={inputType === 'currency' ? 0.01 : 1}
          label={inputLabel}
          sideLabel
          bind:value={workingCopy.meta[metaKey].value}
        />
      {/if}
    </div>
  </ConfigToggleGroup>
</PageCard>
