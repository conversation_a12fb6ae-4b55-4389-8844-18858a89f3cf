<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import Secret from '@/components/Config/shared/components/Secret.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { Secret as SecretType } from '@/models/secrets';

  export let title: string = 'Secrets';
  export let connectorKind: SecretType['connector_kind'];
  export let connectorId: number;
  export let key: string;
  export let info: string = '';
  export let label: string = '';
</script>

<PageCard {title}>
  {#if info}
    <InlineNotification type="info">{info}</InlineNotification>
  {/if}
  <Secret keyLabel={label || title} {key} {connectorKind} {connectorId} />
</PageCard>
