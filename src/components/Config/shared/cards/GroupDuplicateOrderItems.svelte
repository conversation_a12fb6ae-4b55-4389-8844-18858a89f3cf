<script lang="ts">
  import ConfigToggle from '@/components/Config/shared/cards/ConfigToggle.svelte';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject.js';

  export let workingCopy: ChannelWorkingCopy;
</script>

<ConfigToggle
  bind:workingCopy
  metaKey="group_duplicate_order_items"
  title="Group Duplicate Order Items"
  label="Group Duplicate Items?"
  description="{workingCopy.description} may send duplicate order items (same SKU) to Stock2Shop, especially during promotions like 'Buy One, Get One Free.' Enable this setting to automatically group them."
/>
