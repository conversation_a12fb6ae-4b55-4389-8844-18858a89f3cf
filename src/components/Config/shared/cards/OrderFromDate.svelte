<script lang="ts">
  import { ORDER_FROM_DATE } from '@/components/Config/constants';
  import PageCard from '@/components/Config/PageCard.svelte';
  import DateInput from '@/components/Controls/DateInput.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import { getDateFromStringOrTimestamp } from '@/lib/date-utils';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: ChannelWorkingCopy | SourceWorkingCopy;

  let formattedDate: string;

  $: date = workingCopy.meta[ORDER_FROM_DATE]?.value
    ? getDateFromStringOrTimestamp(workingCopy.meta[ORDER_FROM_DATE]?.value)
    : undefined;

  $: if (formattedDate) {
    workingCopy.meta[ORDER_FROM_DATE].value = formattedDate;
  }

  const handleClear = () => {
    formattedDate = '';
    workingCopy.meta[ORDER_FROM_DATE].value = '';
  };

  $: setDefaultConfigMeta(workingCopy, [ORDER_FROM_DATE]);

  // Date string must be in ISO 8061 format, you can include timezone (e.g. 2020-11-02T06:04:07+02:00)
</script>

<PageCard title="Order From Date">
  <InlineNotification type="info">
    <p>Only orders created after this date will be processed.</p>
  </InlineNotification>
  <div class="max-w-max">
    <DateInput
      canClear
      {handleClear}
      bind:value={date}
      bind:formattedValue={formattedDate}
      options={{ enableTime: true, dateFormat: 'Z', altInput: true, altFormat: 'Y-m-d h:i K' }}
    />
  </div>
</PageCard>
