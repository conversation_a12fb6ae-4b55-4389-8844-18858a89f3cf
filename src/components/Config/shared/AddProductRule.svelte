<script lang="ts">
  import Button from '@/components/Controls/Button.svelte';
  import FieldSelect from '@/components/Controls/Select/FieldSelector/FieldSelect.svelte';
  import FieldValueSelector from '@/components/Controls/Select/FieldSelector/FieldValueSelector.svelte';
  import OperatorSelector from '@/components/Controls/Select/OperatorSelector.svelte';
  import { productRuleConvertor } from '@/models/fields/fieldConvertors';
  import { isString } from '@/utils/typeguards';

  export let onAdd: (key: string, value: string, operator: string) => boolean;

  const handleAdd = () => {
    if (!key || !isString(value) || !operator) return;
    if (!onAdd(key, value, operator)) return;
    key = undefined;
    fieldSelectComponent.clear();
    value = undefined;
    operator = undefined;
  };

  let key: string | undefined;
  let value: string | undefined;
  let operator: string | undefined;
  let fieldSelectComponent: FieldSelect;
  $: disabled = !key || !operator || (operator !== 'is not empty' && !value);

  $: {
    if (operator === 'is not empty') value = '';
  }
</script>

<div class="grid w-full grid-cols-[2fr_1fr_2fr_5rem] gap-2">
  <FieldSelect
    bind:this={fieldSelectComponent}
    entity="product"
    bind:value={key}
    convertor={productRuleConvertor}
  />
  <OperatorSelector bind:value={operator} />
  <FieldValueSelector
    entity="product"
    name={key}
    bind:value
    multiple={operator === 'lookup'}
    convertor={productRuleConvertor}
    disabled={operator === 'is not empty'}
  />
  <Button {disabled} fullWidth size="large" variant="gray" on:click={handleAdd}>Add</Button>
</div>
