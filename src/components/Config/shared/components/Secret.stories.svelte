<script context="module" lang="ts">
  import Secret from '@/components/Config/shared/components/Secret.svelte';
  import { Story, Template } from '@storybook/addon-svelte-csf';

  export const meta = {
    component: Secret
  };
</script>

<script lang="ts">
  import StorybookWrapper from '@/components/StorybookWrapper.svelte';
  import PageCard from '@/components/Config/PageCard.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
</script>

<Template>
  <StorybookWrapper>
    <PageCard title="Secret component">
      <InlineNotification type="info">
        <p>
          This is the secret component. It takes in a key, connector_kind and connector_id then
          creates a secret for the specified connector
        </p>
      </InlineNotification>
      <Secret
        keyLabel="Some secret"
        key="some_secret"
        connectorKind="fulfillmentservice"
        connectorId={8}
      /></PageCard
    >
  </StorybookWrapper>
</Template>

<Story name="Price Tier Page" />
