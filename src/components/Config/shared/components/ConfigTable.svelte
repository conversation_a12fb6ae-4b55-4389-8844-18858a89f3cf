<script context="module" lang="ts">
  export type TableItem<T> = { key: string } & T;
  export type TableDisplayComponent<T> = ComponentType<SvelteComponent<{ listItem: TableItem<T> }>>;

  export const tableItemsToItems = <T,>(tableItems: TableItem<T>[]) =>
    tableItems.map((t) => tableItemToItem(t));
  export const tableItemToItem = <T,>(tableItem: TableItem<T>) => {
    const { key: _, ...rest } = tableItem;
    return rest;
  };
</script>

<script lang="ts" generics="T">
  import ConfigTableItem from '@/components/Config/shared/components/ConfigTableItem.svelte';
  import { flip } from 'svelte/animate';
  import { SOURCES, TRIGGERS, dndzone } from 'svelte-dnd-action';
  import type { ComponentType, SvelteComponent } from 'svelte';

  /**
   * Dont bind, use callbacks
   */
  export let items: TableItem<T>[] = [];
  export let displayComponent: TableDisplayComponent<T>;

  /**
   * Callback that returns the new order of items
   */
  export let onReorder: ((items: TableItem<T>[]) => void) | undefined = undefined;
  export let onDelete: ((key: string) => void) | undefined = undefined;
  export let onEdit: ((key: string) => void) | undefined = undefined;
  /**
   * Keeping this set to false will disable drag and drop
   */
  export let dnd = false;
  export let flipDurationMs = 300;

  interface DndId {
    _dndId: string;
  }

  type DndItem = TableItem<T> & DndId;
  // This for dnd to be able to mutate an id
  let dndItems: DndItem[];
  $: dndItems = items.map((item) => ({ ...item, _dndId: item.key as string }));

  const dndItemsToItems = (newDndItems: DndItem[]) => {
    const newItems = newDndItems.map((agg) => {
      const { _dndId: _, ...rest } = agg;
      return rest;
    });
    return newItems as TableItem<T>[];
  };

  let dragDisabled = true;

  const startDrag = (e: Event) => {
    // preventing default to prevent lag on touch devices (because of the browser checking for screen scrolling)
    e.preventDefault();
    dragDisabled = false;
  };

  const handleKeyDown = (e: KeyboardEvent) => {
    if ((e.key === 'Enter' || e.key === ' ') && dragDisabled) dragDisabled = false;
  };

  const handleDndConsider = (e: CustomEvent<DndEvent<DndItem>>) => {
    const {
      items: newItems,
      info: { source, trigger }
    } = e.detail;

    dndItems = newItems;

    // Ensure dragging is stopped on drag finish via keyboard
    if (source === SOURCES.KEYBOARD && trigger === TRIGGERS.DRAG_STOPPED) {
      dragDisabled = true;
    }
  };

  const handleDndFinalize = (e: CustomEvent<DndEvent<DndItem>>) => {
    const {
      items: newItems,
      info: { source }
    } = e.detail;

    dndItems = newItems;

    if (onReorder) {
      onReorder(dndItemsToItems(newItems));
    }

    // Ensure dragging is stopped on drag finish via pointer (mouse, touch)
    if (source === SOURCES.POINTER) {
      dragDisabled = true;
    }
  };

  const hasActions = onDelete || onEdit;
  const colStyles = [
    { 'grid grid-cols-[auto_1fr_auto]': dnd && hasActions },
    { 'grid grid-cols-[auto_1fr]': dnd && !hasActions },
    { 'grid grid-cols-[1fr_1fr]': !dnd && !hasActions },
    { 'grid grid-cols-[1fr_auto]': !dnd && hasActions }
  ];
</script>

<!-- @component
  Config table with DnD support
  Takes the add component as a the default slot
  @see overrideItemIdKeyNameBeforeInitialisingDndZones in root layout for setting id key
 -->
<div class="flex flex-col gap-6">
  <div
    use:dndzone={{
      items: dndItems,
      dragDisabled: !dnd || dragDisabled,
      dropTargetStyle: {},
      flipDurationMs,
      dropFromOthersDisabled: true
    }}
    on:consider={handleDndConsider}
    on:finalize={handleDndFinalize}
  >
    {#each dndItems as item (item._dndId)}
      <div animate:flip={{ duration: flipDurationMs }} class="focus-visible:outline-none">
        <ConfigTableItem
          {dnd}
          listItem={item}
          {dragDisabled}
          {onDelete}
          {onEdit}
          {startDrag}
          {handleKeyDown}
          {colStyles}
          {displayComponent}
        />
      </div>
    {/each}
  </div>
  <slot />
</div>
