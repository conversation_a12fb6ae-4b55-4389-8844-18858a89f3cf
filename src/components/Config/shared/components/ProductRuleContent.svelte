<script lang="ts">
  import type { TableItem } from '@/components/Config/shared/components/ConfigTable.svelte';
  import type { ProductRuleItem } from '@/components/Config/channels/shared/pages/ProductRules.svelte';
  import LabelStatusNotification from '@/components/Controls/LabelStatusNotification.svelte';
  import { operatorMap } from '@/components/Controls/Select/OperatorSelector.svelte';

  import TagsDisplay from '@/components/Controls/Tag/TagsDisplay.svelte';
  import { productRuleConvertor } from '@/models/fields/fieldConvertors';

  export let listItem: TableItem<ProductRuleItem>;
</script>

<div class="flex items-center gap-1">
  <span class="capitalize">{productRuleConvertor.valueToField(listItem.key)}</span>
  <span class="font-bold text-brand-notification">{operatorMap[listItem.operator]}</span>
  {#if listItem.value}
    {#if listItem.operator === 'lookup'}
      <span class="truncate overflow-ellipsis">
        <TagsDisplay size="small" tagsCsv={listItem.value} separator="or" />
      </span>
    {:else}
      <LabelStatusNotification status="info" size="small">{listItem.value}</LabelStatusNotification>
    {/if}
  {/if}
</div>
