<script lang="ts" generics="T">
  import IconEdit from '@/components/Icons/IconEdit.svelte';

  import IconDelete from '@/components/Icons/IconDelete.svelte';
  import type {
    TableDisplayComponent,
    TableItem
  } from '@/components/Config/shared/components/ConfigTable.svelte';

  import type { ClassValue } from 'clsx';

  import Button from '@/components/Controls/Button.svelte';
  import IconMenu from '@/components/Icons/IconMenu.svelte';
  import { cn } from '@/utils/classname';

  export let listItem: TableItem<T>;
  export let onDelete: ((key: string) => void) | undefined = undefined;
  export let onEdit: ((key: string) => void) | undefined = undefined;
  export let dnd = false;
  export let startDrag: (e: MouseEvent | TouchEvent) => void;
  export let handleKeyDown: (e: KeyboardEvent) => void;
  export let dragDisabled = true;
  export let colStyles: ClassValue[];
  export let displayComponent: TableDisplayComponent<T>;
</script>

<div
  class={cn(
    'label flex items-center justify-between gap-4 border-b border-neutral-200 p-4 focus-visible:outline-none',
    ...colStyles
  )}
>
  {#if dnd}
    <Button
      icon={IconMenu}
      size="small"
      variant="gray"
      on:mousedown={startDrag}
      on:touchstart={startDrag}
      on:keydown={handleKeyDown}
      draggable
      dragging={!dragDisabled}
    />
  {/if}

  <div><svelte:component this={displayComponent} {listItem} /></div>
  <div class="flex gap-6">
    {#if onEdit}
      <div>
        <Button
          testId="table-edit"
          on:click={() => {
            onEdit(listItem.key);
          }}
          icon={IconEdit}
          size="small"
          variant="gray"
        />
      </div>
    {/if}

    {#if onDelete}
      <div>
        <Button
          testId="table-delete"
          on:click={() => {
            onDelete(listItem.key);
          }}
          icon={IconDelete}
          size="small"
          variant="gray"
        />
      </div>
    {/if}
  </div>
</div>
