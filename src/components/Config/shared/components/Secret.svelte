<script lang="ts">
  import TextInput from '@/components/Controls/TextInput.svelte';
  import Button from '@/components/Controls/Button.svelte';
  import { confirmModal } from '@/components/Modal/modal';
  import LabelStatusNotification from '@/components/Controls/LabelStatusNotification.svelte';
  import IconCheckCircle from '@/components/Icons/IconCheckCircle.svelte';
  import type { Secret } from '@/models/secrets';
  import { createSecretsQuery } from '@/queries/secrets.query';
  import {
    createUpdateSecretMutation,
    createDeleteSecretMutation
  } from '@/mutations/secret.mutation';

  export let keyLabel: string;
  export let connectorKind: Secret['connector_kind'];
  export let connectorId: number;
  export let key: string;

  let hasSecret: boolean = false;
  let secret: string;
  let isSuccess: boolean = false;
  let isError: boolean = false;
  let successMessage = '';
  let errorMessage = '';

  const secretsQuery = createSecretsQuery(connectorKind, connectorId);
  const secretUpdateMutation = createUpdateSecretMutation();
  const secretDeleteMutation = createDeleteSecretMutation();

  async function save() {
    isSuccess = false;
    isError = false;
    $secretUpdateMutation.mutate(
      {
        connector_kind: connectorKind,
        connector_id: connectorId,
        key: key,
        value: secret
      },
      {
        onSuccess: () => {
          hasSecret = true;
          activateStatus('success', 'Successfully added ' + keyLabel);
        }
      }
    );
  }

  async function remove() {
    let confirm: boolean = await confirmModal(
      {
        title: 'Confirm Action',
        description: 'Are you sure you want to remove this secret? This action cannot be undone.',
        actionText: 'Confirm'
      },
      {
        canReject: false
      }
    );
    if (!confirm) {
      return;
    }
    isSuccess = false;
    isError = false;
    $secretDeleteMutation.mutate(
      {
        connector_kind: connectorKind,
        connector_id: connectorId,
        key: key
      },
      {
        onSuccess: () => {
          hasSecret = false;
          secret = '';
          activateStatus('success', 'Successfully deleted ' + keyLabel);
        }
      }
    );
  }

  $: if ($secretUpdateMutation.isError || $secretDeleteMutation.isError)
    activateStatus('error', 'Something went wrong');

  $: if ($secretsQuery.isFetched && !$secretsQuery.isError && $secretsQuery.data) {
    hasSecret = $secretsQuery.data.results[key] !== undefined;
  }

  function activateStatus(statusType: string, message: string) {
    if (statusType === 'success') {
      successMessage = message;
      isSuccess = true;
      setTimeout(() => {
        isSuccess = false;
        successMessage = '';
      }, 3000);
    } else if (statusType === 'error') {
      errorMessage = message;
      isError = true;
      setTimeout(() => {
        isError = false;
        errorMessage = '';
      }, 3000);
    }
  }
</script>

<div class="flex gap-4">
  {#if hasSecret}
    <TextInput type="text" isDisable={hasSecret} sideLabel label={keyLabel} value="********" />
  {:else}
    <TextInput
      type="password"
      isDisable={hasSecret}
      sideLabel
      label={keyLabel}
      bind:value={secret}
    />
  {/if}
  {#if !hasSecret}
    <Button type="button" size="large" variant="outline" on:click={save} disabled={!secret}
      >Save</Button
    >
  {:else}
    <Button variant="outline" size="large" on:click={remove}>Delete</Button>
  {/if}
  {#if $secretUpdateMutation.isPending || $secretDeleteMutation.isPending}
    <LabelStatusNotification status="info" size="large">Saving...</LabelStatusNotification>
  {/if}
  {#if isSuccess}
    <LabelStatusNotification status="success" icon={IconCheckCircle} size="large"
      >{successMessage}
    </LabelStatusNotification>
  {/if}
  {#if isError}
    <LabelStatusNotification status="error" icon={IconCheckCircle} size="large"
      >{errorMessage}
    </LabelStatusNotification>
  {/if}
</div>
