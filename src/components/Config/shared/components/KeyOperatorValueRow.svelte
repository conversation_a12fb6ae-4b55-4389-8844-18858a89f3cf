<script lang="ts">
  import { createEventDispatcher } from 'svelte';

  import Button from '@/components/Controls/Button.svelte';
  import IconDelete from '@/components/Icons/IconDelete.svelte';
  import Chip from '@/components/Controls/Tag/Chip.svelte';

  export let key: string;
  export let operator: string = '';
  export let value: string | undefined = undefined;
  export let label: string | undefined = undefined;

  const dispatch = createEventDispatcher();
</script>

<div class="flex items-center justify-between gap-4 border-b border-neutral-200 p-4">
  <div class="flex items-center gap-1">
    {#if label}
      <span class="text-neutral-700">{label}</span>
    {/if}
    <span>{key}</span>
    {#if operator}
      <span class="font-bold text-brand-notification">{operator}</span>
    {/if}
    {#if value}
      <Chip type="info" size="large" label={value} />
    {/if}
  </div>
  <Button variant="gray" icon={IconDelete} size="small" on:click={() => dispatch('delete')} />
</div>
