<script lang="ts">
  import type { TableItem } from '@/components/Config/shared/components/ConfigTable.svelte';
  import Chip from '@/components/Controls/Tag/Chip.svelte';

  export let listItem: TableItem<{ value: string; label?: string }>;
</script>

<!-- @component
   Generic Table content component where just a value & chip label are all that's needed.
   -->

<div class="flex items-center gap-1">
  <span class="font-bold text-brand-notification">{listItem.value}</span>
  {#if listItem.label}
    <Chip type="info" label={listItem.label} />
  {/if}
</div>
