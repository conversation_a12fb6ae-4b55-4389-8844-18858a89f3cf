<script context="module" lang="ts">
  import ConfigTable, {
    type TableItem
  } from '@/components/Config/shared/components/ConfigTable.svelte';
  import ConfigTableGenericContent from '@/components/Config/shared/components/ConfigTableGenericContent.svelte';

  export const meta = {
    title: 'components/Config/ConfigTable',
    component: ConfigTable
  };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';

  let myList = [
    { id: 'my_key', name: 'My Key', value: 'My Value', info: 'New' },
    { id: 'my_key2', name: 'My Key 2', value: 'My Value 2' },
    { id: 'my_key3', name: 'My Key 3', value: 'My Value 3' },
    { id: 'my_key4', name: 'My Key 4', value: 'My Value 4', info: 'Test' },
    { id: 'my_key5', name: 'My Key 5', value: 'My Value 5' },
    { id: 'my_key6', name: 'My Key 6', value: 'My Value 6' }
  ];

  const onReorder = (items: TableItem[]) => {
    myList = items.map((newItem) => myList.find((i) => i.id === newItem.key) as (typeof myList)[0]);
  };

  const onDelete = (key: string) => {
    myList = myList.filter((item) => item.id !== key);
  };
</script>

<Story name="DnD" id="config_table_dnd">
  <ConfigTable
    dnd
    items={myList.map((item) => ({ key: item.id, value: item.value, label: item?.info }))}
    {onReorder}
    {onDelete}
    displayComponent={ConfigTableGenericContent}
  />
</Story>

<Story name="No DnD" id="config_table_no_dnd">
  <ConfigTable
    items={myList.map((item) => ({ key: item.id, value: item.value, label: item?.info }))}
    {onReorder}
    {onDelete}
    displayComponent={ConfigTableGenericContent}
  />
</Story>
