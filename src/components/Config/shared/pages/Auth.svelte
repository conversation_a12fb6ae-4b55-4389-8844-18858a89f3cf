<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import Button from '@/components/Controls/Button.svelte';

  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import { checkOAuth2Authed } from '@/models/config/config';
  import AuthenticationRepo from '@/repos/authenticate-repo';
  import type { ChannelWorkingCopy } from '@/stores/channelVersionedObject';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import sleep from '@/utils/sleep';
  import capitalize from 'lodash.capitalize';
  import { toast } from 'svelte-sonner';

  export let workingCopy: SourceWorkingCopy | ChannelWorkingCopy;

  // TODO: Look into why this is a reactive statement. It seems like it could just be derived from the workingCopy (since workingCopy.type should'nt change)')
  $: type = capitalize(workingCopy.type);
  $: authed = checkOAuth2Authed(workingCopy);

  let reAuth = false;
  let isLoading = false;

  const authenticationRepo = new AuthenticationRepo();

  const getAuthUrl = async () => {
    isLoading = true;

    let channelId: number | undefined = undefined;
    let sourceId: number | undefined = undefined;

    // TODO: Sanity check this with another dev. This seems like, if the workingCopy is a ChannelWorkingCopy, the channelID and SourceID will always be the same. Is this the desired behavior
    if ((workingCopy as ChannelWorkingCopy)?.isChannel) {
      channelId = (workingCopy as ChannelWorkingCopy).id;
    }

    if ((workingCopy as SourceWorkingCopy)?.id) {
      sourceId = (workingCopy as SourceWorkingCopy).id;
    }

    const url = (await authenticationRepo.getOAuth2AuthenticationLink({ channelId, sourceId }))
      .oauth2_authorization_link;

    // Sleep to make the loading state more apparent and smoothen out the UI
    await sleep(2000);
    isLoading = false;
    return url;
  };

  let toastExpireId: number | string;

  const authorise = async () => {
    const authUrl = await getAuthUrl();
    await sleep(200);
    toastExpireId = toast.info('Please note that this link will expire in 2 hours');
    await sleep(1500);
    window.open(authUrl);
  };

  let toastAuthId: number | string | undefined;

  const handleAuthorisation = async () => {
    toast.dismiss(toastAuthId);
    toast.dismiss(toastExpireId);
    toastAuthId = toast.promise(() => authorise(), {
      loading: `Requesting ${type} authorisation link...`,
      error: (e) => (e instanceof Error ? e.message : 'Error authorising'),
      success: 'Opened authorisation in your browser'
    });
  };
</script>

<PageCard title="{type} Authorisation">
  {#if authed && !reAuth}
    <InlineNotification type="success">Successfully authorised</InlineNotification>
    <Button variant="outline" size="large" on:click={() => (reAuth = true)}>Reauthorise</Button>
  {:else}
    <InlineNotification type="info">
      <p>Click below to link your {type} account to Stock2Shop</p>
    </InlineNotification>

    <div>
      <Button variant="outline" size="large" on:click={handleAuthorisation} disabled={isLoading}>
        {isLoading ? 'Authorising...' : 'Authorise with ' + type}
      </Button>
    </div>
  {/if}
</PageCard>
