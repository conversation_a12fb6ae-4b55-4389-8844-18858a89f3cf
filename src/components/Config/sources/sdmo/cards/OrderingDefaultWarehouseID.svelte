<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: SourceWorkingCopy;

  const DEFAULT_WAREHOUSE_ID = 'default_warehouse_id';

  $: setDefaultConfigMeta(workingCopy, [DEFAULT_WAREHOUSE_ID]);
</script>

<PageCard title="Default Warehouse ID">
  <InlineNotification type="info">
    <p>The default warehouse ID to use when raising orders into SDMO.</p>
  </InlineNotification>
  <div class="max-w-max">
    <TextInput
      sideLabel
      label="Default Warehouse ID"
      bind:value={workingCopy.meta[DEFAULT_WAREHOUSE_ID].value}
    />
  </div>
</PageCard>
