<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import Secret from '@/components/Config/shared/components/Secret.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';

  export let workingCopy: SourceWorkingCopy;

  const CLIENT_ID = 'client_id';
  const CLIENT_SECRET = 'client_secret';
  const TENANT_ID = 'tenant_id';
</script>

<PageCard title="Credentials">
  <InlineNotification type="info">
    <p>
      To enable Stock2Shop to connect to your SDMO account, we require your API credentials. These
      credentials are securely stored in our system as encrypted secrets, ensuring that neither we
      nor anyone else can access them. This guarantees the protection and confidentiality of your
      credentials.
    </p>
  </InlineNotification>
  <Secret
    keyLabel="Client ID"
    key={CLIENT_ID}
    connectorKind="source"
    connectorId={workingCopy.id}
  />
  <Secret
    keyLabel="Client Secret"
    key={CLIENT_SECRET}
    connectorKind="source"
    connectorId={workingCopy.id}
  />
  <Secret
    keyLabel="Tenant ID"
    key={TENANT_ID}
    connectorKind="source"
    connectorId={workingCopy.id}
  />
</PageCard>
