<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: SourceWorkingCopy;

  const TAX_ITEM_CODE = 'tax_item_code';

  $: setDefaultConfigMeta(workingCopy, [TAX_ITEM_CODE]);
</script>

<PageCard title="Tax Item Code">
  <InlineNotification type="info">
    <p>The tax item code that will be applied to order items.</p>
  </InlineNotification>
  <div class="max-w-max">
    <TextInput sideLabel label="Tax Item Code" bind:value={workingCopy.meta[TAX_ITEM_CODE].value} />
  </div>
</PageCard>
