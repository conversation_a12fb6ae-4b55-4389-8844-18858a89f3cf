<script lang="ts">
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import OrderFromDate from '@/components/Config/shared/cards/OrderFromDate.svelte';
  import DefaultCustomerCode from '@/components/Config/sources/shared/cards/OrderingDefaultCustomerCode.svelte';
  import DefaultShippingCode from '@/components/Config/sources/shared/cards/OrderingDefaultShippingCode.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import CreateOrderEnabled from '@/components/Config/shared/cards/CreateOrderEnabled.svelte';
  import OrderingDefaultWarehouseId from '@/components/Config/sources/sdmo/cards/OrderingDefaultWarehouseID.svelte';
  import OrderingTaxItemCode from '@/components/Config/sources/sdmo/cards/OrderingTaxItemCode.svelte';

  export let workingCopy: SourceWorkingCopy;

  const META_KEY = 'create_order_enabled';

  $: setDefaultConfigMeta(workingCopy, [META_KEY]);
</script>

<PageWrapper>
  <CreateOrderEnabled bind:workingCopy />
  <OrderFromDate bind:workingCopy />
  <DefaultCustomerCode bind:workingCopy />
  <DefaultShippingCode bind:workingCopy />
  <OrderingDefaultWarehouseId bind:workingCopy />
  <OrderingTaxItemCode bind:workingCopy />
</PageWrapper>
