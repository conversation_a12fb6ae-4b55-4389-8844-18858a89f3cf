<script lang="ts">
  import NumberInput from '@/components/Controls/NumberInput.svelte';
  import { onMount } from 'svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import PageCard from '@/components/Config/PageCard.svelte';
  import { isNumber } from '@/utils/typeguards';
  import { XERO_QUOTE_EXPIRY_DAYS } from '@/components/Config/constants';

  export let workingCopy: SourceWorkingCopy;

  let internalValue: number;

  onMount(() => {
    if (!workingCopy.meta[XERO_QUOTE_EXPIRY_DAYS]) {
      internalValue = 0;
    } else {
      internalValue = parseInt(workingCopy.meta[XERO_QUOTE_EXPIRY_DAYS]?.value);
    }
  });

  $: {
    if (isNumber(internalValue)) {
      workingCopy.meta[XERO_QUOTE_EXPIRY_DAYS] = {
        key: XERO_QUOTE_EXPIRY_DAYS,
        value: internalValue ? internalValue.toString() : ''
      };
    }
  }
</script>

<PageCard title=" Quote Expiry Days">
  <NumberInput
    label="The number of days a quote is valid for"
    bind:value={internalValue}
    required
    min={1}
  />
</PageCard>
