<script lang="ts">
  import { PRODUCT_INACTIVE_KEYWORD } from '@/components/Config/constants';
  import PageCard from '@/components/Config/PageCard.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: SourceWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [PRODUCT_INACTIVE_KEYWORD]);
</script>

<PageCard title="Inactive Keyword">
  <InlineNotification type="info">
    <p>
      The Xero API does not include any information about whether a product is archived or not. For
      this reason Stock2Shop will sync all products, even those that are archived. If you would like
      to exclude some products, you can configure a keyword that you would add to the 'Name' field
      in Xero to indicate that the product is inactive.
    </p>
    <br />
    <p>
      For example, if you configure "Inactive" as the keyword, you could add that word at any
      position in the product "Name" field on Xero (e.g. "Black T-Shirt - inactive" or "[Inactive]
      Black T-Shirt").
    </p>
  </InlineNotification>
  <div class="max-w-max">
    <TextInput
      label="Keyword"
      sideLabel
      bind:value={workingCopy.meta[PRODUCT_INACTIVE_KEYWORD].value}
    />
  </div>
</PageCard>
