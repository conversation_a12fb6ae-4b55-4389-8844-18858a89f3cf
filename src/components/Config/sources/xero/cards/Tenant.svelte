<script lang="ts">
  import { page } from '$app/stores';
  import { XERO_TENANT_ID } from '@/components/Config/constants';
  import PageCard from '@/components/Config/PageCard.svelte';
  import Select from '@/components/Controls/Select/Select.svelte';
  import Skeleton from '@/components/Loader/Skeleton.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import XeroTenantConnectionRepo from '@/repos/xero-tenant-connection-repo';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { createQuery } from '@tanstack/svelte-query';
  import { getContext } from 'svelte';
  import { toast } from 'svelte-sonner';
  import type { Writable } from 'svelte/store';

  export let workingCopy: SourceWorkingCopy;

  const xeroTenantConnectionRepo = new XeroTenantConnectionRepo();

  const xeroTenantConnectionData = createQuery({
    queryKey: ['xero-tenant-connection', $page.params.id],
    queryFn: () => xeroTenantConnectionRepo.get(parseInt($page.params.id))
  });

  let internalTenantIdOption: string | undefined;
  $: {
    if (typeof internalTenantIdOption === 'undefined') {
      internalTenantIdOption = $xeroTenantConnectionData.data?.find(
        (tenant) => tenant.tenantId === workingCopy.meta[XERO_TENANT_ID].value
      )?.tenantId;
    } else {
      workingCopy.meta[XERO_TENANT_ID].value = internalTenantIdOption;
    }
  }

  const handleSave = getContext<Writable<() => Promise<void>>>('handleSave');

  let automaticallySetTenant = false;
  /**
   * Auto-save to set tenant to first option on initial login.
   * This is to prevent tenant_id from not being set if the user forgets to save
   * when visiting this route after authenticating their Xero account.
   */
  $: {
    if (
      !automaticallySetTenant &&
      $xeroTenantConnectionData.data?.length &&
      workingCopy.meta[XERO_TENANT_ID].value.length <= 1
    ) {
      const firstTenant = $xeroTenantConnectionData.data[0];
      workingCopy.meta[XERO_TENANT_ID].value = firstTenant.tenantId;
      automaticallySetTenant = true;
      toast.promise($handleSave, {
        loading: 'Setting connected Xero tenant',
        error: 'Failed to automatically set the connected Xero tenant, please try saving.',
        success: `Tenant set to ${firstTenant.tenantName}`,
        duration: 6000
      });
    }
  }
</script>

<PageCard title="Tenant">
  <span class=" label min-w-[150px]">Select a tenant from your Xero account</span>
  {#if $xeroTenantConnectionData.isError}
    <InlineNotification type="error">{$xeroTenantConnectionData.error.message}</InlineNotification>
  {:else if $xeroTenantConnectionData.isLoading}
    <Skeleton shimmer class="h-10 w-full" />
  {:else}
    {#if workingCopy.meta[XERO_TENANT_ID].value?.length <= 1}
      <InlineNotification type="error">Please select a tenant and save</InlineNotification>
    {/if}

    <Select
      optionsLoading={$xeroTenantConnectionData.isLoading}
      options={$xeroTenantConnectionData.data?.map((o) => ({
        value: o.tenantId,
        label: o.tenantName
      })) ?? []}
      bind:value={internalTenantIdOption}
    />
  {/if}
</PageCard>
