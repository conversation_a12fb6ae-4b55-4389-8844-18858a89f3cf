<script lang="ts">
  import { XERO_QUOTE_TERMS } from '@/components/Config/constants';
  import PageCard from '@/components/Config/PageCard.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';

  export let workingCopy: SourceWorkingCopy;

  $: {
    if (!workingCopy.meta[XERO_QUOTE_TERMS]) {
      workingCopy.meta[XERO_QUOTE_TERMS] = { key: XERO_QUOTE_TERMS, value: '' };
    }
  }
</script>

<PageCard title="Quote Terms">
  <TextInput
    label="The terms to use for creating a quote"
    bind:value={workingCopy.meta[XERO_QUOTE_TERMS].value}
    maxLength={40}
    required
  />
</PageCard>
