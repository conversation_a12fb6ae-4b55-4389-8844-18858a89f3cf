<script lang="ts">
  import { DEFAULT_ORDER_STATUS } from '@/components/Config/constants';
  import PageCard from '@/components/Config/PageCard.svelte';
  import Select from '@/components/Controls/Select/Select.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';

  export let workingCopy: SourceWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [DEFAULT_ORDER_STATUS]);

  const options = ['DRAFT', 'SUBMITTED', 'AUTHORISED'];
</script>

<PageCard title="Default Order Status">
  <InlineNotification type="info">
    The status assigned to the order when it is raised in {workingCopy.description}.
  </InlineNotification>
  <div class="max-w-[331px]">
    <Select
      sideLabel
      label="Order Status"
      bind:value={workingCopy.meta[DEFAULT_ORDER_STATUS].value}
      options={options.map((o) => ({ value: o, label: o }))}
      required
    />
  </div>
</PageCard>
