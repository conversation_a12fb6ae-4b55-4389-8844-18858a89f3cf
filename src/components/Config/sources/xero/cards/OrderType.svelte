<script lang="ts">
  import { ORDER_TYPE } from '@/components/Config/constants';
  import PageCard from '@/components/Config/PageCard.svelte';
  import Select from '@/components/Controls/Select/Select.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import capitalize from 'lodash.capitalize';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';

  export let workingCopy: SourceWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [ORDER_TYPE]);

  const options = ['Invoice', 'Quote'];
</script>

<PageCard title="Order Type">
  <InlineNotification type="info">
    Specify the type of order that should be created in {workingCopy.description}, such as an
    Invoice or Quote.
  </InlineNotification>
  <div class="max-w-[331px]">
    <Select
      sideLabel
      label="Order Type"
      bind:value={workingCopy.meta[ORDER_TYPE].value}
      options={options.map((o) => ({ value: o, label: capitalize(o) }))}
      required
    />
  </div>
</PageCard>
