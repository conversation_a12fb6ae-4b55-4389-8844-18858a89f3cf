<script lang="ts">
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import OrderFromDate from '@/components/Config/shared/cards/OrderFromDate.svelte';
  import SourceParamDefaultCustomerCodeCard from '@/components/Config/sources/shared/cards/OrderingDefaultCustomerCode.svelte';
  import DefaultOrderStatus from '@/components/Config/sources/xero/cards/DefaultOrderStatus.svelte';
  import DefaultShippingCode from '@/components/Config/sources/shared/cards/OrderingDefaultShippingCode.svelte';
  import OrderType from '@/components/Config/sources/xero/cards/OrderType.svelte';
  import QuoteExpiryDays from '@/components/Config/sources/xero/cards/QuoteExpiryDays.svelte';
  import QuoteTerms from '@/components/Config/sources/xero/cards/QuoteTerms.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import CreateOrderEnabled from '@/components/Config/shared/cards/CreateOrderEnabled.svelte';
  import { CREATE_ORDER_ENABLED } from '@/components/Config/constants';

  export let workingCopy: SourceWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [CREATE_ORDER_ENABLED]);
</script>

<PageWrapper>
  <SourceParamDefaultCustomerCodeCard bind:workingCopy />
  <CreateOrderEnabled bind:workingCopy />
  {#if workingCopy.meta[CREATE_ORDER_ENABLED].value === 'true'}
    <OrderFromDate bind:workingCopy />
    <OrderType bind:workingCopy />
    <DefaultOrderStatus bind:workingCopy />
    <DefaultShippingCode bind:workingCopy />

    {#if workingCopy.meta['order_type'].value === 'Quote'}
      <QuoteExpiryDays bind:workingCopy />
      <QuoteTerms bind:workingCopy />
    {/if}
  {/if}
</PageWrapper>
