<script context="module" lang="ts">
  import Connection from './Connection.svelte';
  import { Story, Template } from '@storybook/addon-svelte-csf';
  import { writable } from 'svelte/store';
  import { setContext } from 'svelte';
  import sleep from '@/utils/sleep';

  export const meta = {
    component: Connection
  };
</script>

<script lang="ts">
  const handleSave = writable(async () => {
    await sleep(1000);
  });
  setContext('handleSave', handleSave);
</script>

<Template let:args>
  <Connection
    workingCopy={{
      id: 1577, // This is is set for xero-tenant-connection-repo test data to return the correct tenant list
      type: 'xero',
      meta: args.meta,
      description: 'Xero'
    }}
  />
</Template>

<Story
  name="Unauthorised"
  args={{
    meta: {
      sync_mode: {
        value: 'pull'
      },
      xero_tenant_id: {
        value: 'x'
      }
    }
  }}
/>

<Story
  name="Authorised: Tenant not set"
  args={{
    meta: {
      sync_mode: {
        value: 'pull'
      },
      xero_tenant_id: {
        value: 'x'
      },
      oauth2_is_authorised: {
        value: 'true'
      }
    }
  }}
/>

<Story
  name="Authorised: Tenant set"
  args={{
    meta: {
      sync_mode: {
        value: 'pull'
      },
      xero_tenant_id: {
        value: '123456'
      },
      oauth2_is_authorised: {
        value: 'true'
      }
    }
  }}
/>
