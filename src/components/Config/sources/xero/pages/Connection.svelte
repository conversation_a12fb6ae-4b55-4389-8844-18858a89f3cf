<script lang="ts">
  import Auth from '@/components/Config/shared/pages/Auth.svelte';
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import Tenant from '@/components/Config/sources/xero/cards/Tenant.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { OAUTH2_IS_AUTHORISED } from '@/components/Config/constants';

  export let workingCopy: SourceWorkingCopy;
</script>

<PageWrapper>
  <Auth bind:workingCopy />
  {#if workingCopy?.meta?.[OAUTH2_IS_AUTHORISED]?.value === 'true'}
    <Tenant bind:workingCopy />
  {/if}
</PageWrapper>
