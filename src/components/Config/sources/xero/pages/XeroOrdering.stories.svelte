<script context="module" lang="ts">
  import XeroOrdering from './XeroOrdering.svelte';
  import { Story, Template } from '@storybook/addon-svelte-csf';
  import { writable } from 'svelte/store';
  import { setContext } from 'svelte';
  import sleep from '@/utils/sleep';

  export const meta = {
    component: XeroOrdering
  };
</script>

<script lang="ts">
  const handleSave = writable(async () => {
    await sleep(1000);
  });
  setContext('handleSave', handleSave);
</script>

<Template let:args>
  <XeroOrdering
    workingCopy={{
      id: 1577,
      type: 'xero',
      meta: args.meta,
      description: 'Xero'
    }}
  />
</Template>

<Story
  id="config_xero_orders_disabled"
  name="Disabled"
  args={{
    meta: {}
  }}
/>

<Story
  id="config_xero_orders_enabled_invoice"
  name="Enabled: Invoice type"
  args={{
    meta: {
      create_order_enabled: {
        value: 'true'
      },
      order_type: {
        value: 'Invoice'
      },
      default_order_status: {
        value: 'DRAFT'
      },
      default_shipping_code: {
        value: '123'
      }
    }
  }}
/>

<Story
  id="config_xero_orders_enabled_quote"
  name="Enabled: Quote type"
  args={{
    meta: {
      create_order_enabled: {
        value: 'true'
      },
      order_type: {
        value: 'Quote'
      },
      default_order_status: {
        value: 'DRAFT'
      },
      default_shipping_code: {
        value: '123'
      },
      xero_quote_terms: {
        value: 'Terms go here'
      },
      xero_quote_expiry_days: {
        value: '10'
      }
    }
  }}
/>
