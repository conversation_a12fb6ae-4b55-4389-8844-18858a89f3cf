<script lang="ts">
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';
  import PageCard from '../../../PageCard.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import {
    PARAM_IGNORE_DUE_DATE,
    PARAM_USE_CUSTOMER_PAYMENT_TERMS
  } from '@/components/Config/constants';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';

  export let workingCopy: SourceWorkingCopy;
  let setDueDate = true;

  $: {
    setDefaultConfigMeta(workingCopy, [PARAM_IGNORE_DUE_DATE, PARAM_USE_CUSTOMER_PAYMENT_TERMS]);
    if (setDueDate) {
      workingCopy.meta[PARAM_IGNORE_DUE_DATE].value = 'false';
    } else {
      workingCopy.meta[PARAM_IGNORE_DUE_DATE].value = 'true';
    }
  }
</script>

<PageCard title="Set Due Date">
  <InlineNotification type="info">
    Set the order due date in {workingCopy.description} to match the channel's order date.
  </InlineNotification>
  <div class="max-w-max">
    <Toggle label="Set Due Date?" bind:checked={setDueDate} noLabelBorder />
  </div>
</PageCard>

{#if !setDueDate}
  <PageCard title="Use Customer Payment Terms">
    <InlineNotification type="info">
      Enable to set the order due date based on the customers payment terms. If disabled, the
      channels order date will be used as the per the "Set Due Date" settings description.
    </InlineNotification>
    <div class="max-w-max">
      <Toggle
        label="Use Customer Payment Terms?"
        bind:checked={workingCopy.meta[PARAM_USE_CUSTOMER_PAYMENT_TERMS].value}
        type="string"
        noLabelBorder
      />
    </div>
  </PageCard>
{/if}
