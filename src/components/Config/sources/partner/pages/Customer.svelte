<script lang="ts">
  import {
    PARAM_CREATE_CUSTOMER_ENABLED,
    PARAM_DEFAULT_CUSTOMER_CODE,
    PARAM_USE_CUSTOMER_ADDRESS,
    PARAM_USE_CUSTOMER_CURRENCY,
    PARAM_USE_LINE_ITEM_UOM
  } from '@/components/Config/constants';
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import ConfigTextInput from '@/components/Config/shared/cards/ConfigTextInput.svelte';
  import ConfigToggle from '@/components/Config/shared/cards/ConfigToggle.svelte';
  import { TRUE } from '@/constants';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: SourceWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [PARAM_CREATE_CUSTOMER_ENABLED, PARAM_USE_CUSTOMER_ADDRESS]);
</script>

<PageWrapper>
  <ConfigToggle
    bind:workingCopy
    metaKey={PARAM_CREATE_CUSTOMER_ENABLED}
    title="Create Customer"
    label="Create Customer?"
    description="Allow Stock2Shop to create a new customer on {workingCopy.description} if no default customer code is found."
  />
  {#if workingCopy.meta[PARAM_CREATE_CUSTOMER_ENABLED]?.value === TRUE}
    <ConfigToggle
      bind:workingCopy
      metaKey={PARAM_USE_CUSTOMER_CURRENCY}
      title="Use Default Customer Currency Code"
      label="Enable"
      description="Create customers using the default customers currency code in {workingCopy.description} (if supplied) instead of the default currency code: 00"
    />
  {/if}
  <ConfigTextInput
    bind:workingCopy
    metaKey={PARAM_DEFAULT_CUSTOMER_CODE}
    title="Default Customer Code"
    label="Customer Code"
    sideLabel
    description="Default customer code to use if no customer code was specified."
  />
  <ConfigToggle
    bind:workingCopy
    metaKey={PARAM_USE_CUSTOMER_ADDRESS}
    title="Use Sage Customer Address"
    label="Use customer address in Sage?"
    description="Use the customer address stored in {workingCopy.description} instead of the address details on the order."
  />
  <ConfigToggle
    bind:workingCopy
    metaKey={PARAM_USE_LINE_ITEM_UOM}
    title="Use Line Item UOM"
    label="Use line item UOM?"
    description="Use the order line items' unit of measure instead of the default: EACH"
  />
</PageWrapper>
