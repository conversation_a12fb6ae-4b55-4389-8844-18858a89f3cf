<script lang="ts">
  import {
    PARAM_SHIPPING_CODE,
    PARAM_TAX_TYPE,
    PARAM_TAX_TYPE_EXEMPT
  } from '@/components/Config/constants';
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import ConfigTextInput from '@/components/Config/shared/cards/ConfigTextInput.svelte';
  import SetDueDate from '@/components/Config/sources/partner/cards/SetDueDate.svelte';
  import OrderDocumentType from '@/components/Config/sources/shared/cards/OrderDocumentType.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import CreateOrderEnabled from '@/components/Config/shared/cards/CreateOrderEnabled.svelte';

  export let workingCopy: SourceWorkingCopy;
</script>

<PageWrapper>
  <CreateOrderEnabled bind:workingCopy />
  <OrderDocumentType
    bind:workingCopy
    options={[
      { value: 'Quotation', label: 'Quotation' },
      { value: 'Sales Order', label: 'Sales Order' },
      { value: 'Invoice', label: 'Invoice' }
    ]}
  />
  <SetDueDate bind:workingCopy />
  <ConfigTextInput
    bind:workingCopy
    metaKey={PARAM_SHIPPING_CODE}
    title="Default Shipping Code"
    label="Shipping Code"
    sideLabel
    description="The shipping code to use when raising orders. This is an item code already existing in {workingCopy.description}."
  />
  <ConfigTextInput
    bind:workingCopy
    metaKey={PARAM_TAX_TYPE}
    title="Tax Type"
    label="Tax Type Number"
    sideLabel
    description="The number assigned to the tax type. This tax type will be used for all orders that are not tax exempt. Uses 01 if it is not set."
  />
  <ConfigTextInput
    bind:workingCopy
    metaKey={PARAM_TAX_TYPE_EXEMPT}
    title="Tax Type Exempt"
    label="Tax Type Number"
    sideLabel
    description="The number assigned to the tax exempt type. This tax type will be used for all orders that are tax exempt. Uses 01 if it is not set."
  />
</PageWrapper>
