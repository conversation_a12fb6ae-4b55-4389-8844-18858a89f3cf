<script lang="ts">
  import {
    CREATE_ORDER_ENABLED,
    PARAM_CHECK_CREDIT_LIMIT,
    PARAM_NEGATIVE_STOCK_DISABLED
  } from '@/components/Config/constants';
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import ConfigToggle from '@/components/Config/shared/cards/ConfigToggle.svelte';
  import CreditNote from '@/components/Config/sources/evolution/cards/CreditNote.svelte';
  import OrderCodes from '@/components/Config/sources/evolution/cards/OrderCodes.svelte';
  import OrderInvoice from '@/components/Config/sources/evolution/cards/OrderInvoice.svelte';
  import OrderDocumentType from '@/components/Config/sources/shared/cards/OrderDocumentType.svelte';
  import WarehouseCode from '@/components/Config/sources/shared/cards/WarehouseCode.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import CreateOrderEnabled from '@/components/Config/shared/cards/CreateOrderEnabled.svelte';

  export let workingCopy: SourceWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [
    CREATE_ORDER_ENABLED,
    PARAM_CHECK_CREDIT_LIMIT,
    PARAM_NEGATIVE_STOCK_DISABLED
  ]);
</script>

<PageWrapper>
  <CreateOrderEnabled bind:workingCopy />
  <OrderDocumentType bind:workingCopy />
  <OrderCodes bind:workingCopy />
  <OrderInvoice bind:workingCopy />
  <CreditNote bind:workingCopy />
  <WarehouseCode bind:workingCopy />
  <ConfigToggle
    bind:workingCopy
    metaKey={PARAM_NEGATIVE_STOCK_DISABLED}
    title="Negative Stock"
    label="Disable negative stock?"
    description="Disable negative stock to check that order line items are in stock."
  />
  <ConfigToggle
    bind:workingCopy
    metaKey={PARAM_CHECK_CREDIT_LIMIT}
    title="Credit Limit"
    label="Check credit limit?"
    description="Check if the customer has enough credit before creating the order in {workingCopy.description}."
  />
  <!--
  TODO Remove CurrencyCode and TimeZone, they are params without default values?
  Or if they are required then create a shared card for each
  -->
</PageWrapper>
