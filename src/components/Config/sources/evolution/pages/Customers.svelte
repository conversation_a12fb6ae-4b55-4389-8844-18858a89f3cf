<script lang="ts">
  import {
    PARAM_CREATE_CUSTOMER_ENABLED,
    PARAM_USE_CUSTOMER_ADDRESS
  } from '@/components/Config/constants';
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import ConfigToggle from '@/components/Config/shared/cards/ConfigToggle.svelte';
  import NewCustomer from '@/components/Config/sources/evolution/cards/NewCustomer.svelte';
  import DefaultCustomerCode from '@/components/Config/sources/shared/cards/CustomersDefaultCustomerCode.svelte';
  import { TRUE } from '@/constants';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: SourceWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [PARAM_CREATE_CUSTOMER_ENABLED, PARAM_USE_CUSTOMER_ADDRESS]);
</script>

<PageWrapper>
  <ConfigToggle
    bind:workingCopy
    metaKey={PARAM_CREATE_CUSTOMER_ENABLED}
    title="Create Customer"
    label="Create Customer?"
    description="Allow Stock2Shop to create customers in {workingCopy.description}."
  />
  {#if workingCopy.meta[PARAM_CREATE_CUSTOMER_ENABLED]?.value === TRUE}
    <NewCustomer bind:workingCopy />
  {:else}
    <DefaultCustomerCode bind:workingCopy />
  {/if}
  <ConfigToggle
    bind:workingCopy
    metaKey={PARAM_USE_CUSTOMER_ADDRESS}
    title="Use Sage Customer Address"
    label="Use customer address in Sage?"
    description="Use the customer address stored in {workingCopy.description} instead of the address details on the order."
  />
</PageWrapper>
