<script lang="ts">
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';
  import PageCard from '@/components/Config/PageCard.svelte';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import { FALSE, TRUE } from '@/constants';
  import {
    PARAM_CHECK_CREDIT_LIMIT,
    PARAM_COMPLETE_INVOICE,
    PARAM_NEGATIVE_STOCK_DISABLED,
    PARAM_PROCESS_INVOICE,
    PARAM_PROCESS_RESERVED
  } from '@/components/Config/constants';

  export let workingCopy: SourceWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [
    PARAM_PROCESS_RESERVED,
    PARAM_PROCESS_INVOICE,
    PARAM_COMPLETE_INVOICE,
    PARAM_NEGATIVE_STOCK_DISABLED,
    PARAM_CHECK_CREDIT_LIMIT
  ]);

  function flip(keys: string[], val: string) {
    for (const key of keys) {
      if (val === TRUE) {
        // Only flip to false, e.g.
        // don't set param_process_reserved to true
        // if param_process_invoice is set to false
        workingCopy.meta[key].value = FALSE;
      }
    }
  }
</script>

<PageCard title="Invoice">
  <InlineNotification type="info">
    The order has to be saved and not processed to reserve stock quantity on order. This only
    applies to Sales Orders
  </InlineNotification>
  <div class="max-w-max">
    <Toggle
      label="Reserve stock?"
      bind:checked={workingCopy.meta[PARAM_PROCESS_RESERVED].value}
      onChange={(val) => {
        flip([PARAM_PROCESS_INVOICE, PARAM_COMPLETE_INVOICE], val);
      }}
      type="string"
      noLabelBorder
    />
  </div>
  <InlineNotification type="info"
    >Process and complete the invoice if not reserving stock</InlineNotification
  >
  <div class="max-w-max">
    <Toggle
      label="Process invoice?"
      bind:checked={workingCopy.meta[PARAM_PROCESS_INVOICE].value}
      onChange={(val) => flip([PARAM_PROCESS_RESERVED], val)}
      type="string"
      noLabelBorder
    />
    <Toggle
      label="Complete invoice?"
      bind:checked={workingCopy.meta[PARAM_COMPLETE_INVOICE].value}
      onChange={(val) => flip([PARAM_PROCESS_RESERVED], val)}
      type="string"
      noLabelBorder
    />
  </div>
</PageCard>
