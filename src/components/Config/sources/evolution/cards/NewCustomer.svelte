<script lang="ts">
  import TextInput from '@/components/Controls/TextInput.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import PageCard from '@/components/Config/PageCard.svelte';
  import {
    PARAM_NEW_CUSTOMER_AREA_CODE,
    PARAM_NEW_CUSTOMER_GROUP_CODE,
    PARAM_NEW_CUSTOMER_PRICE_LIST_CODE,
    PARAM_NEW_CUSTOMER_REPRESENTATIVE_CODE
  } from '@/components/Config/constants';

  export let workingCopy: SourceWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [
    PARAM_NEW_CUSTOMER_AREA_CODE,
    PARAM_NEW_CUSTOMER_GROUP_CODE,
    PARAM_NEW_CUSTOMER_PRICE_LIST_CODE,
    PARAM_NEW_CUSTOMER_REPRESENTATIVE_CODE
  ]);
</script>

<PageCard title="New customers">
  <InlineNotification type="info">Defaults when creating new customers</InlineNotification>
  <div class="max-w-max">
    <TextInput
      label="Area code"
      bind:value={workingCopy.meta[PARAM_NEW_CUSTOMER_AREA_CODE].value}
      sideLabel
    />
  </div>
  <div class="max-w-max">
    <TextInput
      label="Group code"
      bind:value={workingCopy.meta[PARAM_NEW_CUSTOMER_GROUP_CODE].value}
      sideLabel
    />
  </div>
  <div class="max-w-max">
    <TextInput
      label="Price list code"
      bind:value={workingCopy.meta[PARAM_NEW_CUSTOMER_PRICE_LIST_CODE].value}
      sideLabel
    />
  </div>
  <InlineNotification type="info">Default sales representative for new customers</InlineNotification
  >
  <div class="max-w-max">
    <TextInput
      label="Sales representative code"
      bind:value={workingCopy.meta[PARAM_NEW_CUSTOMER_REPRESENTATIVE_CODE].value}
      sideLabel
    />
  </div>
</PageCard>
