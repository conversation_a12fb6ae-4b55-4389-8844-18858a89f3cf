<script lang="ts">
  import TextInput from '@/components/Controls/TextInput.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import PageCard from '@/components/Config/PageCard.svelte';
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';
  import {
    PARAM_BRANCH_CODE,
    PARAM_DEFAULT_TAX_CODE,
    PARAM_ORDER_PROJECT_CODE,
    PARAM_ORDER_REPRESENTATIVE_CODE,
    PARAM_USE_CHANNEL_ORDER_CODE
  } from '@/components/Config/constants';

  export let workingCopy: SourceWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [
    PARAM_ORDER_PROJECT_CODE,
    PARAM_ORDER_REPRESENTATIVE_CODE,
    PARAM_BRANCH_CODE,
    PARAM_DEFAULT_TAX_CODE,
    PARAM_USE_CHANNEL_ORDER_CODE
  ]);
</script>

<PageCard title="Order codes">
  <InlineNotification type="info">Project code to set on the order</InlineNotification>
  <div class="max-w-max">
    <TextInput
      label="Project code"
      bind:value={workingCopy.meta[PARAM_ORDER_PROJECT_CODE].value}
      sideLabel
    />
  </div>
  <InlineNotification type="info">Lookup the sales representative by code</InlineNotification>
  <div class="max-w-max">
    <TextInput
      label="Representative code"
      bind:value={workingCopy.meta[PARAM_ORDER_REPRESENTATIVE_CODE].value}
      sideLabel
    />
  </div>
  <InlineNotification type="info">Lookup the branch ID by code</InlineNotification>
  <div class="max-w-max">
    <TextInput
      label="Branch code"
      bind:value={workingCopy.meta[PARAM_BRANCH_CODE].value}
      sideLabel
    />
  </div>
  <InlineNotification type="info">Tax code is used to lookup the tax rate</InlineNotification>
  <div class="max-w-max">
    <TextInput
      label="Tax code"
      bind:value={workingCopy.meta[PARAM_DEFAULT_TAX_CODE].value}
      sideLabel
    />
  </div>
  <InlineNotification type="info"
    >A new order number is generated by default, use the channel order code instead?</InlineNotification
  >
  <div class="max-w-max">
    <Toggle
      label="Use channel order code?"
      bind:checked={workingCopy.meta[PARAM_USE_CHANNEL_ORDER_CODE].value}
      type="string"
      noLabelBorder
    />
  </div>
</PageCard>
