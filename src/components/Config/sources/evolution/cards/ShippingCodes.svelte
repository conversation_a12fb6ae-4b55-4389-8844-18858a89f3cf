<script lang="ts">
  import TextInput from '@/components/Controls/TextInput.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import PageCard from '@/components/Config/PageCard.svelte';
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';
  import { TRUE } from '@/constants';
  import {
    PARAM_IGNORE_SHIPPING,
    PARAM_GL_SHIPPING_CODE,
    PARAM_SHIPPING_CODE
  } from '@/components/Config/constants';

  export let workingCopy: SourceWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [
    PARAM_IGNORE_SHIPPING,
    PARAM_GL_SHIPPING_CODE,
    PARAM_SHIPPING_CODE
  ]);
</script>

<PageCard title="Shipping">
  <InlineNotification type="info">Do not add shipping lines to the order.</InlineNotification>
  <div class="max-w-max">
    <Toggle
      label="Ignore shipping?"
      bind:checked={workingCopy.meta[PARAM_IGNORE_SHIPPING].value}
      type="string"
      noLabelBorder
    />
  </div>
  {#if workingCopy.meta[PARAM_IGNORE_SHIPPING].value != TRUE}
    <InlineNotification type="info">
      Lookup GL account and add order detail for shipping.
    </InlineNotification>
    <div class="max-w-max">
      <Toggle
        label="GL shipping code?"
        bind:checked={workingCopy.meta[PARAM_GL_SHIPPING_CODE].value}
        type="string"
        noLabelBorder
      />
    </div>
    {#if workingCopy.meta[PARAM_GL_SHIPPING_CODE].value != TRUE}
      <InlineNotification type="info">
        Lookup stock item by shipping code and set inventory item as order detail. If the inventory
        item is warehouse tracked, then set warehouse as order detail.
      </InlineNotification>
      <div class="max-w-max">
        <TextInput
          label="Shipping code"
          bind:value={workingCopy.meta[PARAM_SHIPPING_CODE].value}
          sideLabel
        />
      </div>
    {/if}
  {/if}
</PageCard>
