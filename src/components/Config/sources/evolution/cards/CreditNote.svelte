<script lang="ts">
  import TextInput from '@/components/Controls/TextInput.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import PageCard from '@/components/Config/PageCard.svelte';
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';
  import {
    PARAM_COMPLETE_CREDIT_NOTE,
    PARAM_GL_DISCOUNT_CODE,
    PARAM_GL_SHIPPING_DISCOUNT_CODE,
    PARAM_PROCESS_CREDIT_NOTE,
    PARAM_USE_CREDIT_NOTE_DISCOUNT
  } from '@/components/Config/constants';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import { TRUE } from '@/constants';

  export let workingCopy: SourceWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [
    PARAM_COMPLETE_CREDIT_NOTE,
    PARAM_GL_DISCOUNT_CODE,
    PARAM_GL_SHIPPING_DISCOUNT_CODE,
    PARAM_PROCESS_CREDIT_NOTE,
    PARAM_USE_CREDIT_NOTE_DISCOUNT
  ]);
</script>

<PageCard title="Credit Note">
  <InlineNotification type="info"
    >Discount is applied as a percentage on the order by default, create a credit note instead?</InlineNotification
  >
  <div class="max-w-max">
    <Toggle
      label="Use credit note discount?"
      bind:checked={workingCopy.meta[PARAM_USE_CREDIT_NOTE_DISCOUNT].value}
      type="string"
      noLabelBorder
    />
  </div>
  {#if workingCopy.meta[PARAM_USE_CREDIT_NOTE_DISCOUNT]?.value === TRUE}
    <InlineNotification type="info"
      >Credit note is only processed or completed if there are non-zero discount or shipping
      discount lines</InlineNotification
    >
    <div class="max-w-max">
      <Toggle
        label="Process credit note?"
        bind:checked={workingCopy.meta[PARAM_PROCESS_CREDIT_NOTE].value}
        type="string"
        noLabelBorder
      />
    </div>
    <div class="max-w-max">
      <Toggle
        label="Complete credit note?"
        bind:checked={workingCopy.meta[PARAM_COMPLETE_CREDIT_NOTE].value}
        type="string"
        noLabelBorder
      />
    </div>
    <InlineNotification type="info"
      >Lookup GL account and add detail for order discount</InlineNotification
    >
    <div class="max-w-max">
      <TextInput
        label="GL discount code"
        bind:value={workingCopy.meta[PARAM_GL_DISCOUNT_CODE].value}
        sideLabel
      />
    </div>
    <InlineNotification type="info"
      >Lookup GL account and add detail for shipping discount</InlineNotification
    >
    <div class="max-w-max">
      <TextInput
        label="GL shipping discount code"
        bind:value={workingCopy.meta[PARAM_GL_SHIPPING_DISCOUNT_CODE].value}
        sideLabel
      />
    </div>
  {/if}
</PageCard>
