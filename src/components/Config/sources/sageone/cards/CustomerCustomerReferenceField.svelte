<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: SourceWorkingCopy;

  const META_CUSTOMER_REFERENCE_FIELD = 'customer_reference_field';

  let isChecked =
    workingCopy.meta[META_CUSTOMER_REFERENCE_FIELD] &&
    workingCopy.meta[META_CUSTOMER_REFERENCE_FIELD].value === 'channel_order_code';

  $: {
    setDefaultConfigMeta(workingCopy, [META_CUSTOMER_REFERENCE_FIELD]);
    if (isChecked) {
      workingCopy.meta[META_CUSTOMER_REFERENCE_FIELD].value = 'channel_order_code';
    } else {
      workingCopy.meta[META_CUSTOMER_REFERENCE_FIELD].value = '';
    }
  }
</script>

<PageCard title="Customer reference">
  <InlineNotification type="info">
    When enabled, Stock2Shop will use the unique channel order ID as the customer reference on all
    orders.
  </InlineNotification>
  <div class="max-w-max">
    <Toggle
      bind:checked={isChecked}
      label="Use the channel order ID as the customer reference"
      noLabelBorder
    />
  </div>
</PageCard>
