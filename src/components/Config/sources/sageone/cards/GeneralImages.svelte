<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: SourceWorkingCopy;

  const META_PARAM_GET_IMAGES_ENABLED = 'param_get_images_enabled';
  const META_QUEUE_FETCH_IMAGES = 'queue_fetch_images';

  let enabled =
    workingCopy.meta[META_QUEUE_FETCH_IMAGES].value === 'true' &&
    workingCopy.meta[META_PARAM_GET_IMAGES_ENABLED].value === 'true';

  $: {
    workingCopy.meta[META_QUEUE_FETCH_IMAGES].value = enabled.toString();
    workingCopy.meta[META_PARAM_GET_IMAGES_ENABLED].value = enabled.toString();
  }

  $: setDefaultConfigMeta(workingCopy, [META_PARAM_GET_IMAGES_ENABLED, META_QUEUE_FETCH_IMAGES]);
</script>

<PageCard title="Image Sync">
  <InlineNotification type="info">
    Allow Stock2Shop to sync product images from {workingCopy.description}.
  </InlineNotification>
  <InlineNotification type="warning">
    Only the following formats are supported: '.jpg' '.jpeg' and '.png'
  </InlineNotification>
  <div class="max-w-max">
    <Toggle bind:checked={enabled} label="Sync Images?" noLabelBorder />
  </div>
</PageCard>
