<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import NumberInput from '@/components/Controls/NumberInput.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import { ORDER_DELIVERY_DATE } from '@/components/Config/constants';

  export let workingCopy: SourceWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [ORDER_DELIVERY_DATE]);
</script>

<!-- TODO: This meta doesn't appear to be used anywhere in app. Remove? -->
<PageCard title="Days to Delivery Date">
  <InlineNotification type="info">
    Set the number of days to add to the created timestamp of the order in Stock2Shop.
  </InlineNotification>
  <div class="max-w-max">
    <NumberInput
      label="Delivery Date"
      sideLabel
      step={1}
      bind:value={workingCopy.meta[ORDER_DELIVERY_DATE].value}
    />
  </div>
</PageCard>
