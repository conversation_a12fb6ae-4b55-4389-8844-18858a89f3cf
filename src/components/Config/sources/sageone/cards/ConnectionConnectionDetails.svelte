<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import Secret from '@/components/Config/shared/components/Secret.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: SourceWorkingCopy;

  const META_USERNAME = 'username';
  const META_PASSWORD = 'password';
  const META_COMPANY_ID = 'company_id';

  $: setDefaultConfigMeta(workingCopy, [META_USERNAME, META_PASSWORD, META_COMPANY_ID]);
</script>

<PageCard title="Connection Details">
  <InlineNotification type="info">
    Your Sage One ERP username and password are required to connect to the Sage One API.
  </InlineNotification>
  <div class="max-w-max">
    <TextInput label="Username" sideLabel bind:value={workingCopy.meta[META_USERNAME].value} />
  </div>
  <Secret
    keyLabel="Password"
    key={META_PASSWORD}
    connectorKind="source"
    connectorId={workingCopy.id}
  />
  <InlineNotification type="info">
    To get your Company ID, log into your Sage One ERP and go to "Company > Open and Manage
    Companies". Hover over the company name that Stock2Shop will be integrated with and note the
    number appearing in the bottom left corner of your browser (may differer depending on the
    browser).
  </InlineNotification>
  <div class="max-w-max">
    <TextInput bind:value={workingCopy.meta[META_COMPANY_ID].value} label="Company ID" sideLabel />
  </div>
</PageCard>
