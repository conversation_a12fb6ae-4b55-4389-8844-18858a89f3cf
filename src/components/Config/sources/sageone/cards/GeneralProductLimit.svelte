<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import NumberInput from '@/components/Controls/NumberInput.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: SourceWorkingCopy;

  const META_GET_PRODUCT_LIMIT = 'get_products_limit';

  $: setDefaultConfigMeta(workingCopy, [META_GET_PRODUCT_LIMIT]);
</script>

<PageCard title="Product Limit">
  <InlineNotification type="info">
    Set the maximum number of products to fetch at a time.
  </InlineNotification>
  <div class="max-w-max">
    <NumberInput bind:value={workingCopy.meta[META_GET_PRODUCT_LIMIT].value} />
  </div>
</PageCard>
