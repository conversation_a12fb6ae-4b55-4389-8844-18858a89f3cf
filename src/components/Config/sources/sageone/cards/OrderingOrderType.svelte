<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import Select from '@/components/Controls/Select/Select.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { ORDER_TYPE } from '@/components/Config/constants';

  export let workingCopy: SourceWorkingCopy;
</script>

<PageCard title="Order type">
  <InlineNotification type="info">
    Choose one of the three supported order types to be used when creating orders on Sage One.
  </InlineNotification>
  <div class="max-w-[352px]">
    <Select
      label="Order Type"
      sideLabel
      bind:value={workingCopy.meta[ORDER_TYPE].value}
      options={[
        { value: 'sales_order', label: 'Sales Order' },
        { value: 'tax_invoice', label: 'Tax Invoice' },
        { value: 'quote', label: 'Quote' }
      ]}
    />
  </div>
</PageCard>
