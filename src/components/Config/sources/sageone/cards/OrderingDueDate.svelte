<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import NumberInput from '@/components/Controls/NumberInput.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import { PARAM_DUE_DATE_DAYS } from '@/components/Config/constants';

  export let workingCopy: SourceWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [PARAM_DUE_DATE_DAYS]);
</script>

<PageCard title="Days to Due Date">
  <InlineNotification type="info">
    Automatically sets the order due date a specified number of days after the order date.
  </InlineNotification>
  <div class="max-w-max">
    <NumberInput
      label="Days"
      sideLabel
      step={1}
      bind:value={workingCopy.meta[PARAM_DUE_DATE_DAYS].value}
    />
  </div>
</PageCard>
