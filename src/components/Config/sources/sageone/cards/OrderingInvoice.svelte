<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';
  import TextArea from '@/components/Controls/TextArea.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: SourceWorkingCopy;

  const META_SEND_INVOICE_ENABLED = 'param_send_invoice_enabled';
  const META_PARAM_INVOICE_SUBJECT = 'param_invoice_subject';
  const META_PARAM_INVOICE_BODY = 'param_invoice_body';

  $: setDefaultConfigMeta(workingCopy, [
    META_SEND_INVOICE_ENABLED,
    META_PARAM_INVOICE_SUBJECT,
    META_PARAM_INVOICE_BODY
  ]);
</script>

<PageCard title="Send Invoice to Customer">
  <InlineNotification type="info">
    Send the Sage One tax invoice to customers after it has been successfully created on Sage. You
    can customize the email subject and body.
  </InlineNotification>
  <div class="max-w-max">
    <Toggle
      label="Send Invoice to Customer?"
      noLabelBorder
      type="string"
      bind:checked={workingCopy.meta[META_SEND_INVOICE_ENABLED].value}
    />
  </div>
  {#if workingCopy.meta[META_SEND_INVOICE_ENABLED].value === 'true'}
    <TextInput bind:value={workingCopy.meta[META_PARAM_INVOICE_SUBJECT].value} label="Subject" />
    <TextArea bind:value={workingCopy.meta[META_PARAM_INVOICE_BODY].value} rows={10} label="Body" />
  {/if}
</PageCard>
