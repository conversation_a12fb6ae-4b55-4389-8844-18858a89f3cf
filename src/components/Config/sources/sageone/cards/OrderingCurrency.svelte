<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: SourceWorkingCopy;

  const META_PARAM_MULTICURRENCY_ENABLED = 'param_multicurrency_enabled';
  const META_PARAM_CURRENCY_CODE = 'param_currency_code';

  $: setDefaultConfigMeta(workingCopy, [
    META_PARAM_MULTICURRENCY_ENABLED,
    META_PARAM_CURRENCY_CODE
  ]);
</script>

<PageCard title="Multi-Currency">
  <InlineNotification type="info">
    <p>
      If your Sage One setup is configured to support multi-currency, you can enable this feature to
      specify the currency code you would like to use.
    </p>
    <p class="bold">Note that the multi-currency module needs to be enabled on Sage One.</p>
  </InlineNotification>
  <div class="max-w-max">
    <Toggle
      label="Specify Currency Code?"
      noLabelBorder
      type="boolean"
      bind:checked={workingCopy.meta[META_PARAM_MULTICURRENCY_ENABLED].value}
    />
  </div>
  {#if workingCopy.meta[META_PARAM_MULTICURRENCY_ENABLED].value}
    <div class="max-w-max">
      <TextInput
        label="Currency code"
        bind:value={workingCopy.meta[META_PARAM_CURRENCY_CODE].value}
        sideLabel
      />
    </div>
  {/if}
</PageCard>
