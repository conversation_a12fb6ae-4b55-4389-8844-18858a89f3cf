<script lang="ts">
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import FetchProducts from '@/components/Config/sources/shared/cards/ProductsFetchProducts.svelte';
  import ProductsSchedule from '@/components/Config/sources/shared/cards/ProductsProductsSchedule.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import ProductLimit from '@/components/Config/sources/sageone/cards/GeneralProductLimit.svelte';

  export let workingCopy: SourceWorkingCopy;
</script>

<PageWrapper>
  <ProductsSchedule bind:workingCopy />
  <FetchProducts bind:workingCopy />
  <ProductLimit bind:workingCopy />
</PageWrapper>
