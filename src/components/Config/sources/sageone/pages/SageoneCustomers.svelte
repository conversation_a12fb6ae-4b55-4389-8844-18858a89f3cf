<script lang="ts">
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import CustomerReferenceField from '@/components/Config/sources/sageone/cards/CustomerCustomerReferenceField.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import DefaultCustomerCode from '@/components/Config/sources/shared/cards/CustomersDefaultCustomerCode.svelte';
  import ConfigToggle from '@/components/Config/shared/cards/ConfigToggle.svelte';
  import {
    PARAM_USE_CUSTOMER_ADDRESS,
    CREATE_CUSTOMER_ENABLED
  } from '@/components/Config/constants';

  export let workingCopy: SourceWorkingCopy;
</script>

<PageWrapper>
  <ConfigToggle
    bind:workingCopy
    label="Create customer?"
    metaKey={CREATE_CUSTOMER_ENABLED}
    title="Create customer"
    description={`Allow Stock2Shop to create a new customer on ${workingCopy.description} if no default customer code is found.`}
  />
  <!-- NOT USING THE ConfigTextInput component purposely -->
  <DefaultCustomerCode bind:workingCopy />
  <CustomerReferenceField bind:workingCopy />
  <ConfigToggle
    bind:workingCopy
    label="Use Sage customer address?"
    metaKey={PARAM_USE_CUSTOMER_ADDRESS}
    title="Address"
    description="Use the customer address stored in Sage One"
  />
</PageWrapper>
