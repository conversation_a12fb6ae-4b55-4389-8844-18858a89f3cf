<script lang="ts">
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import ConfigTextInput from '@/components/Config/shared/cards/ConfigTextInput.svelte';
  import ConfigToggle from '@/components/Config/shared/cards/ConfigToggle.svelte';
  import OrderType from '@/components/Config/sources/sageone/cards/OrderingOrderType.svelte';
  import Invoice from '@/components/Config/sources/sageone/cards/OrderingInvoice.svelte';
  import DeliveryDate from '@/components/Config/sources/sageone/cards/OrderingDeliveryDate.svelte';
  import DueDate from '@/components/Config/sources/sageone/cards/OrderingDueDate.svelte';
  import Currency from '@/components/Config/sources/sageone/cards/OrderingCurrency.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import CreateOrderEnabled from '@/components/Config/shared/cards/CreateOrderEnabled.svelte';

  export let workingCopy: SourceWorkingCopy;

  const META_PARAM_NEGATIVE_STOCK_DISABLED = 'param_negative_stock_disabled';
  const META_PARAM_CHANNEL_ORDER_CODE_ENABLED = 'param_channel_order_code_enabled';
  const META_PARAM_DEFAULT_SHIPPING_CODE = 'param_default_shipping_code';
  const META_PARAM_TAX_EXEMPT_CODE = 'param_tax_exempt_code';
  const META_ORDER_CREATED_DATE_NOW = 'order_created_date_now';
  const META_ORDER_TYPE = 'order_type';
  const META_PARAM_USE_SAGE_PRODUCT_UOM = 'param_use_sage_product_uom';

  $: setDefaultConfigMeta(workingCopy, [
    META_PARAM_NEGATIVE_STOCK_DISABLED,
    META_PARAM_CHANNEL_ORDER_CODE_ENABLED,
    META_PARAM_DEFAULT_SHIPPING_CODE,
    META_PARAM_TAX_EXEMPT_CODE,
    META_ORDER_CREATED_DATE_NOW,
    META_ORDER_TYPE,
    META_PARAM_USE_SAGE_PRODUCT_UOM
  ]);
</script>

<PageWrapper>
  <CreateOrderEnabled bind:workingCopy />

  <ConfigToggle
    bind:workingCopy
    label="Enable"
    metaKey={META_PARAM_NEGATIVE_STOCK_DISABLED}
    title="Allow negative stock ordering"
    description="Allow {workingCopy.description} to have negative stock. If disabled, orders will fail unless stock is available."
  />

  <ConfigToggle
    bind:workingCopy
    label="Enable"
    metaKey={META_PARAM_CHANNEL_ORDER_CODE_ENABLED}
    title="Use channel order code as order code in Sage One"
    description="Use the channel order code as the Invoice or Quote code on Sage One."
  />

  <ConfigTextInput
    bind:workingCopy
    title="Default Shipping Code"
    description="default customer to use when no source customer code provided."
    metaKey={META_PARAM_DEFAULT_SHIPPING_CODE}
    label="Shipping Code"
    sideLabel
  />

  <Invoice bind:workingCopy />

  <ConfigTextInput
    bind:workingCopy
    title="Tax Exempt Code"
    description="Tax exempt code is used when raising an order, default on pastel is 0."
    metaKey={META_PARAM_TAX_EXEMPT_CODE}
    label="Tax Exempt Code"
    sideLabel
  />

  <ConfigToggle
    bind:workingCopy
    label="Use current date as Order Date?"
    metaKey={META_ORDER_CREATED_DATE_NOW}
    title="Use Current Date"
    description="When enabled, orders will be assigned the current date as their creation date. If disabled, the date
    from the sales channel will be used instead."
  />

  <OrderType bind:workingCopy />
  {#if workingCopy.meta['order_type'].value === 'sales_order'}
    <DeliveryDate bind:workingCopy />
  {/if}

  <DueDate bind:workingCopy />

  <Currency bind:workingCopy />

  <ConfigToggle
    bind:workingCopy
    label="Use Sage Product UOM?"
    metaKey={META_PARAM_USE_SAGE_PRODUCT_UOM}
    title="Sage Product UOM"
    description="Set the product's Unit of Measure (UOM) to match the one stored in Sage One."
  />
</PageWrapper>
