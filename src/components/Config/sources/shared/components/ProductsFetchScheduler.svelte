<script lang="ts">
  import Select from '@/components/Controls/Select/Select.svelte';
  import { isString } from '@/utils/typeguards';
  import cronParser from 'cron-parser';

  export let cronValue: string;

  let internalMinuteValue: string =
    cronParser.parseExpression(cronValue).fields.minute.length === 1
      ? cronParser.parseExpression(cronValue).fields.minute[0].toString()
      : '00';
  let internalHourValue: string =
    cronParser.parseExpression(cronValue).fields.hour.length === 1
      ? cronParser.parseExpression(cronValue).fields.hour[0].toString().padStart(2, '0')
      : '*';

  $: {
    if (!isString(internalMinuteValue)) {
      internalMinuteValue = '0';
    } else {
      if (internalHourValue === 'every') {
        cronValue = `${internalMinuteValue.padStart(2, '0')} * * * *`;
      } else {
        cronValue = `${internalMinuteValue.padStart(2, '0')} ${internalHourValue} * * *`;
      }
    }
  }
</script>

<p class=" label flex items-center gap-2">
  <span>At</span>
  <span class="w-16">
    <Select
      bind:value={internalMinuteValue}
      options={[
        ...Array.from({ length: 60 }, (_, i) => ({
          value: i.toString(),
          label: i.toString()
        }))
      ]}
    />
  </span>
  <span class="text-center">minutes past</span>
  <span class="w-28">
    <Select
      bind:value={internalHourValue}
      options={[
        { value: '*', label: 'every hour' },
        ...Array.from({ length: 24 }).map((_, i) => ({
          value: i.toString().padStart(2, '0'),
          label: `${i.toString().padStart(2, '0')}:00`
        }))
      ]}
    />
  </span>
</p>
