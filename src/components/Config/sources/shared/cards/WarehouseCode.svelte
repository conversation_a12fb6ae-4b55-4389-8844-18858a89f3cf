<script lang="ts">
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import PageCard from '@/components/Config/PageCard.svelte';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import { PARAM_IGNORE_WAREHOUSE_CODE, PARAM_WAREHOUSE_CODE } from '@/components/Config/constants';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';
  import { FALSE } from '@/constants';

  export let workingCopy: SourceWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [PARAM_IGNORE_WAREHOUSE_CODE, PARAM_WAREHOUSE_CODE]);
</script>

<PageCard title="Warehouse">
  <InlineNotification type="info"
    >Ignore the warehouse code and it won't be set on the order details</InlineNotification
  >
  <div class="max-w-max">
    <Toggle
      label="Ignore warehouse code?"
      bind:checked={workingCopy.meta[PARAM_IGNORE_WAREHOUSE_CODE].value}
      type="string"
      noLabelBorder
    />
  </div>
  {#if workingCopy.meta[PARAM_IGNORE_WAREHOUSE_CODE]?.value == FALSE}
    <InlineNotification type="info"
      >Default warehouse code for the order line items. If a warehouse code is set for the SKU, that
      will override the default value</InlineNotification
    >
    <div class="max-w-max">
      <TextInput
        label="Default warehouse code"
        bind:value={workingCopy.meta[PARAM_WAREHOUSE_CODE].value}
        sideLabel
      />
    </div>
  {/if}
</PageCard>
