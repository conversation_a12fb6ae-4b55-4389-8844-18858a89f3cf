<script lang="ts">
  import TextInput from '@/components/Controls/TextInput.svelte';
  import PageCard from '../../../PageCard.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import { DEFAULT_SHIPPING_CODE } from '@/components/Config/constants';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';

  export let workingCopy: SourceWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [DEFAULT_SHIPPING_CODE]);
</script>

<PageCard title="Default Shipping Code">
  <InlineNotification type="info">
    The shipping code to use when raising orders into {workingCopy.description}.
  </InlineNotification>
  <div class="max-w-max">
    <TextInput
      sideLabel
      label="Shipping Code"
      bind:value={workingCopy.meta[DEFAULT_SHIPPING_CODE].value}
    />
  </div>
</PageCard>
