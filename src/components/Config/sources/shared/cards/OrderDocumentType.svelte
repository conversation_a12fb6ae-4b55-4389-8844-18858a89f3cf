<script lang="ts">
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import PageCard from '@/components/Config/PageCard.svelte';
  import Select from '@/components/Controls/Select/Select.svelte';
  import { PARAM_ORDER_DOCUMENT_TYPE } from '@/components/Config/constants';

  export let placeholder = 'Select document type';

  export let options = [
    { value: 'Sales Order', label: 'Sales Order' },
    { value: 'Quotation', label: 'Quotation' }
  ];

  export let workingCopy: SourceWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [PARAM_ORDER_DOCUMENT_TYPE]);
</script>

<PageCard title="Order Document Type">
  <InlineNotification type="info">
    Choose the document type to use when creating orders in {workingCopy.description}.
  </InlineNotification>
  <div class="max-w-[377px]">
    <Select
      label="Order type"
      sideLabel
      bind:value={workingCopy.meta[PARAM_ORDER_DOCUMENT_TYPE].value}
      {placeholder}
      {options}
    />
  </div>
</PageCard>
