<script lang="ts">
  import TextInput from '@/components/Controls/TextInput.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import PageCard from '@/components/Config/PageCard.svelte';
  import {
    DEFAULT_CUSTOMER_CODE,
    PARAM_DEFAULT_CUSTOMER_CODE
  } from '@/components/Config/constants';

  export let workingCopy: SourceWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [DEFAULT_CUSTOMER_CODE, PARAM_DEFAULT_CUSTOMER_CODE]);
</script>

<PageCard title="Default customer code">
  <InlineNotification type="info">
    Default customer to use when no source customer code is provided.
  </InlineNotification>
  <div class="max-w-max">
    <!-- ONLY ONE OF THESE WILL EVER BE SET AT A TIME... -->
    {#if workingCopy.meta[PARAM_DEFAULT_CUSTOMER_CODE]?.value !== ''}
      <TextInput
        label="Customer code"
        bind:value={workingCopy.meta[PARAM_DEFAULT_CUSTOMER_CODE].value}
        sideLabel
      />
    {:else}
      <TextInput
        label="Customer code"
        bind:value={workingCopy.meta[DEFAULT_CUSTOMER_CODE].value}
        sideLabel
      />
    {/if}
  </div>
</PageCard>
