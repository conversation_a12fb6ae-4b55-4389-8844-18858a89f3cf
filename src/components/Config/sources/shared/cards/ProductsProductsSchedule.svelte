<script lang="ts">
  import { CRON_GET_PRODUCTS_SCHEDULE } from '@/components/Config/constants';
  import PageCard from '@/components/Config/PageCard.svelte';
  import ProductsFetchScheduler from '@/components/Config/sources/shared/components/ProductsFetchScheduler.svelte';
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import cronParser from 'cron-parser';

  export let workingCopy: SourceWorkingCopy;

  const isValidCron = (value: string) => {
    try {
      cronParser.parseExpression(value);
      return true;
    } catch (e) {
      return false;
    }
  };

  const onChange = (changeValue: boolean) => {
    if (changeValue) {
      if (!workingCopy.meta[CRON_GET_PRODUCTS_SCHEDULE]?.value) {
        setDefaultConfigMeta(workingCopy, [CRON_GET_PRODUCTS_SCHEDULE]);
      } else if (!isValidCron(workingCopy.meta[CRON_GET_PRODUCTS_SCHEDULE]?.value)) {
        workingCopy.meta[CRON_GET_PRODUCTS_SCHEDULE].value = '';
      }
    } else {
      delete workingCopy.meta[CRON_GET_PRODUCTS_SCHEDULE];
      workingCopy.meta = workingCopy.meta;
    }
  };

  let enabled = workingCopy.meta[CRON_GET_PRODUCTS_SCHEDULE]?.value
    ? isValidCron(workingCopy.meta[CRON_GET_PRODUCTS_SCHEDULE]?.value)
    : false;
</script>

<PageCard title="Product Sync">
  <InlineNotification type="info">
    Automatically sync products from {workingCopy.description} to Stock2Shop.
  </InlineNotification>
  <InlineNotification type="warning">Notice: Schedule is based off UTC</InlineNotification>
  <div class="flex flex-col gap-2">
    <div class="max-w-max">
      <Toggle {onChange} label="Enable Automatic Sync?" bind:checked={enabled} noLabelBorder />
    </div>

    {#if enabled}
      <ProductsFetchScheduler bind:cronValue={workingCopy.meta[CRON_GET_PRODUCTS_SCHEDULE].value} />
    {/if}
  </div>
</PageCard>
