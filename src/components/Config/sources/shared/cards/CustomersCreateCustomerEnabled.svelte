<script lang="ts">
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';
  import PageCard from '@/components/Config/PageCard.svelte';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import { CREATE_CUSTOMER_ENABLED } from '@/components/Config/constants';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';

  export let workingCopy: SourceWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [CREATE_CUSTOMER_ENABLED]);
</script>

<PageCard title="Create customer">
  <InlineNotification type="info">
    Allow Stock2Shop to create customers in {workingCopy.description}.
  </InlineNotification>
  <div class="max-w-max">
    <Toggle
      label="Create customer?"
      bind:checked={workingCopy.meta[CREATE_CUSTOMER_ENABLED].value}
      type="string"
      noLabelBorder
    />
  </div>
</PageCard>
