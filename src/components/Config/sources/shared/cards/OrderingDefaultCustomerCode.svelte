<script lang="ts">
  import { PARAM_DEFAULT_CUSTOMER_CODE } from '@/components/Config/constants';
  import PageCard from '@/components/Config/PageCard.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: SourceWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [PARAM_DEFAULT_CUSTOMER_CODE]);
</script>

<PageCard title="Default Customer Code">
  <InlineNotification type="info"
    >Which debtors account do you want to put in {workingCopy.description} for orders?
    <p class="font-bold">Note that this can be overridden by the channel</p></InlineNotification
  >
  <div class="max-w-max">
    <TextInput
      sideLabel
      label="Customer Code"
      bind:value={workingCopy.meta[PARAM_DEFAULT_CUSTOMER_CODE].value}
    />
  </div>
</PageCard>
