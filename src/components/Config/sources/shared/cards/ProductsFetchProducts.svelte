<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import Button from '@/components/Controls/Button.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import { HTTPError } from '@/lib/api/api';
  import { createGetProductsMutation } from '@/mutations/worker.mutation';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { isNumber } from '@/utils/typeguards';
  import { toast } from 'svelte-sonner';

  export let workingCopy: SourceWorkingCopy;

  const fetchProductsMutation = createGetProductsMutation();

  const getSuccessMessage = (productCount?: number) =>
    `${isNumber(productCount) ? `${productCount} products` : `Products`} fetched and added to queue successfully.`;

  const handleFetchProducts = () => {
    toast.promise($fetchProductsMutation.mutateAsync({ sourceId: workingCopy.id }), {
      loading: 'Fetching products...',
      success: (d) => getSuccessMessage(d.worker_product.sync_product),
      error: (e) => (e instanceof HTTPError ? e.message : 'Failed to fetch products')
    });
  };
</script>

{#if workingCopy.meta['sync_mode']?.value === 'pull'}
  <PageCard title="Fetch Products">
    <InlineNotification type="info">
      Fetches products from {workingCopy.description} and adds these products to the queue for processing.
    </InlineNotification>
    <div>
      {#if $fetchProductsMutation.isSuccess}
        <InlineNotification type="success">
          {getSuccessMessage($fetchProductsMutation.data?.worker_product.sync_product)}
          <a href="/queue" class="font-bold">View queue</a>
        </InlineNotification>
      {:else}
        <Button
          on:click={handleFetchProducts}
          disabled={$fetchProductsMutation.isPending}
          size="large">Fetch products</Button
        >
      {/if}
    </div>
  </PageCard>
{/if}
