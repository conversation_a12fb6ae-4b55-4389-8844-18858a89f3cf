<script context="module" lang="ts">
  import { Template, Story } from '@storybook/addon-svelte-csf';
  import Customers from './SharedCustomers.svelte';

  export const meta = {
    component: Customers
  };
</script>

<Template let:args>
  <Customers
    workingCopy={{
      id: 1567,
      type: 'xero',
      description: 'Xero',
      ...args
    }}
  />
</Template>

<Story
  name="Default"
  args={{
    meta: {
      create_customer_enabled: {
        value: ''
      }
    }
  }}
/>

<Story
  name="Create Customer Enabled Toggle:On"
  args={{
    meta: {
      create_customer_enabled: {
        value: 'true'
      }
    }
  }}
/>

<Story
  name="Default Customer Code Populated"
  args={{
    meta: {
      default_customer_code: {
        value: '5148e9d4-35ea-4529-a635-4d3318a9f967'
      }
    }
  }}
/>
