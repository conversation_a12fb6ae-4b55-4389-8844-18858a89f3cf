<script lang="ts">
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import OrderFromDate from '@/components/Config/shared/cards/OrderFromDate.svelte';
  import DefaultCustomerCode from '@/components/Config/sources/shared/cards/OrderingDefaultCustomerCode.svelte';
  import DefaultShippingCode from '@/components/Config/sources/shared/cards/OrderingDefaultShippingCode.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import CreateOrderEnabled from '@/components/Config/shared/cards/CreateOrderEnabled.svelte';
  import { CREATE_ORDER_ENABLED } from '@/components/Config/constants';

  export let workingCopy: SourceWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [CREATE_ORDER_ENABLED]);
</script>

<PageWrapper>
  <OrderFromDate bind:workingCopy />
  <DefaultCustomerCode bind:workingCopy />
  <CreateOrderEnabled bind:workingCopy />
  <DefaultShippingCode bind:workingCopy />
</PageWrapper>
