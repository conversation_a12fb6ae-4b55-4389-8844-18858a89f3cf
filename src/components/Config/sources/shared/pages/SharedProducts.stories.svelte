<script context="module" lang="ts">
  import { Template, Story } from '@storybook/addon-svelte-csf';
  import SharedProducts from './SharedProducts.svelte';

  export const meta = {
    component: SharedProducts
  };
</script>

<Template let:args>
  <div class="max-w-[876px]">
    <SharedProducts
      workingCopy={{
        id: 1567,
        type: 'xero',
        description: 'Xero',
        ...args
      }}
    />
  </div>
</Template>

<Story
  name="Default"
  args={{
    meta: {
      cron_get_products_schedule: {
        value: '* * * * * *'
      }
    }
  }}
/>

<Story
  name="Invalid value for 'cron_get_products_schedule'"
  args={{
    meta: {
      cron_get_products_schedule: {
        value: 'xx 17 * * * *'
      }
    }
  }}
/>

<Story
  name="Sync time set at: 20mins past Every Hour"
  args={{
    meta: {
      cron_get_products_schedule: {
        value: '20 * * * *'
      }
    }
  }}
/>

<Story
  name="Sync time set at: 20mins past 10:00"
  args={{
    meta: {
      cron_get_products_schedule: {
        value: '20 10 * * *'
      }
    }
  }}
/>

<Story
  name="Default"
  args={{
    meta: {
      cron_get_products_schedule: {
        value: '17 * * * *'
      }
    }
  }}
/>

<Story
  name="Enable Automatic Sync Toggle:Off"
  args={{
    meta: {}
  }}
/>
