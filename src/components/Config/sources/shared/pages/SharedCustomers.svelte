<script lang="ts">
  import { CREATE_CUSTOMER_ENABLED } from '@/components/Config/constants';
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import CreateCustomerEnabled from '@/components/Config/sources/shared/cards/CustomersCreateCustomerEnabled.svelte';
  import DefaultCustomerCode from '@/components/Config/sources/shared/cards/CustomersDefaultCustomerCode.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';

  export let workingCopy: SourceWorkingCopy;
</script>

<PageWrapper>
  <CreateCustomerEnabled bind:workingCopy />
  {#if workingCopy.meta[CREATE_CUSTOMER_ENABLED]?.value !== 'true'}
    <DefaultCustomerCode bind:workingCopy />
  {/if}
</PageWrapper>
