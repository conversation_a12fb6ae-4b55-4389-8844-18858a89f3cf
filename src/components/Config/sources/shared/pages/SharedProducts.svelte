<script lang="ts">
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import FetchProducts from '@/components/Config/sources/shared/cards/ProductsFetchProducts.svelte';
  import ProductsSchedule from '@/components/Config/sources/shared/cards/ProductsProductsSchedule.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';

  export let workingCopy: SourceWorkingCopy;
</script>

<PageWrapper>
  <ProductsSchedule bind:workingCopy />
  <FetchProducts bind:workingCopy />
</PageWrapper>
