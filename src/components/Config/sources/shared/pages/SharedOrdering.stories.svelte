<script context="module" lang="ts">
  import { Template, Story } from '@storybook/addon-svelte-csf';
  import SharedSourceOrderingPage from '@/components/Config/sources/shared/pages/SharedOrdering.svelte';

  export const meta = {
    component: SharedSourceOrderingPage
  };
</script>

<Template let:args>
  <div class="max-w-[876px]">
    <SharedSourceOrderingPage
      workingCopy={{
        id: 1567,
        type: 'xero',
        description: 'Xero',
        ...args
      }}
    />
  </div>
</Template>

<Story
  name="Default"
  args={{
    meta: {}
  }}
/>

<Story
  name="Order from Date: Populated"
  args={{
    meta: {
      order_from_date: {
        value: '2024-07-02T10:00:00.000Z'
      }
    }
  }}
/>

<Story
  name="Default Customer Code: Populated"
  args={{
    meta: {
      param_default_customer_code: {
        value: 'Cash'
      }
    }
  }}
/>

<Story
  name="Create Order Enabled Toggle:On"
  args={{
    meta: {
      create_order_enabled: {
        value: 'true'
      }
    }
  }}
/>

<Story
  name="Default Shipping Code: Populated"
  args={{
    meta: {
      create_order_enabled: {
        value: 'true'
      },
      default_shipping_code: {
        value: 'TEST'
      }
    }
  }}
/>
