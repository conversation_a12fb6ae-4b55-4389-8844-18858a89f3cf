<script lang="ts">
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import ConfigToggle from '@/components/Config/shared/cards/ConfigToggle.svelte';
  import { PARAM_SKIP_IMAGE_HASH } from '@/components/Config/constants';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: SourceWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [PARAM_SKIP_IMAGE_HASH]);
</script>

<PageWrapper>
  <!-- TODO Currently there is no sync_products_enabled config.
   Implementing it would require the app backend changing config in local.db
   via the apifact proxy, to enable or disable push products  -->
  <!-- <ConfigToggle
    bind:workingCopy
    metaKey={SYNC_PRODUCTS_ENABLED}
    title="Sync Products"
    label="Enable"
    description="Sync products from this source"
  /> -->
  <ConfigToggle
    bind:workingCopy
    metaKey={PARAM_SKIP_IMAGE_HASH}
    title="Re-sync Images"
    label="Enable"
    description="Enable to resend previously fetched images. Note that images are only fetched
    when the corresponding product has changes"
  />
</PageWrapper>
