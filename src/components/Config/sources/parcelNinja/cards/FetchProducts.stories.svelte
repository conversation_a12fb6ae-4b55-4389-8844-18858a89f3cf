<script context="module">
  import FetchProducts from '@/components/Config/sources/parcelNinja/cards/ProductsFetchProducts.svelte';
  import { Story } from '@storybook/addon-svelte-csf';

  export const meta = {
    component: FetchProducts
  };
</script>

<script lang="ts"></script>

<Story name="Default">
  <div class="max-w-[846px]">
    <div class="p-4">
      <h3>To Note:</h3>
      <p>
        This fetch Products component differs from the one shared between the other sources in that
        it allows for the user to set a start date. Setting the start date makes a request that
        changes the value of the sync token on the source. For parcel ninja, the sync token is
        representative of the date from which to fetch products, i.e. any products that were created
        before that date will not be fetched.
      </p>
    </div>
    <FetchProducts
      workingCopy={{
        id: 1568,
        type: 'parcelninja',
        meta: {
          sync_mode: {
            id: 31287,
            key: 'sync_mode',
            value: 'pull',
            template_name: null
          }
        },
        description: 'parcel ninja'
      }}
    />
  </div>
</Story>
