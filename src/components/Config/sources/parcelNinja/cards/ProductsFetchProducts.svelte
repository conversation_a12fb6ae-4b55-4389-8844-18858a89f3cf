<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import Button from '@/components/Controls/Button.svelte';
  import DateInput from '@/components/Controls/DateInput.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import { HTTPError } from '@/lib/api/api';
  import { updateSourcesMutation } from '@/mutations/sources.mutation';
  import { createGetProductsMutation } from '@/mutations/worker.mutation';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { isNumber } from '@/utils/typeguards';
  import { onMount } from 'svelte';
  import { toast } from 'svelte-sonner';
  import SourcesRepo from '@/repos/sources-repo';

  // REPOS
  const sourcesRepo = new SourcesRepo();

  // PROPS
  export let workingCopy: SourceWorkingCopy;

  // MUTATIONS
  const updateSources = updateSourcesMutation();
  const fetchProductsMutation = createGetProductsMutation();

  // LOCAL STATE
  let newStartDate: string | Date = new Date();

  function createNewSyncTokenFromDate(date: string | Date): string | false {
    if (!date) return false;

    let dateObj = date instanceof Date ? date : new Date(date);

    if (isNaN(dateObj.getTime())) return false;

    let year = dateObj.getFullYear();
    let month = (dateObj.getMonth() + 1).toString();
    let day = dateObj.getDate().toString();

    month = month.length < 2 ? '0' + month : month;
    day = day.length < 2 ? '0' + day : day;

    return year + '-' + month + '-' + day + 'T00:00:00.000Z';
  }

  const getSuccessMessage = (productCount?: number) =>
    `${isNumber(productCount) ? `${productCount} products` : `Products`} fetched and added to queue successfully.`;

  const handleFetchProducts = async () => {
    // First update the sync token of the source to represent the new date
    const sync_token = createNewSyncTokenFromDate(newStartDate);

    if (sync_token) {
      await $updateSources.mutateAsync({
        id: workingCopy.id,
        sync_token
      });
    }

    // Then run the fetch
    toast.promise($fetchProductsMutation.mutateAsync({ sourceId: workingCopy.id }), {
      loading: 'Fetching products...',
      success: (d) => getSuccessMessage(d.worker_product.sync_product),
      error: (e) => (e instanceof HTTPError ? e.message : 'Failed to fetch products')
    });
  };

  onMount(async () => {
    const result = await sourcesRepo.getById(workingCopy.id);
    if (!result?.sync_token) return (newStartDate = new Date());
    newStartDate = new Date(result.sync_token);
  });
</script>

{#if workingCopy.meta['sync_mode']?.value === 'pull'}
  <PageCard title="Fetch Products">
    <InlineNotification type="info">
      Fetches products from {workingCopy.description} and adds these products to the queue for processing.
    </InlineNotification>
    <div>
      {#if $fetchProductsMutation.isSuccess}
        <InlineNotification type="success">
          {getSuccessMessage($fetchProductsMutation.data?.worker_product.sync_product)}
          <a href="/queue" class="font-bold">View queue</a>
        </InlineNotification>
      {:else}
        <div class="max-w-max space-y-2">
          <label class="label" for="fetch-products-start-date">Start Date</label>
          <DateInput
            bind:value={newStartDate}
            id="fetch-products-start-date"
            options={{}}
            required
          />
          <Button
            on:click={handleFetchProducts}
            disabled={$fetchProductsMutation.isPending}
            size="large">Fetch products</Button
          >
        </div>
      {/if}
    </div>
  </PageCard>
{/if}
