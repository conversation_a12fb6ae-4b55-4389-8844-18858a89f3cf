<script lang="ts">
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import OrderFromDate from '@/components/Config/shared/cards/OrderFromDate.svelte';
  import DefaultCustomerCode from '@/components/Config/sources/shared/cards/OrderingDefaultCustomerCode.svelte';
  import DefaultShippingCode from '@/components/Config/sources/shared/cards/OrderingDefaultShippingCode.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import CreateOrderEnabled from '@/components/Config/shared/cards/CreateOrderEnabled.svelte';
  import LocationCode from '@/components/Config/sources/sage300/cards/LocationCode.svelte';

  export let workingCopy: SourceWorkingCopy;
</script>

<PageWrapper>
  <CreateOrderEnabled bind:workingCopy />
  <OrderFromDate bind:workingCopy />
  <DefaultCustomerCode bind:workingCopy />
  <DefaultShippingCode bind:workingCopy />
  <LocationCode bind:workingCopy />
</PageWrapper>
