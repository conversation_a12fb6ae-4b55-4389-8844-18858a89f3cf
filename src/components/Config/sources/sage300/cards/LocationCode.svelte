<script lang="ts">
  import { PARAM_LOCATION_CODE } from '@/components/Config/constants';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import PageCard from '../../../PageCard.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';

  export let workingCopy: SourceWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [PARAM_LOCATION_CODE]);
</script>

<PageCard title="Default Location Code">
  <InlineNotification type="info">
    The default location code to source stock from.
  </InlineNotification>
  <div class="max-w-max">
    <TextInput
      sideLabel
      label="Location Code"
      bind:value={workingCopy.meta[PARAM_LOCATION_CODE].value}
    />
  </div>
</PageCard>
