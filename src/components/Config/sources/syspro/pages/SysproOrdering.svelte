<script lang="ts">
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import OrderFromDate from '@/components/Config/shared/cards/OrderFromDate.svelte';
  import DefaultCustomerCode from '@/components/Config/sources/shared/cards/OrderingDefaultCustomerCode.svelte';
  import DefaultShippingCode from '@/components/Config/sources/shared/cards/OrderingDefaultShippingCode.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import CreateOrderEnabled from '@/components/Config/shared/cards/CreateOrderEnabled.svelte';
  import OrderDocumentType from '@/components/Config/sources/syspro/cards/OrderDocumentType.svelte';
  import OrderingCurrency from '@/components/Config/sources/syspro/cards/OrderingCurrency.svelte';
  import OrderSalesRep from '@/components/Config/sources/syspro/cards/OrderSalesRep.svelte';
  import OrderingDefaultWarehouseCode from '@/components/Config/sources/syspro/cards/OrderingDefaultWarehouseCode.svelte';

  export let workingCopy: SourceWorkingCopy;
</script>

<PageWrapper>
  <CreateOrderEnabled bind:workingCopy />
  <OrderDocumentType bind:workingCopy />
  <OrderingCurrency bind:workingCopy />
  <OrderFromDate bind:workingCopy />
  <DefaultCustomerCode bind:workingCopy />
  <DefaultShippingCode bind:workingCopy />
  <OrderingDefaultWarehouseCode bind:workingCopy />
  <OrderSalesRep bind:workingCopy />
</PageWrapper>
