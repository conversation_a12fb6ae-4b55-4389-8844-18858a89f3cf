<script lang="ts">
  import PageWrapper from '@/components/Config/PageWrapper.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import ConnectionCompany from '@/components/Config/sources/syspro/cards/ConnectionCompany.svelte';
  import ConnectionOperator from '@/components/Config/sources/syspro/cards/ConnectionOperator.svelte';

  export let workingCopy: SourceWorkingCopy;
</script>

<PageWrapper>
  <ConnectionCompany bind:workingCopy />
  <ConnectionOperator bind:workingCopy />
</PageWrapper>
