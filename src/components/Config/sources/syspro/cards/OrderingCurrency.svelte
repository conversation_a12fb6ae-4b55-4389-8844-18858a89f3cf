<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { PARAM_CURRENCY } from '@/components/Config/constants';
  import CurrencyCode from '@/components/Controls/CurrencyCode.svelte';

  export let workingCopy: SourceWorkingCopy;

  $: {
    if (!workingCopy.meta[PARAM_CURRENCY]) {
      workingCopy.meta[PARAM_CURRENCY] = {
        key: PARAM_CURRENCY,
        value: ''
      };
    }
  }
</script>

<PageCard title="Currency Code">
  <InlineNotification type="info">
    The currency to use when raising orders. The default currency configured in {workingCopy.description}
    will be used if this is left empty.
  </InlineNotification>
  <div class="max-w-[331px]">
    <CurrencyCode sideLabel label="Currency" bind:value={workingCopy.meta[PARAM_CURRENCY].value} />
  </div>
</PageCard>
