<script lang="ts">
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import PageCard from '@/components/Config/PageCard.svelte';
  import { PARAM_SALES_REP_ID } from '@/components/Config/constants';
  import TextInput from '@/components/Controls/TextInput.svelte';

  export let workingCopy: SourceWorkingCopy;

  $: {
    if (!workingCopy.meta[PARAM_SALES_REP_ID]) {
      workingCopy.meta[PARAM_SALES_REP_ID] = {
        key: PARAM_SALES_REP_ID,
        value: ''
      };
    }
  }
</script>

<PageCard title="Default Sales Representative">
  <InlineNotification type="info">
    The default sales representative code to use when creating orders in {workingCopy.description}.
    Leave this field empty if you do not wish to set the sales representative.
  </InlineNotification>
  <div class="max-w-max">
    <TextInput
      sideLabel
      label="Sales Representative Code"
      bind:value={workingCopy.meta[PARAM_SALES_REP_ID].value}
    />
  </div>
</PageCard>
