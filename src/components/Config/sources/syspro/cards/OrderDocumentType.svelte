<script lang="ts">
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import PageCard from '@/components/Config/PageCard.svelte';
  import { PARAM_ORDER_TYPE } from '@/components/Config/constants';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: SourceWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [PARAM_ORDER_TYPE]);

  $: {
    if (!workingCopy.meta[PARAM_ORDER_TYPE]) {
      workingCopy.meta[PARAM_ORDER_TYPE] = {
        key: PARAM_ORDER_TYPE,
        value: ''
      };
    }
  }
</script>

<PageCard title="Document Type">
  <InlineNotification type="info">
    The document type to create the order as in {workingCopy.description}. Supported documents are
    Sales Orders, Quotations and Invoices. Enter the name of the document type as it appears in {workingCopy.description}.
  </InlineNotification>
  <div class="max-w-max">
    <TextInput
      sideLabel
      label="Document Type Code"
      bind:value={workingCopy.meta[PARAM_ORDER_TYPE].value}
    />
  </div>
</PageCard>
