<script lang="ts">
  import PageCard from '@/components/Config/PageCard.svelte';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';
  import { PARAM_WAREHOUSE_CODE } from '@/components/Config/constants';

  export let workingCopy: SourceWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [PARAM_WAREHOUSE_CODE]);
</script>

<PageCard title="Default Warehouse Code">
  <InlineNotification type="info">
    <p>The default warehouse code to use when raising orders into {workingCopy.description}.</p>
  </InlineNotification>
  <div class="max-w-max">
    <TextInput
      sideLabel
      label="Warehouse Code"
      bind:value={workingCopy.meta[PARAM_WAREHOUSE_CODE].value}
    />
  </div>
</PageCard>
