<script lang="ts">
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import PageCard from '@/components/Config/PageCard.svelte';
  import { PARAM_OPERATOR, PARAM_OPERATOR_PASSWORD } from '@/components/Config/constants';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import Secret from '@/components/Config/shared/components/Secret.svelte';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: SourceWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [PARAM_OPERATOR, PARAM_OPERATOR_PASSWORD]);
</script>

<PageCard title="Operator">
  <InlineNotification type="info">
    The operator to authenticate with when creating orders in this source.
  </InlineNotification>
  <div class="max-w-max">
    <TextInput sideLabel label="Code" bind:value={workingCopy.meta[PARAM_OPERATOR].value} />
  </div>
  <Secret
    keyLabel="Password"
    key={PARAM_OPERATOR_PASSWORD}
    connectorKind="source"
    connectorId={workingCopy.id}
  />
</PageCard>
