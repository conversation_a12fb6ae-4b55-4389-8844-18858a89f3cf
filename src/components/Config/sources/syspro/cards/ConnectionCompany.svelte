<script lang="ts">
  import type { SourceWorkingCopy } from '@/stores/sourceVersionedObject';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import PageCard from '@/components/Config/PageCard.svelte';
  import { PARAM_COMPANY_ID, PARAM_COMPANY_PASSWORD } from '@/components/Config/constants';
  import TextInput from '@/components/Controls/TextInput.svelte';
  import Secret from '@/components/Config/shared/components/Secret.svelte';
  import { setDefaultConfigMeta } from '@/utils/defaultConfigMeta';

  export let workingCopy: SourceWorkingCopy;

  $: setDefaultConfigMeta(workingCopy, [PARAM_COMPANY_ID, PARAM_COMPANY_PASSWORD]);
</script>

<PageCard title="Company">
  <InlineNotification type="info">
    The company to authenticate with when creating orders in this source.
  </InlineNotification>
  <div class="max-w-max">
    <TextInput sideLabel label="ID" bind:value={workingCopy.meta[PARAM_COMPANY_ID].value} />
  </div>
  <Secret
    keyLabel="Password"
    key={PARAM_COMPANY_PASSWORD}
    connectorKind="source"
    connectorId={workingCopy.id}
  />
</PageCard>
