<script context="module">
  export const meta = {
    title: 'components/General/Toast',
    parameters: {
      chromatic: { delay: 1000 }
    }
  };
</script>

<script lang="ts">
  import { Story, Template } from '@storybook/addon-svelte-csf';

  import { toast } from 'svelte-sonner';

  const fakePromise = async (fail: boolean): Promise<string> =>
    new Promise((resolve, reject) =>
      setTimeout(() => {
        if (!fail) {
          resolve('It works!');
        } else {
          reject('Showing a error works!');
        }
      }, 2000)
    );
</script>

<Template>See toast below</Template>
<Story
  name="Success"
  play={() => {
    toast.success('Success!', { duration: Number.POSITIVE_INFINITY });
  }}
/>
<Story
  name="Error"
  play={() => {
    toast.error('Error!', { duration: Number.POSITIVE_INFINITY });
  }}
/>
<Story
  name="Info"
  play={() => {
    toast.info('Info', { duration: Number.POSITIVE_INFINITY });
  }}
/>
<Story
  name="Successful Promise"
  play={() => {
    toast.promise(() => fakePromise(false), {
      success: (res) => res,
      loading: 'Loading...',
      error: (e) => e.message
    });
  }}
/>
<Story
  name="Failed Promise"
  play={() => {
    toast.promise(() => fakePromise(true), {
      success: (res) => res,
      loading: 'Loading...',
      error: 'Failed!'
    });
  }}
/>
