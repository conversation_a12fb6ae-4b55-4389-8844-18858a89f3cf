<script lang="ts">
  import {
    Chart,
    LineController,
    CategoryScale,
    LinearScale,
    LineElement,
    PointElement,
    Legend,
    Tooltip
  } from 'chart.js';
  import type { ChartConfiguration, ChartItem } from 'chart.js';

  export let config: ChartConfiguration;

  const handleChart = (element: ChartItem, config: ChartConfiguration) => {
    // We use tree shaking to make bundle smaller.
    // Register the components needed here.
    // https://www.chartjs.org/docs/latest/getting-started/usage.html#tree-shaking
    Chart.register(
      LineController,
      CategoryScale,
      LinearScale,
      LineElement,
      PointElement,
      Legend,
      Tooltip
    );

    // Create fresh copy of the chart
    let theChart = new Chart(element, config);
    return {
      update(config: ChartConfiguration) {
        theChart.destroy();
        theChart = new Chart(element, config);
      },
      destroy() {
        theChart.destroy();
      }
    };
  };
</script>

<canvas use:handleChart={config} />
