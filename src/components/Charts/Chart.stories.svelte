<script context="module">
  import Chart from '@/components/Charts/Chart.svelte';

  export const meta = {
    title: 'components/Charts/Chart',
    component: Chart
  };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';
  import type { ChartConfiguration } from 'chart.js';

  let config: ChartConfiguration = {
    type: 'line',
    data: {
      datasets: [
        {
          label: 'Completed',
          data: [800, 0, 200, 0, 0, 300, 750, 1000, 400],
          borderColor: 'rgb(53, 162, 235)',
          backgroundColor: 'rgba(53, 162, 235, 0.5)',
          pointBackgroundColor: 'white',
          borderWidth: 1
        },
        {
          label: 'Error',
          data: [0, 200, 0, 0, 0, 0, 10, 0, 200],
          borderColor: 'rgb(255, 99, 132)',
          backgroundColor: 'rgba(255, 99, 132, 0.5)',
          pointBackgroundColor: 'white',
          borderWidth: 1
        }
      ],
      labels: ['05:45', '06:00', '06:15', '06:30', '06:45', '07:00', '07:15']
    }
  };
</script>

<Story name="Line Chart">
  <div id="Page">
    <Chart {config} />
  </div>
</Story>
