<script lang="ts">
  import DashboardHeader from './DashboardHeader.svelte';
  import DashboardInfoBlock from './DashboardInfoBlock.svelte';
  import type { RangeKey } from '@/models/logs/ranges';
  import DashboardInfoError from '@/components/Dashboard/DashboardInfoError.svelte';
  import type { EntityKey } from '@/models/logs/entity';
  import { createConfigQuery } from '@/queries/config.query';
  import { hasFulfillmentService, hasTradeChannel } from '@/models/config/config';
  import DashboardInfoBlockNoTradeCustomer from '@/components/Dashboard/DashboardInfoBlockNoTradeCustomer.svelte';
  import DashboardInfoBlockNoFulfillment from '@/components/Dashboard/DashboardInfoBlockNoFulfillment.svelte';
  import type { InfoErrorResponse } from '@/models/logs/info-error-response';
  import { createLogsInfoErrorQuery, createLogsSourceSyncQuery } from '@/queries/logs.query';
  import DashboardSync from '@/components/Dashboard/DashboardSync.svelte';
  import { createQueueInstructionsQuery } from '@/queries/queue.query';
  import Skeleton from '@/components/Loader/Skeleton.svelte';

  let range: RangeKey = '1 Week';
  let entity: EntityKey = 'Products';

  // Static Queries
  const queryConfig = createConfigQuery();
  const queryLogsSourceSync = createLogsSourceSyncQuery();
  const queueInstructionQuery = createQueueInstructionsQuery();

  // Dynamic Queries
  $: queryEgressProducts = createLogsInfoErrorQuery(range, 'Products', 'egress');
  $: queryEgressCustomers = createLogsInfoErrorQuery(range, 'Customers', 'egress');
  $: queryEgressOrders = createLogsInfoErrorQuery(range, 'Orders', 'egress');
  $: queryEgressFulfillments = createLogsInfoErrorQuery(range, 'Fulfillments', 'egress');
  $: queryIngressProducts = createLogsInfoErrorQuery(range, 'Products', 'ingress');
  $: queryIngressCustomers = createLogsInfoErrorQuery(range, 'Customers', 'ingress');
  $: queryIngressOrders = createLogsInfoErrorQuery(range, 'Orders', 'ingress');
  $: queryIngressFulfillments = createLogsInfoErrorQuery(range, 'Fulfillments', 'ingress');

  // check if trade channel and fulfillment service exist
  $: isTradeChannel = $queryConfig.data && hasTradeChannel($queryConfig.data);
  $: isFulfillmentService = $queryConfig.data && hasFulfillmentService($queryConfig.data);

  // set loading state
  // We check ".isFetchedAfterMount" because only checking for
  // ".data" causes the chart to be fired twice
  $: isLoading =
    $queryEgressProducts.isFetching ||
    $queryEgressProducts.isFetching ||
    $queryEgressCustomers.isFetching ||
    $queryEgressOrders.isFetching ||
    $queryEgressFulfillments.isFetching ||
    $queryIngressProducts.isFetching ||
    $queryIngressCustomers.isFetching ||
    $queryIngressOrders.isFetching ||
    $queryIngressFulfillments.isFetching ||
    !$queryEgressProducts.isFetchedAfterMount ||
    !$queryEgressProducts.isFetchedAfterMount ||
    !$queryEgressCustomers.isFetchedAfterMount ||
    !$queryEgressOrders.isFetchedAfterMount ||
    !$queryEgressFulfillments.isFetchedAfterMount ||
    !$queryIngressProducts.isFetchedAfterMount ||
    !$queryIngressCustomers.isFetchedAfterMount ||
    !$queryIngressOrders.isFetchedAfterMount ||
    !$queryIngressFulfillments.isFetchedAfterMount;

  // set graph log data depending on entity
  let graphLogsEgress: InfoErrorResponse | undefined;
  let graphLogsIngress: InfoErrorResponse | undefined;
  $: if (entity === 'Products') {
    graphLogsEgress = $queryEgressProducts.data;
    graphLogsIngress = $queryIngressProducts.data;
  } else if (entity === 'Customers') {
    graphLogsEgress = $queryEgressCustomers.data;
    graphLogsIngress = $queryIngressCustomers.data;
  } else if (entity === 'Orders') {
    graphLogsEgress = $queryEgressOrders.data;
    graphLogsIngress = $queryIngressOrders.data;
  } else if (entity === 'Fulfillments') {
    graphLogsEgress = $queryEgressFulfillments.data;
    graphLogsIngress = $queryIngressFulfillments.data;
  }
</script>

<div>
  <div>
    <DashboardHeader bind:range />
  </div>
  <div class="mt-7">
    {#if isLoading}
      <Skeleton class="h-16 grow" shimmer={true} />
    {:else}
      <DashboardSync
        {isLoading}
        config={$queryConfig.data}
        sourceSync={$queryLogsSourceSync.data}
      />
    {/if}
  </div>
  <div class="mt-7">
    <div class="grid gap-6 lg:grid-cols-2 xl:grid-cols-4">
      <DashboardInfoBlock
        {isLoading}
        {range}
        logsEgress={$queryEgressProducts.data}
        logsIngress={$queryIngressProducts.data}
        entity="Products"
      />
      {#if isTradeChannel}
        <DashboardInfoBlock
          {isLoading}
          {range}
          logsEgress={$queryEgressCustomers.data}
          logsIngress={$queryIngressCustomers.data}
          entity="Customers"
        />
      {:else}
        <DashboardInfoBlockNoTradeCustomer {isLoading} />
      {/if}
      <DashboardInfoBlock
        {isLoading}
        {range}
        logsEgress={$queryEgressOrders.data}
        logsIngress={$queryIngressOrders.data}
        entity="Orders"
      />
      {#if isFulfillmentService}
        <DashboardInfoBlock
          {isLoading}
          {range}
          logsEgress={$queryEgressFulfillments.data}
          logsIngress={$queryIngressFulfillments.data}
          entity="Fulfillments"
        />
      {:else}
        <DashboardInfoBlockNoFulfillment {isLoading} />
      {/if}
    </div>
  </div>
  <div class="mt-7">
    <div class="grid gap-6 lg:grid-cols-1 xl:grid-cols-2">
      <DashboardInfoError
        bind:entity
        {range}
        logs={graphLogsIngress}
        direction="ingress"
        {isLoading}
        instructions={$queueInstructionQuery.data}
        config={$queryConfig.data}
      />
      <DashboardInfoError
        bind:entity
        {range}
        logs={graphLogsEgress}
        direction="egress"
        {isLoading}
        instructions={$queueInstructionQuery.data}
        config={$queryConfig.data}
      />
    </div>
  </div>
</div>
