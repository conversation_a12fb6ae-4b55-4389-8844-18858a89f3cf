<script lang="ts">
  import DateInput from '@/components/Controls/DateInput.svelte';
  import { getDateFromStringOrTimestamp, getUTCDateTime } from '@/lib/date-utils';
  import flatpickr from 'flatpickr';

  export let value: string | undefined;

  let fromFormatted = value?.split('.')[0];
  let toFormatted = value?.split('.')[1];

  $: fromInternal = value?.split('.')[0]
    ? getDateFromStringOrTimestamp(value?.split('.')[0])
    : undefined;
  $: toInternal = value?.split('.')[1]
    ? getDateFromStringOrTimestamp(value?.split('.')[1])
    : undefined;

  const options: flatpickr.Options.Options = {
    enableTime: true,
    dateFormat: 'Y-m-d H:i:S',
    altInput: true,
    altFormat: 'Y-m-d h:i K',

    onChange: (_, __, instance) => {
      instance.close();
    }
  };

  $: {
    const fromUtc = fromFormatted ? getUTCDateTime(fromFormatted) : undefined;
    const toUtc = toFormatted ? getUTCDateTime(toFormatted) : undefined;
    if (fromUtc && toUtc) {
      value = `${fromUtc}.${toUtc}`;
    } else if (fromUtc) {
      value = `${fromUtc}.`;
    } else if (toUtc) {
      value = `.${toUtc}`;
    } else {
      value = '';
    }
  }
</script>

<div class="flex flex-col gap-2 pt-2">
  <DateInput
    placeholder="From Date"
    size="small"
    bind:formattedValue={fromFormatted}
    bind:value={fromInternal}
    options={{ ...options, maxDate: toInternal }}
    canClear
  />
  <DateInput
    placeholder="To Date"
    size="small"
    bind:formattedValue={toFormatted}
    bind:value={toInternal}
    options={{ ...options, minDate: fromInternal }}
    canClear
  />
</div>
