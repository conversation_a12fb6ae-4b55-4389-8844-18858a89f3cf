<script lang="ts">
  import RadioGroup from '@/components/Controls/Switches/RadioGroup.svelte';
  import type { FacetRadioOption } from '@/components/ES/Facets';
  import FacetCount from '@/components/ES/Facets/Shared/FacetCount.svelte';
  import Skeleton from '@/components/Loader/Skeleton.svelte';

  export let isLoading = false;
  export let value: string;
  export let options: FacetRadioOption[] | undefined;
</script>

<div class="pl-2 pt-2">
  {#if !options}
    <Skeleton shimmer class="mt-2.5 h-6 w-full" />
    <Skeleton shimmer class="mt-2.5 h-6 w-full" />
    <Skeleton shimmer class="mt-2.5 h-6 w-full" />
  {:else if options.length === 0}
    {#if isLoading}
      <Skeleton shimmer class="mt-2.5 h-6 w-full" />
    {:else}
      <span class="pl-2 text-regular italic text-neutral-700">No Results</span>
    {/if}
  {:else if isLoading}
    <Skeleton shimmer class="mt-2.5 h-6 w-full" />
  {:else}
    <RadioGroup {options} bind:value let:option>
      {#if option.count}
        <FacetCount>
          {option.count}
        </FacetCount>
      {/if}
    </RadioGroup>
  {/if}
</div>
