<script lang="ts">
  import Icon from '@/components/Icons/Icon.svelte';
  import type { Facet } from '@/models/es/facet';
  import Skeleton from '@/components/Loader/Skeleton.svelte';
  import IconClose from '@/components/Icons/IconClose.svelte';
  import type { Bucket } from '@/lib/es/types';
  import FacetCount from '@/components/ES/Facets/Shared/FacetCount.svelte';

  export let isLoading: boolean;
  export let facet: Facet;
  export let value: string;
  export let buckets: Bucket[] | undefined;

  export let getSelectedTitle = (key: string) => {
    if (facet.getSelectedTitle) {
      return facet.getSelectedTitle(key);
    }
    return key;
  };

  const addFilter = (key: string) => {
    value = key;
  };

  const removeFilter = () => {
    value = '';
  };
</script>

{#if value}
  <div
    class="flex h-7 w-full items-center rounded bg-brand-selected pl-2 pr-1 text-regular text-brand-notification"
  >
    <span class="h-4 grow overflow-hidden truncate text-ellipsis text-left leading-4">
      {getSelectedTitle(value)}
    </span>
    <button
      on:click={removeFilter}
      class="flex h-4 w-4 items-center justify-center rounded-full bg-brand-selectedAction"
    >
      <Icon IconComponent={IconClose} size="smallest" />
    </button>
  </div>
{/if}
{#if !value}
  <div>
    {#if !buckets}
      <Skeleton shimmer class="mt-2.5 h-6 w-full" />
      <Skeleton shimmer class="mt-2.5 h-6 w-full" />
      <Skeleton shimmer class="mt-2.5 h-6 w-full" />
    {:else if buckets.length === 0}
      {#if isLoading}
        <Skeleton shimmer class="mt-2.5 h-6 w-full" />
      {:else}
        <span class="pl-2 text-regular italic text-neutral-700">No Results</span>
      {/if}
    {:else}
      {#each buckets as bucket}
        {#if isLoading}
          <Skeleton shimmer class="mt-2.5 h-6 w-full" />
        {:else}
          <button
            on:click={() => addFilter(bucket.key)}
            class="flex h-7 w-full items-center gap-2 pl-2 pr-1 text-regular text-neutral-700"
          >
            <span class="grow overflow-hidden truncate text-ellipsis text-left">
              {bucket.title ? bucket.title : getSelectedTitle(bucket.key)}
            </span>
            {#if bucket.doc_count}
              <FacetCount>
                {bucket.doc_count}
              </FacetCount>
            {/if}
          </button>
        {/if}
      {/each}
    {/if}
  </div>
{/if}
