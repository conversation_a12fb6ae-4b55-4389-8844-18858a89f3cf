/**
 * @module types here should map 1:1 with components in this folder in camelCase
 */

export const FACET_TYPE_BUCKET = 'buckets' as const;
export const FACET_TYPE_DATE_FROM_TO = 'dateFromTo' as const;
export const FACET_TYPE_RADIO = 'radio' as const;
export type FacetRadioOption = { label: string; value: string; count?: number; default?: boolean };

export const FACET_TYPES = [FACET_TYPE_BUCKET, FACET_TYPE_DATE_FROM_TO, FACET_TYPE_RADIO] as const;
