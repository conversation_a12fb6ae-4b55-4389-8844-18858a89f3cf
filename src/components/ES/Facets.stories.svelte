<script context="module">
  import FacetsComponent from '@/components/ES/Facets.svelte';

  export const meta = {
    title: 'components/ES/Facets',
    component: FacetsComponent
  };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';
  import type { Facet } from '@/models/es/facet';
  import type { Aggregations } from '@/lib/es/types';

  let filterTerms = {
    size: 'my value is long',
    color: '',
    custom: ''
  };

  const aggsResponse: Aggregations = {
    size: {
      buckets: [
        {
          key: 'small',
          doc_count: 2
        }
      ]
    },
    color: {
      buckets: [
        {
          key: 'small',
          doc_count: 2
        },
        {
          key: 'a very very long name that probably wont fit in the box',
          doc_count: 10
        },
        {
          key: 'a big count',
          doc_count: 1234210
        }
      ]
    },
    custom: {
      buckets: [
        {
          key: 'small',
          doc_count: 2
        },
        {
          key: 'a very very long name that probably wont fit in the box',
          doc_count: 10
        }
      ]
    }
  };

  const facets: Facet[] = [
    {
      key: 'size',
      title: 'Filter Selected',
      type: 'buckets',
      buckets: aggsResponse.size?.buckets
    },
    {
      key: 'color',
      title: 'My Colour',
      type: 'buckets',
      buckets: aggsResponse.color?.buckets
    },
    {
      key: 'custom',
      title: 'Another list custom key',
      type: 'buckets',
      buckets: aggsResponse.custom?.buckets,
      getSelectedTitle: () => 'custom title that can be derived from a function'
    }
  ];
</script>

<Story name="Primary">
  <div id="Page" class="min-w-[260px] max-w-[260px]">
    <FacetsComponent bind:filterTerms isLoading={false} {facets} />
  </div>
</Story>

<Story name="Is Loading">
  <div id="Page" class="min-w-[260px] max-w-[260px]">
    <FacetsComponent bind:filterTerms isLoading={true} {facets} />
  </div>
</Story>
