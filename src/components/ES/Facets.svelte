<script lang="ts">
  import IconExpandAll from '@/components/Icons/IconExpandAll.svelte';
  import IconCollapseAll from '@/components/Icons/IconCollapseAll.svelte';
  import Icon from '@/components/Icons/Icon.svelte';
  import BucketsFacet from '@/components/ES/Facets/BucketsFacet.svelte';
  import type { Facet } from '@/models/es/facet';
  import type { FilterTerms } from '@/models/es/filter';
  import FacetContainer from '@/components/ES/FacetContainer.svelte';
  import DateFromToFacet from '@/components/ES/Facets/DateFromToFacet.svelte';
  import {
    FACET_TYPE_BUCKET,
    FACET_TYPE_DATE_FROM_TO,
    FACET_TYPE_RADIO
  } from '@/components/ES/Facets';
  import RadioFacet from '@/components/ES/Facets/RadioFacet.svelte';
  import Button from '@/components/Controls/Button.svelte';

  export let isLoading: boolean;
  export let facets: Facet[];
  export let filterTerms: FilterTerms = {};
  /**
   * Bind this if you want the search input to get reset on `Reset Filters` click
   */
  export let search: string | null = null;

  let collapsed: boolean = false;

  // toggle collapsed state
  const toggle = () => {
    collapsed = !collapsed;
  };

  // reset filters
  const resetFilters = () => {
    search = null;
    facets.forEach((facet) => {
      if (facet.type === 'radio') {
        filterTerms[facet.key] = facet.options.find((opt) => opt.default === true)?.value || '';
        return;
      }
      filterTerms[facet.key] = '';
    });
  };
</script>

<!--Header-->
<div class="flex items-center justify-between p-4">
  <Button on:click={resetFilters} variant="gray-outline" size="small">Reset Filters</Button>
  <button on:click={toggle}>
    <Icon IconComponent={collapsed ? IconExpandAll : IconCollapseAll} size="extra-small" />
  </button>
</div>

{#each facets as facet}
  <div class="border-t border-neutral-200">
    <FacetContainer {collapsed} {facet}>
      {#if facet.type === FACET_TYPE_BUCKET || !facet.type}
        <BucketsFacet
          {facet}
          bind:value={filterTerms[facet.key]}
          buckets={facet.buckets}
          {isLoading}
        />
      {:else if facet.type === FACET_TYPE_RADIO}
        <RadioFacet bind:value={filterTerms[facet.key]} options={facet.options} {isLoading} />
      {:else if facet.type === FACET_TYPE_DATE_FROM_TO}
        <DateFromToFacet bind:value={filterTerms[facet.key]} />
      {/if}
    </FacetContainer>
  </div>
{/each}
