<script lang="ts">
  import Icon from '@/components/Icons/Icon.svelte';
  import IconChevronUp from '@/components/Icons/IconChevronUp.svelte';
  import IconChevronDown from '@/components/Icons/IconChevronDown.svelte';
  import type { Facet } from '@/models/es/facet';

  export let facet: Facet;
  export let collapsed = false;

  const toggle = () => {
    collapsed = !collapsed;
  };
</script>

{#if facet}
  <div class="w-full space-y-2 p-3">
    <button
      on:click={toggle}
      class="flex h-7 w-full grow items-center justify-between overflow-hidden truncate text-ellipsis p-1 text-left text-smaller text-neutral-700"
    >
      <span class="w-full text-left font-bold uppercase tracking-widest">{facet.title}</span>
      <span class="h-4 w-4">
        <Icon IconComponent={collapsed ? IconChevronDown : IconChevronUp} size="extra-small" />
      </span>
    </button>
    {#if !collapsed}
      <slot />
    {/if}
  </div>
{/if}
