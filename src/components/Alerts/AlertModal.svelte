<script lang="ts">
  import ModalInternal from '@/components/Modal/ModalInternal.svelte';
  import LabelStatus from '@/components/Controls/LabelStatus.svelte';
  import type { Alert } from '@/models/alerts/alert';
  import AlertRows from '@/components/Alerts/AlertRows.svelte';
  import AlertRowSkeleton from '@/components/Alerts/AlertRowSkeleton.svelte';
  import { goto } from '$app/navigation';

  export let alerts: Alert[] | undefined;
  export let open: boolean;
  export let isLoading: boolean;

  // if we need, we can add "action" property on alerts to do some custom thing
  // at the moment we just redirect to the linklink
  const close = (event: CustomEvent<Alert>) => {
    if (event.detail.link) {
      goto(event.detail.link);
    }
    open = false;
  };
</script>

<ModalInternal bind:open position="right" topPosition="top-16" size="narrow">
  <div slot="header-prefix">
    <div class="flex gap-2">
      <LabelStatus type="info">Notices</LabelStatus>
      <LabelStatus type="warning">Warnings</LabelStatus>
      <LabelStatus type="error">Errors</LabelStatus>
    </div>
  </div>
  <div slot="body">
    <div class="-mt-4">
      {#if !alerts}
        <AlertRowSkeleton />
        <AlertRowSkeleton />
        <AlertRowSkeleton />
      {:else}
        <AlertRows {alerts} {isLoading} on:alertClick={close} />
      {/if}
    </div>
  </div>
</ModalInternal>
