<script context="module">
  import AlertModal from '@/components/Alerts/AlertModal.svelte';

  export const meta = {
    title: 'components/Alerts/AlertModal',
    component: AlertModal,
    parameters: {
      chromatic: { delay: 1000 }
    }
  };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';

  let openModal = true;
</script>

<Story name="Primary">
  <div id="Page" class="">
    <button on:click={() => (openModal = true)}>open</button>
    {#if openModal}
      <AlertModal
        bind:open={openModal}
        isLoading={false}
        alerts={[
          {
            title: 'Alert title',
            message:
              'Alert message that could be a really really long message that wraps to the next line',
            status: 'info',
            date: '2024-01-11 12:13:05',
            link: '/queue'
          },
          {
            title: 'Some error',
            message:
              'Alert message that could be a really really long message that wraps to the next line',
            status: 'error',
            date: '2024-01-11 12:13:05',
            link: '/queue'
          },
          {
            title: 'A really long title that could break a line if it gets to the end of the line',
            message: 'Word wrap on title',
            status: 'warning',
            date: '2024-01-11 12:13:05',
            link: '/queue'
          },
          {
            title: 'Word Wrap on body',
            message:
              'If the description is longer than 3 lines, it should be truncated with a read more link that expands the description of the alert like an accordion. We should also add a character limit here to avoid it breaking the UI.',
            status: 'warning',
            date: '2024-01-11 12:13:05',
            link: '/queue'
          },
          {
            title: 'Large Payload',
            message: `HTTP/1.1 400 Bad Request Server: nginx/1.18.0 Date: Tue, 23 Apr 2024 07:43:49 GMT Content-Type: application/json; charset=utf-8 Transfer-Encoding: chunked Connection: keep-alive X-Powered-By: PHP/8.1.6 Set-Cookie: PHPSESSID=62b770dbe902e1a204cd14e240502c78; expires=Tue, 23-Apr-2024 08:43:48 GMT; Max-Age=3600; path=/; domain=magento.test; secure; HttpOnly; SameSite=Lax Expires: Thu, 19 Nov 1981 08:52:00 GMT Pragma: no-cache errorRedirectAction: #shipping Cache-Control: no-store {"message":"The stock item was unable to be saved. Please try again.","trace":"#0  var www html vendor magento module-catalog-inventory Model StockRegistry.php(181): Magento CatalogInventory Model Stock StockItemRepository->save(Object(Magento CatalogInventory Model Stock Item Interceptor)) #1  var www html vendor magento framework Interception Interceptor.php(58): Magento CatalogInventory Model StockRegistry->updateStockItemBySku('TEST_PRODUCT1', Object(Magento CatalogInventory Model Stock Item Interceptor)) #2  var www html vendor magento framework Interception Interceptor.php(138): Magento CatalogInventory Model StockRegistry Interceptor->___callParent('updateStockItem...', Array) #3  var www html vendor magento framework Interception Interceptor.php(153): Magento CatalogInventory Model StockRegistry Interceptor->Magento Framework Interception {closure}('TEST_PRODUCT1', Object(Magento CatalogInventory Model Stock Item Interceptor)) #4  var www html generated code Magento CatalogInventory Model StockRegistry Interceptor.php(95): Magento CatalogInventory Model StockRegistry Interceptor->___callPlugins('updateStockItem...', Array, Array) #5  var www html vendor magento module-catalog-inventory Observer SaveInventoryDataObserver.php(125): Magento CatalogInventory Model StockRegistry Interceptor->updateStockItemBySku('TEST_PRODUCT1', Object(Magento CatalogInventory Model Stock Item Interceptor)) #6  var www html vendor magento framework Event Invoker InvokerDefault.php(88): Magento CatalogInventory Observer SaveInventoryDataObserver->execute(Object(Magento Framework Event Observer)) #7  var www html vendor magento framework Event Invoker InvokerDefault.php(74): Magento Framework Event Invoker InvokerDefault->_callObserverMethod(Object(Magento CatalogInventory Observer SaveInventoryDataObserver), Object(Magento Framework Event Observer)) #8  var www html vendor magento framework Event Manager.php(65): Magento Framework Event Invoker InvokerDefault->dispatch(Array, Object(Magento Framework Event Observer)) #9  var www html generated code Magento Framework Event Manager Proxy.php(95): Magento Framework Event Manager->dispatch('catalog_product...', Array) #10  var www html vendor magento framework Model AbstractModel.php(837): Magento Framework Event Manager Proxy->dispatch('catalog_product...', Array) #11  var www html vendor magento module-catalog Model Product.php(985): Magento Framework Model AbstractModel->afterSave() #12  var www html generated code Magento Catalog Model Product Interceptor.php(248): Magento Catalog Model Product->afterSave() #13  var www html vendor magento framework EntityManager Observer AfterEntitySave.php(34): Magento Catalog Model Product Interceptor->afterSave() #14  var www html vendor magento framework Event Invoker InvokerDefault.php(88): Magento Framework EntityManager Observer AfterEntitySave->execute(Object(Magento Framework Event Observer)) #15  var www html vendor magento framework Event Invoker InvokerDefault.php(74): Magento Framework Event Invoker InvokerDefault->_callObserverMethod(Object(Magento Framework EntityManager Observer AfterEntitySave), Object(Magento Framework Event Observer)) #16  var www html vendor magento framework Event Manager`,
            status: 'error',
            date: '2024-01-11 12:13:05',
            link: '/queue'
          },
          {
            title: 'Large Payload - escaped',
            message: `HTTP/1.1 400 Bad Request Server: nginx/1.18.0 Date: Tue, 23 Apr 2024 07:43:49 GMT Content-Type: application/json; charset=utf-8 Transfer-Encoding: chunked Connection: keep-alive X-Powered-By: PHP/8.1.6 Set-Cookie: PHPSESSID=62b770dbe902e1a204cd14e240502c78; expires=Tue, 23-Apr-2024 08:43:48 GMT; Max-Age=3600; path=/; domain=magento.test; secure; HttpOnly; SameSite=Lax Expires: Thu, 19 Nov 1981 08:52:00 GMT Pragma: no-cache errorRedirectAction: #shipping Cache-Control: no-store {"message":"The stock item was unable to be saved. Please try again.","trace":"#0 \/var\/www\/html\/vendor\/magento\/module-catalog-inventory\/Model\/StockRegistry.php(181): Magento\\CatalogInventory\\Model\\Stock\\StockItemRepository->save(Object(Magento\\CatalogInventory\\Model\\Stock\\Item\\Interceptor))\n#1 \/var\/www\/html\/vendor\/magento\/framework\/Interception\/Interceptor.php(58): Magento\\CatalogInventory\\Model\\StockRegistry->updateStockItemBySku('TEST_PRODUCT1', Object(Magento\\CatalogInventory\\Model\\Stock\\Item\\Interceptor))\n#2 \/var\/www\/html\/vendor\/magento\/framework\/Interception\/Interceptor.php(138): Magento\\CatalogInventory\\Model\\StockRegistry\\Interceptor->___callParent('updateStockItem...', Array)\n#3 \/var\/www\/html\/vendor\/magento\/framework\/Interception\/Interceptor.php(153): Magento\\CatalogInventory\\Model\\StockRegistry\\Interceptor->Magento\\Framework\\Interception\\{closure}('TEST_PRODUCT1', Object(Magento\\CatalogInventory\\Model\\Stock\\Item\\Interceptor))\n#4 \/var\/www\/html\/generated\/code\/Magento\/CatalogInventory\/Model\/StockRegistry\/Interceptor.php(95): Magento\\CatalogInventory\\Model\\StockRegistry\\Interceptor->___callPlugins('updateStockItem...', Array, Array)\n#5 \/var\/www\/html\/vendor\/magento\/module-catalog-inventory\/Observer\/SaveInventoryDataObserver.php(125): Magento\\CatalogInventory\\Model\\StockRegistry\\Interceptor->updateStockItemBySku('TEST_PRODUCT1', Object(Magento\\CatalogInventory\\Model\\Stock\\Item\\Interceptor))\n#6 \/var\/www\/html\/vendor\/magento\/framework\/Event\/Invoker\/InvokerDefault.php(88): Magento\\CatalogInventory\\Observer\\SaveInventoryDataObserver->execute(Object(Magento\\Framework\\Event\\Observer))\n#7 \/var\/www\/html\/vendor\/magento\/framework\/Event\/Invoker\/InvokerDefault.php(74): Magento\\Framework\\Event\\Invoker\\InvokerDefault->_callObserverMethod(Object(Magento\\CatalogInventory\\Observer\\SaveInventoryDataObserver), Object(Magento\\Framework\\Event\\Observer))\n#8 \/var\/www\/html\/vendor\/magento\/framework\/Event\/Manager.php(65): Magento\\Framework\\Event\\Invoker\\InvokerDefault->dispatch(Array, Object(Magento\\Framework\\Event\\Observer))\n#9 \/var\/www\/html\/generated\/code\/Magento\/Framework\/Event\/Manager\/Proxy.php(95): Magento\\Framework\\Event\\Manager->dispatch('catalog_product...', Array)\n#10 \/var\/www\/html\/vendor\/magento\/framework\/Model\/AbstractModel.php(837): Magento\\Framework\\Event\\Manager\\Proxy->dispatch('catalog_product...', Array)\n#11 \/var\/www\/html\/vendor\/magento\/module-catalog\/Model\/Product.php(985): Magento\\Framework\\Model\\AbstractModel->afterSave()\n#12 \/var\/www\/html\/generated\/code\/Magento\/Catalog\/Model\/Product\/Interceptor.php(248): Magento\\Catalog\\Model\\Product->afterSave()\n#13 \/var\/www\/html\/vendor\/magento\/framework\/EntityManager\/Observer\/AfterEntitySave.php(34): Magento\\Catalog\\Model\\Product\\Interceptor->afterSave()\n#14 \/var\/www\/html\/vendor\/magento\/framework\/Event\/Invoker\/InvokerDefault.php(88): Magento\\Framework\\EntityManager\\Observer\\AfterEntitySave->execute(Object(Magento\\Framework\\Event\\Observer))\n#15 \/var\/www\/html\/vendor\/magento\/framework\/Event\/Invoker\/InvokerDefault.php(74): Magento\\Framework\\Event\\Invoker\\InvokerDefault->_callObserverMethod(Object(Magento\\Framework\\EntityManager\\Observer\\AfterEntitySave), Object(Magento\\Framework\\Event\\Observer))\n#16 \/var\/www\/html\/vendor\/magento\/framework\/Event\/Manager`,
            status: 'error',
            date: '2024-01-11 12:13:05',
            link: '/queue'
          }
        ]}
      />
    {/if}
  </div>
</Story>
<Story name="Is Loading">
  <div id="Page" class="">
    <button on:click={() => (openModal = true)}>open</button>
    {#if openModal}
      <AlertModal
        bind:open={openModal}
        isLoading={true}
        alerts={[
          {
            title: 'Alert title',
            message:
              'Alert message that could be a really really long message that wraps to the next line',
            status: 'info',
            date: '2024-01-11 12:13:05',
            link: '/queue'
          },
          {
            title: 'Some error',
            message:
              'Alert message that could be a really really long message that wraps to the next line',
            status: 'error',
            date: '2024-01-11 12:13:05',
            link: '/queue'
          },
          {
            title: 'A really long title that could break a line if it gets to the end of the line',
            message: 'Word wrap on title',
            status: 'warning',
            date: '2024-01-11 12:13:05',
            link: '/queue'
          },
          {
            title: 'Word Wrap on body',
            message:
              'If the description is longer than 3 lines, it should be truncated with a read more link that expands the description of the alert like an accordion. We should also add a character limit here to avoid it breaking the UI.',
            status: 'warning',
            date: '2024-01-11 12:13:05',
            link: '/queue'
          }
        ]}
      />
    {/if}
  </div>
</Story>

<Story name="No data">
  <div id="Page" class="">
    <button on:click={() => (openModal = true)}>open</button>
    {#if openModal}
      <AlertModal bind:open={openModal} isLoading={true} alerts={undefined} />
    {/if}
  </div>
</Story>
