<script context="module">
  import AlertRow from '@/components/Alerts/AlertRow.svelte';

  export const meta = {
    title: 'components/Alerts/AlertRow',
    component: AlertRow
  };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';
</script>

<Story name="Primary">
  <div id="Page" class="w-[460px]">
    <AlertRow
      isLoading={false}
      alert={{
        title: 'Alert title',
        message:
          'Alert message that could be a really really long message that wraps to the next line',
        status: 'info',
        date: '2024-01-11 12:13:05',
        link: '/queue'
      }}
    />
  </div>
</Story>

<Story name="Short message error">
  <div id="Page" class="w-[460px]">
    <AlertRow
      isLoading={false}
      alert={{
        title: 'Alert title',
        message: 'A message',
        status: 'error',
        date: '2024-01-11 12:13:05',
        link: '/queue'
      }}
    />
  </div>
</Story>

<Story name="Is Loading">
  <div id="Page" class="w-[460px]">
    <AlertRow
      isLoading={true}
      alert={{
        title: 'Alert title',
        message: 'A message',
        status: 'error',
        date: '2024-01-11 12:13:05',
        link: '/queue'
      }}
    />
  </div>
</Story>
