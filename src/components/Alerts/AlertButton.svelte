<script lang="ts">
  import IconNotification from '@/components/Icons/IconNotification.svelte';
  import Icon from '@/components/Icons/Icon.svelte';
  import type { StatusType } from '@/components/Controls/status';
  import { type Alert, alertsFromReportDashboard, statusFromAlerts } from '@/models/alerts/alert';
  import { getBackgroundColourClass } from '@/components/Controls/status';
  import AlertModal from '@/components/Alerts/AlertModal.svelte';
  import {
    createReportsBlockedQueueQuery,
    createReportsFailedQueueQuery
  } from '@/queries/reports.query';

  const blockedQueueQuery = createReportsBlockedQueueQuery();
  const failedQueueQuery = createReportsFailedQueueQuery();
  let status: StatusType = 'default';
  let showModal = false;
  $: alerts = [] as Alert[];
  $: isLoading = $blockedQueueQuery.isFetching || $failedQueueQuery.isFetching;

  // set alerts from various sources of data
  // currently we are just looking at blocked queue items
  $: if ($blockedQueueQuery.data && $failedQueueQuery.data) {
    alerts = [
      ...alertsFromReportDashboard($blockedQueueQuery.data),
      ...alertsFromReportDashboard($failedQueueQuery.data)
    ];
    status = statusFromAlerts(alerts);
  }
</script>

<button
  class="{status === 'error' && !showModal ? 'animate-bounce' : ''} relative bg-brand-brand"
  on:click={() => (showModal = true)}
>
  <Icon IconComponent={IconNotification} size="small" />
  {#if status !== 'default'}
    <div class="absolute right-0 top-0 h-3 w-3 rounded-full bg-brand-brand p-0.5">
      <div class="h-full w-full rounded-full {getBackgroundColourClass(status)}" />
    </div>
  {/if}
</button>

{#if showModal}
  <AlertModal bind:open={showModal} {alerts} {isLoading} />
{/if}
