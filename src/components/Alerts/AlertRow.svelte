<script lang="ts">
  import type { Alert } from '@/models/alerts/alert';
  import { getBackgroundColourClass } from '@/components/Controls/status';
  import AlertRowSkeleton from '@/components/Alerts/AlertRowSkeleton.svelte';
  import { getLocalShortDayTime } from '$lib/date-utils.js';
  import { createEventDispatcher } from 'svelte';

  const dispatch = createEventDispatcher();

  export let alert: Alert;
  export let isLoading: boolean;
</script>

{#if isLoading}
  <AlertRowSkeleton />
{:else}
  <a
    href={alert.link}
    on:click|preventDefault={() => dispatch('alertClick', alert)}
    class="flex gap-4 border-b border-neutral-200 py-4"
  >
    <div class="w-3 pt-1.5">
      <div class="h-3 w-3 rounded-full {getBackgroundColourClass(alert.status)}"></div>
    </div>
    <div class="w-full grow">
      <div class="w-full flex-col gap-1">
        <div class="flex justify-between gap-3">
          <div class="w-64 items-center truncate text-regular font-bold leading-6 text-neutral-700">
            {alert.title}
          </div>
          <p class="truncate text-smaller leading-6 text-neutral-500">
            {getLocalShortDayTime(alert.date)}
          </p>
        </div>
        <div class="line-clamp-3 break-all text-regular leading-4 text-neutral-500">
          {alert.message}
        </div>
      </div>
    </div>
  </a>
{/if}
