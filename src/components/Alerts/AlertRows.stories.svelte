<script context="module">
  import AlertRows from '@/components/Alerts/AlertRows.svelte';

  export const meta = {
    title: 'components/Alerts/AlertRows',
    component: AlertRows
  };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';
</script>

<Story name="Primary">
  <div id="Page">
    <AlertRows
      isLoading={false}
      alerts={[
        {
          title: 'Alert title',
          message:
            'Alert message that could be a really really long message that wraps to the next line',
          status: 'info',
          date: '2024-01-11 12:13:05',
          link: '/queue'
        },
        {
          title: 'Some error',
          message:
            'Alert message that could be a really really long message that wraps to the next line',
          status: 'error',
          date: '2024-01-11 12:13:05',
          link: '/queue'
        },
        {
          title: 'A really long title that could break a line if it gets to the end of the line',
          message: 'Word wrap on title',
          status: 'warning',
          date: '2024-01-11 12:13:05',
          link: '/queue'
        },
        {
          title: 'Word Wrap on body',
          message:
            'If the description is longer than 3 lines, it should be truncated with a read more link that expands the description of the alert like an accordion. We should also add a character limit here to avoid it breaking the UI.',
          status: 'warning',
          date: '2024-01-11 12:13:05',
          link: '/queue'
        }
      ]}
    />
  </div>
</Story>
