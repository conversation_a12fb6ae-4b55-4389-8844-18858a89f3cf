<script lang="ts">
  import Skeleton from '@/components/Loader/Skeleton.svelte';
</script>

<div class="flex gap-4 border-b border-neutral-200 py-4">
  <div class="w-3 pt-1.5">
    <div class="h-3 w-3 rounded-full">
      <Skeleton shimmer class="h-3 w-3" />
    </div>
  </div>
  <div class="flex-grow">
    <div class="flex-col gap-1">
      <div class="w-full">
        <div class="flex h-6 w-full gap-1">
          <div
            class="grow items-center overflow-hidden truncate text-regular font-bold leading-6 text-neutral-700"
          >
            <Skeleton shimmer class="h-4 w-full" />
          </div>
          <div class="w-16 items-center justify-end text-smaller leading-6 text-neutral-500">
            <Skeleton shimmer class="h-4 w-full" />
          </div>
        </div>
      </div>
      <div class="line-clamp-3 w-full text-regular leading-4 text-neutral-500">
        <Skeleton shimmer class="h-8 w-full" />
      </div>
    </div>
  </div>
</div>
