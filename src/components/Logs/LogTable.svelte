<script lang="ts">
  import { type Config, getChannelSourceDescription } from '@/models/config/config';
  import Table from '@/components/Controls/Table/Table.svelte';
  import type { TableProps, TableRow } from '@/components/Controls/Table/table';
  import type { LogHit } from '@/models/logs/hits';
  import LogTableCountCell from '@/components/Logs/LogTableCountCell.svelte';
  import LogTableStatusCell from '@/components/Logs/LogTableStatusCell.svelte';
  import LogTableMessageCell from '@/components/Logs/LogTableMessageCell.svelte';
  import LogTableTitleCell from '@/components/Logs/LogTableTitleCell.svelte';
  import type { QueueInstructions } from '@/models/queue/instructions';
  import LogModal from '@/components/Logs/LogModal.svelte';
  import TableCellShortDateTime from '@/components/Controls/Table/Cells/TableCellShortDateTime.svelte';

  export let config: Config | undefined;
  export let hits: LogHit[] | undefined;
  export let instructions: QueueInstructions | undefined;
  export let isLoading: boolean;
  export let includeHeader = true;

  let selectedHit: LogHit | undefined;
  let openModal = false;

  $: tableProps = <TableProps>{
    header: includeHeader,
    selectable: false,
    isLoading: isLoading,
    isLoadingSkeletonCount: 10,
    onRowClick: rowClickEventHandler,
    rowKey: ['channel_description', 'instruction', '_id', 'created'],
    rows:
      hits?.map((item) => {
        const row = item._source as unknown as TableRow;
        row._id = item._id;
        if (config) {
          row.connector = getChannelSourceDescription(
            config,
            (row.source_id as number) ?? 0,
            (row.channel_id as number) ?? 0
          );
        }
        return row;
      }) ?? [],
    rowPointer: true,
    rowHover: true,
    columns: [
      {
        columnKey: 'created',
        label: 'Date',
        align: 'left',
        component: TableCellShortDateTime
      },
      {
        columnKey: 'title',
        label: 'Title',
        align: 'left',
        component: LogTableTitleCell,
        componentProps: {
          config: config,
          instructions: instructions
        }
      },
      {
        columnKey: 'message',
        label: 'Message',
        align: 'left',
        component: LogTableMessageCell
      },
      {
        columnKey: 'count',
        label: 'Count',
        align: 'right',
        component: LogTableCountCell
      },
      {
        columnKey: 'level',
        label: 'Level',
        align: 'right',
        component: LogTableStatusCell
      }
    ]
  };

  const rowClickEventHandler = (row: TableRow) => {
    openModal = true;
    selectedHit = {
      _source: row
    } as unknown as LogHit;
  };
</script>

<!--
@component
Table for log items
-->

<Table {tableProps} />

{#if instructions && config && selectedHit && openModal}
  <LogModal {instructions} {config} hit={selectedHit} bind:open={openModal} />
{/if}
