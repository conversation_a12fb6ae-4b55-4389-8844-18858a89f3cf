<script lang="ts">
  import type { TableRow } from '@/components/Controls/Table/table';
  import { getEntityCount, type LogHit, isError } from '@/models/logs/hits';

  export let row: TableRow;

  const hit = { _source: row } as unknown as LogHit;
</script>

<div
  class="{isError(hit)
    ? 'bg-brand-action/10 text-brand-action'
    : 'bg-neutral-500/10 text-neutral-500'} flex h-5 w-fit items-center rounded p-1"
>
  {getEntityCount(hit)}
</div>
