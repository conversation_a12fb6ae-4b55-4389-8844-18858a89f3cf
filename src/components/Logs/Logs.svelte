<script lang="ts">
  import Facets from '@/components/ES/Facets.svelte';
  import type { Config } from '@/models/config/config';
  import type { FilterTerms } from '@/models/es/filter';
  import { getChannel, getSource } from '@/models/config/config';
  import Search from '@/components/Controls/Search.svelte';
  import LogTable from '@/components/Logs/LogTable.svelte';
  import type { QueueInstructions } from '@/models/queue/instructions';
  import type { Facet } from '@/models/es/facet';
  import type { ESData } from '@/lib/es/types';
  import type { LogHits } from '@/models/logs/hits';
  import SearchPageLayout from '@/components/Layout/SearchPageLayout.svelte';
  import TableSearchLayout from '@/components/Layout/TableSearchLayout.svelte';

  export let config: Config | undefined;
  export let data: ESData<LogHits> | undefined;
  export let instructions: QueueInstructions | undefined;
  export let isLoading: boolean;

  export let filterTerms: FilterTerms;
  export let search: string | null;

  $: aggsResponse = data?.aggregations;

  let facets: Facet[];
  $: facets = [
    {
      key: 'level',
      title: 'Log Level',
      type: 'buckets',
      buckets: aggsResponse?.level?.buckets
    },
    {
      key: 'entity',
      title: 'Entity',
      type: 'buckets',
      buckets: aggsResponse?.entity?.buckets
    },
    {
      key: 'direction',
      title: 'Direction',
      type: 'buckets',
      buckets: aggsResponse?.direction?.buckets
    },
    {
      key: 'source_id',
      title: 'Source',
      type: 'buckets',
      buckets: aggsResponse?.source_id?.buckets,
      getSelectedTitle: (key: string) => {
        if (!config) return key;
        return getSource(config, parseInt(key))?.description ?? key;
      }
    },
    {
      key: 'channel_id',
      title: 'Channel',
      type: 'buckets',
      buckets: aggsResponse?.channel_id?.buckets,
      getSelectedTitle: (key: string) => {
        if (!config) return key;
        return getChannel(config, parseInt(key))?.description ?? key;
      }
    },
    {
      key: 'tags',
      title: 'Tags',
      type: 'buckets',
      buckets: aggsResponse?.tags?.buckets
    },
    {
      key: 'origin',
      title: 'Origin',
      type: 'buckets',
      buckets: aggsResponse?.origin?.buckets
    }
  ];

  const onSearch = (s: string) => {
    search = s;
  };

  const clearSearch = () => {
    search = null;
  };
</script>

<!--
@component
Manages the layout of the logs page.
-->
<SearchPageLayout title="Logs">
  <svelte:fragment slot="sidebar">
    <Facets {facets} bind:filterTerms {isLoading} />
  </svelte:fragment>
  <svelte:fragment slot="content">
    <TableSearchLayout>
      <svelte:fragment slot="search">
        <Search {onSearch} {clearSearch} search={search ?? undefined} placeholder="Search" />
      </svelte:fragment>

      <svelte:fragment slot="table">
        <LogTable {isLoading} {config} hits={data?.hits?.hits} {instructions} />
      </svelte:fragment>
    </TableSearchLayout>
  </svelte:fragment>
</SearchPageLayout>
