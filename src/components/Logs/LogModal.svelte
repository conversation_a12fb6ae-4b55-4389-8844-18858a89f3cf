<script lang="ts">
  import ModalInternal from '@/components/Modal/ModalInternal.svelte';
  import { getEntityKey, getTitle, type LogHit } from '@/models/logs/hits';
  import type { QueueInstructions } from '@/models/queue/instructions';
  import type { Config } from '@/models/config/config';
  import Icon from '@/components/Icons/Icon.svelte';
  import { getEntityIcon } from '@/models/logs/entity';

  export let open: boolean;
  export let hit: LogHit;
  export let instructions: QueueInstructions;
  export let config: Config;

  const json = JSON.stringify(hit._source, null, 2);
  const title = getTitle(hit, instructions.system_queue_instructions, config);
  const icon = getEntityIcon(getEntityKey(hit));
</script>

<!--
@component
Loads a log in a modal component.
-->
<ModalInternal bind:open position="right" size="full">
  <div slot="header-prefix">
    <div class="flex items-center gap-4">
      <Icon IconComponent={icon} size="i-6" />
      {title}
    </div>
  </div>
  <div slot="body" class="rounded bg-neutral-100 text-neutral-500">
    <pre class="whitespace-pre-wrap p-4">{json}</pre>
  </div>
</ModalInternal>
