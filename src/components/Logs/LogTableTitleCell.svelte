<script lang="ts">
  import type { TableRow } from '@/components/Controls/Table/table';
  import { type LogHit } from '@/models/logs/hits';
  import { type Config } from '@/models/config/config';
  import { type QueueInstructions } from '@/models/queue/instructions';
  import LogTitle from '@/components/Logs/LogTitle.svelte';

  export let row: TableRow;
  export let config: Config;
  export let instructions: QueueInstructions;

  const hit = { _source: row } as unknown as LogHit;
</script>

<!--
@component
creates a title cell with an icon based on instruction and entity type
-->
<LogTitle {hit} {instructions} {config} />
