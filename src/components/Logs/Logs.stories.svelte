<script context="module">
  import Logs from '@/components/Logs/Logs.svelte';

  export const meta = {
    title: 'components/Logs/Logs',
    component: Logs
  };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';
  import type { FilterTerms } from '@/models/es/filter';
  import type { Config } from '@/models/config/config';
  import type { QueueInstructions } from '@/models/queue/instructions';
  import type { ESData } from '@/lib/es/types';
  import type { LogHits } from '@/models/logs/hits';

  let search: string | null = '';

  let instructions: QueueInstructions = {
    system_queue_instructions: [
      {
        description: 'some description',
        mode: 'blocking',
        instruction: 'add_order'
      }
    ]
  };

  let config: Config = {
    client_id: 1,
    meta: {},
    channels: {
      1: {
        description: 'Trade Store',
        flags: [],
        id: 1,
        sources: [],
        rules: [],
        client_id: 1,
        active: true,
        type: 'trade',
        meta: []
      }
    },
    sources: {
      1: {
        description: 'Sage One',
        flags: [],
        id: 1,
        client_id: 1,
        active: true,
        type: 'sage_one',
        meta: [],
        sync_token: '0'
      }
    }
  };

  const data: ESData<LogHits> = {
    took: 57,
    aggregations: {
      level: {
        buckets: [
          {
            key: 'tag1',
            doc_count: 1
          },
          {
            key: 'tag2',
            doc_count: 1
          }
        ]
      },
      channel_id: {
        buckets: [
          {
            key: '1',
            doc_count: 10
          }
        ]
      },
      source_id: {
        buckets: [
          {
            key: '1',
            doc_count: 1
          }
        ]
      },
      direction: {
        buckets: [
          {
            key: 'red',
            doc_count: 1
          },
          {
            key: 'blue',
            doc_count: 1
          }
        ]
      },
      entity: {
        buckets: [
          {
            key: 'red',
            doc_count: 1
          },
          {
            key: 'blue',
            doc_count: 1
          }
        ]
      },
      tags: {
        buckets: []
      },
      origin: {
        buckets: []
      }
    },
    hits: {
      total: 1,
      max_score: 1,
      hits: [
        {
          _id: 'MSN7x44BQMR73LOw34gC1',
          _source: {
            message: 'my message',
            created: '2021-01-01 00:00:00',
            level: 'info'
          }
        },
        {
          _id: 'MSN7x44BQMR73LOw34gC2',
          _source: {
            message: 'a error',
            created: '2021-01-01 00:00:00',
            level: 'error'
          }
        },
        {
          _id: 'MSN7x44BQMR73LOw34gC3',
          _source: {
            message: 'a error',
            created: '2021-01-01 00:00:00',
            level: 'error'
          }
        },
        {
          _id: 'MSN7x44BQMR73LOw34gC4',
          _source: {
            message: 'a error',
            created: '2021-01-01 00:00:00',
            level: 'error'
          }
        },
        {
          _id: 'MSN7x44BQMR73LOw34gC5',
          _source: {
            message: 'a error',
            created: '2021-01-01 00:00:00',
            level: 'error'
          }
        },
        {
          _id: 'MSN7x44BQMR73LOw34gC6',
          _source: {
            message: 'a error',
            created: '2021-01-01 00:00:00',
            level: 'error'
          }
        },
        {
          _id: 'MSN7x44BQMR73LOw34gC7',
          _source: {
            message: 'a error',
            created: '2021-01-01 00:00:00',
            level: 'error'
          }
        },
        {
          _id: 'MSN7x44BQMR73LOw34gC8',
          _source: {
            message: 'a error',
            created: '2021-01-01 00:00:00',
            level: 'error'
          }
        },
        {
          _id: 'MSN7x44BQMR73LOw34gC9',
          _source: {
            message: 'a error',
            created: '2021-01-01 00:00:00',
            level: 'error'
          }
        },
        {
          _id: 'MSN7x44BQMR73LOw34gC10',
          _source: {
            message: 'a error',
            created: '2021-01-01 00:00:00',
            level: 'error'
          }
        }
      ]
    }
  };

  let filterTerms: FilterTerms = {
    size: '10',
    from: '0',
    search: '',
    level: '',
    channel_id: '',
    source_id: '',
    direction: '',
    entity: '',
    tags: '',
    origin: ''
  };
</script>

<Story name="Primary">
  <div id="Page" class="">
    <Logs {instructions} isLoading={false} {data} {config} bind:search bind:filterTerms />
  </div>
</Story>

<Story name="Is Loading, no data">
  <div id="Page" class="">
    <Logs {instructions} isLoading={true} data={undefined} bind:search {config} bind:filterTerms />
  </div>
</Story>

<Story name="Is Loading, existing data">
  <div id="Page" class="">
    <Logs {instructions} isLoading={true} {data} bind:search {config} bind:filterTerms />
  </div>
</Story>
