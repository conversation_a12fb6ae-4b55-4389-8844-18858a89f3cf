<script lang="ts">
  import type { TableRow } from '@/components/Controls/Table/table';
  import type { LogHit } from '@/models/logs/hits';
  import Text from '@/components/Controls/Text/Text.svelte';

  export let row: TableRow;
  const hit = { _source: row } as unknown as LogHit;
</script>

{#if hit._source.message}
  <Text tooltip={hit._source.message} class="line-clamp-1">
    {hit._source.message}
  </Text>
{/if}
