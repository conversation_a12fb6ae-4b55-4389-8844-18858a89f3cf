<script context="module">
  import LogModal from '@/components/Logs/LogModal.svelte';

  export const meta = {
    title: 'components/Logs/LogModal',
    component: LogModal
  };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';
  import type { QueueInstructions } from '@/models/queue/instructions';
  import type { Config } from '@/models/config/config';
  import type { LogHit } from '@/models/logs/hits';

  let instructions: QueueInstructions = {
    system_queue_instructions: [
      {
        description: 'some description',
        mode: 'blocking',
        instruction: 'add_order'
      }
    ]
  };

  let config: Config = {
    client_id: 1,
    meta: {},
    channels: {
      1: {
        description: 'Trade Store',
        flags: [],
        id: 1,
        sources: [],
        rules: [],
        client_id: 1,
        active: true,
        type: 'trade',
        meta: []
      }
    },
    sources: {
      1: {
        description: 'Sage One',
        flags: [],
        id: 1,
        client_id: 1,
        active: true,
        type: 'sage_one',
        meta: [],
        sync_token: '0'
      }
    }
  };
  let hit: LogHit = {
    _id: 'MSN7x44BQMR73LOw34gC',
    _source: {
      message: 'my message',
      created: '2021-01-01 00:00:00',
      level: 'info'
    }
  };
  let openModal = true;
</script>

<Story name="LogModal" id="log_modal">
  <div id="Page" class="">
    <button on:click={() => (openModal = true)}>open</button>
    {#if openModal}
      <LogModal {instructions} {config} {hit} bind:open={openModal} />
    {/if}
  </div>
</Story>
