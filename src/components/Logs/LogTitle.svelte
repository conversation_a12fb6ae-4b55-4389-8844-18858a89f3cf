<script lang="ts">
  import Icon from '@/components/Icons/Icon.svelte';
  import { getEntityKey, getTitle, type LogHit } from '@/models/logs/hits';
  import { getEntityIcon } from '@/models/logs/entity';
  import type { QueueInstructions } from '@/models/queue/instructions';
  import type { Config } from '@/models/config/config';

  export let hit: LogHit;
  export let instructions: QueueInstructions;
  export let config: Config;

  const title = getTitle(hit, instructions?.system_queue_instructions || [], config);
  const icon = getEntityIcon(getEntityKey(hit));
</script>

<div class="flex w-full items-center gap-1">
  <div class="w-min">
    <Icon IconComponent={icon} size="i-4" />
  </div>
  <div class="w-max flex-1 font-bold leading-none">
    {title}
  </div>
</div>
