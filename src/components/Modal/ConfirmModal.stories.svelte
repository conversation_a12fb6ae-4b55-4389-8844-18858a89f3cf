<script context="module">
  import ConfirmModalInternal from '@/components/Modal/ConfirmModalInternal.svelte';
  import { confirmModal, confirmOverrideModal } from '@/components/Modal/modal';

  export const meta = {
    title: 'components/Modal/ConfirmModal',
    component: ConfirmModalInternal
  };
</script>

<script lang="ts">
  import Btn from '@/components/form/Btn.svelte';
  import { Story } from '@storybook/addon-svelte-csf';
  import { toast } from 'svelte-sonner';
  let res: boolean | undefined;
</script>

<Story name="Default">
  <Btn
    onClick={async () => {
      res = await confirmModal();
      if (res) {
        // Do action here
      }
    }}>Do action</Btn
  >
  Res = {res}
</Story>

<Story name="Confirm Override Modal">
  Wrapper for overrides that rejects promise on cancel
  <Btn
    onClick={() =>
      toast.promise(
        async () => {
          res = await confirmOverrideModal('product');
          if (res) {
            // Do action here
          }
        },
        { loading: 'loading...', error: (e) => e?.message, success: 'Updated' }
      )}>Save</Btn
  >
  Res = {res}
</Story>
