<script lang="ts">
  import IconClose from '@/components/Icons/IconClose.svelte';
  import { fade, fly } from 'svelte/transition';
  import { cn } from '@/utils/classname';
  import type { ModalPosition, ModalSize } from './modal';
  import { createDialog, melt } from '@melt-ui/svelte';
  import Button from '@/components/Controls/Button.svelte';
  import SkeletonText from '@/components/Loader/SkeletonText.svelte';
  import Icon from '@/components/Icons/Icon.svelte';
  import type { ComponentType } from 'svelte';

  export let title: string | undefined = undefined;
  export let titleLoading = false;
  export let formSubmit: (() => void) | ((parm: any) => void) = (e) => {};
  export let icon: ComponentType | undefined = undefined;
  /**
   * Used to bind only, render with conditional in highest parent
   */
  export let open: boolean = true;
  export let size: ModalSize = 'narrow';
  export let position: ModalPosition = 'right';
  export let topPosition = 'top-0';
  export let noXPadding = false;
  export let noYPadding = false;
  /**
   * This should be set for modals with text inputs to lower user frustration.
   */
  export let disableClickOutside: boolean = false;
  /**
   * This should be set for modals where open is unbound (confirm modal)
   */
  export let disableCloseOnEscape: boolean = false;

  export let hideBottomBorder = false;

  let className = '';
  export { className as class };

  export let hideModal: () => void = () => {
    open = false;
  };

  // position defaults to "center" if size is set to "full"
  position = size === 'full' ? 'center' : position;

  const sizeClassMap = {
    narrow: `w-[460px] ${position === 'center' ? 'rounded-lg' : ' h-full'}`,
    half: `w-1/2 ${position === 'center' ? 'rounded-lg' : ' h-full'}`,
    full: 'w-full rounded-lg h-full'
  };

  const positionMap = {
    right: 'justify-end',
    left: 'justify-start',
    center: 'justify-center items-center p-8'
  };

  const {
    // We're not using trigger/close here since we can't set use:melt={$trigger} on <Btn>,
    elements: { portalled, overlay, content, title: titleInternal },
    states: { open: openInternal } // Open conditional is done in highest parent
  } = createDialog({
    onOpenChange: ({ next }) => {
      // Set externally binded `open` state to next value
      open = next;
      return next;
    },
    // To ensure portals get rendered within a
    // story's root div instead of the default body
    portal: window?.IS_STORYBOOK ? '#storybook-root' : 'body',
    closeOnOutsideClick: !disableClickOutside,
    closeOnEscape: !disableCloseOnEscape,
    defaultOpen: open
  });
</script>

<!--
  @component Use conditional render in parent to ensure all encompassing code only renders when needed
-->

<div
  use:melt={$portalled}
  class={cn('fixed inset-0 z-50 flex', positionMap[position], topPosition)}
>
  <div
    use:melt={$overlay}
    transition:fade|global={{ duration: 100 }}
    class={cn('fixed inset-0 z-0 bg-neutral-800/30', topPosition)}
  />
  <form
    on:submit|preventDefault={formSubmit}
    transition:fly|global={{
      x: position === 'right' ? 100 : position === 'left' ? -100 : undefined,
      y: position === 'center' ? -100 : undefined,
      duration: 200
    }}
    class={cn('group z-10 flex flex-col bg-white', sizeClassMap[size], className)}
    use:melt={$content}
  >
    <!-- header -->
    <div
      class="flex items-center justify-between gap-2 px-8 py-4 {$$slots.body
        ? 'border-b border-neutral-200'
        : ''}"
    >
      <div class="flex w-full items-center gap-4">
        {#if icon}
          <Icon IconComponent={icon} size="i-6" />
        {/if}
        <slot name="header-prefix" />
        {#if titleLoading}
          <SkeletonText
            bgsToIgnore={['bg-gray-400', 'bg-gray-500', 'bg-gray-600']}
            shimmer
            class="h-7"
          />
        {:else if title}
          <h3 use:melt={$titleInternal} class="text-neutral-800">
            {title}
          </h3>
        {/if}
      </div>
      <div class="shrink-0">
        {#if $$slots['header-suffix']}
          <slot name="header-suffix" />
        {:else}
          <Button size="medium" variant="gray" icon={IconClose} on:click={hideModal} />
        {/if}
      </div>
    </div>

    <!-- body -->
    {#if $$slots.body}
      <div
        class="relative grow overflow-y-auto"
        class:py-8={noXPadding && !noYPadding}
        class:px-8={noYPadding && !noXPadding}
        class:p-8={!noXPadding && !noYPadding}
      >
        <slot name="body" />
      </div>
    {/if}

    <!-- footer -->
    {#if $$slots.footer}
      <div class="px-8 {hideBottomBorder ? 'pb-4' : 'border-t border-neutral-200 py-4'}">
        <slot name="footer" />
      </div>
    {/if}
  </form>
</div>
