<script lang="ts">
  import { fade, fly } from 'svelte/transition';
  import Loader from '../Loader/Loader.svelte';
  import handleClickOutside from '@/utils/actions/handleClickOutside';
  import { cn } from '@/utils/classname';

  /**
   * Used only to bind
   */
  export let open: boolean;
  export let isLoading: boolean = false;
  export let width = 'w-5/6';
  export let height = 'h-5/6';
  export let enableClickOutside: boolean = true;
  let className = '';
  export { className as class };
  export let onClose: (() => void) | undefined = undefined;
  const handleClose = () => {
    open = false;
    if (onClose) {
      onClose();
    }
  };
</script>

<!-- 
  @component
  ToDo: refactor this into our main Modal component (Modal.svelte)
  Conditional rendering should be down in the highest parent to ensure everything gets unmounted
  
 -->

{#if open}
  <div class="fixed left-0 top-0 z-50 flex h-screen w-full items-center justify-center">
    <div
      class={cn(
        'relative z-10 overflow-hidden overflow-y-auto rounded-md bg-white',
        width,
        height,
        className
      )}
      transition:fly|global={{
        y: -100,
        duration: 200
      }}
      use:handleClickOutside={enableClickOutside ? handleClose : () => {}}
    >
      {#if isLoading}
        <Loader />
      {:else}
        <div
          class="sticky top-0 z-10 flex items-center border-b border-neutral-200 bg-white px-6 py-4"
        >
          <slot name="title" />
        </div>
        <div class="h-full w-full">
          <div>
            <slot name="notification" />
          </div>
          <div class="h-full overflow-y-auto">
            <slot name="content" />
          </div>
          {#if $$slots.btn}
            <div
              class="sticky bottom-0 z-10 flex items-center border-t border-neutral-200 bg-white px-6 py-4"
            >
              <slot name="btn" />
            </div>
          {/if}
        </div>
      {/if}
    </div>
    <div
      transition:fade={{ duration: 100 }}
      class="fixed z-0 h-full w-full bg-neutral-transparent800"
    />
  </div>
{/if}
