<script lang="ts">
  import Button from '@/components/Controls/Button.svelte';
  import type { ModalPosition, ModalSize } from '@/components/Modal/modal';
  import ModalInternal from '@/components/Modal/ModalInternal.svelte';
  import InlineNotification from '@/components/Notification/InlineNotification.svelte';
  import { type ComponentType } from 'svelte';
  export let title: string;
  export let open = false;
  export let actionClick: (() => void) | ((parm: any) => void) = () => {};
  export let cancelClick = () => {
    open = false;
  };
  /**
   * For when the modal is used for data display
   * not for adding or editing anything
   *
   */
  export let noAction = false;
  export let actionText = 'Add';
  export let position: ModalPosition = 'right';
  export let size: ModalSize = 'narrow';
  export let hideBottomBorder = false;
  export let icon: ComponentType | undefined = undefined;
  export let disableCloseOnEscape = false;
  export let validationError: string | false = false;
  /**
   * Used to render skeleton for title and override button disabled
   */
  export let loading = false;
  /**
   * Title doesnt get affected by loading state
   */
  export let dontLoadTitle = false;

  /**
   * Whether the user can click the add/edit button
   */
  export let disabled = false;
</script>

<!-- @component
  A simple wrapper around Modal for add/edit modals (with the add and close buttons added to the bottom)
  Any input content lives in the default slot
  For inputs, use this with the `Controls/Input/Group/*` components
  ToDo - rename this to `Modal` since it is the main modal component now
       - ModalInternal is only used where we need deep level customisation
   -->
<ModalInternal
  bind:open
  {title}
  disableClickOutside
  {disableCloseOnEscape}
  {position}
  {size}
  hideModal={cancelClick}
  hideBottomBorder={hideBottomBorder || noAction}
  formSubmit={actionClick}
  titleLoading={loading && !dontLoadTitle}
  {icon}
>
  {#if $$slots['header-prefix']}
    <slot name="header-prefix" />
  {/if}

  <div slot="body">
    <slot />
  </div>

  <div slot="footer">
    {#if !noAction}
      <div class="flex gap-4">
        <Button size="large" variant="gray" on:click={cancelClick} fullWidth>Cancel</Button>
        <Button
          focusOnMount
          size="large"
          type="submit"
          disabled={loading || disabled}
          variant="solid"
          fullWidth>{actionText}</Button
        >
      </div>
    {/if}
    {#if validationError}
      <div class="mt-2">
        <InlineNotification type="warning">
          <p>{validationError}</p>
        </InlineNotification>
      </div>
    {/if}
  </div>
</ModalInternal>
