<script lang="ts">
  import PromptModal from '@/components/Modal/PromptModal.svelte';
  import { createEventDispatcher } from 'svelte';

  export let title = 'Please confirm';
  export let description = 'Are you sure you want to proceed with this action?';
  export let actionText = 'Confirm';
  const handleCancel = () => dispatch('result', false);
  const handleConfirm = () => dispatch('result', true);

  const dispatch = createEventDispatcher();
</script>

<!-- @component
Use this component by calling the confirmModal async function
 -->

<PromptModal
  {title}
  open
  {actionText}
  position="center"
  actionClick={handleConfirm}
  cancelClick={handleCancel}
  hideBottomBorder
  disableCloseOnEscape
>
  <p class="text-center text-regular text-neutral-700">{description}</p>
</PromptModal>
