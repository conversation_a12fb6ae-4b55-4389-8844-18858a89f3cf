<script context="module">
  import Button from '@/components/Controls/Button.svelte';
  import PromptModal from '@/components/Modal/PromptModal.svelte';

  export const meta = {
    title: 'components/Modal/PromptModal',
    component: PromptModal
  };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';
  let open = true;
</script>

<Story name="Right">
  <Button
    on:click={() => {
      open = true;
    }}>Open</Button
  >
  {#if open}
    <PromptModal
      bind:open
      title="Add/Update your data"
      actionClick={() => {
        alert('Saved!');
        open = false;
      }}
      actionText="Save"
    >
      Inputs go here</PromptModal
    >
  {/if}
</Story>

<Story name="Right - half">
  <Button
    on:click={() => {
      open = true;
    }}>Open</Button
  >
  {#if open}
    <PromptModal
      size="half"
      bind:open
      title="Add/Update your data"
      actionClick={() => {
        alert('Saved!');
        open = false;
      }}
      actionText="Save"
    >
      Inputs go here</PromptModal
    >
  {/if}
</Story>

<Story name="Center">
  <Button
    on:click={() => {
      open = true;
    }}>Open</Button
  >
  {#if open}
    <PromptModal
      bind:open
      position="center"
      title="Add/Update your data"
      actionClick={() => {
        alert('Saved!');
        open = false;
      }}
      actionText="Save"
    >
      Inputs go here</PromptModal
    >
  {/if}
</Story>

<Story name="Center - half">
  <Button
    on:click={() => {
      open = true;
    }}>Open</Button
  >
  {#if open}
    <PromptModal
      bind:open
      position="center"
      size="half"
      title="Add/Update your data"
      actionClick={() => {
        alert('Saved!');
        open = false;
      }}
      actionText="Save"
    >
      Inputs go here</PromptModal
    >
  {/if}
</Story>

<Story name="Full">
  <Button
    on:click={() => {
      open = true;
    }}>Open</Button
  >
  Note that when size is set to `full` the position won't make a difference

  {#if open}
    <PromptModal
      size="full"
      bind:open
      title="Add/Update your data"
      actionClick={() => {
        alert('Saved!');
        open = false;
      }}
      actionText="Save"
    >
      Inputs go here</PromptModal
    >
  {/if}
</Story>

<Story name="Full - unused position prop">
  <Button
    on:click={() => {
      open = true;
    }}>Open</Button
  >
  Note that when size is set to `full` the position won't make a difference

  {#if open}
    <PromptModal
      size="full"
      position="right"
      bind:open
      title="Add/Update your data"
      actionClick={() => {
        alert('Saved!');
        open = false;
      }}
      actionText="Save"
    >
      Inputs go here</PromptModal
    >
  {/if}
</Story>
