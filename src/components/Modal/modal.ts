import ConfirmModalInternal from '@/components/Modal/ConfirmModalInternal.svelte';
import outroAndDestroy from '@/utils/outroAndDestroy';

export type ModalSize = 'narrow' | 'half' | 'full';
export type ModalPosition = 'right' | 'left' | 'center';

/**
 * Async function that renders a confirm dialog
 * and resolves with their response as a boolean
 */
export const confirmModal = (
  props?: {
    title?: string;
    description?: string;
    actionText?: string;
  },
  options: { canReject?: boolean; rejectMessage?: string } = {
    canReject: false
  }
): Promise<boolean> => {
  return new Promise((resolve, reject) => {
    const modal = new ConfirmModalInternal({
      target: document.body,
      props,
      intro: true
    });
    modal.$on('result', (e) => {
      const res = e.detail as boolean;
      if (options?.canReject && !res) {
        reject(new Error(options.rejectMessage ?? 'Cancelled'));
      }
      resolve(res);
      outroAndDestroy(modal);
    });
  });
};

/**
 * Wrapper of confirmModal for overriding server changes
 */
export const confirmOverrideModal = (entity: string) =>
  confirmModal(
    {
      title: `Override changes`,
      description: `The ${entity} data has changed on the server, by saving you will override these changes. Are you sure you want to proceed?`
    },
    { canReject: true, rejectMessage: `Cancelled updating ${entity}` }
  );
