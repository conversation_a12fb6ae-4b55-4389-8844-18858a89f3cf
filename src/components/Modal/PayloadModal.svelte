<script lang="ts">
  import PromptModal from '@/components/Modal/PromptModal.svelte';
  import InputWrapper from '@/components/Controls/Group/InputWrapper.svelte';
  import ModalInputGroup from '@/components/Controls/Group/ModalInputGroup.svelte';
  import Skeleton from '@/components/Loader/Skeleton.svelte';
  import type { ComponentType } from 'svelte';
  import ListItem from '@/components/Modal/Items/ListItem.svelte';

  export let open = false;
  export let icon: ComponentType | undefined = undefined;
  export let loading = true;
  export let title = '';
  export let metadata: Record<string, string> = {};
  export let payload: unknown | undefined = undefined;

  $: formatted = JSON.stringify(payload, null, 2);
</script>

<!-- @component
 Display metadata and JSON payload in a modal
 -->

<PromptModal {icon} {title} size="half" bind:open noAction>
  <InputWrapper>
    {#if loading}
      {#if Object.keys(metadata).length}
        {#each Object.keys(metadata) as _}
          <Skeleton class="h-16 w-full" shimmer />
        {/each}
      {:else}
        <Skeleton class="h-16 w-full" shimmer />
        <Skeleton class="h-16 w-full" shimmer />
        <Skeleton class="h-16 w-full" shimmer />
      {/if}
      <Skeleton class="h-96 w-full" shimmer />
    {:else}
      <ModalInputGroup hideSeparator>
        {#each Object.entries(metadata) as [title, description]}
          <ListItem {title} {description} />
        {/each}
      </ModalInputGroup>
      {#if payload}
        <ModalInputGroup>
          <div class="mt-4 w-full overflow-x-auto rounded bg-neutral-100 p-4 text-neutral-500">
            <pre class="font-mono text-regular">{formatted}</pre>
          </div>
        </ModalInputGroup>
      {/if}
    {/if}
  </InputWrapper>
</PromptModal>
