<script lang="ts">
  import Btn from '@/components/form/Btn.svelte';
  import type { NumberRange } from '@/lib/es/types';
  import Select from '../Controls/Select/Select.svelte';

  export let selectedItem: string = null;
  export let items: Array<{ value: string; label: string }> = [];

  export let from: number;
  export let to: number;

  export let onSubmit: (data: NumberRange & { item: string }) => void;

  const handleSubmit = (e: Event) => {
    e.preventDefault();
    if (typeof from === 'number' && typeof to === 'number' && from > to) {
      let temp = from;
      from = to;
      to = temp;
    }
    onSubmit({
      from: typeof from === 'number' ? from : undefined,
      to: typeof to === 'number' ? to : undefined,
      item: selectedItem
    });
  };

  let showSelectArrow = true;
</script>

<div>
  <form on:submit|preventDefault={handleSubmit}>
    {#if items.length > 0}
      <div class="mb-2 pl-1 pr-1.5">
        <Select size="medium" bind:value={selectedItem} options={items} />
      </div>
    {/if}
    <div class="flex items-center justify-between pl-1 pr-1.5">
      <input
        class="block h-8 w-full rounded border border-neutral-200 px-3
        py-1 text-regular text-neutral-700 placeholder-neutral-400
        focus:border-neutral-200 focus:shadow-none focus:ring-0"
        type="number"
        placeholder="From"
        name="from"
        bind:value={from}
        {...$$restProps}
      />
      <input
        class="ml-2 block h-8 w-full rounded border border-neutral-200 px-3
        py-1 text-regular text-neutral-700 placeholder-neutral-400
        focus:border-neutral-200 focus:shadow-none focus:ring-0"
        type="number"
        placeholder="To"
        name="to"
        bind:value={to}
        {...$$restProps}
      />
      <Btn size="medium" variant="gray-outline" class="ml-2 px-[0.5rem]" type="submit">Apply</Btn>
    </div>
  </form>
</div>
