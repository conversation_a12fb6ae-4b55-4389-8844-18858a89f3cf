<script context="module">
  import FilterDateTime from '@/components/Filter/FilterDateTime.svelte';

  export const meta = {
    title: 'components/Filter/FilterDateTime',
    component: FilterDateTime
  };
</script>

<script lang="ts">
  import { Template, Story } from '@storybook/addon-svelte-csf';
  import { expect } from '@storybook/test';
  import { userEvent, within } from '@storybook/test';
  import FilterWrapper from '@/components/Filter/FilterWrapper.svelte';

  async function openFromDatePicker({ canvasElement }) {
    const canvas = within(canvasElement);
    const fromInputs = await canvas.findAllByPlaceholderText('From');
    const textInputs = fromInputs.filter((i) => i.type === 'text');
    expect(textInputs.length).toBe(1);
    userEvent.click(textInputs[0]);
  }
</script>

<Template let:args>
  <div class="mr-6 h-full min-w-[260px] max-w-[260px] overflow-y-auto rounded bg-white shadow">
    <FilterWrapper label="Date">
      <FilterDateTime {...args} />
    </FilterWrapper>
  </div>
</Template>

<Story name="Empty" />

<!--
  This one doesn't work at the moment because it seems that the daterange has internal "Date" variables that need to be kept in sync
  with the string 'from' and 'to' props - but they aren't initialised from the props
-->
<Story
  name="Filled [Broken]"
  args={{
    from: '2023-02-17 14:00:00',
    to: '2023-03-01 11:45:00'
  }}
/>

<Story name="From Calendar Open" play={openFromDatePicker} />
