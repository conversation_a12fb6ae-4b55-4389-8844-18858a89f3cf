<script lang="ts">
  import IconSearch from '@/components/Icons/IconSearch.svelte';
  import FilterOption from '@/components/Filter/FilterOption.svelte';

  const exampleOptions = new Array(20);
  let optionSelected: { index: number; selected: boolean } = {
    selected: false,
    index: null
  };

  const selectOption = (index: number) => {
    if (optionSelected.selected) {
      optionSelected.selected = false;
    } else {
      optionSelected = {
        selected: true,
        index
      };
    }
  };
</script>

<div>
  <div class="relative pl-1 pr-1.5">
    <input
      type="text"
      class="h-8 w-full rounded border border-neutral-200 py-1 pl-8
        pr-4 text-regular text-neutral-700 focus:border-neutral-200
        focus:shadow-none focus:ring-0"
    />
    <div class="search-icon absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2">
      <IconSearch />
    </div>
  </div>
  <div class="mt-2 max-h-36 overflow-y-auto">
    {#each exampleOptions as option, idx}
      <FilterOption docCount={Math.floor(Math.random() * 10)} onClick={selectOption} value={idx}>
        Option {idx + 1}
      </FilterOption>
    {/each}
  </div>
</div>
