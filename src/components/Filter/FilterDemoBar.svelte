<script lang="ts">
  import FilterBarHeader from '@/components/Filter/FilterBarHeader.svelte';
  import FilterSingleSelect from '@/components/Filter/FilterDemoSingleSelect.svelte';
  import FilterWrapper from '@/components/Filter/FilterWrapper.svelte';
  import FilterRange from '@/components/Filter/FilterRange.svelte';
  import FilterDateTime from '@/components/Filter/FilterDateTime.svelte';
  import Btn from '@/components/form/Btn.svelte';

  let collapseAll = true;

  // example data
  let filters = {
    single: true,
    range: true,
    date: true
  };

  const toggleSingle = () => {
    filters = { ...filters, single: !filters.single };
  };

  const toggleRange = () => {
    filters = { ...filters, range: !filters.range };
  };

  const toggleDate = () => {
    filters = { ...filters, date: !filters.date };
  };

  $: collapseAll = Object.values(filters).reduce((a, b) => a && b, true);

  const toggleAll = () => {
    Object.keys(filters).forEach((key) => {
      filters[key] = !collapseAll;
    });
    filters = { ...filters };
  };
</script>

<div class="relative">
  <div class="sticky top-0 z-10 bg-white">
    <FilterBarHeader collapsed={collapseAll} onCollapseAll={toggleAll}>
      <Btn size="small" variant="outline" slot="add" class="mr-1 flex-1">Button 1</Btn>
      <Btn slot="reset" variant="gray-outline" class="ml-1 flex-1" size="small">Button 2</Btn>
    </FilterBarHeader>
  </div>
  <FilterWrapper label="Single Select" collapsed={filters.single} onClick={toggleSingle}>
    <FilterSingleSelect />
  </FilterWrapper>
  <FilterWrapper label="Range" collapsed={filters.range} onClick={toggleRange}>
    <FilterRange
      items={[
        { value: 'item1', label: 'item1' },
        { value: 'item2', label: 'item2' },
        { value: 'item3', label: 'item3' },
        { value: 'item4', label: 'item4' }
      ]}
    />
  </FilterWrapper>
  <FilterWrapper label="DateTime" collapsed={filters.date} onClick={toggleDate}>
    <FilterDateTime />
  </FilterWrapper>
</div>
