<script lang="ts">
  import Icon from '@/components/Icons/Icon.svelte';
  import IconExpandArrow from '@/components/Icons/IconExpandArrow.svelte';

  export let label: string;
  export let collapsed: boolean;
  export let onClick: () => void;
  $: formattedLabel = label.replaceAll('_', ' ');
</script>

<div class="select-none border-t border-neutral-200 bg-white p-3">
  <button on:click={onClick} class="w-full">
    <div
      class="flex cursor-pointer items-center justify-between px-1 py-0.5 text-smaller font-bold uppercase leading-4 tracking-widest text-neutral-700"
    >
      {formattedLabel}
      <div class="p-1" class:rotate-180={!collapsed}>
        <Icon IconComponent={IconExpandArrow} size="i-3" />
      </div>
    </div>
  </button>
  {#if !collapsed}
    <div class="py-2">
      <slot />
    </div>
  {/if}
</div>
