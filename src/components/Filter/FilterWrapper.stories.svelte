<script context="module">
  import FilterWrapper from '@/components/Filter/FilterWrapper.svelte';

  export const meta = {
    title: 'components/Filter/FilterWrapper',
    component: FilterWrapper
  };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';
</script>

<Story name="Collapsed" let:args>
  <div class="mr-6 h-full min-w-[260px] max-w-[260px] overflow-y-auto rounded bg-white shadow">
    <FilterWrapper label="Item 1" collapsed={true} {...args}>
      <div>SHOULD BE HIDDEN</div>
    </FilterWrapper>
  </div>
</Story>

<Story name="Expanded" let:args>
  <div class="mr-6 h-full min-w-[260px] max-w-[260px] overflow-y-auto rounded bg-white shadow">
    <FilterWrapper label="Item 1" collapsed={false} {...args}>
      <div>SHOWN</div>
    </FilterWrapper>
  </div>
</Story>

<Story name="Three Filters Expanded" let:args>
  <div class="mr-6 h-full min-w-[260px] max-w-[260px] overflow-y-auto rounded bg-white shadow">
    <FilterWrapper label="Item 1" collapsed={false} {...args}>
      <div>SHOWN</div>
    </FilterWrapper>
    <FilterWrapper label="Item 2" collapsed={false} {...args}>
      <div>SHOWN</div>
    </FilterWrapper>
    <FilterWrapper label="Item 3" collapsed={false} {...args}>
      <div>SHOWN</div>
    </FilterWrapper>
  </div>
</Story>

<Story name="Three Filters Collapsed" let:args>
  <div class="mr-6 h-full min-w-[260px] max-w-[260px] overflow-y-auto rounded bg-white shadow">
    <FilterWrapper label="Item 1" collapsed={true} {...args}>
      <div>SHOULD BE HIDDEN</div>
    </FilterWrapper>
    <FilterWrapper label="Item 2" collapsed={true} {...args}>
      <div>SHOULD BE HIDDEN</div>
    </FilterWrapper>
    <FilterWrapper label="Item 3" collapsed={true} {...args}>
      <div>SHOULD BE HIDDEN</div>
    </FilterWrapper>
  </div>
</Story>

<Story name="Three Filters - Second expanded" let:args>
  <div class="mr-6 h-full min-w-[260px] max-w-[260px] overflow-y-auto rounded bg-white shadow">
    <FilterWrapper label="Item 1" collapsed={true} {...args}>
      <div>SHOULD BE HIDDEN</div>
    </FilterWrapper>
    <FilterWrapper label="Item 2" collapsed={false} {...args}>
      <div>SHOWN</div>
    </FilterWrapper>
    <FilterWrapper label="Item 3" collapsed={true} {...args}>
      <div>SHOULD BE HIDDEN</div>
    </FilterWrapper>
  </div>
</Story>
