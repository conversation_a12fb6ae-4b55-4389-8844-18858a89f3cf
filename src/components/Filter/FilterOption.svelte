<script lang="ts">
  import IconClose from '@/components/Icons/IconClose.svelte';

  export let value: string;
  export let selected: boolean = false;
  export let docCount: number | undefined = undefined;
  export let onClick: (value: string) => void;
  export let displayCount: boolean = true;

  const handleClick = () => {
    onClick(value);
  };
</script>

<button
  on:click={handleClick}
  class="flex w-full cursor-pointer items-center justify-between rounded pb-1 pl-2 pr-1 pt-1.5 text-regular text-neutral-700"
  class:bg-brand-selected={selected}
  class:text-brand-notification={selected}
>
  <span class="option max-h-5 overflow-hidden text-ellipsis">
    <slot />
  </span>
  {#if displayCount && docCount !== undefined}
    {#if !selected}
      <div class="flex h-5 flex-none items-center rounded bg-neutral-100 px-2 text-neutral-800">
        {docCount}
      </div>
    {:else}
      <div
        class="mr-2 flex h-4 w-4 items-center justify-center rounded-full bg-brand-selectedAction text-brand-notification"
      >
        <IconClose />
      </div>
    {/if}
  {/if}
</button>

<style>
  .option {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
</style>
