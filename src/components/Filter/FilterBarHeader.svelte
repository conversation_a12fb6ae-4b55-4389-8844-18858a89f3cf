<script lang="ts">
  import Icon from '@/components/Icons/Icon.svelte';
  import IconCollapseAll from '@/components/Icons/IconCollapseAll.svelte';
  import IconExpandAll from '@/components/Icons/IconExpandAll.svelte';
  import { slide } from 'svelte/transition';

  export let collapsed: boolean;
  export let popupVisible: boolean;
  export let onCollapseAll: () => void;
</script>

<div class="relative flex items-center justify-between border-b border-neutral-200 p-4">
  <div class="flex flex-1">
    <slot name="add" />
    <slot name="reset" />
  </div>
  <button on:click={onCollapseAll} class="cursor-pointer p-1">
    {#if collapsed}
      <Icon IconComponent={IconCollapseAll} size="i-3" />
    {:else}
      <Icon IconComponent={IconExpandAll} size="i-3" />
    {/if}
  </button>
  {#if popupVisible}
    <div
      transition:slide
      class="column-configurator absolute right-3 top-[60px] z-10 w-60 rounded bg-white shadow before:shadow"
    >
      <slot name="popup" />
    </div>
  {/if}
</div>
