<script lang="ts">
  import Flatpickr from 'svelte-flatpickr';
  import Btn from '@/components/form/Btn.svelte';
  import type { StringRange } from '@/lib/es/types';

  export let from = '';
  export let to = '';

  let fromDate: Date;
  let toDate: Date;

  const optionsFrom = {
    enableTime: true,
    altInput: true,
    altFormat: 'Y-m-d h:i K',
    dateFormat: 'Y-m-d H:i:S'
  };

  const optionsTo = {
    enableTime: true,
    altFormat: 'Y-m-d h:i K',
    altInput: true,
    dateFormat: 'Y-m-d H:i:S'
  };

  $: if (!from) {
    fromDate = undefined;
  }
  $: if (!to) {
    toDate = undefined;
  }

  export let onSubmit: (data: StringRange) => void;

  const handleSubmit = () => {
    onSubmit({
      from: from || undefined,
      to: to || undefined
    });
  };
  const onReset = (e: Event) => {
    e.preventDefault();
    to = '';
    from = '';
    onSubmit({
      from: undefined,
      to: undefined
    });
  };
</script>

<div>
  <form on:submit|preventDefault={handleSubmit}>
    <div class="relative pl-1 pr-1.5">
      <Flatpickr
        options={optionsFrom}
        bind:value={fromDate}
        bind:formattedValue={from}
        name="dateFrom"
        class="mb-2 block h-8 w-full rounded border border-neutral-200 px-3
        py-1 text-regular text-neutral-700 placeholder-neutral-700
        focus:border-neutral-200 focus:shadow-none focus:ring-0"
        placeholder="From"
      />
      <div class="absolute right-6 top-1/2 h-4 w-4 -translate-y-1/2">
        <!-- TODO: add IconCalendar here -->
      </div>
    </div>
    <div class="relative pl-1 pr-1.5">
      <Flatpickr
        options={optionsTo}
        bind:value={toDate}
        bind:formattedValue={to}
        name="dateTo"
        class="mb-2 block h-8 w-full rounded border border-neutral-200 px-3
        py-1 text-regular text-neutral-700 placeholder-neutral-700
        focus:border-neutral-200 focus:shadow-none focus:ring-0"
        placeholder="To"
      />
      <div class="absolute right-6 top-1/2 h-4 w-4 -translate-y-1/2">
        <!-- TODO: add IconCalendar here -->
      </div>
    </div>
    <div class="flex justify-end">
      <Btn
        size="small"
        variant="gray-outline"
        class="ml-1 px-[0.5rem]"
        type="reset"
        on:click={onReset}>Clear</Btn
      >
      <Btn size="small" variant="outline" class="ml-1 px-[0.5rem]" type="submit">Apply</Btn>
    </div>
  </form>
</div>
