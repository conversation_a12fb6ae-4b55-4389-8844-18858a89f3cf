<script lang="ts">
  import type { FieldEntity } from '@/models/fields/fields';
  import capitalize from 'lodash.capitalize';
  import pluralizeWord from '@/utils/pluralize';
  import IconDownload from '../Icons/IconDownload.svelte';
  import InlineNotification from '../Notification/InlineNotification.svelte';
  import Btn from '../form/Btn.svelte';
  import { createExportMutation } from '@/mutations/export.mutation';
  import { createConfigQuery } from '@/queries/config.query';
  import type { ExportParams } from '@/repos/export-repo';
  import type { Channel, Source } from '@/models/config/config';
  import Loader from '../Loader/Loader.svelte';
  import { createQuery, useQueryClient } from '@tanstack/svelte-query';
  import ExportsRepo from '@/repos/export-repo';
  import Progress from '../Loader/Progress.svelte';
  import { onDestroy } from 'svelte';
  import { toast } from 'svelte-sonner';
  import sleep from '@/utils/sleep';
  import Select from '../Controls/Select/Select.svelte';
  import ModalInternal from '@/components/Modal/ModalInternal.svelte';
  import Icon from '@/components/Icons/Icon.svelte';
  import { isNumber } from '@/utils/typeguards';

  export let open: boolean;
  export let entity: FieldEntity;

  const configQuery = createConfigQuery();

  const queryClient = useQueryClient();

  let selectedChannel: Channel | undefined;
  let selectedChannelId: number;
  let selectedSource: Source | undefined;
  let selectedSourceId: number;

  $: {
    if (typeof selectedChannelId === 'undefined' && isNumber(selectedChannel?.id)) {
      selectedChannelId = selectedChannel?.id;
    } else {
      selectedChannel = channels.find((channel) => channel.id === selectedChannelId);
    }
  }
  $: {
    if (typeof selectedSourceId === 'undefined' && isNumber(selectedSource?.id)) {
      selectedSourceId = selectedSource?.id;
    } else {
      selectedSource = sources.find((source) => source.id === selectedSourceId);
    }
  }

  const exportsRepo = new ExportsRepo();

  $: channels = Object.values($configQuery.data?.channels ?? {}).filter(
    (channel) => channel.type === 'trade'
  );
  $: sources = Object.values($configQuery.data?.sources ?? {}).filter(
    (source) => !source.source_id
  );

  $: {
    if (sources.length === 1) {
      selectedSource = sources[0];
    }
  }

  let exportParams: ExportParams<FieldEntity>;
  $: {
    if (entity === 'customer' && selectedChannel) {
      exportParams = { channel_id: selectedChannel.id.toString() };
    }
    if (entity === 'product' && selectedSource) {
      exportParams = { source_id: selectedSource.id.toString() };
    }
  }

  $: exportMutation = createExportMutation({
    entity,
    options: exportParams
  });

  // This wrapper with cause the query to poll the endpoint until there is a csv url
  const getQueryWithCsvUrl = async () => {
    const res = await exportsRepo.getById(id);
    if (!res.url) {
      throw new Error('No CSV yet');
    }
    return res;
  };

  const RETRY_DELAY = 5000;
  const RETRY_MAX = 60;

  $: exportByIdQueryKey = ['exports-by-id', id];

  $: exportByIdQuery = createQuery({
    queryKey: exportByIdQueryKey,
    queryFn: () => (id ? getQueryWithCsvUrl() : undefined),
    enabled: !!id,
    retryDelay: RETRY_DELAY,
    retry: RETRY_MAX // with a delay of 5s, this means that we'll timeout after 5min
  });

  let id: number;

  // Garbage collect the query immediately
  onDestroy(() => {
    queryClient.removeQueries({ queryKey: exportByIdQueryKey });
  });

  $: csvUrl = $exportByIdQuery.data?.url;

  const runExport = async () => {
    const exportRes = await $exportMutation.mutateAsync();
    id = exportRes.id;
  };

  /**
   *  Simple promise for the toast to keep polling (we dont the entire need exportByIdQuery for the toast)
   */
  const checkForCSV = async (retries: number) => {
    try {
      const exportReq = await getQueryWithCsvUrl();
      const newCSVUrl = exportReq.url as string;
      window.location.assign(newCSVUrl);
      return;
    } catch {
      if (retries <= RETRY_MAX) {
        await sleep(RETRY_DELAY);
        await checkForCSV(retries + 1);
      } else {
        throw new Error(
          'Export is taking too long, please wait for an email with the download link'
        );
      }
    }
  };

  const handleInBackground = () => {
    toast.promise(() => checkForCSV($exportByIdQuery.failureCount), {
      loading: 'Export is processing',
      error: (e) => (e as Error).message,
      success: () => `Export is ready to download`
    });
    open = false;
  };

  const hideModal = () => {
    if (id && !csvUrl) {
      return handleInBackground();
    }
    open = false;
  };
</script>

<ModalInternal
  {hideModal}
  bind:open
  position="center"
  size="narrow"
  title="Export {capitalize(pluralizeWord(entity))}"
  disableClickOutside
>
  <Icon slot="header-prefix" IconComponent={IconDownload} size="i-6" />
  <div slot="body" class="flex flex-col gap-3 text-regular text-neutral-700">
    {#if $configQuery.isLoading}
      <Loader />
    {:else if ($exportMutation.isSuccess || $exportMutation.isPending) && !$exportByIdQuery.isError}
      {#if csvUrl}
        <InlineNotification type="success"
          >Please click below to download your export.
        </InlineNotification>
      {:else}
        <Progress
          title={`${!csvUrl ? `Processing` : `Processed`} export ${id ? `(id: ${id})` : ``}`}
          indeterminate
        />
        <InlineNotification type="info"
          >You will also a receive an email with a link to the completed export.
        </InlineNotification>
      {/if}
    {:else if $exportMutation.isError}
      <InlineNotification type="error"
        >Error encountered while trying to export{$exportMutation.error?.message
          ? `: ${$exportMutation.error?.message}`
          : `.`}
      </InlineNotification>
    {:else if $exportByIdQuery.isError}
      <InlineNotification type="info"
        >Export is taking too long, please wait for the email or try again. {$exportMutation.error
          ?.message
          ? `Error: ${$exportMutation.error?.message}`
          : ``}
      </InlineNotification>
    {:else}
      <p>
        You are about to export {pluralizeWord(entity)}. You will receive an email once your file
        download is ready.
      </p>
      {#if entity === 'customer'}
        <div class="mt-4">
          <Select
            label="Please select a channel"
            options={channels.map((o) => ({ value: o.id, label: o.description }))}
            bind:value={selectedChannelId}
          />
        </div>
      {/if}
      {#if entity === 'product' && sources.length > 0}
        <Select
          label="Please select a source"
          options={sources.map((o) => ({ value: o.id, label: o.description }))}
          bind:value={selectedSourceId}
        />
      {/if}
    {/if}
  </div>

  <div slot="footer" class="flex w-full gap-x-4 border-neutral-200">
    {#if !!id || $exportByIdQuery.data}
      {#if csvUrl}
        <a href={csvUrl} download class="w-full">
          <Btn size="large" variant="solid" class="w-full">Download Export</Btn>
        </a>
      {:else}
        <Btn size="large" variant="gray" onClick={handleInBackground} class="w-full"
          >Run in background</Btn
        >
      {/if}
    {:else}
      <Btn
        size="large"
        variant="gray"
        disabled={$exportMutation.isPending}
        onClick={() => (open = false)}
        class="w-1/2">Cancel</Btn
      >

      <Btn
        size="large"
        variant="solid"
        onClick={runExport}
        disabled={!(selectedChannel || selectedSource || $configQuery.isLoading) ||
          $exportMutation.isPending}
        class="w-1/2">Confirm Export</Btn
      >
    {/if}
  </div>
</ModalInternal>
