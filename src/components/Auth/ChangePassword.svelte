<script lang="ts">
  import { createMutation } from '@tanstack/svelte-query';
  import Button from '../Controls/Button.svelte';
  import TextInput from '../Controls/TextInput.svelte';
  import UsersRepo from '@/repos/users-repo';
  import { toast } from 'svelte-sonner';
  import { handleLogin } from '@/utils/auth/login';
  import { HTTPError } from '@/lib/api/api';

  export let username: string;
  export let oldPassword: string;
  export let mfaSent: boolean;
  let newPassword: string;
  let confirmPassword: string;

  const userRepo = new UsersRepo();

  const changePasswordMutation = createMutation({
    mutationFn: ({
      username,
      oldPassword,
      newPassword
    }: {
      username: string;
      oldPassword: string;
      newPassword: string;
    }) => userRepo.changePassword(username, oldPassword, newPassword)
  });

  const handleSubmit = async (e: Event) => {
    e.preventDefault();
    // Stop parent form from submitting
    e.stopPropagation();
    toast.dismiss();
    toast.promise(
      async () => {
        if (newPassword !== confirmPassword) {
          throw new Error('Passwords do not match');
        }
        const systemUser = await $changePasswordMutation.mutateAsync({
          username,
          oldPassword,
          newPassword
        });
        // ToDo - handle MFA (waiting on https://github.com/stock2shop/app/pull/2700#discussion_r1592432469)
        return await handleLogin(systemUser, true);
      },
      {
        loading: 'Changing password...',
        success: 'Password changed successfully',
        error: (e) => (e as Error).message
      }
    );
  };
</script>

<form on:submit={handleSubmit}>
  <p class="mb-8 text-center">
    <strong>Your password has expired</strong><br /> Please update your password
  </p>
  <div class="flex flex-col gap-4">
    <TextInput
      type="password"
      placeholder="New Password"
      id="password"
      required
      bind:value={newPassword}
    />
    <TextInput
      type="password"
      placeholder="Confirm New Password"
      id="password"
      required
      bind:value={confirmPassword}
    />
  </div>
  <div class="pt-5">
    <Button size="large" fullWidth disabled={$changePasswordMutation.isPending} type="submit"
      >Change Password</Button
    >
  </div>
</form>
