<script lang="ts">
  import Button from '../Controls/Button.svelte';
  import TextInput from '../Controls/TextInput.svelte';

  export let username: string;
  export let password: string;
  export let isLoading: boolean;
  export let showAccountLocked = false;
</script>

<div class="flex flex-col gap-4">
  <div>
    <TextInput bind:value={username} id="email" placeholder="Username" required />
  </div>
  <div class="flex flex-col gap-1">
    <TextInput
      type="password"
      placeholder="Password"
      id="password"
      required
      bind:value={password}
    />
    <div class="w-full text-right">
      <a href="/forgot" class="text-xs font-thin">Forgot password?</a>
    </div>
    {#if showAccountLocked}
      <div class="rounded-lg bg-white p-2">
        <p class="text-brand-error">
          <strong>Account locked!</strong> Your account has been locked for 1 hour due to multiple failed
          login attempts.
        </p>
      </div>
    {/if}
  </div>
</div>
<div class="pt-5">
  <Button size="large" fullWidth disabled={isLoading} type="submit">Login</Button>
</div>
