<script lang="ts">
  import {
    getBackgroundColourClass,
    getTextColourClass,
    type StatusType
  } from '@/components/Controls/status';
  import type { ComponentSize } from '@/components/Controls/size';
  import { cn } from '@/utils/classname';

  export let type: StatusType = 'success';
  export let size: ComponentSize = 'small';
  export let iconType: 'dot' | 'icon' = 'dot';

  // large and medium are the same
  const sizeStyles: Record<ComponentSize, string> = {
    small: 'px-2 py-1 gap-2 rounded-xl',
    medium: 'px-3 py-2 gap-3 rounded-xl',
    large: 'px-3 py-2 gap-3 rounded-xl'
  };
  const dotSizeStyles: Record<ComponentSize, string> = {
    small: 'h-2 w-2',
    medium: 'h-3 w-3',
    large: 'h-3 w-3'
  };
</script>

<div
  class={cn(
    getBackgroundColourClass(type),
    sizeStyles[size],
    `round flex w-max items-center bg-opacity-10`
  )}
>
  {#if iconType === 'dot'}
    <span class="{getBackgroundColourClass(type)} {dotSizeStyles[size]} rounded-full border-none"
    ></span>
  {/if}
  <div class="text-regular leading-4 text-neutral-700">
    <div class="{getTextColourClass(type)} overflow-hidden text-ellipsis">
      <slot />
    </div>
  </div>
</div>
