<script context="module">
  import Search from '@/components/Controls/Search.svelte';

  export const meta = {
    title: 'components/Controls/Search',
    component: Search
  };

  // custom event returns a string
  function alertSearch(event) {
    toast.info(event);
  }
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';
  import { toast } from 'svelte-sonner';
</script>

<Story name="Ghost large">
  <div id="Page" class="">
    <Search placeholder="Ghost large" size="large" type="ghost" onSearch={alertSearch} />
  </div>
</Story>

<Story name="Solid large">
  <div id="Page" class="">
    <Search placeholder="Solid large" type="solid" onSearch={alertSearch} />
  </div>
</Story>

<Story name="Ghost small">
  <div id="Page" class="">
    <Search placeholder="Ghost small" size="small" type="ghost" onSearch={alertSearch} />
  </div>
</Story>

<Story name="Solid small">
  <div id="Page" class="">
    <Search placeholder="Solid small" type="solid" size="small" onSearch={alertSearch} />
  </div>
</Story>
