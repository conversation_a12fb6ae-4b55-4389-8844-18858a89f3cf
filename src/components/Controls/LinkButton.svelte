<script lang="ts">
  import { onMount, type ComponentType } from 'svelte';
  import { createTooltip, melt } from '@melt-ui/svelte';
  import type { IconSize, ComponentSize } from '@/components/Controls/size';
  import IconComponent from '@/components/Icons/Icon.svelte';
  import TooltipContent from '@/components/Controls/Tooltip/TooltipContent.svelte';
  import { taphold } from '@/utils/actions/taphold';
  import { cn } from '@/utils/classname';
  import { goto } from '$app/navigation';

  export let icon: ComponentType | undefined = undefined;
  export let iconPosition: 'left' | 'right' = 'left';
  export let iconRotation: '0' | '90' | '180' = '0';
  export let size: ComponentSize = 'medium';
  export let disabled = false;
  export let type: 'submit' | 'button' | 'reset' = 'button';
  export let variant: 'solid' | 'gray' | 'outline' | 'gray-outline' | 'ghost' = 'solid';
  export let tapholdInterval = 200;
  export let tooltip: string | undefined = undefined;
  export let fullWidth = false;
  export let testId = 'test-button';
  export let draggable = false;
  export let dragging = false;
  export let focusOnMount = false;
  export let href = '#';

  let theButton: HTMLButtonElement;

  // tooltip config
  const {
    elements: { trigger, content, arrow },
    states: { open }
  } = createTooltip({
    openDelay: 0,
    closeDelay: 0,
    closeOnPointerDown: false,
    forceVisible: true,
    disableHoverableContent: true
  });

  const onlyIcon = !$$slots.default && icon;

  // if there is only an icon set the width of the button
  // and the icon sizes

  const buttonSizes = {
    small: onlyIcon ? 'h-6 w-6' : 'text-small px-2 leading-3 h-6',
    medium: onlyIcon ? 'h-8 w-8' : 'text-regular px-2.5 leading-3 h-8',
    large: onlyIcon ? 'h-10 w-10' : 'text-regular px-4 leading-4 h-10'
  };

  const iconSizes: Record<ComponentSize, IconSize> = {
    small: onlyIcon ? 'i-3' : 'i-3.5',
    medium: onlyIcon ? 'i-5' : 'i-3.5',
    large: onlyIcon ? 'i-6' : 'i-4'
  };

  const variants = {
    solid: `
              text-white bg-brand-action border-brand-action
              disabled:opacity-10 disabled:border-none
    `,
    gray: `
              bg-neutral-100 text-neutral-700 border-neutral-100
              hover:bg-neutral-200 hover:border-neutral-200
              disabled:border-neutral-100 disabled:text-neutral-400 disabled:bg-neutral-100
    `,
    outline: `
              bg-white text-brand-action border-brand-action
              hover:bg-brand-action hover:text-white
              disabled:border-neutral-200 disabled:text-neutral-400 disabled:bg-white
    `,
    ghost: `
              bg-white text-neutral-700 border-none
              hover:bg-neutral-100 hover:text-none
              disabled:text-neutral-400 disabled:bg-white
    `,
    'gray-outline': `
              bg-white text-neutral-700 border-neutral-200
              hover:bg-neutral-100
              disabled:border-neutral-200 disabled:text-neutral-400
    `
  };

  onMount(() => {
    if (focusOnMount) {
      theButton.focus();
    }
  });

  function navigate() {
    goto(href);
  }
</script>

<button
  bind:this={theButton}
  tabindex={dragging ? -1 : 0}
  {disabled}
  {type}
  use:melt={$trigger}
  use:taphold={tapholdInterval}
  on:taphold
  on:mousedown
  on:touchstart
  on:keydown
  on:click={navigate}
  data-testid={testId}
  class:rounded={!onlyIcon}
  class:rounded-full={onlyIcon}
  class:w-full={fullWidth}
  class:cursor-grab={draggable}
  class:cursor-grabbing={draggable && dragging}
  class={cn(
    `relative
     flex
     items-center
     justify-center
     gap-2
     border
     font-bold outline-none focus:border-[1px] focus:border-brand-notification focus:ring-0`,
    buttonSizes[size],
    variants[variant]
  )}
>
  {#if icon && iconPosition === 'left'}
    <span class={cn(`rotate-${iconRotation}`)}>
      <IconComponent IconComponent={icon} size={iconSizes[size]} />
    </span>
  {/if}
  {#if $$slots.default}
    <span>
      <slot />
    </span>
  {/if}
  {#if icon && iconPosition === 'right'}
    <span class={cn(`rotate-${iconRotation}`)}>
      <IconComponent IconComponent={icon} size={iconSizes[size]} />
    </span>
  {/if}
</button>

<!-- Tooltip Content-->
{#if tooltip}
  <TooltipContent {content} {arrow} {open} {tooltip}></TooltipContent>
{/if}
