<script lang="ts">
  import Select from '@/components/Controls/Select/Select.svelte';
  export let value = '';
  export let label = 'Time Zone';

  // List of TZ Identifiers
  // https://en.wikipedia.org/wiki/List_of_tz_database_time_zones
  const timezones = [
    // Allow user to remove value,
    // placeholder is not shown if value is selected
    { value: '', label: 'Select a time zone' },
    { value: 'Africa/Johannesburg', label: 'Africa/Johannesburg' }
  ];
</script>

<!-- TODO How should label be styled? -->
{label}
<Select bind:value options={timezones} />
