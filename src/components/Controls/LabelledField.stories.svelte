<script context="module">
  import PageCard from '@/components/Config/PageCard.svelte';
  import LabelledField from '@/components/Orders/LabelledField.svelte';

  export const meta = {
    title: 'components/Controls/LabelledField',
    component: LabelledField
  };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';
</script>

<Story name="Single">
  <div class="max-w-[1000px]">
    <PageCard noGap>
      <div class="mb-4 text-smaller font-bold text-brand-action">Single</div>
      <LabelledField label="Label" field="Field" />
    </PageCard>
  </div>
</Story>

<Story name="Many">
  <div class="max-w-[1000px]">
    <PageCard noGap>
      <div class="mb-4 text-smaller font-bold text-brand-action">Many</div>
      <LabelledField label="One" field="First" />
      <LabelledField label="Two" field="Second" />
      <LabelledField label="Three" field="third" />
      <LabelledField label="Four" field="Fourth" />
    </PageCard>
  </div>
</Story>

<Story name="Limited space">
  <div class="max-w-[1000px]">
    <PageCard noGap>
      <div class="mb-4 text-smaller font-bold text-brand-action">Limited space</div>
      <div class="max-w-[400px] bg-gray-50">
        <LabelledField label="One" field="What happens when there's more words than can fit" />
        <LabelledField label="Two" field="Second" />
        <LabelledField label="Three" field="third" />
        <LabelledField label="Four" field="Fourth" />
      </div>
    </PageCard>
  </div>
</Story>

<Story name="Field inactive">
  <div class="max-w-[1000px]">
    <PageCard noGap>
      <div class="mb-4 text-smaller font-bold text-brand-action">Field inactive</div>
      <LabelledField label="One" field="Inactive field one" fieldInactive />
      <LabelledField label="Two" field="Inactive field two" fieldInactive />
      <LabelledField label="Three" field="Inactive field three" fieldInactive />
      <LabelledField label="Four" field="Active field" />
    </PageCard>
  </div>
</Story>

<Story name="Status">
  <div class="max-w-[1000px]">
    <PageCard noGap>
      <div class="mb-4 text-smaller font-bold text-brand-action">Status</div>
      <LabelledField label="One" field="Default" statusField />
      <LabelledField label="Two" field="Error" statusField statusFieldType="error" />
      <LabelledField label="Three" field="Warning" statusField statusFieldType="warning" />
      <LabelledField label="Four" field="Success" statusField statusFieldType="success" />
    </PageCard>
  </div>
</Story>
