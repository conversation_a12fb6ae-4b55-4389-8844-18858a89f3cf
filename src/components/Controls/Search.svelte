<script lang="ts">
  import debounce from 'just-debounce-it';
  import IconSearch from '@/components/Icons/IconSearch.svelte';
  import Icon from '@/components/Icons/Icon.svelte';
  import { cn } from '@/utils/classname';
  import Clear from '@/components/Controls/Clear.svelte';
  import type { IconSize } from '@/components/Controls/size';

  export let search: string | undefined;
  export let onInput = (s: string) => {};
  export let onSearch = (s: string) => {};
  export let clearSearch = () => {
    search = '';
  };
  export let placeholder = 'Search';
  export let type: 'ghost' | 'solid' = 'ghost';
  export let size: 'small' | 'large' = 'large';
  export let timeoutMilliseconds = 300;

  const searchStyles = {
    ghost:
      'focus:ring-0 focus:border-blue-500 border-none focus:bg-neutral-50 hover:bg-neutral-50 ',
    solid:
      'focus:ring-brand-notification focus:border-blue-500 focus:ring-0 border-neutral-200 bg-white'
  };

  const searchSizes = {
    small: 'h-32 ps-8 h-8',
    large: 'p-2 ps-10 h-10'
  };

  const iconSizes: Record<string, IconSize> = {
    small: 'i-4',
    large: 'i-5'
  };

  const iconPosition = {
    small: 'left-2',
    large: 'left-3'
  };

  // use to get non-debounced input
  const onInputInternal = () => {
    onInput(search ?? '');
    typeahead();
  };

  const typeahead = debounce(() => {
    const cur = (search ?? '').trim();
    onSearch(cur);
  }, timeoutMilliseconds);
</script>

<div class="relative">
  <div class="absolute top-1/2 z-[1] mr-2 -translate-y-1/2 {iconPosition[size]}">
    <Icon IconComponent={IconSearch} size={iconSizes[size]} />
  </div>

  <input
    on:input={onInputInternal}
    {placeholder}
    bind:value={search}
    class={cn(
      `text-neutral-700" block w-full border text-regular 
      focus:border-brand-notification focus:ring-0`,
      searchStyles[type],
      searchSizes[size]
    )}
  />
  {#if search && search.length}
    <div class="absolute right-3 top-1/2 -translate-y-1/2">
      <Clear {size} onClick={clearSearch} />
    </div>
  {/if}
</div>
