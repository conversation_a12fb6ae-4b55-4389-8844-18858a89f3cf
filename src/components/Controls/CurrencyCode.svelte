<script lang="ts">
  import Select from '@/components/Controls/Select/Select.svelte';
  export let value = '';
  export let label = 'Currency Code';
  export let sideLabel = false;

  // List of currency codes
  // https://en.wikipedia.org/wiki/ISO_4217
  const CURRENCIES = [
    // Allow user to remove value,
    // placeholder is not shown if value is selected
    { value: '', label: 'Select a currency' },
    { value: 'EUR', label: 'EUR - Euro' },
    { value: 'NZD', label: 'NZD - New Zealand' },
    { value: 'USD', label: 'USD - United States' },
    { value: 'ZAR', label: 'ZAR - South Africa' }
  ];
</script>

<Select {sideLabel} {label} bind:value options={CURRENCIES} />
