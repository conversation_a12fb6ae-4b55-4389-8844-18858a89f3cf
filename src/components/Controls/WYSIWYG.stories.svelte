<script context="module">
  import WYSIWYG from '@/components/Controls/WYSIWYG.svelte';

  export const meta = { title: 'components/Controls/WYSIWYG', component: WYSIWYG };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';
  let value: string =
    '<h1>Great Heading!</h1><p>A paragraph.</p><p></p><p>Another one that.</p><p></p><ul><li><p>A very</p></li><li><p>Good</p></li><li><p>Bullet</p></li><li><p>List</p></li></ul>';
</script>

<Story name="Primary">
  <WYSIWYG bind:value />
  <div class="w-full pt-6">
    <pre class="whitespace-pre-wrap">{value}</pre>
  </div>
</Story>
