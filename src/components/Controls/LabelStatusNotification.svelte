<script lang="ts">
  import {
    getBackgroundColourClass,
    getTextColourClass,
    type StatusType
  } from '@/components/Controls/status';
  import type { ComponentSize, IconSize } from '@/components/Controls/size';
  import type { ComponentType } from 'svelte';
  import IconComponent from '@/components/Icons/Icon.svelte';

  export let status: StatusType = 'success';
  export let size: ComponentSize = 'small';
  export let icon: ComponentType | undefined = undefined;

  const sizes = {
    small: 'text-small px-4 leading-3 h-5 gap-2',
    medium: 'text-regular px-4 leading-3 h-8 gap-2',
    large: 'text-regular px-4 leading-4 h-10 gap-2'
  };

  const iconSizes: Record<ComponentSize, IconSize> = {
    small: 'i-3.5',
    medium: 'i-3.5',
    large: 'i-4'
  };
</script>

<div
  class="{getBackgroundColourClass(status)} {getTextColourClass(status)} {sizes[
    size
  ]} flex w-max items-center rounded bg-opacity-10"
>
  {#if icon}
    <IconComponent IconComponent={icon} size={iconSizes[size]} />
  {/if}
  <slot />
</div>
