<script context="module">
  import Image from '@/components/Controls/Image.svelte';

  export const meta = {
    title: 'components/Controls/Image',
    component: Image
  };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';
  import type ImageType from '@/models/image';

  const imageWithDeadLinks = {
    src: 'https://example.com/this-image-wont-resolve.png',
    src_160x160: 'https://example.com/this-image-wont-resolve_160x160.png',
    src_50x50: 'https://example.com/this-image-wont-resolve_50x50.png'
  } as ImageType;

  const imageWithWorkingLinks = {
    src: '/images/5784c14b337adaf4d598178b26c640bd.jpg',
    src_160x160: '/images/thumb/160x160/5784c14b337adaf4d598178b26c640bd.jpg',
    src_50x50: '/images/thumb/50x50/5784c14b337adaf4d598178b26c640bd.jpg'
  } as ImageType;
</script>

<Story name="Placeholder">
  <p>Note: wrapping div can be any size, size just affects the icon size</p>
  <br />
  Large (src)
  <div class=" h-64 w-64">
    <Image src="src" />
  </div>
  Thumb (160px x 160px)
  <div class=" h-[160px] w-[160px]">
    <Image src="src_160x160" />
  </div>

  Thumb (50px x 50px)
  <div class=" h-[50px] w-[50px]">
    <Image src="src_50x50" />
  </div>
</Story>

<Story name="No image found">
  <p>Note: wrapping div can be any size, size just affects the icon size</p>
  <br />
  <p>Large (src)</p>
  <div class=" h-64 w-64">
    <Image src="src" image={imageWithDeadLinks} />
  </div>
  <p>Thumb (160px x 160px)</p>
  <div class=" h-[160px] w-[160px]">
    <Image src="src_160x160" image={imageWithDeadLinks} />
  </div>

  <p>Thumb (50px x 50px)</p>
  <div class=" h-[50px] w-[50px]">
    <Image src="src_50x50" image={imageWithDeadLinks} />
  </div>
</Story>

<Story name="With images">
  <p>Note: wrapping div can be any size, size just affects the icon size</p>
  <br />
  <p>Large (src)</p>
  <div class=" h-64 w-64">
    <Image src="src" image={imageWithWorkingLinks} />
  </div>
  <p>Thumb (160px x 160px)</p>
  <div class=" h-[160px] w-[160px]">
    <Image src="src_160x160" image={imageWithWorkingLinks} />
  </div>

  <p>Thumb (50px x 50px)</p>
  <div class=" h-[50px] w-[50px]">
    <Image src="src_50x50" image={imageWithWorkingLinks} />
  </div>
</Story>
