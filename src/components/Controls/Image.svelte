<script lang="ts">
  import { type IconSize, type ImageSize } from '@/components/Controls/size';
  import Icon from '@/components/Icons/Icon.svelte';
  import Skeleton from '@/components/Loader/Skeleton.svelte';
  import type Image from '@/models/image';
  import { cn } from '@/utils/classname';
  import IconAlert from '../Icons/IconAlert.svelte';
  import IconImage from '@/components/Icons/IconImage.svelte';
  import { IMAGE_DOMAIN } from '@/constants.js';

  export let image: Image | undefined = undefined;
  export let src: ImageSize;
  export let alt = 'Image';

  const placeholderIconSizes: Record<ImageSize, IconSize> = {
    src: 'i-8',
    src_160x160: 'i-7',
    src_50x50: 'i-3'
  };

  let srcUrl: string | undefined;
  $: srcUrl = image ? image[src] : undefined;
  $: if (image) {
    switch (src) {
      case 'src':
        srcUrl = `${IMAGE_DOMAIN}/${image.storage_code}`;
        break;
      case 'src_160x160':
        srcUrl = `${IMAGE_DOMAIN}/thumb/160x160/${image.storage_code}`;
        break;
      case 'src_50x50':
        srcUrl = `${IMAGE_DOMAIN}/thumb/50x50/${image.storage_code}`;
        break;
    }
  }

  let isLoading = !srcUrl;
  let isError = false;
</script>

<!-- @component
 Image display with loader and placeholder fallback.

 ToDo: add lightbox onclick functionality
-->

<!-- Try load the image as base64 then pass to img in {:then} block if it exists -->
{#if srcUrl && !isError}
  <Skeleton shimmer class={cn('h-full w-full', { hidden: !isLoading })} />
  <img
    src={srcUrl}
    {alt}
    on:load={() => {
      isLoading = false;
    }}
    on:error={() => {
      isError = true;
    }}
    class={cn('h-full w-full object-contain', { hidden: isLoading })}
  />
{:else}
  <!-- Placeholder container will always scale to parent container.
       Icon size will just change base on size.
  -->
  <div
    aria-label={alt}
    class="flex h-full w-full items-center justify-center bg-neutral-100 text-neutral-400"
  >
    <Icon IconComponent={isError ? IconAlert : IconImage} size={placeholderIconSizes[src]} />
  </div>
{/if}
