<script lang="ts">
  import { cn } from '@/utils/classname';
  import Icon from '../Icons/Icon.svelte';
  import { createLabel } from '@melt-ui/svelte';
  import IconExpandArrow from '@/components/Icons/IconExpandArrow.svelte';
  import { isNumber } from '@/utils/typeguards';
  import LabelWrapper from '@/components/Controls/LabelWrapper.svelte';
  import { inputSizeMap, type LimitedComponentSize } from '@/components/Controls/size';
  import { generateId } from '@/utils/generateId';

  export let id = generateId();
  export let tabIndex = 0;
  export let value: number | string;
  export let isDisable = false;
  export let max = Number.POSITIVE_INFINITY;
  export let min = 0;
  export let hideSpinner = false;
  export let onChange = (_: Event) => {};
  export let onBlur = (_: Event) => {};
  export let step = 1;
  /**
   * Defaults to deriving type from initial `value` type
   */
  export let type: 'number' | 'string' | undefined = undefined;
  /**
   * Make sure to also set the step value
   * to match the correct amount of decimal
   * places that you'd like to support (ie: 0.1)
   */
  export let decimals = true;
  export let label = '';
  export let sideLabel = false;
  export let textAlign: 'left' | 'right' = 'left';
  export let unit = '';
  export let required = false;
  export let size: LimitedComponentSize = 'large';
  /**
   * ONLY use when you you need a smaller label width
   * (useful when it's a side label)
   */
  export let smallLabel = false;

  // Allows us to bind to number or string
  const valToInternal = (val: number | string) => (isNumber(val) ? val : parseFloat(val));
  const internalToVal = (internal: number) =>
    isNumber(value) || type === 'number' ? internal : internal.toString();

  let valueInternal = valToInternal(value);

  $: decimalLength = step?.toString().split('.')[1]?.length ?? 0;

  function increment() {
    valueInternal = parseFloat((valueInternal + step).toFixed(decimalLength));
  }

  function decrement() {
    valueInternal = parseFloat((valueInternal - step).toFixed(decimalLength));
  }

  $: {
    if (typeof valueInternal !== 'number') {
      valueInternal = 0;
    } else if (
      valueInternal >= min &&
      valueInternal <= max &&
      (!decimals ? valueInternal % 1 === 0 : true)
    ) {
      value = internalToVal(valueInternal);
    } else {
      valueInternal = valToInternal(value);
    }
  }

  let focus = false;

  const {
    elements: { root }
  } = createLabel();
</script>

<LabelWrapper {smallLabel} meltAction={root} {label} {required} side={sideLabel}>
  <div class="relative">
    <input
      on:focus={() => (focus = true)}
      {id}
      class={cn(
        inputSizeMap[size],
        `flex flex-grow flex-row items-center justify-start rounded border
         border-neutral-200 bg-white text-regular text-neutral-700 
          focus:border-brand-notification focus:ring-0 
          [&::-webkit-inner-spin-button]:appearance-none`,
        'w-full',
        !hideSpinner && 'pr-9',
        { 'text-right': textAlign === 'right' },
        { ' invalid:border-brand-error': focus }
      )}
      name={id}
      tabindex={tabIndex}
      disabled={isDisable}
      type="number"
      bind:value={valueInternal}
      on:change={onChange}
      on:blur={onBlur}
      {step}
      {required}
      {min}
      {max}
    />
    {#if unit}
      <div
        class={cn(
          'pointer-events-none absolute  top-1/2 ml-2 -translate-y-1/2 text-regular leading-4 text-neutral-400',
          !hideSpinner ? 'right-8' : 'right-4'
        )}
      >
        {unit}
      </div>
    {/if}

    {#if !hideSpinner}
      <div class="absolute bottom-[1px] right-[1px] top-[1px] flex w-[26px] flex-col">
        <button
          type="button"
          class="95 flex h-full cursor-pointer items-center justify-center rounded-tr border-l border-neutral-200"
          on:click={increment}
        >
          <div class="rotate-180">
            <Icon size="i-3" IconComponent={IconExpandArrow} />
          </div>
        </button>
        <button
          type="button"
          class=" bottom-0 right-0 flex h-full cursor-pointer items-center justify-center rounded-br border-l border-t border-neutral-200"
          on:click={decrement}
        >
          <div>
            <Icon size="i-3" IconComponent={IconExpandArrow} />
          </div>
        </button>
      </div>
    {/if}
  </div>
</LabelWrapper>
