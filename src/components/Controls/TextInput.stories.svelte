<script context="module">
  export const meta = {
    title: 'components/Controls/TextInput',
    component: TextInput
  };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';
  import TextInput from '@/components/Controls/TextInput.svelte';

  const value = 'Test value';
</script>

<Story name="Default">
  <TextInput />
</Story>

<Story name="Value set">
  <TextInput {value} />
</Story>

<Story name="With label">
  <TextInput {value} label="This is an input label" />
</Story>

<Story name="With side label">
  <TextInput {value} label="This is an input label" sideLabel={true} />
</Story>

<Story name="With long label">
  <div class="w-32">
    <TextInput
      {value}
      label="Lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet, consectetur adipiscing elit."
    />
  </div>
</Story>

<Story name="With long side label">
  <div class="w-64">
    <TextInput
      sideLabel
      {value}
      label="Lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet, consectetur adipiscing elit."
    />
  </div>
</Story>

<Story name="With long side label (small)">
  <div class="w-64">
    <TextInput
      smallLabel
      sideLabel
      {value}
      label="Lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet, consectetur adipiscing elit."
    />
  </div>
</Story>

<Story name="Disabled">
  <TextInput isDisable={true} {value} />
</Story>

<Story name="Password">
  <div class="w-[300px]">
    <TextInput type="password" {value} sideLabel={true} />
  </div>
</Story>

<Story name="Small">
  <div class="w-[300px]">
    <TextInput {value} size="small" />
  </div>
</Story>

<Story name="Large">
  <div class="w-[300px]">
    <TextInput {value} size="large" />
  </div>
</Story>
