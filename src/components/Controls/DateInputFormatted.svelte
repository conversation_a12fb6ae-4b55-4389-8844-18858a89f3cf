<script lang="ts">
  import DateInput from '@/components/Controls/DateInput.svelte';
  import { getDateFromStringOrTimestamp } from '@/lib/date-utils';

  // value is the formatted date
  export let value: string;

  $: date = value ? getDateFromStringOrTimestamp(value) : undefined;

  const handleClear = () => {
    value = '';
  };

  // Date string must be in ISO 8061 format, you can include timezone (e.g. 2020-11-02T06:04:07+02:00)

  // This component was inspired by Config/shared/cards/OrderFromDateCard.svelte
  // Instead of hard-coding the META_KEY etc,
  // try to be a generic input component for formatted dates
</script>

<DateInput
  canClear
  {handleClear}
  bind:value={date}
  bind:formattedValue={value}
  options={{ enableTime: false, dateFormat: 'Z', altInput: true, altFormat: 'Y-m-d h:i K' }}
/>
