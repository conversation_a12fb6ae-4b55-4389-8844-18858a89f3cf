<script lang="ts">
  import Label from '@/components/Controls/Label.svelte';
  import { cn } from '@/utils/classname';
  import { createLabel, createSelect } from '@melt-ui/svelte';

  const {
    elements: { root: labelInternal }
  } = createLabel();

  const {
    elements: { label: selectInternal }
  } = createSelect();

  export let label: string | undefined;
  export let required = false;
  export let side = false;
  /**
   * ONLY use when you you need a smaller label width
   * (useful when it's a side label)
   *
   * When this is enabled the label will be 90px wide instead of a minimum of 150px.
   */
  export let smallLabel = false;
  export let meltAction: typeof labelInternal | typeof selectInternal;
  export let id: string = ($meltAction as typeof $selectInternal).id ?? '';
  /**
   * Additional class names to add to the wrapping div.
   *
   * Generally, we try to avoid these but it's justified in
   * this case as it's a wrapper component.
   */
  let className = '';
  export { className as class };
</script>

<div
  class={cn(
    'relative flex flex-col',
    {
      'flex flex-row items-center justify-between': side
    },
    className
  )}
>
  {#if label}
    <div class={cn(smallLabel ? 'min-w-[90px] max-w-[90px]' : 'min-w-[150px]', { 'pr-1': side })}>
      <!-- svelte-ignore a11y-label-has-associated-control - $label contains the 'for' attribute -->
      <Label {id} {meltAction} {side} {required} title={label} />
    </div>
  {/if}
  <slot />
</div>
