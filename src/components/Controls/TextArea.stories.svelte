<script context="module">
  import TextArea from '@/components/Controls/TextArea.svelte';

  export const meta = {
    title: 'components/Controls/TextArea',
    components: TextArea
  };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';

  const value = 'Test value';
  const rows = 5;
</script>

<Story name="Default">
  <TextArea {value} {rows} />
</Story>

<Story name="With title">
  <TextArea {value} {rows} label={'This is a title'} />
</Story>
