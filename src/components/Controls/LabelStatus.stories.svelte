<script context="module">
  import LabelStatus from '@/components/Controls/LabelStatus.svelte';

  export const meta = {
    title: 'components/Controls/LabelStatus',
    component: LabelStatus
  };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';
</script>

<Story name="Primary">
  <div id="Page" class="space-y-2">
    <LabelStatus type="default"><div class="w-16">Default</div></LabelStatus>
    <LabelStatus type="success"><div class="w-16">Success</div></LabelStatus>
    <LabelStatus type="warning">Warning</LabelStatus>
    <LabelStatus type="info">Info</LabelStatus>
    <LabelStatus type="error">Error</LabelStatus>
    <LabelStatus type="default" size="medium"><div class="w-16">Default</div></LabelStatus>
    <LabelStatus type="success" size="medium">Success</LabelStatus>
    <LabelStatus type="warning" size="medium">Warning</LabelStatus>
    <LabelStatus type="info" size="medium">Info</LabelStatus>
    <LabelStatus type="error" size="medium">Error</LabelStatus>
  </div>
</Story>
