<script lang="ts" generics="T extends {label: string; value: string}">
  import { quintOut } from 'svelte/easing';

  import { scale } from 'svelte/transition';

  import { cn } from '@/utils/classname';
  import { generateId } from '@/utils/generateId';
  import { isString } from '@/utils/typeguards';
  import { createRadioGroup, melt } from '@melt-ui/svelte';

  type Orientation = 'horizontal' | 'vertical';

  export let options: T[] = [];
  export let value: string | undefined = undefined;
  export let orientation: Orientation = 'vertical';
  export let name: string = generateId();
  export let label = '';
  /**
   * Trigger validation by make the radio group required
   */
  export let required = false;

  const radioDirectionMap: Record<Orientation, string> = {
    horizontal: 'flex-row gap-5',
    vertical: 'flex-col gap-0'
  } as const;

  const {
    elements: { root, item, hiddenInput },
    helpers: { isChecked },
    states: { value: valueInternal }
  } = createRadioGroup({
    defaultValue: value,
    orientation,
    required,
    onValueChange: ({ curr, next }) => {
      value = next;
      return next;
    }
  });

  $: if (isString(value)) {
    // Sync external changes with internal state
    $valueInternal = value;
  }
</script>

<!-- @component
Radio group 
-->

<div
  aria-label={label}
  use:melt={$root}
  class={cn('flex text-neutral-700', radioDirectionMap[orientation])}
>
  {#each options as option}
    <div class="flex h-7 w-full items-center justify-between text-neutral-500">
      <div class="relative flex items-center">
        <!-- Note: Similar to Select Melt's $hiddenInput doesnt work as desired here,
             so we're rather opting for these faux inputs 
        -->
        <input
          {required}
          type="radio"
          class="absolute opacity-0"
          bind:group={value}
          {name}
          value={option.value}
        />
        <button
          use:melt={$item(option.value)}
          class={`grid h-[18px] w-[18px] cursor-default place-items-center 
          rounded-full border-2 border-solid  
          ${$isChecked(option.value) ? 'border-brand-notification bg-transparent' : 'border-neutral-300 bg-white hover:bg-gray-100 '}`}
          id={option.value}
          aria-labelledby="{option.value}-label"
        >
          {#if $isChecked(option.value)}
            <div
              transition:scale={{ duration: 300, opacity: 1, start: 0, easing: quintOut }}
              class="h-2 w-2 rounded-full bg-brand-notification"
            />
          {/if}
        </button>
        <label for={option.value} id="{option.value}-label" class="ml-2 text-regular">
          {option.label}
        </label>
      </div>
      <slot {option} />
    </div>
  {/each}
  <!-- <input use:melt={$hiddenInput} /> -->
</div>
