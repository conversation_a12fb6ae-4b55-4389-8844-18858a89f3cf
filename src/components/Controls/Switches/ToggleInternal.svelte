<script lang="ts" generics="T extends boolean | string">
  import { isString } from '@/utils/typeguards';

  export let checked: T;
  export let disabled = false;
  export let type: 'string' | 'boolean' = 'boolean';
  export let id: string;

  /**
   * Callback for changes when reactive block doesn't make sense
   */
  export let onChange = (_: T) => {};

  const bindToBooleanString = type === 'string';

  const bindToBoolean = type === 'boolean';

  $: internalCheck = bindToBooleanString
    ? //Catch incorrect case
      isString(checked) && checked.toLowerCase() === 'true'
      ? true
      : false
    : bindToBoolean
      ? (checked as boolean)
      : undefined;

  // We're not binding so that we dont set values if the user hasnt made any changes
  const handleChange = (e: Event & { currentTarget: HTMLInputElement }) => {
    internalCheck = e.currentTarget.checked;

    if (bindToBoolean) {
      onChange(internalCheck as T);
      checked = internalCheck as T;
      return;
    }
    /**
     * Allows toggles to be binded to 'true'/'false' values instead of boolean
     */
    if (bindToBooleanString) {
      const newCheckString = internalCheck.toString() as T;
      onChange(newCheckString);
      checked = newCheckString;
    }
  };
</script>

<label class="switch">
  <input on:change={handleChange} checked={internalCheck} type="checkbox" {disabled} {id} />
  <span class="slider round" />
</label>

<style lang="postcss">
  .switch {
    position: relative;
    display: inline-block;
    min-width: 36px;
    width: 36px;
    height: 18px;
  }

  .switch input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: 0.4s;
    transition: 0.4s;
    transition-property: color;
  }

  .slider:before {
    position: absolute;
    content: '';
    height: 14px;
    width: 14px;
    left: 2px;
    right: auto;
    top: 50%;
    transform: translateY(-50%);
    bottom: 2px;
    background-color: white;
    -webkit-transition: 0.4s;
    transition: 0.4s;
  }

  input:checked + .slider {
    @apply bg-brand-notification/90;
  }

  input:focus + .slider {
    box-shadow: 0 0 1px;
    @apply shadow-neutral-500 !important;
    @apply outline outline-brand-notification;
  }

  input:checked + .slider:before {
    -webkit-transform: translateX(18px) translateY(-50%);
    -ms-transform: translateX(18px) translateY(-50%);
    transform: translateX(18px) translateY(-50%);
  }

  /* Rounded sliders */
  .slider.round {
    border-radius: 34px;
  }

  .slider.round:before {
    border-radius: 50%;
  }
</style>
