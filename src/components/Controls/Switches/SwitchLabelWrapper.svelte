<script lang="ts">
  import { createLabel } from '@melt-ui/svelte';
  import { cn } from '@/utils/classname';
  import Label from '@/components/Controls/Label.svelte';
  import type { ComponentSize, LimitedComponentSize } from '@/components/Controls/size';

  export let label = '';
  export let noLabelBorder = false;
  const {
    elements: { root: labelInternal }
  } = createLabel();
  /**
   * Only affects the size of the wrapper div, not the toggle itself,
   * the switches are always the same size.
   */
  export let size: LimitedComponentSize = 'large';
  export let id: string;
  export let disabled = false;
  export let meltAction = labelInternal;
  export let labelRight = false;

  /**
   * Describes the minimum height
   */
  const switchSizeMap: Record<Exclude<ComponentSize, 'medium'>, string> = {
    small: 'min-h-8',
    large: 'min-h-10'
  };
</script>

<!-- @component
 Label wrapper for switch components (Toggle, Radio and Checkbox)
 -->

<div
  class={cn(
    label
      ? [
          'flex items-center justify-between gap-[10px] rounded bg-white  ',
          { 'border border-neutral-200 px-3': !noLabelBorder },
          {
            'opacity-70': disabled
          },
          { 'flex-row-reverse justify-end': labelRight },
          switchSizeMap[size]
        ]
      : []
  )}
>
  {#if label}
    <!-- 
      Add some y-axis padding here since this wrapper uses border. 
      (the regular LabelWrapper does not use border)
    -->
    <div class="py-2">
      <Label side {id} {meltAction} title={label} />
    </div>
  {/if}

  <slot />
</div>
