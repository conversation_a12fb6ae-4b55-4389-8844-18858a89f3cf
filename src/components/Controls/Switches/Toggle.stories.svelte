<script context="module">
  export const meta = { title: 'components/Controls/Switches/Toggle', component: Toggle };
</script>

<script lang="ts">
  import Toggle from '@/components/Controls/Switches/Toggle.svelte';

  import { Story } from '@storybook/addon-svelte-csf';
  let checked: boolean = false;
</script>

<Story name="No label">
  <div class="max-w-xs">
    <Toggle {checked} />
  </div>
</Story>

<Story name="No label - checked">
  <div class="max-w-xs">
    <Toggle checked />
  </div>
</Story>

<Story name="Label">
  <div class="max-w-xs">
    <Toggle {checked} label="Enable sync" />
  </div>
</Story>

<Story name="Label - small">
  <div class="max-w-xs">
    <Toggle {checked} label="Enable sync" size="small" />
  </div>
</Story>

<Story name="Label - checked">
  <div class="max-w-xs">
    <Toggle checked label="Enable sync" />
  </div>
</Story>

<Story name="Label right - checked">
  <div class="max-w-xs">
    <Toggle labelRight checked label="Enable sync" />
  </div>
</Story>

<Story name="Long Label">
  <div class="max-w-xs">
    <Toggle
      {checked}
      label="Lorem ipsum dolor sit amet, consectetur adipiscing elit Lorem ipsum dolor sit amet, consectetur adipiscing elit Lorem ipsum dolor sit amet, consectetur adipiscing elit "
    />
  </div>
</Story>

<Story name="Long Label - small (intentional no change since min-w)">
  <div class="max-w-xs">
    <Toggle
      size="small"
      {checked}
      label="Lorem ipsum dolor sit amet, consectetur adipiscing elit Lorem ipsum dolor sit amet, consectetur adipiscing elit Lorem ipsum dolor sit amet, consectetur adipiscing elit "
    />
  </div>
</Story>

<Story name="Label no border">
  <div class="max-w-xs">
    <Toggle {checked} noLabelBorder label="Enable sync" />
  </div>
</Story>

<Story name="Label no border - checked">
  <div class="max-w-xs">
    <Toggle checked noLabelBorder label="Enable sync" />
  </div>
</Story>
