<script context="module">
  export const meta = {
    title: 'components/Controls/Switches/RadioGroup',
    component: RadioGroup
  };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';
  import RadioGroup from '@/components/Controls/Switches/RadioGroup.svelte';
  import Button from '@/components/Controls/Button.svelte';

  let value = '';
  const options = [
    { label: 'Option 1', value: '1' },
    { label: 'Option 2', value: '2' },
    { label: 'Option 3', value: '3' }
  ];
</script>

<Story name="Vertical - default">
  <RadioGroup orientation="vertical" bind:value {options} />
  {value}
</Story>

<Story name="Horizontal">
  <RadioGroup orientation="horizontal" bind:value {options} />
  {value}
</Story>

<Story name="Validation">
  <form on:submit|preventDefault>
    <RadioGroup required orientation="horizontal" bind:value {options} />
    {value}
    <Button type="submit">Submit</Button>
  </form>
</Story>
