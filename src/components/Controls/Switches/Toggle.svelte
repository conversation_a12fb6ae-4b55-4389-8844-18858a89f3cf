<script lang="ts" generics="T extends boolean | string">
  import SwitchLabelWrapper from '@/components/Controls/Switches/SwitchLabelWrapper.svelte';
  import ToggleInternal from '@/components/Controls/Switches/ToggleInternal.svelte';
  import { type LimitedComponentSize } from '@/components/Controls/size';
  import { generateId } from '@/utils/generateId';
  import { createLabel } from '@melt-ui/svelte';

  export let label = '';
  export let id = generateId();
  export let checked: T;
  export let disabled = false;
  export let type: 'string' | 'boolean' = 'boolean';
  export let noLabelBorder = false;
  export let labelRight = false;
  export let onChange = (_: T) => {};

  /**
   * Only affects the size of the wrapper div, not the toggle itself,
   * the toggle itself is always the same size.
   */
  export let size: LimitedComponentSize = 'large';

  const {
    elements: { root: labelInternal }
  } = createLabel();
</script>

<!-- @component
    A clean toggle switch or a toggle switch with a label if a label is provided.
 -->

<SwitchLabelWrapper
  {size}
  {labelRight}
  {label}
  {noLabelBorder}
  {id}
  {disabled}
  meltAction={labelInternal}
>
  <ToggleInternal {id} bind:checked {disabled} {type} {onChange} />
</SwitchLabelWrapper>
