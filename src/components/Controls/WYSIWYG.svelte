<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { Editor } from '@tiptap/core';
  // TODO seems tree-shaking is available. figure out only the required extensions
  import StarterKit from '@tiptap/starter-kit';
  import BulletList from '@tiptap/extension-bullet-list';
  import TextAlign from '@tiptap/extension-text-align';
  import IconList from '@/components/Icons/IconList.svelte';
  import IconAlignLeft from '@/components/Icons/IconAlignLeft.svelte';
  import IconAlignCenter from '@/components/Icons/IconAlignCenter.svelte';
  import IconAlignRight from '@/components/Icons/IconAlignRight.svelte';
  import IconAlignJustify from '@/components/Icons/IconAlignJustify.svelte';

  export let value: string;

  let viewHTML = false;
  let element: HTMLElement;
  let editor: Editor;

  $: elementHTML = editor?.getHTML();

  // TODO linter does not like the editorProps type
  onMount(() => {
    editor = new Editor({
      element: element,
      extensions: [
        StarterKit,
        BulletList.configure({
          HTMLAttributes: {
            class: 'list-disc'
          }
        }),
        TextAlign.configure({
          types: ['heading', 'paragraph'] // Apply alignment to these block types
        })
      ],
      editorProps: {
        attributes: {
          class: 'border p-6 rounded'
        }
      },
      content: value,
      onTransaction: () => {
        // force re-render so `editor.isActive` works as expected
        editor = editor;
      },
      onUpdate: ({ editor }) => {
        value = patchTipTapHTML(editor.getHTML()) as string;
      }
    });
  });

  onDestroy(() => {
    if (editor) {
      editor.destroy();
    }
  });

  function setTextAlign(newValue: 'left' | 'center' | 'right' | 'justify') {
    editor.chain().focus().setTextAlign(newValue).run();
  }

  function toggleHTML() {
    viewHTML = !viewHTML;
  }

  function handleChange(e: Event) {
    const textValue = (e.target as HTMLTextAreaElement).value;
    if (textValue) {
      editor.commands.setContent(textValue);
    }
    value = textValue;
  }

  export function setContent(content: string) {
    editor.commands.setContent(content);
  }

  function patchTipTapHTML(content: string | null) {
    // TODO this is a workaround for the issue with <p> inside <li>
    // This is a hack but there seems to be no good way to do this (https://github.com/ueberdosis/tiptap/issues/118)
    if (content === null) return null;

    const parser = new DOMParser();
    const doc = parser.parseFromString(content, 'text/html');

    const listItems = doc.querySelectorAll('li');
    listItems.forEach((li) => {
      const p = li.querySelector('p');
      if (p) {
        while (p.firstChild) {
          li.insertBefore(p.firstChild, p);
        }
        p.remove();
      }
    });

    return doc.body.innerHTML;
  }
</script>

<div class="space-y-4">
  {#if editor}
    <div class="flex w-min gap-0.5 overflow-hidden rounded-full bg-gray-200 p-0.5">
      <button
        type="button"
        class="w-10 rounded-l-full"
        class:bg-white={editor.isActive('heading', { level: 1 }) && !viewHTML}
        disabled={viewHTML}
        on:click|preventDefault={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
      >
        H1
      </button>
      <button
        type="button"
        class="w-10"
        class:bg-white={editor.isActive('heading', { level: 2 }) && !viewHTML}
        disabled={viewHTML}
        on:click|preventDefault={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
      >
        H2
      </button>
      <button
        type="button"
        class="w-10"
        class:bg-white={editor.isActive('heading', { level: 3 }) && !viewHTML}
        disabled={viewHTML}
        on:click|preventDefault={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
      >
        H3
      </button>
      <button
        type="button"
        class="w-10"
        class:bg-white={editor.isActive('bold') && !viewHTML}
        disabled={viewHTML}
        on:click|preventDefault={() => editor.chain().focus().toggleBold().run()}
      >
        B
      </button>
      <button
        type="button"
        class="w-10 italic"
        class:bg-white={editor.isActive('italic') && !viewHTML}
        disabled={viewHTML}
        on:click|preventDefault={() => editor.chain().focus().toggleItalic().run()}
      >
        I
      </button>
      <button
        type="button"
        class="flex w-10 justify-center"
        class:bg-white={editor.isActive('bulletList') && !viewHTML}
        disabled={viewHTML}
        on:click|preventDefault={() => editor.chain().focus().toggleBulletList().run()}
      >
        <IconList />
      </button>
      <button
        class="flex w-10 justify-center"
        class:bg-white={editor?.isActive({ textAlign: 'left' }) && !viewHTML}
        disabled={viewHTML}
        on:click|preventDefault={() => setTextAlign('left')}
        ><IconAlignLeft />
      </button>
      <button
        class="flex w-10 justify-center"
        class:bg-white={editor?.isActive({ textAlign: 'center' }) && !viewHTML}
        disabled={viewHTML}
        on:click|preventDefault={() => setTextAlign('center')}
        ><IconAlignCenter />
      </button>
      <button
        class="flex w-10 justify-center"
        class:bg-white={editor?.isActive({ textAlign: 'right' }) && !viewHTML}
        disabled={viewHTML}
        on:click|preventDefault={() => setTextAlign('right')}
        ><IconAlignRight />
      </button>
      <button
        class="flex w-10 justify-center"
        class:bg-white={editor?.isActive({ textAlign: 'justify' }) && !viewHTML}
        disabled={viewHTML}
        on:click|preventDefault={() => setTextAlign('justify')}
        ><IconAlignJustify />
      </button>
      <button
        class="flex w-10 items-center justify-center rounded-r-full font-bold"
        class:bg-white={viewHTML}
        on:click|preventDefault={toggleHTML}
        >&lt;/&gt;
      </button>
    </div>
  {/if}

  <textarea
    hidden={!viewHTML}
    class="min-h-40 w-full rounded border border-neutral-200 p-6"
    on:change={handleChange}
    value={elementHTML}
  />
  <div hidden={viewHTML} bind:this={element} />
</div>
