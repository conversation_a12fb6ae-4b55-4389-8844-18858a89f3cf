<script lang="ts">
  import { onMount } from 'svelte';
  import Icon from '../Icons/Icon.svelte';
  import IconInfo from '../Icons/IconInfo.svelte';
  import IconShow from '../Icons/IconShow.svelte';
  import { cn } from '@/utils/classname';
  import type { Action } from 'svelte/action';
  import { createLabel } from '@melt-ui/svelte';
  import {
    inputSizeMap,
    inputIconSizeMap,
    type LimitedComponentSize
  } from '@/components/Controls/size';
  import LabelWrapper from '@/components/Controls/LabelWrapper.svelte';
  import { generateId } from '@/utils/generateId';

  export let id = generateId();
  export let label = '';
  export let tabIndex = 0;
  export let value = '';
  export let placeholder = '';
  export let isDisable = false;
  export let required = false;
  export let maxLength = 1000;
  export let minLength = 0;
  export let sideLabel = false;
  export let type: 'text' | 'password' | 'email' | 'tel' = 'text';
  export let info = '';
  export let size: LimitedComponentSize = 'large';
  /**
   * Only the input with no border/styling (used for tagbox)
   */
  export let inputOnly = false;
  export let showInvalidFeedback = false;

  export let autofocus = false;
  export let onChanged: ((event: Event) => void) | undefined = undefined;
  export let onFocus: ((event: Event) => void) | undefined = undefined;
  export let onBlur: ((event: Event) => void) | undefined = undefined;
  export let onInput: ((event: Event) => void) | undefined = undefined;
  export let onKeyPress: ((event: Event) => void) | undefined = undefined;
  export let actionForInput: Action<HTMLInputElement> = () => {};
  /** Used for when we need to pass in another action */
  export let actionForInput2: Action<HTMLInputElement> = () => {};
  export let border: 'solid' | 'dashed' = 'solid';
  export let font: 'normal' | 'bold' = 'normal';
  /**
   * ONLY use when you you need a smaller label width
   * (useful when it's a side label)
   */
  export let smallLabel = false;

  let focus = false;

  let enableTogglePasswordVisibilty = false;
  let inputRef: HTMLInputElement;

  onMount(() => {
    enableTogglePasswordVisibilty = type == 'password';
  });

  function togglePasswordVisibilty() {
    type = type == 'text' ? 'password' : 'text';
    typeAction(inputRef);
  }

  // https://stackoverflow.com/questions/57392773/error-type-attribute-cannot-be-dynamic-if-input-uses-two-way-binding
  function typeAction(node: HTMLInputElement) {
    node.type = type;
  }

  const {
    elements: { root: labelInternal }
  } = createLabel();
</script>

<LabelWrapper
  {label}
  {required}
  side={sideLabel}
  {smallLabel}
  meltAction={labelInternal}
  class={inputOnly ? 'flex-grow' : ''}
>
  <!-- 
  Comment below removes autofocus warning
  https://github.com/sveltejs/svelte/issues/6629
  -->
  <!-- svelte-ignore a11y-autofocus -->
  <input
    on:focus={() => (focus = true)}
    autofocus={autofocus || undefined}
    {id}
    class={cn(
      `text-regular text-neutral-700`,
      inputSizeMap[size],
      !inputOnly
        ? [
            `peer flex w-full flex-grow flex-row items-center justify-start rounded border border-neutral-200 bg-white focus:border-brand-notification  focus:ring-0`,
            { 'border-dashed': border === 'dashed' },
            font === 'bold' ? 'font-bold' : 'font-normal',
            { 'bg-gray-100': isDisable },
            { ' invalid:border-brand-error': focus }
          ]
        : `border-none bg-transparent px-1 outline-none focus:!ring-0`,
      { 'data-[invalid]:text-brand-error': showInvalidFeedback }
    )}
    use:typeAction
    name={id}
    tabindex={tabIndex}
    disabled={isDisable}
    {placeholder}
    maxlength={maxLength}
    minlength={minLength}
    bind:this={inputRef}
    bind:value
    on:change={onChanged}
    on:focus={onFocus}
    on:blur={onBlur}
    on:input={onInput}
    on:keypress={onKeyPress}
    {required}
    {...$$restProps}
    use:actionForInput
    use:actionForInput2
  />
  {#if enableTogglePasswordVisibilty}
    <button
      class="absolute right-3 {!sideLabel ? 'bottom-3' : ''}  cursor-pointer text-brand-brand"
      on:mousedown={() => togglePasswordVisibilty()}
      on:mouseup={() => togglePasswordVisibilty()}
    >
      {#if type == 'text'}
        <div class="opacity-30">
          <Icon size={inputIconSizeMap[size]} IconComponent={IconShow} />
        </div>
      {:else}
        <Icon size={inputIconSizeMap[size]} IconComponent={IconShow} />
      {/if}
    </button>
  {/if}
  {#if info}
    <div class="mt-2 flex items-center">
      <Icon IconComponent={IconInfo} size={inputIconSizeMap[size]} />
      <span class="ml-2 text-small text-neutral-700">{info}</span>
    </div>
  {/if}
</LabelWrapper>
