export type ComponentSize = 'small' | 'medium' | 'large';
/**
 * For components that only have a 'small'
 * and 'large' size option as per our design
 */
export type LimitedComponentSize = Exclude<ComponentSize, 'medium'>;

export type IconSize =
  | 'i-3'
  | 'i-3.5'
  | 'i-4'
  | 'i-5'
  | 'i-6'
  | 'i-7'
  | 'i-8'
  | 'i-9'
  | 'i-10'
  // Deprecated
  | 'smallest'
  | 'extra-small'
  | 'smaller'
  | 'small'
  | 'medium'
  | 'large';

export const imagesSizeOptions = ['src_50x50', 'src_160x160', 'src'] as const;
export type ImageSize = (typeof imagesSizeOptions)[number];

/**
 * Shared input (Select, Text and Number input) size classes (no medium size)
 *
 * Note that the TagBox component uses a different size map
 * so that it can scale (see TagBoxInternal.svelte)
 */
export const inputSizeMap: Record<LimitedComponentSize, string> = {
  small: 'h-8 px-3',
  // Note that 'small' here is the same as 'medium' button (h-8)
  large: 'h-10 px-3 '
} as const;

/**
 * Shared input (Select, Text and Number input) icon size classes
 */
export const inputIconSizeMap: Record<LimitedComponentSize, IconSize> = {
  small: 'i-4',
  large: 'i-4'
} as const;
