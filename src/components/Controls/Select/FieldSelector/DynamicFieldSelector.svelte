<script lang="ts">
  import { createGroupFieldsQuery } from '@/queries/fields.query';
  import ComboboxInternal from '@/components/Controls/Select/Combobox/ComboboxInternal.svelte';
  import type { FieldEntity } from '@/models/fields/fields';
  import { isValidContract } from '@/models/pricing-contracts/pricing-contract';
  import debounce from 'just-debounce-it';

  export let entity: FieldEntity;
  export let group: string;
  export let value: string | undefined;
  export let title = '';
  export let canAdd = false;
  export let placeholder: string | undefined = undefined;
  export let listOpen: boolean | undefined = undefined;
  export let required = false;
  export let canClear = false;
  export let sideLabel = false;
  export let handleClear = () => {};
  export let excludeValues: string[] = [];
  export let defaultOption: { name: string; value: string } | undefined = undefined;

  /**
   * Clear to this value
   */
  export let clearValue: string | undefined = undefined;
  /**
   * if true, the returned value will be prefixed with `${group}_` - ie: `qty_Cape Town`
   */
  export let addPrefix = false;

  export let selectCallback = (_: string) => {};

  let query = '';
  let debouncedQuery = '';

  const setDebouncedQuery = debounce((q: string) => {
    debouncedQuery = q;
  }, 300);

  $: setDebouncedQuery(query);

  $: fieldsData = createGroupFieldsQuery({
    entity,
    group,
    search: debouncedQuery,
    enabled: !!group //ensure this query on runs on mounted
  });

  $: options =
    $fieldsData.data?.fields
      ?.filter((field) => !isValidContract({ key: field.name }))
      .map((field) => {
        const fieldWithoutPrefix = field.name.replace(`${group}_`, '');
        return {
          name: fieldWithoutPrefix,
          value: fieldWithoutPrefix
        };
      })
      .filter((option) => !excludeValues.includes(option.value)) ?? [];

  $: optionsWithDefault = defaultOption ? [defaultOption, ...options] : options;

  const handleSelect = (newValue: string) => {
    selectCallback(newValue);
    const newPropValue = addPrefix ? `${group}_${newValue}` : newValue;
    if (newPropValue !== value) {
      value = newPropValue;
    }
  };
</script>

<!--
   Ternary below checks if there's a prefix (ie: `qty_`)
   and sets to none prefixed value. Otherwise set undefined if
   we're working with prefixed values (so you dont see meta_ if group === qty_)
   Otherwise set to regular (non-prefixed) value that was passed in
-->
<ComboboxInternal
  {sideLabel}
  options={optionsWithDefault}
  optionsLoading={$fieldsData.isLoading}
  label={title}
  {canAdd}
  {canClear}
  {handleSelect}
  {clearValue}
  bind:searchValue={query}
  value={value?.includes(group) && value?.split('_').length > 1
    ? value.replace(`${group}_`, '')
    : addPrefix
      ? undefined
      : value}
  {listOpen}
  {placeholder}
  {required}
  on:clear={handleClear}
  valueToDisplayName={(value) => {
    return optionsWithDefault.find((option) => option.value === value)?.name ?? value;
  }}
/>
