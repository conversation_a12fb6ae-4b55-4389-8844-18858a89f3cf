<script context="module">
  import FieldSelect from '@/components/Controls/Select/FieldSelector/FieldSelect.svelte';

  export const meta = {
    title: 'components/Controls/Select/FieldSelector/FieldSelect',
    component: FieldSelect,
    parameters: {
      chromatic: { delay: 1000 }
    }
  };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';
  import { productRuleConvertor } from '@/models/fields/fieldConvertors';

  let value: string;
  let dynamicValue = 'meta_bar';
</script>

<Story name="Product Rule Field Select">
  <FieldSelect entity="product" bind:value convertor={productRuleConvertor} />
  <div class="flex flex-col">
    <span>value = {value}</span>
  </div>
</Story>

<Story name="Dynamic Rule Field Select">
  <FieldSelect entity="product" bind:value={dynamicValue} convertor={productRuleConvertor} />
  <div class="flex flex-col">
    <span>value = {dynamicValue}</span>
  </div>
</Story>
