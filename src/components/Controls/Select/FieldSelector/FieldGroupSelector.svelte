<script lang="ts">
  import Select from '@/components/Controls/Select/Select.svelte';
  import type { FieldEntity } from '@/models/fields/fields';
  import { createGroupFieldsQuery } from '@/queries/fields.query';

  export let value: string | undefined = undefined;
  export let label: string | undefined = undefined;
  export let placeholder = 'Select an option';
  export let sideLabel = false;
  export let group: string;
  export let entity: FieldEntity;
  export let optionValuesToFilter: string[] = [];
  export let defaultOption: { label: string; value: string } | undefined = undefined;
  export let includePrefix = false;
  export let required = false;

  $: fieldsData = createGroupFieldsQuery({
    entity,
    group
  });

  $: options =
    $fieldsData.data?.fields
      .map((f) => {
        const fieldWithoutPrefix = f.name.replace(`${group}_`, '');
        return { label: fieldWithoutPrefix, value: includePrefix ? f.name : fieldWithoutPrefix };
      })
      .filter((opt) => !optionValuesToFilter.includes(opt.value)) ?? [];
</script>

{#if !$fieldsData.isLoading}
  <Select
    {required}
    bind:value
    {sideLabel}
    {label}
    {placeholder}
    options={defaultOption ? [defaultOption, ...options] : options}
  />
{/if}
