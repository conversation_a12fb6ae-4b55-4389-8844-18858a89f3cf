<script lang="ts">
  import type { FieldEntity } from '@/models/fields/fields';
  import { createFieldValuesQuery } from '@/queries/fields.query';
  import capitalize from 'lodash.capitalize';
  import ComboboxInternal, {
    type ComboOption
  } from '@/components/Controls/Select/Combobox/ComboboxInternal.svelte';
  import debounce from 'just-debounce-it';
  import { sortArrayString } from '@/utils/sortArrayString';
  import CommaSeparatedInput from '../../Tag/CommaSeparatedInput.svelte';
  import TextInput from '../../TextInput.svelte';
  import { defaultFieldConvertor, type FieldConvertor } from '@/models/fields/fieldConvertors';

  export let entity: FieldEntity;
  /**
   * Use `convertor` prop to pass converter if used with FieldSelect and custom convertor
   */
  export let name: string | undefined;
  export let convertor: FieldConvertor = defaultFieldConvertor;
  $: nameInternal = convertor.valueToField(name ?? '');
  export let title: string = capitalize(name);
  export let value: string | undefined = undefined;
  export let canClear = true;
  /** If comma separated needed */
  export let multiple = false;
  export let disabled = false;
  export let required = false;

  let query = '';
  let debouncedQuery = '';

  const setDebouncedQuery = debounce((q: string) => {
    debouncedQuery = q;
  }, 300);

  $: setDebouncedQuery(query);

  //Don't enable the query by default (this will change when isOpen changes)
  let queryEnabled: boolean = false;

  const searchableProductNames = [
    'source_product_code',
    'collection',
    'title',
    'product_type',
    'vendor',
    'tags'
  ];

  /**
   * only query field values that we define as queryable
   *
   */
  let canQuery: boolean = false;
  $: {
    if (nameInternal) {
      if (entity === 'customer') {
        if (nameInternal.startsWith('meta_')) {
          canQuery = true;
        } else {
          canQuery = false;
        }
      } else if (entity === 'product') {
        if (
          nameInternal.startsWith('meta_') ||
          nameInternal.startsWith('option_') ||
          searchableProductNames.includes(nameInternal)
        ) {
          canQuery = true;
        } else {
          canQuery = false;
        }
      }
    }
  }

  $: fieldValuesQuery = createFieldValuesQuery({
    entity,
    name: nameInternal ?? '', //will be disabled if no name
    search: debouncedQuery,
    enabled: canQuery && queryEnabled
  });

  $: enablePreviousEntry =
    canQuery &&
    queryEnabled &&
    !$fieldValuesQuery.isLoading &&
    !$fieldValuesQuery.data?.find((v) => v === value);

  $: previousEntryQuery = createFieldValuesQuery({
    entity,
    name: nameInternal ?? '',
    search: value ?? '',
    enabled: enablePreviousEntry
  });

  let tagsInternal: string[];

  $: {
    // Bind tags array to comma separated string
    // only do this if we are in multiple mode and can query.
    // If just multiple CommaSeparatedInput will handle the value.
    if (multiple && canQuery) {
      if (!tagsInternal) {
        tagsInternal = value ? value.split(',') : [];
      } else {
        value = sortArrayString(tagsInternal).join();
      }
    }
  }

  let options: ComboOption<string>[];

  $: {
    const queryOpts =
      $fieldValuesQuery.data?.map((value) => ({
        name: value,
        value: value
      })) ?? [];

    // If we previously selected a value from a fieldValuesQuery result
    // that is not in the initial query results, add it to the options
    // so that it can be selected (instead of being displayed as a new value)
    if (enablePreviousEntry) {
      const previousOpt = $previousEntryQuery.data?.find((v) => v === value);
      if (previousOpt) {
        queryOpts.unshift({ name: previousOpt, value: previousOpt });
      }
    }

    options = queryOpts;
  }
</script>

<!-- @component
Lets you select field values with suggested dropdown.
Pass in multiple for tags
 -->
{#if canQuery}
  <ComboboxInternal
    {options}
    optionsLoading={$fieldValuesQuery.isLoading || $previousEntryQuery.isLoading}
    canAdd
    {canClear}
    bind:searchValue={query}
    disableFilter
    clearValue=""
    label={title}
    bind:value
    bind:valueArray={tagsInternal}
    bind:isOpen={queryEnabled}
    {multiple}
    {disabled}
    {required}
  />
{:else if multiple}
  <CommaSeparatedInput bind:value {disabled} />
{:else}
  <TextInput bind:value placeholder="Enter value" isDisable={disabled} {required} />
{/if}
