<script lang="ts">
  import { createSelect, type ComboboxOptionProps } from '@melt-ui/svelte';
  import { createFieldsQuery } from '@/queries/fields.query';
  import { type Writable } from 'svelte/store';
  import { fieldToDisplayName, type Field, type FieldEntity } from '@/models/fields/fields';
  import DynamicFieldSelector from './DynamicFieldSelector.svelte';
  import ListIndicator from '../Shared/ListIndicator.svelte';
  import Clear from '@/components/Controls/Clear.svelte';
  import GroupLabel from '@/components/Controls/Select/GroupLabel.svelte';
  import type { LimitedComponentSize } from '@/components/Controls/size';
  import SelectListItem from '@/components/Controls/Select/Shared/SelectListItem.svelte';
  import SelectList from '@/components/Controls/Select/Shared/SelectList.svelte';
  import LabelWrapper from '@/components/Controls/LabelWrapper.svelte';
  import SelectButton from '@/components/Controls/Select/Shared/SelectButton.svelte';
  import handleClickOutside from '@/utils/actions/handleClickOutside';
  import type { SelectOption } from '@melt-ui/svelte';
  import { shortcut } from '@svelte-put/shortcut';
  import { createEventDispatcher } from 'svelte';

  /**
   * Extended Field type with support for dynamic fields
   */
  type SelectorField = Field & { isDynamic?: boolean; skip?: boolean };

  /**
   * Handle open trigger with external trigger
   */
  export let open: Writable<boolean> | undefined = undefined;
  export let propValue: string | undefined;
  export let entity: FieldEntity;
  export let label: string | undefined = undefined;
  export let placeholder = 'Select an option';
  export let required = false;
  export let size: LimitedComponentSize = 'large';
  export let sideLabel = false;
  export let bold = false;
  export let listHeightPx = 800;
  /**
   * Event that fires when a new value/dynamic field value is selected
   */
  export let onChange: ((name: string) => void) | undefined = undefined;
  /**
   * Whether text should be different colour is value set
   */
  export let colourIfSelected = false;
  export let disabled = false;

  // query used to fetch fields.
  // This is a prop as there maybe be fields that should not be used in the selector.
  // We use query select for this:
  // https://tanstack.com/query/v5/docs/framework/react/reference/useQuery#select
  // TODO A better model might be to provide all the data to this component rather than the query.
  // I did not want to refactor all the usages for this.
  export let fieldsQuery = createFieldsQuery({ params: { entity } });

  let showDynamicFieldSelector = false;

  /**
   * The current dynamic field used for the DeepFieldModal
   * to fetch options by the field name for it's group.
   */
  let currentDynamicField: Field;

  let previousSelected: SelectOption<SelectorField> | undefined;

  let previousPropValue: string | undefined;

  const dispatch = createEventDispatcher();

  // Dropdown builder creation
  const {
    elements: { trigger: dropdownTrigger, menu, option, label: labelInternal },
    states: { selectedLabel, open: openInternal, selected },
    helpers: { isSelected }
  } = createSelect<SelectorField>({
    open,
    forceVisible: true,
    portal: undefined,
    disabled: disabled,
    onSelectedChange: ({ curr, next }) => {
      // To be able to easily access previous selected
      previousSelected = curr;
      previousPropValue = propValue;

      if (!next) {
        propValue = '';
        return next;
      }
      if (onChange && next?.value.name) {
        onChange(next?.value.name);
      }

      if (next?.value?.isDynamic && !next.value.skip) {
        currentDynamicField = next.value;
        showDynamicFieldSelector = true;
        propValue = undefined;
        return next;
      }

      // This allows us to skip over the above logic when
      // we initialise a selected option that is dynamic
      if (next?.value?.skip) {
        delete next.value.skip;
        // we also don't want to set propValue
        // to just the prefix, so return here.
        return next;
      }

      propValue = next?.value.name;

      return next;
    },
    positioning: { placement: 'bottom', sameWidth: true, fitViewport: true }
  });

  $: options = $fieldsQuery.data?.fields ?? [];

  let deepSelection: (Field & { entityName: string | undefined }) | undefined;

  $: existingOption = options.find((opt) => opt.name === propValue);
  $: {
    if (existingOption) {
      // Initialise selected (if NOT a dynamic field group)
      if (!$selected) {
        $selected = toInternalOption(existingOption);
      }
      if (deepSelection) {
        deepSelection = undefined;
      }
    } else if (propValue?.length) {
      const propEntityName = propValue.split('_')[0];
      const prefix = propValue.split('_')[0];
      const dynamicFieldGroup = dynamicFieldGroups.find((entity) => entity.name === propEntityName);
      deepSelection = {
        name: propValue,
        label: propValue.replace(`${prefix}_`, ' '),
        entityName: dynamicFieldGroup?.name
      };
      if (!$selected && dynamicFieldGroup?.isDynamic) {
        // Initialise selected (if a dynamic field group)
        // Note that we use `skip` here to prevent running the logic
        // that is usually triggered when a dynamic field is selected
        $selected = toInternalOption({ ...dynamicFieldGroup, skip: true });
      }
    }
  }

  const handleClear = (e: Event) => {
    // Method to prevent this nested button from firing on the parent button
    // ToDo: find a way to do this without nesting a button (this works great for now)
    e.stopPropagation();
    dispatch('clear');
    clear();
  };

  /**
   * Clear all internal selections
   */
  export const clear = () => {
    deepSelection = undefined;
    selected.set(undefined);
  };

  /**
   * This is for melt Combobox optionElement
   */
  const toInternalOption = (item: SelectorField): ComboboxOptionProps<Field> => ({
    value: item,
    label: fieldToDisplayName(item)
  });

  /**
   * DynamicFieldGroup is parsed to use the the same
   * format as Field to make rendering simpler.
   *
   * We use isDynamic to differentiate it from Field.
   * @todo investigate using typeguards rather
   * (which would result in slightly more verbose code
   *  ie: isField and isFieldGroup)
   */
  let dynamicFieldGroups: SelectorField[];
  $: dynamicFieldGroups =
    $fieldsQuery.data?.dynamic_field_groups.map((fieldGroup) => ({
      name: fieldGroup.group,
      label: fieldGroup.label,
      isDynamic: true
    })) || [];

  let focus = false;
  let selectElement: HTMLSelectElement | undefined;
  let buttonElement: HTMLButtonElement | undefined;

  const handleDynamicFieldSelectorCancel = () => {
    showDynamicFieldSelector = false;
    if (previousSelected?.value.isDynamic) {
      // we need to set this here if dynamic
      // since this is usually set directly by
      // DynamicFieldSelector since it binds propValue
      propValue = previousPropValue;
    }
    $selected = previousSelected
      ? { ...previousSelected, value: { ...previousSelected.value, skip: true } }
      : undefined;
  };
</script>

<!-- @component
Field Selector for filters, import mappings and various other parts of the application.

Renders a modal for dynamicFieldGroup selections (ie: prefixed-fields like option, price, qty and meta (attributes))

@todo consolidate this with the Select component
 -->

<svelte:window
  use:shortcut={{
    trigger: {
      key: 'Escape',
      preventDefault: true,
      enabled: showDynamicFieldSelector,
      callback: handleDynamicFieldSelectorCancel
    }
  }}
/>

<LabelWrapper {label} {required} side={sideLabel} meltAction={labelInternal}>
  <!-- Dynamic Field Selector Combobox (ie: you can type into this to search/add) -->
  {#if showDynamicFieldSelector}
    <div use:handleClickOutside={handleDynamicFieldSelectorCancel} class="w-full">
      <DynamicFieldSelector
        bind:value={propValue}
        {entity}
        group={currentDynamicField.name}
        addPrefix
        selectCallback={() => {
          showDynamicFieldSelector = false;
        }}
        canAdd
        placeholder="Search {currentDynamicField.label} or type to add"
        listOpen
        {required}
      />
    </div>
  {:else}
    <!-- Field Selector Dropdown -->
    <div class="relative w-full flex-col gap-1">
      {#if !open}
        <!-- Select to trigger browser native validation -->
        <select
          bind:this={selectElement}
          on:focus={() => {
            focus = true;
            if (buttonElement) {
              buttonElement.focus();
            }
          }}
          tabindex="-1"
          value={propValue}
          name={label}
          {required}
          class="peer absolute inset-0 -z-max select-none opacity-0"
        >
          {#if propValue}
            <option value={propValue}>{propValue}</option>
          {/if}
        </select>

        <SelectButton
          bind:element={buttonElement}
          {bold}
          title={deepSelection?.label || $selectedLabel || placeholder}
          {disabled}
          {size}
          {dropdownTrigger}
          {focus}
        >
          <div class="flex flex-col items-center justify-center text-regular">
            <div>
              <p class:text-brand-confirmation={colourIfSelected && ($selected || deepSelection)}>
                {deepSelection?.label || $selectedLabel || placeholder}
              </p>
            </div>
          </div>
          {#if !disabled}
            {#if ($selected || deepSelection) && !disabled}
              <div class="absolute right-8 top-1/2 z-10 flex -translate-y-1/2 items-center">
                <Clear onClick={handleClear} />
              </div>
            {/if}
            <ListIndicator open={$openInternal} />
            <div
              class="absolute right-[0px] top-1 h-5/6 w-[54px] overflow-hidden bg-white blur-sm"
            ></div>
          {/if}
        </SelectButton>
      {/if}
      {#if $openInternal}
        <SelectList {listHeightPx} menuElement={menu}>
          <GroupLabel label="Base fields" />
          {#each options as item}
            <SelectListItem
              group
              {toInternalOption}
              option={item}
              displayName={fieldToDisplayName(item)}
              {isSelected}
              optionElement={option}
            />
          {/each}
          {#if dynamicFieldGroups.length}
            <GroupLabel label="Dynamic fields" />
            {#each dynamicFieldGroups as item}
              <SelectListItem
                group
                {toInternalOption}
                option={item}
                displayName={fieldToDisplayName(item)}
                {isSelected}
                labelChip={deepSelection?.entityName === item.name ? deepSelection.label : ''}
                optionElement={option}
              />
            {/each}
          {/if}
        </SelectList>
      {/if}
    </div>
  {/if}
</LabelWrapper>
