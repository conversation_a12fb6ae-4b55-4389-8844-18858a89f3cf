<script context="module">
  import FieldValueSelector from '@/components/Controls/Select/FieldSelector/FieldValueSelector.svelte';
  export const meta = {
    title: 'components/Controls/Select/FieldSelector/FieldValueSelector',
    component: FieldValueSelector
  };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';
</script>

<Story name="Product">
  <FieldValueSelector entity="product" name="vendor" value={''} />
</Story>
<Story name="Product - multiple">
  <FieldValueSelector entity="product" name="tags" value={'Discontinued, New'} multiple />
</Story>
