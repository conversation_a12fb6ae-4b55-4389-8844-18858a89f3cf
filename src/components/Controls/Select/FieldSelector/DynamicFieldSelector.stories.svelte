<script context="module">
  import DynamicFieldSelector from '@/components/Controls/Select/FieldSelector/DynamicFieldSelector.svelte';
  export const meta = {
    title: 'components/Controls/Select/FieldSelector/DynamicFieldSelector.',
    component: DynamicFieldSelector
  };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';
</script>

<Story name="Product - Price Tier">
  <DynamicFieldSelector value="" entity="product" group="price" title="Price Tier" />
</Story>
<Story name="Product - Custom Fields">
  <DynamicFieldSelector value="" entity="product" group="meta" title="Custom Fields" />
</Story>
<Story name="Product - Warehouse">
  <DynamicFieldSelector value="" entity="product" group="qty" title="Warehouse" />
</Story>
<Story name="Product - Option">
  <DynamicFieldSelector value="" entity="product" group="option" title="Option" />
</Story>
