<script lang="ts">
  import { defaultFieldConvertor, type FieldConvertor } from '@/models/fields/fieldConvertors';
  import FieldSelector from '@/components/Controls/Select/FieldSelector/FieldSelector.svelte';
  import { createFieldsQuery } from '@/queries/fields.query';
  import type { Field, FieldEntity } from '@/models/fields/fields';
  import type { LimitedComponentSize } from '@/components/Controls/size';

  // The field used by specific component.
  // e.g. segment field
  export let value: string | undefined;
  export let entity: FieldEntity;
  export let label = '';
  export let placeholder = 'Select an option';
  export let convertor: FieldConvertor = defaultFieldConvertor;
  export let size: LimitedComponentSize = 'large';
  export let required = false;
  /**
   * Use with caution, this will add additional hard-coded fields to the field selector
   */
  export let additionalFields: Field[] = [];
  export const clear = () => {
    fieldSelectorComponent.clear();
  };

  let fieldSelectorComponent: FieldSelector;

  const fieldsQuery = createFieldsQuery({
    params: { entity, converter: convertor.key },
    // remove fields and groups that are set to be ignored by the convertor
    select: (data) => {
      data.fields = [
        ...data.fields.filter((field) => !convertor.ignoreFields.includes(field.name)),
        ...additionalFields
      ];
      data.dynamic_field_groups = data.dynamic_field_groups.filter(
        (group) => !convertor.ignoreGroups.includes(group.group)
      );
      return data;
    }
  });

  // this is the field
  let fieldName: string | undefined;
  $: if (fieldName) {
    value = convertor.fieldToValue(fieldName);
  }

  // convert the field name when mounted
  if (value) {
    fieldName = convertor.valueToField(value);
  }
</script>

<!-- @component
Field Selector to return a field from an entity
Uses a convertor to convert the results.

This component is a simple wrapper that will use
the provided fieldConverter and the binded `value` prop to:
 - convert `value` to a FieldSelector-friendly string (`fieldName`) (for example: no prefixes)
 - listen for changes made by FieldSelector to `fieldName` and convert back appropriately for `value`
 -->
<FieldSelector
  bind:this={fieldSelectorComponent}
  {size}
  bind:propValue={fieldName}
  {entity}
  {label}
  {fieldsQuery}
  {required}
  {placeholder}
  on:clear={() => (value = '')}
/>
