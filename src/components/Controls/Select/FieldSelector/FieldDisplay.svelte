<script lang="ts">
  import SkeletonText from '@/components/Loader/SkeletonText.svelte';
  import { defaultFieldConvertor, type FieldConvertor } from '@/models/fields/fieldConvertors';
  import { fieldToDisplayName } from '@/models/fields/fields';
  import { createFieldsQuery } from '@/queries/fields.query';
  import { writable } from 'svelte/store';

  export let key: string;
  export let converter: FieldConvertor = defaultFieldConvertor;
  export let skeleton = true;

  const fieldsQuery = createFieldsQuery({
    params: { entity: 'product', converter: converter.key }
  });

  const loadingStore = writable($fieldsQuery.isLoading);
  $: loadingStore.set($fieldsQuery.isLoading);

  /**
   * Set skeleton to false when using this
   */
  export const loading = loadingStore;

  $: field = $fieldsQuery.data?.fields.find((f) => f.name === converter.valueToField(key));
</script>

<!-- @component
Simple component that displays the field label from the fields query from the field key.

Exports loading state store for custom skeleton display if needed. 

@todo Support for field group values
 -->
{#if skeleton && $loadingStore}
  <SkeletonText class="h-4" widthsToIgnore={['w-52', 'w-64', 'w-72', 'w-96']} shimmer />
{:else}
  {field ? fieldToDisplayName(field) : key}
{/if}
