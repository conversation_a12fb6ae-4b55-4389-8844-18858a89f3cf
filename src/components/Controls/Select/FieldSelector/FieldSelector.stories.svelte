<script context="module">
  import FieldSelector from '@/components/Controls/Select/FieldSelector/FieldSelector.svelte';
  export const meta = {
    title: 'components/Controls/Select/FieldSelector/FieldSelector',
    component: FieldSelector,
    parameters: {
      chromatic: { delay: 1000 }
    }
  };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';
  let propValue;
  let propValue2 = 'meta_tax_reference';
</script>

<Story name="Customer">
  <FieldSelector bind:propValue entity="customer" />
  {propValue}
</Story>

<Story name="Customer with initial value">
  <FieldSelector bind:propValue={propValue2} entity="customer" />
  {propValue2}
</Story>

<Story name="Product">
  <FieldSelector propValue="" entity="product" />
  Small container:
  <div class="w-44">
    <FieldSelector propValue="" entity="product" />
  </div>
</Story>
