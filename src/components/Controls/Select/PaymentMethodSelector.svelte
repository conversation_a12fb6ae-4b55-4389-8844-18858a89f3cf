<script lang="ts">
  import Select from '@/components/Controls/Select/Select.svelte';
  import {
    PAYMENT_METHOD_OPTIONS,
    type PaymentMethodOption
  } from '@/models/config/channels/trade/payment/methods';

  export let method: PaymentMethodOption | undefined = undefined;
  export let placeholder = 'Select payment method';
</script>

<Select
  bind:value={method}
  {placeholder}
  options={Object.entries(PAYMENT_METHOD_OPTIONS).map(([value, label]) => ({ value, label }))}
/>
