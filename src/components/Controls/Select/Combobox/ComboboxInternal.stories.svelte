<script context="module">
  import ComboboxInternal from '@/components/Controls/Select/Combobox/ComboboxInternal.svelte';

  export const meta = {
    title: 'components/Controls/Select/Combobox/ComboboxInternal',
    component: ComboboxInternal,
    parameters: {
      chromatic: { delay: 1000 }
    }
  };
</script>

<script lang="ts">
  import { Story, Template } from '@storybook/addon-svelte-csf';
  const options = [...new Array(30)].map((_, i) => ({
    value: `Option ${i + 1}`,
    name: `Option ${i + 1}`
  }));

  let valueArray = ['Option 1', 'Option 11'];
  let clearable = 'Option 7';
</script>

<!-- Note: This is the story for a `Internal` or builder component
   - the examples here can help you to building simple wrappers on
  top of the component -->

<Story name="Empty">
  <ComboboxInternal options={[]} canAdd value="" />
</Story>

<Story name="Value set">
  <ComboboxInternal {options} canAdd value="Option 7" />
</Story>

<Story name="Small: Value set">
  <ComboboxInternal size="small" {options} canAdd value="Option 7" />
</Story>

<Story name="Value set - with label">
  <ComboboxInternal title="This is the label" {options} canAdd value="Option 7" />
</Story>

<Story name="Value set with chevron">
  <ComboboxInternal {options} showChevron canAdd value="Option 7" />
</Story>

<Story name="Can clear">
  <ComboboxInternal {options} canAdd canClear clearValue="" bind:value={clearable} />
</Story>

<Story name="Can clear and chevron">
  <ComboboxInternal
    {options}
    canAdd
    canClear
    showChevron
    clearValue=""
    value={'Really really long value to test clipping'}
  />
</Story>

<Story name="Always open">
  <ComboboxInternal {options} listOpen canAdd value="Option 7" />
</Story>

<Story name="New value">
  <ComboboxInternal {options} canAdd value="New option" />
</Story>

<Story name="Long text">
  <div class="w-32">
    <ComboboxInternal showChevron {options} canAdd value="Really really really long text" />
  </div>
</Story>

<Story name="Options loading">
  <ComboboxInternal
    {options}
    optionsLoading
    canAdd
    value="You can add me as a new value while existing values load"
  />
</Story>

<Story name="Custom list height">
  <ComboboxInternal {options} listHeightPx={120} canAdd value="Option 4" />
</Story>

<Story name="Multiple">
  <ComboboxInternal multiple {options} bind:valueArray />
</Story>

<Story name="Multiple - Small">
  <ComboboxInternal size="small" multiple {options} bind:valueArray />
</Story>

<Story name="Multiple - Can add">
  <ComboboxInternal multiple canAdd {options} bind:valueArray />
</Story>

<Story name="Multiple - No dropdown">
  <p class="sm">This is what the TagsInput wrapper component is:</p>
  <ComboboxInternal multiple canAdd bind:valueArray listOpen={false} />
</Story>

<Story name="Label">
  <ComboboxInternal label="This is a label" multiple canAdd {options} bind:valueArray />
</Story>

<Story name="Side label ">
  <ComboboxInternal
    sideLabel
    label="This is a side label"
    canAdd
    multiple
    {options}
    bind:valueArray
  />
</Story>
