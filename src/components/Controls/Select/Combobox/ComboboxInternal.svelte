<script lang="ts" context="module">
  export type ComboOption<T> = {
    /**
     * If not string, set valueToDisplayName and valueToName to convert value to a string
     */
    value: T;
    /**
     * Serves as the key/reference for the selection,
     * also used to diff against the $inputValue string
     */
    name: string;
    disabled?: boolean;
  };
</script>

<script lang="ts" generics="T">
  import type { LimitedComponentSize } from '@/components/Controls/size';

  import SelectList from '@/components/Controls/Select/Shared/SelectList.svelte';
  import Clear from '@/components/Controls/Clear.svelte';
  import LabelWrapper from '@/components/Controls/LabelWrapper.svelte';
  import SelectListItem from '@/components/Controls/Select/Shared/SelectListItem.svelte';
  import SelectListItemNoResults from '@/components/Controls/Select/Shared/SelectListItemNoResults.svelte';
  import { createCombobox, type ComboboxOptionProps } from '@melt-ui/svelte';
  import { createEventDispatcher, onMount } from 'svelte';
  import { writable } from 'svelte/store';
  import SkeletonText from '../../../Loader/SkeletonText.svelte';
  import ListIndicator from '../Shared/ListIndicator.svelte';
  import TextInput from '../../TextInput.svelte';
  import TagboxInternal from './TagboxInternal.svelte';

  export let options: ComboOption<T>[] = [];

  export let size: LimitedComponentSize = 'large';
  /**
   * Force the list to always be open if `true` or never show if `false`. Leave `undefined` for default behaviour.
   */
  export let listOpen: boolean | undefined = undefined;
  /**
   * Don't filter results.
   * Used for when we want to
   * handle filtering in API request
   * */
  export let disableFilter = false;
  /**
   * Use this to expose (bind) the
   * inputValue for searching the API
   * Will only be set on touchedInput
   */
  export let searchValue: string | undefined = undefined;
  $: searchValue = $touchedInput ? $inputValue : '';
  /**
   * Use this to expose (bind) the
   * openState for external reverence
   * (ie: to only enable a query when open)
   */
  export let isOpen: boolean | undefined = undefined;
  $: isOpen = $openStore ?? false;

  export let optionsLoading = false;
  /**
   * Allow the user to add new value,
   * the user can add new value while optionsLoading
   */
  export let canAdd = false;
  /**
   * Ability to set value to `clearValue`
   */
  export let canClear = false;
  /**
   * Clear to this value
   */
  export let clearValue: T | undefined = undefined;
  /**
   * Set to true to indicate if list is open
   */
  export let showChevron = false;
  export let label: string | undefined = undefined;
  export let sideLabel = false;
  export let required = false;
  /**
   * Function that converts the new input string
   * the correct type of value (T)
   * Useful for adding new values that can't just be strings (ie: when `T` isn't `string`)
   */
  export let inputParser: (s: string) => T = (s: string) => s as T;

  /**
   * The default height of the list,
   * ideally should be *50 to show indicate clipped values
   *
   * @default 350 (px)
   */
  export let listHeightPx = 350;
  /**
   * Bind this if you want instant updates, can be any value of type T
   * Will also be used to set initial value
   */
  export let value: T | undefined = undefined;
  /**
   * If true, use valueArray instead of value
   */
  export let multiple: boolean = false;
  /**
   * used for multiple
   */
  export let valueArray: T[] | undefined = undefined;
  /**
   * For new values: Converts value of type T to `name` which serves as the key/reference
   *
   * Should match ComboOptions's name field
   */
  export let valueToName: (value: T) => string = (v) => v as string;
  /**
   * Use this util when you want a custom display value derived from ComboOption
   */
  export let valueToDisplayName: (value: T) => string = (value: T) => value as string;
  export let customFilter: ((value: ComboOption<T>) => ComboOption<T> | boolean) | undefined =
    undefined;
  export let placeholder = multiple ? 'Enter tags...' : 'Select or add new';
  /**
   * Handler for selected
   */
  export let handleSelect: ((value: T) => void) | undefined = undefined;

  /**
   * Disable this input
   */
  export let disabled = false;

  /**
   * This is for melt Combobox optionElement
   */
  const toInternalOption = (option: ComboOption<T>): ComboboxOptionProps<ComboOption<T>> => ({
    value: option,
    label: valueToDisplayName(option.value),
    disabled: option.disabled
  });

  const openStore = writable<boolean>(listOpen);

  const {
    elements: { menu, input, option: optionElement, label: labelInternal },
    states: { inputValue, touchedInput, selected, highlightedItem },
    helpers: { isSelected }
  } = createCombobox<ComboOption<T>>({
    forceVisible: listOpen,
    portal: null,
    open: openStore,
    preventScroll: true,
    loop: true,
    disabled: disabled,
    onOpenChange: ({ curr, next }) => (typeof listOpen === 'boolean' ? listOpen ?? false : next),
    onSelectedChange: ({ curr, next }) => {
      if (next) {
        if (multiple && valueArray) {
          // Check that we don't add the new value to the array more than once
          if (!valueArray.find((value) => valueToName(value) === valueToName(next.value.value))) {
            valueArray = [...valueArray, next.value.value];
          }
          // Input is removed since we're adding the value to
          // the tags instead of the regular input value
          $inputValue = '';
          return undefined;
        } else {
          value = next.value.value;
        }
        if (handleSelect) {
          handleSelect(next.value.value);
        }
      }

      return next;
    },
    positioning: { placement: 'bottom', sameWidth: true, fitViewport: true }
  });

  $: {
    // Set default selected (only for none multiple value, otherwise tagbox will handle)
    if (!multiple && JSON.stringify($selected?.value.value) !== JSON.stringify(value)) {
      $selected =
        typeof value !== 'undefined'
          ? {
              value: { name: valueToName(value), value },
              label: valueToDisplayName(value)
            }
          : undefined;
    }
  }

  $: optionsWithoutSelectedItems = multiple
    ? options.filter((opt) => !valueArray?.includes(opt.value))
    : options;
  $: filteredOptions =
    $touchedInput && !disableFilter
      ? optionsWithoutSelectedItems.filter((option) => {
          if (customFilter) {
            return customFilter(option);
          }
          const searchValue = $inputValue.toLowerCase();
          return (
            option.name?.toLowerCase().includes(searchValue) ||
            valueToDisplayName(option.value)?.toLowerCase().includes(searchValue)
          );
        })
      : optionsWithoutSelectedItems;

  let newOption: ComboOption;
  $: newOption = {
    name: $inputValue,
    value: inputParser($inputValue)
  };

  // Set the initial input value if one exists
  onMount(() => {
    $inputValue = value && !multiple ? valueToDisplayName(value) : '';
  });

  $: {
    // Set the input value to the correct selected value on close
    // (close is triggered on value selection, enter and escape
    // as per the above createCombobox config)
    if (!$openStore) {
      $inputValue = !multiple && $selected?.label ? $selected?.label : '';
    }
  }

  let newListItem: HTMLElement;
  let listElement: HTMLElement;

  $: {
    // Set the highlighted <li> to that of newOption, should it exist
    // Allows the user to keypress `enter` and to set the new
    // value since it would be selected
    if (newListItem) {
      highlightedItem.set(newListItem);
    } // Set the highlighted <li> to that of the only option in the the list
    // this will be the case when the existing is entirely typed out by the user
    else if (filteredOptions.length === 1) {
      const firstLiElement = listElement?.querySelector('li:first-child') as HTMLElement;
      highlightedItem.set(firstLiElement);
    } else {
      highlightedItem.set(null);
    }
  }
  const dispatch = createEventDispatcher();
  const handleClear = () => {
    if (multiple) {
      valueArray = [];
    } else {
      $inputValue = '';
      value = clearValue;
      $selected = undefined;
    }
    dispatch('clear');
  };

  // Render the chevron when listOpen us set default behaviour
  $: displayChevron = typeof listOpen === 'undefined' && showChevron;
</script>

<!-- @component

Base component for creating typeahead combobox dropdown-like input with tags-style functionality for multiples.

You should only be using this directly when building wrapper components like CommaSeperatedInput, FieldValueSelector or DynamicFieldSelector

Can change value by either:
 1. binding the `value` prop (useful when you want to bind an entire value (T) or just a string (T is a string))
 2. passing the new value to a `handleSelect` function (useful when you want to pass in a whole value (T) but bind a string (ie: a property on T))

 Can change `valueArray` only by:
 1. binding the `valueArray` prop
 -->
<LabelWrapper {label} {required} side={sideLabel} meltAction={labelInternal}>
  <div class="flex w-full flex-col">
    <div class="relative">
      {#if multiple}
        {#if Array.isArray(valueArray)}
          <TagboxInternal
            {canAdd}
            comboboxInput={input}
            bind:valueArray
            {valueToName}
            options={options.map((opt) => opt.value)}
            {inputParser}
            {placeholder}
            disableChanges={!(typeof listOpen === 'boolean' && listOpen === false)}
            {size}
            addOnComma={typeof listOpen === 'boolean' && listOpen === false}
          />

          <!--  Only add new values using TagboxInternal if listOpen is false (ie: for when we just want a simple tagsbox) -->
        {/if}
      {:else}
        <TextInput
          isDisable={disabled}
          {...$input}
          type="text"
          actionForInput={input}
          {placeholder}
          {required}
          {size}
        />
      {/if}
      {#if canClear && value}
        <div
          class="absolute {displayChevron
            ? 'right-8'
            : 'right-3'} top-1/2 z-10 flex -translate-y-1/2 items-center"
        >
          <Clear onClick={handleClear} />
        </div>
      {/if}
      {#if displayChevron}
        <ListIndicator open={$openStore} />
      {/if}
      {#if (canClear && value) || displayChevron}
        <div
          class="absolute right-[0px] top-1 h-5/6 overflow-hidden bg-white blur-sm {displayChevron &&
          canClear &&
          value
            ? 'w-14'
            : 'w-8'}"
        ></div>
      {/if}
    </div>
  </div>
</LabelWrapper>
{#if $openStore}
  <SelectList bind:element={listElement} menuElement={menu} {listHeightPx}>
    {#if canAdd ? $inputValue.length && !options.find((option) => option.name.toLowerCase() === $inputValue.toLowerCase()) : false}
      <SelectListItem
        bind:element={newListItem}
        {toInternalOption}
        option={newOption}
        isNew
        displayName={valueToDisplayName(newOption.value)}
        {isSelected}
        {optionElement}
      />
    {:else if !filteredOptions.length && !optionsLoading}
      <SelectListItemNoResults />
    {/if}
    {#if optionsLoading}
      {#each new Array(4) as _, i (i)}
        <div class="py-1">
          <SkeletonText class="h-7 w-full" bgsToIgnore={['bg-gray-500', 'bg-gray-600']} shimmer />
        </div>
      {/each}
    {:else}
      {#each filteredOptions as option, index (index)}
        <SelectListItem
          {toInternalOption}
          {option}
          {optionElement}
          displayName={valueToDisplayName(option.value)}
          {isSelected}
        />
      {/each}
    {/if}
  </SelectList>
{/if}
