<script lang="ts" , generics="T">
  import { type ComponentSize, type LimitedComponentSize } from '@/components/Controls/size';
  import TextInput from '../../TextInput.svelte';
  import { createCombobox, createTagsInput, type Tag } from '@melt-ui/svelte';
  import { writable } from 'svelte/store';
  import Chip from '../../Tag/Chip.svelte';
  import { cn } from '@/utils/classname';

  const {
    elements: { input: comboboxInputInternal }
  } = createCombobox();

  /**
   * Generic will be an array here
   */
  export let valueArray: T[];
  export let options: T[];
  export let comboboxInput = comboboxInputInternal;
  /**
   * Converts value of type T to `name` which serves as the key/reference
   */
  export let valueToName: (value: T) => string;
  export let canAdd: boolean;
  export let disabled = false;
  /**
   * Function that converts the new input string
   * the correct type of value (T)
   */
  export let inputParser: (s: string) => T;
  export let placeholder: string;
  /**
   * Add a value when the user types a comma.
   * Useful for when we dont show dropdown options (TagsInput)
   */
  export let addOnComma = false;
  /**
   * Set this to handle changes in parent,
   * this should be true when we dont want
   * combobox to handle mutations to the array.
   *
   */
  export let disableChanges = false;
  export let size: LimitedComponentSize = 'large';

  export const tagboxSizeMap: Record<Exclude<ComponentSize, 'medium'>, string> = {
    small: 'min-h-8 ',
    // Note that 'small' here is the same as 'medium' button (h-8)
    large: 'min-h-10  '
  } as const;

  const parseTags = (valuesToParse: T[]): Tag[] =>
    valuesToParse.map((v) => {
      const stringValue = valueToName(v);
      // we return an id and value here
      // to conform to melt's Tag type
      return {
        id: stringValue,
        value: stringValue
      };
    });

  /**
   *  Tags state is controlled internally from this store
   */
  const tags = writable(parseTags(valueArray));
  $: tags.set(parseTags(valueArray));

  const {
    elements: { root, input, tag, deleteTrigger, edit },
    helpers: { addTag }
  } = createTagsInput({
    tags: tags,

    onTagsChange: ({ curr, next }) => {
      // Use tag id to get original T values
      valueArray = next.map((tag) => {
        // find the original T value from valueToName
        const value = valueArray.find((v) => valueToName(v) === tag.value);
        if (value) {
          // return existing value
          return value;
        } else {
          // return new value
          return inputParser(tag.value);
        }
      });

      return next;
    },
    editable: false,
    unique: true,
    add: (tag) => {
      if (disableChanges) {
        throw new Error();
      }
      if (!canAdd && !options.find((opt) => valueToName(opt) === tag)) {
        throw new Error();
      }
      return { id: tag, value: tag };
    }
  });

  let inputValue: string;
  /**
   * Trigger an 'add' when the user presses the comma key
   */
  $: {
    if (addOnComma && inputValue?.slice(-1) === ',') {
      addTag(inputValue.slice(0, -1));
      inputValue = '';
    }
  }
</script>

<!-- 
  @component internal text-input style component used for `multiple` in Combobox. 
  Used by ComboboxInternal
  DO NOT use this directly
-->

<div class="flex w-full flex-col items-start justify-center">
  <div
    {...$root}
    use:root
    class={cn(
      `peer flex w-full flex-grow flex-wrap items-center justify-start rounded 
      border border-neutral-200 bg-white px-3 align-middle text-regular
       text-neutral-700 focus:border-brand-notification focus:ring-0`,
      { 'bg-gray-100': disabled },
      { 'invalid:border-brand-error': focus },
      'gap-x-3'
    )}
  >
    {#each $tags as t (t)}
      <div class={cn(tagboxSizeMap[size], ' flex items-center')}>
        <Chip
          variant="tag"
          {size}
          type="info"
          meltTag={t}
          tagElement={tag}
          {deleteTrigger}
          {edit}
          {disableChanges}
        />
      </div>
    {/each}

    <TextInput
      bind:value={inputValue}
      {...$comboboxInput}
      {...$input}
      actionForInput={input}
      actionForInput2={comboboxInput}
      type="text"
      {placeholder}
      inputOnly
      showInvalidFeedback
      {size}
    />
  </div>
</div>
