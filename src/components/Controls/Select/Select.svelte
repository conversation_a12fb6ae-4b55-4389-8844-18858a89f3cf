<script lang="ts" context="module">
  export type ListOption = {
    value: unknown;
    label: string;
    disabled?: boolean;
  };
</script>

<script lang="ts" generics="T extends string | number | null | undefined">
  import type { LimitedComponentSize } from '@/components/Controls/size';

  import ListIndicator from './Shared/ListIndicator.svelte';
  import { createSelect, melt, type SelectOptionProps } from '@melt-ui/svelte';
  import SkeletonText from '../../Loader/SkeletonText.svelte';
  import GroupLabel from '@/components/Controls/Select/GroupLabel.svelte';
  import SelectListItem from '@/components/Controls/Select/Shared/SelectListItem.svelte';
  import SelectList from '@/components/Controls/Select/Shared/SelectList.svelte';
  import SelectListItemNoResults from '@/components/Controls/Select/Shared/SelectListItemNoResults.svelte';
  import LabelWrapper from '@/components/Controls/LabelWrapper.svelte';
  import SelectButton from '@/components/Controls/Select/Shared/SelectButton.svelte';

  type GroupedListOptions = Record<string, ListOption[]>;

  const isGrouped = (options: unknown): options is GroupedListOptions => {
    return typeof options === 'object' && !Array.isArray(options);
  };

  const flattenGroup = (options: GroupedListOptions): ListOption[] => Object.values(options).flat();

  /**
   * Bind this.
   */
  export let value: T;

  /**
   * Options, which can be a list of options or a grouped list of options
   */
  export let options: ListOption[] | GroupedListOptions;

  export let label = '';
  export let disabled = false;
  export let size: LimitedComponentSize = 'large';
  export let bold = false;
  export let optionsLoading = false;
  export let sideLabel = false;
  export let placeholder = 'Select an option';
  export let required = false;
  export let optionToDisplayName: (option: ListOption) => string = (option: ListOption) =>
    (option?.label as string) ?? (option.value as string);
  export let onSelectedChange = (_curr: T, _next: T) => {};
  /**
   * Use this to expose (bind) the
   * openState for external reverence
   * (ie: to only enable a query when open)
   */
  export let isOpen: boolean | undefined = undefined;
  $: isOpen = $openInternal ?? false;

  /**
   * The default height of the list,
   * ideally should be end in 50 (ie: 450, 550, etc) to show indicate clipped values
   *
   * @default 350 (px)
   */
  export let listHeightPx = 350;

  const toInternalOption = (option: ListOption): SelectOptionProps<ListOption> => ({
    value: option,
    label: optionToDisplayName(option),
    disabled: option.disabled
  });

  const {
    elements: {
      trigger: dropdownTrigger,
      menu: menuElement,
      option: optionElement,
      group,
      groupLabel,
      label: labelInternal
    },
    states: { selectedLabel, open: openInternal, selected },
    helpers: { isSelected }
  } = createSelect<ListOption>({
    forceVisible: true,
    positioning: {
      placement: 'bottom',
      fitViewport: true,
      sameWidth: true,
      gutter: size === 'large' ? 5 : 3
    },
    disabled,
    onSelectedChange: ({ curr, next }) => {
      if (next?.value) {
        value = next.value.value as T;
      }
      onSelectedChange(curr?.value?.value as T, next?.value?.value as T);
      return next;
    }
  });

  $: {
    if ($selected?.value.value !== value) {
      $selected =
        typeof value !== 'undefined'
          ? toInternalOption({
              value,
              label: (isGrouped(options) ? flattenGroup(options) : options).find(
                (o) => o.value === value
              )?.label as string
            })
          : undefined;
    }
  }
  let focus = false;
  let selectElement: HTMLSelectElement | undefined;
  let buttonElement: HTMLButtonElement | undefined;
</script>

<!--
@component
  Select component: just a regular select-like dropdown...
  for when you don't need any typeahead Combobox magic

  Use by binding value.
  -->

<LabelWrapper {label} {required} side={sideLabel} meltAction={labelInternal}>
  <!-- Select to trigger browser native validation, TODO: use melt's hiddenInput? -->
  <select
    bind:this={selectElement}
    on:focus={() => {
      focus = true;
      if (buttonElement) buttonElement.focus();
    }}
    tabindex="-1"
    {value}
    name={label}
    {required}
    class="peer absolute inset-0 -z-max select-none opacity-0"
  >
    {#each isGrouped(options) ? flattenGroup(options) : options as option}
      <option value={option.value} disabled={option.disabled}>
        {option.label}
      </option>
    {/each}
  </select>

  <SelectButton
    bind:element={buttonElement}
    {bold}
    title={$selectedLabel || placeholder}
    {disabled}
    {size}
    {dropdownTrigger}
    {focus}
  >
    <p class="truncate">{$selectedLabel || placeholder}</p>
    {#if !disabled}
      <ListIndicator open={$openInternal} />
      <div
        class="absolute right-[0px] top-1 z-[2] h-5/6 w-8 overflow-hidden bg-white blur-sm"
      ></div>
    {/if}
  </SelectButton>

  {#if $openInternal}
    <SelectList {menuElement} {listHeightPx}>
      {#if optionsLoading}
        {#each new Array(4) as _, i (i)}
          <div class="py-1">
            <SkeletonText class="h-8 w-full" bgsToIgnore={['bg-gray-500', 'bg-gray-600']} shimmer />
          </div>
        {/each}
      {:else if (isGrouped(options) ? flattenGroup(options) : options).length}
        {#each Object.entries(isGrouped(options) ? options : { 'Single Group': options }) as [groupName, groupOptions]}
          <div use:melt={$group(groupName)}>
            {#if isGrouped(options)}
              <GroupLabel label={groupName} {groupLabel} />
            {/if}
            {#each groupOptions as option, index (index)}
              <SelectListItem
                group={isGrouped(options)}
                {toInternalOption}
                {option}
                {optionElement}
                displayName={optionToDisplayName(option)}
                {isSelected}
              />
            {/each}
          </div>
        {/each}
      {:else}
        <SelectListItemNoResults />
      {/if}
    </SelectList>
  {/if}
</LabelWrapper>
