<script context="module">
  export const operatorMap = {
    'less than': 'is less than',
    equal: 'equals',
    'not contains': 'does not contain',
    contains: 'contains',
    'greater than': 'is greater than',
    lookup: 'matches either',
    'is not empty': 'is not empty',
    'not equal': 'not equal'
  };
</script>

<script lang="ts">
  import Select from '@/components/Controls/Select/Select.svelte';

  export let disabled = false;
  export let value: string | undefined = undefined;

  const operators: { label: string; value: keyof typeof operatorMap }[] = [
    { label: 'Equal', value: 'equal' },
    { label: 'Contains', value: 'contains' },
    { label: 'Does Not Contain', value: 'not contains' },
    { label: 'Greater Than', value: 'greater than' },
    { label: 'Less Than', value: 'less than' },
    { label: 'Lookup (list of values)', value: 'lookup' },
    { label: 'Is not empty', value: 'is not empty' },
    { label: 'Does not equal', value: 'not equal' }
  ];
  export let required = false;
</script>

<Select options={operators} bind:value {disabled} {required} placeholder="Select operator" />
