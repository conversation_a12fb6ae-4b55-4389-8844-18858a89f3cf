<script lang="ts">
  import Select from '@/components/Controls/Select/Select.svelte';
  import { ES_WEIGHTINGS, esWeightingToDisplay } from '@/lib/es/utils';

  export let weight: number | undefined = undefined;
  export let placeholder = 'Select weighting';
</script>

<Select
  bind:value={weight}
  {placeholder}
  options={ES_WEIGHTINGS.map((w) => ({ value: w, label: esWeightingToDisplay(w) }))}
/>
