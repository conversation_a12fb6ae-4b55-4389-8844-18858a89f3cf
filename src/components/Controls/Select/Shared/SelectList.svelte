<script lang="ts">
  import { createSelect, melt } from '@melt-ui/svelte';
  import { fly } from 'svelte/transition';

  const {
    elements: { menu }
  } = createSelect();

  export let menuElement = menu;
  export let listHeightPx = 350;
  export let element: HTMLElement | undefined = undefined;
</script>

<ul
  bind:this={element}
  class="z-50 flex flex-col rounded bg-white p-1 shadow outline-none"
  use:melt={$menuElement}
  transition:fly={{ y: -20, duration: 150 }}
>
  <!-- svelte-ignore a11y-no-noninteractive-tabindex -->
  <div
    class="flex max-h-full flex-col gap-0 overflow-y-auto bg-white focus:outline-none"
    tabindex="0"
    style="max-height: {listHeightPx}px;"
  >
    <slot />
  </div>
</ul>
