<script lang="ts">
  import {
    inputSizeMap,
    type ComponentSize,
    type LimitedComponentSize
  } from '@/components/Controls/size';
  import { cn } from '@/utils/classname';
  import { createSelect, melt } from '@melt-ui/svelte';

  const {
    elements: { trigger: dropdownTriggerInternal }
  } = createSelect();

  export let element: HTMLElement | undefined = undefined;
  export let bold: boolean;
  export let disabled: boolean;
  export let title: string;
  export let size: LimitedComponentSize = 'large';
  export let onFocus = (event: FocusEvent) => {};
  export let dropdownTrigger = dropdownTriggerInternal;
  export let focus: boolean;
</script>

<button
  bind:this={element}
  on:focus={onFocus}
  {title}
  class={cn(
    `relative flex w-full items-center justify-between truncate rounded  
  border border-neutral-200 bg-white px-3 py-2 ring-0 transition-opacity
@container hover:opacity-90 focus:border-brand-notification focus:ring-0`,
    { 'bg-gray-100': disabled },
    { 'font-bold': bold },
    inputSizeMap[size],
    { ' peer-[:invalid]:border-brand-error': focus }
  )}
  use:melt={$dropdownTrigger}
>
  <slot />
</button>
