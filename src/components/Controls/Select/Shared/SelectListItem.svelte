<script lang="ts" generics="T">
  import Chip from '@/components/Controls/Tag/Chip.svelte';

  import Icon from '@/components/Icons/Icon.svelte';
  import IconCheck from '@/components/Icons/IconCheck.svelte';
  import { cn } from '@/utils/classname';
  import { createSelect, melt, type SelectOptionProps } from '@melt-ui/svelte';

  const {
    elements: { option: optionElementInternal },
    helpers: { isSelected: isSelectedInternal }
  } = createSelect();

  export let group = false;
  export let toInternalOption: (option: T) => SelectOptionProps<T>;
  export let option: T;
  export let optionElement = optionElementInternal;
  export let displayName: string;
  export let isSelected = isSelectedInternal;
  export let isNew = false;
  /**
   * Used for additional info in a chip
   */
  export let labelChip = '';
  export let element: HTMLElement | undefined = undefined;
</script>

<li
  bind:this={element}
  use:melt={$optionElement(toInternalOption(option))}
  class={cn(
    `relative flex cursor-pointer scroll-my-2 flex-row py-2 pr-4
     hover:bg-brand-notification/10 focus:z-10
      data-[highlighted]:bg-brand-notification/10 data-[selected]:bg-brand-notification/10
       data-[disabled]:opacity-50`,
    { 'bg-brand-notification/10': $isSelected(option) },
    group ? 'pl-8' : 'pl-3'
  )}
>
  {#if $isSelected(option)}
    <div class="absolute left-2 z-10 self-center">
      <Icon IconComponent={IconCheck} size="i-4" />
    </div>
  {/if}
  <div class={cn('flex flex-wrap gap-x-2', $isSelected(option) && !group ? 'pl-4' : '')}>
    <span title={displayName} class="text-regular text-neutral-700">{displayName}</span>
    {#if labelChip}
      <div class="self-center">
        <Chip label={labelChip} size="small" type="default" />
      </div>
    {/if}
    {#if isNew}
      <div class="self-center">
        <Chip label="new" size="small" type="info" />
      </div>
    {/if}
  </div>
</li>
