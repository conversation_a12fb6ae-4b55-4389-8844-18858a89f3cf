<script lang="ts">
  import { createSelect, melt } from '@melt-ui/svelte';

  const {
    elements: { groupLabel: groupLabelInternal }
  } = createSelect();

  export let label: string;
  export let groupLabel = groupLabelInternal;
</script>

<div
  class="cursor-pointer bg-white px-4 py-3 text-smaller font-bold uppercase leading-4 tracking-widest text-neutral-700"
  use:melt={$groupLabel(label)}
>
  {label}
</div>
