<script context="module">
  import Select from '@/components/Controls/Select/Select.svelte';

  export const meta = {
    title: 'components/Controls/Select/Select',
    component: Select
  };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';

  let selectedValue = 'Option 2';
  const options = [...new Array(30)].map((_, i) => ({
    value: `Option ${i + 1}`,
    label: `Option ${i + 1}`,
    disabled: i % 4 === 0
  }));

  const groupedOptions = {
    Group1: options.slice(0, 10),
    Group2: options.slice(10, 20),
    Group3: options.slice(20, 30)
  };

  let selectedNumber = 1234;
  const numbersObjectArray = [
    { value: 1234, disabled: false, label: `Option 0` },
    ...[...new Array(30)].map((_, i) => ({
      value: Math.random() * 10,
      disabled: i % 4 === 0,
      label: `Option ${i + 1}`
    }))
  ];

  let emptySelected: string;
  const handler = (val: string) => {
    console.log('handled', val, selectedValue);
  };
</script>

<Story name="With no value selected">
  <Select value={undefined} {options} />
</Story>

<Story name="With value selected">
  <Select {options} bind:value={selectedValue} />
</Story>

<Story name="With groups">
  <Select options={groupedOptions} bind:value={selectedValue} />
</Story>

<Story name="With value selected - small">
  <Select {options} bind:value={selectedValue} size="small" />
</Story>

<Story name="With value selected - medium">
  <Select {options} bind:value={selectedValue} size="medium" />
</Story>

<Story name="Value with different to display name but not object">
  <!-- This is for when value is a string but value !== UI value -->

  <Select options={numbersObjectArray} bind:value={selectedNumber} />

  Selected hidden value: {selectedNumber}
</Story>

<Story name="With label">
  <Select label="This is a title" {options} bind:value={selectedValue} />
</Story>

<Story name="With side label">
  <Select label="This is a title" sideLabel {options} bind:value={selectedValue} />
</Story>

<Story name="With custom placeholder">
  <Select placeholder={'Custom placeholder goes here'} {options} value={undefined} />
</Story>

<Story name="With long text">
  <Select {options} value={'Really really long text test'} />
</Story>

<Story name="Loading">
  <Select {options} bind:value={selectedValue} optionsLoading />
</Story>

<Story name="Disabled">
  <Select disabled {options} bind:value={selectedValue} />
</Story>
