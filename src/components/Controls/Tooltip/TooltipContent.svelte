<script lang="ts">
  import { createTooltip, melt } from '@melt-ui/svelte';
  import { fade as fader } from 'svelte/transition';

  // At the expense of initialising an unused tooltip builder,
  // this allows for easy type inference
  const {
    elements: { content: contentInternal, arrow: arrowInternal },
    states: { open: openInternal }
  } = createTooltip();

  export let content = contentInternal;
  export let arrow = arrowInternal;
  export let open = openInternal;
  export let tooltip: string | undefined;
  export let fade: number = 0;
</script>

{#if tooltip && $open}
  <div
    use:melt={$content}
    transition:fader={{ duration: fade }}
    class="z-max max-w-80 rounded bg-neutral-700 shadow"
  >
    <div use:melt={$arrow} />
    <p class="px-4 py-1 text-regular font-normal leading-6 text-white">
      {tooltip}
    </p>
  </div>
{/if}
