<script context="module">
  export const meta = { title: 'components/`Controls/NumberInput', component: NumberInput };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';
  import NumberInput from '@/components/Controls/NumberInput.svelte';

  const value = 0;
</script>

<Story name="With spinner">
  <div class="w-32">
    <NumberInput {value} />
  </div>
</Story>

<Story name="Without spinner">
  <div class="w-32">
    <NumberInput {value} hideSpinner />
  </div>
</Story>

<Story name="With unit">
  <div class="w-32">
    <NumberInput {value} unit="cm" />
  </div>
</Story>

<Story name="With label">
  <div class="w-32">
    <NumberInput label="Label" {value} unit="cm" />
  </div>
</Story>

<Story name="With two decimal points">
  <div class="w-32">
    <NumberInput step={0.02} {value} />
  </div>
</Story>
