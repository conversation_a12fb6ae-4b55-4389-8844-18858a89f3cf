<script context="module">
  import Button from '@/components/Controls/Button.svelte';

  export const meta = {
    component: Button
  };

  let tooltip = 'Never look a gift horse in the mouth';
  const clickHandler = () => {
    alert('You clicked me!');
  };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';
  import IconAdd from '@/components/Icons/IconAdd.svelte';
  import IconEdit from '@/components/Icons/IconEdit.svelte';
  import IconInfo from '@/components/Icons/IconInfo.svelte';
  import IconRefresh from '@/components/Icons/IconRefresh.svelte';
  import IconFile from '@/components/Icons/IconFile.svelte';
  import IconServer from '@/components/Icons/IconServer.svelte';
</script>

<Story name="Primary">
  <div id="Page" class="">
    <div class="grid grid-cols-6 place-items-start items-center gap-1">
      <div>Type</div>
      <div>Default</div>
      <div>Prefix Icon</div>
      <div>Suffix Icon</div>
      <div>Disabled</div>
      <div>Icon Only</div>
      <div>Solid Large</div>
      <Button size="large" variant="solid" on:click={clickHandler}>Label</Button>
      <Button {tooltip} size="large" variant="solid" icon={IconAdd}>Label</Button>
      <Button {tooltip} size="large" variant="solid" icon={IconEdit} iconPosition="right"
        >Label
      </Button>
      <Button {tooltip} size="large" variant="solid" icon={IconInfo} disabled>Label</Button>
      <Button {tooltip} size="large" variant="solid" icon={IconRefresh} />
      <div>Solid Medium</div>
      <Button {tooltip} size="medium" variant="solid">Label</Button>
      <Button {tooltip} size="medium" variant="solid" icon={IconAdd}>Label</Button>
      <Button {tooltip} size="medium" variant="solid" icon={IconEdit} iconPosition="right"
        >Label
      </Button>
      <Button {tooltip} size="medium" variant="solid" icon={IconInfo} disabled>Label</Button>
      <Button {tooltip} size="medium" variant="solid" icon={IconFile} />
      <div>Solid Small</div>
      <Button {tooltip} size="small" variant="solid">Label</Button>
      <Button {tooltip} size="small" variant="solid" icon={IconAdd}>Label</Button>
      <Button {tooltip} size="small" variant="solid" icon={IconEdit} iconPosition="right"
        >Label
      </Button>
      <Button {tooltip} size="small" variant="solid" icon={IconInfo} disabled>Label</Button>
      <Button {tooltip} size="small" variant="solid" icon={IconServer} />
      <div>Outline Large</div>
      <Button {tooltip} size="large" variant="outline" on:click={clickHandler}>Label</Button>
      <Button {tooltip} size="large" variant="outline" icon={IconAdd}>Label</Button>
      <Button {tooltip} size="large" variant="outline" icon={IconEdit} iconPosition="right"
        >Label
      </Button>
      <Button {tooltip} size="large" variant="outline" icon={IconInfo} disabled>Label</Button>
      <Button {tooltip} size="large" variant="outline" icon={IconRefresh} />
      <div>Outline Medium</div>
      <Button {tooltip} size="medium" variant="outline">Label</Button>
      <Button {tooltip} size="medium" variant="outline" icon={IconAdd}>Label</Button>
      <Button {tooltip} size="medium" variant="outline" icon={IconEdit} iconPosition="right"
        >Label
      </Button>
      <Button {tooltip} size="medium" variant="outline" icon={IconInfo} disabled>Label</Button>
      <Button {tooltip} size="medium" variant="outline" icon={IconFile} />
      <div>Outline Small</div>
      <Button {tooltip} size="small" variant="outline">Label</Button>
      <Button {tooltip} size="small" variant="outline" icon={IconAdd}>Label</Button>
      <Button {tooltip} size="small" variant="outline" icon={IconEdit} iconPosition="right"
        >Label
      </Button>
      <Button {tooltip} size="small" variant="outline" icon={IconInfo} disabled>Label</Button>
      <Button {tooltip} size="small" variant="outline" icon={IconServer} />
      <div>Gray Large</div>
      <Button {tooltip} size="large" variant="gray" on:click={clickHandler}>Label</Button>
      <Button {tooltip} size="large" variant="gray" icon={IconAdd}>Label</Button>
      <Button {tooltip} size="large" variant="gray" icon={IconEdit} iconPosition="right"
        >Label
      </Button>
      <Button {tooltip} size="large" variant="gray" icon={IconInfo} disabled>Label</Button>
      <Button {tooltip} size="large" variant="gray" icon={IconRefresh} />
      <div>Gray Medium</div>
      <Button {tooltip} size="medium" variant="gray">Label</Button>
      <Button {tooltip} size="medium" variant="gray" icon={IconAdd}>Label</Button>
      <Button {tooltip} size="medium" variant="gray" icon={IconEdit} iconPosition="right"
        >Label
      </Button>
      <Button {tooltip} size="medium" variant="gray" icon={IconInfo} disabled>Label</Button>
      <Button {tooltip} size="medium" variant="gray" icon={IconFile} />
      <div>Gray Small</div>
      <Button {tooltip} size="small" variant="gray">Label</Button>
      <Button {tooltip} size="small" variant="gray" icon={IconAdd}>Label</Button>
      <Button {tooltip} size="small" variant="gray" icon={IconEdit} iconPosition="right"
        >Label
      </Button>
      <Button {tooltip} size="small" variant="gray" icon={IconInfo} disabled>Label</Button>
      <Button {tooltip} size="small" variant="gray" icon={IconServer} />
      <div>Gray-outline Large</div>
      <Button {tooltip} size="large" variant="gray-outline" on:click={clickHandler}>Label</Button>
      <Button {tooltip} size="large" variant="gray-outline" icon={IconAdd}>Label</Button>
      <Button {tooltip} size="large" variant="gray-outline" icon={IconEdit} iconPosition="right"
        >Label
      </Button>
      <Button {tooltip} size="large" variant="gray-outline" icon={IconInfo} disabled>Label</Button>
      <Button {tooltip} size="large" variant="gray-outline" icon={IconRefresh} />
      <div>Gray-outline Medium</div>
      <Button {tooltip} size="medium" variant="gray-outline">Label</Button>
      <Button {tooltip} size="medium" variant="gray-outline" icon={IconAdd}>Label</Button>
      <Button {tooltip} size="medium" variant="gray-outline" icon={IconEdit} iconPosition="right"
        >Label
      </Button>
      <Button {tooltip} size="medium" variant="gray-outline" icon={IconInfo} disabled>Label</Button>
      <Button {tooltip} size="medium" variant="gray-outline" icon={IconFile} />
      <div>Gray-outline Small</div>
      <Button {tooltip} size="small" variant="gray-outline">Label</Button>
      <Button {tooltip} size="small" variant="gray-outline" icon={IconAdd}>Label</Button>
      <Button {tooltip} size="small" variant="gray-outline" icon={IconEdit} iconPosition="right"
        >Label
      </Button>
      <Button {tooltip} size="small" variant="gray-outline" icon={IconInfo} disabled>Label</Button>
      <Button {tooltip} size="small" variant="gray-outline" icon={IconServer} />
      <div>Ghost Large</div>
      <Button {tooltip} size="large" variant="ghost" on:click={clickHandler}>Label</Button>
      <Button {tooltip} size="large" variant="ghost" icon={IconAdd}>Label</Button>
      <Button {tooltip} size="large" variant="ghost" icon={IconEdit} iconPosition="right"
        >Label
      </Button>
      <Button {tooltip} size="large" variant="ghost" icon={IconInfo} disabled>Label</Button>
      <Button {tooltip} size="large" variant="ghost" icon={IconRefresh} />
      <div>Ghost Medium</div>
      <Button {tooltip} size="medium" variant="ghost">Label</Button>
      <Button {tooltip} size="medium" variant="ghost" icon={IconAdd}>Label</Button>
      <Button {tooltip} size="medium" variant="ghost" icon={IconEdit} iconPosition="right"
        >Label
      </Button>
      <Button {tooltip} size="medium" variant="ghost" icon={IconInfo} disabled>Label</Button>
      <Button {tooltip} size="medium" variant="ghost" icon={IconFile} />
      <div>Ghost Small</div>
      <Button {tooltip} size="small" variant="ghost">Label</Button>
      <Button {tooltip} size="small" variant="ghost" icon={IconAdd}>Label</Button>
      <Button {tooltip} size="small" variant="ghost" icon={IconEdit} iconPosition="right"
        >Label
      </Button>
      <Button {tooltip} size="small" variant="ghost" icon={IconInfo} disabled>Label</Button>
      <Button {tooltip} size="small" variant="ghost" icon={IconServer} />
    </div>
  </div>
</Story>

<Story name="Button fill">
  Example of button filling container. Container width is w-96
  <div class="w-96">
    <Button fullWidth {tooltip} size="large" variant="outline" on:click={clickHandler}>Label</Button
    >
  </div>
</Story>
