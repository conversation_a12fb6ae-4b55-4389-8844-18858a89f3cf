<script lang="ts">
  import { createLabel, createSelect, melt } from '@melt-ui/svelte';

  const {
    elements: { root: labelInternal }
  } = createLabel();

  const {
    elements: { label: selectInternal }
  } = createSelect();

  export let title: string;
  export let required = false;
  export let side = false;
  export let meltAction: typeof labelInternal | typeof selectInternal;
  export let id: string = ($meltAction as typeof $selectInternal).id ?? '';
</script>

<label
  use:melt={$meltAction}
  {...$meltAction}
  class="label block w-full {!side ? 'pb-2' : ''}"
  for={id}
  >{title}
  {#if required}
    <span class="text-brand-error">*</span>
  {/if}
</label>
