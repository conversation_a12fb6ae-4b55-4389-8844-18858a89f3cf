<script context="module">
  export const meta = {
    title: 'components/Controls/DateInput',
    component: DateInput
  };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';
  import DateInput from '@/components/Controls/DateInput.svelte';

  let value: Date;
</script>

<Story name="Date">
  <DateInput bind:value options={{ enableTime: true }} />
</Story>

<Story name="Date - can clear">
  <DateInput bind:value options={{ enableTime: true }} canClear />
</Story>

<Story name="Date - can clear - small">
  <DateInput bind:value size="small" options={{ enableTime: true }} canClear />
</Story>

<Story name="Date with label">
  <DateInput label="Date" bind:value options={{ enableTime: true }} />
</Story>
