<script lang="ts">
  import { onMount } from 'svelte';
  import Icon from '../Icons/Icon.svelte';
  import IconInfo from '../Icons/IconInfo.svelte';
  import { cn } from '@/utils/classname';
  import { createLabel } from '@melt-ui/svelte';
  import {
    inputSizeMap,
    inputIconSizeMap,
    type LimitedComponentSize,
    type IconSize
  } from '@/components/Controls/size';
  import LabelWrapper from '@/components/Controls/LabelWrapper.svelte';
  import { generateId } from '@/utils/generateId';
  import Flatpickr from 'svelte-flatpickr';
  import flatpickr from 'flatpickr';
  import Clear from '@/components/Controls/Clear.svelte';
  import IconCalendar from '@/components/Icons/IconCalendar.svelte';

  export let id = generateId();
  export let label = '';
  export let tabIndex = 0;
  export let value: Date | Date[] | undefined = undefined;
  export let formattedValue: string | undefined = undefined;
  export let alt = '';
  export let placeholder = 'Select Date';
  export let isDisable = false;
  export let required = false;

  export let sideLabel = false;
  export let type: 'text' | 'password' | 'email' | 'tel' = 'text';
  export let info = '';
  export let size: LimitedComponentSize = 'large';
  /**
   * Only the input with no border/styling (used for tagbox)
   */
  export let inputOnly = false;
  export let showInvalidFeedback = false;
  export let canClear = false;

  export let autofocus = false;
  export let onChanged: ((event: Event) => void) | undefined = undefined;
  export let border: 'solid' | 'dashed' = 'solid';
  export let font: 'normal' | 'bold' = 'normal';
  /**
   * Flatpickr options
   *@see https://flatpickr.js.org/options/
   */
  export let options: flatpickr.Options.Options;
  /**
   * ONLY use when you you need a smaller label width
   * (useful when it's a side label)
   */
  export let smallLabel = false;
  export let handleClear = () => {
    value = undefined;
  };

  let focus = false;

  let enableTogglePasswordVisibilty = false;

  onMount(() => {
    enableTogglePasswordVisibilty = type == 'password';
  });

  const {
    elements: { root: labelInternal }
  } = createLabel();

  const iconSizes: Record<LimitedComponentSize, IconSize> = {
    small: 'i-3',
    large: 'i-3.5'
  };

  let flatpickrInstance: flatpickr.Instance;
</script>

<!-- @component
  Date Input component that uses flatpickr, 
  keeps the same conventions a TextInput, Select, etc
 -->

<LabelWrapper
  {label}
  {required}
  side={sideLabel}
  {smallLabel}
  meltAction={labelInternal}
  class={inputOnly ? 'flex-grow' : ''}
>
  <Flatpickr
    bind:flatpickr={flatpickrInstance}
    autofocus={autofocus || undefined}
    {id}
    {options}
    class={cn(
      `text-regular text-neutral-700`,
      inputSizeMap[size],
      !inputOnly
        ? [
            `peer flex w-full flex-grow flex-row items-center justify-start rounded border border-neutral-200 bg-white focus:border-brand-notification  focus:ring-0`,
            { 'border-dashed': border === 'dashed' },
            font === 'bold' ? 'font-bold' : 'font-normal',
            { 'bg-gray-100': isDisable },
            { ' invalid:border-brand-error': focus }
          ]
        : `border-none bg-transparent px-1 outline-none focus:!ring-0`,
      { 'data-[invalid]:text-brand-error': showInvalidFeedback }
    )}
    name={id}
    tabindex={tabIndex}
    disabled={isDisable}
    {placeholder}
    bind:value
    bind:formattedValue
    bind:alt
    on:change={onChanged}
    {required}
  />

  <div class="absolute bottom-0 right-3 top-0 flex items-center">
    {#if value && canClear}
      <Clear {size} onClick={handleClear} />
    {:else}
      <button
        type="button"
        on:click={() => {
          flatpickrInstance.open();
        }}
      >
        <Icon IconComponent={IconCalendar} size={iconSizes[size]} />
      </button>
    {/if}
  </div>

  {#if info}
    <div class="mt-2 flex items-center">
      <Icon IconComponent={IconInfo} size={inputIconSizeMap[size]} />
      <span class="ml-2 text-small text-neutral-700">{info}</span>
    </div>
  {/if}
</LabelWrapper>
