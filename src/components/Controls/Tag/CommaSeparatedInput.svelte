<script lang="ts">
  import { sortArrayString } from '@/utils/sortArrayString';
  import Icon from '../../Icons/Icon.svelte';
  import IconInfo from '../../Icons/IconInfo.svelte';
  import TagsInput from '@/components/Controls/Tag/TagsInput.svelte';
  import { onMount } from 'svelte';
  import type { ComponentSize, LimitedComponentSize } from '@/components/Controls/size';

  export let value: string | undefined;
  export let placeholder: string | undefined = undefined;
  export let disabled = false;
  export let size: LimitedComponentSize = 'large';
  export let hideHint = false;
  export let label = '';
  export let sideLabel = false;

  let pills: string[] = value ? value.split(',') : [];
  // Comma separated values come back sorted from our api
  $: value = sortArrayString(pills).join(',');

  onMount(() => {
    if (!value) {
      value = '';
    }
  });
</script>

<!-- @component
 Simple Wrapper around TagsInput to bind values to a comma separated string
 -->

<TagsInput {label} {sideLabel} {size} bind:value={pills} {placeholder} {disabled} />

{#if !hideHint}
  <div class="mt-1 flex items-center">
    <Icon IconComponent={IconInfo} size="extra-small" />
    <span class="ml-2 text-small text-neutral-700">Add values separated by a comma</span>
  </div>
{/if}
