<script lang="ts">
  import type { TableProps } from '@/components/Controls/Table/table';
  import Skeleton from '@/components/Loader/Skeleton.svelte';

  export let tableProps: TableProps;
</script>

{#if tableProps.pagination}
  <div class="flex w-full px-6 text-regular text-neutral-500">
    <div class="flex flex-auto gap-2">
      {#if tableProps.pagination.pageSizeOptions}
        <Skeleton shimmer class="h-5 w-16" />
        <Skeleton shimmer class="h-5 w-16" />
      {/if}
    </div>
    <div class="flex flex-none gap-2">
      <div class="align-middle leading-6">
        <Skeleton shimmer class="h-5 w-16" />
      </div>
      <Skeleton shimmer class="h-5 w-10" />
      <Skeleton shimmer class="h-5 w-10" />
    </div>
  </div>
{/if}
