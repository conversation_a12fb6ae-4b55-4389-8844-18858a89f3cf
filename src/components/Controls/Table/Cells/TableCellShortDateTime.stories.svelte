<script context="module">
  import TableCellShortDateTime from '@/components/Controls/Table/Cells/TableCellShortDateTime.svelte';

  export const meta = {
    title: 'components/Controls/Table/Cells/TableCellShortDateTime',
    component: TableCellShortDateTime
  };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';
  import Highlight from 'svelte-highlight';
  import typescript from 'svelte-highlight/languages/typescript';
  import 'svelte-highlight/styles/github.css';
  import {
    getDateFromStringOrTimestamp,
    getLocalShortDayTime,
    getLocalShortDay,
    getLocalShortTime,
    hasBeenXDays,
    getLocalCompactDateTime
  } from '@/lib/date-utils';
</script>

<Story name="Overview">
  <div class="max-w-[1000px] space-y-2 rounded-lg p-4 shadow">
    <h3>TableCellShortDateTime</h3>
    <p>
      This component is responsible for rendering the date in a table. If the date is less than 4
      days old, it is displayed without the month and year. Otherwise the full date is shown.
    </p>
    <table class="my-4 table w-full table-auto border-collapse border-spacing-0">
      <tbody>
        <tr class="bg-neutral-100">
          <td class="border-red-200 w-12 border px-6 py-2 text-left leading-6"
            ><TableCellShortDateTime
              row={{ created: '2024-09-06 07:38:01' }}
              key="created"
              currentDate="2024-09-8 07:38:01"
            /></td
          >
          <td class="w-12 border-b border-b-neutral-200 px-6 py-2 text-left leading-6">Column 2</td>
          <td class="w-12 border-b border-b-neutral-200 px-6 py-2 text-left leading-6">Column 3</td>
          <td class="w-12 border-b border-b-neutral-200 px-6 py-2 text-left leading-6">Column 4</td>
        </tr>
        <tr class="bg-neutral-100">
          <td class="border-red-200 w-12 border px-6 py-2 text-left leading-6"
            ><TableCellShortDateTime row={{ created: '2024-08-06 09:35:01' }} key="created" /></td
          >
          <td class="w-12 border-b border-b-neutral-200 px-6 py-2 text-left leading-6">Column 2</td>
          <td class="w-12 border-b border-b-neutral-200 px-6 py-2 text-left leading-6">Column 3</td>
          <td class="w-12 border-b border-b-neutral-200 px-6 py-2 text-left leading-6">Column 4</td>
        </tr>
        <tr class="bg-neutral-100">
          <td class="border-red-200 w-12 border px-6 py-2 text-left leading-6"
            ><TableCellShortDateTime row={{ created: '2024-08-05 11:38:01' }} key="created" /></td
          >
          <td class="w-12 border-b border-b-neutral-200 px-6 py-2 text-left leading-6">Column 2</td>
          <td class="w-12 border-b border-b-neutral-200 px-6 py-2 text-left leading-6">Column 3</td>
          <td class="w-12 border-b border-b-neutral-200 px-6 py-2 text-left leading-6">Column 4</td>
        </tr>
        <tr class="bg-neutral-100">
          <td class="border-red-200 w-12 border px-6 py-2 text-left leading-6"
            ><TableCellShortDateTime row={{ created: '2024-03-01 06:38:01' }} key="created" /></td
          >
          <td class="w-12 border-b border-b-neutral-200 px-6 py-2 text-left leading-6">Column 2</td>
          <td class="w-12 border-b border-b-neutral-200 px-6 py-2 text-left leading-6">Column 3</td>
          <td class="w-12 border-b border-b-neutral-200 px-6 py-2 text-left leading-6">Column 4</td>
        </tr>
      </tbody>
    </table>
  </div>
</Story>

<Story name="Functions">
  <div class="max-w-[1000px] space-y-2 rounded-lg p-4 shadow">
    <h3>The component makes use of a some functions in the date-utils file.</h3>
    <Highlight language={typescript} code="hasBeenXDays()" />
    <Highlight language={typescript} code="getDateFromStringOrTimestamp()" />
    <Highlight language={typescript} code="getLocalCompactDateTime()" />
    <Highlight language={typescript} code="getLocalShortDayTime()" />
  </div>
</Story>

<Story name="Function: hasBeenXDays">
  <div class="max-w-[1000px] space-y-2 rounded-lg p-4 text-neutral-700 shadow">
    <h3>hasBeenXDays</h3>
    <p class="leading-tight">
      The purpose of this function is to take in an amount of days and a date, and then to tell us
      whether or not the specified amount of days have passed.
    </p>
    <p class="leading-tight">
      You can optionally add a third parameter, which is also a date, incase you want to check if a
      certain amount of day have passed between the first and the second date.
    </p>
    <Highlight language={typescript} code={hasBeenXDays.toString()} />
    <h3>Test</h3>
    <div class="grid grid-cols-4 bg-gray-100 p-4">
      <!-- HEADINGS -->
      <h4 class="col-span-3">Input</h4>
      <h4>Output</h4>
      <div class="col-span-3 grid grid-cols-3">
        <p class="font-bold">amountOfDays</p>
        <p class="font-bold">dateTimeString</p>
        <p class="font-bold">currentDate</p>
      </div>
      <div><!-- SPACER --></div>

      <!-- Test 1 -->
      <p>1</p>
      <p>'2024-09-05 11:05:00'</p>
      <p>'2024-09-07 06:00:01'</p>
      <p>{hasBeenXDays(1, '2024-09-05 11:05:00', '2024-09-07 06:00:01')}</p>

      <!-- Test 2 -->
      <p>2</p>
      <p>'2024-09-05 11:05:00'</p>
      <p>'2024-09-06 06:00:01'</p>
      <p>{hasBeenXDays(2, '2024-09-05 11:05:00', '2024-09-06 06:00:01')}</p>

      <!-- Test 3 -->
      <p>7</p>
      <p>"new Date().toDateString()"</p>
      <p></p>
      <p>{hasBeenXDays(7, new Date().toDateString())}</p>

      <!-- Test 4 -->
      <p>5</p>
      <p>'2022-11-06 07:38:01'</p>
      <p>'2022-12-06 07:38:01'</p>
      <p>{hasBeenXDays(5, '2022-11-06 07:38:01', '2022-12-06 07:38:01')}</p>

      <!-- Test 5 -->
      <p>5</p>
      <p>'2022-12-06 07:38:01'</p>
      <p>'2022-12-10 07:38:01'</p>
      <p>{hasBeenXDays(5, '2022-12-06 07:38:01', '2022-12-10 07:38:01')}</p>
    </div>
  </div>
</Story>

<Story name="Function: getDateFromStringOrTimestamp">
  <div class="max-w-[1000px] space-y-2 rounded-lg p-4 text-neutral-700 shadow">
    <h3>getDateFromStringOrTimestamp</h3>

    <Highlight language={typescript} code={getDateFromStringOrTimestamp.toString()} />
    <p class="leading-tight">
      This function takes in a date in one of three formats below and returns a new Date object
      based on that string.
    </p>

    <ul class="pl-4 text-sm">
      <li><strong>a)</strong> a number representing milliseconds eg: 1725618053391</li>
      <li><strong>b)</strong> a string in the form "2024-09-06 07:38:01"</li>
      <li><strong>c)</strong> a string in the form "2024-09-06T07:38:01"</li>
    </ul>

    <h3>Test:</h3>
    <div class="grid grid-cols-2 bg-gray-100 p-2">
      <h4>Input</h4>
      <h4>Output</h4>
      <!-- Test One -->
      <p>1725618053391</p>
      <p>{getDateFromStringOrTimestamp(1725618053391)}</p>
      <!-- Test Two -->
      <p>2024-09-06 07:38:01</p>
      <p>{getDateFromStringOrTimestamp('2024-09-06 07:38:01')}</p>
      <!-- Test Three -->
      <p>2024-09-06T07:38:01</p>
      <p>{getDateFromStringOrTimestamp('2024-09-06T07:38:01')}</p>
    </div>

    <h3>Why is this function needed?</h3>
    <p class="leading-tight">
      The need for this function arises when we have to cater for multiple time zones. When we get
      our data from the server, we know that it is in utc time. However, when we create a new date
      using javascript's Date class, javascript creates this date based on the timezone in which the
      user is in.
    </p>

    <h3>Consider the two date strings "2024-09-06T07:38:01" and "2024-09-06T07:38:01Z"</h3>
    <p class="leading-tight">
      The "Z" at the end of the string stands for Zulu time and will tell javascript not to use the
      browsers local time zone, but instead use UTC. So when we create data objects based on these
      strings we get:
    </p>

    <div class="grid grid-cols-2 bg-gray-100 p-4">
      <h4>Input</h4>
      <h4>Output</h4>
      <!-- Test One -->
      <p>"2024-09-06T07:38:01"</p>
      <p>{new Date('2024-09-06T07:38:01')}</p>
      <!-- Test Two -->
      <p>"2024-09-06 07:38:01"</p>
      <p>{new Date('2024-09-06 07:38:01')}</p>
      <!-- Test Three -->
      <p>"2024-09-06T07:38:01Z"</p>
      <p>{new Date('2024-09-06T07:38:01Z')}</p>
      <!-- Test Four -->
      <p>"2024-09-06 07:38:01Z"</p>
      <p>{new Date('2024-09-06 07:38:01Z')}</p>
    </div>
    <p>Note that the results are not the same when we omit the "Z"</p>

    <h3>What happens if you create a new Date in javascript using a number instead of a string?</h3>
    <p class="leading-tight">
      When you create a new Date object in JavaScript using a number, the number is interpreted as
      the number of milliseconds since the Unix epoch (January 1, 1970, 00:00:00 UTC). <span
        class="bg-[lightblue]"
        >This number is always treated as a UTC timestamp, regardless of the local time zone.</span
      >
    </p>

    <h3>What happens if you create a new Date in javascript without adding any value?</h3>
    <p class="leading-tight">
      When you create a new Date in JavaScript without passing any arguments, it generates a Date
      object representing the current date and time at the moment the Date object is created. <span
        class="bg-[lightblue]"
      >
        The time will be in the local time zone of the environment (e.g., your computer's system
        time).</span
      >
    </p>
  </div>
</Story>

<Story name="Function: getLocalCompactDateTime">
  <div class="max-w-[1000px] space-y-2 rounded-lg p-4 shadow">
    <h3>getLocalCompactDateTime</h3>
    <p>The component takes in a date string and formats it to be readable and compact.</p>
    <Highlight language={typescript} code={getLocalCompactDateTime.toString()} />

    <h3>Test:</h3>
    <div class="grid grid-cols-2 bg-gray-100 p-4">
      <!-- HEADINGS -->
      <h4>Input</h4>
      <h4>Output</h4>

      <!-- Test 1 -->
      <p>'2024-09-05 11:05:00'</p>
      <p>{getLocalCompactDateTime('2024-09-05 11:05:00')}</p>

      <!-- Test 2 -->
      <p>'2022-12-06 07:38:01'</p>
      <p>{getLocalCompactDateTime('2022-12-06 07:38:01')}</p>
    </div>
  </div>
</Story>

<Story name="Function: getLocalShortDayTime">
  <div class="space-y-2 rounded-lg p-4 text-neutral-700 shadow">
    <h3>getLocalShortDayTime</h3>
    <p class="leading-tight">
      This Function concatenates the results of 2 other functions i.e. getLocalShortDay and
      getLocalShortTime, both of which simply formats the date to be more human readable.
    </p>
    <Highlight language={typescript} code={getLocalShortDayTime.toString()} />

    <h3>getLocalShortDay</h3>
    <Highlight language={typescript} code={getLocalShortDay.toString()} />
    <h3>getLocalShortTime</h3>
    <Highlight language={typescript} code={getLocalShortTime.toString()} />
  </div>
</Story>
