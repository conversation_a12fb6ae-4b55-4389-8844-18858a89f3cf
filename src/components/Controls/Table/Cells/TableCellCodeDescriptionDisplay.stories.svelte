<script context="module">
  import TableCellCodeDescriptionDisplay from '@/components/Controls/Table/Cells/TableCellCodeDescriptionDisplay.svelte';
  export const meta = {
    component: TableCellCodeDescriptionDisplay
  };
</script>

<script lang="ts">
  import Table from '@/components/Controls/Table/Table.svelte';
  import { Story } from '@storybook/addon-svelte-csf';
</script>

<Story name="Default">
  <Table
    tableProps={{
      rowKey: 'id',
      isLoading: false,
      isLoadingSkeletonCount: 10,
      header: true,
      rows: [
        {
          id: 1,
          code: 514,
          description_of_code: 'This is a description for code 514',
          description: 'Both key and description exist'
        },
        {
          id: 2,
          description_of_code: 'this is when no code exists, but a description does',
          description: 'Only description exists'
        },
        {
          id: 3,
          code: '404',
          description: 'Only Code exists'
        }
      ],
      columns: [
        { columnKey: 'description', label: 'Description', align: 'left' },
        {
          columnKey: 'entityMeta',
          label: 'TableCellEntityMeta',
          align: 'right',
          component: TableCellCodeDescriptionDisplay,
          componentProps: {
            codeKey: 'code',
            descriptionKey: 'description_of_code'
          }
        }
      ]
    }}
  />
</Story>
