<script lang="ts">
  import type { TableRow } from '@/components/Controls/Table/table';
  import Text from '@/components/Controls/Text/Text.svelte';
  import EntityMetaSideView from '@/components/EntityMetaSideView.svelte';
  import Icon from '@/components/Icons/Icon.svelte';
  import IconInfo from '@/components/Icons/IconInfo.svelte';
  import ModalInternal from '@/components/Modal/ModalInternal.svelte';
  import type { Meta } from '@/models/meta';

  export let row: TableRow;
  export let key: string;
  export let width: 'long' | 'narrow' | 'full' = 'full';

  let open = false;

  $: value = row[key] as Meta[] | undefined;
  $: widthClass = width === 'narrow' ? 'max-w-56' : width === 'long' ? 'max-w-80' : 'max-w-max';
  $: hideClasses = !value || value?.length === 0 ? 'pointer-events-none opacity-0' : '';
</script>

<Text tooltip="View Meta" class="{widthClass} {hideClasses} line-clamp-1 w-full text-wrap">
  <button on:click={() => (open = true)} class="flex">
    <Icon IconComponent={IconInfo} size="smaller" />
  </button>
</Text>

{#if open && value}
  <ModalInternal bind:open size="narrow" position="center" title="Meta">
    <EntityMetaSideView slot="body" meta={value} />
  </ModalInternal>
{/if}
