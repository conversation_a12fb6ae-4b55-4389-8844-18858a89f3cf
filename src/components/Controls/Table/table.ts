import type { ComponentType } from 'svelte';

type TableAlign = 'left' | 'right';

export interface TableColumns {
  columnKey?: string; // not required when using component. The key that will be used to get the value from the row
  component?: ComponentType;
  componentProps?: Record<string, unknown>; // additional props to pass into component if required
  label?: string;
  align: TableAlign;
  customClass?: string;
  isSpacerColumn?: boolean; // used to add a spacer column
}

export interface TableRow {
  [key: string]: unknown;
}

export interface TableSelectable {
  rowIndexes: Record<string, boolean>;
  allSelected: boolean;
  globalSelected: boolean;
}

export interface TableProps {
  selectable?: boolean;
  selectableGlobal?: boolean;
  selectableComponent?: ComponentType;
  selectableState?: TableSelectable;
  header?: boolean;
  columns?: TableColumns[];
  rows: TableRow[];
  rowPointer?: boolean; // used to show pointer on hover
  rowHover?: boolean; // used to show hover on row
  isLoading: boolean;
  isLoadingSkeletonCount: number;
  rowKey: string | string[];
  pagination?: TablePagination;
  onPagination?: (tableProps: TableProps) => void;
  onRowClick?: (row: TableRow) => void;
  footerColumns?: TableColumns[]; // columns to show in the footer
}

export interface TablePagination {
  pageNumber: number; // starts at 0
  pageSize: number; // max rows returned
  pageSizeOptions?: number[]; // list of available page sizes
  total?: number; // if no total provided, consider as infinite.
}

export const resetSelectedState = (props: TableProps): void => {
  props.selectableState = {
    allSelected: false,
    globalSelected: false,
    rowIndexes: {}
  };
};

// return table rows that match  indexes which are true
export const getSelectedRows = (props: TableProps): TableRow[] => {
  if (props.selectableState && props.selectableState.rowIndexes) {
    return props.rows.filter(
      (row) => props.selectableState?.rowIndexes[getRowKey(row, props.rowKey)]
    );
  }
  return [];
};

// return the key for a row
export const getRowKey = (row: TableRow, rowKey: string | string[]): string => {
  if (typeof rowKey === 'string') {
    return row[rowKey] as string;
  }
  return rowKey.map((key) => row[key]).join('-');
};

// Get row keys for all rows
export const getRowIndexes = (tableProps: TableProps): Record<string, boolean> => {
  if (tableProps.selectableState && tableProps.selectableState.allSelected !== undefined) {
    return tableProps.rows.reduce((acc: Record<string, boolean>, row) => {
      acc[getRowKey(row, tableProps.rowKey)] = tableProps.selectableState?.allSelected ?? false;
      return acc;
    }, {});
  }
  return {};
};

// gets the number of columns used in the table included the column for the checkboxes
export const getColCount = (tableProps: TableProps): number => {
  const extraCol = tableProps.selectable ? 1 : 0;
  return (tableProps.columns?.length ?? 0) + extraCol;
};
