<script context="module">
  import Table from '@/components/Controls/Table/Table.svelte';

  export const meta = {
    title: 'components/Controls/Table/Table',
    component: Table
  };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';
  import TableCellIcon from '@/components/Controls/Table/Cells/TableCellIcon.svelte';
  import type { TableProps, TableRow } from '@/components/Controls/Table/table';
  import IconAddCircle from '@/components/Icons/IconAddCircle.svelte';
  import Button from '@/components/Controls/Button.svelte';

  const generateRows = () => {
    const rows = [];
    for (let i = 0; i < 5; i++) {
      const rand = Math.floor(Math.random() * (10000 - 10 + 1) + 10);
      rows.push({
        '1': `Cell ${rand}`,
        '2': `Cell ${rand + 1}`,
        '3': `Cell ${rand + 2}`,
        '4': `Cell ${rand + 3}`
      });
    }
    return rows;
  };

  // https://stackoverflow.com/questions/2450954/how-to-randomize-shuffle-a-javascript-array
  function shuffleRows(rows: TableRow[]) {
    let currentIndex = rows.length,
      randomIndex;

    // While there remain elements to shuffle.
    while (currentIndex > 0) {
      // Pick a remaining element.
      randomIndex = Math.floor(Math.random() * currentIndex);
      currentIndex--;

      // And swap it with the current element.
      [rows[currentIndex], rows[randomIndex]] = [rows[randomIndex], rows[currentIndex]];
    }
    return rows;
  }

  const iconClickHandler = (row: TableRow) => {
    alert('Icon clicked:' + JSON.stringify(row, null, 2));
  };
  const rowClickHandler = (row: TableRow) => {
    alert('Row clicked:' + JSON.stringify(row, null, 2));
  };

  let tableProps: TableProps = {
    rowKey: '1',
    isLoading: false,
    isLoadingSkeletonCount: 10,
    selectable: true,
    selectableGlobal: true,
    header: true,
    rowHover: true,
    rows: [...generateRows()],
    columns: [
      {
        columnKey: '1',
        label: 'Cell 1 (primary key)',
        align: 'left'
      },
      {
        columnKey: '2',
        label: 'Left Aligned',
        align: 'left'
      },
      {
        columnKey: '3',
        label: 'Right Aligned',
        align: 'right'
      },
      {
        align: 'right',
        component: TableCellIcon,
        componentProps: {
          icon: IconAddCircle,
          onClick: iconClickHandler
        }
      }
    ]
  };
</script>

<Story name="Interactive">
  <div class="grid grid-cols-[1fr_auto_auto] gap-4">
    <div class="max-w-[1000px] shadow">
      <Table bind:tableProps />
    </div>
    <div class="space-y-2">
      <Button on:click={() => (tableProps.rows = [...tableProps.rows, ...generateRows()])}
        >append new rows
      </Button>
      <Button on:click={() => (tableProps.rows = shuffleRows(tableProps.rows))}>Shuffle rows</Button
      >
      <Button on:click={() => (tableProps.rows = [])}>Clear Rows</Button>
      <Button on:click={() => (tableProps.isLoading = !tableProps.isLoading)}>Toggle Loading</Button
      >
      <Button on:click={() => (tableProps.selectable = !tableProps.selectable)}
        >Toggle Selectable
      </Button>
      <Button on:click={() => (tableProps.header = !tableProps.header)}>Toggle Header</Button>
    </div>
    <div class="bg-gray-100 p-4">
      <h3>Selectable State</h3>
      <pre>{JSON.stringify(tableProps.selectableState, null, 2)}</pre>
    </div>
  </div>
</Story>

<Story name="No Rows">
  <div class="max-w-[1000px] shadow">
    <Table
      tableProps={{
        rowKey: '1',
        isLoading: false,
        isLoadingSkeletonCount: 10,
        rows: [],
        selectable: true,
        selectableGlobal: true,
        columns: [
          {
            columnKey: '1',
            label: 'Cell 1 (primary key)',
            align: 'left'
          },
          {
            columnKey: '2',
            label: 'Left Aligned',
            align: 'left'
          },
          {
            columnKey: '3',
            label: 'Right Aligned',
            align: 'right'
          }
        ]
      }}
    />
  </div>
</Story>

<Story name="No Rows No Selector">
  <div class="max-w-[1000px] shadow">
    <Table
      tableProps={{
        rowKey: '1',
        isLoading: false,
        isLoadingSkeletonCount: 10,
        rows: [],
        columns: [
          {
            columnKey: '1',
            label: 'Cell 1 (primary key)',
            align: 'left'
          },
          {
            columnKey: '2',
            label: 'Left Aligned',
            align: 'left'
          },
          {
            columnKey: '3',
            label: 'Right Aligned',
            align: 'right'
          }
        ]
      }}
    />
  </div>
</Story>

<Story name="No Selector">
  <div class="max-w-[1000px] shadow">
    <Table
      tableProps={{
        rowKey: '1',
        isLoading: false,
        isLoadingSkeletonCount: 10,
        rows: [...generateRows()],
        columns: [
          {
            columnKey: '1',
            label: 'Cell 1 (primary key)',
            align: 'left'
          },
          {
            columnKey: '2',
            label: 'Left Aligned',
            align: 'left'
          },
          {
            columnKey: '3',
            label: 'Right Aligned',
            align: 'right'
          }
        ]
      }}
    />
  </div>
</Story>

<Story name="Row Hover Click">
  <div class="max-w-[1000px] shadow">
    <Table
      tableProps={{
        rowKey: '1',
        isLoading: false,
        isLoadingSkeletonCount: 10,
        selectable: true,
        selectableGlobal: true,
        rowHover: true,
        rowPointer: true,
        header: true,
        onRowClick: rowClickHandler,
        rows: [...generateRows()],
        columns: [
          {
            columnKey: '1',
            label: 'Cell 1 (primary key)',
            align: 'left'
          },
          {
            columnKey: '2',
            label: 'Left Aligned',
            align: 'left'
          },
          {
            columnKey: '3',
            label: 'Last col left',
            align: 'left'
          },
          {
            align: 'right',
            component: TableCellIcon,
            componentProps: {
              icon: IconAddCircle,
              onClick: iconClickHandler
            }
          }
        ]
      }}
    />
  </div>
</Story>

<Story name="Pagination">
  <div class="max-w-[1000px] shadow">
    <Table
      tableProps={{
        rowKey: '1',
        isLoading: false,
        isLoadingSkeletonCount: 10,
        selectable: true,
        selectableGlobal: true,
        rowHover: true,
        rowPointer: true,
        header: true,
        rows: [...generateRows()],
        onPagination: (p) => alert('pagination:' + JSON.stringify(p.pagination, null, 2)),
        pagination: {
          pageSize: 2,
          pageNumber: 1,
          pageSizeOptions: [10, 20, 50, 100]
        },
        columns: [
          {
            columnKey: '1',
            label: 'Cell 1 (primary key)',
            align: 'left'
          },
          {
            columnKey: '2',
            label: 'Left Aligned',
            align: 'left'
          },
          {
            columnKey: '3',
            label: 'Right Aligned',
            align: 'right'
          }
        ]
      }}
    />
  </div>
</Story>

<Story name="Loading">
  <div class="max-w-[1000px] shadow">
    <Table
      tableProps={{
        rowKey: '1',
        isLoading: true,
        isLoadingSkeletonCount: 10,
        header: true,
        rows: [...generateRows()],
        columns: [
          {
            columnKey: '1',
            label: 'Cell 1 (primary key)',
            align: 'left'
          },
          {
            columnKey: '2',
            label: 'Left Aligned',
            align: 'left'
          },
          {
            columnKey: '3',
            label: 'Right Aligned',
            align: 'right'
          }
        ]
      }}
    />
  </div>
</Story>
<Story name="Loading Selectable">
  <div class="max-w-[1000px] shadow">
    <Table
      tableProps={{
        rowKey: '1',
        isLoading: true,
        isLoadingSkeletonCount: 10,
        selectable: true,
        selectableGlobal: true,
        header: true,
        rows: [...generateRows()],
        columns: [
          {
            columnKey: '1',
            label: 'Cell 1 (primary key)',
            align: 'left'
          },
          {
            columnKey: '2',
            label: 'Left Aligned',
            align: 'left'
          },
          {
            columnKey: '3',
            label: 'Right Aligned',
            align: 'right'
          }
        ]
      }}
    />
  </div>
</Story>

<Story name="Loading Selectable Pagination">
  <div class="max-w-[1000px] shadow">
    <Table
      tableProps={{
        rowKey: '1',
        isLoading: true,
        isLoadingSkeletonCount: 10,
        selectable: true,
        selectableGlobal: true,
        header: true,
        rows: [...generateRows()],
        columns: [
          {
            columnKey: '1',
            label: 'Cell 1 (primary key)',
            align: 'left'
          },
          {
            columnKey: '2',
            label: 'Left Aligned',
            align: 'left'
          },
          {
            columnKey: '3',
            label: 'Right Aligned',
            align: 'right'
          }
        ],
        pagination: {
          pageSize: 10,
          pageNumber: 1,
          pageSizeOptions: [10, 20, 50, 100]
        }
      }}
    />
  </div>
</Story>

<Story name="Loading Pagination">
  <div class="max-w-[1000px] shadow">
    <Table
      tableProps={{
        rowKey: '1',
        isLoading: true,
        isLoadingSkeletonCount: 10,
        header: true,
        rows: [...generateRows()],
        columns: [
          {
            columnKey: '1',
            label: 'Cell 1 (primary key)',
            align: 'left'
          },
          {
            columnKey: '2',
            label: 'Left Aligned',
            align: 'left'
          },
          {
            columnKey: '3',
            label: 'Right Aligned',
            align: 'right'
          }
        ],
        pagination: {
          pageSize: 10,
          pageNumber: 1
        }
      }}
    />
  </div>
</Story>
