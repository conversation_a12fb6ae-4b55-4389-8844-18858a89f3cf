<script lang="ts">
  import {
    getRowIndexes,
    getColCount,
    resetSelectedState,
    type TableProps
  } from '@/components/Controls/Table/table';
  import TableSelectedAction from '@/components/Controls/Table/TableSelectedAction.svelte';

  export let tableProps: TableProps;
  $: showHeader = tableProps && (tableProps.header === undefined || tableProps.header === true);

  // show select action if table is selectable and at least one row is selected
  $: showSelectAction =
    tableProps &&
    tableProps.selectable &&
    tableProps.selectableState?.rowIndexes &&
    Object.values(tableProps.selectableState.rowIndexes).filter((item) => item).length > 0;

  const select = () => {
    if (tableProps.selectableState) {
      tableProps.selectableState.rowIndexes = getRowIndexes(tableProps);
    }
  };

  const setGlobalSelected = () => {
    if (tableProps.selectableState) {
      tableProps.selectableState.globalSelected = true;
    }
  };

  // clear all selections
  const clearSelection = () => {
    if (tableProps.selectableState) {
      tableProps.selectableState.globalSelected = false;
    }
    resetSelectedState(tableProps);
  };
</script>

{#if showHeader}
  <thead class="">
    <tr class="text-small text-neutral-500">
      <!-- Show selectable checkbox-->
      {#if tableProps.selectable && tableProps.selectableState}
        <th class="w-12 border-b border-b-neutral-200 px-6 py-2 text-left">
          <input
            bind:checked={tableProps.selectableState.allSelected}
            on:change={select}
            disabled={tableProps.isLoading || tableProps.rows.length === 0}
            type="checkbox"
            class="h-4 w-4 rounded border border-neutral-300 bg-white text-brand-notification checked:bg-brand-notification focus:ring-0 focus:ring-offset-0"
          />
        </th>
      {/if}

      <!-- If one or more rows is selected, show select action-->
      {#if showSelectAction}
        <th
          colspan={getColCount(tableProps)}
          class="border-b border-b-neutral-200 py-2 text-left leading-6"
        >
          {#if tableProps.selectableComponent}
            <svelte:component this={tableProps.selectableComponent} bind:tableProps />
          {:else}
            <TableSelectedAction bind:tableProps />
          {/if}
        </th>
      {:else if tableProps.columns}
        <!-- Show columns-->
        {#each tableProps.columns as header}
          <th
            class:first:pl-6={!tableProps.selectable}
            class="{header.align === 'left'
              ? 'text-left'
              : 'text-right'} border-b border-b-neutral-200 px-3 py-2 leading-6 last:pr-6"
          >
            {header.label ?? ''}
          </th>
        {/each}
      {/if}
    </tr>

    {#if tableProps.selectable && tableProps.selectableGlobal && tableProps.selectableState?.allSelected}
      <tr class="">
        <th colspan={getColCount(tableProps) + 1} class="border-none">
          <div
            class="flex w-full items-start justify-center gap-2 bg-brand-notification bg-opacity-20 px-6 py-2 text-small font-normal text-neutral-700"
          >
            {#if tableProps.selectableState.globalSelected}
              <div>All items are selected.</div>
              <button class="font-bold" on:click={clearSelection}> Clear Selection</button>
            {:else}
              <div>
                {Object.values(tableProps.selectableState.rowIndexes).length} items on this page are
                selected.
              </div>
              <button class="font-bold" on:click={setGlobalSelected}>
                Select all items that match your search.
              </button>
            {/if}
          </div>
        </th>
      </tr>
    {/if}
  </thead>
{/if}
