<script lang="ts">
  import type { TableProps } from '@/components/Controls/Table/table';
  import Skeleton from '@/components/Loader/Skeleton.svelte';

  export let tableProps: TableProps;

  $: count =
    !tableProps.rows || tableProps.rows.length === 0
      ? tableProps.isLoadingSkeletonCount
      : tableProps.rows.length;
</script>

<tbody>
  {#each Array(count) as _}
    <tr>
      <!--  Set select box for the row-->
      {#if tableProps.selectable && tableProps.selectableState?.rowIndexes}
        <td class="w-12 px-6 pb-2 pt-1 text-left leading-6">
          <input
            type="checkbox"
            disabled
            class="h-4 w-4 rounded border border-neutral-300 bg-white text-brand-notification checked:bg-brand-notification focus:ring-0 focus:ring-offset-0"
          />
        </td>
      {/if}

      <!-- set each cell in the row-->
      {#each Object.values(tableProps.columns ?? {}) as col}
        <td
          class:first:pl-6={!tableProps.selectable}
          class="min-w-4 py-2.5 text-regular leading-6 text-neutral-700 last:pr-6"
        >
          <div class="flex {col.align === 'left' ? 'justify-start' : 'justify-end'} ">
            <Skeleton shimmer class="h-5 w-5/6" />
          </div>
        </td>
      {/each}
    </tr>
  {/each}
</tbody>
