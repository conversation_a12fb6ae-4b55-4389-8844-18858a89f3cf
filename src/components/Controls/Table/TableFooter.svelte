<script lang="ts">
  import { getColCount, type TableProps } from '@/components/Controls/Table/table';
  import TablePagination from '@/components/Controls/Table/TablePagination.svelte';
  import TablePaginationSkeleton from '@/components/Controls/Table/TablePaginationSkeleton.svelte';

  export let tableProps: TableProps;
</script>

{#if tableProps.pagination}
  <tfoot>
    <tr>
      <td colspan={getColCount(tableProps)} class="py-2">
        {#if tableProps.isLoading}
          <TablePaginationSkeleton {tableProps} />
        {:else}
          <TablePagination bind:tableProps on:paginated />
        {/if}
      </td>
    </tr>
  </tfoot>
{/if}
