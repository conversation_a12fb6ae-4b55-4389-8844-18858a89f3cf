<script context="module">
  import TablePagination from '@/components/Controls/Table/TablePagination.svelte';

  export const meta = {
    title: 'components/Controls/Table/TablePagination',
    component: TablePagination
  };
</script>

<script lang="ts">
  import { Story } from '@storybook/addon-svelte-csf';
  import type { TableProps } from '@/components/Controls/Table/table';

  /**
   * return page size number of rows, unless we get to page number 3, then return one less
   */
  const makeRows = (props: TableProps) => {
    if (props.pagination) {
      const totalPages = 3;
      if (props.pagination.total) {
        props.pagination.total = props.pagination.pageSize * totalPages - 1;
      }
      const end = props.pagination.pageNumber * props.pagination.pageSize;
      props.rows = [];
      for (let i = props.pagination.pageSize - 1; i >= 0; i--) {
        props.rows.push({ a: end - i });
      }
      if (props.pagination.pageNumber >= totalPages) {
        props.rows.pop();
      }
    }
  };

  let tableProps: TableProps = {
    rowKey: '1',
    isLoading: false,
    isLoadingSkeletonCount: 10,
    rows: [{ a: 1 }, { a: 2 }],
    columns: [],
    pagination: {
      pageNumber: 1,
      pageSize: 2,
      total: 5,
      pageSizeOptions: [2, 4, 6]
    },
    onPagination: makeRows
  };

  let tablePropsNoTotal: TableProps = {
    rowKey: '1',
    isLoading: false,
    isLoadingSkeletonCount: 10,
    rows: [{ a: 1 }, { a: 2 }],
    columns: [],
    pagination: {
      pageNumber: 1,
      pageSize: 2,
      pageSizeOptions: [2, 4, 6]
    },
    onPagination: makeRows
  };
</script>

<Story name="With Total">
  <TablePagination bind:tableProps />
  <div class="p-6">
    <pre>{JSON.stringify(tableProps, null, 2)}</pre>
  </div>
</Story>

<Story name="No Total">
  <TablePagination bind:tableProps={tablePropsNoTotal} />
  <div class="p-6">
    <pre>{JSON.stringify(tablePropsNoTotal, null, 2)}</pre>
  </div>
</Story>
