<script lang="ts">
  import Button from '@/components/Controls/Button.svelte';
  import IconBack from '@/components/Icons/IconBack.svelte';
  import IconArrowRight from '@/components/Icons/IconArrowRight.svelte';
  import type { TableProps } from '@/components/Controls/Table/table';
  import Select from '@/components/Controls/Select/Select.svelte';

  export let tableProps: TableProps;

  /**
   * Change page number
   */
  const changePage = (pageNumber: number) => {
    if (tableProps?.pagination) {
      tableProps.pagination.pageNumber += pageNumber;
      if (tableProps?.onPagination) {
        tableProps.onPagination(tableProps);
      }
    }
  };

  let pageSize = tableProps.pagination?.pageSize;
  /**
   * resets the size
   */
  const changeSize = (size: number) => {
    if (size === tableProps.pagination?.pageSize) {
      return;
    }
    if (tableProps?.pagination) {
      tableProps.pagination.pageNumber = 1;
      tableProps.pagination.pageSize = size;
      if (tableProps?.onPagination) {
        tableProps.onPagination(tableProps);
      }
    }
  };

  $: {
    if (pageSize) changeSize(pageSize);
  }

  $: lastPage = (tableProps.rows.length ?? 0) < (tableProps.pagination?.pageSize ?? 0);
  $: start =
    (tableProps.pagination?.pageNumber ?? 0) * (tableProps.pagination?.pageSize ?? 0) -
    (tableProps.pagination?.pageSize ?? 0) +
    1;
  $: end = start + tableProps.rows.length - 1;
  $: resultText = () => {
    if (!tableProps.pagination) {
      return '';
    }
    let result = start.toString() + ' to ' + end.toString();
    if (tableProps.pagination.total) {
      result += ' of ' + tableProps.pagination.total;
    } else if (!lastPage) {
      result += ' of many';
    }
    return result;
  };
  $: isEnd = (): boolean => {
    if (!tableProps?.pagination) {
      return true;
    }

    // paging using total
    if (tableProps.pagination.total) {
      return (
        tableProps.pagination.pageNumber * tableProps.pagination.pageSize >=
        tableProps.pagination.total
      );
    }

    // paging without total
    return (tableProps.rows.length ?? 0) < tableProps.pagination.pageSize;
  };

  $: getOptions = () => {
    if (!tableProps?.pagination?.pageSizeOptions) {
      return [];
    }
    return tableProps.pagination.pageSizeOptions.map((size: number) => {
      return {
        value: size,
        label: size.toString()
      };
    });
  };
</script>

{#if tableProps?.pagination}
  <div class="flex w-full px-6 text-regular text-neutral-500">
    <div class="flex flex-auto gap-2">
      {#if tableProps.pagination.pageSizeOptions}
        <div class="w-16">
          <Select size="small" options={getOptions()} bind:value={pageSize} />
        </div>
        <div class="my-auto align-middle leading-6">items per page</div>
      {/if}
    </div>
    <div class="flex flex-none gap-2">
      {#if tableProps.rows.length > 0}
        <div class="align-middle leading-6">{resultText()}</div>
      {/if}
      <Button
        disabled={tableProps.pagination.pageNumber <= 1}
        icon={IconBack}
        variant="gray-outline"
        size="small"
        on:click={() => changePage(-1)}
      />
      <Button
        disabled={isEnd()}
        icon={IconArrowRight}
        variant="gray-outline"
        size="small"
        on:click={() => changePage(1)}
      />
    </div>
  </div>
{/if}
