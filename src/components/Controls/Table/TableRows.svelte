<script lang="ts">
  import { getRowKey, type TableProps, type TableRow } from '@/components/Controls/Table/table';

  export let tableProps: TableProps;

  // if a row is selected, unset the allSelected prop
  const select = () => {
    if (tableProps.selectableState && tableProps.selectableState.allSelected === true) {
      tableProps.selectableState.allSelected = false;
      tableProps.selectableState.globalSelected = false;
    }
  };

  const isRowSelected = (row: TableRow) => {
    return tableProps.selectableState?.rowIndexes[getRowKey(row, tableProps.rowKey)];
  };

  const handleRowClick = (row: TableRow) => {
    if (tableProps.onRowClick) {
      tableProps.onRowClick(row);
    }
  };
</script>

{#if tableProps}
  <tbody>
    {#each tableProps.rows as row (tableProps.rowKey ? getRowKey(row, tableProps.rowKey) : row)}
      <tr
        class:bg-neutral-100={isRowSelected(row)}
        class:hover:bg-neutral-100={tableProps.rowHover}
        class:cursor-pointer={tableProps.rowPointer}
      >
        <!--  Set select box for the row-->
        {#if tableProps.selectable && tableProps.selectableState?.rowIndexes}
          <td class="w-12 border-b border-b-neutral-200 px-6 pb-2 pt-1 text-left leading-6">
            <input
              bind:checked={tableProps.selectableState.rowIndexes[
                getRowKey(row, tableProps.rowKey)
              ]}
              on:change={select}
              type="checkbox"
              class="h-4 w-4 rounded border border-neutral-300 bg-white text-brand-notification checked:bg-brand-notification focus:ring-0 focus:ring-offset-0"
            />
          </td>
        {/if}

        <!-- set each cell in the row-->
        {#each Object.values(tableProps.columns ?? {}) as col}
          <td
            class:first:pl-6={!tableProps.selectable}
            on:click={() => handleRowClick(row)}
            class="{col.customClass ?? ''} {col.align === 'left'
              ? 'text-left'
              : 'text-right'} border-b border-b-neutral-200 px-3 py-2 text-regular leading-6 text-neutral-700 last:pr-6"
          >
            {#if col.component}
              <div class="{col.align === 'left' ? 'justify-start' : 'justify-end'} flex">
                <svelte:component
                  this={col.component}
                  {row}
                  key={col.columnKey}
                  {...col.componentProps}
                />
              </div>
            {:else if row[col.columnKey ?? '']}
              {row[col.columnKey ?? '']}
            {/if}
          </td>
        {/each}
      </tr>
    {/each}
    {#if tableProps.footerColumns}
      <tr class="border-t border-t-neutral-200 bg-neutral-50">
        {#each tableProps.footerColumns as col}
          <td
            class="border-b border-b-neutral-200 px-3 pb-2 pt-1 text-left leading-6 first:pl-6 last:pr-6"
            class:text-right={col.align === 'right'}
          >
            {#if col.isSpacerColumn}
              <div class="h-4 w-full" />
            {:else if col.component}
              <div class="{col.align === 'left' ? 'justify-start' : 'justify-end'} flex">
                <svelte:component
                  this={col.component}
                  key={col.columnKey}
                  rows={tableProps.rows}
                  {...col.componentProps}
                />
              </div>
            {:else if col.columnKey ?? ''}
              {col.columnKey ?? ''}
            {/if}
          </td>
        {/each}
      </tr>
    {/if}
  </tbody>
{/if}
