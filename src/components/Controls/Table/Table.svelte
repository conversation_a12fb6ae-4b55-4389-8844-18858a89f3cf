<script lang="ts">
  import {
    getRowKey,
    resetSelectedState,
    type TableProps
  } from '@/components/Controls/Table/table';
  import TableHeader from '@/components/Controls/Table/TableHeader.svelte';
  import TableRows from '@/components/Controls/Table/TableRows.svelte';
  import TableRowsEmpty from '@/components/Controls/Table/TableRowsEmpty.svelte';
  import TableRowsSkeleton from '@/components/Controls/Table/TableRowsSkeleton.svelte';
  import TableFooter from '@/components/Controls/Table/TableFooter.svelte';

  export let tableProps: TableProps;

  // if rows change, reset all selectables
  // make sure there is always a selectableState
  let lastVersion = '';
  $: if (tableProps.rows) {
    if (tableProps.selectable) {
      if (!tableProps.selectableState) {
        resetSelectedState(tableProps);
      } else {
        const current = tableProps.rows.reduce((acc, row) => {
          acc += getRowKey(row, tableProps.rowKey);
          return acc;
        }, '');
        if (current !== lastVersion) {
          resetSelectedState(tableProps);
          lastVersion = current;
        }
      }
    }
  }
</script>

<!--
@component table
Table with styled for S2S.
Supports select, custom components
-->
<table class="table w-full table-auto border-collapse border-spacing-0">
  <TableHeader bind:tableProps />
  {#if tableProps.isLoading}
    <TableRowsSkeleton {tableProps} />
  {:else if tableProps?.rows?.length === 0}
    <TableRowsEmpty {tableProps} />
  {:else}
    <TableRows bind:tableProps />
  {/if}
  {#if tableProps.pagination}
    <TableFooter bind:tableProps />
  {/if}
</table>
