<script lang="ts">
  import { page } from '$app/stores';

  export let links: { title: string; url: string }[];
</script>

<div
  class="flex h-min w-fit items-center gap-1 rounded border border-neutral-200 bg-white p-[3px] font-bold text-neutral-700"
>
  {#each links as { title, url }}
    <a
      href={url}
      class="flex h-[26px] min-w-20 cursor-pointer items-center justify-center rounded px-2.5 py-2 text-center"
      class:bg-neutral-500={url === $page.url.pathname}
      class:text-white={url === $page.url.pathname}
      class:pointer-events-none={$page.url.pathname.includes(url)}
    >
      {title}
    </a>
  {/each}
</div>
