<script lang="ts">
  import { cn } from '@/utils/classname';
  import Icon from '../Icons/Icon.svelte';
  import IconClose from '../Icons/IconClose.svelte';
  import type { IconSize, LimitedComponentSize } from '@/components/Controls/size';

  export let size: LimitedComponentSize = 'small';
  export let onClick: (e: Event) => void;

  const clearSizes: Record<LimitedComponentSize, string> = {
    small: 'h-4 w-4',
    large: 'h-5 w-5'
  };
  const iconSizes: Record<LimitedComponentSize, IconSize> = {
    small: 'i-3',
    large: 'i-3.5'
  };
</script>

<button
  tabindex="-1"
  type="button"
  on:click={onClick}
  class={cn(
    'flex items-center justify-center rounded-full bg-neutral-500 text-white',
    clearSizes[size]
  )}
>
  <Icon IconComponent={IconClose} size={iconSizes[size]} />
</button>
