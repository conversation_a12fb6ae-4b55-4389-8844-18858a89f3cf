export type StatusType = 'info' | 'inactive' | 'error' | 'warning' | 'success' | 'default';

export function getTextColourClass(type: StatusType | undefined): string {
  switch (type) {
    case 'info':
      return 'text-brand-notification';
    case 'error':
      return 'text-brand-error';
    case 'warning':
      return 'text-brand-warning';
    case 'success':
      return 'text-brand-confirmation';
    case 'inactive':
      return 'text-neutral-400';
    default:
      return 'text-neutral-700';
  }
}

export function getBackgroundColourClass(type: StatusType | undefined): string {
  switch (type) {
    case 'info':
      return 'bg-brand-notification';
    case 'error':
      return 'bg-brand-error';
    case 'warning':
      return 'bg-brand-warning';
    case 'success':
      return 'bg-brand-confirmation';
    case 'inactive':
      return 'bg-neutral-400';
    default:
      return 'bg-neutral-500';
  }
}
