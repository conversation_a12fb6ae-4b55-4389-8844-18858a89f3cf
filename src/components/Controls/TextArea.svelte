<script lang="ts">
  import LabelWrapper from '@/components/Controls/LabelWrapper.svelte';
  import { cn } from '@/utils/classname';
  import { generateId } from '@/utils/generateId';
  import { createLabel } from '@melt-ui/svelte';

  export let id = generateId();
  export let label = '';
  export let value = '';
  export let rows = 1;
  export let maxLength = 50000;
  export let required = false;
  export let sideLabel = false;
  export let smallLabel = false;
  export let placeholder = '';

  let focus = false;

  const {
    elements: { root: labelInternal }
  } = createLabel();
</script>

<LabelWrapper {label} {required} side={sideLabel} {smallLabel} meltAction={labelInternal}>
  <textarea
    on:focus={() => (focus = true)}
    maxlength={maxLength}
    {placeholder}
    {required}
    {rows}
    {id}
    class={cn(
      'flex w-full flex-grow flex-row items-center justify-start rounded border border-neutral-200 bg-white text-regular text-neutral-700 focus:border-brand-notification focus:ring-0',

      { ' invalid:border-brand-error': focus }
    )}
    name={id}
    bind:value
  />
</LabelWrapper>
