import { createMutation, type CreateMutationOptions } from '@tanstack/svelte-query';
import TasksRepo, { type PostTaskParams } from '@/repos/tasks-repo';
import type { TaskInstruction } from '@/models/tasks/tasks';

const tasksRepo = new TasksRepo();
export const createPostTaskMutation = <T extends TaskInstruction>(
  options?: Omit<CreateMutationOptions<void, Error, PostTaskParams<T>, unknown>, 'mutationFn'>
) =>
  createMutation({
    mutationFn: async (params: PostTaskParams<T>) =>
      tasksRepo.post({ instruction: params.instruction, payload: params.payload }),
    ...(options ? options : {})
  });
