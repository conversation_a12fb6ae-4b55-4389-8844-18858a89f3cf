import type { SystemChannel } from '@/models/channels/system-channel';
import ChannelsRepo from '@/repos/channels-repo';
import { createMutation, type MutationOptions } from '@tanstack/svelte-query';

const channelsRepo = new ChannelsRepo();

export const createDeleteChannelMetaMutation = () =>
  createMutation({
    mutationFn: (channelMetaId: number) => channelsRepo.deleteChannelMeta(channelMetaId)
  });

type ChannelSyncSourceParams = { channelId: number; sources: number[] };
export const createChannelSyncSourceMutation = (
  options?: Omit<
    MutationOptions<SystemChannel, unknown, ChannelSyncSourceParams, unknown>,
    'mutationFn'
  >
) =>
  createMutation({
    mutationFn: ({ channelId, sources }: ChannelSyncSourceParams) =>
      channelsRepo.syncSource(channelId, sources),
    ...options
  });
