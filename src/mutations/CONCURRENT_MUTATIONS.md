Also see: https://tanstack.com/query/v4/docs/react/guides/mutations#consecutive-mutations

## OPTION 1 (in non-async function):

```js
//Upload all files concurrently
newFiles.map((file) =>
  $uploadProductImageMutation.mutate(
    { file, productId: $product.workingCopy.id },
    {
      // Observer callback, will only be invoked for last one (since it unsubscribes from previous each time you call)
      onSuccess: async () =>
        await queryClient.invalidateQueries({
          queryKey: productQueryKeys.id($product.workingCopy.id)
        })
    }
  )
);

// Code here runs immediately so we need to use onSuccess
```

## OPTION 2 (in async function):

```js
//Upload all files concurrently (but get the responses)
const newImages: Image[] = await Promise.all(
  newFiles.map((file) =>
    $uploadProductImageMutation.mutateAsync(
      { file, productId: $product.workingCopy.id },
      // Observer callback, will only be invoked for last one (since it unsubscribes from previous each time you call)
      {
        onSuccess: async () => {
          await queryClient.invalidateQueries({
            queryKey: productQueryKeys.id($product.workingCopy.id)});
        }
      }
    )
  )
);

// Code here runs after ALL the concurrent requests above resolve
// so we can do something newImages
// we could also just invalidate here after all the above have run...
```

.mutateAsync's callbacks are also observers which is nice... although not as useful in the case of onSuccess since we can just run any code after the `const newImages: Image[] = await Promise.all(` and have access to all the responses
