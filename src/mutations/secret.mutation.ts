import { createMutation } from '@tanstack/svelte-query';
import SecretsRepo from '@/repos/secrets-repo';
import type { Secret } from '@/models/secrets';
import type { ConnectorKind } from '@/models/config/config';

const secretsRepo = new SecretsRepo();
export const createUpdateSecretMutation = () =>
  createMutation({
    mutationFn: async (secretDetail: Secret) => secretsRepo.put(secretDetail)
  });

export const createDeleteSecretMutation = () =>
  createMutation({
    mutationFn: async (secretDetail: Partial<Secret>) =>
      secretsRepo.delete(
        secretDetail.connector_kind as ConnectorKind,
        secretDetail.connector_id as number,
        secretDetail.key as string
      )
  });
