import type {
  SystemChannelSource,
  SystemChannelSourcePayload
} from '@/models/channel-sources/channel-source';
import ChannelSourcesRepo from '@/repos/channel-sources-repo';
import { createMutation, type MutationOptions } from '@tanstack/svelte-query';

const channelSourcesRepo = new ChannelSourcesRepo();

export const createUpdateChannelSourceMutation = (
  options?: Omit<
    MutationOptions<SystemChannelSource, unknown, SystemChannelSource, unknown>,
    'mutationFn'
  >
) =>
  createMutation({
    mutationFn: async (channelSource: SystemChannelSource) => channelSourcesRepo.put(channelSource),
    ...options
  });

export const createPostChannelSourceMutation = (
  options?: Omit<
    MutationOptions<SystemChannelSourcePayload, unknown, SystemChannelSourcePayload, unknown>,
    'mutationFn'
  >
) =>
  createMutation({
    mutationFn: async (channelSource: SystemChannelSourcePayload) =>
      channelSourcesRepo.post(channelSource),
    ...options
  });
