import type { SystemOrderExport } from '@/models/orders/system-orders-export';
import OrdersRepo from '@/repos/orders-repo';
import { createMutation, type MutationOptions } from '@tanstack/svelte-query';

const ordersRepo = new OrdersRepo();
export const createOrdersExportMutation = (
  options?: Omit<MutationOptions<SystemOrderExport, unknown, void, unknown>, 'mutationFn'>
) =>
  createMutation({
    ...options,
    mutationFn: () => ordersRepo.exportOrders()
  });
