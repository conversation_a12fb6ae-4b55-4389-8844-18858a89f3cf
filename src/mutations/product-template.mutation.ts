import { createMutation } from '@tanstack/svelte-query';
import ProductTemplatesRepo from '@/repos/product-templates-repo';
import type { ApiResult } from '@/models/api-result';
import type { SystemMetaTemplateItem } from '@/models/product-templates/system-meta-template-item';
import type { SystemMetaTemplateItemDataWrapper } from '@/models/product-templates/system-meta-template-item-data-wrapper';
import { PRODUCT_META_TEMPLATE_PREFIX } from '@/lib/constants';
import { ATTRIBUTE_TYPES } from '@/models/attributes/attribute';

export interface ProductTemplateMutationArgs {
  templateName: string;
  templateItems: SystemMetaTemplateItem[];
  itemsToDelete?: SystemMetaTemplateItem[];
}

const productTemplatesRepo = new ProductTemplatesRepo();

export const createProductTemplateDeleteMutation = () =>
  createMutation({
    mutationFn: async (name: string): Promise<ApiResult<boolean>> =>
      productTemplatesRepo.delete(name)
  });

export const createProductTemplateAddMutation = () =>
  createMutation({
    mutationFn: async ({
      templateName,
      templateItems,
      itemsToDelete: _
    }: ProductTemplateMutationArgs): Promise<ApiResult<SystemMetaTemplateItemDataWrapper>[]> =>
      addTemplateItems(templateName, templateItems)
  });

export const createProductTemplateUpdateMutation = () =>
  createMutation({
    mutationFn: async ({
      templateName,
      templateItems,
      itemsToDelete
    }: ProductTemplateMutationArgs): Promise<
      ApiResult<SystemMetaTemplateItemDataWrapper | Boolean>[]
    > => editTemplateItems(templateName, templateItems, itemsToDelete ?? [])
  });

async function addTemplateItems(
  templateName: string,
  templateItems: SystemMetaTemplateItem[]
): Promise<ApiResult<SystemMetaTemplateItemDataWrapper>[]> {
  const addItemsResult = await Promise.all(
    templateItems.map(async (templateItem: SystemMetaTemplateItem) => {
      templateItem.name = `${PRODUCT_META_TEMPLATE_PREFIX}${templateName}`;
      templateItem.id = undefined;
      return await productTemplatesRepo.post(templateItem);
    })
  );

  return [...addItemsResult, ...(await addOptionsForNewTemplateItems(templateName, templateItems))];
}

async function addOptionsForNewTemplateItems(
  templateName: string,
  templateItems: SystemMetaTemplateItem[]
): Promise<ApiResult<SystemMetaTemplateItemDataWrapper>[]> {
  // options can't be added during template item creation, only during update, so do as separate request
  return Promise.all(
    templateItems
      .filter(
        (templateItem) =>
          !templateItem.client_id && templateItem.type === ATTRIBUTE_TYPES.select.value
      )
      .map(async (templateItem: SystemMetaTemplateItem) => {
        templateItem.name = `${PRODUCT_META_TEMPLATE_PREFIX}${templateName}`;
        // do not not edit the template itself, only its options
        templateItem.new_key = undefined;
        templateItem.id = undefined;
        return await productTemplatesRepo.put(templateItem);
      })
  );
}

async function editTemplateItems(
  templateName: string,
  templateItems: SystemMetaTemplateItem[],
  itemsToDelete: SystemMetaTemplateItem[]
): Promise<ApiResult<SystemMetaTemplateItemDataWrapper | Boolean>[]> {
  // can only delete an item server side when editing
  // process deletes first, in case an item is deleted and re-added
  let deleteResults: ApiResult<Boolean>[] = [];
  if (itemsToDelete.length > 0) {
    deleteResults = await Promise.all(
      itemsToDelete.map(async (templateItem: SystemMetaTemplateItem) => {
        return await productTemplatesRepo.deleteItem(templateItem.name, templateItem.key);
      })
    );
  }

  const editResults = await Promise.all(
    templateItems.map(async (templateItem: SystemMetaTemplateItem) => {
      if (templateItem.key) {
        return await productTemplatesRepo.put(templateItem);
      } else {
        //new items added when editing a template
        templateItem.name = `${PRODUCT_META_TEMPLATE_PREFIX}${templateName}`;
        templateItem.key = templateItem.new_key;
        templateItem.id = undefined;
        return await productTemplatesRepo.post(templateItem);
      }
    })
  );

  const newOptionsResult = await addOptionsForNewTemplateItems(templateName, templateItems);

  return [...deleteResults, ...editResults, ...newOptionsResult];
}
