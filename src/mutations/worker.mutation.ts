import { createMutation, type CreateMutationOptions } from '@tanstack/svelte-query';
import WorkerRepo, { type WorkerGetProductsParams } from '@/repos/worker-repo';

const workerRepo = new WorkerRepo();
export const createGetProductsMutation = (
  options?: Omit<CreateMutationOptions<void, Error, number, unknown>, 'mutationFn'>
) =>
  createMutation({
    /**
     * Even though this is a GET, it makes more sense as a mutation
     */
    mutationFn: async (params: WorkerGetProductsParams) => await workerRepo.getProducts(params),
    ...(options ? options : {})
  });
