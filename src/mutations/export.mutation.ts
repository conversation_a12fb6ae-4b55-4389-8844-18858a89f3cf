import type { FieldEntity } from '@/models/fields/fields';
import ExportsRepo, { type ExportParams } from '@/repos/export-repo';
import { createMutation } from '@tanstack/svelte-query';

const exportRepo = new ExportsRepo();
export const createExportMutation = <T extends FieldEntity>({
  entity,
  options
}: {
  entity: T;
  options: ExportParams<T>;
}) =>
  createMutation({
    mutationFn: () => exportRepo.queueExport(entity, { ...options })
  });
