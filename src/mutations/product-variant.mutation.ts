import ProductVariantsRepo from '@/repos/product-variants-repo';
import { createMutation, type CreateMutationOptions } from '@tanstack/svelte-query';
import type Variant from '@/models/variant';

const variantsRepo = new ProductVariantsRepo();

export const createUpdateProductVariantMutation = (
  options: Omit<
    CreateMutationOptions<Variant, unknown, { variant: Variant }, unknown>,
    'mutationFn'
  >
) =>
  createMutation({
    mutationFn: async ({ variant }: { variant: Variant }) =>
      (await variantsRepo.put(variant)).result,
    ...options
  });
