import { createMutation } from '@tanstack/svelte-query';
import QueueRepo from '@/repos/queue-repo';
import type { QueueClearParams, QueueSummary } from '@/models/queue/summary';
import type { QueueItemBase } from '@/models/queue/item';

const queueRepo = new QueueRepo();
export const createQueueMutation = () =>
  createMutation({
    mutationFn: async (param: QueueClearParams): Promise<QueueSummary> =>
      queueRepo.putClearQueue(param)
  });

export const createQueueResetMutation = () =>
  createMutation({
    mutationFn: async (id: number): Promise<QueueItemBase> => queueRepo.putResetQueue(id)
  });

export const createQueueSkipMutation = () =>
  createMutation({
    mutationFn: async (id: number): Promise<QueueItemBase> => queueRepo.putSkipQueue(id)
  });
