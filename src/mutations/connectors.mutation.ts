import type { ConnectorWebhookResponse } from '@/models/connectors/webhooks';
import ConnectorsRepo, { type ConnectorsWebhookParams } from '@/repos/connectors-repo';

import { createMutation, type CreateMutationOptions } from '@tanstack/svelte-query';

const connectorsRepo = new ConnectorsRepo();
export const createPutConnectorWebhookMutation = (
  options?: Omit<
    CreateMutationOptions<ConnectorWebhookResponse, unknown, ConnectorsWebhookParams, unknown>,
    'mutationFn'
  >
) =>
  createMutation({
    mutationFn: (params: ConnectorsWebhookParams) => connectorsRepo.putWebhook(params),
    ...(options ? options : {})
  });

export const createDeleteConnectorWebhookMutation = (
  options?: Omit<
    CreateMutationOptions<ConnectorWebhookResponse, unknown, ConnectorsWebhookParams, unknown>,
    'mutationFn'
  >
) =>
  createMutation({
    mutationFn: (params: ConnectorsWebhookParams) => connectorsRepo.deleteWebhook(params),
    ...(options ? options : {})
  });
