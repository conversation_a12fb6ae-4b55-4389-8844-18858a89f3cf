import type Image from '@/models/image';
import ImagesRepo, { type ImageUploadParams } from '@/repos/images-repo';
import { createMutation, type MutationOptions } from '@tanstack/svelte-query';

const imagesRepo = new ImagesRepo();

export const createDeleteImageMutation = () =>
  createMutation({
    mutationFn: (imageId: number) => imagesRepo.delete(imageId)
  });

/**
 * Mutation to upload one or more images concurrently,
 */
export const createUploadProductImageMutation = (
  options?: Omit<MutationOptions<Image, unknown, ImageUploadParams, unknown>, 'mutationFn'>
) =>
  createMutation({
    ...options,
    mutationFn: async (variables: ImageUploadParams) => imagesRepo.post(variables)
  });
