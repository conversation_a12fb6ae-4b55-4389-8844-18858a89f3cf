import { createMutation } from '@tanstack/svelte-query';
import OrderRepo, { type ShippingAddress } from '@/repos/order-repo';
import type { Order } from '@/models/orders/order';
import { queryClient } from '@/utils/client';
import { orderQueryKeys } from '@/queries/order.query';

const orderRepo = new OrderRepo();

export const createUpdateOrderMutation = () =>
  createMutation({
    mutationFn: async (orderDetail: Order) => orderRepo.put({ system_order: orderDetail }),
    onSuccess: (d) => queryClient.invalidateQueries({ queryKey: orderQueryKeys.id(d.id) })
  });

export const createResetOrderMutation = () =>
  createMutation({
    mutationFn: async (order_id: string) => orderRepo.reset(parseInt(order_id)),
    onSuccess: async (order_id) =>
      queryClient.invalidateQueries({ queryKey: orderQueryKeys.id(order_id) })
  });

export const createUpdateShippingAddressMutation = () =>
  createMutation({
    mutationFn: async ({
      order_id,
      shipping_address
    }: {
      order_id: string | number;
      shipping_address: ShippingAddress;
    }) => orderRepo.updateShippingAddress(order_id, shipping_address)
  });

export const createFulfillOrderMutation = () =>
  createMutation({
    mutationFn: async (order_id: number | string) => orderRepo.fulfillOrder(order_id)
  });

export const createRetryRefundMutation = () =>
  createMutation({
    mutationFn: async ({
      order_id,
      refund_identifier,
      connector_kind
    }: {
      order_id: number;
      refund_identifier: string;
      connector_kind: string;
    }) => orderRepo.retryRefund(order_id, refund_identifier, connector_kind)
  });
