import { createMutation } from '@tanstack/svelte-query';
import type { ConfigStore } from '@/stores';

export type SaveResponse = {
  success: boolean;
  message: string;
};

/**
 * Mutations can have default values set. To do this, you need to set a query key.
 * If we want to set defaults for different versioned objects than we need a unique
 * key per object.
 * Not sure if we will practically use this feature, adding in unique key in case it
 * is required.
 */
export const versionedObjectMutationKeys = {
  save: (key: string) => ['save-versioned-object', key]
};

export const createVersionedObjectMutation = (obj: ConfigStore) =>
  createMutation({
    mutationKey: versionedObjectMutationKeys.save(obj.getQueryKey()),
    mutationFn: async (): Promise<SaveResponse> => obj.saveChanges()
  });
