#!/usr/bin/env bash
set -eu
bash -c 'set -o pipefail'

CONFFILE="$1"
DOMAIN="$2"

originalContent="$(cat "$CONFFILE")"

#First we comment out the SSL related settings (cos e.g. the referenced certificates don't exist any more, so site can't be enabled)
confContent="$(sed 's/SSLCertificate/#SSLCertificate/' "$CONFFILE" | sed 's/BrowserMatch/#BrowserMatch/' | sed 's/SSLEngine/#SSLEngine/' | sed 's/Include \/etc\/letsencrypt\/options-ssl-apache.conf/#Include \/etc\/letsencrypt\/options-ssl-apache.conf/' )"
echo "$confContent" > "$CONFFILE"

sudo a2ensite "$CONFFILE"
sudo systemctl reload apache2

echo "Running Certbot"
sudo certbot --apache -d "$DOMAIN"

echo "Certbot changes are shown here (but will be reverted):"
git --no-pager diff -- "$CONFFILE"

echo "$originalContent" > "$CONFFILE"
sudo systemctl reload apache2

