//line sql.y:2
package sqlfmt

import __yyfmt__ "fmt"

//line sql.y:3
//line sql.y:7
type yySymType struct {
	yys                 int
	sqlSelect           *SelectStmt
	simpleSelect        *SimpleSelect
	fields              []Expr
	expr                Expr
	str                 string
	identifiers         []string
	anyNames            []AnyName
	intoClause          *IntoClause
	fromClause          *FromClause
	whereClause         *WhereClause
	orderExpr           OrderExpr
	orderClause         *OrderClause
	groupByClause       *GroupByClause
	limitClause         *LimitClause
	lockingClause       *LockingClause
	lockingItem         LockingItem
	boolean             bool
	placeholder         interface{}
	columnRef           ColumnRef
	whenClauses         []WhenClause
	whenClause          WhenClause
	pgType              PgType
	pgTypes             []PgType
	row                 Row
	valuesRow           ValuesRow
	valuesClause        ValuesClause
	funcApplication     FuncApplication
	funcArgs            []FuncArg
	funcArg             FuncArg
	withinGroupClause   *WithinGroupClause
	filterClause        *FilterClause
	relationExpr        *RelationExpr
	windowDefinitions   []WindowDefinition
	windowDefinition    WindowDefinition
	windowSpecification WindowSpecification
	overClause          *OverClause
	partitionClause     PartitionClause
	frameClause         *FrameClause
	frameBound          *FrameBound
	arrayExpr           ArrayExpr
	anyName             AnyName
	indirectionEl       IndirectionEl
	indirection         Indirection
	iconst              IntegerConst
	optArrayBounds      []IntegerConst
	optInterval         *OptInterval
	intervalSecond      *IntervalSecond
	subqueryOp          SubqueryOp
	extractList         *ExtractList
	overlayList         OverlayList
	positionList        *PositionList
	substrList          SubstrList
	trimList            TrimList
	xmlAttributes       XmlAttributes
	xmlAttributeEls     []XmlAttributeEl
	xmlAttributeEl      XmlAttributeEl
	xmlExistsArgument   XmlExistsArgument
	xmlRootVersion      XmlRootVersion
}

const IDENT = 57346
const FCONST = 57347
const SCONST = 57348
const BCONST = 57349
const XCONST = 57350
const Op = 57351
const ICONST = 57352
const PARAM = 57353
const TYPECAST = 57354
const DOT_DOT = 57355
const COLON_EQUALS = 57356
const EQUALS_GREATER = 57357
const LESS_EQUALS = 57358
const GREATER_EQUALS = 57359
const NOT_EQUALS = 57360
const ABORT_P = 57361
const ABSOLUTE_P = 57362
const ACCESS = 57363
const ACTION = 57364
const ADD_P = 57365
const ADMIN = 57366
const AFTER = 57367
const AGGREGATE = 57368
const ALL = 57369
const ALSO = 57370
const ALTER = 57371
const ALWAYS = 57372
const ANALYSE = 57373
const ANALYZE = 57374
const AND = 57375
const ANY = 57376
const ARRAY = 57377
const AS = 57378
const ASC = 57379
const ASSERTION = 57380
const ASSIGNMENT = 57381
const ASYMMETRIC = 57382
const AT = 57383
const ATTRIBUTE = 57384
const AUTHORIZATION = 57385
const BACKWARD = 57386
const BEFORE = 57387
const BEGIN_P = 57388
const BETWEEN = 57389
const BIGINT = 57390
const BINARY = 57391
const BIT = 57392
const BOOLEAN_P = 57393
const BOTH = 57394
const BY = 57395
const CACHE = 57396
const CALLED = 57397
const CASCADE = 57398
const CASCADED = 57399
const CASE = 57400
const CAST = 57401
const CATALOG_P = 57402
const CHAIN = 57403
const CHAR_P = 57404
const CHARACTER = 57405
const CHARACTERISTICS = 57406
const CHECK = 57407
const CHECKPOINT = 57408
const CLASS = 57409
const CLOSE = 57410
const CLUSTER = 57411
const COALESCE = 57412
const COLLATE = 57413
const COLLATION = 57414
const COLUMN = 57415
const COMMENT = 57416
const COMMENTS = 57417
const COMMIT = 57418
const COMMITTED = 57419
const CONCURRENTLY = 57420
const CONFIGURATION = 57421
const CONFLICT = 57422
const CONNECTION = 57423
const CONSTRAINT = 57424
const CONSTRAINTS = 57425
const CONTENT_P = 57426
const CONTINUE_P = 57427
const CONVERSION_P = 57428
const COPY = 57429
const COST = 57430
const CREATE = 57431
const CROSS = 57432
const CSV = 57433
const CUBE = 57434
const CURRENT_P = 57435
const CURRENT_CATALOG = 57436
const CURRENT_DATE = 57437
const CURRENT_ROLE = 57438
const CURRENT_SCHEMA = 57439
const CURRENT_TIME = 57440
const CURRENT_TIMESTAMP = 57441
const CURRENT_USER = 57442
const CURSOR = 57443
const CYCLE = 57444
const DATA_P = 57445
const DATABASE = 57446
const DAY_P = 57447
const DEALLOCATE = 57448
const DEC = 57449
const DECIMAL_P = 57450
const DECLARE = 57451
const DEFAULT = 57452
const DEFAULTS = 57453
const DEFERRABLE = 57454
const DEFERRED = 57455
const DEFINER = 57456
const DELETE_P = 57457
const DELIMITER = 57458
const DELIMITERS = 57459
const DESC = 57460
const DICTIONARY = 57461
const DISABLE_P = 57462
const DISCARD = 57463
const DISTINCT = 57464
const DO = 57465
const DOCUMENT_P = 57466
const DOMAIN_P = 57467
const DOUBLE_P = 57468
const DROP = 57469
const EACH = 57470
const ELSE = 57471
const ENABLE_P = 57472
const ENCODING = 57473
const ENCRYPTED = 57474
const END_P = 57475
const ENUM_P = 57476
const ESCAPE = 57477
const EVENT = 57478
const EXCEPT = 57479
const EXCLUDE = 57480
const EXCLUDING = 57481
const EXCLUSIVE = 57482
const EXECUTE = 57483
const EXISTS = 57484
const EXPLAIN = 57485
const EXTENSION = 57486
const EXTERNAL = 57487
const EXTRACT = 57488
const FALSE_P = 57489
const FAMILY = 57490
const FETCH = 57491
const FILTER = 57492
const FIRST_P = 57493
const FLOAT_P = 57494
const FOLLOWING = 57495
const FOR = 57496
const FORCE = 57497
const FOREIGN = 57498
const FORWARD = 57499
const FREEZE = 57500
const FROM = 57501
const FULL = 57502
const FUNCTION = 57503
const FUNCTIONS = 57504
const GLOBAL = 57505
const GRANT = 57506
const GRANTED = 57507
const GREATEST = 57508
const GROUP_P = 57509
const GROUPING = 57510
const HANDLER = 57511
const HAVING = 57512
const HEADER_P = 57513
const HOLD = 57514
const HOUR_P = 57515
const IDENTITY_P = 57516
const IF_P = 57517
const ILIKE = 57518
const IMMEDIATE = 57519
const IMMUTABLE = 57520
const IMPLICIT_P = 57521
const IMPORT_P = 57522
const IN_P = 57523
const INCLUDING = 57524
const INCREMENT = 57525
const INDEX = 57526
const INDEXES = 57527
const INHERIT = 57528
const INHERITS = 57529
const INITIALLY = 57530
const INLINE_P = 57531
const INNER_P = 57532
const INOUT = 57533
const INPUT_P = 57534
const INSENSITIVE = 57535
const INSERT = 57536
const INSTEAD = 57537
const INT_P = 57538
const INTEGER = 57539
const INTERSECT = 57540
const INTERVAL = 57541
const INTO = 57542
const INVOKER = 57543
const IS = 57544
const ISNULL = 57545
const ISOLATION = 57546
const JOIN = 57547
const KEY = 57548
const LABEL = 57549
const LANGUAGE = 57550
const LARGE_P = 57551
const LAST_P = 57552
const LATERAL_P = 57553
const LEADING = 57554
const LEAKPROOF = 57555
const LEAST = 57556
const LEFT = 57557
const LEVEL = 57558
const LIKE = 57559
const LIMIT = 57560
const LISTEN = 57561
const LOAD = 57562
const LOCAL = 57563
const LOCALTIME = 57564
const LOCALTIMESTAMP = 57565
const LOCATION = 57566
const LOCK_P = 57567
const LOCKED = 57568
const LOGGED = 57569
const MAPPING = 57570
const MATCH = 57571
const MATERIALIZED = 57572
const MAXVALUE = 57573
const MINUTE_P = 57574
const MINVALUE = 57575
const MODE = 57576
const MONTH_P = 57577
const MOVE = 57578
const NAME_P = 57579
const NAMES = 57580
const NATIONAL = 57581
const NATURAL = 57582
const NCHAR = 57583
const NEXT = 57584
const NO = 57585
const NONE = 57586
const NOT = 57587
const NOTHING = 57588
const NOTIFY = 57589
const NOTNULL = 57590
const NOWAIT = 57591
const NULL_P = 57592
const NULLIF = 57593
const NULLS_P = 57594
const NUMERIC = 57595
const OBJECT_P = 57596
const OF = 57597
const OFF = 57598
const OFFSET = 57599
const OIDS = 57600
const ON = 57601
const ONLY = 57602
const OPERATOR = 57603
const OPTION = 57604
const OPTIONS = 57605
const OR = 57606
const ORDER = 57607
const ORDINALITY = 57608
const OUT_P = 57609
const OUTER_P = 57610
const OVER = 57611
const OVERLAPS = 57612
const OVERLAY = 57613
const OWNED = 57614
const OWNER = 57615
const PARSER = 57616
const PARTIAL = 57617
const PARTITION = 57618
const PASSING = 57619
const PASSWORD = 57620
const PLACING = 57621
const PLANS = 57622
const POLICY = 57623
const POSITION = 57624
const PRECEDING = 57625
const PRECISION = 57626
const PRESERVE = 57627
const PREPARE = 57628
const PREPARED = 57629
const PRIMARY = 57630
const PRIOR = 57631
const PRIVILEGES = 57632
const PROCEDURAL = 57633
const PROCEDURE = 57634
const PROGRAM = 57635
const QUOTE = 57636
const RANGE = 57637
const READ = 57638
const REAL = 57639
const REASSIGN = 57640
const RECHECK = 57641
const RECURSIVE = 57642
const REF = 57643
const REFERENCES = 57644
const REFRESH = 57645
const REINDEX = 57646
const RELATIVE_P = 57647
const RELEASE = 57648
const RENAME = 57649
const REPEATABLE = 57650
const REPLACE = 57651
const REPLICA = 57652
const RESET = 57653
const RESTART = 57654
const RESTRICT = 57655
const RETURNING = 57656
const RETURNS = 57657
const REVOKE = 57658
const RIGHT = 57659
const ROLE = 57660
const ROLLBACK = 57661
const ROLLUP = 57662
const ROW = 57663
const ROWS = 57664
const RULE = 57665
const SAVEPOINT = 57666
const SCHEMA = 57667
const SCROLL = 57668
const SEARCH = 57669
const SECOND_P = 57670
const SECURITY = 57671
const SELECT = 57672
const SEQUENCE = 57673
const SEQUENCES = 57674
const SERIALIZABLE = 57675
const SERVER = 57676
const SESSION = 57677
const SESSION_USER = 57678
const SET = 57679
const SETS = 57680
const SETOF = 57681
const SHARE = 57682
const SHOW = 57683
const SIMILAR = 57684
const SIMPLE = 57685
const SKIP = 57686
const SMALLINT = 57687
const SNAPSHOT = 57688
const SOME = 57689
const SQL_P = 57690
const STABLE = 57691
const STANDALONE_P = 57692
const START = 57693
const STATEMENT = 57694
const STATISTICS = 57695
const STDIN = 57696
const STDOUT = 57697
const STORAGE = 57698
const STRICT_P = 57699
const STRIP_P = 57700
const SUBSTRING = 57701
const SYMMETRIC = 57702
const SYSID = 57703
const SYSTEM_P = 57704
const TABLE = 57705
const TABLES = 57706
const TABLESAMPLE = 57707
const TABLESPACE = 57708
const TEMP = 57709
const TEMPLATE = 57710
const TEMPORARY = 57711
const TEXT_P = 57712
const THEN = 57713
const TIME = 57714
const TIMESTAMP = 57715
const TO = 57716
const TRAILING = 57717
const TRANSACTION = 57718
const TRANSFORM = 57719
const TREAT = 57720
const TRIGGER = 57721
const TRIM = 57722
const TRUE_P = 57723
const TRUNCATE = 57724
const TRUSTED = 57725
const TYPE_P = 57726
const TYPES_P = 57727
const UNBOUNDED = 57728
const UNCOMMITTED = 57729
const UNENCRYPTED = 57730
const UNION = 57731
const UNIQUE = 57732
const UNKNOWN = 57733
const UNLISTEN = 57734
const UNLOGGED = 57735
const UNTIL = 57736
const UPDATE = 57737
const USER = 57738
const USING = 57739
const VACUUM = 57740
const VALID = 57741
const VALIDATE = 57742
const VALIDATOR = 57743
const VALUE_P = 57744
const VALUES = 57745
const VARCHAR = 57746
const VARIADIC = 57747
const VARYING = 57748
const VERBOSE = 57749
const VERSION_P = 57750
const VIEW = 57751
const VIEWS = 57752
const VOLATILE = 57753
const WHEN = 57754
const WHERE = 57755
const WHITESPACE_P = 57756
const WINDOW = 57757
const WITH = 57758
const WITHIN = 57759
const WITHOUT = 57760
const WORK = 57761
const WRAPPER = 57762
const WRITE = 57763
const XML_P = 57764
const XMLATTRIBUTES = 57765
const XMLCONCAT = 57766
const XMLELEMENT = 57767
const XMLEXISTS = 57768
const XMLFOREST = 57769
const XMLPARSE = 57770
const XMLPI = 57771
const XMLROOT = 57772
const XMLSERIALIZE = 57773
const YEAR_P = 57774
const YES_P = 57775
const ZONE = 57776
const NOT_LA = 57777
const NULLS_LA = 57778
const WITH_LA = 57779
const OP = 57780
const POSTFIXOP = 57781
const UMINUS = 57782

var yyToknames = [...]string{
	"$end",
	"error",
	"$unk",
	"IDENT",
	"FCONST",
	"SCONST",
	"BCONST",
	"XCONST",
	"Op",
	"ICONST",
	"PARAM",
	"TYPECAST",
	"DOT_DOT",
	"COLON_EQUALS",
	"EQUALS_GREATER",
	"LESS_EQUALS",
	"GREATER_EQUALS",
	"NOT_EQUALS",
	"ABORT_P",
	"ABSOLUTE_P",
	"ACCESS",
	"ACTION",
	"ADD_P",
	"ADMIN",
	"AFTER",
	"AGGREGATE",
	"ALL",
	"ALSO",
	"ALTER",
	"ALWAYS",
	"ANALYSE",
	"ANALYZE",
	"AND",
	"ANY",
	"ARRAY",
	"AS",
	"ASC",
	"ASSERTION",
	"ASSIGNMENT",
	"ASYMMETRIC",
	"AT",
	"ATTRIBUTE",
	"AUTHORIZATION",
	"BACKWARD",
	"BEFORE",
	"BEGIN_P",
	"BETWEEN",
	"BIGINT",
	"BINARY",
	"BIT",
	"BOOLEAN_P",
	"BOTH",
	"BY",
	"CACHE",
	"CALLED",
	"CASCADE",
	"CASCADED",
	"CASE",
	"CAST",
	"CATALOG_P",
	"CHAIN",
	"CHAR_P",
	"CHARACTER",
	"CHARACTERISTICS",
	"CHECK",
	"CHECKPOINT",
	"CLASS",
	"CLOSE",
	"CLUSTER",
	"COALESCE",
	"COLLATE",
	"COLLATION",
	"COLUMN",
	"COMMENT",
	"COMMENTS",
	"COMMIT",
	"COMMITTED",
	"CONCURRENTLY",
	"CONFIGURATION",
	"CONFLICT",
	"CONNECTION",
	"CONSTRAINT",
	"CONSTRAINTS",
	"CONTENT_P",
	"CONTINUE_P",
	"CONVERSION_P",
	"COPY",
	"COST",
	"CREATE",
	"CROSS",
	"CSV",
	"CUBE",
	"CURRENT_P",
	"CURRENT_CATALOG",
	"CURRENT_DATE",
	"CURRENT_ROLE",
	"CURRENT_SCHEMA",
	"CURRENT_TIME",
	"CURRENT_TIMESTAMP",
	"CURRENT_USER",
	"CURSOR",
	"CYCLE",
	"DATA_P",
	"DATABASE",
	"DAY_P",
	"DEALLOCATE",
	"DEC",
	"DECIMAL_P",
	"DECLARE",
	"DEFAULT",
	"DEFAULTS",
	"DEFERRABLE",
	"DEFERRED",
	"DEFINER",
	"DELETE_P",
	"DELIMITER",
	"DELIMITERS",
	"DESC",
	"DICTIONARY",
	"DISABLE_P",
	"DISCARD",
	"DISTINCT",
	"DO",
	"DOCUMENT_P",
	"DOMAIN_P",
	"DOUBLE_P",
	"DROP",
	"EACH",
	"ELSE",
	"ENABLE_P",
	"ENCODING",
	"ENCRYPTED",
	"END_P",
	"ENUM_P",
	"ESCAPE",
	"EVENT",
	"EXCEPT",
	"EXCLUDE",
	"EXCLUDING",
	"EXCLUSIVE",
	"EXECUTE",
	"EXISTS",
	"EXPLAIN",
	"EXTENSION",
	"EXTERNAL",
	"EXTRACT",
	"FALSE_P",
	"FAMILY",
	"FETCH",
	"FILTER",
	"FIRST_P",
	"FLOAT_P",
	"FOLLOWING",
	"FOR",
	"FORCE",
	"FOREIGN",
	"FORWARD",
	"FREEZE",
	"FROM",
	"FULL",
	"FUNCTION",
	"FUNCTIONS",
	"GLOBAL",
	"GRANT",
	"GRANTED",
	"GREATEST",
	"GROUP_P",
	"GROUPING",
	"HANDLER",
	"HAVING",
	"HEADER_P",
	"HOLD",
	"HOUR_P",
	"IDENTITY_P",
	"IF_P",
	"ILIKE",
	"IMMEDIATE",
	"IMMUTABLE",
	"IMPLICIT_P",
	"IMPORT_P",
	"IN_P",
	"INCLUDING",
	"INCREMENT",
	"INDEX",
	"INDEXES",
	"INHERIT",
	"INHERITS",
	"INITIALLY",
	"INLINE_P",
	"INNER_P",
	"INOUT",
	"INPUT_P",
	"INSENSITIVE",
	"INSERT",
	"INSTEAD",
	"INT_P",
	"INTEGER",
	"INTERSECT",
	"INTERVAL",
	"INTO",
	"INVOKER",
	"IS",
	"ISNULL",
	"ISOLATION",
	"JOIN",
	"KEY",
	"LABEL",
	"LANGUAGE",
	"LARGE_P",
	"LAST_P",
	"LATERAL_P",
	"LEADING",
	"LEAKPROOF",
	"LEAST",
	"LEFT",
	"LEVEL",
	"LIKE",
	"LIMIT",
	"LISTEN",
	"LOAD",
	"LOCAL",
	"LOCALTIME",
	"LOCALTIMESTAMP",
	"LOCATION",
	"LOCK_P",
	"LOCKED",
	"LOGGED",
	"MAPPING",
	"MATCH",
	"MATERIALIZED",
	"MAXVALUE",
	"MINUTE_P",
	"MINVALUE",
	"MODE",
	"MONTH_P",
	"MOVE",
	"NAME_P",
	"NAMES",
	"NATIONAL",
	"NATURAL",
	"NCHAR",
	"NEXT",
	"NO",
	"NONE",
	"NOT",
	"NOTHING",
	"NOTIFY",
	"NOTNULL",
	"NOWAIT",
	"NULL_P",
	"NULLIF",
	"NULLS_P",
	"NUMERIC",
	"OBJECT_P",
	"OF",
	"OFF",
	"OFFSET",
	"OIDS",
	"ON",
	"ONLY",
	"OPERATOR",
	"OPTION",
	"OPTIONS",
	"OR",
	"ORDER",
	"ORDINALITY",
	"OUT_P",
	"OUTER_P",
	"OVER",
	"OVERLAPS",
	"OVERLAY",
	"OWNED",
	"OWNER",
	"PARSER",
	"PARTIAL",
	"PARTITION",
	"PASSING",
	"PASSWORD",
	"PLACING",
	"PLANS",
	"POLICY",
	"POSITION",
	"PRECEDING",
	"PRECISION",
	"PRESERVE",
	"PREPARE",
	"PREPARED",
	"PRIMARY",
	"PRIOR",
	"PRIVILEGES",
	"PROCEDURAL",
	"PROCEDURE",
	"PROGRAM",
	"QUOTE",
	"RANGE",
	"READ",
	"REAL",
	"REASSIGN",
	"RECHECK",
	"RECURSIVE",
	"REF",
	"REFERENCES",
	"REFRESH",
	"REINDEX",
	"RELATIVE_P",
	"RELEASE",
	"RENAME",
	"REPEATABLE",
	"REPLACE",
	"REPLICA",
	"RESET",
	"RESTART",
	"RESTRICT",
	"RETURNING",
	"RETURNS",
	"REVOKE",
	"RIGHT",
	"ROLE",
	"ROLLBACK",
	"ROLLUP",
	"ROW",
	"ROWS",
	"RULE",
	"SAVEPOINT",
	"SCHEMA",
	"SCROLL",
	"SEARCH",
	"SECOND_P",
	"SECURITY",
	"SELECT",
	"SEQUENCE",
	"SEQUENCES",
	"SERIALIZABLE",
	"SERVER",
	"SESSION",
	"SESSION_USER",
	"SET",
	"SETS",
	"SETOF",
	"SHARE",
	"SHOW",
	"SIMILAR",
	"SIMPLE",
	"SKIP",
	"SMALLINT",
	"SNAPSHOT",
	"SOME",
	"SQL_P",
	"STABLE",
	"STANDALONE_P",
	"START",
	"STATEMENT",
	"STATISTICS",
	"STDIN",
	"STDOUT",
	"STORAGE",
	"STRICT_P",
	"STRIP_P",
	"SUBSTRING",
	"SYMMETRIC",
	"SYSID",
	"SYSTEM_P",
	"TABLE",
	"TABLES",
	"TABLESAMPLE",
	"TABLESPACE",
	"TEMP",
	"TEMPLATE",
	"TEMPORARY",
	"TEXT_P",
	"THEN",
	"TIME",
	"TIMESTAMP",
	"TO",
	"TRAILING",
	"TRANSACTION",
	"TRANSFORM",
	"TREAT",
	"TRIGGER",
	"TRIM",
	"TRUE_P",
	"TRUNCATE",
	"TRUSTED",
	"TYPE_P",
	"TYPES_P",
	"UNBOUNDED",
	"UNCOMMITTED",
	"UNENCRYPTED",
	"UNION",
	"UNIQUE",
	"UNKNOWN",
	"UNLISTEN",
	"UNLOGGED",
	"UNTIL",
	"UPDATE",
	"USER",
	"USING",
	"VACUUM",
	"VALID",
	"VALIDATE",
	"VALIDATOR",
	"VALUE_P",
	"VALUES",
	"VARCHAR",
	"VARIADIC",
	"VARYING",
	"VERBOSE",
	"VERSION_P",
	"VIEW",
	"VIEWS",
	"VOLATILE",
	"WHEN",
	"WHERE",
	"WHITESPACE_P",
	"WINDOW",
	"WITH",
	"WITHIN",
	"WITHOUT",
	"WORK",
	"WRAPPER",
	"WRITE",
	"XML_P",
	"XMLATTRIBUTES",
	"XMLCONCAT",
	"XMLELEMENT",
	"XMLEXISTS",
	"XMLFOREST",
	"XMLPARSE",
	"XMLPI",
	"XMLROOT",
	"XMLSERIALIZE",
	"YEAR_P",
	"YES_P",
	"ZONE",
	"NOT_LA",
	"NULLS_LA",
	"WITH_LA",
	"OP",
	"'<'",
	"'>'",
	"'='",
	"POSTFIXOP",
	"'+'",
	"'-'",
	"'*'",
	"'/'",
	"'%'",
	"'^'",
	"UMINUS",
	"'['",
	"']'",
	"'('",
	"')'",
	"'.'",
	"';'",
	"','",
	"':'",
}
var yyStatenames = [...]string{}

const yyEofCode = 1
const yyErrCode = 2
const yyMaxDepth = 200

//line sql.y:3467

// The parser expects the lexer to return 0 on EOF.  Give it a name
// for clarity.
const eof = 0

//line yacctab:1
var yyExca = [...]int{
	-1, 1,
	1, -1,
	-2, 0,
	-1, 4,
	1, 332,
	455, 332,
	-2, 340,
	-1, 5,
	1, 335,
	453, 335,
	455, 335,
	-2, 339,
	-1, 13,
	1, 336,
	453, 336,
	455, 336,
	-2, 368,
	-1, 410,
	6, 541,
	14, 541,
	15, 541,
	452, 541,
	-2, 538,
	-1, 411,
	6, 542,
	14, 542,
	15, 542,
	452, 542,
	-2, 539,
	-1, 419,
	6, 82,
	452, 82,
	-2, 834,
	-1, 431,
	6, 870,
	14, 870,
	15, 870,
	452, 870,
	-2, 225,
	-1, 452,
	6, 46,
	-2, 818,
	-1, 453,
	6, 75,
	452, 75,
	-2, 819,
	-1, 454,
	6, 53,
	-2, 820,
	-1, 455,
	6, 75,
	63, 75,
	452, 75,
	-2, 821,
	-1, 456,
	6, 75,
	63, 75,
	452, 75,
	-2, 822,
	-1, 457,
	6, 42,
	-2, 824,
	-1, 458,
	6, 42,
	-2, 825,
	-1, 459,
	6, 55,
	-2, 828,
	-1, 460,
	6, 43,
	-2, 832,
	-1, 461,
	6, 44,
	-2, 833,
	-1, 463,
	6, 75,
	63, 75,
	452, 75,
	-2, 837,
	-1, 464,
	6, 42,
	-2, 840,
	-1, 465,
	6, 47,
	-2, 845,
	-1, 466,
	6, 45,
	-2, 848,
	-1, 467,
	6, 85,
	-2, 850,
	-1, 468,
	6, 85,
	-2, 851,
	-1, 469,
	6, 70,
	63, 70,
	452, 70,
	-2, 855,
	-1, 533,
	321, 438,
	322, 438,
	-2, 102,
	-1, 577,
	27, 460,
	34, 460,
	347, 460,
	-2, 474,
	-1, 588,
	137, 340,
	149, 340,
	154, 340,
	198, 340,
	218, 340,
	257, 340,
	265, 340,
	389, 340,
	-2, 194,
	-1, 598,
	6, 519,
	452, 519,
	-2, 489,
	-1, 774,
	1, 780,
	137, 780,
	149, 780,
	154, 780,
	159, 780,
	167, 780,
	170, 780,
	198, 780,
	218, 780,
	257, 780,
	265, 780,
	389, 780,
	413, 780,
	415, 780,
	450, 780,
	453, 780,
	454, 780,
	455, 780,
	-2, 360,
	-1, 775,
	1, 778,
	137, 778,
	149, 778,
	154, 778,
	159, 778,
	167, 778,
	170, 778,
	198, 778,
	218, 778,
	257, 778,
	265, 778,
	389, 778,
	413, 778,
	415, 778,
	450, 778,
	453, 778,
	454, 778,
	455, 778,
	-2, 360,
	-1, 778,
	1, 794,
	137, 794,
	149, 794,
	154, 794,
	159, 794,
	167, 794,
	170, 794,
	198, 794,
	218, 794,
	257, 794,
	265, 794,
	389, 794,
	413, 794,
	415, 794,
	450, 794,
	453, 794,
	454, 794,
	455, 794,
	-2, 360,
	-1, 826,
	16, 0,
	17, 0,
	18, 0,
	439, 0,
	440, 0,
	441, 0,
	-2, 114,
	-1, 827,
	16, 0,
	17, 0,
	18, 0,
	439, 0,
	440, 0,
	441, 0,
	-2, 115,
	-1, 828,
	16, 0,
	17, 0,
	18, 0,
	439, 0,
	440, 0,
	441, 0,
	-2, 116,
	-1, 829,
	16, 0,
	17, 0,
	18, 0,
	439, 0,
	440, 0,
	441, 0,
	-2, 117,
	-1, 830,
	16, 0,
	17, 0,
	18, 0,
	439, 0,
	440, 0,
	441, 0,
	-2, 118,
	-1, 831,
	16, 0,
	17, 0,
	18, 0,
	439, 0,
	440, 0,
	441, 0,
	-2, 119,
	-1, 835,
	47, 0,
	176, 0,
	181, 0,
	217, 0,
	342, 0,
	435, 0,
	-2, 127,
	-1, 841,
	47, 0,
	176, 0,
	181, 0,
	217, 0,
	342, 0,
	435, 0,
	-2, 131,
	-1, 890,
	270, 452,
	-2, 455,
	-1, 900,
	14, 9,
	15, 9,
	-2, 518,
	-1, 1024,
	47, 0,
	176, 0,
	181, 0,
	217, 0,
	342, 0,
	435, 0,
	-2, 129,
	-1, 1025,
	47, 0,
	176, 0,
	181, 0,
	217, 0,
	342, 0,
	435, 0,
	-2, 133,
	-1, 1031,
	47, 0,
	176, 0,
	181, 0,
	217, 0,
	342, 0,
	435, 0,
	-2, 135,
	-1, 1057,
	270, 451,
	-2, 454,
	-1, 1186,
	47, 0,
	176, 0,
	181, 0,
	217, 0,
	342, 0,
	435, 0,
	-2, 128,
	-1, 1189,
	47, 0,
	176, 0,
	181, 0,
	217, 0,
	342, 0,
	435, 0,
	-2, 137,
	-1, 1192,
	47, 0,
	176, 0,
	181, 0,
	217, 0,
	342, 0,
	435, 0,
	-2, 132,
	-1, 1196,
	202, 0,
	203, 0,
	248, 0,
	-2, 150,
	-1, 1203,
	27, 286,
	34, 286,
	347, 286,
	-2, 475,
	-1, 1208,
	270, 453,
	-2, 456,
	-1, 1250,
	16, 0,
	17, 0,
	18, 0,
	439, 0,
	440, 0,
	441, 0,
	-2, 174,
	-1, 1251,
	16, 0,
	17, 0,
	18, 0,
	439, 0,
	440, 0,
	441, 0,
	-2, 175,
	-1, 1252,
	16, 0,
	17, 0,
	18, 0,
	439, 0,
	440, 0,
	441, 0,
	-2, 176,
	-1, 1253,
	16, 0,
	17, 0,
	18, 0,
	439, 0,
	440, 0,
	441, 0,
	-2, 177,
	-1, 1254,
	16, 0,
	17, 0,
	18, 0,
	439, 0,
	440, 0,
	441, 0,
	-2, 178,
	-1, 1255,
	16, 0,
	17, 0,
	18, 0,
	439, 0,
	440, 0,
	441, 0,
	-2, 179,
	-1, 1315,
	47, 0,
	176, 0,
	181, 0,
	217, 0,
	342, 0,
	435, 0,
	-2, 130,
	-1, 1316,
	47, 0,
	176, 0,
	181, 0,
	217, 0,
	342, 0,
	435, 0,
	-2, 134,
	-1, 1320,
	47, 0,
	176, 0,
	181, 0,
	217, 0,
	342, 0,
	435, 0,
	-2, 136,
	-1, 1321,
	202, 0,
	203, 0,
	248, 0,
	-2, 151,
	-1, 1325,
	47, 0,
	176, 0,
	181, 0,
	217, 0,
	342, 0,
	435, 0,
	-2, 154,
	-1, 1326,
	47, 0,
	176, 0,
	181, 0,
	217, 0,
	342, 0,
	435, 0,
	-2, 156,
	-1, 1381,
	47, 0,
	176, 0,
	181, 0,
	217, 0,
	342, 0,
	435, 0,
	-2, 138,
	-1, 1382,
	47, 0,
	176, 0,
	181, 0,
	217, 0,
	342, 0,
	435, 0,
	-2, 155,
	-1, 1383,
	47, 0,
	176, 0,
	181, 0,
	217, 0,
	342, 0,
	435, 0,
	-2, 157,
	-1, 1391,
	202, 0,
	-2, 183,
	-1, 1418,
	202, 0,
	-2, 184,
	-1, 1447,
	47, 0,
	176, 0,
	217, 0,
	342, 0,
	435, 0,
	-2, 817,
}

const yyNprod = 965
const yyPrivate = 57344

var yyTokenNames []string
var yyStates []string

const yyLast = 19778

var yyAct = [...]int{

	378, 1446, 1445, 1233, 1372, 1001, 395, 1409, 960, 14,
	1197, 1368, 783, 470, 952, 1297, 1117, 29, 398, 897,
	1161, 653, 997, 1198, 659, 594, 27, 1060, 32, 1116,
	961, 661, 1013, 999, 13, 29, 911, 854, 1019, 647,
	542, 851, 504, 758, 949, 508, 637, 396, 901, 771,
	963, 372, 544, 907, 892, 501, 381, 1165, 873, 904,
	633, 577, 1443, 1437, 547, 1442, 1136, 1436, 1435, 579,
	1051, 1324, 1420, 1398, 1396, 1324, 1051, 1397, 1384, 1349,
	1328, 1324, 1051, 1051, 1323, 1290, 1285, 1324, 1051, 1286,
	18, 1275, 1201, 549, 1276, 1051, 545, 1152, 1143, 572,
	1051, 1051, 1135, 1131, 1130, 1136, 1051, 1051, 1129, 1128,
	18, 1051, 1051, 1057, 1053, 1052, 1051, 387, 4, 1054,
	1051, 748, 1051, 548, 747, 20, 1426, 905, 1411, 1362,
	1262, 1206, 992, 867, 506, 766, 546, 369, 505, 25,
	506, 638, 8, 1182, 505, 547, 638, 1004, 1020, 649,
	12, 1182, 1056, 1020, 1444, 1415, 1406, 1403, 577, 649,
	1367, 547, 1357, 1350, 1341, 1340, 412, 1335, 648, 1334,
	1333, 1332, 1167, 1313, 549, 10, 1277, 1272, 648, 1271,
	1270, 1212, 1203, 646, 864, 1149, 906, 1148, 392, 903,
	549, 1093, 1145, 650, 413, 1103, 1104, 1105, 1144, 1124,
	1115, 1092, 1089, 1087, 548, 1085, 1084, 1166, 1083, 1082,
	392, 1072, 1319, 1093, 1064, 11, 1055, 982, 413, 595,
	548, 7, 1312, 369, 368, 1235, 654, 1412, 567, 1399,
	1393, 1347, 596, 573, 547, 1195, 1158, 1114, 1080, 1079,
	1071, 1047, 392, 1045, 1040, 1093, 856, 638, 641, 1103,
	1104, 1105, 1093, 969, 412, 916, 350, 862, 656, 631,
	630, 629, 628, 549, 7, 627, 1318, 626, 403, 565,
	625, 624, 623, 622, 621, 620, 619, 1093, 618, 617,
	577, 1217, 908, 547, 1317, 616, 615, 559, 560, 561,
	614, 613, 612, 548, 611, 610, 577, 1177, 609, 547,
	597, 1059, 7, 559, 560, 561, 1093, 1414, 1379, 1378,
	547, 595, 549, 578, 1178, 865, 577, 500, 572, 547,
	563, 649, 577, 998, 1425, 547, 1147, 1146, 549, 1022,
	607, 418, 1369, 1359, 572, 1358, 472, 1236, 392, 549,
	648, 1093, 548, 1000, 912, 1103, 1104, 1105, 549, 1288,
	634, 1439, 546, 1405, 549, 1377, 986, 17, 548, 1075,
	1070, 1069, 1200, 1068, 577, 1067, 1026, 547, 591, 548,
	842, 976, 975, 819, 562, 1010, 853, 1009, 548, 1005,
	1008, 1107, 1007, 357, 548, 1404, 902, 853, 355, 751,
	351, 762, 356, 979, 568, 908, 549, 352, 503, 589,
	5, 860, 521, 1432, 759, 760, 1017, 1225, 858, 1456,
	578, 1222, 1438, 521, 1400, 1356, 632, 6, 16, 1389,
	600, 601, 602, 1164, 1134, 586, 548, 1455, 471, 1078,
	1433, 18, 1154, 749, 528, 1107, 498, 360, 535, 519,
	1109, 966, 1193, 958, 1221, 991, 359, 567, 1188, 359,
	519, 354, 573, 359, 1344, 750, 1346, 392, 1302, 1301,
	1093, 1159, 1109, 567, 1103, 1104, 1105, 935, 573, 545,
	1223, 16, 1298, 1162, 1392, 915, 1343, 1118, 1194, 1088,
	538, 1199, 1039, 538, 538, 18, 763, 566, 565, 569,
	570, 1119, 517, 1376, 1109, 550, 551, 552, 553, 554,
	555, 375, 588, 908, 565, 639, 592, 593, 651, 29,
	562, 645, 772, 608, 1424, 358, 635, 636, 358, 665,
	1160, 516, 358, 354, 644, 529, 562, 914, 1230, 657,
	965, 1107, 578, 562, 664, 571, 520, 654, 411, 1423,
	363, 1417, 29, 1355, 562, 1299, 658, 520, 578, 31,
	655, 564, 29, 367, 360, 818, 1427, 1454, 1180, 23,
	1014, 360, 765, 643, 642, 1093, 908, 31, 578, 547,
	964, 782, 780, 1459, 578, 755, 550, 551, 552, 553,
	554, 555, 562, 562, 562, 562, 562, 1345, 562, 665,
	1109, 518, 550, 551, 552, 553, 554, 555, 781, 756,
	757, 474, 518, 875, 664, 414, 562, 861, 754, 15,
	868, 473, 889, 568, 415, 652, 578, 939, 1100, 1101,
	1102, 872, 1094, 1095, 1096, 1097, 1098, 1099, 548, 568,
	349, 547, 944, 931, 658, 364, 954, 955, 956, 957,
	603, 658, 912, 863, 1094, 1095, 1096, 1097, 1098, 1099,
	1107, 909, 904, 970, 24, 412, 499, 917, 918, 919,
	920, 1065, 1066, 413, 599, 1452, 874, 552, 553, 554,
	555, 981, 1100, 1101, 1102, 362, 1094, 1095, 1096, 1097,
	1098, 1099, 968, 1094, 1095, 1096, 1097, 1098, 1099, 971,
	1430, 855, 365, 366, 974, 1309, 1028, 1304, 977, 417,
	978, 371, 416, 972, 973, 980, 566, 852, 402, 1109,
	556, 557, 558, 1099, 550, 551, 552, 553, 554, 555,
	905, 859, 566, 787, 497, 496, 556, 557, 558, 788,
	550, 551, 552, 553, 554, 555, 785, 515, 983, 1096,
	1097, 1098, 1099, 514, 984, 523, 555, 562, 522, 512,
	550, 551, 552, 553, 554, 555, 550, 551, 552, 553,
	554, 555, 401, 537, 598, 666, 537, 537, 1100, 1101,
	1102, 1361, 1094, 1095, 1096, 1097, 1098, 1099, 1287, 906,
	536, 1282, 903, 539, 540, 1257, 29, 1260, 1133, 547,
	19, 3, 1352, 985, 574, 839, 987, 1015, 550, 551,
	552, 553, 554, 555, 989, 990, 1012, 993, 924, 817,
	413, 382, 869, 1431, 1388, 1337, 790, 1077, 549, 1408,
	409, 562, 562, 562, 562, 562, 562, 562, 562, 562,
	562, 562, 562, 562, 562, 562, 562, 995, 1018, 1016,
	386, 408, 562, 391, 390, 639, 651, 645, 548, 507,
	910, 1073, 939, 939, 604, 385, 640, 1021, 383, 753,
	1371, 994, 533, 857, 580, 899, 1049, 534, 764, 875,
	761, 1043, 562, 636, 635, 908, 361, 644, 1029, 789,
	1048, 1027, 353, 513, 803, 531, 752, 1100, 1101, 1102,
	530, 1094, 1095, 1096, 1097, 1098, 1099, 524, 511, 562,
	942, 934, 1058, 1090, 932, 923, 922, 26, 1258, 927,
	913, 606, 527, 1061, 768, 541, 773, 1002, 1259, 1429,
	871, 1046, 562, 1006, 837, 21, 1011, 22, 370, 840,
	9, 2, 874, 1, 562, 0, 1106, 0, 939, 939,
	939, 0, 373, 373, 562, 0, 562, 1074, 0, 1062,
	1063, 562, 0, 0, 562, 1113, 0, 0, 855, 0,
	848, 0, 850, 562, 0, 836, 1126, 0, 562, 0,
	0, 0, 665, 0, 588, 1044, 802, 928, 0, 902,
	665, 0, 0, 0, 1142, 846, 1150, 664, 0, 562,
	1132, 1121, 1122, 1123, 0, 664, 0, 0, 1139, 29,
	0, 0, 0, 0, 562, 0, 0, 0, 0, 0,
	0, 29, 0, 29, 1151, 0, 0, 1157, 29, 1156,
	0, 0, 0, 0, 0, 562, 562, 0, 939, 939,
	0, 1170, 562, 1171, 665, 0, 929, 0, 1176, 926,
	0, 31, 1106, 1106, 663, 1184, 0, 0, 1179, 664,
	0, 562, 0, 790, 0, 0, 0, 0, 588, 0,
	0, 0, 1204, 875, 0, 1181, 1183, 0, 0, 0,
	0, 1215, 1216, 1218, 31, 0, 0, 562, 0, 1214,
	1210, 0, 562, 844, 31, 1234, 810, 31, 843, 0,
	838, 1229, 1207, 849, 0, 939, 939, 939, 939, 939,
	939, 939, 939, 939, 939, 939, 939, 939, 1237, 939,
	1239, 1106, 1106, 1106, 663, 1211, 789, 1243, 936, 1228,
	1241, 803, 1224, 1226, 1227, 0, 874, 0, 959, 1263,
	0, 0, 930, 1265, 0, 31, 0, 0, 0, 562,
	1273, 0, 562, 0, 1269, 0, 0, 1266, 0, 805,
	0, 665, 0, 791, 562, 1172, 1173, 1174, 1175, 804,
	0, 0, 786, 588, 562, 875, 664, 1280, 0, 1281,
	1300, 1293, 1294, 1303, 0, 0, 0, 0, 0, 29,
	29, 29, 29, 0, 0, 1291, 562, 562, 1292, 0,
	562, 1106, 1106, 562, 0, 0, 665, 562, 0, 1305,
	1306, 1307, 1308, 562, 0, 900, 1322, 1314, 0, 0,
	562, 664, 0, 802, 0, 0, 0, 1296, 0, 845,
	562, 562, 0, 0, 0, 1310, 1311, 0, 874, 847,
	0, 1330, 562, 1331, 0, 0, 925, 1214, 0, 0,
	0, 562, 1338, 562, 0, 1106, 1106, 1106, 1106, 1106,
	1106, 1106, 1106, 1106, 1106, 1106, 1106, 1106, 1342, 0,
	0, 0, 1106, 0, 0, 0, 0, 0, 562, 562,
	0, 509, 0, 0, 0, 562, 0, 0, 0, 525,
	0, 532, 0, 0, 0, 0, 0, 1036, 543, 1038,
	0, 1353, 0, 0, 0, 0, 0, 581, 582, 583,
	584, 585, 1365, 0, 0, 587, 0, 1374, 1375, 790,
	1366, 0, 1034, 0, 0, 0, 562, 562, 31, 1041,
	1042, 562, 562, 810, 0, 0, 562, 562, 605, 0,
	562, 0, 0, 0, 0, 0, 0, 562, 0, 0,
	562, 1387, 0, 0, 939, 0, 0, 0, 1385, 562,
	0, 0, 0, 936, 936, 790, 0, 0, 1394, 0,
	1380, 562, 790, 0, 562, 0, 0, 0, 0, 0,
	0, 562, 789, 0, 562, 0, 1410, 803, 1407, 0,
	0, 0, 562, 562, 562, 0, 805, 0, 0, 790,
	791, 0, 1106, 939, 0, 0, 804, 0, 1416, 786,
	0, 31, 0, 0, 1419, 1110, 1111, 1112, 1422, 0,
	0, 0, 1421, 0, 562, 1032, 1428, 0, 789, 1106,
	1037, 0, 0, 803, 0, 789, 0, 746, 0, 0,
	803, 1441, 1434, 1410, 1440, 0, 1451, 0, 0, 936,
	936, 936, 0, 0, 0, 0, 0, 0, 0, 1453,
	0, 562, 789, 0, 0, 0, 0, 803, 0, 0,
	0, 1460, 0, 0, 790, 0, 0, 373, 0, 802,
	0, 820, 821, 822, 823, 824, 825, 826, 827, 828,
	829, 830, 831, 832, 833, 834, 835, 0, 841, 0,
	0, 0, 0, 0, 0, 1190, 1191, 663, 0, 900,
	900, 900, 0, 0, 0, 663, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 802, 0, 0, 31, 0,
	898, 0, 802, 0, 0, 0, 0, 789, 0, 936,
	936, 31, 803, 0, 921, 0, 933, 0, 943, 945,
	950, 953, 0, 31, 0, 31, 1033, 0, 962, 802,
	31, 967, 0, 0, 0, 0, 1035, 0, 790, 663,
	0, 0, 1244, 1245, 1246, 1247, 1248, 1249, 1250, 1251,
	1252, 1253, 1254, 1255, 1256, 0, 1261, 0, 0, 810,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 790, 0, 31, 936, 936, 936, 936,
	936, 936, 936, 936, 936, 936, 936, 936, 936, 0,
	936, 0, 0, 0, 0, 0, 790, 31, 0, 0,
	0, 789, 0, 0, 802, 810, 803, 0, 577, 0,
	0, 547, 810, 0, 0, 0, 1278, 0, 0, 0,
	0, 577, 805, 0, 547, 0, 791, 0, 0, 0,
	0, 0, 804, 0, 0, 786, 789, 0, 0, 810,
	549, 803, 790, 392, 0, 900, 1093, 0, 509, 0,
	1103, 1104, 1105, 549, 0, 988, 663, 0, 0, 789,
	0, 0, 0, 0, 803, 0, 0, 543, 805, 0,
	548, 0, 791, 1003, 0, 805, 0, 31, 804, 791,
	0, 786, 0, 548, 0, 804, 577, 0, 786, 547,
	0, 31, 31, 31, 31, 0, 0, 0, 802, 0,
	0, 663, 805, 0, 0, 789, 791, 0, 0, 0,
	803, 0, 804, 0, 810, 786, 0, 0, 549, 0,
	577, 0, 0, 547, 0, 0, 0, 559, 560, 561,
	0, 0, 0, 802, 1187, 0, 0, 1024, 1025, 0,
	0, 0, 392, 1031, 563, 1093, 0, 1030, 548, 1103,
	1104, 1105, 549, 0, 31, 0, 802, 0, 572, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 1050, 0,
	0, 0, 0, 0, 0, 0, 0, 805, 0, 0,
	0, 791, 548, 0, 0, 0, 0, 804, 0, 0,
	786, 1391, 0, 0, 898, 898, 898, 577, 0, 900,
	547, 0, 802, 900, 559, 560, 561, 0, 810, 0,
	0, 392, 1023, 1076, 1093, 1108, 0, 1081, 1103, 1104,
	1105, 563, 0, 0, 876, 936, 0, 0, 0, 549,
	0, 886, 887, 888, 0, 572, 1107, 0, 1401, 0,
	1418, 587, 0, 810, 0, 0, 0, 950, 950, 950,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 548,
	578, 0, 0, 0, 1138, 0, 810, 0, 0, 1141,
	0, 805, 0, 578, 936, 791, 0, 0, 0, 0,
	0, 804, 0, 0, 786, 1153, 0, 567, 31, 0,
	0, 0, 573, 0, 0, 1109, 0, 0, 0, 0,
	0, 1163, 0, 0, 0, 0, 805, 0, 0, 0,
	791, 0, 810, 569, 570, 0, 804, 0, 0, 786,
	0, 0, 0, 1185, 1186, 0, 0, 1189, 565, 805,
	0, 1192, 0, 791, 0, 1107, 0, 0, 578, 804,
	1196, 0, 786, 0, 0, 31, 1202, 0, 0, 0,
	0, 0, 1209, 0, 0, 0, 0, 0, 0, 571,
	898, 0, 0, 0, 567, 0, 1219, 1220, 0, 573,
	0, 0, 578, 0, 0, 805, 1231, 0, 0, 791,
	0, 0, 0, 0, 0, 804, 0, 0, 786, 1240,
	569, 570, 1242, 0, 1109, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 565, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 1267,
	1268, 0, 0, 0, 0, 0, 0, 0, 1274, 0,
	0, 0, 0, 0, 0, 0, 571, 962, 0, 0,
	0, 0, 550, 551, 552, 553, 554, 555, 0, 578,
	0, 0, 564, 568, 0, 550, 551, 552, 553, 554,
	555, 0, 0, 1109, 0, 1003, 0, 0, 1003, 0,
	0, 0, 0, 1100, 1101, 1102, 996, 1094, 1095, 1096,
	1097, 1098, 1099, 0, 0, 0, 0, 0, 1315, 1316,
	0, 0, 0, 0, 1320, 1321, 0, 0, 0, 0,
	1325, 1326, 0, 0, 0, 0, 0, 1329, 0, 0,
	0, 0, 0, 0, 898, 0, 0, 0, 898, 0,
	550, 551, 552, 553, 554, 555, 0, 0, 0, 0,
	568, 0, 0, 1336, 0, 0, 0, 1339, 0, 0,
	0, 0, 0, 0, 0, 0, 566, 0, 0, 0,
	556, 557, 558, 0, 550, 551, 552, 553, 554, 555,
	0, 0, 0, 1348, 0, 0, 0, 0, 0, 0,
	0, 0, 1100, 1101, 1102, 0, 1094, 1095, 1096, 1097,
	1098, 1099, 0, 0, 0, 0, 0, 1360, 0, 1363,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 1370,
	1373, 0, 1003, 1003, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 1381, 1382,
	1383, 0, 0, 566, 0, 0, 0, 556, 557, 558,
	0, 550, 551, 552, 553, 554, 555, 0, 0, 0,
	0, 1100, 1101, 1102, 1140, 1094, 1095, 1096, 1097, 1098,
	1099, 0, 0, 0, 883, 884, 885, 0, 877, 878,
	879, 880, 881, 882, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 1413, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	962, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 1373, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 1450, 1450, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 662, 0, 1450, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	1450, 33, 34, 35, 36, 37, 38, 39, 40, 669,
	41, 42, 43, 670, 671, 672, 673, 674, 675, 676,
	44, 45, 677, 46, 47, 475, 48, 49, 50, 301,
	302, 476, 303, 304, 678, 51, 52, 53, 54, 55,
	679, 680, 56, 57, 305, 306, 58, 681, 59, 60,
	61, 62, 307, 682, 667, 683, 63, 64, 65, 66,
	477, 67, 68, 69, 684, 70, 71, 72, 73, 74,
	75, 685, 478, 76, 77, 78, 686, 687, 688, 668,
	689, 690, 691, 79, 80, 81, 82, 83, 84, 308,
	309, 85, 692, 86, 693, 87, 88, 89, 90, 91,
	694, 92, 93, 94, 695, 696, 95, 96, 97, 98,
	99, 697, 100, 101, 102, 698, 103, 104, 105, 699,
	106, 107, 108, 109, 310, 110, 111, 112, 311, 700,
	113, 701, 114, 115, 312, 116, 702, 117, 703, 118,
	479, 704, 480, 119, 120, 121, 705, 122, 313, 706,
	314, 123, 707, 124, 125, 126, 127, 128, 481, 129,
	130, 131, 132, 708, 133, 134, 135, 136, 137, 138,
	709, 139, 482, 315, 140, 141, 142, 143, 316, 317,
	710, 318, 711, 144, 483, 484, 145, 485, 146, 147,
	148, 149, 150, 712, 713, 151, 319, 486, 152, 487,
	714, 153, 154, 155, 715, 716, 156, 157, 158, 159,
	160, 161, 162, 163, 164, 165, 166, 167, 168, 169,
	170, 320, 488, 321, 171, 172, 322, 717, 173, 174,
	489, 175, 718, 323, 176, 324, 177, 178, 179, 719,
	180, 720, 721, 181, 182, 183, 722, 723, 184, 325,
	490, 185, 491, 326, 186, 187, 188, 189, 190, 191,
	192, 724, 193, 194, 327, 195, 328, 198, 196, 197,
	725, 199, 200, 201, 202, 203, 204, 205, 206, 329,
	207, 208, 209, 210, 726, 211, 212, 213, 214, 215,
	216, 217, 218, 219, 220, 221, 727, 222, 223, 492,
	224, 225, 226, 330, 227, 228, 229, 230, 231, 232,
	233, 234, 728, 235, 236, 237, 238, 239, 729, 240,
	241, 331, 242, 243, 493, 244, 245, 332, 246, 730,
	247, 248, 249, 250, 251, 252, 253, 254, 255, 256,
	257, 333, 731, 258, 259, 732, 260, 494, 261, 262,
	263, 264, 265, 733, 334, 335, 734, 735, 266, 267,
	336, 268, 337, 736, 269, 270, 271, 272, 273, 274,
	275, 737, 738, 276, 277, 278, 279, 280, 739, 740,
	281, 282, 283, 284, 285, 338, 339, 741, 286, 495,
	287, 288, 289, 290, 742, 743, 291, 744, 745, 292,
	293, 294, 295, 296, 297, 340, 341, 342, 343, 344,
	345, 346, 347, 348, 298, 299, 300, 662, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 660, 0, 0,
	0, 0, 33, 34, 35, 36, 37, 38, 39, 40,
	669, 41, 42, 43, 670, 671, 672, 673, 674, 675,
	676, 44, 45, 677, 46, 47, 475, 48, 49, 50,
	301, 302, 476, 303, 304, 678, 51, 52, 53, 54,
	55, 679, 680, 56, 57, 305, 306, 58, 681, 59,
	60, 61, 62, 307, 682, 667, 683, 63, 64, 65,
	66, 477, 67, 68, 69, 684, 70, 71, 72, 73,
	74, 75, 685, 478, 76, 77, 78, 686, 687, 688,
	668, 689, 690, 691, 79, 80, 81, 82, 83, 84,
	308, 309, 85, 692, 86, 693, 87, 88, 89, 90,
	91, 694, 92, 93, 94, 695, 696, 95, 96, 97,
	98, 99, 697, 100, 101, 102, 698, 103, 104, 105,
	699, 106, 107, 108, 109, 310, 110, 111, 112, 311,
	700, 113, 701, 114, 115, 312, 116, 702, 117, 703,
	118, 479, 704, 480, 119, 120, 121, 705, 122, 313,
	706, 314, 123, 707, 124, 125, 126, 127, 128, 481,
	129, 130, 131, 132, 708, 133, 134, 135, 136, 137,
	138, 709, 139, 482, 315, 140, 141, 142, 143, 316,
	317, 710, 318, 711, 144, 483, 484, 145, 485, 146,
	147, 148, 149, 150, 712, 713, 151, 319, 486, 152,
	487, 714, 153, 154, 155, 715, 716, 156, 157, 158,
	159, 160, 161, 162, 163, 164, 165, 166, 167, 168,
	169, 170, 320, 488, 321, 171, 172, 322, 717, 173,
	174, 489, 175, 718, 323, 176, 324, 177, 178, 179,
	719, 180, 720, 721, 181, 182, 183, 722, 723, 184,
	325, 490, 185, 491, 326, 186, 187, 188, 189, 190,
	191, 192, 724, 193, 194, 327, 195, 328, 198, 196,
	197, 725, 199, 200, 201, 202, 203, 204, 205, 206,
	329, 207, 208, 209, 210, 726, 211, 212, 213, 214,
	215, 216, 217, 218, 219, 220, 221, 727, 222, 223,
	492, 224, 225, 226, 330, 227, 228, 229, 230, 231,
	232, 233, 234, 728, 235, 236, 237, 238, 239, 729,
	240, 241, 331, 242, 243, 493, 244, 245, 332, 246,
	730, 247, 248, 249, 250, 251, 252, 253, 254, 255,
	256, 257, 333, 731, 258, 259, 732, 260, 494, 261,
	262, 263, 264, 265, 733, 334, 335, 734, 735, 266,
	267, 336, 268, 337, 736, 269, 270, 271, 272, 273,
	274, 275, 737, 738, 276, 277, 278, 279, 280, 739,
	740, 281, 282, 283, 284, 285, 338, 339, 741, 286,
	495, 287, 288, 289, 290, 742, 743, 291, 744, 745,
	292, 293, 294, 295, 296, 297, 340, 341, 342, 343,
	344, 345, 346, 347, 348, 298, 299, 300, 410, 397,
	413, 399, 400, 392, 412, 0, 0, 0, 0, 0,
	0, 0, 0, 33, 34, 35, 36, 37, 38, 39,
	40, 894, 41, 42, 43, 0, 0, 0, 0, 389,
	0, 0, 44, 45, 0, 46, 47, 475, 48, 49,
	50, 301, 452, 476, 453, 454, 0, 51, 52, 53,
	54, 55, 407, 432, 56, 57, 455, 456, 58, 0,
	59, 60, 61, 62, 440, 0, 420, 0, 63, 64,
	65, 66, 477, 67, 68, 69, 0, 70, 71, 72,
	73, 74, 75, 0, 478, 76, 77, 78, 430, 421,
	426, 431, 422, 423, 427, 79, 80, 81, 82, 83,
	84, 457, 458, 85, 0, 86, 0, 87, 88, 89,
	90, 91, 0, 92, 93, 94, 895, 0, 95, 96,
	451, 98, 99, 0, 100, 101, 102, 0, 103, 104,
	105, 0, 106, 107, 108, 109, 388, 110, 111, 112,
	433, 405, 113, 0, 114, 115, 459, 116, 0, 117,
	0, 118, 479, 0, 480, 119, 120, 121, 0, 122,
	441, 0, 314, 123, 0, 124, 125, 126, 127, 128,
	481, 129, 130, 131, 132, 0, 133, 134, 135, 136,
	137, 138, 0, 139, 482, 315, 140, 141, 142, 143,
	460, 461, 0, 419, 0, 144, 483, 484, 145, 485,
	146, 147, 148, 149, 150, 0, 0, 151, 442, 486,
	152, 487, 0, 153, 154, 155, 424, 425, 156, 157,
	158, 159, 160, 161, 162, 163, 164, 165, 166, 167,
	168, 169, 170, 462, 488, 463, 171, 172, 322, 379,
	173, 174, 489, 175, 406, 439, 176, 464, 177, 178,
	179, 0, 180, 0, 0, 393, 182, 183, 0, 0,
	184, 325, 490, 185, 491, 434, 186, 187, 188, 189,
	190, 191, 192, 0, 193, 194, 435, 195, 328, 198,
	196, 197, 0, 199, 200, 201, 202, 203, 204, 205,
	206, 465, 207, 208, 209, 210, 0, 211, 212, 213,
	214, 215, 216, 217, 218, 219, 220, 221, 0, 222,
	223, 492, 224, 225, 226, 394, 227, 228, 229, 230,
	231, 232, 233, 234, 0, 235, 236, 237, 238, 239,
	428, 240, 241, 331, 242, 243, 493, 244, 245, 466,
	246, 0, 247, 248, 249, 250, 251, 252, 253, 254,
	255, 256, 257, 436, 0, 258, 259, 0, 260, 494,
	261, 262, 263, 264, 265, 0, 467, 468, 0, 0,
	266, 267, 437, 268, 438, 404, 269, 270, 271, 272,
	273, 274, 275, 0, 0, 276, 277, 278, 279, 280,
	429, 0, 281, 282, 283, 284, 285, 338, 469, 893,
	286, 495, 287, 288, 289, 290, 0, 0, 291, 0,
	0, 292, 293, 294, 295, 296, 297, 340, 443, 444,
	445, 446, 447, 448, 449, 450, 298, 299, 300, 380,
	0, 0, 0, 0, 0, 0, 0, 376, 377, 896,
	0, 0, 0, 0, 0, 0, 384, 891, 410, 397,
	413, 399, 400, 392, 412, 0, 0, 0, 0, 0,
	0, 0, 0, 33, 34, 35, 36, 37, 38, 39,
	40, 0, 41, 42, 43, 0, 0, 0, 0, 389,
	0, 0, 44, 45, 0, 46, 47, 475, 48, 49,
	50, 301, 452, 476, 453, 454, 946, 51, 52, 53,
	54, 55, 407, 432, 56, 57, 455, 456, 58, 0,
	59, 60, 61, 62, 440, 0, 420, 0, 63, 64,
	65, 66, 477, 67, 68, 69, 0, 70, 71, 72,
	73, 74, 75, 0, 478, 76, 77, 78, 430, 421,
	426, 431, 422, 423, 427, 79, 80, 81, 82, 83,
	84, 457, 458, 85, 0, 86, 0, 87, 88, 89,
	90, 91, 0, 92, 93, 94, 0, 0, 95, 96,
	451, 98, 99, 0, 100, 101, 102, 0, 103, 104,
	105, 0, 106, 107, 108, 109, 388, 110, 111, 112,
	433, 405, 113, 0, 114, 115, 459, 116, 0, 117,
	0, 118, 479, 951, 480, 119, 120, 121, 0, 122,
	441, 0, 314, 123, 0, 124, 125, 126, 127, 128,
	481, 129, 130, 131, 132, 0, 133, 134, 135, 136,
	137, 138, 0, 139, 482, 315, 140, 141, 142, 143,
	460, 461, 0, 419, 0, 144, 483, 484, 145, 485,
	146, 147, 148, 149, 150, 0, 947, 151, 442, 486,
	152, 487, 0, 153, 154, 155, 424, 425, 156, 157,
	158, 159, 160, 161, 162, 163, 164, 165, 166, 167,
	168, 169, 170, 462, 488, 463, 171, 172, 322, 379,
	173, 174, 489, 175, 406, 439, 176, 464, 177, 178,
	179, 0, 180, 0, 0, 393, 182, 183, 0, 0,
	184, 325, 490, 185, 491, 434, 186, 187, 188, 189,
	190, 191, 192, 0, 193, 194, 435, 195, 328, 198,
	196, 197, 0, 199, 200, 201, 202, 203, 204, 205,
	206, 465, 207, 208, 209, 210, 0, 211, 212, 213,
	214, 215, 216, 217, 218, 219, 220, 221, 0, 222,
	223, 492, 224, 225, 226, 394, 227, 228, 229, 230,
	231, 232, 233, 234, 0, 235, 236, 237, 238, 239,
	428, 240, 241, 331, 242, 243, 493, 244, 245, 466,
	246, 0, 247, 248, 249, 250, 251, 252, 253, 254,
	255, 256, 257, 436, 0, 258, 259, 0, 260, 494,
	261, 262, 263, 264, 265, 0, 467, 468, 0, 948,
	266, 267, 437, 268, 438, 404, 269, 270, 271, 272,
	273, 274, 275, 0, 0, 276, 277, 278, 279, 280,
	429, 0, 281, 282, 283, 284, 285, 338, 469, 0,
	286, 495, 287, 288, 289, 290, 0, 0, 291, 0,
	0, 292, 293, 294, 295, 296, 297, 340, 443, 444,
	445, 446, 447, 448, 449, 450, 298, 299, 300, 380,
	0, 0, 0, 0, 0, 0, 0, 376, 377, 410,
	397, 413, 399, 400, 392, 412, 384, 0, 0, 0,
	0, 0, 0, 0, 33, 34, 35, 36, 37, 38,
	39, 40, 0, 41, 42, 43, 0, 0, 0, 0,
	389, 0, 0, 44, 45, 0, 46, 47, 475, 48,
	49, 50, 301, 452, 476, 453, 454, 0, 51, 52,
	53, 54, 55, 407, 432, 56, 57, 455, 456, 58,
	0, 59, 60, 61, 62, 440, 0, 420, 0, 63,
	64, 65, 66, 477, 67, 68, 69, 0, 70, 71,
	72, 73, 74, 75, 0, 478, 76, 77, 78, 430,
	421, 426, 431, 422, 423, 427, 79, 80, 81, 82,
	83, 84, 457, 458, 85, 0, 86, 0, 87, 88,
	89, 90, 91, 0, 92, 93, 94, 0, 0, 95,
	96, 451, 98, 99, 0, 100, 101, 102, 0, 103,
	104, 105, 0, 106, 107, 108, 109, 388, 110, 111,
	112, 433, 405, 113, 0, 114, 115, 459, 116, 0,
	117, 0, 118, 479, 0, 480, 119, 120, 121, 0,
	122, 441, 0, 314, 123, 0, 124, 125, 126, 127,
	128, 481, 129, 130, 131, 132, 0, 133, 134, 135,
	136, 137, 138, 0, 139, 482, 315, 140, 141, 142,
	143, 460, 461, 0, 419, 0, 144, 483, 484, 145,
	485, 146, 147, 148, 149, 150, 0, 0, 151, 442,
	486, 152, 487, 0, 153, 154, 155, 424, 425, 156,
	157, 158, 159, 160, 161, 162, 163, 164, 165, 166,
	167, 168, 169, 170, 462, 488, 463, 171, 172, 322,
	379, 173, 174, 489, 175, 406, 439, 176, 464, 177,
	178, 179, 0, 180, 0, 0, 393, 182, 183, 0,
	0, 184, 325, 490, 185, 491, 434, 186, 187, 188,
	189, 190, 191, 192, 0, 193, 194, 435, 195, 328,
	198, 196, 197, 0, 199, 200, 201, 202, 203, 204,
	205, 206, 465, 207, 208, 209, 210, 0, 211, 212,
	213, 214, 215, 216, 217, 218, 219, 220, 221, 0,
	222, 223, 492, 224, 225, 226, 394, 227, 228, 229,
	230, 231, 232, 233, 234, 0, 235, 236, 237, 238,
	239, 428, 240, 241, 331, 242, 243, 493, 244, 245,
	466, 246, 0, 247, 248, 249, 250, 251, 252, 253,
	254, 255, 256, 257, 436, 0, 258, 259, 0, 260,
	494, 261, 262, 263, 264, 265, 0, 467, 468, 0,
	0, 266, 267, 437, 268, 438, 404, 269, 270, 271,
	272, 273, 274, 275, 0, 0, 276, 277, 278, 279,
	280, 429, 0, 281, 282, 283, 284, 285, 338, 469,
	0, 286, 495, 287, 288, 289, 290, 0, 0, 291,
	0, 0, 292, 293, 294, 295, 296, 297, 340, 443,
	444, 445, 446, 447, 448, 449, 450, 298, 299, 300,
	380, 0, 0, 0, 0, 0, 0, 0, 376, 377,
	410, 397, 413, 399, 400, 392, 412, 384, 1264, 0,
	0, 0, 0, 0, 0, 33, 34, 35, 36, 37,
	38, 39, 40, 0, 41, 42, 43, 0, 0, 0,
	0, 389, 0, 0, 44, 45, 0, 46, 47, 475,
	48, 49, 50, 301, 452, 476, 453, 454, 0, 51,
	52, 53, 54, 55, 407, 432, 56, 57, 455, 456,
	58, 0, 59, 60, 61, 62, 440, 0, 420, 0,
	63, 64, 65, 66, 477, 67, 68, 69, 0, 70,
	71, 72, 73, 74, 75, 0, 478, 76, 77, 78,
	430, 421, 426, 431, 422, 423, 427, 79, 80, 81,
	82, 83, 84, 457, 458, 85, 0, 86, 0, 87,
	88, 89, 90, 91, 0, 92, 93, 94, 0, 0,
	95, 96, 451, 98, 99, 0, 100, 101, 102, 0,
	103, 104, 105, 0, 106, 107, 108, 109, 388, 110,
	111, 112, 433, 405, 113, 0, 114, 115, 459, 116,
	0, 117, 0, 118, 479, 0, 480, 119, 120, 121,
	0, 122, 441, 0, 314, 123, 0, 124, 125, 126,
	127, 128, 481, 129, 130, 131, 132, 0, 133, 134,
	135, 136, 137, 138, 0, 139, 482, 315, 140, 141,
	142, 143, 460, 461, 0, 419, 0, 144, 483, 484,
	145, 485, 146, 147, 148, 149, 150, 0, 0, 151,
	442, 486, 152, 487, 0, 153, 154, 155, 424, 425,
	156, 157, 158, 159, 160, 161, 162, 163, 164, 165,
	166, 167, 168, 169, 170, 462, 488, 463, 171, 172,
	322, 379, 173, 174, 489, 175, 406, 439, 176, 464,
	177, 178, 179, 0, 180, 0, 0, 393, 182, 183,
	0, 0, 184, 325, 490, 185, 491, 434, 186, 187,
	188, 189, 190, 191, 192, 0, 193, 194, 435, 195,
	328, 198, 196, 197, 0, 199, 200, 201, 202, 203,
	204, 205, 206, 465, 207, 208, 209, 210, 0, 211,
	212, 213, 214, 215, 216, 217, 218, 219, 220, 221,
	0, 222, 223, 492, 224, 225, 226, 394, 227, 228,
	229, 230, 231, 232, 233, 234, 0, 235, 236, 237,
	238, 239, 428, 240, 241, 331, 242, 243, 493, 244,
	245, 466, 246, 0, 247, 248, 249, 250, 251, 252,
	253, 254, 255, 256, 257, 436, 0, 258, 259, 0,
	260, 494, 261, 262, 263, 264, 265, 0, 467, 468,
	0, 0, 266, 267, 437, 268, 438, 404, 269, 270,
	271, 272, 273, 274, 275, 0, 0, 276, 277, 278,
	279, 280, 429, 0, 281, 282, 283, 284, 285, 338,
	469, 0, 286, 495, 287, 288, 289, 290, 0, 0,
	291, 0, 0, 292, 293, 294, 295, 296, 297, 340,
	443, 444, 445, 446, 447, 448, 449, 450, 298, 299,
	300, 380, 0, 0, 0, 0, 0, 0, 0, 376,
	377, 410, 397, 413, 399, 400, 392, 412, 384, 1205,
	0, 0, 0, 0, 0, 0, 33, 34, 35, 36,
	37, 38, 39, 40, 0, 41, 42, 43, 0, 0,
	0, 0, 389, 0, 0, 44, 45, 0, 46, 47,
	475, 48, 49, 50, 301, 452, 476, 453, 454, 0,
	51, 52, 53, 54, 55, 407, 432, 56, 57, 455,
	456, 58, 0, 59, 60, 61, 62, 440, 0, 420,
	0, 63, 64, 65, 66, 477, 67, 68, 69, 0,
	70, 71, 72, 73, 74, 75, 0, 478, 76, 77,
	78, 430, 421, 426, 431, 422, 423, 427, 79, 80,
	81, 82, 83, 84, 457, 458, 85, 0, 86, 0,
	87, 88, 89, 90, 91, 0, 92, 93, 94, 0,
	0, 95, 96, 451, 98, 99, 0, 100, 101, 102,
	0, 103, 104, 105, 0, 106, 107, 108, 109, 388,
	110, 111, 112, 433, 405, 113, 0, 114, 115, 459,
	116, 0, 117, 0, 118, 479, 0, 480, 119, 120,
	121, 0, 122, 441, 0, 314, 123, 0, 124, 125,
	126, 127, 128, 481, 129, 130, 131, 132, 0, 133,
	134, 135, 136, 137, 138, 0, 139, 482, 315, 140,
	141, 142, 143, 460, 461, 0, 419, 0, 144, 483,
	484, 145, 485, 146, 147, 148, 149, 150, 0, 0,
	151, 442, 486, 152, 487, 0, 153, 154, 155, 424,
	425, 156, 157, 158, 159, 160, 161, 162, 163, 164,
	165, 166, 167, 168, 169, 170, 462, 488, 463, 171,
	172, 322, 379, 173, 174, 489, 175, 406, 439, 176,
	464, 177, 178, 179, 0, 180, 0, 0, 393, 182,
	183, 0, 0, 184, 325, 490, 185, 491, 434, 186,
	187, 188, 189, 190, 191, 192, 0, 193, 194, 435,
	195, 328, 198, 196, 197, 0, 199, 200, 201, 202,
	203, 204, 205, 206, 465, 207, 208, 209, 210, 0,
	211, 212, 213, 214, 215, 216, 217, 218, 219, 220,
	221, 0, 222, 223, 492, 224, 225, 226, 394, 227,
	228, 229, 230, 231, 232, 233, 234, 8, 235, 236,
	237, 238, 239, 428, 240, 241, 331, 242, 243, 493,
	244, 245, 466, 246, 0, 247, 248, 249, 250, 251,
	252, 253, 254, 255, 256, 257, 436, 0, 258, 259,
	10, 260, 494, 261, 262, 263, 264, 265, 0, 467,
	468, 0, 0, 266, 267, 437, 268, 438, 404, 269,
	270, 271, 272, 273, 274, 275, 0, 0, 276, 277,
	278, 279, 280, 429, 0, 281, 282, 283, 284, 285,
	590, 469, 0, 286, 495, 287, 288, 289, 290, 0,
	0, 291, 0, 0, 292, 293, 294, 295, 296, 297,
	340, 443, 444, 445, 446, 447, 448, 449, 450, 298,
	299, 300, 380, 0, 0, 0, 0, 0, 0, 0,
	376, 377, 410, 397, 413, 399, 400, 392, 412, 384,
	0, 0, 0, 0, 0, 0, 0, 33, 34, 35,
	36, 37, 38, 39, 40, 0, 41, 42, 43, 0,
	0, 0, 0, 389, 0, 0, 44, 45, 0, 46,
	47, 475, 48, 49, 50, 301, 452, 476, 453, 454,
	0, 51, 52, 53, 54, 55, 407, 432, 56, 57,
	455, 456, 58, 0, 59, 60, 61, 62, 440, 0,
	420, 0, 63, 64, 65, 66, 477, 67, 68, 69,
	0, 70, 71, 72, 73, 74, 75, 0, 478, 76,
	77, 78, 430, 421, 426, 431, 422, 423, 427, 79,
	80, 81, 82, 83, 84, 457, 458, 85, 0, 86,
	0, 87, 88, 89, 90, 91, 0, 92, 93, 94,
	0, 0, 95, 96, 451, 98, 99, 0, 100, 101,
	102, 0, 103, 104, 105, 0, 106, 107, 108, 109,
	388, 110, 111, 112, 433, 405, 113, 0, 114, 115,
	459, 116, 0, 117, 0, 118, 479, 0, 480, 119,
	120, 121, 0, 122, 441, 0, 314, 123, 0, 124,
	125, 126, 127, 128, 481, 129, 130, 131, 132, 0,
	133, 134, 135, 136, 137, 138, 0, 139, 482, 315,
	140, 141, 142, 143, 460, 461, 0, 419, 0, 144,
	483, 484, 145, 485, 146, 147, 148, 149, 150, 0,
	0, 151, 442, 486, 152, 487, 0, 153, 154, 155,
	424, 425, 156, 157, 158, 159, 160, 161, 162, 163,
	164, 165, 166, 167, 168, 169, 170, 462, 488, 463,
	171, 172, 322, 379, 173, 174, 489, 175, 406, 439,
	176, 464, 177, 178, 179, 0, 180, 0, 0, 393,
	182, 183, 0, 0, 184, 325, 490, 185, 491, 434,
	186, 187, 188, 189, 190, 191, 192, 0, 193, 194,
	435, 195, 328, 198, 196, 197, 0, 199, 200, 201,
	202, 203, 204, 205, 206, 465, 207, 208, 209, 210,
	0, 211, 212, 213, 214, 215, 216, 217, 218, 219,
	220, 221, 0, 222, 223, 492, 224, 225, 226, 394,
	227, 228, 229, 230, 231, 232, 233, 234, 0, 235,
	236, 237, 238, 239, 428, 240, 241, 331, 242, 243,
	493, 244, 245, 466, 246, 0, 247, 248, 249, 250,
	251, 252, 253, 254, 255, 256, 257, 436, 0, 258,
	259, 0, 260, 494, 261, 262, 263, 264, 265, 0,
	467, 468, 0, 0, 266, 267, 437, 268, 438, 404,
	269, 270, 271, 272, 273, 274, 275, 0, 0, 276,
	277, 278, 279, 280, 429, 0, 281, 282, 283, 284,
	285, 338, 469, 0, 286, 495, 287, 288, 289, 290,
	0, 0, 291, 0, 0, 292, 293, 294, 295, 296,
	297, 340, 443, 444, 445, 446, 447, 448, 449, 450,
	298, 299, 300, 380, 0, 0, 0, 0, 0, 0,
	0, 376, 377, 410, 397, 413, 399, 400, 392, 412,
	384, 890, 0, 0, 0, 0, 0, 0, 33, 34,
	35, 36, 37, 38, 39, 40, 0, 41, 42, 43,
	0, 0, 0, 0, 389, 0, 0, 44, 45, 0,
	46, 47, 475, 48, 49, 50, 301, 452, 476, 453,
	454, 0, 51, 52, 53, 54, 55, 407, 432, 56,
	57, 455, 456, 58, 0, 59, 60, 61, 62, 440,
	0, 420, 0, 63, 64, 65, 66, 477, 67, 68,
	69, 0, 70, 71, 72, 73, 74, 75, 0, 478,
	76, 77, 78, 430, 421, 426, 431, 422, 423, 427,
	79, 80, 81, 82, 83, 84, 457, 458, 85, 0,
	86, 0, 87, 88, 89, 90, 91, 0, 92, 93,
	94, 0, 0, 95, 96, 451, 98, 99, 0, 100,
	101, 102, 0, 103, 104, 105, 0, 106, 107, 108,
	109, 388, 110, 111, 112, 433, 405, 113, 0, 114,
	115, 459, 116, 0, 117, 0, 118, 479, 0, 480,
	119, 120, 121, 0, 122, 441, 0, 314, 123, 0,
	124, 125, 126, 127, 128, 481, 129, 130, 131, 132,
	0, 133, 134, 135, 136, 137, 138, 0, 139, 482,
	315, 140, 141, 142, 143, 460, 461, 0, 419, 0,
	144, 483, 484, 145, 485, 146, 147, 148, 149, 150,
	0, 0, 151, 442, 486, 152, 487, 0, 153, 154,
	155, 424, 425, 156, 157, 158, 159, 160, 161, 162,
	163, 164, 165, 166, 167, 168, 169, 170, 462, 488,
	463, 171, 172, 322, 379, 173, 174, 489, 175, 406,
	439, 176, 464, 177, 178, 179, 0, 180, 0, 0,
	393, 182, 183, 0, 0, 184, 325, 490, 185, 491,
	434, 186, 187, 188, 189, 190, 191, 192, 0, 193,
	194, 435, 195, 328, 198, 196, 197, 0, 199, 200,
	201, 202, 203, 204, 205, 206, 465, 207, 208, 209,
	210, 0, 211, 212, 213, 214, 215, 216, 217, 218,
	219, 220, 221, 0, 222, 223, 492, 224, 225, 226,
	394, 227, 228, 229, 230, 231, 232, 233, 234, 0,
	235, 236, 237, 238, 239, 428, 240, 241, 331, 242,
	243, 493, 244, 245, 466, 246, 0, 247, 248, 249,
	250, 251, 252, 253, 254, 255, 256, 257, 436, 0,
	258, 259, 0, 260, 494, 261, 262, 263, 264, 265,
	0, 467, 468, 0, 0, 266, 267, 437, 268, 438,
	404, 269, 270, 271, 272, 273, 274, 275, 0, 0,
	276, 277, 278, 279, 280, 429, 0, 281, 282, 283,
	284, 285, 338, 469, 0, 286, 495, 287, 288, 289,
	290, 0, 0, 291, 0, 0, 292, 293, 294, 295,
	296, 297, 340, 443, 444, 445, 446, 447, 448, 449,
	450, 298, 299, 300, 380, 0, 0, 0, 0, 0,
	0, 0, 376, 377, 0, 0, 0, 0, 0, 595,
	870, 384, 410, 397, 413, 399, 400, 392, 412, 0,
	0, 0, 0, 0, 0, 0, 0, 33, 34, 35,
	36, 37, 38, 39, 40, 0, 41, 42, 43, 0,
	0, 0, 0, 389, 0, 0, 44, 45, 0, 46,
	47, 475, 48, 49, 50, 301, 452, 476, 453, 454,
	0, 51, 52, 53, 54, 55, 407, 432, 56, 57,
	455, 456, 58, 0, 59, 60, 61, 62, 440, 0,
	420, 0, 63, 64, 65, 66, 477, 67, 68, 69,
	0, 70, 71, 72, 73, 74, 75, 0, 478, 76,
	77, 78, 430, 421, 426, 431, 422, 423, 427, 79,
	80, 81, 82, 83, 84, 457, 458, 85, 0, 86,
	0, 87, 88, 89, 90, 91, 0, 92, 93, 94,
	0, 0, 95, 96, 451, 98, 99, 0, 100, 101,
	102, 0, 103, 104, 105, 0, 106, 107, 108, 109,
	388, 110, 111, 112, 433, 405, 113, 0, 114, 115,
	459, 116, 0, 117, 0, 118, 479, 0, 480, 119,
	120, 121, 0, 122, 441, 0, 314, 123, 0, 124,
	125, 126, 127, 128, 481, 129, 130, 131, 132, 0,
	133, 134, 135, 136, 137, 138, 0, 139, 482, 315,
	140, 141, 142, 143, 460, 461, 0, 419, 0, 144,
	483, 484, 145, 485, 146, 147, 148, 149, 150, 0,
	0, 151, 442, 486, 152, 487, 0, 153, 154, 155,
	424, 425, 156, 157, 158, 159, 160, 161, 162, 163,
	164, 165, 166, 167, 168, 169, 170, 462, 488, 463,
	171, 172, 322, 379, 173, 174, 489, 175, 406, 439,
	176, 464, 177, 178, 179, 0, 180, 0, 0, 393,
	182, 183, 0, 0, 184, 325, 490, 185, 491, 434,
	186, 187, 188, 189, 190, 191, 192, 0, 193, 194,
	435, 195, 328, 198, 196, 197, 0, 199, 200, 201,
	202, 203, 204, 205, 206, 465, 207, 208, 209, 210,
	0, 211, 212, 213, 214, 215, 216, 217, 218, 219,
	220, 221, 0, 222, 223, 492, 224, 225, 226, 394,
	227, 228, 229, 230, 231, 232, 233, 234, 0, 235,
	236, 237, 238, 239, 428, 240, 241, 331, 242, 243,
	493, 244, 245, 466, 246, 0, 247, 248, 249, 250,
	251, 252, 253, 254, 255, 256, 257, 436, 0, 258,
	259, 0, 260, 494, 261, 262, 263, 264, 265, 0,
	467, 468, 0, 0, 266, 267, 437, 268, 438, 404,
	269, 270, 271, 272, 273, 274, 275, 0, 0, 276,
	277, 278, 279, 280, 429, 0, 281, 282, 283, 284,
	285, 338, 469, 1213, 286, 495, 287, 288, 289, 290,
	0, 0, 291, 0, 0, 292, 293, 294, 295, 296,
	297, 340, 443, 444, 445, 446, 447, 448, 449, 450,
	298, 299, 300, 380, 0, 0, 0, 0, 0, 0,
	0, 376, 377, 410, 397, 413, 399, 400, 392, 412,
	384, 0, 0, 0, 0, 0, 0, 0, 33, 34,
	35, 36, 37, 38, 39, 40, 0, 41, 42, 43,
	0, 0, 0, 0, 389, 0, 0, 44, 45, 0,
	46, 47, 475, 48, 49, 50, 301, 452, 476, 453,
	454, 0, 51, 52, 53, 54, 55, 407, 432, 56,
	57, 455, 456, 58, 0, 59, 60, 61, 62, 440,
	0, 420, 0, 63, 64, 65, 66, 477, 67, 68,
	69, 0, 70, 71, 72, 73, 74, 75, 0, 478,
	76, 77, 78, 430, 421, 426, 431, 422, 423, 427,
	79, 80, 81, 82, 83, 84, 457, 458, 85, 0,
	86, 0, 87, 88, 89, 90, 91, 0, 92, 93,
	94, 0, 0, 95, 96, 451, 98, 99, 0, 100,
	101, 102, 0, 103, 104, 105, 0, 106, 107, 108,
	109, 388, 110, 111, 112, 433, 405, 113, 0, 114,
	115, 459, 116, 0, 117, 0, 118, 479, 951, 480,
	119, 120, 121, 0, 122, 441, 0, 314, 123, 0,
	124, 125, 126, 127, 128, 481, 129, 130, 131, 132,
	0, 133, 134, 135, 136, 137, 138, 0, 139, 482,
	315, 140, 141, 142, 143, 460, 461, 0, 419, 0,
	144, 483, 484, 145, 485, 146, 147, 148, 149, 150,
	0, 0, 151, 442, 486, 152, 487, 0, 153, 154,
	155, 424, 425, 156, 157, 158, 159, 160, 161, 162,
	163, 164, 165, 166, 167, 168, 169, 170, 462, 488,
	463, 171, 172, 322, 379, 173, 174, 489, 175, 406,
	439, 176, 464, 177, 178, 179, 0, 180, 0, 0,
	393, 182, 183, 0, 0, 184, 325, 490, 185, 491,
	434, 186, 187, 188, 189, 190, 191, 192, 0, 193,
	194, 435, 195, 328, 198, 196, 197, 0, 199, 200,
	201, 202, 203, 204, 205, 206, 465, 207, 208, 209,
	210, 0, 211, 212, 213, 214, 215, 216, 217, 218,
	219, 220, 221, 0, 222, 223, 492, 224, 225, 226,
	394, 227, 228, 229, 230, 231, 232, 233, 234, 0,
	235, 236, 237, 238, 239, 428, 240, 241, 331, 242,
	243, 493, 244, 245, 466, 246, 0, 247, 248, 249,
	250, 251, 252, 253, 254, 255, 256, 257, 436, 0,
	258, 259, 0, 260, 494, 261, 262, 263, 264, 265,
	0, 467, 468, 0, 0, 266, 267, 437, 268, 438,
	404, 269, 270, 271, 272, 273, 274, 275, 0, 0,
	276, 277, 278, 279, 280, 429, 0, 281, 282, 283,
	284, 285, 338, 469, 0, 286, 495, 287, 288, 289,
	290, 0, 0, 291, 0, 0, 292, 293, 294, 295,
	296, 297, 340, 443, 444, 445, 446, 447, 448, 449,
	450, 298, 299, 300, 380, 0, 0, 0, 0, 0,
	0, 0, 376, 377, 410, 397, 413, 399, 400, 392,
	412, 384, 0, 0, 0, 0, 0, 0, 0, 33,
	34, 35, 36, 37, 38, 39, 40, 0, 41, 42,
	43, 0, 0, 0, 0, 389, 0, 0, 44, 45,
	0, 46, 47, 475, 48, 49, 50, 301, 452, 476,
	453, 454, 0, 51, 52, 53, 54, 55, 407, 432,
	56, 57, 455, 456, 58, 0, 59, 60, 61, 62,
	440, 0, 420, 0, 63, 64, 65, 66, 477, 67,
	68, 69, 0, 70, 71, 72, 73, 74, 75, 0,
	478, 76, 77, 78, 430, 421, 426, 431, 422, 423,
	427, 79, 80, 81, 82, 83, 84, 457, 458, 85,
	510, 86, 0, 87, 88, 89, 90, 91, 0, 92,
	93, 94, 0, 0, 95, 96, 451, 98, 99, 0,
	100, 101, 102, 0, 103, 104, 105, 0, 106, 107,
	108, 109, 388, 110, 111, 112, 433, 405, 113, 0,
	114, 115, 459, 116, 0, 117, 0, 118, 479, 0,
	480, 119, 120, 121, 0, 122, 441, 0, 314, 123,
	0, 124, 125, 126, 127, 128, 481, 129, 130, 131,
	132, 0, 133, 134, 135, 136, 137, 138, 0, 139,
	482, 315, 140, 141, 142, 143, 460, 461, 0, 419,
	0, 144, 483, 484, 145, 485, 146, 147, 148, 149,
	150, 0, 0, 151, 442, 486, 152, 487, 0, 153,
	154, 155, 424, 425, 156, 157, 158, 159, 160, 161,
	162, 163, 164, 165, 166, 167, 168, 169, 170, 462,
	488, 463, 171, 172, 322, 379, 173, 174, 489, 175,
	406, 439, 176, 464, 177, 178, 179, 0, 180, 0,
	0, 393, 182, 183, 0, 0, 184, 325, 490, 185,
	491, 434, 186, 187, 188, 189, 190, 191, 192, 0,
	193, 194, 435, 195, 328, 198, 196, 197, 0, 199,
	200, 201, 202, 203, 204, 205, 206, 465, 207, 208,
	209, 210, 0, 211, 212, 213, 214, 215, 216, 217,
	218, 219, 220, 221, 0, 222, 223, 492, 224, 225,
	226, 394, 227, 228, 229, 230, 231, 232, 233, 234,
	0, 235, 236, 237, 238, 239, 428, 240, 241, 331,
	242, 243, 493, 244, 245, 466, 246, 0, 247, 248,
	249, 250, 251, 252, 253, 254, 255, 256, 257, 436,
	0, 258, 259, 0, 260, 494, 261, 262, 263, 264,
	265, 0, 467, 468, 0, 0, 266, 267, 437, 268,
	438, 404, 269, 270, 271, 272, 273, 274, 275, 0,
	0, 276, 277, 278, 279, 280, 429, 0, 281, 282,
	283, 284, 285, 338, 469, 0, 286, 495, 287, 288,
	289, 290, 0, 0, 291, 0, 0, 292, 293, 294,
	295, 296, 297, 340, 443, 444, 445, 446, 447, 448,
	449, 450, 298, 299, 300, 380, 0, 0, 0, 0,
	0, 0, 0, 376, 377, 410, 397, 413, 399, 400,
	392, 412, 384, 0, 0, 0, 0, 0, 0, 0,
	33, 34, 35, 36, 37, 38, 39, 40, 0, 41,
	42, 43, 0, 0, 0, 0, 389, 0, 0, 44,
	45, 0, 46, 47, 475, 48, 49, 50, 301, 452,
	476, 453, 454, 0, 51, 52, 53, 54, 55, 407,
	432, 56, 57, 455, 456, 58, 0, 59, 60, 61,
	62, 440, 0, 420, 0, 63, 64, 65, 66, 477,
	67, 68, 69, 0, 70, 71, 72, 73, 74, 75,
	0, 478, 76, 77, 78, 430, 421, 426, 431, 422,
	423, 427, 79, 80, 81, 82, 83, 84, 457, 458,
	85, 0, 86, 0, 87, 88, 89, 90, 91, 0,
	92, 93, 94, 0, 0, 95, 96, 451, 98, 99,
	0, 100, 101, 102, 0, 103, 104, 105, 0, 106,
	107, 108, 109, 388, 110, 111, 112, 433, 405, 113,
	0, 114, 115, 459, 116, 0, 117, 0, 118, 479,
	0, 480, 119, 120, 121, 0, 122, 441, 0, 314,
	123, 0, 124, 125, 126, 127, 128, 481, 129, 130,
	131, 132, 0, 133, 134, 135, 136, 137, 138, 0,
	139, 482, 315, 140, 141, 142, 143, 460, 461, 0,
	419, 0, 144, 483, 484, 145, 485, 146, 147, 148,
	149, 150, 0, 0, 151, 442, 486, 152, 487, 0,
	153, 154, 155, 424, 425, 156, 157, 158, 159, 160,
	161, 162, 163, 164, 165, 166, 167, 168, 169, 170,
	462, 488, 463, 171, 172, 322, 379, 173, 174, 489,
	175, 406, 439, 176, 464, 177, 178, 179, 0, 180,
	0, 0, 393, 182, 183, 0, 0, 184, 325, 490,
	185, 491, 434, 186, 187, 188, 189, 190, 191, 192,
	0, 193, 194, 435, 195, 328, 198, 196, 197, 0,
	199, 200, 201, 202, 203, 204, 205, 206, 465, 207,
	208, 209, 210, 0, 211, 212, 213, 214, 215, 216,
	217, 218, 219, 220, 221, 0, 222, 223, 492, 224,
	225, 226, 394, 227, 228, 229, 230, 231, 232, 233,
	234, 0, 235, 236, 237, 238, 239, 428, 240, 241,
	331, 242, 243, 493, 244, 245, 466, 246, 0, 247,
	248, 249, 250, 251, 252, 253, 254, 255, 256, 257,
	436, 0, 258, 259, 0, 260, 494, 261, 262, 263,
	264, 265, 0, 467, 468, 0, 0, 266, 267, 437,
	268, 438, 404, 269, 270, 271, 272, 273, 274, 275,
	0, 0, 276, 277, 278, 279, 280, 429, 0, 281,
	282, 283, 284, 285, 338, 469, 0, 286, 495, 287,
	288, 289, 290, 0, 0, 291, 0, 0, 292, 293,
	294, 295, 296, 297, 340, 443, 444, 445, 446, 447,
	448, 449, 450, 298, 299, 300, 380, 0, 0, 0,
	0, 0, 0, 0, 376, 377, 374, 0, 0, 0,
	0, 0, 0, 384, 410, 397, 413, 399, 400, 392,
	412, 0, 0, 0, 0, 0, 0, 0, 0, 33,
	34, 35, 36, 37, 38, 39, 40, 526, 41, 42,
	43, 0, 0, 0, 0, 389, 0, 0, 44, 45,
	0, 46, 47, 475, 48, 49, 50, 301, 452, 476,
	453, 454, 0, 51, 52, 53, 54, 55, 407, 432,
	56, 57, 455, 456, 58, 0, 59, 60, 61, 62,
	440, 0, 420, 0, 63, 64, 65, 66, 477, 67,
	68, 69, 0, 70, 71, 72, 73, 74, 75, 0,
	478, 76, 77, 78, 430, 421, 426, 431, 422, 423,
	427, 79, 80, 81, 82, 83, 84, 457, 458, 85,
	0, 86, 0, 87, 88, 89, 90, 91, 0, 92,
	93, 94, 0, 0, 95, 96, 451, 98, 99, 0,
	100, 101, 102, 0, 103, 104, 105, 0, 106, 107,
	108, 109, 388, 110, 111, 112, 433, 405, 113, 0,
	114, 115, 459, 116, 0, 117, 0, 118, 479, 0,
	480, 119, 120, 121, 0, 122, 441, 0, 314, 123,
	0, 124, 125, 126, 127, 128, 481, 129, 130, 131,
	132, 0, 133, 134, 135, 136, 137, 138, 0, 139,
	482, 315, 140, 141, 142, 143, 460, 461, 0, 419,
	0, 144, 483, 484, 145, 485, 146, 147, 148, 149,
	150, 0, 0, 151, 442, 486, 152, 487, 0, 153,
	154, 155, 424, 425, 156, 157, 158, 159, 160, 161,
	162, 163, 164, 165, 166, 167, 168, 169, 170, 462,
	488, 463, 171, 172, 322, 379, 173, 174, 489, 175,
	406, 439, 176, 464, 177, 178, 179, 0, 180, 0,
	0, 393, 182, 183, 0, 0, 184, 325, 490, 185,
	491, 434, 186, 187, 188, 189, 190, 191, 192, 0,
	193, 194, 435, 195, 328, 198, 196, 197, 0, 199,
	200, 201, 202, 203, 204, 205, 206, 465, 207, 208,
	209, 210, 0, 211, 212, 213, 214, 215, 216, 217,
	218, 219, 220, 221, 0, 222, 223, 492, 224, 225,
	226, 394, 227, 228, 229, 230, 231, 232, 233, 234,
	0, 235, 236, 237, 238, 239, 428, 240, 241, 331,
	242, 243, 493, 244, 245, 466, 246, 0, 247, 248,
	249, 250, 251, 252, 253, 254, 255, 256, 257, 436,
	0, 258, 259, 0, 260, 494, 261, 262, 263, 264,
	265, 0, 467, 468, 0, 0, 266, 267, 437, 268,
	438, 404, 269, 270, 271, 272, 273, 274, 275, 0,
	0, 276, 277, 278, 279, 280, 429, 0, 281, 282,
	283, 284, 285, 338, 469, 0, 286, 495, 287, 288,
	289, 290, 0, 0, 291, 0, 0, 292, 293, 294,
	295, 296, 297, 340, 443, 444, 445, 446, 447, 448,
	449, 450, 298, 299, 300, 380, 0, 0, 0, 0,
	0, 0, 0, 376, 377, 410, 397, 413, 399, 400,
	392, 412, 384, 0, 0, 0, 0, 0, 0, 0,
	33, 34, 35, 36, 37, 38, 39, 40, 0, 41,
	42, 43, 0, 0, 0, 0, 389, 0, 0, 44,
	45, 0, 46, 47, 475, 48, 49, 50, 301, 452,
	476, 453, 454, 0, 51, 52, 53, 54, 55, 407,
	432, 56, 57, 455, 456, 58, 0, 59, 60, 61,
	62, 440, 0, 420, 0, 63, 64, 65, 66, 477,
	67, 68, 69, 0, 70, 71, 72, 73, 74, 75,
	0, 478, 76, 77, 1449, 430, 421, 426, 431, 422,
	423, 427, 79, 80, 81, 82, 83, 84, 457, 458,
	85, 0, 86, 0, 87, 88, 89, 90, 91, 0,
	92, 93, 94, 0, 0, 95, 96, 451, 98, 99,
	0, 100, 101, 102, 0, 103, 104, 105, 0, 106,
	107, 108, 109, 388, 110, 111, 112, 433, 405, 113,
	0, 114, 115, 459, 116, 0, 117, 0, 118, 479,
	0, 480, 119, 120, 121, 0, 122, 441, 0, 314,
	123, 0, 124, 125, 126, 127, 128, 481, 129, 130,
	131, 132, 0, 133, 134, 135, 136, 137, 138, 0,
	139, 482, 315, 140, 141, 142, 143, 460, 461, 0,
	419, 0, 144, 483, 484, 145, 485, 146, 147, 148,
	149, 150, 0, 0, 151, 442, 486, 152, 487, 0,
	153, 154, 155, 424, 425, 156, 157, 158, 159, 160,
	161, 162, 163, 164, 165, 166, 167, 168, 169, 170,
	462, 488, 463, 171, 172, 322, 379, 173, 174, 489,
	175, 406, 439, 176, 464, 177, 178, 179, 0, 180,
	0, 0, 393, 182, 183, 0, 0, 184, 325, 490,
	185, 491, 434, 186, 187, 188, 189, 190, 191, 192,
	0, 193, 194, 435, 195, 328, 198, 196, 197, 0,
	199, 200, 201, 202, 203, 204, 205, 206, 465, 207,
	208, 209, 210, 0, 211, 212, 213, 214, 215, 216,
	217, 218, 219, 220, 221, 0, 222, 223, 492, 224,
	225, 226, 394, 227, 228, 229, 230, 231, 232, 233,
	234, 0, 235, 236, 237, 238, 239, 428, 240, 241,
	331, 242, 243, 493, 244, 245, 466, 246, 0, 247,
	248, 249, 250, 251, 252, 253, 254, 255, 256, 257,
	436, 0, 258, 259, 0, 260, 494, 261, 262, 263,
	264, 265, 0, 467, 468, 0, 0, 266, 267, 437,
	268, 438, 404, 269, 270, 271, 272, 1448, 274, 275,
	0, 0, 276, 277, 278, 279, 280, 429, 0, 281,
	282, 283, 284, 285, 338, 469, 0, 286, 495, 287,
	288, 289, 290, 0, 0, 291, 0, 0, 292, 293,
	294, 295, 296, 297, 340, 443, 444, 445, 446, 447,
	448, 449, 450, 298, 299, 300, 380, 0, 0, 0,
	0, 0, 0, 0, 376, 377, 410, 397, 413, 399,
	400, 392, 412, 384, 0, 0, 0, 0, 0, 0,
	0, 33, 34, 35, 36, 37, 38, 39, 40, 0,
	41, 42, 43, 0, 0, 0, 0, 389, 0, 0,
	44, 45, 0, 46, 47, 475, 48, 49, 50, 1447,
	452, 476, 453, 454, 0, 51, 52, 53, 54, 55,
	407, 432, 56, 57, 455, 456, 58, 0, 59, 60,
	61, 62, 440, 0, 420, 0, 63, 64, 65, 66,
	477, 67, 68, 69, 0, 70, 71, 72, 73, 74,
	75, 0, 478, 76, 77, 1449, 430, 421, 426, 431,
	422, 423, 427, 79, 80, 81, 82, 83, 84, 457,
	458, 85, 0, 86, 0, 87, 88, 89, 90, 91,
	0, 92, 93, 94, 0, 0, 95, 96, 451, 98,
	99, 0, 100, 101, 102, 0, 103, 104, 105, 0,
	106, 107, 108, 109, 388, 110, 111, 112, 433, 405,
	113, 0, 114, 115, 459, 116, 0, 117, 0, 118,
	479, 0, 480, 119, 120, 121, 0, 122, 441, 0,
	314, 123, 0, 124, 125, 126, 127, 128, 481, 129,
	130, 131, 132, 0, 133, 134, 135, 136, 137, 138,
	0, 139, 482, 315, 140, 141, 142, 143, 460, 461,
	0, 419, 0, 144, 483, 484, 145, 485, 146, 147,
	148, 149, 150, 0, 0, 151, 442, 486, 152, 487,
	0, 153, 154, 155, 424, 425, 156, 157, 158, 159,
	160, 161, 162, 163, 164, 165, 166, 167, 168, 169,
	170, 462, 488, 463, 171, 172, 322, 379, 173, 174,
	489, 175, 406, 439, 176, 464, 177, 178, 179, 0,
	180, 0, 0, 393, 182, 183, 0, 0, 184, 325,
	490, 185, 491, 434, 186, 187, 188, 189, 190, 191,
	192, 0, 193, 194, 435, 195, 328, 198, 196, 197,
	0, 199, 200, 201, 202, 203, 204, 205, 206, 465,
	207, 208, 209, 210, 0, 211, 212, 213, 214, 215,
	216, 217, 218, 219, 220, 221, 0, 222, 223, 492,
	224, 225, 226, 394, 227, 228, 229, 230, 231, 232,
	233, 234, 0, 235, 236, 237, 238, 239, 428, 240,
	241, 331, 242, 243, 493, 244, 245, 466, 246, 0,
	247, 248, 249, 250, 251, 252, 253, 254, 255, 256,
	257, 436, 0, 258, 259, 0, 260, 494, 261, 262,
	263, 264, 265, 0, 467, 468, 0, 0, 266, 267,
	437, 268, 438, 404, 269, 270, 271, 272, 1448, 274,
	275, 0, 0, 276, 277, 278, 279, 280, 429, 0,
	281, 282, 283, 284, 285, 338, 469, 0, 286, 495,
	287, 288, 289, 290, 0, 0, 291, 0, 0, 292,
	293, 294, 295, 296, 297, 340, 443, 444, 445, 446,
	447, 448, 449, 450, 298, 299, 300, 380, 0, 0,
	0, 0, 0, 0, 0, 376, 377, 410, 397, 413,
	399, 400, 392, 412, 384, 0, 0, 0, 0, 0,
	0, 0, 33, 34, 35, 36, 37, 38, 39, 40,
	0, 41, 42, 43, 0, 0, 0, 0, 389, 0,
	0, 44, 45, 0, 46, 47, 475, 48, 49, 50,
	301, 452, 476, 453, 454, 0, 51, 52, 53, 54,
	55, 407, 432, 56, 57, 455, 456, 58, 0, 59,
	60, 61, 62, 440, 0, 420, 0, 63, 64, 65,
	66, 477, 67, 68, 69, 0, 70, 71, 72, 73,
	74, 75, 0, 478, 76, 77, 78, 430, 421, 426,
	431, 422, 423, 427, 79, 80, 81, 82, 83, 84,
	457, 458, 85, 0, 86, 0, 87, 88, 89, 90,
	91, 0, 92, 93, 94, 0, 0, 95, 96, 451,
	98, 99, 0, 100, 101, 102, 0, 103, 104, 105,
	0, 106, 107, 108, 109, 388, 110, 111, 112, 433,
	405, 113, 0, 114, 115, 459, 116, 0, 117, 0,
	118, 479, 0, 480, 119, 120, 121, 0, 122, 441,
	0, 314, 123, 0, 124, 125, 126, 127, 128, 481,
	129, 130, 131, 132, 0, 133, 134, 135, 136, 137,
	138, 0, 139, 482, 315, 140, 141, 142, 143, 460,
	461, 0, 419, 0, 144, 483, 484, 145, 485, 146,
	147, 148, 149, 150, 0, 0, 151, 442, 486, 152,
	487, 0, 153, 154, 155, 424, 425, 156, 157, 158,
	159, 160, 161, 162, 163, 164, 165, 166, 167, 168,
	169, 170, 462, 488, 463, 171, 172, 322, 379, 173,
	174, 489, 175, 406, 439, 176, 464, 177, 178, 179,
	0, 180, 0, 0, 393, 182, 183, 0, 0, 184,
	325, 490, 185, 491, 434, 186, 187, 188, 189, 190,
	191, 192, 0, 193, 194, 435, 195, 328, 198, 196,
	197, 0, 199, 200, 201, 202, 203, 204, 205, 206,
	465, 207, 208, 209, 210, 0, 211, 212, 213, 214,
	215, 216, 217, 218, 219, 220, 221, 0, 222, 223,
	492, 224, 225, 226, 394, 227, 228, 229, 230, 231,
	232, 233, 234, 0, 235, 236, 237, 238, 239, 428,
	240, 241, 331, 242, 243, 493, 244, 245, 466, 246,
	0, 247, 248, 249, 250, 251, 252, 253, 254, 255,
	256, 257, 436, 0, 258, 259, 0, 260, 494, 261,
	262, 263, 264, 265, 0, 467, 468, 0, 0, 266,
	267, 437, 268, 438, 404, 269, 270, 271, 272, 273,
	274, 275, 0, 0, 276, 277, 278, 279, 280, 429,
	0, 281, 282, 283, 284, 285, 338, 469, 0, 286,
	495, 287, 288, 289, 290, 0, 0, 291, 0, 0,
	292, 293, 294, 295, 296, 297, 340, 443, 444, 445,
	446, 447, 448, 449, 450, 298, 299, 300, 380, 0,
	0, 0, 0, 0, 0, 0, 376, 377, 410, 397,
	413, 399, 400, 392, 412, 384, 0, 0, 0, 0,
	0, 0, 0, 33, 34, 35, 36, 37, 38, 39,
	40, 0, 41, 42, 43, 0, 0, 0, 0, 389,
	0, 0, 44, 45, 0, 46, 47, 475, 48, 49,
	50, 301, 452, 476, 453, 454, 0, 51, 52, 53,
	54, 55, 407, 432, 56, 57, 455, 456, 58, 0,
	59, 60, 61, 62, 440, 0, 420, 0, 63, 64,
	65, 66, 477, 67, 68, 69, 0, 70, 71, 72,
	73, 74, 75, 0, 478, 76, 77, 78, 430, 421,
	426, 431, 422, 423, 427, 79, 80, 81, 82, 83,
	84, 457, 458, 85, 0, 86, 0, 87, 88, 89,
	90, 91, 0, 92, 93, 94, 0, 0, 95, 96,
	451, 98, 99, 0, 100, 101, 102, 0, 103, 104,
	105, 0, 106, 107, 108, 109, 388, 110, 111, 112,
	433, 405, 113, 0, 114, 115, 459, 116, 0, 117,
	0, 118, 479, 0, 480, 119, 120, 121, 0, 122,
	441, 0, 314, 123, 0, 124, 125, 126, 127, 128,
	481, 129, 130, 131, 132, 0, 133, 134, 135, 136,
	137, 138, 0, 139, 482, 315, 140, 141, 142, 143,
	460, 461, 0, 419, 0, 144, 483, 484, 145, 485,
	146, 147, 148, 149, 150, 0, 0, 151, 442, 486,
	152, 487, 0, 153, 154, 155, 424, 425, 156, 157,
	158, 159, 160, 161, 162, 163, 164, 165, 166, 167,
	168, 169, 170, 462, 488, 463, 171, 1364, 322, 379,
	173, 174, 489, 175, 406, 439, 176, 464, 177, 178,
	179, 0, 180, 0, 0, 393, 182, 183, 0, 0,
	184, 325, 490, 185, 491, 434, 186, 187, 188, 189,
	190, 191, 192, 0, 193, 194, 435, 195, 328, 198,
	196, 197, 0, 199, 200, 201, 202, 203, 204, 205,
	206, 465, 207, 208, 209, 210, 0, 211, 212, 213,
	214, 215, 216, 217, 218, 219, 220, 221, 0, 222,
	223, 492, 224, 225, 226, 394, 227, 228, 229, 230,
	231, 232, 233, 234, 0, 235, 236, 237, 238, 239,
	428, 240, 241, 331, 242, 243, 493, 244, 245, 466,
	246, 0, 247, 248, 249, 250, 251, 252, 253, 254,
	255, 256, 257, 436, 0, 258, 259, 0, 260, 494,
	261, 262, 263, 264, 265, 0, 467, 468, 0, 0,
	266, 267, 437, 268, 438, 404, 269, 270, 271, 272,
	273, 274, 275, 0, 0, 276, 277, 278, 279, 280,
	429, 0, 281, 282, 283, 284, 285, 338, 469, 0,
	286, 495, 287, 288, 289, 290, 0, 0, 291, 0,
	0, 292, 293, 294, 295, 296, 297, 340, 443, 444,
	445, 446, 447, 448, 449, 450, 298, 299, 300, 380,
	0, 0, 0, 0, 0, 0, 0, 376, 377, 410,
	397, 413, 399, 400, 392, 412, 384, 0, 0, 0,
	0, 0, 0, 0, 33, 34, 35, 36, 37, 38,
	39, 40, 0, 41, 42, 43, 0, 0, 0, 0,
	389, 0, 0, 44, 45, 0, 46, 47, 475, 48,
	49, 50, 301, 452, 476, 453, 454, 0, 51, 52,
	53, 54, 55, 407, 432, 56, 57, 455, 456, 58,
	0, 59, 60, 61, 62, 440, 0, 420, 0, 63,
	64, 65, 66, 477, 67, 68, 69, 0, 70, 71,
	72, 73, 74, 75, 0, 478, 76, 77, 78, 430,
	421, 426, 431, 422, 423, 427, 79, 80, 81, 82,
	83, 84, 457, 458, 85, 0, 86, 0, 87, 88,
	89, 90, 91, 0, 92, 93, 94, 0, 0, 95,
	96, 451, 98, 99, 0, 100, 101, 102, 0, 103,
	104, 105, 0, 106, 107, 108, 109, 388, 110, 111,
	112, 433, 405, 113, 0, 114, 115, 459, 116, 0,
	117, 0, 118, 479, 0, 480, 119, 120, 121, 0,
	122, 441, 0, 314, 123, 0, 124, 125, 126, 127,
	128, 481, 129, 130, 131, 132, 0, 133, 134, 135,
	136, 137, 138, 0, 139, 482, 315, 140, 141, 142,
	143, 460, 461, 0, 419, 0, 144, 483, 484, 145,
	485, 146, 147, 148, 149, 150, 0, 0, 151, 442,
	486, 152, 487, 0, 153, 154, 155, 424, 425, 156,
	157, 158, 159, 160, 161, 162, 163, 164, 165, 166,
	167, 168, 169, 170, 462, 488, 463, 171, 172, 322,
	379, 173, 174, 489, 175, 406, 439, 176, 464, 177,
	178, 179, 0, 180, 0, 0, 393, 182, 183, 0,
	0, 184, 325, 490, 185, 491, 434, 186, 187, 188,
	189, 190, 191, 192, 0, 193, 194, 435, 195, 328,
	198, 196, 197, 0, 199, 200, 201, 202, 203, 204,
	205, 206, 465, 207, 208, 209, 210, 0, 211, 212,
	213, 214, 215, 216, 217, 218, 219, 220, 221, 0,
	222, 223, 492, 224, 225, 226, 394, 227, 228, 229,
	230, 231, 232, 233, 234, 0, 235, 236, 237, 238,
	239, 428, 240, 241, 331, 242, 243, 493, 244, 245,
	466, 246, 0, 247, 248, 249, 250, 251, 252, 253,
	254, 255, 256, 257, 436, 0, 258, 259, 0, 260,
	494, 261, 262, 263, 264, 265, 0, 467, 468, 0,
	0, 266, 267, 437, 268, 438, 404, 269, 270, 271,
	272, 273, 274, 275, 0, 0, 276, 277, 278, 279,
	280, 429, 0, 281, 282, 283, 284, 285, 338, 469,
	0, 286, 495, 287, 288, 289, 290, 0, 0, 291,
	0, 0, 292, 293, 294, 295, 296, 297, 1354, 443,
	444, 445, 446, 447, 448, 449, 450, 298, 299, 300,
	380, 0, 0, 0, 0, 0, 0, 0, 376, 377,
	410, 397, 413, 399, 400, 392, 412, 384, 0, 0,
	0, 0, 0, 0, 0, 33, 34, 35, 36, 37,
	38, 39, 40, 0, 41, 42, 43, 0, 0, 0,
	0, 389, 0, 0, 44, 45, 0, 46, 47, 475,
	48, 49, 50, 301, 452, 476, 453, 454, 0, 51,
	52, 53, 54, 55, 407, 432, 56, 57, 455, 456,
	58, 0, 59, 60, 61, 62, 440, 0, 420, 0,
	63, 64, 65, 66, 477, 67, 68, 69, 0, 70,
	71, 72, 73, 74, 75, 0, 478, 76, 77, 78,
	430, 421, 426, 431, 422, 423, 427, 79, 80, 81,
	82, 83, 84, 457, 458, 85, 0, 86, 0, 87,
	88, 89, 90, 91, 0, 92, 93, 94, 0, 0,
	95, 96, 451, 98, 99, 0, 100, 101, 102, 0,
	103, 104, 105, 0, 106, 107, 108, 109, 388, 110,
	111, 112, 433, 405, 113, 0, 114, 115, 459, 116,
	0, 117, 0, 118, 479, 0, 480, 119, 120, 121,
	0, 122, 441, 0, 314, 123, 0, 124, 125, 126,
	127, 128, 481, 129, 130, 131, 132, 0, 133, 134,
	135, 136, 137, 138, 0, 139, 482, 315, 140, 141,
	142, 143, 460, 461, 0, 419, 0, 144, 483, 484,
	145, 485, 146, 147, 148, 149, 150, 0, 0, 151,
	442, 486, 152, 487, 0, 153, 154, 155, 424, 425,
	156, 157, 158, 159, 160, 161, 162, 163, 164, 165,
	166, 167, 168, 169, 170, 462, 488, 463, 171, 172,
	322, 0, 173, 174, 489, 175, 406, 439, 176, 464,
	177, 178, 179, 0, 180, 0, 0, 393, 182, 183,
	0, 0, 184, 325, 490, 185, 491, 434, 186, 187,
	188, 189, 190, 191, 192, 0, 193, 194, 435, 195,
	328, 198, 196, 197, 0, 199, 200, 201, 202, 203,
	204, 205, 206, 465, 207, 208, 209, 210, 0, 211,
	212, 213, 214, 215, 216, 217, 218, 219, 220, 221,
	0, 222, 223, 492, 224, 225, 226, 941, 227, 228,
	229, 230, 231, 232, 233, 234, 0, 235, 236, 237,
	238, 239, 428, 240, 241, 331, 242, 243, 493, 244,
	245, 466, 246, 0, 247, 248, 249, 250, 251, 252,
	253, 254, 255, 256, 257, 436, 0, 258, 259, 0,
	260, 494, 261, 262, 263, 264, 265, 0, 467, 468,
	0, 0, 266, 267, 437, 268, 438, 404, 269, 270,
	271, 272, 273, 274, 275, 0, 0, 276, 277, 278,
	279, 280, 429, 0, 281, 282, 283, 284, 285, 338,
	469, 0, 286, 495, 287, 288, 289, 290, 0, 0,
	291, 0, 0, 292, 293, 294, 295, 296, 297, 340,
	443, 444, 445, 446, 447, 448, 449, 450, 298, 299,
	300, 0, 0, 0, 0, 0, 0, 0, 0, 937,
	938, 410, 397, 413, 399, 400, 392, 412, 940, 0,
	0, 0, 0, 0, 0, 0, 33, 34, 35, 36,
	37, 38, 39, 40, 0, 41, 42, 43, 0, 0,
	0, 0, 389, 0, 0, 44, 45, 0, 46, 47,
	475, 48, 49, 50, 301, 452, 476, 453, 454, 0,
	51, 52, 53, 54, 55, 407, 432, 56, 57, 455,
	456, 58, 0, 59, 60, 61, 62, 440, 0, 420,
	0, 63, 64, 65, 66, 477, 67, 68, 69, 0,
	70, 71, 72, 73, 74, 75, 0, 478, 76, 77,
	78, 430, 421, 426, 431, 422, 423, 427, 79, 80,
	81, 82, 83, 84, 457, 458, 85, 0, 86, 0,
	87, 88, 89, 90, 91, 0, 92, 93, 94, 0,
	0, 95, 96, 451, 98, 99, 0, 100, 101, 102,
	0, 103, 104, 105, 0, 106, 107, 108, 109, 388,
	110, 111, 112, 433, 405, 113, 0, 114, 115, 459,
	116, 0, 117, 0, 118, 479, 0, 480, 119, 120,
	121, 0, 122, 441, 0, 314, 123, 0, 124, 125,
	126, 127, 128, 481, 129, 130, 131, 132, 0, 133,
	134, 135, 136, 137, 138, 0, 139, 482, 315, 140,
	141, 142, 143, 460, 461, 0, 419, 0, 144, 0,
	484, 145, 485, 146, 147, 148, 149, 150, 0, 0,
	151, 442, 486, 152, 487, 0, 153, 154, 155, 424,
	425, 156, 157, 158, 159, 160, 161, 162, 163, 164,
	165, 166, 167, 168, 169, 170, 462, 488, 463, 171,
	172, 322, 0, 173, 174, 489, 175, 406, 439, 176,
	464, 177, 178, 179, 0, 180, 0, 0, 393, 182,
	183, 0, 0, 184, 325, 490, 185, 491, 434, 186,
	187, 188, 189, 190, 191, 192, 0, 193, 194, 435,
	195, 328, 198, 196, 197, 0, 199, 200, 201, 202,
	203, 204, 205, 206, 465, 207, 208, 209, 210, 0,
	211, 212, 213, 214, 215, 216, 217, 218, 219, 220,
	221, 0, 222, 223, 492, 224, 225, 226, 941, 227,
	228, 229, 230, 231, 232, 233, 234, 0, 235, 236,
	237, 238, 239, 428, 240, 241, 331, 242, 243, 493,
	244, 245, 466, 246, 0, 247, 248, 249, 250, 251,
	252, 253, 254, 255, 256, 257, 436, 0, 258, 259,
	0, 260, 494, 261, 262, 263, 264, 265, 0, 467,
	468, 0, 0, 266, 267, 437, 268, 438, 404, 269,
	270, 271, 272, 273, 274, 275, 0, 0, 276, 277,
	278, 279, 280, 429, 0, 281, 282, 283, 284, 285,
	338, 469, 0, 286, 495, 287, 288, 289, 290, 0,
	0, 291, 0, 0, 292, 293, 294, 295, 296, 297,
	340, 443, 444, 445, 446, 447, 448, 449, 450, 298,
	299, 300, 0, 0, 0, 0, 0, 0, 0, 0,
	937, 938, 410, 397, 413, 399, 400, 0, 412, 940,
	0, 0, 0, 0, 0, 0, 0, 33, 34, 35,
	36, 37, 38, 39, 40, 0, 41, 42, 43, 0,
	0, 0, 0, 389, 0, 0, 44, 45, 0, 46,
	47, 475, 48, 49, 50, 301, 452, 476, 453, 454,
	0, 51, 52, 53, 54, 55, 407, 432, 56, 57,
	455, 456, 58, 0, 59, 60, 61, 62, 440, 0,
	420, 0, 63, 64, 65, 66, 477, 67, 68, 69,
	0, 70, 71, 72, 73, 74, 75, 0, 478, 76,
	77, 78, 430, 421, 426, 431, 422, 423, 427, 79,
	80, 81, 82, 83, 84, 457, 458, 85, 0, 86,
	0, 87, 88, 89, 90, 91, 0, 92, 93, 94,
	0, 0, 95, 96, 451, 98, 99, 0, 100, 101,
	102, 0, 103, 104, 105, 0, 106, 107, 108, 109,
	388, 110, 111, 112, 433, 405, 113, 0, 114, 115,
	459, 116, 0, 117, 0, 118, 479, 0, 480, 119,
	120, 121, 0, 122, 441, 0, 314, 123, 0, 124,
	125, 126, 127, 128, 481, 129, 130, 131, 132, 0,
	133, 134, 135, 136, 137, 138, 0, 139, 482, 315,
	140, 141, 142, 143, 460, 461, 0, 419, 0, 144,
	483, 484, 145, 485, 146, 147, 148, 149, 150, 0,
	0, 151, 442, 486, 152, 487, 0, 153, 154, 155,
	424, 425, 156, 157, 158, 159, 160, 161, 162, 163,
	164, 165, 166, 167, 168, 169, 170, 462, 488, 463,
	171, 172, 322, 0, 173, 174, 489, 175, 406, 439,
	176, 464, 177, 178, 179, 0, 180, 0, 0, 181,
	182, 183, 0, 0, 184, 325, 490, 185, 491, 434,
	186, 187, 188, 189, 190, 191, 192, 0, 193, 194,
	435, 195, 328, 198, 196, 197, 0, 199, 200, 201,
	202, 203, 204, 205, 206, 465, 207, 208, 209, 210,
	0, 211, 212, 213, 214, 215, 216, 217, 218, 219,
	220, 221, 0, 222, 223, 492, 224, 225, 226, 941,
	227, 228, 229, 230, 231, 232, 233, 234, 0, 235,
	236, 237, 238, 239, 428, 240, 241, 331, 242, 243,
	493, 244, 245, 466, 246, 0, 247, 248, 249, 250,
	251, 252, 253, 254, 255, 256, 257, 436, 0, 258,
	259, 0, 260, 494, 261, 262, 263, 264, 265, 0,
	467, 468, 0, 0, 266, 267, 437, 268, 438, 404,
	269, 270, 271, 272, 273, 274, 275, 0, 0, 276,
	277, 278, 279, 280, 429, 0, 281, 282, 283, 284,
	285, 338, 469, 0, 286, 495, 287, 288, 289, 290,
	0, 0, 291, 0, 0, 292, 293, 294, 295, 296,
	297, 340, 443, 444, 445, 446, 447, 448, 449, 450,
	298, 299, 300, 0, 0, 0, 410, 397, 413, 399,
	400, 0, 412, 0, 0, 0, 0, 0, 0, 0,
	940, 33, 34, 35, 36, 37, 38, 39, 40, 0,
	41, 42, 43, 0, 0, 0, 0, 389, 0, 0,
	44, 45, 0, 46, 47, 475, 48, 49, 50, 301,
	452, 476, 453, 454, 0, 1279, 52, 53, 54, 55,
	407, 432, 56, 57, 455, 456, 58, 0, 59, 60,
	61, 62, 440, 0, 420, 0, 63, 64, 65, 66,
	477, 67, 68, 69, 0, 70, 71, 72, 73, 74,
	75, 0, 478, 76, 77, 78, 430, 421, 426, 431,
	422, 423, 427, 79, 80, 81, 82, 83, 84, 457,
	458, 85, 0, 86, 0, 87, 88, 89, 90, 91,
	0, 92, 93, 94, 0, 0, 95, 96, 451, 98,
	99, 0, 100, 101, 102, 0, 103, 104, 105, 0,
	106, 107, 108, 109, 388, 110, 111, 112, 433, 405,
	113, 0, 114, 115, 459, 116, 0, 117, 0, 118,
	479, 0, 480, 119, 120, 121, 0, 122, 441, 0,
	314, 123, 0, 124, 125, 126, 127, 128, 481, 129,
	130, 131, 132, 0, 133, 134, 135, 136, 137, 138,
	0, 139, 482, 315, 140, 141, 142, 143, 460, 461,
	0, 419, 0, 144, 483, 484, 145, 485, 146, 147,
	148, 149, 150, 0, 0, 151, 442, 486, 152, 487,
	0, 153, 154, 155, 424, 425, 156, 157, 158, 159,
	160, 161, 162, 163, 164, 165, 166, 167, 168, 169,
	170, 462, 488, 463, 171, 172, 322, 0, 173, 174,
	489, 175, 406, 439, 176, 464, 177, 178, 179, 0,
	180, 0, 0, 181, 182, 183, 0, 0, 184, 325,
	490, 185, 491, 434, 186, 187, 188, 189, 190, 191,
	192, 0, 193, 194, 435, 195, 328, 198, 196, 197,
	0, 199, 200, 201, 202, 203, 204, 205, 206, 465,
	207, 208, 209, 210, 0, 211, 212, 213, 214, 215,
	216, 217, 218, 219, 220, 221, 0, 222, 223, 492,
	224, 225, 226, 941, 227, 228, 229, 230, 231, 232,
	233, 234, 0, 235, 236, 237, 238, 239, 428, 240,
	241, 331, 242, 243, 493, 244, 245, 466, 246, 0,
	247, 248, 249, 250, 251, 252, 253, 254, 255, 256,
	257, 436, 0, 258, 259, 0, 260, 494, 261, 262,
	263, 264, 265, 0, 467, 468, 0, 0, 266, 267,
	437, 268, 438, 404, 269, 270, 271, 272, 273, 274,
	275, 0, 0, 276, 277, 278, 279, 280, 429, 0,
	281, 282, 283, 284, 285, 338, 469, 0, 286, 495,
	287, 288, 289, 290, 0, 0, 291, 0, 0, 292,
	293, 294, 295, 296, 297, 340, 443, 444, 445, 446,
	447, 448, 449, 450, 298, 299, 300, 0, 0, 0,
	410, 397, 413, 399, 400, 392, 412, 0, 0, 0,
	0, 0, 0, 0, 940, 33, 34, 35, 36, 37,
	38, 39, 40, 0, 41, 42, 43, 0, 0, 0,
	0, 389, 0, 0, 44, 45, 0, 46, 47, 475,
	48, 49, 50, 0, 452, 476, 453, 454, 0, 51,
	52, 53, 54, 55, 407, 432, 56, 57, 455, 456,
	58, 0, 59, 60, 61, 62, 440, 0, 420, 0,
	63, 64, 65, 66, 477, 67, 68, 69, 0, 70,
	71, 72, 73, 74, 75, 0, 478, 76, 77, 1449,
	430, 421, 426, 431, 422, 423, 427, 79, 80, 81,
	82, 83, 84, 457, 458, 85, 0, 86, 0, 87,
	88, 89, 90, 91, 0, 92, 93, 94, 0, 0,
	95, 96, 451, 98, 99, 0, 100, 101, 102, 0,
	103, 104, 105, 0, 106, 107, 108, 109, 388, 110,
	111, 112, 433, 405, 113, 0, 114, 115, 459, 116,
	0, 117, 0, 118, 479, 0, 480, 119, 120, 121,
	0, 122, 441, 0, 314, 123, 0, 124, 125, 126,
	127, 128, 0, 129, 130, 131, 132, 0, 133, 134,
	135, 136, 137, 138, 0, 139, 482, 315, 140, 141,
	142, 143, 460, 461, 0, 419, 0, 144, 0, 0,
	145, 485, 146, 147, 148, 149, 150, 0, 0, 151,
	442, 486, 152, 0, 0, 153, 154, 155, 424, 425,
	156, 157, 158, 159, 160, 161, 162, 163, 164, 165,
	166, 167, 168, 169, 170, 462, 488, 463, 171, 172,
	322, 379, 173, 174, 0, 175, 406, 439, 176, 464,
	177, 178, 179, 0, 180, 0, 0, 393, 182, 183,
	0, 0, 184, 325, 490, 185, 491, 434, 186, 187,
	188, 189, 190, 191, 192, 0, 193, 194, 435, 195,
	328, 198, 196, 197, 0, 199, 200, 201, 202, 203,
	204, 205, 206, 465, 207, 208, 209, 210, 0, 211,
	212, 213, 214, 215, 216, 217, 218, 219, 220, 221,
	0, 222, 223, 492, 224, 225, 226, 394, 227, 228,
	229, 230, 231, 232, 233, 234, 0, 235, 236, 237,
	238, 239, 428, 240, 241, 331, 242, 243, 0, 244,
	245, 466, 246, 0, 247, 248, 249, 250, 251, 252,
	253, 254, 255, 256, 257, 436, 0, 258, 259, 0,
	260, 494, 261, 262, 263, 264, 265, 0, 467, 468,
	0, 0, 266, 267, 437, 268, 438, 404, 269, 270,
	271, 272, 1448, 274, 275, 0, 0, 276, 277, 278,
	279, 280, 429, 0, 281, 282, 283, 284, 285, 338,
	469, 0, 286, 495, 287, 288, 289, 290, 0, 0,
	291, 0, 0, 292, 293, 294, 295, 296, 297, 340,
	443, 444, 445, 446, 447, 448, 449, 450, 298, 299,
	300, 0, 0, 0, 0, 0, 0, 0, 0, 376,
	377, 410, 397, 413, 399, 400, 392, 412, 384, 0,
	0, 0, 0, 0, 0, 0, 33, 34, 35, 36,
	37, 38, 39, 40, 0, 41, 42, 43, 0, 0,
	0, 0, 389, 0, 0, 44, 45, 0, 46, 47,
	475, 48, 49, 50, 0, 452, 476, 453, 454, 0,
	51, 52, 53, 54, 55, 407, 432, 56, 57, 455,
	456, 58, 0, 59, 60, 61, 62, 440, 0, 420,
	0, 63, 64, 65, 66, 477, 67, 68, 69, 0,
	70, 71, 72, 73, 74, 75, 0, 478, 76, 77,
	78, 430, 421, 426, 431, 422, 423, 427, 79, 80,
	81, 82, 83, 84, 457, 458, 85, 0, 86, 0,
	87, 88, 89, 90, 91, 0, 92, 93, 94, 0,
	0, 95, 96, 451, 98, 99, 0, 100, 101, 102,
	0, 103, 0, 105, 0, 106, 107, 108, 109, 388,
	110, 111, 112, 433, 405, 113, 0, 114, 115, 459,
	116, 0, 117, 0, 118, 479, 0, 480, 119, 120,
	121, 0, 122, 441, 0, 314, 123, 0, 124, 125,
	126, 127, 128, 0, 129, 130, 131, 132, 0, 133,
	134, 135, 136, 137, 138, 0, 139, 482, 315, 140,
	141, 142, 143, 460, 461, 0, 419, 0, 144, 0,
	0, 145, 485, 146, 147, 148, 149, 150, 0, 0,
	151, 442, 486, 152, 0, 0, 153, 154, 155, 424,
	425, 156, 157, 158, 159, 160, 161, 162, 163, 164,
	165, 166, 167, 168, 169, 170, 462, 488, 463, 171,
	172, 322, 379, 173, 174, 0, 175, 406, 439, 176,
	464, 177, 178, 179, 0, 180, 0, 0, 393, 182,
	183, 0, 0, 184, 325, 490, 185, 491, 434, 186,
	187, 188, 189, 190, 191, 192, 0, 193, 194, 435,
	195, 328, 198, 196, 197, 0, 199, 200, 201, 202,
	203, 204, 205, 206, 465, 207, 208, 209, 210, 0,
	211, 212, 213, 214, 215, 216, 217, 218, 219, 220,
	221, 0, 222, 223, 492, 224, 225, 226, 394, 227,
	228, 229, 230, 231, 232, 233, 234, 0, 235, 236,
	237, 238, 239, 428, 240, 241, 331, 242, 243, 0,
	244, 245, 466, 246, 0, 247, 248, 249, 250, 251,
	252, 253, 254, 255, 256, 257, 436, 0, 258, 259,
	0, 260, 494, 261, 262, 263, 264, 265, 0, 467,
	468, 0, 0, 266, 267, 437, 268, 438, 404, 269,
	270, 271, 272, 273, 274, 275, 0, 0, 276, 277,
	278, 279, 280, 429, 0, 281, 282, 283, 284, 285,
	338, 469, 0, 286, 495, 287, 288, 289, 290, 0,
	0, 291, 0, 0, 292, 293, 294, 295, 296, 297,
	340, 443, 444, 445, 446, 447, 448, 449, 450, 298,
	299, 300, 0, 0, 0, 0, 0, 0, 30, 0,
	376, 377, 0, 876, 0, 0, 0, 0, 0, 384,
	886, 887, 888, 33, 34, 35, 36, 37, 38, 39,
	40, 0, 41, 42, 43, 0, 0, 0, 0, 0,
	0, 0, 44, 45, 0, 46, 47, 0, 48, 49,
	50, 301, 302, 0, 303, 304, 0, 51, 52, 53,
	54, 55, 0, 0, 56, 57, 305, 306, 58, 0,
	59, 60, 61, 62, 307, 0, 0, 0, 63, 64,
	65, 66, 0, 67, 68, 69, 0, 70, 71, 72,
	73, 74, 75, 0, 0, 76, 77, 78, 0, 0,
	0, 0, 0, 0, 0, 79, 80, 81, 82, 83,
	84, 308, 309, 85, 0, 86, 0, 87, 88, 89,
	90, 91, 0, 92, 93, 94, 0, 0, 95, 96,
	97, 98, 99, 0, 100, 101, 102, 0, 103, 104,
	105, 0, 106, 107, 108, 109, 310, 110, 111, 112,
	311, 0, 113, 0, 114, 115, 312, 116, 0, 117,
	0, 118, 0, 0, 0, 119, 120, 121, 0, 122,
	313, 0, 314, 123, 0, 124, 125, 126, 127, 128,
	0, 129, 130, 131, 132, 0, 133, 134, 135, 136,
	137, 138, 0, 139, 0, 315, 140, 141, 142, 143,
	316, 317, 0, 318, 0, 144, 0, 0, 145, 0,
	146, 147, 148, 149, 150, 0, 0, 151, 319, 0,
	152, 0, 0, 153, 154, 155, 0, 0, 156, 157,
	158, 159, 160, 161, 162, 163, 164, 165, 166, 167,
	168, 169, 170, 320, 0, 321, 171, 172, 322, 0,
	173, 174, 0, 175, 0, 323, 176, 324, 177, 178,
	179, 0, 180, 0, 0, 181, 182, 183, 0, 0,
	184, 325, 0, 185, 0, 326, 186, 187, 188, 189,
	190, 191, 192, 0, 193, 194, 327, 195, 328, 198,
	196, 197, 0, 199, 200, 201, 202, 203, 204, 205,
	206, 329, 207, 208, 209, 210, 0, 211, 212, 213,
	214, 215, 216, 217, 218, 219, 220, 221, 0, 222,
	223, 0, 224, 225, 226, 330, 227, 228, 229, 230,
	231, 232, 233, 234, 0, 235, 236, 237, 238, 239,
	0, 240, 241, 331, 242, 243, 0, 244, 245, 332,
	246, 0, 247, 248, 249, 250, 251, 252, 253, 254,
	255, 256, 257, 333, 0, 258, 259, 0, 260, 0,
	261, 262, 263, 264, 265, 0, 334, 335, 0, 0,
	266, 267, 336, 268, 337, 0, 269, 270, 271, 272,
	273, 274, 275, 0, 0, 276, 277, 278, 279, 280,
	0, 0, 281, 282, 283, 284, 285, 338, 339, 0,
	286, 0, 287, 288, 289, 290, 0, 0, 291, 0,
	0, 292, 293, 294, 295, 296, 297, 340, 341, 342,
	343, 344, 345, 346, 347, 348, 298, 299, 300, 30,
	0, 0, 0, 883, 884, 885, 0, 877, 878, 879,
	880, 881, 882, 0, 33, 34, 35, 36, 37, 38,
	39, 40, 0, 41, 42, 43, 0, 0, 0, 0,
	0, 0, 0, 44, 45, 0, 46, 47, 0, 48,
	49, 50, 301, 302, 0, 303, 304, 0, 51, 52,
	53, 54, 55, 0, 0, 56, 57, 305, 306, 58,
	0, 59, 60, 61, 62, 307, 0, 0, 0, 63,
	64, 65, 66, 0, 67, 68, 69, 0, 70, 71,
	72, 73, 74, 75, 0, 0, 76, 77, 78, 0,
	0, 0, 0, 0, 0, 0, 79, 80, 81, 82,
	83, 84, 308, 309, 85, 0, 86, 0, 87, 88,
	89, 90, 91, 0, 92, 93, 94, 0, 0, 95,
	96, 97, 98, 99, 0, 100, 101, 102, 0, 103,
	104, 105, 0, 106, 107, 108, 109, 310, 110, 111,
	112, 311, 0, 113, 0, 114, 115, 312, 116, 0,
	117, 0, 118, 0, 0, 0, 119, 120, 121, 0,
	122, 313, 0, 314, 123, 0, 124, 125, 126, 127,
	128, 0, 129, 130, 131, 132, 0, 133, 134, 135,
	136, 137, 138, 0, 139, 0, 315, 140, 141, 142,
	143, 316, 317, 0, 318, 0, 144, 0, 0, 145,
	0, 146, 147, 148, 149, 150, 0, 0, 151, 319,
	0, 152, 0, 0, 153, 154, 155, 0, 0, 156,
	157, 158, 159, 160, 161, 162, 163, 164, 165, 166,
	167, 168, 169, 170, 320, 0, 321, 171, 172, 322,
	0, 173, 174, 0, 175, 0, 323, 176, 324, 177,
	178, 179, 0, 180, 0, 0, 181, 182, 183, 0,
	0, 184, 325, 0, 185, 0, 326, 186, 187, 188,
	189, 190, 191, 192, 0, 193, 194, 327, 195, 328,
	198, 196, 197, 0, 199, 200, 201, 202, 203, 204,
	205, 206, 329, 207, 208, 209, 210, 0, 211, 212,
	213, 214, 215, 216, 217, 218, 219, 220, 221, 0,
	222, 223, 0, 224, 225, 226, 330, 227, 228, 229,
	230, 231, 232, 233, 234, 0, 235, 236, 237, 238,
	239, 0, 240, 241, 331, 242, 243, 0, 244, 245,
	332, 246, 0, 247, 248, 249, 250, 251, 252, 253,
	254, 255, 256, 257, 333, 0, 258, 259, 0, 260,
	0, 261, 262, 263, 264, 265, 0, 334, 335, 0,
	0, 266, 267, 336, 268, 337, 0, 269, 270, 271,
	272, 273, 274, 275, 0, 0, 276, 277, 278, 279,
	280, 0, 0, 281, 282, 283, 284, 285, 338, 339,
	0, 286, 0, 287, 288, 289, 290, 0, 0, 291,
	0, 0, 292, 293, 294, 295, 296, 297, 340, 341,
	342, 343, 344, 345, 346, 347, 348, 298, 299, 300,
	0, 0, 0, 30, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 1235, 33, 34,
	35, 36, 37, 38, 39, 40, 0, 41, 42, 43,
	0, 0, 0, 0, 0, 0, 0, 44, 45, 0,
	46, 47, 0, 48, 49, 50, 301, 302, 0, 303,
	304, 0, 51, 52, 53, 54, 55, 0, 0, 56,
	57, 305, 306, 58, 0, 59, 60, 61, 62, 307,
	0, 0, 0, 63, 64, 65, 66, 0, 67, 68,
	69, 0, 70, 71, 72, 73, 74, 75, 0, 0,
	76, 77, 78, 0, 0, 0, 0, 0, 0, 0,
	79, 80, 81, 82, 83, 84, 308, 309, 85, 0,
	86, 0, 87, 88, 89, 90, 91, 0, 92, 93,
	94, 0, 0, 95, 96, 97, 98, 99, 0, 100,
	101, 102, 0, 103, 104, 105, 0, 106, 107, 108,
	109, 310, 110, 111, 112, 311, 0, 113, 0, 114,
	115, 312, 116, 0, 117, 0, 118, 0, 0, 0,
	119, 120, 121, 0, 122, 313, 0, 314, 123, 0,
	124, 125, 126, 127, 128, 0, 129, 130, 131, 132,
	0, 133, 134, 135, 136, 137, 138, 0, 139, 0,
	315, 140, 141, 142, 143, 316, 317, 0, 318, 0,
	144, 0, 0, 145, 0, 146, 147, 148, 149, 150,
	0, 0, 151, 319, 0, 152, 0, 0, 153, 154,
	155, 0, 0, 156, 157, 158, 159, 160, 161, 162,
	163, 164, 165, 166, 167, 168, 169, 170, 320, 0,
	321, 171, 172, 322, 0, 173, 174, 0, 175, 0,
	323, 176, 324, 177, 178, 179, 0, 180, 0, 0,
	181, 182, 183, 0, 0, 184, 325, 0, 185, 0,
	326, 186, 187, 188, 189, 190, 191, 192, 0, 193,
	194, 327, 195, 328, 198, 196, 197, 0, 199, 200,
	201, 202, 203, 204, 205, 206, 329, 207, 208, 209,
	210, 0, 211, 212, 213, 214, 215, 216, 217, 218,
	219, 220, 221, 0, 222, 223, 0, 224, 225, 226,
	330, 227, 228, 229, 230, 231, 232, 233, 234, 0,
	235, 236, 237, 238, 239, 0, 240, 241, 331, 242,
	243, 0, 244, 245, 332, 246, 0, 247, 248, 249,
	250, 251, 252, 253, 254, 255, 256, 257, 333, 0,
	258, 259, 0, 260, 0, 261, 262, 263, 264, 265,
	0, 334, 335, 0, 0, 266, 267, 336, 268, 337,
	0, 269, 270, 271, 272, 273, 274, 275, 0, 0,
	276, 277, 278, 279, 280, 0, 0, 281, 282, 283,
	284, 285, 338, 339, 0, 286, 0, 287, 288, 289,
	290, 0, 0, 291, 0, 0, 292, 293, 294, 295,
	296, 297, 340, 341, 342, 343, 344, 345, 346, 347,
	348, 298, 299, 300, 0, 0, 0, 30, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 502, 33, 34, 35, 36, 37, 38, 39, 40,
	0, 41, 42, 43, 0, 0, 0, 0, 0, 0,
	0, 44, 45, 0, 46, 47, 0, 48, 49, 50,
	301, 302, 0, 303, 304, 0, 51, 52, 53, 54,
	55, 0, 0, 56, 57, 305, 306, 58, 0, 59,
	60, 61, 62, 307, 0, 0, 0, 63, 64, 65,
	66, 0, 67, 68, 69, 0, 70, 71, 72, 73,
	74, 75, 0, 0, 76, 77, 78, 0, 0, 0,
	0, 0, 0, 0, 79, 80, 81, 82, 83, 84,
	308, 309, 85, 0, 86, 0, 87, 88, 89, 90,
	91, 0, 92, 93, 94, 0, 0, 95, 96, 97,
	98, 99, 0, 100, 101, 102, 0, 103, 104, 105,
	0, 106, 107, 108, 109, 310, 110, 111, 112, 311,
	0, 113, 0, 114, 115, 312, 116, 0, 117, 0,
	118, 0, 0, 0, 119, 120, 777, 0, 122, 313,
	0, 314, 123, 0, 124, 125, 126, 127, 128, 0,
	129, 130, 131, 132, 0, 133, 134, 135, 136, 137,
	138, 0, 139, 0, 315, 140, 141, 142, 143, 316,
	317, 0, 318, 0, 144, 0, 0, 145, 0, 146,
	147, 148, 149, 150, 0, 0, 151, 319, 0, 152,
	0, 0, 153, 154, 776, 0, 0, 156, 157, 158,
	159, 160, 161, 162, 163, 164, 165, 166, 167, 168,
	169, 170, 320, 0, 321, 171, 172, 322, 0, 173,
	174, 0, 175, 0, 323, 176, 324, 177, 178, 179,
	0, 180, 0, 0, 181, 182, 183, 0, 0, 184,
	325, 0, 185, 0, 326, 186, 187, 188, 189, 190,
	191, 192, 0, 193, 194, 327, 195, 328, 198, 196,
	197, 0, 199, 200, 201, 202, 203, 204, 205, 206,
	329, 207, 208, 209, 210, 0, 211, 212, 213, 214,
	215, 216, 217, 218, 219, 220, 221, 0, 222, 223,
	0, 224, 225, 226, 330, 227, 228, 229, 230, 231,
	232, 233, 234, 0, 235, 236, 237, 238, 239, 0,
	240, 241, 331, 242, 243, 0, 244, 245, 332, 246,
	0, 247, 248, 249, 250, 251, 252, 253, 254, 255,
	256, 257, 333, 0, 258, 259, 779, 260, 0, 261,
	775, 263, 774, 265, 0, 334, 335, 0, 0, 266,
	267, 336, 268, 337, 0, 269, 270, 271, 272, 273,
	274, 275, 0, 0, 276, 277, 778, 279, 280, 0,
	0, 281, 282, 283, 284, 285, 338, 339, 0, 286,
	0, 287, 288, 289, 290, 0, 0, 291, 0, 0,
	292, 293, 294, 295, 296, 297, 340, 341, 342, 343,
	344, 345, 346, 347, 348, 298, 299, 300, 30, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 33, 34, 35, 36, 37, 38, 39,
	40, 0, 41, 42, 43, 0, 0, 0, 0, 0,
	0, 0, 44, 45, 0, 46, 47, 0, 48, 49,
	50, 301, 302, 0, 303, 304, 0, 51, 52, 53,
	54, 55, 0, 0, 56, 57, 305, 306, 58, 0,
	59, 60, 61, 62, 307, 0, 0, 0, 63, 64,
	65, 66, 0, 67, 68, 69, 0, 70, 71, 72,
	73, 74, 75, 0, 0, 76, 77, 78, 0, 0,
	0, 0, 0, 0, 0, 79, 80, 81, 82, 83,
	84, 308, 309, 85, 0, 86, 0, 87, 88, 89,
	90, 91, 0, 92, 93, 94, 0, 0, 95, 96,
	97, 98, 99, 0, 100, 101, 102, 0, 103, 104,
	105, 0, 106, 107, 108, 109, 310, 110, 111, 112,
	311, 0, 113, 0, 114, 115, 312, 116, 0, 117,
	0, 118, 0, 0, 0, 119, 120, 121, 0, 122,
	313, 0, 314, 123, 0, 124, 125, 126, 127, 128,
	0, 129, 130, 131, 132, 0, 133, 134, 135, 136,
	137, 138, 0, 139, 0, 315, 140, 141, 142, 143,
	316, 317, 0, 318, 0, 144, 0, 0, 145, 0,
	146, 147, 148, 149, 150, 0, 0, 151, 319, 0,
	152, 0, 0, 153, 154, 155, 0, 0, 156, 157,
	158, 159, 160, 161, 162, 163, 164, 165, 166, 167,
	168, 169, 170, 320, 0, 321, 171, 172, 322, 0,
	173, 174, 0, 175, 0, 323, 176, 324, 177, 178,
	179, 0, 180, 0, 28, 181, 182, 183, 0, 0,
	184, 325, 0, 185, 0, 326, 186, 187, 188, 189,
	190, 191, 192, 0, 193, 194, 327, 195, 328, 198,
	196, 197, 0, 199, 200, 201, 202, 203, 204, 205,
	206, 329, 207, 208, 209, 210, 0, 211, 212, 213,
	214, 215, 216, 217, 218, 219, 220, 221, 0, 222,
	223, 0, 224, 225, 226, 330, 227, 228, 229, 230,
	231, 232, 233, 234, 0, 235, 236, 237, 238, 239,
	0, 240, 241, 331, 242, 243, 0, 244, 245, 332,
	246, 0, 247, 248, 249, 250, 251, 252, 253, 254,
	255, 256, 257, 333, 0, 258, 259, 0, 260, 0,
	261, 262, 263, 264, 265, 0, 334, 335, 0, 0,
	266, 267, 336, 268, 337, 0, 269, 270, 271, 272,
	273, 274, 275, 0, 0, 276, 277, 278, 279, 280,
	0, 0, 281, 282, 283, 284, 285, 338, 339, 0,
	286, 0, 287, 288, 289, 290, 0, 0, 291, 0,
	0, 292, 293, 294, 295, 296, 297, 340, 341, 342,
	343, 344, 345, 346, 347, 348, 298, 299, 300, 30,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 33, 34, 35, 36, 37, 38,
	39, 40, 0, 41, 42, 43, 0, 0, 0, 0,
	0, 0, 0, 44, 45, 0, 46, 47, 0, 48,
	49, 50, 301, 302, 0, 303, 304, 0, 51, 52,
	53, 54, 55, 0, 0, 56, 57, 305, 306, 58,
	0, 59, 60, 61, 62, 307, 0, 0, 0, 63,
	64, 65, 66, 0, 67, 68, 69, 0, 70, 71,
	72, 73, 74, 75, 0, 0, 76, 77, 78, 0,
	0, 0, 0, 0, 0, 0, 79, 80, 81, 82,
	83, 84, 308, 309, 85, 0, 86, 0, 87, 88,
	89, 90, 91, 0, 92, 93, 94, 0, 0, 95,
	96, 97, 98, 99, 0, 100, 101, 102, 0, 103,
	104, 105, 0, 106, 107, 108, 109, 310, 110, 111,
	112, 311, 0, 113, 0, 114, 115, 312, 116, 0,
	117, 0, 118, 0, 0, 0, 119, 120, 121, 0,
	122, 313, 0, 314, 123, 0, 124, 125, 126, 127,
	128, 0, 129, 130, 131, 132, 0, 133, 134, 135,
	136, 137, 138, 0, 139, 0, 315, 140, 141, 142,
	143, 316, 317, 0, 318, 0, 144, 0, 0, 145,
	0, 146, 147, 148, 149, 150, 0, 0, 151, 319,
	0, 152, 0, 0, 153, 154, 155, 0, 0, 156,
	157, 158, 159, 160, 161, 162, 163, 164, 165, 166,
	167, 168, 169, 170, 320, 0, 321, 171, 172, 322,
	0, 173, 174, 0, 175, 0, 323, 176, 324, 177,
	178, 179, 0, 180, 0, 0, 181, 182, 183, 0,
	0, 184, 325, 0, 185, 0, 326, 186, 187, 188,
	189, 190, 191, 192, 0, 193, 194, 327, 195, 328,
	198, 196, 197, 0, 199, 200, 201, 202, 203, 204,
	205, 206, 329, 207, 208, 209, 210, 0, 211, 212,
	213, 214, 215, 216, 217, 218, 219, 220, 221, 0,
	222, 223, 0, 224, 225, 226, 330, 227, 228, 229,
	230, 231, 232, 233, 234, 0, 235, 236, 237, 238,
	239, 0, 240, 241, 331, 242, 243, 0, 244, 245,
	332, 246, 0, 247, 248, 249, 250, 251, 252, 253,
	254, 255, 256, 257, 333, 0, 258, 259, 0, 260,
	0, 261, 262, 263, 264, 265, 0, 334, 335, 0,
	0, 266, 267, 336, 268, 337, 0, 269, 270, 271,
	272, 273, 274, 275, 0, 0, 276, 277, 278, 279,
	280, 0, 0, 281, 282, 283, 284, 285, 338, 339,
	0, 286, 0, 287, 288, 289, 290, 0, 0, 291,
	0, 0, 292, 293, 294, 295, 296, 297, 340, 341,
	342, 343, 344, 345, 346, 347, 348, 298, 299, 300,
	30, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 33, 34, 35, 36, 37,
	38, 39, 40, 0, 41, 42, 43, 0, 0, 0,
	0, 0, 0, 0, 44, 45, 0, 46, 47, 0,
	48, 49, 50, 301, 302, 0, 303, 304, 0, 51,
	52, 53, 54, 55, 0, 0, 56, 57, 305, 306,
	58, 0, 59, 60, 61, 62, 307, 0, 0, 0,
	63, 64, 65, 66, 0, 67, 68, 69, 0, 70,
	71, 72, 73, 74, 75, 0, 0, 76, 77, 78,
	0, 0, 0, 0, 0, 0, 0, 79, 80, 81,
	82, 83, 84, 308, 309, 85, 0, 86, 0, 87,
	88, 89, 90, 91, 0, 92, 93, 94, 0, 0,
	95, 96, 97, 98, 99, 0, 100, 101, 102, 0,
	103, 104, 105, 0, 106, 107, 108, 109, 310, 110,
	111, 112, 311, 0, 113, 0, 114, 115, 312, 116,
	0, 117, 0, 118, 0, 0, 0, 119, 120, 121,
	0, 122, 313, 0, 314, 123, 0, 124, 125, 126,
	127, 128, 0, 129, 130, 131, 132, 0, 133, 134,
	135, 136, 137, 138, 0, 139, 0, 315, 140, 141,
	142, 143, 316, 317, 0, 318, 0, 144, 0, 0,
	145, 0, 146, 147, 148, 149, 150, 0, 0, 151,
	319, 0, 152, 0, 0, 153, 154, 155, 0, 0,
	156, 157, 158, 159, 160, 161, 162, 163, 164, 165,
	166, 167, 168, 169, 170, 320, 0, 321, 171, 172,
	322, 0, 173, 174, 0, 175, 0, 323, 176, 324,
	177, 178, 179, 0, 180, 0, 0, 181, 182, 183,
	0, 0, 184, 325, 0, 185, 0, 326, 186, 187,
	188, 189, 0, 191, 192, 0, 193, 194, 327, 195,
	328, 198, 196, 197, 0, 199, 200, 201, 202, 203,
	204, 0, 206, 329, 207, 208, 209, 210, 0, 211,
	212, 213, 214, 215, 216, 217, 218, 219, 220, 221,
	0, 222, 223, 0, 224, 225, 226, 330, 0, 228,
	229, 230, 231, 232, 233, 234, 0, 235, 236, 237,
	238, 239, 0, 240, 241, 331, 242, 243, 0, 244,
	245, 332, 246, 0, 247, 248, 249, 250, 251, 252,
	253, 254, 255, 256, 257, 333, 0, 258, 259, 0,
	260, 0, 261, 262, 263, 264, 265, 0, 334, 335,
	0, 0, 266, 267, 336, 268, 337, 0, 269, 270,
	271, 272, 273, 274, 275, 0, 0, 276, 277, 278,
	279, 280, 0, 0, 281, 282, 283, 284, 285, 338,
	339, 0, 286, 0, 287, 288, 289, 290, 0, 0,
	291, 0, 0, 292, 293, 294, 295, 296, 297, 340,
	341, 342, 343, 344, 345, 346, 347, 348, 298, 299,
	300, 809, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 33, 34, 35, 36,
	37, 38, 39, 40, 0, 41, 42, 43, 0, 0,
	0, 0, 0, 0, 0, 44, 45, 0, 46, 47,
	475, 48, 49, 50, 0, 795, 476, 811, 801, 0,
	51, 52, 53, 54, 55, 0, 0, 56, 57, 813,
	812, 58, 0, 59, 60, 61, 62, 0, 0, 667,
	0, 63, 64, 65, 66, 477, 67, 68, 69, 0,
	70, 71, 72, 73, 74, 75, 0, 478, 76, 77,
	78, 0, 0, 0, 668, 0, 0, 0, 79, 80,
	81, 82, 83, 84, 799, 798, 85, 0, 86, 0,
	87, 88, 89, 90, 91, 0, 92, 93, 94, 0,
	0, 95, 96, 451, 98, 99, 0, 100, 101, 102,
	0, 103, 104, 105, 0, 106, 107, 108, 109, 0,
	110, 111, 112, 0, 0, 113, 0, 114, 115, 797,
	116, 0, 117, 0, 118, 479, 0, 480, 119, 120,
	121, 0, 122, 0, 0, 0, 123, 0, 124, 125,
	126, 127, 128, 481, 129, 130, 131, 132, 0, 133,
	134, 135, 136, 137, 138, 0, 139, 482, 0, 140,
	141, 142, 143, 792, 793, 0, 808, 0, 144, 483,
	484, 145, 485, 146, 147, 148, 149, 150, 0, 0,
	151, 0, 486, 152, 487, 0, 153, 154, 155, 0,
	0, 156, 157, 158, 159, 160, 161, 162, 163, 164,
	165, 166, 167, 168, 169, 170, 815, 488, 816, 171,
	172, 0, 0, 173, 174, 489, 175, 0, 0, 176,
	800, 177, 178, 179, 0, 180, 0, 0, 181, 182,
	183, 0, 0, 184, 0, 490, 185, 491, 0, 186,
	187, 188, 189, 190, 191, 192, 0, 193, 194, 0,
	195, 0, 198, 196, 197, 0, 199, 200, 201, 202,
	203, 204, 205, 206, 796, 207, 208, 209, 210, 0,
	211, 212, 213, 214, 215, 216, 217, 218, 219, 220,
	221, 0, 222, 223, 492, 224, 225, 226, 0, 227,
	228, 229, 230, 231, 232, 233, 234, 0, 235, 236,
	237, 238, 239, 0, 240, 241, 784, 242, 243, 493,
	244, 245, 794, 246, 0, 247, 248, 249, 250, 251,
	252, 253, 254, 255, 256, 257, 0, 0, 258, 259,
	0, 260, 494, 261, 262, 263, 264, 265, 0, 807,
	806, 0, 0, 266, 267, 0, 268, 0, 0, 269,
	270, 271, 272, 273, 274, 275, 0, 0, 276, 277,
	278, 279, 280, 0, 0, 281, 282, 283, 284, 285,
	0, 814, 0, 286, 495, 287, 288, 289, 290, 0,
	0, 291, 0, 0, 292, 293, 294, 295, 296, 297,
	809, 0, 0, 0, 0, 0, 0, 0, 0, 298,
	299, 300, 0, 0, 0, 33, 34, 35, 36, 37,
	38, 39, 40, 0, 41, 42, 43, 0, 0, 0,
	0, 0, 0, 0, 44, 45, 0, 46, 47, 475,
	48, 49, 50, 0, 795, 476, 811, 801, 0, 51,
	52, 53, 54, 55, 0, 0, 56, 57, 813, 812,
	58, 0, 59, 60, 61, 62, 0, 0, 667, 0,
	63, 64, 65, 66, 477, 67, 68, 69, 0, 70,
	71, 72, 73, 74, 75, 0, 478, 76, 77, 78,
	0, 0, 0, 668, 0, 0, 0, 79, 80, 81,
	82, 83, 84, 799, 798, 85, 0, 86, 0, 87,
	88, 89, 90, 91, 0, 92, 93, 94, 0, 0,
	95, 96, 451, 98, 99, 0, 100, 101, 102, 0,
	103, 104, 105, 0, 106, 107, 108, 109, 0, 110,
	111, 112, 0, 0, 113, 0, 114, 115, 797, 116,
	0, 117, 0, 118, 479, 0, 480, 119, 120, 121,
	0, 122, 0, 0, 0, 123, 0, 124, 125, 126,
	127, 128, 481, 129, 130, 131, 132, 0, 133, 134,
	135, 136, 137, 138, 0, 139, 482, 0, 140, 141,
	142, 143, 792, 793, 0, 808, 0, 144, 483, 484,
	145, 485, 146, 147, 148, 149, 150, 0, 0, 151,
	0, 486, 152, 487, 0, 153, 154, 155, 0, 0,
	156, 157, 158, 159, 160, 161, 162, 163, 164, 165,
	166, 167, 168, 169, 170, 815, 488, 816, 171, 172,
	0, 0, 173, 174, 489, 175, 0, 0, 176, 800,
	177, 178, 179, 0, 180, 0, 0, 181, 182, 183,
	0, 0, 184, 0, 490, 185, 491, 0, 186, 187,
	188, 189, 190, 191, 192, 0, 193, 194, 0, 195,
	0, 198, 196, 197, 0, 199, 200, 201, 202, 203,
	204, 205, 206, 796, 207, 208, 209, 210, 0, 211,
	212, 213, 214, 215, 216, 217, 218, 219, 220, 221,
	0, 222, 223, 492, 224, 225, 226, 0, 227, 228,
	229, 230, 231, 232, 233, 234, 0, 235, 236, 237,
	238, 239, 0, 240, 241, 0, 242, 243, 493, 244,
	245, 794, 246, 0, 247, 248, 249, 250, 251, 252,
	253, 254, 255, 256, 257, 0, 0, 258, 259, 0,
	260, 494, 261, 262, 263, 264, 265, 0, 807, 806,
	0, 0, 266, 267, 0, 268, 0, 0, 269, 270,
	271, 272, 273, 274, 275, 0, 0, 276, 277, 278,
	279, 280, 0, 0, 281, 282, 283, 284, 285, 0,
	814, 0, 286, 495, 287, 288, 289, 290, 0, 0,
	291, 0, 0, 292, 293, 294, 295, 296, 297, 0,
	0, 0, 0, 0, 577, 0, 0, 547, 298, 299,
	300, 559, 560, 561, 0, 0, 0, 0, 0, 0,
	0, 0, 577, 0, 0, 547, 0, 0, 563, 559,
	560, 561, 0, 0, 0, 0, 549, 0, 0, 0,
	0, 577, 572, 0, 547, 0, 563, 0, 559, 560,
	561, 0, 0, 0, 549, 0, 0, 0, 0, 0,
	572, 577, 0, 0, 547, 563, 548, 0, 559, 560,
	561, 0, 0, 549, 0, 0, 0, 0, 0, 572,
	0, 0, 0, 0, 548, 563, 0, 0, 0, 0,
	0, 0, 0, 549, 0, 0, 0, 0, 0, 572,
	0, 0, 0, 548, 577, 0, 0, 547, 0, 0,
	0, 559, 560, 561, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 548, 0, 0, 0, 0, 563, 0,
	0, 0, 0, 0, 0, 0, 549, 577, 0, 0,
	547, 0, 572, 0, 559, 560, 561, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 563, 0, 0, 0, 0, 548, 0, 0, 549,
	0, 567, 0, 0, 0, 572, 573, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 567,
	0, 0, 0, 0, 573, 0, 0, 569, 570, 548,
	0, 0, 0, 0, 0, 0, 0, 0, 567, 0,
	0, 0, 565, 573, 0, 569, 570, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 567, 0,
	565, 0, 0, 573, 569, 570, 0, 0, 0, 0,
	0, 0, 0, 571, 0, 0, 0, 0, 0, 565,
	0, 0, 0, 0, 569, 570, 578, 0, 0, 564,
	0, 571, 0, 0, 0, 0, 0, 0, 0, 565,
	0, 567, 0, 0, 578, 0, 573, 564, 0, 0,
	571, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 578, 0, 0, 564, 569, 570, 0,
	571, 0, 0, 0, 567, 0, 0, 0, 0, 573,
	0, 0, 565, 578, 0, 0, 564, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	569, 570, 0, 0, 0, 0, 0, 568, 0, 577,
	0, 0, 547, 571, 0, 565, 559, 560, 561, 0,
	0, 0, 0, 0, 0, 568, 578, 0, 0, 564,
	0, 0, 0, 563, 0, 0, 0, 0, 0, 0,
	0, 549, 0, 0, 568, 0, 571, 572, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 578,
	0, 0, 564, 0, 568, 0, 0, 0, 0, 0,
	0, 548, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	566, 0, 0, 0, 556, 557, 558, 568, 550, 551,
	552, 553, 554, 555, 0, 0, 0, 0, 566, 0,
	0, 1127, 556, 557, 558, 0, 550, 551, 552, 553,
	554, 555, 0, 0, 0, 0, 1402, 566, 0, 0,
	568, 556, 557, 558, 0, 550, 551, 552, 553, 554,
	555, 0, 0, 0, 0, 1395, 0, 566, 0, 0,
	0, 556, 557, 558, 0, 550, 551, 552, 553, 554,
	555, 0, 0, 0, 0, 1390, 567, 0, 0, 0,
	0, 573, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	566, 0, 569, 570, 556, 557, 558, 0, 550, 551,
	552, 553, 554, 555, 0, 0, 0, 565, 1386, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 566, 0, 0, 0, 556, 557, 558,
	0, 550, 551, 552, 553, 554, 555, 0, 571, 0,
	0, 1351, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 578, 577, 0, 564, 547, 0, 0, 0, 559,
	560, 561, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 577, 0, 0, 547, 563, 0, 0, 559,
	560, 561, 0, 0, 549, 0, 0, 0, 0, 0,
	572, 577, 0, 0, 547, 0, 563, 0, 559, 560,
	561, 0, 0, 0, 549, 0, 0, 0, 0, 0,
	572, 0, 0, 0, 548, 563, 0, 0, 0, 0,
	0, 0, 0, 549, 0, 0, 0, 0, 577, 572,
	0, 547, 568, 0, 548, 559, 560, 561, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 577, 0, 0,
	547, 0, 563, 548, 559, 560, 561, 0, 0, 0,
	549, 0, 0, 0, 0, 0, 572, 0, 0, 0,
	0, 563, 0, 0, 0, 0, 0, 0, 0, 549,
	577, 0, 0, 547, 0, 572, 0, 559, 560, 561,
	548, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 563, 0, 0, 0, 769, 548,
	0, 0, 549, 0, 0, 0, 0, 0, 572, 567,
	0, 0, 0, 0, 573, 566, 0, 0, 0, 556,
	557, 558, 0, 550, 551, 552, 553, 554, 555, 567,
	0, 0, 548, 1327, 573, 569, 570, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 567, 0,
	565, 0, 0, 573, 0, 569, 570, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	565, 0, 0, 0, 569, 570, 0, 0, 0, 770,
	0, 571, 0, 0, 0, 567, 0, 0, 0, 565,
	573, 0, 0, 0, 578, 0, 0, 564, 0, 0,
	0, 571, 0, 0, 567, 0, 0, 0, 0, 573,
	0, 569, 570, 0, 578, 0, 0, 564, 0, 0,
	571, 0, 0, 0, 0, 0, 565, 0, 0, 0,
	569, 570, 0, 578, 0, 0, 564, 567, 0, 0,
	0, 0, 573, 0, 0, 565, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 571, 0, 0,
	0, 0, 0, 569, 570, 0, 0, 0, 0, 0,
	578, 0, 0, 564, 0, 568, 571, 0, 565, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 578,
	0, 0, 564, 0, 0, 568, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 571,
	0, 0, 0, 0, 568, 0, 0, 0, 0, 0,
	577, 0, 578, 547, 0, 564, 0, 559, 560, 561,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 563, 0, 0, 0, 0, 0,
	0, 568, 549, 0, 0, 0, 0, 0, 572, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 566, 0,
	568, 0, 556, 557, 558, 0, 550, 551, 552, 553,
	554, 555, 548, 0, 0, 0, 1238, 0, 566, 0,
	0, 0, 556, 557, 558, 0, 550, 551, 552, 553,
	554, 555, 0, 568, 0, 0, 1208, 566, 0, 0,
	0, 556, 557, 558, 0, 550, 551, 552, 553, 554,
	555, 0, 0, 0, 0, 1155, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 566, 0, 0, 0, 556, 557,
	558, 0, 550, 551, 552, 553, 554, 555, 767, 0,
	0, 0, 866, 566, 1458, 0, 0, 556, 557, 558,
	0, 550, 551, 552, 553, 554, 555, 0, 0, 1295,
	0, 0, 0, 0, 0, 0, 0, 567, 0, 0,
	0, 0, 573, 0, 0, 0, 566, 0, 0, 0,
	556, 557, 558, 0, 550, 551, 552, 553, 554, 555,
	0, 0, 0, 569, 570, 0, 0, 0, 0, 0,
	0, 0, 577, 0, 0, 547, 0, 0, 565, 559,
	560, 561, 0, 0, 0, 0, 1169, 0, 0, 0,
	0, 577, 0, 0, 547, 0, 563, 0, 559, 560,
	561, 0, 0, 0, 549, 0, 0, 0, 0, 571,
	572, 0, 0, 0, 0, 563, 0, 0, 1168, 0,
	0, 0, 578, 549, 0, 564, 0, 0, 0, 572,
	0, 0, 0, 0, 548, 0, 0, 0, 0, 0,
	0, 0, 0, 577, 1457, 0, 547, 0, 0, 0,
	559, 560, 561, 548, 0, 0, 576, 0, 0, 0,
	0, 577, 0, 0, 547, 0, 0, 563, 559, 560,
	561, 0, 0, 0, 0, 549, 0, 0, 0, 0,
	0, 572, 0, 0, 0, 563, 0, 0, 575, 0,
	0, 0, 0, 549, 0, 0, 0, 0, 0, 572,
	0, 0, 0, 568, 0, 548, 0, 0, 0, 577,
	0, 0, 547, 0, 0, 0, 559, 560, 561, 0,
	0, 0, 0, 548, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 563, 0, 0, 1289, 0, 0, 567,
	0, 549, 0, 0, 573, 0, 0, 572, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 567, 0,
	0, 0, 0, 573, 0, 569, 570, 0, 0, 0,
	0, 548, 0, 0, 0, 0, 0, 0, 0, 0,
	565, 0, 0, 0, 569, 570, 0, 0, 1119, 0,
	0, 0, 0, 1118, 0, 0, 566, 0, 0, 565,
	556, 557, 558, 0, 550, 551, 552, 553, 554, 555,
	567, 571, 0, 0, 0, 573, 0, 0, 0, 0,
	0, 0, 0, 0, 578, 0, 0, 564, 567, 0,
	571, 0, 0, 573, 0, 0, 569, 570, 0, 0,
	0, 0, 0, 578, 0, 0, 564, 0, 1283, 0,
	0, 565, 0, 0, 569, 570, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 565,
	0, 0, 0, 0, 0, 0, 567, 577, 0, 0,
	547, 573, 571, 0, 559, 560, 561, 0, 0, 0,
	0, 0, 0, 0, 0, 578, 0, 0, 564, 0,
	571, 563, 569, 570, 0, 568, 0, 0, 0, 549,
	0, 0, 0, 578, 0, 572, 564, 565, 0, 0,
	0, 1284, 0, 0, 568, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 548,
	0, 0, 0, 577, 0, 0, 547, 0, 571, 0,
	559, 560, 561, 0, 0, 0, 0, 0, 0, 0,
	0, 578, 0, 0, 564, 0, 0, 563, 0, 0,
	1137, 0, 0, 0, 0, 549, 568, 0, 0, 0,
	0, 572, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 568, 0, 0, 0, 566, 0,
	0, 0, 556, 557, 558, 548, 550, 551, 552, 553,
	554, 555, 0, 0, 0, 0, 0, 566, 0, 0,
	0, 556, 557, 558, 0, 550, 551, 552, 553, 554,
	555, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 568, 0, 567, 0, 0, 0, 0, 573,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 566,
	569, 570, 0, 556, 557, 558, 0, 550, 551, 552,
	553, 554, 555, 0, 0, 565, 0, 566, 0, 0,
	0, 556, 557, 558, 0, 550, 551, 552, 553, 554,
	555, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	567, 0, 0, 0, 0, 573, 571, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 578,
	0, 0, 564, 0, 0, 566, 569, 570, 0, 556,
	557, 558, 0, 550, 551, 552, 553, 554, 555, 0,
	0, 565, 577, 0, 0, 547, 0, 0, 0, 559,
	560, 561, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 563, 0, 0, 0,
	0, 0, 571, 0, 549, 0, 0, 0, 0, 0,
	572, 0, 0, 0, 577, 578, 0, 547, 564, 0,
	0, 559, 560, 561, 0, 0, 0, 0, 0, 0,
	568, 0, 0, 0, 548, 0, 0, 0, 563, 0,
	0, 1120, 577, 0, 0, 547, 549, 0, 0, 559,
	560, 561, 572, 0, 0, 0, 0, 0, 0, 1232,
	0, 0, 577, 0, 0, 547, 563, 0, 0, 559,
	560, 561, 0, 0, 549, 0, 548, 0, 0, 0,
	572, 0, 0, 0, 0, 0, 563, 0, 0, 1086,
	0, 0, 0, 0, 549, 0, 568, 0, 0, 0,
	572, 0, 0, 0, 548, 577, 0, 0, 547, 0,
	0, 0, 559, 560, 561, 0, 0, 0, 0, 0,
	0, 0, 1125, 566, 548, 0, 0, 556, 557, 558,
	0, 550, 551, 552, 553, 554, 555, 549, 0, 567,
	0, 0, 0, 572, 573, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 569, 570, 548, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	565, 567, 0, 0, 0, 0, 573, 0, 0, 566,
	0, 0, 0, 556, 557, 558, 0, 550, 551, 552,
	553, 554, 555, 0, 0, 0, 0, 569, 570, 567,
	0, 571, 0, 0, 573, 0, 0, 0, 0, 0,
	0, 0, 565, 0, 578, 0, 0, 564, 0, 567,
	0, 0, 0, 0, 573, 569, 570, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	565, 0, 0, 571, 0, 569, 570, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 578, 0, 0, 564,
	565, 0, 567, 0, 0, 0, 0, 573, 0, 0,
	0, 571, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 578, 0, 0, 564, 569, 570,
	0, 571, 0, 0, 0, 568, 0, 0, 0, 0,
	0, 0, 1091, 565, 578, 0, 0, 564, 577, 0,
	0, 547, 0, 0, 0, 559, 560, 561, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 563, 0, 571, 0, 0, 568, 0, 0,
	549, 0, 0, 0, 0, 0, 572, 578, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 568, 0, 0, 0, 0,
	548, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 568, 0, 0, 566, 0,
	0, 0, 556, 557, 558, 0, 550, 551, 552, 553,
	554, 555, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 568, 0,
	566, 0, 0, 0, 556, 557, 558, 0, 550, 551,
	552, 553, 554, 555, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 566, 0,
	0, 0, 556, 557, 558, 0, 550, 551, 552, 553,
	554, 555, 0, 0, 0, 567, 0, 0, 566, 0,
	573, 0, 556, 557, 558, 0, 550, 551, 552, 553,
	554, 555, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 569, 570, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 565, 0, 0, 0,
	0, 566, 0, 0, 0, 556, 557, 558, 0, 550,
	551, 552, 553, 554, 555, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 571, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	578, 0, 0, 564, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 568, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 566, 0, 0, 0, 556, 557,
	558, 0, 550, 551, 552, 553, 554, 555,
}
var yyPact = [...]int{

	-188, -1000, -305, -1000, -1000, -1000, 220, -188, 532, -317,
	15104, -196, -1000, -1000, 297, 513, 513, 513, 500, -229,
	-230, 7651, 7651, -1000, 177, -196, -1000, -128, 14239, -310,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	7210, 304, 369, 367, 196, 180, 300, -1000, 8100, 283,
	9423, 183, -188, -1000, -1000, -188, -188, 9423, -1000, -1000,
	269, -320, -1000, 18492, -1000, -1000, 9423, 9423, 9423, 9423,
	9423, 155, -1000, -1000, 4997, -1000, -1000, -310, -150, -231,
	-1000, -1000, -1000, -220, -152, -310, -1000, -1000, -1000, -1000,
	-1000, 212, 657, 188, -1000, -1000, -1000, 9423, -87, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	359, -1000, -154, -157, -158, -160, -1000, -1000, -1000, -1000,
	-1000, -1000, -161, -162, -166, -167, -173, -174, -176, -177,
	-178, -179, -180, -181, -182, -185, -187, -190, -191, -192,
	-193, 132, -1000, -56, -1000, -56, -56, -205, -205, -204,
	-1000, -1000, 501, -56, -205, -1000, -1000, -269, -259, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, 163, -104, -194, -1000,
	-1000, -1000, 15535, -310, -1000, 2362, 9423, -332, -1000, 19329,
	-1000, -1000, -1000, -1000, -1000, -1000, 207, 173, -1000, 249,
	-1000, 49, -1000, -1000, -1000, 19329, -1000, 156, -1000, -1000,
	-1000, 83, 19329, -1000, 142, 15535, 273, -1000, -1000, -1000,
	273, -321, -1000, 17951, 353, 14673, 7651, 16397, 15535, 1,
	9423, 9423, 9423, 9423, 9423, 9423, 9423, 9423, 9423, 9423,
	9423, 9423, 12937, 9423, 9423, 9423, 748, 9423, -4, 838,
	-1000, -1000, 347, -206, 374, 2793, -1000, -1000, -195, -1000,
	-1000, 619, 619, 133, 19116, 19116, -137, 17899, -316, -323,
	-196, -310, -1000, -1000, -1000, 5879, 13374, 5438, -310, 3224,
	-1000, -1000, 547, 645, -68, 19329, 377, 308, -197, 645,
	645, 645, 645, 9423, 804, 9423, 10746, 9423, 9423, 3674,
	9423, 9423, 9423, 9423, 9423, 206, 11628, 9423, 446, 204,
	9423, 446, -1000, -199, -1000, -1000, -1000, -1000, 9423, -1000,
	-1000, 645, -56, -56, -1000, -1000, 645, -1000, 0, -1,
	645, -1000, 645, -1000, 56, 353, 9423, -236, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, 287, 7210, -1000, -1000,
	-39, -1000, 83, -1000, 9423, -1000, 645, 645, -1000, -1000,
	-1000, -1000, -1000, 219, -324, -1000, 9423, 1835, -113, -1000,
	-1000, -70, 9423, -1000, 16, 16, 13, 8, 16, 15535,
	-1000, -1000, -1000, 525, 16816, -1000, -1000, -1000, -1000, -1000,
	-46, -306, -1000, -1000, -1000, -1000, -1000, -204, -205, -205,
	-205, -1000, -1000, -1000, -1000, -1000, -259, -269, -1000, -1000,
	-1000, -56, -56, -56, -1000, 501, -56, -1000, -301, -105,
	222, 222, 298, 298, 298, 777, 52, 52, 52, 52,
	52, 52, 133, 19116, 1731, 1697, 9423, 9423, -8, 336,
	-206, 1632, 9423, -1000, 1165, -1000, -1000, -1000, 323, -208,
	-1000, 10746, 10746, -1000, -1000, -1000, 4997, -209, -1000, -1000,
	-1000, -1000, 13374, -1000, -211, 9423, -1000, 9423, -336, -337,
	-1000, 19329, -1000, -237, -1000, -302, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -340,
	-1000, -1000, -155, 9423, 9423, 9423, -239, -1000, 19329, 647,
	-1000, -1000, -9, -1000, -11, -13, -14, -1000, -212, -242,
	230, -1000, 9423, 160, -213, -214, 9423, -244, -245, -247,
	-248, 19073, -250, 320, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -251, 19053, -252, 1654, -1000, 10746, 10746, 10746,
	4997, -215, -253, 18474, -334, 19025, 6769, 6769, 6769, -254,
	18983, 9423, -334, 17235, -344, -345, -349, -350, 2793, 147,
	-351, -1000, 18774, 9423, -1000, -1000, 2793, 1808, 9423, 9423,
	-355, -255, -1000, -1000, -261, -107, -108, -266, -268, 15535,
	-70, -356, -1000, -1000, 9423, -1000, -1000, 172, 17862, -1000,
	-1000, -1000, 15535, -1000, -113, -1000, -216, -1000, 310, 306,
	9423, -33, -1000, 18422, 15535, -1000, 15535, 16, 16, 16,
	16, 15535, -1000, -153, -136, 523, -1000, 645, -1000, -311,
	2793, -303, 9423, 9423, 1619, 313, 9423, 10746, 10746, -1000,
	9423, 307, -1000, -1000, -1000, -1000, 319, -217, -1000, 9423,
	16397, 448, 329, -361, -1000, 4997, -271, 4556, -325, -310,
	17843, 9423, -1000, -1000, -139, -1000, 13374, -1000, -272, 6328,
	-1000, 166, -175, -175, -1000, 9423, 9423, 209, 238, 175,
	67, 645, 657, 395, -1000, 9423, 18708, -1000, 13805, -76,
	166, 17823, -1000, -1000, -1000, -1000, 16397, -1000, 9423, -1000,
	318, 9423, -1000, 16397, 10746, 10746, 10746, 10746, 10746, 10746,
	10746, 10746, 10746, 10746, 10746, 10746, 11187, 663, 10746, -220,
	553, 553, 240, -326, 4115, -1000, 337, 318, 9423, 9423,
	16397, -273, -274, -276, -1000, 9423, -334, 9423, -1000, -1000,
	-1000, -1000, -362, -277, 12062, -1000, 9423, 2793, 18403, -367,
	-59, 18540, -368, -1000, -1000, -97, -1000, -1000, -97, 474,
	-1000, 306, -1000, 17918, -1000, -1000, -1000, -1000, 13374, -1000,
	-1000, 302, 492, 19329, 9423, 254, 253, 9423, 693, -1000,
	-1000, -1000, 15535, 15535, 15535, 15535, -1000, 244, 645, -153,
	-228, -280, 2793, -1000, -1000, 557, 355, 9423, 9423, 149,
	233, 179, 355, 9423, 9423, 16397, 271, -369, -1000, 9423,
	9423, -1000, 17570, -1000, -373, -1000, 9423, -1000, -1000, 19329,
	-1000, -1000, 657, 9423, -1000, -282, -283, 9423, -284, 19329,
	19329, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -286, -1000,
	-1000, 19329, 9423, -1000, -1000, 15966, 9423, -288, -1000, -289,
	19329, 337, 19329, -1000, 294, 294, 265, 265, 265, 553,
	201, 201, 201, 201, 201, 201, 240, 317, 332, -221,
	-1000, 1753, 9423, -374, -1000, -1000, -1000, 19329, 19329, -290,
	-1000, -1000, -1000, -334, 17368, -1000, 10305, -1000, 490, 114,
	-1000, -1000, -291, -79, -81, -1000, 9423, -327, 9864, 16816,
	-1000, -1000, -1000, -1000, 302, -1000, -293, -83, 9423, 9423,
	-1000, 9423, 9423, 96, -1000, -1000, -1000, -1000, -1000, -1000,
	-142, -143, 645, -1000, -1000, 355, 355, 9423, 9423, 9423,
	355, 271, -375, -1000, 16397, 355, 355, -1000, -1000, 17335,
	-1000, 166, -1000, -1000, -1000, -1000, 19329, 143, -1000, 17292,
	-1000, -1000, -1000, 10746, 315, -222, -1000, 16397, 17272, -1000,
	-1000, -1000, -379, -380, -223, 113, 11628, -1000, -1000, -1000,
	17253, -296, 35, 19329, -49, -297, -83, -1000, -1000, 15535,
	19329, -328, -1000, 19329, -1000, -1000, -225, 9423, -1000, -1000,
	-144, 355, 355, 355, -1000, -1000, -1000, -298, 166, 488,
	-1000, 1822, 10746, 16397, -381, -1000, -1000, 9423, -1000, 9423,
	-1000, 486, -1000, -1000, 81, -1000, -1000, -1000, -330, -1000,
	520, 9423, 686, 19329, -1000, -1000, 108, 9423, 1822, -385,
	-1000, -386, -390, 111, -1000, -51, 15535, -227, -1000, -391,
	-1000, -299, 8982, 8982, -334, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, 661, -1000, -1000, -1000, -1000, 12496, 274, 88,
	18201, -1000, -1000, 540, -1000, -1000, -1000, -1000, -1000, 8541,
	-1000,
}
var yyPgo = [...]int{

	0, 933, 931, 790, 117, 417, 400, 930, 928, 701,
	927, 14, 925, 5, 51, 920, 467, 501, 49, 919,
	917, 916, 52, 147, 33, 40, 9, 27, 915, 914,
	22, 21, 43, 912, 41, 46, 911, 910, 907, 906,
	905, 904, 903, 29, 16, 901, 900, 44, 397, 898,
	388, 392, 897, 890, 886, 885, 883, 390, 882, 383,
	876, 870, 868, 867, 42, 368, 866, 24, 6, 865,
	26, 864, 69, 863, 0, 861, 20, 860, 4, 37,
	15, 675, 60, 39, 859, 18, 858, 47, 856, 855,
	854, 851, 850, 36, 45, 849, 630, 56, 844, 843,
	841, 54, 19, 840, 820, 819, 11, 7, 3, 817,
	815, 814, 813, 2, 1, 48, 53, 25, 812, 811,
	809, 38, 58, 794, 792, 8, 30, 788, 50, 781,
	778, 771, 31, 538, 28, 605, 13, 765, 762, 736,
	614, 23, 12, 729, 611, 601, 725, 428, 336, 723,
	708, 702, 699, 331, 10, 32, 268,
}
var yyR1 = [...]int{

	0, 1, 1, 29, 29, 29, 30, 30, 30, 69,
	13, 13, 13, 120, 120, 121, 121, 122, 122, 141,
	141, 141, 141, 141, 141, 155, 155, 155, 142, 142,
	142, 142, 142, 142, 142, 150, 150, 150, 150, 139,
	139, 35, 35, 140, 140, 140, 140, 140, 140, 140,
	140, 140, 140, 140, 88, 88, 149, 149, 151, 151,
	147, 148, 143, 143, 152, 152, 144, 145, 146, 146,
	146, 146, 146, 146, 82, 82, 31, 31, 153, 153,
	153, 153, 156, 83, 83, 83, 115, 115, 115, 115,
	115, 115, 115, 115, 115, 115, 115, 115, 115, 115,
	116, 116, 15, 15, 15, 15, 15, 15, 15, 15,
	15, 15, 15, 15, 15, 15, 15, 15, 15, 15,
	15, 15, 15, 15, 15, 15, 15, 15, 15, 15,
	15, 15, 15, 15, 15, 15, 15, 15, 15, 15,
	15, 15, 15, 15, 15, 15, 15, 15, 15, 15,
	15, 15, 15, 15, 15, 15, 15, 15, 15, 15,
	15, 15, 15, 15, 16, 16, 16, 16, 16, 16,
	16, 16, 16, 16, 16, 16, 16, 16, 16, 16,
	16, 16, 16, 16, 16, 16, 16, 16, 16, 17,
	17, 17, 17, 17, 17, 17, 17, 17, 17, 17,
	17, 100, 100, 100, 100, 100, 100, 100, 103, 103,
	104, 104, 104, 104, 104, 104, 104, 104, 104, 104,
	104, 104, 104, 104, 104, 104, 104, 104, 104, 104,
	104, 104, 104, 104, 104, 104, 104, 104, 104, 104,
	104, 104, 104, 104, 104, 104, 104, 104, 104, 104,
	104, 104, 130, 130, 131, 131, 131, 131, 124, 125,
	125, 126, 126, 128, 128, 129, 129, 129, 127, 127,
	127, 127, 19, 19, 20, 20, 20, 20, 20, 18,
	18, 18, 61, 61, 61, 123, 123, 123, 123, 123,
	123, 11, 11, 101, 101, 102, 102, 102, 154, 154,
	117, 117, 117, 118, 118, 39, 39, 40, 40, 40,
	40, 40, 40, 40, 40, 41, 41, 42, 45, 45,
	46, 46, 46, 46, 46, 46, 43, 44, 47, 47,
	47, 2, 2, 4, 4, 3, 3, 3, 3, 5,
	5, 6, 6, 6, 6, 6, 6, 6, 22, 22,
	21, 21, 21, 21, 21, 21, 21, 21, 21, 23,
	23, 81, 81, 81, 10, 10, 12, 12, 26, 26,
	27, 28, 28, 25, 25, 76, 76, 77, 77, 78,
	80, 80, 57, 57, 56, 56, 58, 58, 59, 60,
	60, 60, 60, 63, 63, 106, 106, 105, 105, 107,
	109, 109, 109, 108, 110, 110, 111, 111, 112, 112,
	112, 113, 113, 114, 114, 114, 114, 114, 38, 38,
	38, 38, 48, 48, 48, 48, 49, 49, 50, 50,
	51, 51, 52, 52, 53, 54, 54, 54, 55, 32,
	32, 33, 33, 7, 7, 24, 24, 36, 36, 37,
	37, 97, 97, 97, 98, 98, 99, 73, 73, 73,
	72, 72, 71, 71, 71, 71, 71, 71, 71, 71,
	71, 71, 71, 71, 74, 74, 75, 75, 79, 79,
	89, 92, 92, 93, 91, 91, 90, 90, 119, 119,
	64, 64, 64, 64, 65, 65, 66, 66, 34, 34,
	94, 94, 95, 95, 96, 8, 8, 9, 9, 14,
	14, 14, 14, 62, 62, 70, 70, 67, 138, 138,
	86, 86, 86, 86, 86, 86, 86, 86, 86, 86,
	86, 86, 86, 87, 85, 84, 84, 84, 68, 68,
	68, 135, 135, 135, 132, 132, 132, 132, 132, 133,
	133, 133, 133, 133, 133, 133, 133, 133, 133, 133,
	133, 133, 133, 133, 133, 133, 133, 133, 133, 133,
	133, 133, 133, 133, 133, 133, 133, 133, 133, 133,
	133, 133, 133, 133, 133, 133, 133, 133, 133, 133,
	133, 133, 133, 133, 133, 133, 133, 133, 133, 133,
	133, 133, 133, 133, 133, 133, 133, 133, 133, 133,
	133, 133, 133, 133, 133, 133, 133, 133, 133, 133,
	133, 133, 133, 133, 133, 133, 133, 133, 133, 133,
	133, 133, 133, 133, 133, 133, 133, 133, 133, 133,
	133, 133, 133, 133, 133, 133, 133, 133, 133, 133,
	133, 133, 133, 133, 133, 133, 133, 133, 133, 133,
	133, 133, 133, 133, 133, 133, 133, 133, 133, 133,
	133, 133, 133, 133, 133, 133, 133, 133, 133, 133,
	133, 133, 133, 133, 133, 133, 133, 133, 133, 133,
	133, 133, 133, 133, 133, 133, 133, 133, 133, 133,
	133, 133, 133, 133, 133, 133, 133, 133, 133, 133,
	133, 133, 133, 133, 133, 133, 133, 133, 133, 133,
	133, 133, 133, 133, 133, 133, 133, 133, 133, 133,
	133, 133, 133, 133, 133, 133, 133, 133, 133, 133,
	133, 133, 133, 133, 133, 133, 133, 133, 133, 133,
	133, 133, 133, 133, 133, 133, 133, 133, 133, 133,
	133, 133, 133, 133, 133, 133, 133, 133, 133, 133,
	133, 133, 133, 133, 133, 133, 133, 133, 133, 133,
	133, 133, 133, 133, 133, 133, 133, 133, 133, 133,
	133, 133, 133, 133, 133, 133, 133, 133, 133, 133,
	133, 133, 133, 133, 133, 133, 133, 133, 133, 133,
	133, 133, 133, 133, 133, 133, 133, 134, 134, 134,
	134, 134, 134, 134, 134, 134, 134, 134, 134, 134,
	134, 134, 134, 134, 134, 134, 134, 134, 134, 134,
	134, 134, 134, 134, 134, 134, 134, 134, 134, 134,
	134, 134, 134, 134, 134, 134, 134, 134, 134, 134,
	134, 134, 134, 134, 134, 136, 136, 136, 136, 136,
	136, 136, 136, 136, 136, 136, 136, 136, 136, 136,
	136, 136, 136, 136, 136, 136, 136, 136, 137, 137,
	137, 137, 137, 137, 137, 137, 137, 137, 137, 137,
	137, 137, 137, 137, 137, 137, 137, 137, 137, 137,
	137, 137, 137, 137, 137, 137, 137, 137, 137, 137,
	137, 137, 137, 137, 137, 137, 137, 137, 137, 137,
	137, 137, 137, 137, 137, 137, 137, 137, 137, 137,
	137, 137, 137, 137, 137, 137, 137, 137, 137, 137,
	137, 137, 137, 137, 137, 137, 137, 137, 137, 137,
	137, 137, 137, 137, 137,
}
var yyR2 = [...]int{

	0, 1, 2, 1, 1, 0, 2, 2, 0, 1,
	1, 3, 2, 1, 2, 2, 3, 1, 3, 2,
	3, 5, 6, 2, 3, 3, 4, 0, 1, 1,
	1, 1, 1, 2, 4, 1, 1, 1, 1, 2,
	3, 3, 0, 1, 1, 1, 1, 1, 2, 2,
	2, 2, 2, 1, 3, 0, 1, 1, 1, 1,
	5, 2, 1, 1, 1, 1, 5, 2, 2, 2,
	1, 3, 3, 2, 1, 0, 3, 0, 5, 2,
	5, 2, 1, 3, 3, 0, 1, 1, 1, 1,
	1, 1, 3, 3, 3, 3, 3, 3, 3, 0,
	1, 4, 1, 3, 3, 5, 2, 2, 3, 3,
	3, 3, 3, 3, 3, 3, 3, 3, 3, 3,
	3, 2, 2, 3, 3, 2, 2, 3, 5, 4,
	6, 3, 5, 4, 6, 4, 6, 5, 7, 3,
	2, 4, 2, 3, 3, 4, 3, 4, 3, 4,
	5, 6, 6, 7, 6, 7, 6, 7, 3, 4,
	4, 6, 3, 4, 1, 3, 2, 2, 3, 3,
	3, 3, 3, 3, 3, 3, 3, 3, 3, 3,
	3, 2, 2, 5, 6, 6, 7, 3, 4, 1,
	1, 4, 1, 1, 1, 2, 2, 2, 2, 1,
	1, 3, 5, 6, 8, 6, 6, 4, 4, 1,
	5, 1, 1, 4, 1, 4, 1, 4, 1, 4,
	1, 1, 1, 1, 1, 1, 6, 4, 4, 4,
	4, 6, 5, 5, 5, 4, 6, 4, 4, 4,
	4, 5, 7, 7, 9, 5, 4, 6, 5, 7,
	7, 7, 2, 3, 3, 3, 4, 0, 4, 1,
	3, 3, 1, 1, 1, 2, 2, 0, 2, 4,
	4, 6, 1, 3, 3, 4, 4, 7, 5, 2,
	2, 0, 1, 2, 0, 1, 4, 1, 2, 1,
	2, 1, 3, 1, 3, 1, 3, 3, 1, 3,
	3, 3, 2, 1, 3, 3, 0, 1, 1, 1,
	1, 1, 1, 1, 1, 4, 3, 2, 3, 0,
	3, 3, 2, 2, 1, 0, 2, 2, 3, 2,
	1, 1, 1, 3, 3, 1, 2, 4, 4, 1,
	1, 9, 9, 1, 2, 4, 4, 4, 2, 0,
	3, 3, 4, 4, 4, 4, 3, 2, 1, 1,
	0, 1, 1, 0, 1, 5, 1, 0, 1, 0,
	3, 1, 3, 4, 3, 3, 0, 1, 3, 1,
	2, 0, 1, 3, 1, 0, 1, 2, 3, 2,
	4, 2, 3, 2, 0, 2, 0, 1, 3, 3,
	2, 2, 0, 6, 1, 0, 3, 0, 2, 2,
	0, 1, 4, 2, 2, 2, 2, 2, 1, 2,
	2, 4, 2, 2, 1, 1, 1, 0, 2, 5,
	2, 3, 1, 1, 1, 1, 3, 0, 1, 1,
	1, 1, 1, 2, 3, 2, 0, 5, 0, 5,
	0, 4, 3, 5, 4, 3, 5, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 4, 1, 4, 1, 3,
	5, 1, 2, 4, 2, 0, 1, 0, 1, 2,
	2, 2, 3, 5, 1, 2, 0, 2, 1, 0,
	1, 1, 1, 3, 3, 1, 0, 1, 3, 3,
	2, 1, 1, 1, 3, 1, 2, 1, 1, 2,
	1, 1, 1, 1, 1, 2, 6, 2, 3, 5,
	1, 1, 1, 1, 1, 1, 2, 2, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1,
}
var yyChk = [...]int{

	-1000, -1, -2, -3, -4, -6, -5, 452, 330, -7,
	363, 403, 455, -27, -26, 389, 198, 137, 265, -3,
	-4, -12, -10, 27, 122, 456, -38, -70, 260, -68,
	4, -133, -134, 19, 20, 21, 22, 23, 24, 25,
	26, 28, 29, 30, 38, 39, 41, 42, 44, 45,
	46, 53, 54, 55, 56, 57, 60, 61, 64, 66,
	67, 68, 69, 74, 75, 76, 77, 79, 80, 81,
	83, 84, 85, 86, 87, 88, 91, 92, 93, 101,
	102, 103, 104, 105, 106, 109, 111, 113, 114, 115,
	116, 117, 119, 120, 121, 124, 125, 126, 127, 128,
	130, 131, 132, 134, 135, 136, 138, 139, 140, 141,
	143, 144, 145, 148, 150, 151, 153, 155, 157, 161,
	162, 163, 165, 169, 171, 172, 173, 174, 175, 177,
	178, 179, 180, 182, 183, 184, 185, 186, 187, 189,
	192, 193, 194, 195, 201, 204, 206, 207, 208, 209,
	210, 213, 216, 219, 220, 221, 224, 225, 226, 227,
	228, 229, 230, 231, 232, 233, 234, 235, 236, 237,
	238, 242, 243, 246, 247, 249, 252, 254, 255, 256,
	258, 261, 262, 263, 266, 269, 272, 273, 274, 275,
	276, 277, 278, 280, 281, 283, 286, 287, 285, 289,
	290, 291, 292, 293, 294, 295, 296, 298, 299, 300,
	301, 303, 304, 305, 306, 307, 308, 309, 310, 311,
	312, 313, 315, 316, 318, 319, 320, 322, 323, 324,
	325, 326, 327, 328, 329, 331, 332, 333, 334, 335,
	337, 338, 340, 341, 343, 344, 346, 348, 349, 350,
	351, 352, 353, 354, 355, 356, 357, 358, 361, 362,
	364, 366, 367, 368, 369, 370, 376, 377, 379, 382,
	383, 384, 385, 386, 387, 388, 391, 392, 393, 394,
	395, 398, 399, 400, 401, 402, 406, 408, 409, 410,
	411, 414, 417, 418, 419, 420, 421, 422, 432, 433,
	434, 47, 48, 50, 51, 62, 63, 70, 107, 108,
	142, 146, 152, 166, 168, 191, 196, 197, 199, 214,
	239, 241, 244, 251, 253, 267, 271, 282, 284, 297,
	321, 339, 345, 359, 372, 373, 378, 380, 403, 404,
	423, 424, 425, 426, 427, 428, 429, 430, 431, -96,
	452, -57, -48, -58, 154, -50, -51, -59, 218, 149,
	257, -60, -81, 27, 122, -81, -81, 53, 453, 453,
	-8, -9, -14, -15, 445, -17, 443, 444, -74, 245,
	435, -97, -119, -86, 452, -89, -103, -4, 142, 35,
	-98, -99, 9, 261, 321, -68, -87, 5, -85, 7,
	8, -138, -150, -156, 381, 147, 250, 58, -100, -104,
	4, -133, 10, 6, -135, -140, -151, -152, -153, 199,
	72, 95, 98, 99, 222, 223, 96, 100, 336, 396,
	94, 97, 59, 146, 271, 282, 359, 378, 380, 251,
	70, 166, 214, 424, 425, 426, 427, 428, 429, 430,
	431, 126, 48, 50, 51, 62, 63, 107, 108, 152,
	196, 197, 239, 241, 253, 297, 345, 372, 373, 404,
	-136, -147, -148, -144, -145, 43, 49, 78, 90, 158,
	160, 176, 190, 202, 203, 205, 215, 217, 240, 248,
	268, 270, 317, 342, 365, 407, -146, -9, 259, -96,
	445, -70, 452, -65, -64, 454, 450, -95, -94, -15,
	110, -49, -48, -56, -57, -59, 154, 296, 395, 243,
	340, 206, -51, -50, -52, -15, 27, -33, 151, 242,
	-53, -55, -15, -17, -63, 255, -5, -6, -4, -5,
	-5, -28, -25, -15, -22, 200, 456, 12, 71, 41,
	443, 444, 445, 446, 447, 448, 439, 440, 441, 16,
	17, 18, -74, 33, 264, 217, 435, 176, 342, 202,
	203, 248, 47, 181, -123, 36, 4, 9, 261, -72,
	-71, -15, -15, -15, -15, -15, 270, -15, -4, -11,
	403, -65, -4, -4, -117, 450, 452, 452, -65, 452,
	-85, -85, -85, 452, -90, -15, -36, 417, 154, 452,
	452, 452, 452, 452, 452, 452, 452, 452, 452, 452,
	452, 452, 452, 452, 452, 452, 452, 452, 452, 452,
	452, 452, 284, -82, 406, -82, -82, -35, 452, -35,
	-88, 452, 63, 62, -82, -35, 452, -83, 437, 418,
	452, -83, 452, -31, 63, -22, 452, -70, -64, -67,
	445, -132, 4, -133, -134, -136, -137, 72, 97, 27,
	31, 32, 33, 34, 35, 36, 37, 40, 52, 58,
	59, 65, 71, 73, 82, 89, 94, 95, 96, 98,
	99, 100, 110, 112, 118, 122, 123, 129, 133, 137,
	147, 149, 154, 156, 159, 164, 167, 170, 181, 188,
	198, 200, 211, 212, 218, 222, 223, 245, 250, 257,
	259, 260, 264, 265, 279, 288, 302, 314, 330, 336,
	347, 360, 363, 371, 374, 375, 381, 389, 390, 396,
	397, 405, 412, 413, 415, 416, -15, 456, 453, 260,
	206, 340, -54, -84, 452, -87, 443, 444, -32, 321,
	322, -61, 249, 344, -62, -70, 456, 397, -29, 37,
	118, -18, 159, -21, 369, 367, 221, 163, 393, 363,
	-70, -14, -141, -142, 339, -139, -140, -149, -143, -153,
	-156, -135, 196, 197, 345, 48, 297, 152, 108, 107,
	253, 51, -147, -148, -144, -145, 373, 372, 199, 4,
	-133, 50, 63, 62, 404, 239, 241, -120, -68, 372,
	-15, -15, -15, -15, -15, -15, -15, -15, -15, -15,
	-15, -15, -15, -15, -15, -15, 217, 176, 342, 47,
	181, -15, 374, 250, 245, 381, 147, 391, 122, 255,
	124, -34, 360, 40, -79, -4, 452, -73, 34, 347,
	27, -132, 452, -97, 321, 452, 453, 456, -11, -118,
	451, -15, -117, -122, -72, -68, 9, 443, 444, 445,
	446, 447, 448, 439, 440, 441, 16, 17, 18, -11,
	453, 453, -101, 405, 27, 122, 445, -102, -15, -69,
	-135, -115, 432, 235, 105, 173, 232, -116, 328, -87,
	-92, -93, 412, -37, 150, 167, 452, -87, -87, -87,
	-87, -15, -39, -40, 4, 432, 235, 105, 173, 232,
	328, -85, -41, -15, -45, -16, -17, 443, 444, -74,
	452, 321, -46, -15, -11, -15, 52, 212, 375, -47,
	-15, 159, -11, -15, -11, -11, -11, -11, 237, -17,
	-125, -126, -15, -128, 124, 84, 237, -15, -128, 452,
	-11, -87, -82, -82, -87, 372, 372, -87, -87, 337,
	-18, -11, 453, 451, 457, -94, 395, -32, -15, -87,
	-87, 226, 456, -25, -75, -72, 261, -30, 436, -24,
	413, -13, -20, -15, -23, 363, -23, 369, 367, 369,
	367, -23, -70, -155, 35, -142, -115, 452, -35, -121,
	454, -121, 434, 135, -15, -15, 374, -34, 360, -79,
	135, -15, 250, 381, 147, 391, 122, 255, 124, 159,
	452, -16, -16, -11, -4, 452, -122, 452, -11, -66,
	-15, 456, 451, 451, 456, 453, 454, 453, -26, 456,
	-27, -102, -101, -101, 453, 14, 15, 374, 374, 374,
	374, 452, 453, -91, -93, 129, -15, -109, 269, 452,
	452, -15, 453, 453, 453, 453, 36, 453, 159, 453,
	-42, 279, 453, 12, 443, 444, 445, 446, 447, 448,
	439, 440, 441, 16, 17, 18, -74, 202, 181, 261,
	-16, -16, -16, -11, 452, 453, -43, -44, 159, 154,
	36, -47, -47, -47, 453, 159, -11, 456, 453, 453,
	453, 453, -132, -127, 277, 453, 456, 36, -15, -132,
	456, -15, -11, 453, 453, 453, 434, 434, 453, 453,
	-68, -24, 453, -15, 260, 453, -70, -30, 452, 151,
	210, -76, 167, -15, 456, 90, 240, 205, 36, 4,
	-70, -70, -23, -23, -23, -23, -70, 450, 450, -155,
	35, -87, 454, -35, -67, -15, -15, 135, 135, -15,
	-16, -16, -15, 135, 159, 452, -15, -154, -141, 33,
	33, 453, -15, 453, -11, 453, 456, -64, 453, -15,
	-117, -122, 453, 405, -102, -26, -26, 456, -26, -15,
	-15, 235, 173, 232, -116, 232, -116, -116, -87, -85,
	133, -15, 371, -108, -68, 452, 413, -27, 453, -141,
	-15, -43, -15, -141, -16, -16, -16, -16, -16, -16,
	-16, -16, -16, -16, -16, -16, -16, 122, 245, 255,
	124, -16, 456, -11, 453, -44, -43, -15, -15, -141,
	453, 453, 453, -11, -15, 453, 456, 453, -17, 53,
	-126, -132, -129, 285, 358, 453, 456, -130, 408, 36,
	453, -83, -83, -31, -76, 451, -122, -80, 170, 53,
	-13, 205, 205, -13, 4, -70, -70, -70, -70, 451,
	-87, -87, 450, 453, -67, -15, -15, 135, 33, 33,
	-15, -15, -154, 453, 456, -15, -15, 453, 453, -15,
	-85, -102, 453, 453, 453, 453, -15, -110, -68, -15,
	453, 453, -44, 159, 122, 255, 124, 452, -15, 453,
	453, 453, -124, -11, 423, 53, 301, 453, 414, 414,
	-15, -131, 456, -15, 243, -142, -80, 453, -106, 415,
	-15, -77, -78, -15, -13, -13, 397, 259, 451, 451,
	-87, -15, -15, -15, 453, -141, 453, -26, -111, 276,
	453, -16, 159, 452, -154, 453, 453, 456, 453, 452,
	301, -17, 453, 453, 350, 402, 453, -106, -105, -107,
	-68, 456, 452, -15, 451, 453, -26, 53, -16, -154,
	453, -11, -125, 53, 433, 243, 456, 36, -78, -19,
	4, -112, 295, 322, -11, 453, 453, 453, 301, 402,
	-107, -108, 456, 453, 453, -113, -114, 47, 386, 93,
	-15, -113, 4, -114, 283, 153, 321, 283, 153, 33,
	-114,
}
var yyDef = [...]int{

	0, -2, 1, 331, -2, -2, 369, 0, 367, 343,
	0, 0, 2, -2, 0, 363, 363, 363, 0, 0,
	340, 506, 0, 366, 364, 0, 344, 418, 0, 515,
	538, 539, 540, 549, 550, 551, 552, 553, 554, 555,
	556, 557, 558, 559, 560, 561, 562, 563, 564, 565,
	566, 567, 568, 569, 570, 571, 572, 573, 574, 575,
	576, 577, 578, 579, 580, 581, 582, 583, 584, 585,
	586, 587, 588, 589, 590, 591, 592, 593, 594, 595,
	596, 597, 598, 599, 600, 601, 602, 603, 604, 605,
	606, 607, 608, 609, 610, 611, 612, 613, 614, 615,
	616, 617, 618, 619, 620, 621, 622, 623, 624, 625,
	626, 627, 628, 629, 630, 631, 632, 633, 634, 635,
	636, 637, 638, 639, 640, 641, 642, 643, 644, 645,
	646, 647, 648, 649, 650, 651, 652, 653, 654, 655,
	656, 657, 658, 659, 660, 661, 662, 663, 664, 665,
	666, 667, 668, 669, 670, 671, 672, 673, 674, 675,
	676, 677, 678, 679, 680, 681, 682, 683, 684, 685,
	686, 687, 688, 689, 690, 691, 692, 693, 694, 695,
	696, 697, 698, 699, 700, 701, 702, 703, 704, 705,
	706, 707, 708, 709, 710, 711, 712, 713, 714, 715,
	716, 717, 718, 719, 720, 721, 722, 723, 724, 725,
	726, 727, 728, 729, 730, 731, 732, 733, 734, 735,
	736, 737, 738, 739, 740, 741, 742, 743, 744, 745,
	746, 747, 748, 749, 750, 751, 752, 753, 754, 755,
	756, 757, 758, 759, 760, 761, 762, 763, 764, 765,
	766, 767, 768, 769, 770, 771, 772, 773, 774, 775,
	776, 777, 778, 779, 780, 781, 782, 783, 784, 785,
	786, 787, 788, 789, 790, 791, 792, 793, 794, 795,
	796, 797, 798, 799, 800, 801, 802, 803, 804, 805,
	806, 807, 808, 809, 810, 811, 812, 813, 814, 815,
	816, 817, 818, 819, 820, 821, 822, 823, 824, 825,
	826, 827, 828, 829, 830, 831, 832, 833, 834, 835,
	836, 837, 838, 839, 840, 841, 842, 843, 844, 845,
	846, 847, 848, 849, 850, 851, 852, 853, 854, 855,
	856, 857, 858, 859, 860, 861, 862, 863, 864, 443,
	0, 427, 385, 382, 0, 424, 425, 386, 0, 0,
	0, 394, 0, 361, 362, 0, 0, 0, 333, 334,
	349, 505, 507, 511, 512, 102, 0, 0, 0, 0,
	0, 0, 189, 190, 0, 192, 193, 194, 826, 0,
	199, 200, 474, 697, 846, 488, 520, 521, 522, 523,
	524, 0, 0, 0, 530, 531, 532, 487, 448, 209,
	-2, -2, 533, 534, 518, 35, 36, 37, 38, -2,
	867, 211, 212, 214, 216, 218, 220, 221, 222, 223,
	224, -2, 0, 827, 842, 843, 849, 852, 853, 839,
	823, 829, 835, 857, 858, 859, 860, 861, 862, 863,
	864, 613, -2, -2, -2, -2, -2, -2, -2, -2,
	-2, -2, 836, -2, -2, -2, -2, -2, -2, -2,
	543, 58, 59, 64, 65, 865, 866, 868, 869, 871,
	872, 873, 874, 875, 876, 877, 878, 879, 880, 881,
	882, 883, 884, 885, 886, 887, 77, 349, 0, 444,
	419, 420, 0, 516, 494, 0, 0, 0, 502, 500,
	501, 337, 426, 338, 384, 387, 0, 0, 389, 0,
	391, 0, 422, 423, 428, 432, 433, 437, 441, 442,
	430, 0, 434, -2, 284, 0, 345, 339, 340, 346,
	347, 370, 371, 5, 281, 0, 0, 0, 0, 0,
	462, 463, 464, 465, 466, 467, 468, 469, 470, 471,
	472, 473, 122, 0, 0, 287, 0, 289, 0, 0,
	140, 142, 499, 0, 0, 0, 510, -2, 0, 285,
	461, 106, 107, 121, 125, 126, 0, 291, -2, 0,
	854, 195, 196, 197, 198, 0, 0, 0, -2, 0,
	525, 527, 99, 0, 0, 486, 450, 0, 0, 0,
	0, 0, 0, 0, 306, 0, 319, 325, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 49, 61, 74, 69, 68, 51, 0, 50,
	48, 0, 75, 75, 73, 52, 0, 81, 0, 0,
	0, 79, 0, 67, 0, 281, 0, 0, 495, 490,
	491, 517, 544, 545, 546, 547, 548, 867, 870, 888,
	889, 890, 891, 892, 893, 894, 895, 896, 897, 898,
	899, 900, 901, 902, 903, 904, 905, 906, 907, 908,
	909, 910, 911, 912, 913, 914, 915, 916, 917, 918,
	919, 920, 921, 922, 923, 924, 925, 926, 927, 928,
	929, 930, 931, 932, 933, 934, 935, 936, 937, 938,
	939, 940, 941, 942, 943, 944, 945, 946, 947, 948,
	949, 950, 951, 952, 953, 954, 955, 956, 957, 958,
	959, 960, 961, 962, 963, 964, 0, 0, 504, 383,
	0, 392, 0, 435, 0, 535, 0, 0, 431, 439,
	440, 388, 282, 0, 393, 513, 0, 0, 8, 3,
	4, 446, 0, 348, -2, -2, 671, 637, -2, 0,
	358, 508, 103, 27, 0, 28, 29, 30, 31, 32,
	99, 42, 43, 44, 45, 46, 47, 55, 42, 42,
	42, 53, 56, 57, 62, 63, 85, 85, 82, 541,
	542, 75, 75, 75, 70, 0, 75, 104, 13, 0,
	108, 109, 110, 111, 112, 113, -2, -2, -2, -2,
	-2, -2, 120, 123, 124, -2, 288, 290, 0, 499,
	0, -2, 0, 139, 0, 144, 146, 148, 0, 0,
	162, 0, 0, 498, 158, 478, 0, 0, 457, 458,
	459, 509, 0, 143, 0, 0, 496, 0, 0, 0,
	302, 291, 303, 0, 17, 0, 460, 462, 463, 464,
	465, 466, 467, 468, 469, 470, 471, 472, 473, 0,
	-2, 201, 369, 0, 0, 0, 0, 293, 295, 0,
	-2, 528, 86, 87, 88, 89, 90, 91, 100, 0,
	485, 481, 0, 402, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 307, 308, 309, 310, 311, 312,
	313, 314, 0, 0, 0, 0, 164, 0, 0, 0,
	0, 846, 0, 291, 324, 0, 0, 0, 0, 0,
	291, 0, 330, 0, 0, 0, 0, 0, 0, 0,
	0, 259, 262, 0, 263, 264, 0, 0, 0, 0,
	0, 0, 71, 72, 0, 0, 0, 0, 0, 0,
	446, 0, 421, 492, 0, 503, 390, 0, 0, 536,
	537, 283, 0, 372, 8, 476, 0, 374, 0, 376,
	0, 279, 280, 10, 0, 359, 0, 360, 360, 360,
	360, 0, 357, 19, 23, 27, 33, 0, 39, 42,
	0, 14, 0, 0, -2, -2, 0, 0, 0, 159,
	0, -2, 141, 145, 147, 149, 0, 0, 163, 0,
	0, 0, 0, 0, 160, 0, 0, 0, 0, 191,
	292, 0, 300, 301, 0, 475, 0, -2, 0, 0,
	368, 369, 369, 369, 207, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 482, 0, 0, 208, 0, 0,
	0, 0, 213, 215, 217, 219, 0, 227, 0, 228,
	0, 0, 229, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 182, 0, 0, 0,
	166, 167, 181, 0, 0, 230, 322, 323, 0, 0,
	0, 0, 0, 0, 235, 0, 329, 0, 237, 238,
	239, 240, 0, 0, 0, 246, 0, 0, 267, 0,
	0, 0, 0, 41, 54, 85, 83, 84, 85, 77,
	76, 376, 365, 0, 429, 436, 514, 373, 0, 6,
	7, 381, 0, 445, 0, 0, 0, 0, 0, 12,
	350, 351, 0, 0, 0, 0, 356, 0, 0, 20,
	24, 0, 0, 40, 15, 105, -2, 0, 0, -2,
	0, 0, -2, 0, 0, 0, -2, 0, 298, 0,
	0, 479, 0, -2, 0, 452, 0, 497, -2, 292,
	304, 18, 202, 0, 294, 0, 0, 0, 0, 296,
	297, 92, 93, 94, 95, 96, 97, 98, 0, 529,
	480, 484, 0, 400, 401, 405, 0, 0, 210, 0,
	305, 316, 317, 165, 168, 169, 170, 171, 172, 173,
	-2, -2, -2, -2, -2, -2, 180, 0, 0, 0,
	187, 318, 0, 0, 455, 320, 321, 326, 327, 0,
	232, 233, 234, 328, 0, 241, 0, 245, 268, 567,
	260, 261, 0, 0, 0, 248, 0, 257, 0, 0,
	60, 80, 78, 66, 381, 493, 0, 396, 0, 0,
	274, 0, 0, 0, 11, 352, 353, 354, 355, 25,
	0, 0, 0, 34, 16, -2, -2, 0, 0, 0,
	-2, -2, 0, 152, 0, -2, -2, 161, 451, 292,
	526, 369, 203, 205, 206, 101, 483, 407, 404, 0,
	447, 226, 315, 0, 0, 0, 188, 0, 292, 454,
	231, 236, 0, 0, 856, 0, 0, 247, 265, 266,
	0, 0, 0, 252, 688, 0, 396, 477, 341, 0,
	380, 375, 377, 379, 275, 276, 0, 0, 26, 21,
	0, -2, -2, -2, 153, 299, 453, 0, 369, 0,
	449, -2, 0, 0, 0, 456, 242, 0, 243, 0,
	269, 270, 249, 250, 0, 253, 251, 342, 395, 397,
	0, 0, 0, 278, 22, 204, 410, 0, -2, 0,
	185, 0, 0, 0, 254, 255, 0, 0, 378, 0,
	272, 0, 0, 0, 406, 186, 244, 258, 271, 256,
	398, 399, 0, 277, 403, 408, 411, -2, 789, 594,
	0, 409, 273, 0, 413, 414, 415, 416, 417, 0,
	412,
}
var yyTok1 = [...]int{

	1, 3, 3, 3, 3, 3, 3, 3, 3, 3,
	3, 3, 3, 3, 3, 3, 3, 3, 3, 3,
	3, 3, 3, 3, 3, 3, 3, 3, 3, 3,
	3, 3, 3, 3, 3, 3, 3, 447, 3, 3,
	452, 453, 445, 443, 456, 444, 454, 446, 3, 3,
	3, 3, 3, 3, 3, 3, 3, 3, 457, 455,
	439, 441, 440, 3, 3, 3, 3, 3, 3, 3,
	3, 3, 3, 3, 3, 3, 3, 3, 3, 3,
	3, 3, 3, 3, 3, 3, 3, 3, 3, 3,
	3, 450, 3, 451, 448,
}
var yyTok2 = [...]int{

	2, 3, 4, 5, 6, 7, 8, 9, 10, 11,
	12, 13, 14, 15, 16, 17, 18, 19, 20, 21,
	22, 23, 24, 25, 26, 27, 28, 29, 30, 31,
	32, 33, 34, 35, 36, 37, 38, 39, 40, 41,
	42, 43, 44, 45, 46, 47, 48, 49, 50, 51,
	52, 53, 54, 55, 56, 57, 58, 59, 60, 61,
	62, 63, 64, 65, 66, 67, 68, 69, 70, 71,
	72, 73, 74, 75, 76, 77, 78, 79, 80, 81,
	82, 83, 84, 85, 86, 87, 88, 89, 90, 91,
	92, 93, 94, 95, 96, 97, 98, 99, 100, 101,
	102, 103, 104, 105, 106, 107, 108, 109, 110, 111,
	112, 113, 114, 115, 116, 117, 118, 119, 120, 121,
	122, 123, 124, 125, 126, 127, 128, 129, 130, 131,
	132, 133, 134, 135, 136, 137, 138, 139, 140, 141,
	142, 143, 144, 145, 146, 147, 148, 149, 150, 151,
	152, 153, 154, 155, 156, 157, 158, 159, 160, 161,
	162, 163, 164, 165, 166, 167, 168, 169, 170, 171,
	172, 173, 174, 175, 176, 177, 178, 179, 180, 181,
	182, 183, 184, 185, 186, 187, 188, 189, 190, 191,
	192, 193, 194, 195, 196, 197, 198, 199, 200, 201,
	202, 203, 204, 205, 206, 207, 208, 209, 210, 211,
	212, 213, 214, 215, 216, 217, 218, 219, 220, 221,
	222, 223, 224, 225, 226, 227, 228, 229, 230, 231,
	232, 233, 234, 235, 236, 237, 238, 239, 240, 241,
	242, 243, 244, 245, 246, 247, 248, 249, 250, 251,
	252, 253, 254, 255, 256, 257,
}
var yyTok3 = [...]int{
	57600, 258, 57601, 259, 57602, 260, 57603, 261, 57604, 262,
	57605, 263, 57606, 264, 57607, 265, 57608, 266, 57609, 267,
	57610, 268, 57611, 269, 57612, 270, 57613, 271, 57614, 272,
	57615, 273, 57616, 274, 57617, 275, 57618, 276, 57619, 277,
	57620, 278, 57621, 279, 57622, 280, 57623, 281, 57624, 282,
	57625, 283, 57626, 284, 57627, 285, 57628, 286, 57629, 287,
	57630, 288, 57631, 289, 57632, 290, 57633, 291, 57634, 292,
	57635, 293, 57636, 294, 57637, 295, 57638, 296, 57639, 297,
	57640, 298, 57641, 299, 57642, 300, 57643, 301, 57644, 302,
	57645, 303, 57646, 304, 57647, 305, 57648, 306, 57649, 307,
	57650, 308, 57651, 309, 57652, 310, 57653, 311, 57654, 312,
	57655, 313, 57656, 314, 57657, 315, 57658, 316, 57659, 317,
	57660, 318, 57661, 319, 57662, 320, 57663, 321, 57664, 322,
	57665, 323, 57666, 324, 57667, 325, 57668, 326, 57669, 327,
	57670, 328, 57671, 329, 57672, 330, 57673, 331, 57674, 332,
	57675, 333, 57676, 334, 57677, 335, 57678, 336, 57679, 337,
	57680, 338, 57681, 339, 57682, 340, 57683, 341, 57684, 342,
	57685, 343, 57686, 344, 57687, 345, 57688, 346, 57689, 347,
	57690, 348, 57691, 349, 57692, 350, 57693, 351, 57694, 352,
	57695, 353, 57696, 354, 57697, 355, 57698, 356, 57699, 357,
	57700, 358, 57701, 359, 57702, 360, 57703, 361, 57704, 362,
	57705, 363, 57706, 364, 57707, 365, 57708, 366, 57709, 367,
	57710, 368, 57711, 369, 57712, 370, 57713, 371, 57714, 372,
	57715, 373, 57716, 374, 57717, 375, 57718, 376, 57719, 377,
	57720, 378, 57721, 379, 57722, 380, 57723, 381, 57724, 382,
	57725, 383, 57726, 384, 57727, 385, 57728, 386, 57729, 387,
	57730, 388, 57731, 389, 57732, 390, 57733, 391, 57734, 392,
	57735, 393, 57736, 394, 57737, 395, 57738, 396, 57739, 397,
	57740, 398, 57741, 399, 57742, 400, 57743, 401, 57744, 402,
	57745, 403, 57746, 404, 57747, 405, 57748, 406, 57749, 407,
	57750, 408, 57751, 409, 57752, 410, 57753, 411, 57754, 412,
	57755, 413, 57756, 414, 57757, 415, 57758, 416, 57759, 417,
	57760, 418, 57761, 419, 57762, 420, 57763, 421, 57764, 422,
	57765, 423, 57766, 424, 57767, 425, 57768, 426, 57769, 427,
	57770, 428, 57771, 429, 57772, 430, 57773, 431, 57774, 432,
	57775, 433, 57776, 434, 57777, 435, 57778, 436, 57779, 437,
	57780, 438, 57781, 442, 57782, 449, 0,
}

var yyErrorMessages = [...]struct {
	state int
	token int
	msg   string
}{}

//line yaccpar:1

/*	parser for yacc output	*/

var (
	yyDebug        = 0
	yyErrorVerbose = false
)

type yyLexer interface {
	Lex(lval *yySymType) int
	Error(s string)
}

type yyParser interface {
	Parse(yyLexer) int
	Lookahead() int
}

type yyParserImpl struct {
	lookahead func() int
}

func (p *yyParserImpl) Lookahead() int {
	return p.lookahead()
}

func yyNewParser() yyParser {
	p := &yyParserImpl{
		lookahead: func() int { return -1 },
	}
	return p
}

const yyFlag = -1000

func yyTokname(c int) string {
	if c >= 1 && c-1 < len(yyToknames) {
		if yyToknames[c-1] != "" {
			return yyToknames[c-1]
		}
	}
	return __yyfmt__.Sprintf("tok-%v", c)
}

func yyStatname(s int) string {
	if s >= 0 && s < len(yyStatenames) {
		if yyStatenames[s] != "" {
			return yyStatenames[s]
		}
	}
	return __yyfmt__.Sprintf("state-%v", s)
}

func yyErrorMessage(state, lookAhead int) string {
	const TOKSTART = 4

	if !yyErrorVerbose {
		return "syntax error"
	}

	for _, e := range yyErrorMessages {
		if e.state == state && e.token == lookAhead {
			return "syntax error: " + e.msg
		}
	}

	res := "syntax error: unexpected " + yyTokname(lookAhead)

	// To match Bison, suggest at most four expected tokens.
	expected := make([]int, 0, 4)

	// Look for shiftable tokens.
	base := yyPact[state]
	for tok := TOKSTART; tok-1 < len(yyToknames); tok++ {
		if n := base + tok; n >= 0 && n < yyLast && yyChk[yyAct[n]] == tok {
			if len(expected) == cap(expected) {
				return res
			}
			expected = append(expected, tok)
		}
	}

	if yyDef[state] == -2 {
		i := 0
		for yyExca[i] != -1 || yyExca[i+1] != state {
			i += 2
		}

		// Look for tokens that we accept or reduce.
		for i += 2; yyExca[i] >= 0; i += 2 {
			tok := yyExca[i]
			if tok < TOKSTART || yyExca[i+1] == 0 {
				continue
			}
			if len(expected) == cap(expected) {
				return res
			}
			expected = append(expected, tok)
		}

		// If the default action is to accept or reduce, give up.
		if yyExca[i+1] != 0 {
			return res
		}
	}

	for i, tok := range expected {
		if i == 0 {
			res += ", expecting "
		} else {
			res += " or "
		}
		res += yyTokname(tok)
	}
	return res
}

func yylex1(lex yyLexer, lval *yySymType) (char, token int) {
	token = 0
	char = lex.Lex(lval)
	if char <= 0 {
		token = yyTok1[0]
		goto out
	}
	if char < len(yyTok1) {
		token = yyTok1[char]
		goto out
	}
	if char >= yyPrivate {
		if char < yyPrivate+len(yyTok2) {
			token = yyTok2[char-yyPrivate]
			goto out
		}
	}
	for i := 0; i < len(yyTok3); i += 2 {
		token = yyTok3[i+0]
		if token == char {
			token = yyTok3[i+1]
			goto out
		}
	}

out:
	if token == 0 {
		token = yyTok2[1] /* unknown char */
	}
	if yyDebug >= 3 {
		__yyfmt__.Printf("lex %s(%d)\n", yyTokname(token), uint(char))
	}
	return char, token
}

func yyParse(yylex yyLexer) int {
	return yyNewParser().Parse(yylex)
}

func (yyrcvr *yyParserImpl) Parse(yylex yyLexer) int {
	var yyn int
	var yylval yySymType
	var yyVAL yySymType
	var yyDollar []yySymType
	_ = yyDollar // silence set and not used
	yyS := make([]yySymType, yyMaxDepth)

	Nerrs := 0   /* number of errors */
	Errflag := 0 /* error recovery flag */
	yystate := 0
	yychar := -1
	yytoken := -1 // yychar translated into internal numbering
	yyrcvr.lookahead = func() int { return yychar }
	defer func() {
		// Make sure we report no lookahead when not parsing.
		yystate = -1
		yychar = -1
		yytoken = -1
	}()
	yyp := -1
	goto yystack

ret0:
	return 0

ret1:
	return 1

yystack:
	/* put a state and value onto the stack */
	if yyDebug >= 4 {
		__yyfmt__.Printf("char %v in %v\n", yyTokname(yytoken), yyStatname(yystate))
	}

	yyp++
	if yyp >= len(yyS) {
		nyys := make([]yySymType, len(yyS)*2)
		copy(nyys, yyS)
		yyS = nyys
	}
	yyS[yyp] = yyVAL
	yyS[yyp].yys = yystate

yynewstate:
	yyn = yyPact[yystate]
	if yyn <= yyFlag {
		goto yydefault /* simple state */
	}
	if yychar < 0 {
		yychar, yytoken = yylex1(yylex, &yylval)
	}
	yyn += yytoken
	if yyn < 0 || yyn >= yyLast {
		goto yydefault
	}
	yyn = yyAct[yyn]
	if yyChk[yyn] == yytoken { /* valid shift */
		yychar = -1
		yytoken = -1
		yyVAL = yylval
		yystate = yyn
		if Errflag > 0 {
			Errflag--
		}
		goto yystack
	}

yydefault:
	/* default state action */
	yyn = yyDef[yystate]
	if yyn == -2 {
		if yychar < 0 {
			yychar, yytoken = yylex1(yylex, &yylval)
		}

		/* look through exception table */
		xi := 0
		for {
			if yyExca[xi+0] == -1 && yyExca[xi+1] == yystate {
				break
			}
			xi += 2
		}
		for xi += 2; ; xi += 2 {
			yyn = yyExca[xi+0]
			if yyn < 0 || yyn == yytoken {
				break
			}
		}
		yyn = yyExca[xi+1]
		if yyn < 0 {
			goto ret0
		}
	}
	if yyn == 0 {
		/* error ... attempt to resume parsing */
		switch Errflag {
		case 0: /* brand new error */
			yylex.Error(yyErrorMessage(yystate, yytoken))
			Nerrs++
			if yyDebug >= 1 {
				__yyfmt__.Printf("%s", yyStatname(yystate))
				__yyfmt__.Printf(" saw %s\n", yyTokname(yytoken))
			}
			fallthrough

		case 1, 2: /* incompletely recovered error ... try again */
			Errflag = 3

			/* find a state where "error" is a legal shift action */
			for yyp >= 0 {
				yyn = yyPact[yyS[yyp].yys] + yyErrCode
				if yyn >= 0 && yyn < yyLast {
					yystate = yyAct[yyn] /* simulate a shift of "error" */
					if yyChk[yystate] == yyErrCode {
						goto yystack
					}
				}

				/* the current p has no shift on "error", pop stack */
				if yyDebug >= 2 {
					__yyfmt__.Printf("error recovery pops state %d\n", yyS[yyp].yys)
				}
				yyp--
			}
			/* there is no state on the stack with an error shift ... abort */
			goto ret1

		case 3: /* no shift yet; clobber input char */
			if yyDebug >= 2 {
				__yyfmt__.Printf("error recovery discards %s\n", yyTokname(yytoken))
			}
			if yytoken == yyEofCode {
				goto ret1
			}
			yychar = -1
			yytoken = -1
			goto yynewstate /* try again in the same state */
		}
	}

	/* reduction by production yyn */
	if yyDebug >= 2 {
		__yyfmt__.Printf("reduce %v in:\n\t%v\n", yyn, yyStatname(yystate))
	}

	yynt := yyn
	yypt := yyp
	_ = yypt // guard against "declared and not used"

	yyp -= yyR2[yyn]
	// yyp is now the index of $0. Perform the default action. Iff the
	// reduced production is ε, $1 is possibly out of range.
	if yyp+1 >= len(yyS) {
		nyys := make([]yySymType, len(yyS)*2)
		copy(nyys, yyS)
		yyS = nyys
	}
	yyVAL = yyS[yyp+1]

	/* consult goto table to find next state */
	yyn = yyR1[yyn]
	yyg := yyPgo[yyn]
	yyj := yyg + yyS[yyp].yys + 1

	if yyj >= yyLast {
		yystate = yyAct[yyg]
	} else {
		yystate = yyAct[yyj]
		if yyChk[yystate] != -yyn {
			yystate = yyAct[yyg]
		}
	}
	// dummy call; replaced with literal code
	switch yynt {

	case 1:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:407
		{
			yyVAL.sqlSelect = yyDollar[1].sqlSelect
			yylex.(*sqlLex).stmt = yyDollar[1].sqlSelect
		}
	case 2:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:412
		{
			yyVAL.sqlSelect = yyDollar[1].sqlSelect
			yyVAL.sqlSelect.Semicolon = true
			yylex.(*sqlLex).stmt = yyDollar[1].sqlSelect
		}
	case 3:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:419
		{
			yyVAL.str = "asc"
		}
	case 4:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:420
		{
			yyVAL.str = "desc"
		}
	case 5:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:421
		{
			yyVAL.str = ""
		}
	case 6:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:424
		{
			yyVAL.str = "first"
		}
	case 7:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:425
		{
			yyVAL.str = "last"
		}
	case 8:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:426
		{
			yyVAL.str = ""
		}
	case 10:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:435
		{
			yyVAL.expr = yyDollar[1].expr
		}
	case 11:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:439
		{
			yyVAL.expr = AliasedExpr{Expr: yyDollar[1].expr, Alias: yyDollar[3].str}
		}
	case 12:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:443
		{
			yyVAL.expr = AliasedExpr{Expr: yyDollar[1].expr, Alias: yyDollar[2].str}
		}
	case 13:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:450
		{
			yyVAL.anyName = AnyName{yyDollar[1].str}
		}
	case 14:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:454
		{
			yyVAL.anyName = AnyName{yyDollar[1].str}
			yyVAL.anyName = append(yyVAL.anyName, yyDollar[2].anyName...)
		}
	case 15:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:461
		{
			yyVAL.anyName = AnyName{yyDollar[2].str}
		}
	case 16:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:465
		{
			yyVAL.anyName = append(yyDollar[1].anyName, yyDollar[3].str)
		}
	case 17:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:471
		{
			yyVAL.anyName = AnyName{yyDollar[1].str}
		}
	case 18:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:475
		{
			yyVAL.anyName = append(AnyName{yyDollar[1].str}, yyDollar[3].anyName...)
		}
	case 19:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:491
		{
			yyVAL.pgType = yyDollar[1].pgType
			yyVAL.pgType.ArrayBounds = yyDollar[2].optArrayBounds
		}
	case 20:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:496
		{
			yyVAL.pgType = yyDollar[2].pgType
			yyVAL.pgType.Setof = true
			yyVAL.pgType.ArrayBounds = yyDollar[3].optArrayBounds
		}
	case 21:
		yyDollar = yyS[yypt-5 : yypt+1]
		//line sql.y:503
		{
			yyVAL.pgType = yyDollar[1].pgType
			yyVAL.pgType.ArrayWord = true
			yyVAL.pgType.ArrayBounds = []IntegerConst{yyDollar[4].iconst}
		}
	case 22:
		yyDollar = yyS[yypt-6 : yypt+1]
		//line sql.y:509
		{
			yyVAL.pgType = yyDollar[2].pgType
			yyVAL.pgType.Setof = true
			yyVAL.pgType.ArrayWord = true
			yyVAL.pgType.ArrayBounds = []IntegerConst{yyDollar[5].iconst}
		}
	case 23:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:516
		{
			yyVAL.pgType = yyDollar[1].pgType
			yyVAL.pgType.ArrayWord = true
		}
	case 24:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:521
		{
			yyVAL.pgType = yyDollar[2].pgType
			yyVAL.pgType.Setof = true
			yyVAL.pgType.ArrayWord = true
		}
	case 25:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:529
		{
			yyVAL.optArrayBounds = append(yyDollar[1].optArrayBounds, "")
		}
	case 26:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:533
		{
			yyVAL.optArrayBounds = append(yyDollar[1].optArrayBounds, yyDollar[3].iconst)
		}
	case 27:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:537
		{
			yyVAL.optArrayBounds = nil
		}
	case 33:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:546
		{
			yyVAL.pgType = PgType{Name: AnyName{"interval"}, OptInterval: yyDollar[2].optInterval}
		}
	case 34:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:550
		{
			yyVAL.pgType = PgType{Name: AnyName{"interval"}, TypeMods: []Expr{yyDollar[3].iconst}}
		}
	case 39:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:581
		{
			yyVAL.pgType = PgType{Name: AnyName{yyDollar[1].str}, TypeMods: yyDollar[2].fields}
		}
	case 40:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:585
		{
			yyVAL.pgType = PgType{Name: append(AnyName{yyDollar[1].str}, yyDollar[2].anyName...), TypeMods: yyDollar[3].fields}
		}
	case 41:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:590
		{
			yyVAL.fields = yyDollar[2].fields
		}
	case 42:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:591
		{
			yyVAL.fields = nil
		}
	case 43:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:598
		{
			yyVAL.pgType = PgType{Name: AnyName{"int"}}
		}
	case 44:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:602
		{
			yyVAL.pgType = PgType{Name: AnyName{"integer"}}
		}
	case 45:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:606
		{
			yyVAL.pgType = PgType{Name: AnyName{"smallint"}}
		}
	case 46:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:610
		{
			yyVAL.pgType = PgType{Name: AnyName{"bigint"}}
		}
	case 47:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:614
		{
			yyVAL.pgType = PgType{Name: AnyName{"real"}}
		}
	case 48:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:618
		{
			yyVAL.pgType = PgType{Name: AnyName{"float"}}
			if yyDollar[2].iconst != IntegerConst("") {
				yyVAL.pgType.TypeMods = []Expr{yyDollar[2].iconst}
			}
		}
	case 49:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:625
		{
			yyVAL.pgType = PgType{Name: AnyName{"double precision"}}
		}
	case 50:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:629
		{
			yyVAL.pgType = PgType{Name: AnyName{"decimal"}, TypeMods: yyDollar[2].fields}
		}
	case 51:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:633
		{
			yyVAL.pgType = PgType{Name: AnyName{"dec"}, TypeMods: yyDollar[2].fields}
		}
	case 52:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:637
		{
			yyVAL.pgType = PgType{Name: AnyName{"numeric"}, TypeMods: yyDollar[2].fields}
		}
	case 53:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:641
		{
			yyVAL.pgType = PgType{Name: AnyName{"bool"}}
		}
	case 54:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:647
		{
			yyVAL.iconst = yyDollar[2].iconst
		}
	case 55:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:651
		{
			yyVAL.iconst = IntegerConst("")
		}
	case 60:
		yyDollar = yyS[yypt-5 : yypt+1]
		//line sql.y:665
		{
			yyVAL.pgType = PgType{}
			if yyDollar[2].boolean {
				yyVAL.pgType.Name = AnyName{"varbit"}
			} else {
				yyVAL.pgType.Name = AnyName{"bit"}
			}
			yyVAL.pgType.TypeMods = yyDollar[4].fields
		}
	case 61:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:677
		{
			yyVAL.pgType = PgType{}
			if yyDollar[2].boolean {
				yyVAL.pgType = PgType{Name: AnyName{"varbit"}}
			} else {
				yyVAL.pgType = PgType{Name: AnyName{"bit"}}
			}
		}
	case 66:
		yyDollar = yyS[yypt-5 : yypt+1]
		//line sql.y:700
		{
			yyVAL.pgType = yyDollar[1].pgType
			yyVAL.pgType.TypeMods = []Expr{yyDollar[3].iconst}
			yyVAL.pgType.CharSet = yyDollar[5].str
		}
	case 67:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:708
		{
			yyVAL.pgType = yyDollar[1].pgType
			yyVAL.pgType.CharSet = yyDollar[2].str
		}
	case 68:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:715
		{
			if yyDollar[2].boolean {
				yyVAL.pgType = PgType{Name: AnyName{"varchar"}}
			} else {
				yyVAL.pgType = PgType{Name: AnyName{"char"}}
			}
		}
	case 69:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:723
		{
			if yyDollar[2].boolean {
				yyVAL.pgType = PgType{Name: AnyName{"varchar"}}
			} else {
				yyVAL.pgType = PgType{Name: AnyName{"char"}}
			}
		}
	case 70:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:731
		{
			yyVAL.pgType = PgType{Name: AnyName{"varchar"}}
		}
	case 71:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:735
		{
			if yyDollar[3].boolean {
				yyVAL.pgType = PgType{Name: AnyName{"varchar"}}
			} else {
				yyVAL.pgType = PgType{Name: AnyName{"char"}}
			}
		}
	case 72:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:743
		{
			if yyDollar[3].boolean {
				yyVAL.pgType = PgType{Name: AnyName{"varchar"}}
			} else {
				yyVAL.pgType = PgType{Name: AnyName{"char"}}
			}
		}
	case 73:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:751
		{
			if yyDollar[2].boolean {
				yyVAL.pgType = PgType{Name: AnyName{"varchar"}}
			} else {
				yyVAL.pgType = PgType{Name: AnyName{"char"}}
			}
		}
	case 74:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:761
		{
			yyVAL.boolean = true
		}
	case 75:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:765
		{
			yyVAL.boolean = false
		}
	case 76:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:771
		{
			yyVAL.str = yyDollar[3].str
		}
	case 77:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:775
		{
			yyVAL.str = ""
		}
	case 78:
		yyDollar = yyS[yypt-5 : yypt+1]
		//line sql.y:784
		{
			yyVAL.pgType = PgType{Name: AnyName{"timestamp"}, TypeMods: []Expr{yyDollar[3].iconst}, WithTimeZone: yyDollar[5].boolean}
		}
	case 79:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:788
		{
			yyVAL.pgType = PgType{Name: AnyName{"timestamp"}, WithTimeZone: yyDollar[2].boolean}
		}
	case 80:
		yyDollar = yyS[yypt-5 : yypt+1]
		//line sql.y:792
		{
			yyVAL.pgType = PgType{Name: AnyName{"time"}, TypeMods: []Expr{yyDollar[3].iconst}, WithTimeZone: yyDollar[5].boolean}
		}
	case 81:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:796
		{
			yyVAL.pgType = PgType{Name: AnyName{"time"}, WithTimeZone: yyDollar[2].boolean}
		}
	case 83:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:805
		{
			yyVAL.boolean = true
		}
	case 84:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:809
		{
			yyVAL.boolean = false
		}
	case 85:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:813
		{
			yyVAL.boolean = false
		}
	case 86:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:819
		{
			yyVAL.optInterval = &OptInterval{Left: "year"}
		}
	case 87:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:823
		{
			yyVAL.optInterval = &OptInterval{Left: "month"}
		}
	case 88:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:827
		{
			yyVAL.optInterval = &OptInterval{Left: "day"}
		}
	case 89:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:831
		{
			yyVAL.optInterval = &OptInterval{Left: "hour"}
		}
	case 90:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:835
		{
			yyVAL.optInterval = &OptInterval{Left: "minute"}
		}
	case 91:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:839
		{
			yyVAL.optInterval = &OptInterval{Second: yyDollar[1].intervalSecond}
		}
	case 92:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:843
		{
			yyVAL.optInterval = &OptInterval{Left: "year", Right: "month"}
		}
	case 93:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:847
		{
			yyVAL.optInterval = &OptInterval{Left: "day", Right: "hour"}
		}
	case 94:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:851
		{
			yyVAL.optInterval = &OptInterval{Left: "day", Right: "minute"}
		}
	case 95:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:855
		{
			yyVAL.optInterval = &OptInterval{Left: "day", Second: yyDollar[3].intervalSecond}
		}
	case 96:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:859
		{
			yyVAL.optInterval = &OptInterval{Left: "hour", Right: "minute"}
		}
	case 97:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:863
		{
			yyVAL.optInterval = &OptInterval{Left: "hour", Second: yyDollar[3].intervalSecond}
		}
	case 98:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:867
		{
			yyVAL.optInterval = &OptInterval{Left: "minute", Second: yyDollar[3].intervalSecond}
		}
	case 99:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:871
		{
			yyVAL.optInterval = nil
		}
	case 100:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:877
		{
			yyVAL.intervalSecond = &IntervalSecond{}
		}
	case 101:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:881
		{
			yyVAL.intervalSecond = &IntervalSecond{Precision: yyDollar[3].iconst}
		}
	case 103:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:917
		{
			yyVAL.expr = TypecastExpr{Expr: yyDollar[1].expr, Typename: yyDollar[3].pgType}
		}
	case 104:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:921
		{
			yyVAL.expr = CollateExpr{Expr: yyDollar[1].expr, Collation: yyDollar[3].anyName}
		}
	case 105:
		yyDollar = yyS[yypt-5 : yypt+1]
		//line sql.y:925
		{
			yyVAL.expr = AtTimeZoneExpr{Expr: yyDollar[1].expr, TimeZone: yyDollar[5].expr}
		}
	case 106:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:938
		{
			yyVAL.expr = UnaryExpr{Operator: AnyName{"+"}, Expr: yyDollar[2].expr}
		}
	case 107:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:942
		{
			yyVAL.expr = UnaryExpr{Operator: AnyName{"-"}, Expr: yyDollar[2].expr}
		}
	case 108:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:946
		{
			yyVAL.expr = BinaryExpr{Left: yyDollar[1].expr, Operator: AnyName{"+"}, Right: yyDollar[3].expr}
		}
	case 109:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:950
		{
			yyVAL.expr = BinaryExpr{Left: yyDollar[1].expr, Operator: AnyName{"-"}, Right: yyDollar[3].expr}
		}
	case 110:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:954
		{
			yyVAL.expr = BinaryExpr{Left: yyDollar[1].expr, Operator: AnyName{"*"}, Right: yyDollar[3].expr}
		}
	case 111:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:958
		{
			yyVAL.expr = BinaryExpr{Left: yyDollar[1].expr, Operator: AnyName{"/"}, Right: yyDollar[3].expr}
		}
	case 112:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:962
		{
			yyVAL.expr = BinaryExpr{Left: yyDollar[1].expr, Operator: AnyName{"%"}, Right: yyDollar[3].expr}
		}
	case 113:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:966
		{
			yyVAL.expr = BinaryExpr{Left: yyDollar[1].expr, Operator: AnyName{"^"}, Right: yyDollar[3].expr}
		}
	case 114:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:970
		{
			yyVAL.expr = BinaryExpr{Left: yyDollar[1].expr, Operator: AnyName{"<"}, Right: yyDollar[3].expr}
		}
	case 115:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:974
		{
			yyVAL.expr = BinaryExpr{Left: yyDollar[1].expr, Operator: AnyName{">"}, Right: yyDollar[3].expr}
		}
	case 116:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:978
		{
			yyVAL.expr = BinaryExpr{Left: yyDollar[1].expr, Operator: AnyName{"="}, Right: yyDollar[3].expr}
		}
	case 117:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:982
		{
			yyVAL.expr = BinaryExpr{Left: yyDollar[1].expr, Operator: AnyName{"<="}, Right: yyDollar[3].expr}
		}
	case 118:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:986
		{
			yyVAL.expr = BinaryExpr{Left: yyDollar[1].expr, Operator: AnyName{">="}, Right: yyDollar[3].expr}
		}
	case 119:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:990
		{
			yyVAL.expr = BinaryExpr{Left: yyDollar[1].expr, Operator: AnyName{"!="}, Right: yyDollar[3].expr}
		}
	case 120:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:994
		{
			yyVAL.expr = BinaryExpr{Left: yyDollar[1].expr, Operator: yyDollar[2].anyName, Right: yyDollar[3].expr}
		}
	case 121:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:998
		{
			yyVAL.expr = UnaryExpr{Operator: yyDollar[1].anyName, Expr: yyDollar[2].expr}
		}
	case 122:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:1002
		{
			yyVAL.expr = PostfixExpr{Expr: yyDollar[1].expr, Operator: yyDollar[2].anyName}
		}
	case 123:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1006
		{
			yyVAL.expr = BooleanExpr{Left: yyDollar[1].expr, Operator: "and", Right: yyDollar[3].expr}
		}
	case 124:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1010
		{
			yyVAL.expr = BooleanExpr{Left: yyDollar[1].expr, Operator: "or", Right: yyDollar[3].expr}
		}
	case 125:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:1014
		{
			yyVAL.expr = NotExpr{Expr: yyDollar[2].expr}
		}
	case 126:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:1018
		{
			yyVAL.expr = NotExpr{Expr: yyDollar[2].expr}
		}
	case 127:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1022
		{
			yyVAL.expr = TextOpWithEscapeExpr{Left: yyDollar[1].expr, Operator: "like", Right: yyDollar[3].expr}
		}
	case 128:
		yyDollar = yyS[yypt-5 : yypt+1]
		//line sql.y:1026
		{
			yyVAL.expr = TextOpWithEscapeExpr{Left: yyDollar[1].expr, Operator: "like", Right: yyDollar[3].expr, Escape: yyDollar[5].expr}
		}
	case 129:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:1030
		{
			yyVAL.expr = TextOpWithEscapeExpr{Left: yyDollar[1].expr, Operator: "not like", Right: yyDollar[4].expr}
		}
	case 130:
		yyDollar = yyS[yypt-6 : yypt+1]
		//line sql.y:1034
		{
			yyVAL.expr = TextOpWithEscapeExpr{Left: yyDollar[1].expr, Operator: "not like", Right: yyDollar[4].expr, Escape: yyDollar[6].expr}
		}
	case 131:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1038
		{
			yyVAL.expr = TextOpWithEscapeExpr{Left: yyDollar[1].expr, Operator: "ilike", Right: yyDollar[3].expr}
		}
	case 132:
		yyDollar = yyS[yypt-5 : yypt+1]
		//line sql.y:1042
		{
			yyVAL.expr = TextOpWithEscapeExpr{Left: yyDollar[1].expr, Operator: "ilike", Right: yyDollar[3].expr, Escape: yyDollar[5].expr}
		}
	case 133:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:1046
		{
			yyVAL.expr = TextOpWithEscapeExpr{Left: yyDollar[1].expr, Operator: "not ilike", Right: yyDollar[4].expr}
		}
	case 134:
		yyDollar = yyS[yypt-6 : yypt+1]
		//line sql.y:1050
		{
			yyVAL.expr = TextOpWithEscapeExpr{Left: yyDollar[1].expr, Operator: "not ilike", Right: yyDollar[4].expr, Escape: yyDollar[6].expr}
		}
	case 135:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:1055
		{
			yyVAL.expr = TextOpWithEscapeExpr{Left: yyDollar[1].expr, Operator: "similar to", Right: yyDollar[4].expr}
		}
	case 136:
		yyDollar = yyS[yypt-6 : yypt+1]
		//line sql.y:1059
		{
			yyVAL.expr = TextOpWithEscapeExpr{Left: yyDollar[1].expr, Operator: "similar to", Right: yyDollar[4].expr, Escape: yyDollar[6].expr}
		}
	case 137:
		yyDollar = yyS[yypt-5 : yypt+1]
		//line sql.y:1063
		{
			yyVAL.expr = TextOpWithEscapeExpr{Left: yyDollar[1].expr, Operator: "not similar to", Right: yyDollar[5].expr}
		}
	case 138:
		yyDollar = yyS[yypt-7 : yypt+1]
		//line sql.y:1067
		{
			yyVAL.expr = TextOpWithEscapeExpr{Left: yyDollar[1].expr, Operator: "not similar to", Right: yyDollar[5].expr, Escape: yyDollar[7].expr}
		}
	case 139:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1080
		{
			yyVAL.expr = IsExpr{Expr: yyDollar[1].expr, Op: "null"}
		}
	case 140:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:1084
		{
			yyVAL.expr = IsExpr{Expr: yyDollar[1].expr, Op: "null"}
		}
	case 141:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:1088
		{
			yyVAL.expr = IsExpr{Expr: yyDollar[1].expr, Not: true, Op: "null"}
		}
	case 142:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:1092
		{
			yyVAL.expr = IsExpr{Expr: yyDollar[1].expr, Not: true, Op: "null"}
		}
	case 143:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1096
		{
			yyVAL.expr = BinaryExpr{Left: yyDollar[1].row, Operator: AnyName{"overlaps"}, Right: yyDollar[3].row}
		}
	case 144:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1100
		{
			yyVAL.expr = IsExpr{Expr: yyDollar[1].expr, Op: "true"}
		}
	case 145:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:1104
		{
			yyVAL.expr = IsExpr{Expr: yyDollar[1].expr, Not: true, Op: "true"}
		}
	case 146:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1108
		{
			yyVAL.expr = IsExpr{Expr: yyDollar[1].expr, Op: "false"}
		}
	case 147:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:1112
		{
			yyVAL.expr = IsExpr{Expr: yyDollar[1].expr, Not: true, Op: "false"}
		}
	case 148:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1116
		{
			yyVAL.expr = IsExpr{Expr: yyDollar[1].expr, Op: "unknown"}
		}
	case 149:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:1120
		{
			yyVAL.expr = IsExpr{Expr: yyDollar[1].expr, Not: true, Op: "unknown"}
		}
	case 150:
		yyDollar = yyS[yypt-5 : yypt+1]
		//line sql.y:1124
		{
			yyVAL.expr = BinaryExpr{Left: yyDollar[1].expr, Operator: AnyName{"is distinct from"}, Right: yyDollar[5].expr}
		}
	case 151:
		yyDollar = yyS[yypt-6 : yypt+1]
		//line sql.y:1128
		{
			yyVAL.expr = BinaryExpr{Left: yyDollar[1].expr, Operator: AnyName{"is not distinct from"}, Right: yyDollar[6].expr}
		}
	case 152:
		yyDollar = yyS[yypt-6 : yypt+1]
		//line sql.y:1132
		{
			yyVAL.expr = IsOfExpr{Expr: yyDollar[1].expr, Types: yyDollar[5].pgTypes}
		}
	case 153:
		yyDollar = yyS[yypt-7 : yypt+1]
		//line sql.y:1136
		{
			yyVAL.expr = IsOfExpr{Expr: yyDollar[1].expr, Not: true, Types: yyDollar[6].pgTypes}
		}
	case 154:
		yyDollar = yyS[yypt-6 : yypt+1]
		//line sql.y:1140
		{
			yyVAL.expr = BetweenExpr{Expr: yyDollar[1].expr, Left: yyDollar[4].expr, Right: yyDollar[6].expr}
		}
	case 155:
		yyDollar = yyS[yypt-7 : yypt+1]
		//line sql.y:1144
		{
			yyVAL.expr = BetweenExpr{Expr: yyDollar[1].expr, Not: true, Left: yyDollar[5].expr, Right: yyDollar[7].expr}
		}
	case 156:
		yyDollar = yyS[yypt-6 : yypt+1]
		//line sql.y:1148
		{
			yyVAL.expr = BetweenExpr{Expr: yyDollar[1].expr, Symmetric: true, Left: yyDollar[4].expr, Right: yyDollar[6].expr}
		}
	case 157:
		yyDollar = yyS[yypt-7 : yypt+1]
		//line sql.y:1152
		{
			yyVAL.expr = BetweenExpr{Expr: yyDollar[1].expr, Not: true, Symmetric: true, Left: yyDollar[5].expr, Right: yyDollar[7].expr}
		}
	case 158:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1156
		{
			yyVAL.expr = InExpr{Value: yyDollar[1].expr, In: yyDollar[3].expr}
		}
	case 159:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:1160
		{
			yyVAL.expr = InExpr{Value: yyDollar[1].expr, Not: true, In: yyDollar[4].expr}
		}
	case 160:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:1164
		{
			yyVAL.expr = SubqueryOpExpr{Value: yyDollar[1].expr, Op: yyDollar[2].subqueryOp, Type: yyDollar[3].str, Query: yyDollar[4].sqlSelect}
		}
	case 161:
		yyDollar = yyS[yypt-6 : yypt+1]
		//line sql.y:1168
		{
			yyVAL.expr = SubqueryOpExpr{Value: yyDollar[1].expr, Op: yyDollar[2].subqueryOp, Type: yyDollar[3].str, Query: ParenExpr{Expr: yyDollar[5].expr}}
		}
	case 162:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1172
		{
			yyVAL.expr = IsExpr{Expr: yyDollar[1].expr, Op: "document"}
		}
	case 163:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:1176
		{
			yyVAL.expr = IsExpr{Expr: yyDollar[1].expr, Not: true, Op: "document"}
		}
	case 164:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1191
		{
			yyVAL.expr = yyDollar[1].expr
		}
	case 165:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1195
		{
			yyVAL.expr = TypecastExpr{Expr: yyDollar[1].expr, Typename: yyDollar[3].pgType}
		}
	case 166:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:1199
		{
			yyVAL.expr = UnaryExpr{Operator: AnyName{"+"}, Expr: yyDollar[2].expr}
		}
	case 167:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:1203
		{
			yyVAL.expr = UnaryExpr{Operator: AnyName{"-"}, Expr: yyDollar[2].expr}
		}
	case 168:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1207
		{
			yyVAL.expr = BinaryExpr{Left: yyDollar[1].expr, Operator: AnyName{"+"}, Right: yyDollar[3].expr}
		}
	case 169:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1211
		{
			yyVAL.expr = BinaryExpr{Left: yyDollar[1].expr, Operator: AnyName{"-"}, Right: yyDollar[3].expr}
		}
	case 170:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1215
		{
			yyVAL.expr = BinaryExpr{Left: yyDollar[1].expr, Operator: AnyName{"*"}, Right: yyDollar[3].expr}
		}
	case 171:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1219
		{
			yyVAL.expr = BinaryExpr{Left: yyDollar[1].expr, Operator: AnyName{"/"}, Right: yyDollar[3].expr}
		}
	case 172:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1223
		{
			yyVAL.expr = BinaryExpr{Left: yyDollar[1].expr, Operator: AnyName{"%"}, Right: yyDollar[3].expr}
		}
	case 173:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1227
		{
			yyVAL.expr = BinaryExpr{Left: yyDollar[1].expr, Operator: AnyName{"^"}, Right: yyDollar[3].expr}
		}
	case 174:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1231
		{
			yyVAL.expr = BinaryExpr{Left: yyDollar[1].expr, Operator: AnyName{"<"}, Right: yyDollar[3].expr}
		}
	case 175:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1235
		{
			yyVAL.expr = BinaryExpr{Left: yyDollar[1].expr, Operator: AnyName{">"}, Right: yyDollar[3].expr}
		}
	case 176:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1239
		{
			yyVAL.expr = BinaryExpr{Left: yyDollar[1].expr, Operator: AnyName{"="}, Right: yyDollar[3].expr}
		}
	case 177:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1243
		{
			yyVAL.expr = BinaryExpr{Left: yyDollar[1].expr, Operator: AnyName{"<="}, Right: yyDollar[3].expr}
		}
	case 178:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1247
		{
			yyVAL.expr = BinaryExpr{Left: yyDollar[1].expr, Operator: AnyName{">="}, Right: yyDollar[3].expr}
		}
	case 179:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1251
		{
			yyVAL.expr = BinaryExpr{Left: yyDollar[1].expr, Operator: AnyName{"!="}, Right: yyDollar[3].expr}
		}
	case 180:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1255
		{
			yyVAL.expr = BinaryExpr{Left: yyDollar[1].expr, Operator: yyDollar[2].anyName, Right: yyDollar[3].expr}
		}
	case 181:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:1259
		{
			yyVAL.expr = UnaryExpr{Operator: yyDollar[1].anyName, Expr: yyDollar[2].expr}
		}
	case 182:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:1263
		{
			yyVAL.expr = PostfixExpr{Expr: yyDollar[1].expr, Operator: yyDollar[2].anyName}
		}
	case 183:
		yyDollar = yyS[yypt-5 : yypt+1]
		//line sql.y:1267
		{
			yyVAL.expr = BinaryExpr{Left: yyDollar[1].expr, Operator: AnyName{"is distinct from"}, Right: yyDollar[5].expr}
		}
	case 184:
		yyDollar = yyS[yypt-6 : yypt+1]
		//line sql.y:1271
		{
			yyVAL.expr = BinaryExpr{Left: yyDollar[1].expr, Operator: AnyName{"is not distinct from"}, Right: yyDollar[6].expr}
		}
	case 185:
		yyDollar = yyS[yypt-6 : yypt+1]
		//line sql.y:1275
		{
			yyVAL.expr = IsOfExpr{Expr: yyDollar[1].expr, Types: yyDollar[5].pgTypes}
		}
	case 186:
		yyDollar = yyS[yypt-7 : yypt+1]
		//line sql.y:1279
		{
			yyVAL.expr = IsOfExpr{Expr: yyDollar[1].expr, Not: true, Types: yyDollar[6].pgTypes}
		}
	case 187:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1283
		{
			yyVAL.expr = IsExpr{Expr: yyDollar[1].expr, Op: "document"}
		}
	case 188:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:1287
		{
			yyVAL.expr = IsExpr{Expr: yyDollar[1].expr, Not: true, Op: "document"}
		}
	case 189:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1300
		{
			yyVAL.expr = yyDollar[1].columnRef
		}
	case 190:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1301
		{
			yyVAL.expr = yyDollar[1].expr
		}
	case 191:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:1309
		{
			yyVAL.expr = ParenExpr{Expr: yyDollar[2].expr, Indirection: yyDollar[4].indirection}
		}
	case 192:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1312
		{
			yyVAL.expr = yyDollar[1].expr
		}
	case 193:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1313
		{
			yyVAL.expr = yyDollar[1].expr
		}
	case 194:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1315
		{
			yyVAL.expr = yyDollar[1].sqlSelect
		}
	case 195:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:1319
		{
			yyDollar[1].sqlSelect.ParenWrapped = false
			yyVAL.expr = ParenExpr{Expr: yyDollar[1].sqlSelect, Indirection: yyDollar[2].indirection}
		}
	case 196:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:1324
		{
			yyVAL.expr = ExistsExpr(*yyDollar[2].sqlSelect)
		}
	case 197:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:1328
		{
			yyVAL.expr = ArraySubselect(*yyDollar[2].sqlSelect)
		}
	case 198:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:1331
		{
			yyVAL.expr = ArrayConstructorExpr(yyDollar[2].arrayExpr)
		}
	case 199:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1335
		{
			yyVAL.expr = yyDollar[1].row
		}
	case 200:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1339
		{
			yyVAL.expr = yyDollar[1].row
		}
	case 201:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1350
		{
			yyVAL.funcApplication = FuncApplication{Name: yyDollar[1].anyName}
		}
	case 202:
		yyDollar = yyS[yypt-5 : yypt+1]
		//line sql.y:1354
		{
			yyVAL.funcApplication = FuncApplication{Name: yyDollar[1].anyName, Args: yyDollar[3].funcArgs, OrderClause: yyDollar[4].orderClause}
		}
	case 203:
		yyDollar = yyS[yypt-6 : yypt+1]
		//line sql.y:1358
		{
			va := yyDollar[4].funcArg
			yyVAL.funcApplication = FuncApplication{Name: yyDollar[1].anyName, VariadicArg: &va, OrderClause: yyDollar[5].orderClause}
		}
	case 204:
		yyDollar = yyS[yypt-8 : yypt+1]
		//line sql.y:1363
		{
			va := yyDollar[6].funcArg
			yyVAL.funcApplication = FuncApplication{Name: yyDollar[1].anyName, Args: yyDollar[3].funcArgs, VariadicArg: &va, OrderClause: yyDollar[7].orderClause}
		}
	case 205:
		yyDollar = yyS[yypt-6 : yypt+1]
		//line sql.y:1368
		{
			yyVAL.funcApplication = FuncApplication{Name: yyDollar[1].anyName, Args: yyDollar[4].funcArgs, OrderClause: yyDollar[5].orderClause}
		}
	case 206:
		yyDollar = yyS[yypt-6 : yypt+1]
		//line sql.y:1372
		{
			yyVAL.funcApplication = FuncApplication{Name: yyDollar[1].anyName, Distinct: true, Args: yyDollar[4].funcArgs, OrderClause: yyDollar[5].orderClause}
		}
	case 207:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:1376
		{
			yyVAL.funcApplication = FuncApplication{Name: yyDollar[1].anyName, Star: true}
		}
	case 208:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:1392
		{
			yyVAL.expr = &FuncExpr{FuncApplication: yyDollar[1].funcApplication, WithinGroupClause: yyDollar[2].withinGroupClause, FilterClause: yyDollar[3].filterClause, OverClause: yyDollar[4].overClause}
		}
	case 209:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1396
		{
			yyVAL.expr = yyDollar[1].expr
		}
	case 210:
		yyDollar = yyS[yypt-5 : yypt+1]
		//line sql.y:1409
		{
			yyVAL.expr = FuncApplication{Name: AnyName{"collation for"}, Args: []FuncArg{{Expr: yyDollar[4].expr}}}
		}
	case 211:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1413
		{
			yyVAL.expr = FuncExprNoParens("current_date")
		}
	case 212:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1417
		{
			yyVAL.expr = FuncExprNoParens("current_time")
		}
	case 213:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:1421
		{
			yyVAL.expr = FuncApplication{Name: AnyName{"current_time"}, Args: []FuncArg{{Expr: yyDollar[3].iconst}}}
		}
	case 214:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1425
		{
			yyVAL.expr = FuncExprNoParens("current_timestamp")
		}
	case 215:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:1429
		{
			yyVAL.expr = FuncApplication{Name: AnyName{"current_timestamp"}, Args: []FuncArg{{Expr: yyDollar[3].iconst}}}
		}
	case 216:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1433
		{
			yyVAL.expr = FuncExprNoParens("localtime")
		}
	case 217:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:1437
		{
			yyVAL.expr = FuncApplication{Name: AnyName{"localtime"}, Args: []FuncArg{{Expr: yyDollar[3].iconst}}}
		}
	case 218:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1441
		{
			yyVAL.expr = FuncExprNoParens("localtimestamp")
		}
	case 219:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:1445
		{
			yyVAL.expr = FuncApplication{Name: AnyName{"localtimestamp"}, Args: []FuncArg{{Expr: yyDollar[3].iconst}}}
		}
	case 220:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1449
		{
			yyVAL.expr = FuncExprNoParens("current_role")
		}
	case 221:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1453
		{
			yyVAL.expr = FuncExprNoParens("current_user")
		}
	case 222:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1457
		{
			yyVAL.expr = FuncExprNoParens("session_user")
		}
	case 223:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1461
		{
			yyVAL.expr = FuncExprNoParens("user")
		}
	case 224:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1465
		{
			yyVAL.expr = FuncExprNoParens("current_catalog")
		}
	case 225:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1469
		{
			yyVAL.expr = FuncExprNoParens("current_schema")
		}
	case 226:
		yyDollar = yyS[yypt-6 : yypt+1]
		//line sql.y:1473
		{
			yyVAL.expr = CastFunc{Name: "cast", Expr: yyDollar[3].expr, Type: yyDollar[5].pgType}
		}
	case 227:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:1477
		{
			yyVAL.expr = ExtractExpr(*yyDollar[3].extractList)
		}
	case 228:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:1481
		{
			yyVAL.expr = OverlayExpr(yyDollar[3].overlayList)
		}
	case 229:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:1485
		{
			yyVAL.expr = PositionExpr(*yyDollar[3].positionList)
		}
	case 230:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:1489
		{
			if yyDollar[3].placeholder == nil {
				yyVAL.expr = FuncApplication{Name: AnyName{"substring"}}
			} else if se, ok := yyDollar[3].placeholder.(SubstrList); ok {
				yyVAL.expr = SubstrExpr(se)
			} else {
				var args []FuncArg
				for _, a := range yyDollar[3].placeholder.([]Expr) {
					args = append(args, FuncArg{Expr: a})
				}
				yyVAL.expr = FuncApplication{Name: AnyName{"substring"}, Args: args}
			}
		}
	case 231:
		yyDollar = yyS[yypt-6 : yypt+1]
		//line sql.y:1503
		{
			yyVAL.expr = CastFunc{Name: "treat", Expr: yyDollar[3].expr, Type: yyDollar[5].pgType}
		}
	case 232:
		yyDollar = yyS[yypt-5 : yypt+1]
		//line sql.y:1507
		{
			yyVAL.expr = TrimExpr{Direction: "both", TrimList: yyDollar[4].trimList}
		}
	case 233:
		yyDollar = yyS[yypt-5 : yypt+1]
		//line sql.y:1511
		{
			yyVAL.expr = TrimExpr{Direction: "leading", TrimList: yyDollar[4].trimList}
		}
	case 234:
		yyDollar = yyS[yypt-5 : yypt+1]
		//line sql.y:1515
		{
			yyVAL.expr = TrimExpr{Direction: "trailing", TrimList: yyDollar[4].trimList}
		}
	case 235:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:1519
		{
			yyVAL.expr = TrimExpr{TrimList: yyDollar[3].trimList}
		}
	case 236:
		yyDollar = yyS[yypt-6 : yypt+1]
		//line sql.y:1523
		{
			yyVAL.expr = FuncApplication{Name: AnyName{"nullif"}, Args: []FuncArg{{Expr: yyDollar[3].expr}, {Expr: yyDollar[5].expr}}}
		}
	case 237:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:1527
		{
			fa := FuncApplication{Name: AnyName{"coalesce"}}
			for _, e := range yyDollar[3].fields {
				fa.Args = append(fa.Args, FuncArg{Expr: e})
			}
			yyVAL.expr = fa
		}
	case 238:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:1535
		{
			fa := FuncApplication{Name: AnyName{"greatest"}}
			for _, e := range yyDollar[3].fields {
				fa.Args = append(fa.Args, FuncArg{Expr: e})
			}
			yyVAL.expr = fa
		}
	case 239:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:1543
		{
			fa := FuncApplication{Name: AnyName{"least"}}
			for _, e := range yyDollar[3].fields {
				fa.Args = append(fa.Args, FuncArg{Expr: e})
			}
			yyVAL.expr = fa
		}
	case 240:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:1551
		{
			fa := FuncApplication{Name: AnyName{"xmlconcat"}}
			for _, e := range yyDollar[3].fields {
				fa.Args = append(fa.Args, FuncArg{Expr: e})
			}
			yyVAL.expr = fa
		}
	case 241:
		yyDollar = yyS[yypt-5 : yypt+1]
		//line sql.y:1559
		{
			yyVAL.expr = XmlElement{Name: yyDollar[4].str}
		}
	case 242:
		yyDollar = yyS[yypt-7 : yypt+1]
		//line sql.y:1563
		{
			yyVAL.expr = XmlElement{Name: yyDollar[4].str, Attributes: yyDollar[6].xmlAttributes}
		}
	case 243:
		yyDollar = yyS[yypt-7 : yypt+1]
		//line sql.y:1567
		{
			yyVAL.expr = XmlElement{Name: yyDollar[4].str, Body: yyDollar[6].fields}
		}
	case 244:
		yyDollar = yyS[yypt-9 : yypt+1]
		//line sql.y:1571
		{
			yyVAL.expr = XmlElement{Name: yyDollar[4].str, Attributes: yyDollar[6].xmlAttributes, Body: yyDollar[8].fields}
		}
	case 245:
		yyDollar = yyS[yypt-5 : yypt+1]
		//line sql.y:1575
		{
			yyVAL.expr = XmlExists{Path: yyDollar[3].expr, Body: yyDollar[4].xmlExistsArgument}
		}
	case 246:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:1579
		{
			yyVAL.expr = XmlForest(yyDollar[3].xmlAttributeEls)
		}
	case 247:
		yyDollar = yyS[yypt-6 : yypt+1]
		//line sql.y:1583
		{
			yyVAL.expr = XmlParse{Type: yyDollar[3].str, Content: yyDollar[4].expr, WhitespaceOption: yyDollar[5].str}
		}
	case 248:
		yyDollar = yyS[yypt-5 : yypt+1]
		//line sql.y:1587
		{
			yyVAL.expr = XmlPi{Name: yyDollar[4].str}
		}
	case 249:
		yyDollar = yyS[yypt-7 : yypt+1]
		//line sql.y:1591
		{
			yyVAL.expr = XmlPi{Name: yyDollar[4].str, Content: yyDollar[6].expr}
		}
	case 250:
		yyDollar = yyS[yypt-7 : yypt+1]
		//line sql.y:1595
		{
			yyVAL.expr = XmlRoot{Xml: yyDollar[3].expr, Version: yyDollar[5].xmlRootVersion, Standalone: yyDollar[6].str}
		}
	case 251:
		yyDollar = yyS[yypt-7 : yypt+1]
		//line sql.y:1599
		{
			yyVAL.expr = XmlSerialize{XmlType: yyDollar[3].str, Content: yyDollar[4].expr, Type: yyDollar[6].pgType}
		}
	case 252:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:1608
		{
			yyVAL.xmlRootVersion = XmlRootVersion{Expr: yyDollar[2].expr}
		}
	case 253:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1612
		{
			yyVAL.xmlRootVersion = XmlRootVersion{}
		}
	case 254:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1617
		{
			yyVAL.str = "yes"
		}
	case 255:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1618
		{
			yyVAL.str = "no"
		}
	case 256:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:1619
		{
			yyVAL.str = "no value"
		}
	case 257:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:1620
		{
			yyVAL.str = ""
		}
	case 258:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:1624
		{
			yyVAL.xmlAttributes = XmlAttributes(yyDollar[3].xmlAttributeEls)
		}
	case 259:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1630
		{
			yyVAL.xmlAttributeEls = []XmlAttributeEl{yyDollar[1].xmlAttributeEl}
		}
	case 260:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1634
		{
			yyVAL.xmlAttributeEls = append(yyDollar[1].xmlAttributeEls, yyDollar[3].xmlAttributeEl)
		}
	case 261:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1640
		{
			yyVAL.xmlAttributeEl = XmlAttributeEl{Value: yyDollar[1].expr, Name: yyDollar[3].str}
		}
	case 262:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1644
		{
			yyVAL.xmlAttributeEl = XmlAttributeEl{Value: yyDollar[1].expr}
		}
	case 263:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1649
		{
			yyVAL.str = "document"
		}
	case 264:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1650
		{
			yyVAL.str = "content"
		}
	case 265:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:1653
		{
			yyVAL.str = "preserve whitespace"
		}
	case 266:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:1654
		{
			yyVAL.str = "strip whitespace"
		}
	case 267:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:1655
		{
			yyVAL.str = ""
		}
	case 268:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:1660
		{
			yyVAL.xmlExistsArgument = XmlExistsArgument{Arg: yyDollar[2].expr}
		}
	case 269:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:1664
		{
			yyVAL.xmlExistsArgument = XmlExistsArgument{Arg: yyDollar[2].expr, RightByRef: true}
		}
	case 270:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:1668
		{
			yyVAL.xmlExistsArgument = XmlExistsArgument{LeftByRef: true, Arg: yyDollar[4].expr}
		}
	case 271:
		yyDollar = yyS[yypt-6 : yypt+1]
		//line sql.y:1672
		{
			yyVAL.xmlExistsArgument = XmlExistsArgument{LeftByRef: true, Arg: yyDollar[4].expr, RightByRef: true}
		}
	case 272:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1679
		{
			yyVAL.identifiers = []string{yyDollar[1].str}
		}
	case 273:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1683
		{
			yyVAL.identifiers = append(yyDollar[1].identifiers, yyDollar[3].str)
		}
	case 274:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1689
		{
			yyVAL.expr = JoinExpr{Left: yyDollar[1].expr, Join: ",", Right: yyDollar[3].expr}
		}
	case 275:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:1693
		{
			yyVAL.expr = JoinExpr{Left: yyDollar[1].expr, Join: "cross join", Right: yyDollar[4].expr}
		}
	case 276:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:1697
		{
			yyVAL.expr = JoinExpr{Left: yyDollar[1].expr, Join: "natural join", Right: yyDollar[4].expr}
		}
	case 277:
		yyDollar = yyS[yypt-7 : yypt+1]
		//line sql.y:1701
		{
			yyVAL.expr = JoinExpr{Left: yyDollar[1].expr, Join: "join", Right: yyDollar[3].expr, Using: yyDollar[6].identifiers}
		}
	case 278:
		yyDollar = yyS[yypt-5 : yypt+1]
		//line sql.y:1705
		{
			yyVAL.expr = JoinExpr{Left: yyDollar[1].expr, Join: "join", Right: yyDollar[3].expr, On: yyDollar[5].expr}
		}
	case 279:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:1711
		{
			yyVAL.fromClause = &FromClause{Expr: yyDollar[2].expr}
		}
	case 280:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:1715
		{
			yyVAL.fromClause = &FromClause{Expr: yyDollar[2].expr}
		}
	case 281:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:1718
		{
			yyVAL.fromClause = nil
		}
	case 282:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1722
		{
			yyVAL.str = "nowait"
		}
	case 283:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:1723
		{
			yyVAL.str = "skip locked"
		}
	case 284:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:1724
		{
			yyVAL.str = ""
		}
	case 285:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1728
		{
			yyVAL.subqueryOp = SubqueryOp{Name: AnyName{yyDollar[1].str}}
		}
	case 286:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:1732
		{
			yyVAL.subqueryOp = SubqueryOp{Operator: true, Name: yyDollar[3].anyName}
		}
	case 287:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1736
		{
			yyVAL.subqueryOp = SubqueryOp{Name: AnyName{"like"}}
		}
	case 288:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:1740
		{
			yyVAL.subqueryOp = SubqueryOp{Name: AnyName{"not like"}}
		}
	case 289:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1744
		{
			yyVAL.subqueryOp = SubqueryOp{Name: AnyName{"ilike"}}
		}
	case 290:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:1748
		{
			yyVAL.subqueryOp = SubqueryOp{Name: AnyName{"not ilike"}}
		}
	case 291:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1762
		{
			yyVAL.fields = []Expr{yyDollar[1].expr}
		}
	case 292:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1766
		{
			yyVAL.fields = append(yyDollar[1].fields, yyDollar[3].expr)
		}
	case 293:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1773
		{
			yyVAL.funcArgs = []FuncArg{yyDollar[1].funcArg}
		}
	case 294:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1777
		{
			yyVAL.funcArgs = append(yyDollar[1].funcArgs, yyDollar[3].funcArg)
		}
	case 295:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1783
		{
			yyVAL.funcArg = FuncArg{Expr: yyDollar[1].expr}
		}
	case 296:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1787
		{
			yyVAL.funcArg = FuncArg{Name: yyDollar[1].str, NameOp: ":=", Expr: yyDollar[3].expr}
		}
	case 297:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1791
		{
			yyVAL.funcArg = FuncArg{Name: yyDollar[1].str, NameOp: "=>", Expr: yyDollar[3].expr}
		}
	case 298:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1797
		{
			yyVAL.pgTypes = []PgType{yyDollar[1].pgType}
		}
	case 299:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1801
		{
			yyVAL.pgTypes = append(yyDollar[1].pgTypes, yyDollar[3].pgType)
		}
	case 300:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1807
		{
			yyVAL.arrayExpr = ArrayExpr(yyDollar[2].fields)
		}
	case 301:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1811
		{
			yyVAL.arrayExpr = yyDollar[2].arrayExpr
		}
	case 302:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:1815
		{
			yyVAL.arrayExpr = ArrayExpr{}
		}
	case 303:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1821
		{
			yyVAL.arrayExpr = ArrayExpr{yyDollar[1].arrayExpr}
		}
	case 304:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1825
		{
			yyVAL.arrayExpr = append(yyDollar[1].arrayExpr, yyDollar[3].arrayExpr)
		}
	case 305:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1831
		{
			yyVAL.extractList = &ExtractList{Extract: yyDollar[1].expr, Time: yyDollar[3].expr}
		}
	case 306:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:1835
		{
			yyVAL.extractList = nil
		}
	case 307:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1843
		{
			yyVAL.expr = AnyName{yyDollar[1].str}
		}
	case 308:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1844
		{
			yyVAL.expr = AnyName{"year"}
		}
	case 309:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1845
		{
			yyVAL.expr = AnyName{"month"}
		}
	case 310:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1846
		{
			yyVAL.expr = AnyName{"day"}
		}
	case 311:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1847
		{
			yyVAL.expr = AnyName{"hour"}
		}
	case 312:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1848
		{
			yyVAL.expr = AnyName{"minute"}
		}
	case 313:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1849
		{
			yyVAL.expr = AnyName{"second"}
		}
	case 314:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1850
		{
			yyVAL.expr = yyDollar[1].expr
		}
	case 315:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:1860
		{
			yyVAL.overlayList = OverlayList{Dest: yyDollar[1].expr, Placing: yyDollar[2].expr, From: yyDollar[3].expr, For: yyDollar[4].expr}
		}
	case 316:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1864
		{
			yyVAL.overlayList = OverlayList{Dest: yyDollar[1].expr, Placing: yyDollar[2].expr, From: yyDollar[3].expr}
		}
	case 317:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:1870
		{
			yyVAL.expr = yyDollar[2].expr
		}
	case 318:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1878
		{
			yyVAL.positionList = &PositionList{Substring: yyDollar[1].expr, String: yyDollar[3].expr}
		}
	case 319:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:1881
		{
			yyVAL.positionList = nil
		}
	case 320:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1897
		{
			yyVAL.placeholder = SubstrList{Source: yyDollar[1].expr, From: yyDollar[2].expr, For: yyDollar[3].expr}
		}
	case 321:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1901
		{
			/* not legal per SQL99, but might as well allow it */
			yyVAL.placeholder = SubstrList{Source: yyDollar[1].expr, From: yyDollar[3].expr, For: yyDollar[2].expr}
		}
	case 322:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:1905
		{
			yyVAL.placeholder = SubstrList{Source: yyDollar[1].expr, From: yyDollar[2].expr}
		}
	case 323:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:1909
		{
			yyVAL.placeholder = SubstrList{Source: yyDollar[1].expr, For: yyDollar[2].expr}
		}
	case 324:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1913
		{
			yyVAL.placeholder = yyDollar[1].fields
		}
	case 325:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:1917
		{
			yyVAL.placeholder = nil
		}
	case 326:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:1923
		{
			yyVAL.expr = yyDollar[2].expr
		}
	case 327:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:1929
		{
			yyVAL.expr = yyDollar[2].expr
		}
	case 328:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1935
		{
			yyVAL.trimList = TrimList{Left: yyDollar[1].expr, From: true, Right: yyDollar[3].fields}
		}
	case 329:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:1939
		{
			yyVAL.trimList = TrimList{From: true, Right: yyDollar[2].fields}
		}
	case 330:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:1943
		{
			yyVAL.trimList = TrimList{Right: yyDollar[1].fields}
		}
	case 333:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:1999
		{
			yyDollar[2].sqlSelect.ParenWrapped = true
			yyVAL.sqlSelect = yyDollar[2].sqlSelect
		}
	case 334:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:2003
		{
			yyVAL.sqlSelect = yyDollar[2].sqlSelect
		}
	case 335:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2007
		{
			ss := &SelectStmt{}
			ss.SimpleSelect = *yyDollar[1].simpleSelect
			yyVAL.sqlSelect = ss
		}
	case 336:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2013
		{
			yyDollar[1].sqlSelect.OrderClause = yyDollar[2].orderClause
			yyVAL.sqlSelect = yyDollar[1].sqlSelect
		}
	case 337:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:2018
		{
			yyDollar[1].sqlSelect.OrderClause = yyDollar[2].orderClause
			yyDollar[1].sqlSelect.LockingClause = yyDollar[3].lockingClause
			yyDollar[1].sqlSelect.LimitClause = yyDollar[4].limitClause
			yyVAL.sqlSelect = yyDollar[1].sqlSelect
		}
	case 338:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:2025
		{
			yyDollar[1].sqlSelect.OrderClause = yyDollar[2].orderClause
			yyDollar[1].sqlSelect.LimitClause = yyDollar[3].limitClause
			yyDollar[1].sqlSelect.LockingClause = yyDollar[4].lockingClause
			yyVAL.sqlSelect = yyDollar[1].sqlSelect
		}
	case 339:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2034
		{
			ss := &SelectStmt{}
			ss.SimpleSelect = *yyDollar[1].simpleSelect
			yyVAL.sqlSelect = ss
		}
	case 341:
		yyDollar = yyS[yypt-9 : yypt+1]
		//line sql.y:2068
		{
			ss := &SimpleSelect{}
			ss.TargetList = yyDollar[3].fields
			ss.IntoClause = yyDollar[4].intoClause
			ss.FromClause = yyDollar[5].fromClause
			ss.WhereClause = yyDollar[6].whereClause
			ss.GroupByClause = yyDollar[7].groupByClause
			ss.HavingClause = yyDollar[8].expr
			ss.WindowClause = yyDollar[9].windowDefinitions
			yyVAL.simpleSelect = ss
		}
	case 342:
		yyDollar = yyS[yypt-9 : yypt+1]
		//line sql.y:2082
		{
			ss := &SimpleSelect{}
			ss.DistinctList = yyDollar[2].fields
			ss.TargetList = yyDollar[3].fields
			ss.IntoClause = yyDollar[4].intoClause
			ss.FromClause = yyDollar[5].fromClause
			ss.WhereClause = yyDollar[6].whereClause
			ss.GroupByClause = yyDollar[7].groupByClause
			ss.HavingClause = yyDollar[8].expr
			ss.WindowClause = yyDollar[9].windowDefinitions
			yyVAL.simpleSelect = ss
		}
	case 343:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2095
		{
			ss := &SimpleSelect{}
			ss.ValuesClause = yyDollar[1].valuesClause
			yyVAL.simpleSelect = ss
		}
	case 344:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2101
		{
			ss := &SimpleSelect{}
			ss.Table = yyDollar[2].relationExpr
			yyVAL.simpleSelect = ss
		}
	case 345:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:2107
		{
			ss := &SimpleSelect{}
			ss.LeftSelect = yyDollar[1].sqlSelect
			ss.SetOp = "union"
			ss.SetAll = yyDollar[3].boolean
			ss.RightSelect = yyDollar[4].sqlSelect
			yyVAL.simpleSelect = ss
		}
	case 346:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:2116
		{
			ss := &SimpleSelect{}
			ss.LeftSelect = yyDollar[1].sqlSelect
			ss.SetOp = "intersect"
			ss.SetAll = yyDollar[3].boolean
			ss.RightSelect = yyDollar[4].sqlSelect
			yyVAL.simpleSelect = ss
		}
	case 347:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:2125
		{
			ss := &SimpleSelect{}
			ss.LeftSelect = yyDollar[1].sqlSelect
			ss.SetOp = "except"
			ss.SetAll = yyDollar[3].boolean
			ss.RightSelect = yyDollar[4].sqlSelect
			yyVAL.simpleSelect = ss
		}
	case 348:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2137
		{
			yyVAL.intoClause = yyDollar[2].intoClause
		}
	case 349:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:2141
		{
			yyVAL.intoClause = nil
		}
	case 350:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:2151
		{
			yyVAL.intoClause = &IntoClause{Options: "temporary", OptTable: yyDollar[2].boolean, Target: yyDollar[3].anyName}
		}
	case 351:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:2155
		{
			yyVAL.intoClause = &IntoClause{Options: "temp", OptTable: yyDollar[2].boolean, Target: yyDollar[3].anyName}
		}
	case 352:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:2159
		{
			yyVAL.intoClause = &IntoClause{Options: "local temporary", OptTable: yyDollar[3].boolean, Target: yyDollar[4].anyName}
		}
	case 353:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:2163
		{
			yyVAL.intoClause = &IntoClause{Options: "local temp", OptTable: yyDollar[3].boolean, Target: yyDollar[4].anyName}
		}
	case 354:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:2167
		{
			yyVAL.intoClause = &IntoClause{Options: "global temporary", OptTable: yyDollar[3].boolean, Target: yyDollar[4].anyName}
		}
	case 355:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:2171
		{
			yyVAL.intoClause = &IntoClause{Options: "global temp", OptTable: yyDollar[3].boolean, Target: yyDollar[4].anyName}
		}
	case 356:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:2175
		{
			yyVAL.intoClause = &IntoClause{Options: "unlogged", OptTable: yyDollar[2].boolean, Target: yyDollar[3].anyName}
		}
	case 357:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2179
		{
			yyVAL.intoClause = &IntoClause{OptTable: true, Target: yyDollar[2].anyName}
		}
	case 358:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2183
		{
			yyVAL.intoClause = &IntoClause{Target: yyDollar[1].anyName}
		}
	case 359:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2188
		{
			yyVAL.boolean = true
		}
	case 360:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:2189
		{
			yyVAL.boolean = false
		}
	case 361:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2192
		{
			yyVAL.boolean = true
		}
	case 362:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2193
		{
			yyVAL.boolean = false
		}
	case 363:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:2194
		{
			yyVAL.boolean = false
		}
	case 364:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2199
		{
			yyVAL.fields = make([]Expr, 0)
		}
	case 365:
		yyDollar = yyS[yypt-5 : yypt+1]
		//line sql.y:2200
		{
			yyVAL.fields = yyDollar[4].fields
		}
	case 366:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2203
		{
			yyVAL.placeholder = nil
		}
	case 367:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:2204
		{
			yyVAL.placeholder = nil
		}
	case 368:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2207
		{
			yyVAL.orderClause = yyDollar[1].orderClause
		}
	case 369:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:2208
		{
			yyVAL.orderClause = nil
		}
	case 370:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:2211
		{
			yyVAL.orderClause = yyDollar[3].orderClause
		}
	case 371:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2215
		{
			yyVAL.orderClause = &OrderClause{Exprs: []OrderExpr{yyDollar[1].orderExpr}}
		}
	case 372:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:2219
		{
			yyDollar[1].orderClause.Exprs = append(yyDollar[1].orderClause.Exprs, yyDollar[3].orderExpr)
			yyVAL.orderClause = yyDollar[1].orderClause
		}
	case 373:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:2227
		{
			yyVAL.orderExpr = OrderExpr{Expr: yyDollar[1].expr, Using: yyDollar[3].anyName, Nulls: yyDollar[4].str}
		}
	case 374:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:2231
		{
			yyVAL.orderExpr = OrderExpr{Expr: yyDollar[1].expr, Order: yyDollar[2].str, Nulls: yyDollar[3].str}
		}
	case 375:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:2256
		{
			yyVAL.groupByClause = &GroupByClause{Exprs: yyDollar[3].fields}
		}
	case 376:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:2257
		{
			yyVAL.groupByClause = nil
		}
	case 377:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2261
		{
			yyVAL.fields = []Expr{yyDollar[1].expr}
		}
	case 378:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:2265
		{
			yyVAL.fields = append(yyDollar[1].fields, yyDollar[3].expr)
		}
	case 380:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2278
		{
			yyVAL.expr = yyDollar[2].expr
		}
	case 381:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:2279
		{
			yyVAL.expr = nil
		}
	case 382:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2282
		{
			yyVAL.lockingClause = yyDollar[1].lockingClause
		}
	case 383:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:2283
		{
			yyVAL.lockingClause = nil
		}
	case 384:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2286
		{
			yyVAL.lockingClause = yyDollar[1].lockingClause
		}
	case 385:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:2287
		{
			yyVAL.lockingClause = nil
		}
	case 386:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2291
		{
			yyVAL.lockingClause = &LockingClause{Locks: []LockingItem{yyDollar[1].lockingItem}}
		}
	case 387:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2295
		{
			yyDollar[1].lockingClause.Locks = append(yyDollar[1].lockingClause.Locks, yyDollar[2].lockingItem)
			yyVAL.lockingClause = yyDollar[1].lockingClause
		}
	case 388:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:2302
		{
			yyVAL.lockingItem = LockingItem{Strength: yyDollar[1].str, LockedRels: yyDollar[2].anyNames, WaitPolicy: yyDollar[3].str}
		}
	case 389:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2307
		{
			yyVAL.str = "update"
		}
	case 390:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:2308
		{
			yyVAL.str = "no key update"
		}
	case 391:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2309
		{
			yyVAL.str = "share"
		}
	case 392:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:2310
		{
			yyVAL.str = "key share"
		}
	case 393:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2313
		{
			yyVAL.anyNames = yyDollar[2].anyNames
		}
	case 394:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:2314
		{
			yyVAL.anyNames = nil
		}
	case 395:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2322
		{
			yyVAL.windowDefinitions = yyDollar[2].windowDefinitions
		}
	case 396:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:2325
		{
			yyVAL.windowDefinitions = nil
		}
	case 397:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2329
		{
			yyVAL.windowDefinitions = []WindowDefinition{yyDollar[1].windowDefinition}
		}
	case 398:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:2333
		{
			yyVAL.windowDefinitions = append(yyDollar[1].windowDefinitions, yyDollar[3].windowDefinition)
		}
	case 399:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:2339
		{
			yyVAL.windowDefinition = WindowDefinition{Name: yyDollar[1].str, Specification: yyDollar[3].windowSpecification}
		}
	case 400:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2345
		{
			spec := yyDollar[2].windowSpecification
			yyVAL.overClause = &OverClause{Specification: &spec}
		}
	case 401:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2350
		{
			yyVAL.overClause = &OverClause{Name: yyDollar[2].str}
		}
	case 402:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:2353
		{
			yyVAL.overClause = nil
		}
	case 403:
		yyDollar = yyS[yypt-6 : yypt+1]
		//line sql.y:2357
		{
			yyVAL.windowSpecification = WindowSpecification{ExistingName: yyDollar[2].str, PartitionClause: yyDollar[3].partitionClause, OrderClause: yyDollar[4].orderClause, FrameClause: yyDollar[5].frameClause}
		}
	case 404:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2372
		{
			yyVAL.str = yyDollar[1].str
		}
	case 405:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:2373
		{
			yyVAL.str = ""
		}
	case 406:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:2376
		{
			yyVAL.partitionClause = PartitionClause(yyDollar[3].fields)
		}
	case 407:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:2377
		{
			yyVAL.partitionClause = nil
		}
	case 408:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2388
		{
			yyDollar[2].frameClause.Mode = "range"
			yyVAL.frameClause = yyDollar[2].frameClause
		}
	case 409:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2393
		{
			yyDollar[2].frameClause.Mode = "rows"
			yyVAL.frameClause = yyDollar[2].frameClause
		}
	case 410:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:2398
		{
			yyVAL.frameClause = nil
		}
	case 411:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2404
		{
			yyVAL.frameClause = &FrameClause{Start: yyDollar[1].frameBound}
		}
	case 412:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:2408
		{
			yyVAL.frameClause = &FrameClause{Start: yyDollar[2].frameBound, End: yyDollar[4].frameBound}
		}
	case 413:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2419
		{
			yyVAL.frameBound = &FrameBound{Direction: "preceding"}
		}
	case 414:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2423
		{
			yyVAL.frameBound = &FrameBound{Direction: "following"}
		}
	case 415:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2427
		{
			yyVAL.frameBound = &FrameBound{CurrentRow: true}
		}
	case 416:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2431
		{
			yyVAL.frameBound = &FrameBound{BoundExpr: yyDollar[1].expr, Direction: "preceding"}
		}
	case 417:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2435
		{
			yyVAL.frameBound = &FrameBound{BoundExpr: yyDollar[1].expr, Direction: "following"}
		}
	case 418:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2443
		{
			yyVAL.relationExpr = &RelationExpr{Name: yyDollar[1].anyName}
		}
	case 419:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2447
		{
			yyVAL.relationExpr = &RelationExpr{Name: yyDollar[1].anyName, Star: true}
		}
	case 420:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2451
		{
			yyVAL.relationExpr = &RelationExpr{Name: yyDollar[2].anyName, Only: true}
		}
	case 421:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:2455
		{
			yyVAL.relationExpr = &RelationExpr{Name: yyDollar[3].anyName, Only: true}
		}
	case 422:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2463
		{
			yyVAL.limitClause = &LimitClause{Limit: yyDollar[1].expr, Offset: yyDollar[2].expr}
		}
	case 423:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2467
		{
			yyVAL.limitClause = &LimitClause{Limit: yyDollar[2].expr, Offset: yyDollar[1].expr}
		}
	case 424:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2471
		{
			yyVAL.limitClause = &LimitClause{Limit: yyDollar[1].expr}
		}
	case 425:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2475
		{
			yyVAL.limitClause = &LimitClause{Offset: yyDollar[1].expr}
		}
	case 427:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:2482
		{
			yyVAL.limitClause = nil
		}
	case 428:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2486
		{
			yyVAL.expr = yyDollar[2].expr
		}
	case 429:
		yyDollar = yyS[yypt-5 : yypt+1]
		//line sql.y:2491
		{
			yyVAL.expr = yyDollar[3].expr
		}
	case 430:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2497
		{
			yyVAL.expr = yyDollar[2].expr
		}
	case 431:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:2502
		{
			yyVAL.expr = yyDollar[2].expr
		}
	case 432:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2507
		{
			yyVAL.expr = yyDollar[1].expr
		}
	case 433:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2509
		{
			yyVAL.expr = nil
		}
	case 434:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2514
		{
			yyVAL.expr = yyDollar[1].expr
		}
	case 435:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2523
		{
			yyVAL.expr = yyDollar[1].expr
		}
	case 436:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:2524
		{
			yyVAL.expr = yyDollar[2].expr
		}
	case 437:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:2525
		{
			yyVAL.expr = IntegerConst("1")
		}
	case 438:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2532
		{
			yyVAL.expr = yyDollar[1].expr
		}
	case 439:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2536
		{
			yyVAL.placeholder = 0
		}
	case 440:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2537
		{
			yyVAL.placeholder = 0
		}
	case 441:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2540
		{
			yyVAL.placeholder = 0
		}
	case 442:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2541
		{
			yyVAL.placeholder = 0
		}
	case 443:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2545
		{
			yyVAL.valuesClause = ValuesClause{yyDollar[2].valuesRow}
		}
	case 444:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:2549
		{
			yyVAL.valuesClause = append(yyDollar[1].valuesClause, yyDollar[3].valuesRow)
		}
	case 445:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2566
		{
			yyVAL.whereClause = &WhereClause{Expr: yyDollar[2].expr}
		}
	case 446:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:2567
		{
			yyVAL.whereClause = nil
		}
	case 447:
		yyDollar = yyS[yypt-5 : yypt+1]
		//line sql.y:2577
		{
			yyVAL.withinGroupClause = (*WithinGroupClause)(yyDollar[4].orderClause)
		}
	case 448:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:2580
		{
			yyVAL.withinGroupClause = nil
		}
	case 449:
		yyDollar = yyS[yypt-5 : yypt+1]
		//line sql.y:2584
		{
			yyVAL.filterClause = &FilterClause{Expr: yyDollar[4].expr}
		}
	case 450:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:2587
		{
			yyVAL.filterClause = nil
		}
	case 451:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:2599
		{
			yyVAL.row = Row{RowWord: true, Exprs: yyDollar[3].fields}
		}
	case 452:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:2603
		{
			yyVAL.row = Row{RowWord: true, Exprs: nil}
		}
	case 453:
		yyDollar = yyS[yypt-5 : yypt+1]
		//line sql.y:2607
		{
			yyVAL.row = Row{Exprs: append(yyDollar[2].fields, yyDollar[4].expr)}
		}
	case 454:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:2613
		{
			yyVAL.row = Row{RowWord: true, Exprs: yyDollar[3].fields}
		}
	case 455:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:2617
		{
			yyVAL.row = Row{RowWord: true, Exprs: nil}
		}
	case 456:
		yyDollar = yyS[yypt-5 : yypt+1]
		//line sql.y:2623
		{
			yyVAL.row = Row{Exprs: append(yyDollar[2].fields, yyDollar[4].expr)}
		}
	case 457:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2628
		{
			yyVAL.str = "any"
		}
	case 458:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2629
		{
			yyVAL.str = "some"
		}
	case 459:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2630
		{
			yyVAL.str = "all"
		}
	case 460:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2633
		{
			yyVAL.str = string(yyDollar[1].str)
		}
	case 461:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2634
		{
			yyVAL.str = string(yyDollar[1].str)
		}
	case 462:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2637
		{
			yyVAL.str = "+"
		}
	case 463:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2638
		{
			yyVAL.str = "-"
		}
	case 464:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2639
		{
			yyVAL.str = "*"
		}
	case 465:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2640
		{
			yyVAL.str = "/"
		}
	case 466:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2641
		{
			yyVAL.str = "%"
		}
	case 467:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2642
		{
			yyVAL.str = "^"
		}
	case 468:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2643
		{
			yyVAL.str = "<"
		}
	case 469:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2644
		{
			yyVAL.str = ">"
		}
	case 470:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2645
		{
			yyVAL.str = "="
		}
	case 471:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2646
		{
			yyVAL.str = "<="
		}
	case 472:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2647
		{
			yyVAL.str = ">="
		}
	case 473:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2648
		{
			yyVAL.str = "<>"
		}
	case 474:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2651
		{
			yyVAL.anyName = AnyName{yyDollar[1].str}
		}
	case 475:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:2652
		{
			yyVAL.anyName = yyDollar[3].anyName
		}
	case 476:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2655
		{
			yyVAL.anyName = AnyName{yyDollar[1].str}
		}
	case 477:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:2656
		{
			yyVAL.anyName = yyDollar[3].anyName
		}
	case 478:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2660
		{
			yyVAL.expr = yyDollar[1].sqlSelect
		}
	case 479:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:2664
		{
			yyVAL.expr = ValuesRow(yyDollar[2].fields)
		}
	case 480:
		yyDollar = yyS[yypt-5 : yypt+1]
		//line sql.y:2677
		{
			yyVAL.expr = CaseExpr{CaseArg: yyDollar[2].expr, WhenClauses: yyDollar[3].whenClauses, Default: yyDollar[4].expr}
		}
	case 481:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2684
		{
			yyVAL.whenClauses = []WhenClause{yyDollar[1].whenClause}
		}
	case 482:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2688
		{
			yyVAL.whenClauses = append(yyDollar[1].whenClauses, yyDollar[2].whenClause)
		}
	case 483:
		yyDollar = yyS[yypt-4 : yypt+1]
		//line sql.y:2694
		{
			yyVAL.whenClause = WhenClause{When: yyDollar[2].expr, Then: yyDollar[4].expr}
		}
	case 484:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2699
		{
			yyVAL.expr = yyDollar[2].expr
		}
	case 485:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:2700
		{
			yyVAL.expr = nil
		}
	case 486:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2703
		{
			yyVAL.expr = yyDollar[1].expr
		}
	case 487:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:2704
		{
			yyVAL.expr = nil
		}
	case 488:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2708
		{
			yyVAL.columnRef = ColumnRef{Name: yyDollar[1].str}
		}
	case 489:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2712
		{
			yyVAL.columnRef = ColumnRef{Name: yyDollar[1].str, Indirection: yyDollar[2].indirection}
		}
	case 490:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2720
		{
			yyVAL.indirectionEl = IndirectionEl{Name: yyDollar[2].str}
		}
	case 491:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2724
		{
			yyVAL.indirectionEl = IndirectionEl{Name: "*"}
		}
	case 492:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:2728
		{
			yyVAL.indirectionEl = IndirectionEl{LowerSubscript: yyDollar[2].expr}
		}
	case 493:
		yyDollar = yyS[yypt-5 : yypt+1]
		//line sql.y:2732
		{
			yyVAL.indirectionEl = IndirectionEl{LowerSubscript: yyDollar[2].expr, UpperSubscript: yyDollar[4].expr}
		}
	case 494:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2737
		{
			yyVAL.indirection = Indirection{yyDollar[1].indirectionEl}
		}
	case 495:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2738
		{
			yyVAL.indirection = append(yyDollar[1].indirection, yyDollar[2].indirectionEl)
		}
	case 496:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:2741
		{
			yyVAL.indirection = nil
		}
	case 497:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2743
		{
			if yyDollar[1].indirection != nil {
				yyVAL.indirection = append(yyDollar[1].indirection, yyDollar[2].indirectionEl)
			} else {
				yyVAL.indirection = Indirection{yyDollar[2].indirectionEl}
			}
		}
	case 498:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2753
		{
			yyVAL.placeholder = nil
		}
	case 499:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:2757
		{
			yyVAL.placeholder = nil
		}
	case 500:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2769
		{
			yyVAL.expr = yyDollar[1].expr
		}
	case 501:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2770
		{
			yyVAL.expr = DefaultExpr(true)
		}
	case 502:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2774
		{
			yyVAL.valuesRow = ValuesRow{yyDollar[1].expr}
		}
	case 503:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:2778
		{
			yyVAL.valuesRow = append(yyDollar[1].valuesRow, yyDollar[3].expr)
		}
	case 504:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:2789
		{
			yyVAL.valuesRow = yyDollar[2].valuesRow
		}
	case 505:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2800
		{
			yyVAL.fields = yyDollar[1].fields
		}
	case 506:
		yyDollar = yyS[yypt-0 : yypt+1]
		//line sql.y:2801
		{
			yyVAL.fields = nil
		}
	case 507:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2804
		{
			yyVAL.fields = []Expr{yyDollar[1].expr}
		}
	case 508:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:2806
		{
			yyVAL.fields = append(yyDollar[1].fields, yyDollar[3].expr)
		}
	case 509:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:2812
		{
			yyVAL.expr = AliasedExpr{Expr: yyDollar[1].expr, Alias: yyDollar[3].str}
		}
	case 510:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2816
		{
			yyVAL.expr = AliasedExpr{Expr: yyDollar[1].expr, Alias: yyDollar[2].str}
		}
	case 512:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2821
		{
			yyVAL.expr = ColumnRef{Name: "*"}
		}
	case 513:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2835
		{
			yyVAL.anyNames = []AnyName{yyDollar[1].anyName}
		}
	case 514:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:2839
		{
			yyVAL.anyNames = append(yyDollar[1].anyNames, yyDollar[3].anyName)
		}
	case 515:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2852
		{
			yyVAL.anyName = AnyName{yyDollar[1].str}
		}
	case 516:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2856
		{
			yyVAL.anyName = AnyName{yyDollar[1].str}
			for _, s := range yyDollar[2].indirection {
				yyVAL.anyName = append(yyVAL.anyName, s.Name)
			}
		}
	case 517:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2864
		{
			yyVAL.str = yyDollar[1].str
		}
	case 518:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2877
		{
			yyVAL.anyName = AnyName{yyDollar[1].str}
		}
	case 519:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2881
		{
			yyVAL.anyName = AnyName{yyDollar[1].str}
			for _, s := range yyDollar[2].indirection {
				yyVAL.anyName = append(yyVAL.anyName, s.Name)
			}
		}
	case 520:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2894
		{
			yyVAL.expr = yyDollar[1].iconst
		}
	case 521:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2898
		{
			yyVAL.expr = FloatConst(yyDollar[1].str)
		}
	case 522:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2902
		{
			yyVAL.expr = yyDollar[1].expr
		}
	case 523:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2906
		{
			yyVAL.expr = BitConst(yyDollar[1].str)
		}
	case 524:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2910
		{
			yyVAL.expr = BitConst(yyDollar[1].str)
		}
	case 525:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2914
		{
			yyVAL.expr = ConstTypeExpr{Typename: PgType{Name: yyDollar[1].anyName}, Expr: yyDollar[2].expr}
		}
	case 526:
		yyDollar = yyS[yypt-6 : yypt+1]
		//line sql.y:2918
		{
			pgType := PgType{Name: yyDollar[1].anyName}

			/*
			 * We must use func_arg_list and opt_sort_clause in the
			 * production to avoid reduce/reduce conflicts, but we
			 * don't actually wish to allow NamedArgExpr in this
			 * context, nor ORDER BY.
			 */

			for _, arg := range yyDollar[3].funcArgs {
				if arg.Name != "" {
					yylex.Error("type modifier cannot have parameter name")
				}

				pgType.TypeMods = append(pgType.TypeMods, Expr(arg))
			}

			if yyDollar[4].orderClause != nil {
				yylex.Error("type modifier cannot have ORDER BY")
			}

			yyVAL.expr = ConstTypeExpr{Typename: pgType, Expr: yyDollar[6].expr}
		}
	case 527:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2942
		{
			yyVAL.expr = ConstTypeExpr{Typename: yyDollar[1].pgType, Expr: yyDollar[2].expr}
		}
	case 528:
		yyDollar = yyS[yypt-3 : yypt+1]
		//line sql.y:2946
		{
			yyVAL.expr = ConstIntervalExpr{Value: yyDollar[2].expr, OptInterval: yyDollar[3].optInterval}
		}
	case 529:
		yyDollar = yyS[yypt-5 : yypt+1]
		//line sql.y:2950
		{
			yyVAL.expr = ConstIntervalExpr{Precision: yyDollar[3].iconst, Value: yyDollar[5].expr}
		}
	case 530:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2954
		{
			yyVAL.expr = BoolConst(true)
		}
	case 531:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2958
		{
			yyVAL.expr = BoolConst(false)
		}
	case 532:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2962
		{
			yyVAL.expr = NullConst{}
		}
	case 533:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2966
		{
			yyVAL.iconst = IntegerConst(yyDollar[1].str)
		}
	case 534:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2967
		{
			yyVAL.expr = StringConst(yyDollar[1].str)
		}
	case 535:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:2970
		{
			yyVAL.expr = yyDollar[1].iconst
		}
	case 536:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2971
		{
			yyVAL.expr = "+" + yyDollar[2].iconst
		}
	case 537:
		yyDollar = yyS[yypt-2 : yypt+1]
		//line sql.y:2972
		{
			yyVAL.expr = "-" + yyDollar[2].iconst
		}
	case 544:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:3003
		{
			yyVAL.str = yyDollar[1].str
		}
	case 545:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:3004
		{
			yyVAL.str = yyDollar[1].str
		}
	case 546:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:3005
		{
			yyVAL.str = yyDollar[1].str
		}
	case 547:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:3006
		{
			yyVAL.str = yyDollar[1].str
		}
	case 548:
		yyDollar = yyS[yypt-1 : yypt+1]
		//line sql.y:3007
		{
			yyVAL.str = yyDollar[1].str
		}
	}
	goto yystack /* stack new state and value */
}
