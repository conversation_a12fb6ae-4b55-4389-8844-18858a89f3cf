<?php

namespace tests\v1\stock2shop\dal\ratherchat;

use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Response;
use stock2shop\dal\ratherchat\Api;
use stock2shop\dal\ratherchat\Constants;
use stock2shop\dal\ratherchat\models\OrderTracking;
use tests;

class ApiTest extends tests\TestCase
{

    private function createMockGuzzleClient($responses)
    {
        $mock = new MockHandler($responses);
        $handlerStack = HandlerStack::create($mock);

        // Create a mock client that uses our handler stack
        return new Client(['handler' => $handlerStack]);
    }

    public function testPostOrderTrackingSuccess()
    {
        // Create mock response for successful API call
        $mockResponse = new Response(
            Constants::HTTP_SUCCESS,
            ['Content-Type' => 'application/json'],
            json_encode(['message' => Constants::MESSAGE_SUCCESS])
        );

        // Create order tracking data
        $orderTracking = new OrderTracking([
            'firstname' => 'John',
            'phone_number' => '1234567890',
            'order_status' => Constants::ORDER_STATUS_DISPATCHED,
            'order_number' => 'TEST_ORDER_123',
            'delivery_date' => '2025-01-15',
            'dispatch_date' => '2025-01-10',
            'store_name' => 'Test Store',
            'store_address' => '123 Test Street',
            'tracking_link' => 'https://tracking.example.com/123'
        ]);

        $accessKey    = 'fake-access-key';
        $url          = 'https://api.ratherchat.com/tracking';

        // Mock the Guzzle client
        $mockClient = $this->createMockGuzzleClient([$mockResponse]);

        Api::setClient($mockClient);
        // Test the method - it should not throw an exception
        Api::postOrderTracking($orderTracking, $accessKey, $url);
        $this->assertTrue(true); // If we get here, no exception was thrown
    }

    public function testPostOrderTrackingHttpError()
    {
        // Create mock response for HTTP error
        $mockResponse = new Response(
            Constants::HTTP_BAD_REQUEST,
            ['Content-Type' => 'application/json'],
            '{"error": "Bad Request"}'
        );

        $orderTracking = new OrderTracking([
            'firstname' => 'John',
            'order_number' => 'TEST_ORDER_123'
        ]);

        // Test that the method throws an exception for HTTP errors
        $this->expectException(Exception::class);

        $accessKey    = 'fake-access-key';
        $url          = 'https://api.ratherchat.com/tracking';

        // Mock the Guzzle client
        $mockClient = $this->createMockGuzzleClient([$mockResponse]);

        Api::setClient($mockClient);
        // Test the method - it should throw an exception
        Api::postOrderTracking($orderTracking, $accessKey, $url);
    }

    public function testPostOrderTrackingApiError()
    {
        // Create mock response for API error (successful HTTP but error in response body)
        $mockResponse = new Response(
            Constants::HTTP_SUCCESS,
            ['Content-Type' => 'application/json'],
            json_encode(['message' => 'Invalid order data'])
        );

        $orderTracking = new OrderTracking([
            'firstname' => 'John',
            'order_number' => 'TEST_ORDER_123'
        ]);

        // Test that the method throws an exception for API errors
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Failed to post order tracking data. Message: Invalid order data');

        $accessKey    = 'fake-access-key';
        $url          = 'https://api.ratherchat.com/tracking';

        // Mock the Guzzle client
        $mockClient = $this->createMockGuzzleClient([$mockResponse]);

        Api::setClient($mockClient);
        // Test the method - it should throw an exception
        Api::postOrderTracking($orderTracking, $accessKey, $url);
    }

    public function testOrderTrackingDataMapping()
    {
        $orderTracking = new OrderTracking([
            'firstname' => 'John Doe',
            'phone_number' => '1234567890',
            'order_status' => Constants::ORDER_STATUS_DELIVERED,
            'order_number' => 'ORDER_123',
            'delivery_date' => '2025-01-15',
            'dispatch_date' => '2025-01-10',
            'store_name' => 'Test Store',
            'store_address' => '123 Test Street',
            'tracking_link' => 'https://tracking.example.com/123'
        ]);

        $this->assertEquals('John Doe', $orderTracking->firstname);
        $this->assertEquals('1234567890', $orderTracking->phone_number);
        $this->assertEquals(Constants::ORDER_STATUS_DELIVERED, $orderTracking->order_status);
        $this->assertEquals('ORDER_123', $orderTracking->order_number);
        $this->assertEquals('2025-01-15', $orderTracking->delivery_date);
        $this->assertEquals('2025-01-10', $orderTracking->dispatch_date);
        $this->assertEquals('Test Store', $orderTracking->store_name);
        $this->assertEquals('123 Test Street', $orderTracking->store_address);
        $this->assertEquals('https://tracking.example.com/123', $orderTracking->tracking_link);
    }
}