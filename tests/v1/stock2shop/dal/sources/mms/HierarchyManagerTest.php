<?php

namespace tests\v1\stock2shop\dal\sources\mms;

use stock2shop\dal\connector\ConnectorConfiguration;
use stock2shop\dal\s3\S3ObjectStream;
use stock2shop\dal\sources\mms\Configuration;
use stock2shop\dal\sources\mms\ProductsIngressMapper;
use stock2shop\dal\sources\mms\ProductsIngressParser;
use stock2shop\dal\source\ProductsIngressMapperArgs;
use stock2shop\dal\source\ProductsIngressParserArgs;
use stock2shop\dal\source\ProductsIngressState;

class HierarchyManagerTest extends MMSSourceTestCase
{
    /**
     * Test full MMS workflow: Parser -> Mapper -> HierarchyManager
     * Verifies that hierarchy metadata is applied without suffix
     */
    public function testMMSWorkflowWithHierarchyMetadata()
    {
        // Enable virtualization to use test CSV data
        global $config;
        $config['virtualization']['enabled'] = true;

        // Initialize test configuration
        $conf = $this->initTest();
        $conf->setProductsFieldMap(Configuration::DEFAULT_PRODUCT_FIELD_MAP);

        // Create S3 stream with the product master data
        $stream = fopen(__DIR__ . '/data/product_master_data.json', 'r');
        $s3Stream = new S3ObjectStream();
        $s3Stream->handle = $stream;

        // Track the final mapped product
        $finalMappedProduct = null;

        // Create parser arguments with callback
        $parserArgs = new ProductsIngressParserArgs(
            $s3Stream,
            new ProductsIngressState(
                ConnectorConfiguration::getInstance()->getClientConfig(),
                self::SOURCE_ID
            ),
            function ($rows) use (&$finalMappedProduct) {
                // Run the mapper on the parsed rows (this calls HierarchyManager)
                $mapperArgs = new ProductsIngressMapperArgs(
                    $rows,
                    new ProductsIngressState(
                        ConnectorConfiguration::getInstance()->getClientConfig(),
                        self::SOURCE_ID
                    )
                );

                $mappedProducts = ProductsIngressMapper::map($mapperArgs);
                $finalMappedProduct = $mappedProducts[0] ?? null;
            }
        );

        // Run the MMS parser workflow
        ProductsIngressParser::parse($parserArgs);

        // Verify we got a mapped product
        $this->assertNotNull($finalMappedProduct, 'Should have a mapped product');

        // Verify basic product data
        $this->assertEquals('DCK73', $finalMappedProduct['source_product_code']);
        $this->assertEquals('ZOMBI GLASS - REG', $finalMappedProduct['title']);

        // MAIN TEST: Verify hierarchy metadata is applied WITHOUT suffix
        $this->assertArrayHasKey('meta_business_unit', $finalMappedProduct);
        $this->assertArrayHasKey('meta_product_division', $finalMappedProduct);
        $this->assertArrayHasKey('meta_level_1_online_division_primary', $finalMappedProduct);

        // Verify the hierarchy values from our test CSV
        $this->assertEquals('03-ADULT CLOTHING AND ACCESSORIES', $finalMappedProduct['meta_business_unit']);
        $this->assertEquals('02-OUTERWEAR', $finalMappedProduct['meta_product_division']);
        $this->assertEquals('Men', $finalMappedProduct['meta_level_1_online_division_primary']);

        // CRITICAL: Verify NO _secondary suffixed keys exist (suffix removal test)
        $this->assertArrayNotHasKey('meta_business_unit_secondary', $finalMappedProduct);
        $this->assertArrayNotHasKey('meta_product_division_secondary', $finalMappedProduct);
        $this->assertArrayNotHasKey('meta_level_1_online_division_primary_secondary', $finalMappedProduct);

        // Verify ignored columns are not present
        $this->assertArrayNotHasKey('meta_sub_class_code', $finalMappedProduct);
        $this->assertArrayNotHasKey('meta_primary_secondary', $finalMappedProduct);

        // Close the stream
        fclose($stream);
    }
}