<?php

namespace tests\v1\stock2shop\dal\sources\mms;

use stock2shop\dal\sources\mms\ProductsIngressParser;
use stock2shop\dal\sources\mms\ProductsIngressMapper;
use stock2shop\dal\source\ProductsIngressParserArgs;
use stock2shop\dal\source\ProductsIngressMapperArgs;
use stock2shop\dal\source\ProductsIngressState;
use stock2shop\dal\s3\S3ObjectStream;

class HierarchyManagerTest extends MMSSourceTestCase
{
    /**
     * Test full MMS workflow: Parser -> Mapper -> HierarchyManager
     * Verifies that hierarchy metadata is applied without suffix
     */
    public function testMMSWorkflowWithHierarchyMetadata()
    {
        // Enable virtualization to use test CSV data
        global $config;
        $config['virtualization']['enabled'] = true;

        // Initialize test configuration
        $conf = $this->initTest();
        $conf->setProductsFieldMap(\stock2shop\dal\sources\mms\Configuration::DEFAULT_PRODUCT_FIELD_MAP);

        // Create S3 stream with the product master data
        $stream = fopen(__DIR__ . '/data/product_master_data.json', 'r');
        $s3Stream = new S3ObjectStream();
        $s3Stream->handle = $stream;

        // Track the final mapped product
        $finalMappedProduct = null;

        // Create parser arguments with callback
        $parserArgs = new ProductsIngressParserArgs(
            $s3Stream,
            new ProductsIngressState(
                \stock2shop\dal\connector\ConnectorConfiguration::getInstance()->getClientConfig(),
                self::SOURCE_ID
            ),
            function ($rows) use (&$finalMappedProduct) {
                // Run the mapper on the parsed rows (this calls HierarchyManager)
                $mapperArgs = new ProductsIngressMapperArgs(
                    $rows,
                    new ProductsIngressState(
                        \stock2shop\dal\connector\ConnectorConfiguration::getInstance()->getClientConfig(),
                        self::SOURCE_ID
                    )
                );

                $mappedProducts = ProductsIngressMapper::map($mapperArgs);
                $finalMappedProduct = $mappedProducts[0] ?? null;
            }
        );

        // Run the MMS parser workflow
        ProductsIngressParser::parse($parserArgs);

        // Verify we got a mapped product
        $this->assertNotNull($finalMappedProduct, 'Should have a mapped product');

        // Verify basic product data
        $this->assertEquals('DCK73', $finalMappedProduct['source_product_code']);
        $this->assertEquals('ZOMBI GLASS - REG', $finalMappedProduct['title']);

        // MAIN TEST: Verify hierarchy metadata is applied WITHOUT suffix
        $this->assertArrayHasKey('meta_business_unit', $finalMappedProduct);
        $this->assertArrayHasKey('meta_product_division', $finalMappedProduct);
        $this->assertArrayHasKey('meta_level_1_online_division_primary', $finalMappedProduct);

        // Verify the hierarchy values from our test CSV
        $this->assertEquals('03-ADULT CLOTHING AND ACCESSORIES', $finalMappedProduct['meta_business_unit']);
        $this->assertEquals('02-OUTERWEAR', $finalMappedProduct['meta_product_division']);
        $this->assertEquals('Men', $finalMappedProduct['meta_level_1_online_division_primary']);

        // CRITICAL: Verify NO _secondary suffixed keys exist (suffix removal test)
        $this->assertArrayNotHasKey('meta_business_unit_secondary', $finalMappedProduct);
        $this->assertArrayNotHasKey('meta_product_division_secondary', $finalMappedProduct);
        $this->assertArrayNotHasKey('meta_level_1_online_division_primary_secondary', $finalMappedProduct);

        // Verify ignored columns are not present
        $this->assertArrayNotHasKey('meta_sub_class_code', $finalMappedProduct);
        $this->assertArrayNotHasKey('meta_primary_secondary', $finalMappedProduct);

        // Close the stream
        fclose($stream);
    }

    /**
     * Test that primary_secondary column is properly ignored
     */
    public function testPrimarySecondaryColumnIgnored()
    {
        // Enable virtualization to use test CSV data
        global $config;
        $config['virtualization']['enabled'] = true;

        $hierarchyManager = new HierarchyManager();

        // Test that primary_secondary column is ignored
        $flatProduct = ['meta_SubClassCode' => '00848'];
        $hierarchyManager->setHierarchyMeta($flatProduct);

        // Should have other metadata
        $this->assertArrayHasKey('meta_business_unit', $flatProduct);
        $this->assertArrayHasKey('meta_level_1_online_division_primary', $flatProduct);

        // Should NOT have primary_secondary as metadata (it's in IGNORE_COLS)
        $this->assertArrayNotHasKey('meta_primary_secondary', $flatProduct);
    }

    /**
     * Test that subclass column is properly ignored
     */
    public function testSubclassColumnIgnored()
    {
        // Enable virtualization to use test CSV data
        global $config;
        $config['virtualization']['enabled'] = true;

        $hierarchyManager = new HierarchyManager();

        $flatProduct = ['meta_SubClassCode' => '00848'];
        $hierarchyManager->setHierarchyMeta($flatProduct);

        // Should have other metadata
        $this->assertArrayHasKey('meta_business_unit', $flatProduct);
        $this->assertArrayHasKey('meta_level_1_online_division_primary', $flatProduct);

        // Should NOT have subclass as metadata (it's used for lookup but not added as meta)
        $this->assertArrayNotHasKey('meta_subclass', $flatProduct);
        $this->assertArrayNotHasKey('meta_sub_class_code', $flatProduct);
    }

    /**
     * Test with non-existent subclass
     */
    public function testNonExistentSubclass()
    {
        // Enable virtualization to use test CSV data
        global $config;
        $config['virtualization']['enabled'] = true;

        $hierarchyManager = new HierarchyManager();

        $flatProduct = ['meta_SubClassCode' => 'NONEXISTENT'];
        $originalProduct = $flatProduct;
        $hierarchyManager->setHierarchyMeta($flatProduct);

        // Should remain unchanged
        $this->assertEquals($originalProduct, $flatProduct);
    }

    /**
     * Test with missing SubClassCode
     */
    public function testMissingSubClassCode()
    {
        // Enable virtualization to use test CSV data
        global $config;
        $config['virtualization']['enabled'] = true;

        $hierarchyManager = new HierarchyManager();

        $flatProduct = ['some_other_field' => 'value'];
        $originalProduct = $flatProduct;
        $hierarchyManager->setHierarchyMeta($flatProduct);

        // Should remain unchanged
        $this->assertEquals($originalProduct, $flatProduct);
    }

    /**
     * Test that empty values in CSV are ignored
     */
    public function testEmptyValuesInCsvIgnored()
    {
        // Enable virtualization to use test CSV data
        global $config;
        $config['virtualization']['enabled'] = true;

        $hierarchyManager = new HierarchyManager();

        // Use a subclass that has some empty values in the CSV
        $flatProduct = ['meta_SubClassCode' => '00848'];
        $hierarchyManager->setHierarchyMeta($flatProduct);

        // Should have metadata for non-empty values
        $this->assertArrayHasKey('meta_business_unit', $flatProduct);
        $this->assertArrayHasKey('meta_level_1_online_division_primary', $flatProduct);

        // Check that we don't have metadata for columns that are empty in this row
        // (based on the sample CSV, some secondary columns are empty)
        $this->assertArrayNotHasKey('meta_level_1_online_division_secondary', $flatProduct);
        $this->assertArrayNotHasKey('meta_level_2_online _department_secondary', $flatProduct);
    }

    /**
     * Integration test: Load product master data JSON and run full workflow with hierarchy metadata
     */
    public function testFullWorkflowWithProductMasterData()
    {
        // Enable virtualization to use test CSV data
        global $config;
        $config['virtualization']['enabled'] = true;

        // Load the actual product master data JSON
        $productDataJson = file_get_contents(__DIR__ . '/data/product_master_data.json');
        $this->assertNotEmpty($productDataJson, 'Product master data JSON should be loaded');

        // Parse and flatten the product data (similar to how ProductsIngressParser would do it)
        $productData = json_decode($productDataJson, true);
        $this->assertNotEmpty($productData, 'Product data should be parsed successfully');

        // Create flattened product data similar to what MMSProductFields::getFlattened would produce
        $flatProduct = [
            'source_product_code' => $productData['Product']['Style']['StyleCode'],
            'title' => $productData['Product']['Style']['StyleDesc'] ?? 'ZOMBI GLASS - REG',
            'body_html' => $productData['Product']['Style']['StyleShortDesc'] ?? 'ZOMBI GLASS',
            'vendor' => $productData['Product']['Style']['BrandName'] ?? 'Brand X',
            'collection' => $productData['Product']['Style']['FamilyName'] ?? 'Premium',
            'meta_SubClassCode' => (string)$productData['Product']['Style']['SubClassCode'], // Convert to string as expected
            'meta_subClassName' => $productData['Product']['Style']['SubClassName'] ?? 'ZOMBI GLASS - REG',
            'product_active' => true,
            'variant_active' => true,
            'inventory_management' => true,
        ];

        // Verify the SubClassCode is what we expect
        $this->assertEquals('4605', $flatProduct['meta_SubClassCode']);

        // Create HierarchyManager and apply hierarchy metadata
        $hierarchyManager = new \stock2shop\dal\sources\mms\HierarchyManager();

        // Apply hierarchy metadata (this is the main functionality we're testing)
        $hierarchyManager->setHierarchyMeta($flatProduct);

        // Verify that hierarchy metadata was applied WITHOUT any suffix
        $this->assertArrayHasKey('meta_business_unit', $flatProduct);
        $this->assertArrayHasKey('meta_product_division', $flatProduct);
        $this->assertArrayHasKey('meta_product_group', $flatProduct);
        $this->assertArrayHasKey('meta_department', $flatProduct);
        $this->assertArrayHasKey('meta_class', $flatProduct);
        $this->assertArrayHasKey('meta_sub_class', $flatProduct);
        $this->assertArrayHasKey('meta_level_1_online_division_primary', $flatProduct);
        $this->assertArrayHasKey('meta_level_2_online _department_primary', $flatProduct);
        $this->assertArrayHasKey('meta_level_3_online_product_category_primary', $flatProduct);

        // Verify the actual values match what we added to the CSV
        $this->assertEquals('03-ADULT CLOTHING AND ACCESSORIES', $flatProduct['meta_business_unit']);
        $this->assertEquals('02-OUTERWEAR', $flatProduct['meta_product_division']);
        $this->assertEquals('06-MENS OUTERWEAR', $flatProduct['meta_product_group']);
        $this->assertEquals('015-MENS OUTERWEAR', $flatProduct['meta_department']);
        $this->assertEquals('0142-MENS S/S TOPS', $flatProduct['meta_class']);
        $this->assertEquals('04605-ZOMBI GLASS - REG', $flatProduct['meta_sub_class']);
        $this->assertEquals('Men', $flatProduct['meta_level_1_online_division_primary']);
        $this->assertEquals('Clothing', $flatProduct['meta_level_2_online _department_primary']);
        $this->assertEquals('T-Shirts & Vests', $flatProduct['meta_level_3_online_product_category_primary']);

        // Verify that NO _secondary suffixed keys exist (this is the main test for suffix removal)
        $this->assertArrayNotHasKey('meta_business_unit_secondary', $flatProduct);
        $this->assertArrayNotHasKey('meta_product_division_secondary', $flatProduct);
        $this->assertArrayNotHasKey('meta_product_group_secondary', $flatProduct);
        $this->assertArrayNotHasKey('meta_department_secondary', $flatProduct);
        $this->assertArrayNotHasKey('meta_class_secondary', $flatProduct);
        $this->assertArrayNotHasKey('meta_sub_class_secondary', $flatProduct);
        $this->assertArrayNotHasKey('meta_level_1_online_division_primary_secondary', $flatProduct);
        $this->assertArrayNotHasKey('meta_level_2_online _department_primary_secondary', $flatProduct);
        $this->assertArrayNotHasKey('meta_level_3_online_product_category_primary_secondary', $flatProduct);

        // Verify that ignored columns are not present as metadata
        $this->assertArrayNotHasKey('meta_sub_class_code', $flatProduct); // subclass is ignored
        $this->assertArrayNotHasKey('meta_primary_secondary', $flatProduct); // primary_secondary is ignored

        // Verify that original product data is preserved
        $this->assertEquals('DCK73', $flatProduct['source_product_code']);
        $this->assertEquals('ZOMBI GLASS - REG', $flatProduct['title']);
        $this->assertEquals('Brand X', $flatProduct['vendor']);
        $this->assertTrue($flatProduct['product_active']);
        $this->assertTrue($flatProduct['variant_active']);
        $this->assertTrue($flatProduct['inventory_management']);
    }

    /**
     * Integration test: Test full workflow using ProductsIngressParser and ProductsIngressMapper
     */
    public function testFullWorkflowWithParserAndMapper()
    {
        // Enable virtualization to use test CSV data
        global $config;
        $config['virtualization']['enabled'] = true;

        // Initialize test configuration
        $conf = $this->initTest();
        $conf->setProductsFieldMap(\stock2shop\dal\sources\mms\Configuration::DEFAULT_PRODUCT_FIELD_MAP);

        // Create S3 stream with the product master data
        $stream = fopen(__DIR__ . '/data/product_master_data.json', 'r');
        $s3Stream = new \stock2shop\dal\s3\S3ObjectStream();
        $s3Stream->handle = $stream;

        // Track parsed rows
        $parsedRows = [];
        $callbackStatus = new \stdClass();
        $callbackStatus->called = false;

        // Create parser arguments with callback to capture parsed data
        $parserArgs = new \stock2shop\dal\source\ProductsIngressParserArgs(
            $s3Stream,
            new \stock2shop\dal\source\ProductsIngressState(
                \stock2shop\dal\connector\ConnectorConfiguration::getInstance()->getClientConfig(),
                self::SOURCE_ID
            ),
            function ($rows) use (&$parsedRows, $callbackStatus) {
                $callbackStatus->called = true;
                $parsedRows = array_merge($parsedRows, $rows);

                // Now run the mapper on the parsed rows (this is where HierarchyManager gets called)
                $mapperArgs = new \stock2shop\dal\source\ProductsIngressMapperArgs(
                    $rows,
                    new \stock2shop\dal\source\ProductsIngressState(
                        \stock2shop\dal\connector\ConnectorConfiguration::getInstance()->getClientConfig(),
                        self::SOURCE_ID
                    )
                );

                $mappedProducts = \stock2shop\dal\sources\mms\ProductsIngressMapper::map($mapperArgs);

                // Store mapped products for verification
                $callbackStatus->mappedProducts = $mappedProducts;
            }
        );

        // Run the parser (this will trigger the callback with mapper)
        \stock2shop\dal\sources\mms\ProductsIngressParser::parse($parserArgs);

        // Verify the callback was called
        $this->assertTrue($callbackStatus->called, 'Parser callback should have been called');
        $this->assertNotEmpty($parsedRows, 'Parser should have produced rows');
        $this->assertNotEmpty($callbackStatus->mappedProducts, 'Mapper should have produced mapped products');

        // Get the first mapped product for detailed verification
        $mappedProduct = $callbackStatus->mappedProducts[0];

        // Verify basic product data from the JSON
        $this->assertEquals('DCK73', $mappedProduct['source_product_code']);
        $this->assertEquals('ZOMBI GLASS - REG', $mappedProduct['title']);
        $this->assertEquals('ZOMBI GLASS', $mappedProduct['body_html']);
        $this->assertEquals('Brand X', $mappedProduct['vendor']);

        // Verify hierarchy metadata was applied WITHOUT suffix (this is the main test)
        $this->assertArrayHasKey('meta_business_unit', $mappedProduct);
        $this->assertArrayHasKey('meta_product_division', $mappedProduct);
        $this->assertArrayHasKey('meta_level_1_online_division_primary', $mappedProduct);

        // Verify the hierarchy values match our CSV data
        $this->assertEquals('03-ADULT CLOTHING AND ACCESSORIES', $mappedProduct['meta_business_unit']);
        $this->assertEquals('02-OUTERWEAR', $mappedProduct['meta_product_division']);
        $this->assertEquals('Men', $mappedProduct['meta_level_1_online_division_primary']);

        // CRITICAL TEST: Verify NO _secondary suffixed keys exist
        $this->assertArrayNotHasKey('meta_business_unit_secondary', $mappedProduct);
        $this->assertArrayNotHasKey('meta_product_division_secondary', $mappedProduct);
        $this->assertArrayNotHasKey('meta_level_1_online_division_primary_secondary', $mappedProduct);
        $this->assertArrayNotHasKey('meta_product_group_secondary', $mappedProduct);
        $this->assertArrayNotHasKey('meta_department_secondary', $mappedProduct);
        $this->assertArrayNotHasKey('meta_class_secondary', $mappedProduct);
        $this->assertArrayNotHasKey('meta_sub_class_secondary', $mappedProduct);

        // Verify ignored columns are not present as metadata
        $this->assertArrayNotHasKey('meta_sub_class_code', $mappedProduct);
        $this->assertArrayNotHasKey('meta_primary_secondary', $mappedProduct);

        // Verify standard mapper fields are present
        $this->assertTrue($mappedProduct['product_active']);
        $this->assertTrue($mappedProduct['variant_active']);
        $this->assertTrue($mappedProduct['inventory_management']);

        // Close the stream
        fclose($stream);
    }
}