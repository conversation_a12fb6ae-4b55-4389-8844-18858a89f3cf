<?php

namespace tests\v1\stock2shop\dal\store_credits\credits_yard;

use GuzzleHttp;
use stock2shop\dal\channels\shopify2;
use stock2shop\dal\store_credit\StoreCreditPost;
use stock2shop\dal\store_credits\credits_yard\Client;
use stock2shop\repository\OrderRepository;
use stock2shop\vo;
use stock2shop\vo\Refund;
use tests\v1\stock2shop\dal\channels\shopify2\TestCase;

use Exception;
use GuzzleHttp\Exception\GuzzleException;
use stock2shop\dal\store_credits\credits_yard\StoreCredit;
use stock2shop\vo\Transaction;
use tests;

class StoreCreditTest extends TestCase
{
    /**
     * Uses Shopify2 configuration since we cannot have more than one config instance at a time
     * @return void
     * @throws Exception
     * @throws GuzzleException
     */
    public function testDoesNotPostStoreCredit()
    {
        $config = $this->initShopify2Test([], [
            shopify2\Configuration::SECRET_CREDITS_YARD_MERCHANT_API_KEY => 'asdf'
        ]);

        $container    = [];
        $handlerStack = GuzzleHttp\HandlerStack::create(
            new GuzzleHttp\Handler\MockHandler([
                new GuzzleHttp\Psr7\Response(
                    200,
                    ['Content-Type' => 'application/json'],
                    file_get_contents(__DIR__ . '/mocks/FetchCustomerResponse.json')
                )
            ])
        );
        $handlerStack->push(GuzzleHttp\Middleware::history($container));

        $order = tests\DB::makeOrder(
            tests\DB::makeFromConfig($config),
            tests\Generator::generateOrder($config, self::CHANNEL_ID, self::SOURCE_ID)
        );

        $refund         = $this->createRefund($order);
        $order->refunds = [$refund];
        $oRepo          = new OrderRepository($config, $order);
        $oRepo->saveRefunds();
        $oRepo->load();

        $order        = $oRepo->getOrder();
        $transaction  = $this->getTransaction($order);
        $postArgs     = new StoreCreditPost($oRepo->getOrder(), $transaction);
        $store_credit = new StoreCredit();
        $store_credit->postStoreCredit($postArgs, new Client($handlerStack));

        // 0 api requests because transaction is credit and store credit
        $this->assertCount(0, $container);
    }

    /**
     * @throws Exception|GuzzleException
     */
    public function testStoreCreditAlreadyAdjusted()
    {
        $config = $this->initShopify2Test([], [
            shopify2\Configuration::SECRET_CREDITS_YARD_MERCHANT_API_KEY => 'asdf'
        ]);

        $container    = [];
        $handlerStack = GuzzleHttp\HandlerStack::create(
            new GuzzleHttp\Handler\MockHandler([
                new GuzzleHttp\Psr7\Response(
                    200,
                    ['Content-Type' => 'application/json'],
                    file_get_contents(__DIR__ . '/mocks/FetchCustomerResponse.json')
                )
            ])
        );
        $handlerStack->push(GuzzleHttp\Middleware::history($container));

        $order = tests\DB::makeOrder(
            tests\DB::makeFromConfig($config),
            tests\Generator::generateOrder($config, self::CHANNEL_ID, self::SOURCE_ID)
        );

        $refund         = $this->createRefund($order);
        $order->refunds = [$refund];
        $oRepo          = new OrderRepository($config, $order);
        $oRepo->saveRefunds();
        $oRepo->load();

        $order              = $oRepo->getOrder();
        $transaction        = $this->getTransaction($order);
        $transaction->state = vo\Transaction::STATE_AUTHORISING;
        $postArgs           = new StoreCreditPost($oRepo->getOrder(), $transaction);
        $store_credit       = new StoreCredit();
        $this->expectExceptionMessage('Transaction already exists for order channel_order_code-2');
        $store_credit->postStoreCredit($postArgs, new Client($handlerStack));
    }

    /**
     * Uses Shopify2 configuration since we cannot have more than one config instance at a time
     * @return void
     * @throws Exception
     * @throws GuzzleException
     */
    public function testPostStoreCredit()
    {
        $config = $this->initShopify2Test([], [
            shopify2\Configuration::ACCESS_TOKEN                         => 'shopify2_asdf',
            shopify2\Configuration::SECRET_CREDITS_YARD_MERCHANT_API_KEY => 'credits_yard_asdf'
        ]);

        $mock         = new GuzzleHttp\Handler\MockHandler([
            new GuzzleHttp\Psr7\Response(
                200,
                ['Content-Type' => 'application/json'],
                file_get_contents(__DIR__ . '/mocks/FetchCustomerResponse.json')
            ),
            new GuzzleHttp\Psr7\Response(200)
        ]);
        $handlerStack = GuzzleHttp\HandlerStack::create($mock);
        $container    = [];
        $handlerStack->push(GuzzleHttp\Middleware::history($container));

        // create order refund with store credit transaction
        $order_data                       = tests\Generator::generateOrder(
            $config,
            self::CHANNEL_ID,
            self::SOURCE_ID
        );
        $order_data['channel_order_code'] = '123';
        $order                            = tests\DB::makeOrder(tests\DB::makeFromConfig($config), $order_data);

        $refund         = $this->createRefund($order);
        $order->refunds = [$refund];
        $oRepo          = new OrderRepository($config, $order);
        $oRepo->saveRefunds();
        $oRepo->load();

        $order              = $oRepo->getOrder();
        $transaction        = $this->getTransaction($order);
        $transaction->state = vo\Transaction::STATE_AUTHORISING;
        $refund             = $order->refunds[0];
        $postArgs           = new StoreCreditPost($oRepo->getOrder(), $transaction);

        $store_credit = new StoreCredit();
        $store_credit->postStoreCredit($postArgs, new Client($handlerStack));

        // 1 api request to check transactions
        // + 1 api request to post adjustment
        $this->assertCount(2, $container);
        $fetch_customers_request = json_decode($container[0]['request']->getBody()->getContents(), true);
        $this->assertEquals('email-2', $fetch_customers_request['customer_email']);

        $adjust_credit_request = json_decode($container[1]['request']->getBody()->getContents(), true);
        $this->assertEquals(460.46, $adjust_credit_request['amount']);
        $this->assertEquals('S2S_123', $adjust_credit_request['reason']);
        $this->assertEquals('email-2', $adjust_credit_request['customer_email']);
    }

    /**
     * @param vo\SystemOrder $order
     * @return Refund
     * @throws Exception
     */
    private function createRefund(vo\SystemOrder $order): vo\Refund
    {
        return Refund::createFromOrder(
            $order,
            TestCase::FULFILLMENTSERVICE_ID,
            vo\Config::CONNECTOR_FULFILLMENTSERVICE,
            '123',
            [],
            false);
    }

    private function getTransaction(vo\SystemOrder $order): Transaction
    {
        $trans                            = new vo\Transaction();
        $trans->is_store_credit           = true;
        $trans->gateway                   = 'test';
        $trans->kind                      = vo\Transaction::KIND_CREDIT;
        $trans->amount_calculation_method = vo\Transaction::AMOUNT_CALCULATION_METHOD_CHANNEL;
        $trans->amount                    = $order->total;
        return $trans;
    }
}