<?php
namespace tests\v1\stock2shop\dal\search;

use tests;
use stock2shop\dal\search;
use stock2shop\vo;

class ElasticsearchQueryTest extends tests\TestCase
{
    public function testSetQuery() {

        // constructor calls setQuery()
        $q = new search\ElasticsearchQuery();
        $test = $q->get();

        // we should have template query now
        $this->assertTrue(is_object($test->aggs));
        $this->assertTrue(is_object($test->query));
        $this->assertTrue(is_object($test->query->bool));
        $this->assertTrue(is_array($test->query->bool->filter));
        $this->assertTrue(is_array($test->query->bool->must));
        $this->assertTrue(is_array($test->query->bool->must_not));
    }

    public function testSetSegmentFilters() {
        $segments = [];

        // product segments
        // es filter 1
        $segments[] = new vo\SystemSegment([
            'type'     => 'products',
            'key'      => 'collection',
            'owner'    => 'system',
            'operator' => 'equal',
            'value'    => 'bar'
        ]);

        // es filter 2
        $segments[] = new vo\SystemSegment([
            'type'     => 'products',
            'key'      => 'variants.qty',
            'owner'    => 'system',
            'operator' => 'less than',
            'value'    => '10'
        ]);

        // nested
        // es filter 3
        $segments[] = new vo\SystemSegment([
            'type'     => 'products',
            'key'      => 'meta.key',
            'owner'    => 'system',
            'operator' => 'equal',
            'value'    => 'size'
        ]);
        $segments[] = new vo\SystemSegment([
            'type'     => 'products',
            'key'      => 'meta.value',
            'owner'    => 'system',
            'operator' => 'greater than',
            'value'    => '0'
        ]);
        $segments[] = new vo\SystemSegment([
            'type'     => 'products',
            'key'      => 'meta.key',
            'owner'    => 'system',
            'operator' => 'not contains',
            'value'    => 'demo'
        ]);
        $segments[] = new vo\SystemSegment([
            'type'     => 'products',
            'key'      => 'meta.value',
            'owner'    => 'system',
            'operator' => 'not contains',
            'value'    => 'true'
        ]);
        $segments[] = new vo\SystemSegment([
            'type'     => 'products',
            'key'      => 'meta.key',
            'owner'    => 'system',
            'operator' => 'not contains',
            'value'    => 'staff'
        ]);

        // es filter 4
        $segments[] = new vo\SystemSegment([
            'type'     => 'products',
            'key'      => 'variants.price_tiers.price',
            'owner'    => 'system',
            'operator' => 'greater than',
            'value'    => '0'
        ]);
        $segments[] = new vo\SystemSegment([
            'type'     => 'products',
            'key'      => 'variants.price_tiers.tier',
            'owner'    => 'system',
            'operator' => 'equal',
            'value'    => 'foo'
        ]);

        // es filter 5
        $segments[] = new vo\SystemSegment([
            'type'     => 'products',
            'key'      => 'variants.qty_availability.qty',
            'owner'    => 'system',
            'operator' => 'greater than',
            'value'    => '0'
        ]);
        $segments[] = new vo\SystemSegment([
            'type'     => 'products',
            'key'      => 'variants.qty_availability.description',
            'owner'    => 'system',
            'operator' => 'lookup',
            'value'    => 'a,b,c'
        ]);

        // es filter 6 contains
        $segments[] = new vo\SystemSegment([
            'type'     => 'products',
            'key'      => 'collection',
            'owner'    => 'system',
            'operator' => 'contains',
            'value'    => 'bar'
        ]);

        // es filter 7 not contains
        $segments[] = new vo\SystemSegment([
            'type'     => 'products',
            'key'      => 'collection',
            'owner'    => 'system',
            'operator' => 'not contains',
            'value'    => 'baz'
        ]);

        // Order filter
        $segments[] = new vo\SystemSegment([
            'type'     => 'orders',
            'key'      => 'variants.price_tiers.tier',
            'owner'    => 'system',
            'operator' => 'equal',
            'value'    => 'baz'
        ]);


        // test invalid index (segments should  be ignored)
        $q = new search\ElasticsearchQuery();
        $q->setSegmentFilters($segments,'xyz');
        $test = $q->get();
        $this->assertEquals(0, count($test->query->bool->filter), "ignore invalid index");

        // valid
        $q = new search\ElasticsearchQuery();
        $q->setSegmentFilters($segments,'products');
        $test = $q->get();

        // There should be 6 filters in total
        $this->assertEquals(
            6,
            count($test->query->bool->filter),
            "term set"
        );

        // There should be 2 "must_not" clauses
        $this->assertEquals(
            2,
            count($test->query->bool->must_not),
            "term set"
        );

        // es filter 1 collection (no nested)
        $this->assertEquals(
            "bar",
            $test->query->bool->filter[0]->term->collection,
            "term set"
        );

        // es filter 2 variants.qty (no nested less than)
        $this->assertEquals(
            "10",
            $test->query->bool->filter[1]->range->{"variants.qty"}->lt,
            "term set"
        );

        // field type is number should be cast
        $this->assertTrue(
            is_int($test->query->bool->filter[1]->range->{"variants.qty"}->lt)
        );

        // es filter 1 meta, nested with two terms
        $this->assertEquals(
            2,
            count($test->query->bool->filter[3]->nested->query->bool->filter),
            "nested set"
        );
        $this->assertEquals(
            "size",
            $test->query->bool->filter[3]->nested->query->bool->filter[0]->term->{"meta.key"},
            "nested set"
        );
        $this->assertEquals(
            "0",
            $test->query->bool->filter[3]->nested->query->bool->filter[1]->range->{"meta.value"}->gt,
            "nested set"
        );

        // es filter 2 meta, nested with 3 terms in must_not clause
        $this->assertEquals(
            3,
            count($test->query->bool->must_not[1]->nested->query->bool->filter),
            "nested set"
        );
        $this->assertEquals(
            "*demo*",
            $test->query->bool->must_not[1]->nested->query->bool->filter[0]->wildcard->{"meta.key"},
            "nested set"
        );
        $this->assertEquals(
            "*true*",
            $test->query->bool->must_not[1]->nested->query->bool->filter[1]->wildcard->{"meta.value"},
            "nested set"
        );
        $this->assertEquals(
            "*staff*",
            $test->query->bool->must_not[1]->nested->query->bool->filter[2]->wildcard->{"meta.key"},
            "nested set"
        );

        // es filter 4 price tiers
        $this->assertEquals(
            2,
            count($test->query->bool->filter[4]->nested->query->bool->filter)
        );
        $this->assertEquals(
            0,
            $test->query->bool->filter[4]->nested->query->bool->filter[0]->range->{'variants.price_tiers.price'}->gt
        );
        $this->assertEquals(
            'foo',
            $test->query->bool->filter[4]->nested->query->bool->filter[1]->term->{'variants.price_tiers.tier'}
        );

        // es filter 5 qty
        $this->assertEquals(
            2,
            count($test->query->bool->filter[5]->nested->query->bool->filter)
        );
        $this->assertEquals(
            0,
            $test->query->bool->filter[5]->nested->query->bool->filter[0]->range->{'variants.qty_availability.qty'}->gt
        );
        $this->assertEquals(
            3,
            count($test->query->bool->filter[5]->nested->query->bool->filter[1]->terms->{'variants.qty_availability.description'})
        );

        // Test that nested aggregate values (meta) and user segments are each added to their own nested queries
        // Ref: https://github.com/stock2shop/b2b/issues/121#issuecomment-1737566562
        $payload = '{
          "query": {
            "bool": {
              "must": [],
              "filter": [
                {
                  "nested": {
                    "path": "meta",
                    "query": {
                      "bool": {
                        "filter": [
                          {
                            "term": {
                              "meta.key": "foo"
                            }
                          },
                          {
                            "term": {
                              "meta.value": "aggs value"
                            }
                          }
                        ]
                      }
                    }
                  }
                }
              ]
            }
          },
          "size": "10",
          "from": 0,
          "sort": {
            "_score": {
              "order": "desc"
            }
          }
        }';
        $query = json_decode($payload);
        $q = new search\ElasticsearchQuery($query);

        $segments = [];
        $segments[] = new vo\SystemSegment([
            'type'     => 'products',
            'key'      => 'meta.key',
            'owner'    => 'system',
            'operator' => 'equal',
            'value'    => 'bar'
        ]);
        $segments[] = new vo\SystemSegment([
            'type'     => 'products',
            'key'      => 'meta.value',
            'owner'    => 'system',
            'operator' => 'equal',
            'value'    => 'user segment'
        ]);
        $q->setSegmentFilters($segments, 'products');
        $test = $q->get();

        $this->assertCount(
            2,
            $test->query->bool->filter,
            "filters set"
        );

        // aggs (menu) filter
        $this->assertEquals(
            2,
            count($test->query->bool->filter[0]->nested->query->bool->filter),
            "nested set"
        );
        $this->assertEquals(
            "foo",
            $test->query->bool->filter[0]->nested->query->bool->filter[0]->term->{"meta.key"},
            "nested set"
        );
        $this->assertEquals(
            "aggs value",
            $test->query->bool->filter[0]->nested->query->bool->filter[1]->term->{"meta.value"},
            "nested set"
        );

        // user segment
        $this->assertEquals(
            2,
            count($test->query->bool->filter[1]->nested->query->bool->filter),
            "nested set"
        );
        $this->assertEquals(
            "bar",
            $test->query->bool->filter[1]->nested->query->bool->filter[0]->term->{"meta.key"},
            "nested set"
        );
        $this->assertEquals(
            "user segment",
            $test->query->bool->filter[1]->nested->query->bool->filter[1]->term->{"meta.value"},
            "nested set"
        );
    }

    /**
     * @dataProvider setSegmentFilters2DataProvider
     * @param $expected
     * @param $segments
     * @param $index
     * @param $existing_query
     * @return void
     */
    public function testSetSegmentFilters2($expected, $segments, $index, $existing_query)
    {
        global $config;
        $query = ($existing_query === false) ? false : json_decode($existing_query);
        $q = new search\ElasticsearchQuery($query);

        $q->setSegmentFilters($segments, $index);
        $test = $q->get();

        $path = $config['baseDir'] . "/../../tests/v1/stock2shop/dal/search/ElasticsearchQueryExpectedResults/" . $expected;
        $file = file_get_contents($path);
        $expected = json_decode($file);

        $this->assertEquals(
            $expected,
            $test->query,
            "query does not match"
        );
    }

    public static function setSegmentFilters2DataProvider(): \Generator
    {
        // PRODUCT SEGMENTS:

        yield "Invalid index" => [
            "expected" => 'testSetSegmentFilters-InvalidIndex.json',
            "segments" => [
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'collection',
                    'owner'    => 'system',
                    'operator' => 'equal',
                    'value'    => 'baz'
                ])
            ],
            "index" => "foo",
            "existing_query" => false
        ];

        // Basic operator tests
        yield "Equal to" => [
            "expected" => 'testSetSegmentFilters-EqualTo.json',
            "segments" => [
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'collection',
                    'owner'    => 'system',
                    'operator' => 'equal',
                    'value'    => 'bar'
                ])
            ],
            "index" => "products",
            "existing_query" => false
        ];
        yield "Less than" => [
            "expected" => 'testSetSegmentFilters-LessThan.json',
            "segments" => [
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'variants.qty',
                    'owner'    => 'system',
                    'operator' => 'less than',
                    'value'    => '10'
                ])
            ],
            "index" => "products",
            "existing_query" => false
        ];
        yield "Greater than" => [
            "expected" => 'testSetSegmentFilters-GreaterThan.json',
            "segments" => [
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'variants.price_tiers.price',
                    'owner'    => 'system',
                    'operator' => 'greater than',
                    'value'    => '0'
                ])
            ],
            "index" => "products",
            "existing_query" => false
        ];
        yield "Lookup" => [
            "expected" => 'testSetSegmentFilters-Lookup.json',
            "segments" => [
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'variants.qty_availability.description',
                    'owner'    => 'system',
                    'operator' => 'lookup',
                    'value'    => 'a,b,c'
                ])
            ],
            "index" => "products",
            "existing_query" => false
        ];
        yield "Contains" => [
            "expected" => 'testSetSegmentFilters-Contains.json',
            "segments" => [
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'collection',
                    'owner'    => 'system',
                    'operator' => 'contains',
                    'value'    => 'bar'
                ])
            ],
            "index" => "products",
            "existing_query" => false
        ];
        yield "Not contains" => [
            "expected" => 'testSetSegmentFilters-NotContains.json',
            "segments" => [
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'collection',
                    'owner'    => 'system',
                    'operator' => 'not contains',
                    'value'    => 'baz'
                ])
            ],
            "index" => "products",
            "existing_query" => false
        ];

        // Combine operators
        yield "Equal to + greater than" => [
            "expected" => 'testSetSegmentFilters1.json',
            "segments" => [
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'variants.price_tiers.tier',
                    'owner'    => 'system',
                    'operator' => 'equal',
                    'value'    => 'foo'
                ]),
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'variants.price_tiers.price',
                    'owner'    => 'system',
                    'operator' => 'greater than',
                    'value'    => '0'
                ])
            ],
            "index" => "products",
            "existing_query" => false
        ];
        yield "Contains + greater than" => [
            "expected" => 'testSetSegmentFilters2.json',
            "segments" => [
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'collection',
                    'owner'    => 'system',
                    'operator' => 'contains',
                    'value'    => 'bar'
                ]),
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'variants.price_tiers.price',
                    'owner'    => 'system',
                    'operator' => 'greater than',
                    'value'    => '0'
                ])
            ],
            "index" => "products",
            "existing_query" => false
        ];
        yield "Does not contain + equal to + greater than" => [
            "expected" => 'testSetSegmentFilters3.json',
            "segments" => [
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'collection',
                    'owner'    => 'system',
                    'operator' => 'not contains',
                    'value'    => 'apparel'
                ]),
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'variants.price_tiers.tier',
                    'owner'    => 'system',
                    'operator' => 'equal',
                    'value'    => 'foo'
                ]),
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'variants.price_tiers.price',
                    'owner'    => 'system',
                    'operator' => 'greater than',
                    'value'    => '0'
                ])
            ],
            "index" => "products",
            "existing_query" => false
        ];
        yield "Lookup + greater than" => [
            "expected" => 'testSetSegmentFilters4.json',
            "segments" => [
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'variants.qty_availability.description',
                    'owner'    => 'system',
                    'operator' => 'lookup',
                    'value'    => 'a,b,c'
                ]),
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'variants.qty_availability.qty',
                    'owner'    => 'system',
                    'operator' => 'greater than',
                    'value'    => '0'
                ])
            ],
            "index" => "products",
            "existing_query" => false
        ];

        // Meta combinations (nested)
        yield "Meta equal to + greater than" => [
            "expected" => 'testSetSegmentFilters5.json',
            "segments" => [
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'meta.key',
                    'owner'    => 'system',
                    'operator' => 'equal',
                    'value'    => 'size'
                ]),
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'meta.value',
                    'owner'    => 'system',
                    'operator' => 'greater than',
                    'value'    => '0'
                ])
            ],
            "index" => "products",
            "existing_query" => false
        ];
        yield "User segment meta key/value + aggs using meta key/value" => [
            "expected" => 'testSetSegmentFilters6.json',
            "segments" => [
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'meta.key',
                    'owner'    => 'system',
                    'operator' => 'equal',
                    'value'    => 'bar'
                ]),
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'meta.value',
                    'owner'    => 'system',
                    'operator' => 'equal',
                    'value'    => 'user segment'
                ])
            ],
            "index" => "products",
            "existing_query" => '{
              "query": {
                "bool": {
                  "must": [],
                  "filter": [
                    {
                      "nested": {
                        "path": "meta",
                        "query": {
                          "bool": {
                            "filter": [
                              {
                                "term": {
                                  "meta.key": "foo"
                                }
                              },
                              {
                                "term": {
                                  "meta.value": "aggs value"
                                }
                              }
                            ]
                          }
                        }
                      }
                    }
                  ]
                }
              },
              "size": "10",
              "from": 0,
              "sort": {
                "_score": {
                  "order": "desc"
                }
              }
            }'
        ];
        yield "Meta key/value does not contain" => [
            "expected" => 'testSetSegmentFilters7.json',
            "segments" => [
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'meta.key',
                    'owner'    => 'system',
                    'operator' => 'not contains',
                    'value'    => 'demo'
                ]),
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'meta.value',
                    'owner'    => 'system',
                    'operator' => 'not contains',
                    'value'    => 'true'
                ])
            ],
            "index" => "products",
            "existing_query" => false
        ];
        yield "All segment operators" => [
            "expected" => 'testSetSegmentFilters-AllOperators.json',
            "segments" => [
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'meta.key',
                    'owner'    => 'system',
                    'operator' => 'equal',
                    'value'    => 'bar'
                ]),
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'meta.value',
                    'owner'    => 'system',
                    'operator' => 'contains',
                    'value'    => 'xyz'
                ]),
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'collection',
                    'owner'    => 'system',
                    'operator' => 'equal',
                    'value'    => 'test'
                ]),
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'variants.price',
                    'owner'    => 'system',
                    'operator' => 'greater than',
                    'value'    => '1'
                ]),
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'variants.qty',
                    'owner'    => 'system',
                    'operator' => 'less than',
                    'value'    => '1'
                ]),
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'tags',
                    'owner'    => 'system',
                    'operator' => 'not contains',
                    'value'    => '1'
                ]),
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'variants.price_tiers.tier',
                    'owner'    => 'system',
                    'operator' => 'equal',
                    'value'    => 'foo'
                ]),
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'variants.price_tiers.price',
                    'owner'    => 'system',
                    'operator' => 'equal',
                    'value'    => '1'
                ]),
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'vendor',
                    'owner'    => 'system',
                    'operator' => 'lookup',
                    'value'    => '1,2'
                ])

            ],
            "index" => "products",
            "existing_query" => '{
              "query": {
                "bool": {
                  "filter": [
                    {
                      "term": {
                        "collection": "apparel"
                      }
                    },
                    {
                      "term": {
                        "product_type": "accessories"
                      }
                    },
                    {
                      "nested": {
                        "path": "meta",
                        "query": {
                          "bool": {
                            "filter": [
                              {
                                "term": {
                                  "meta.key": "width_cm"
                                }
                              },
                              {
                                "term": {
                                  "meta.value": "20"
                                }
                              }
                            ]
                          }
                        }
                      }
                    },
                    {
                      "term": {
                        "channels.channel_id": "47"
                      }
                    },
                    {
                      "term": {
                        "client_id": 21
                      }
                    },
                    {
                      "term": {
                        "active": true
                      }
                    }
                  ],
                  "must": [
                    {
                      "exists": {
                        "field": "channels.channel_product_code"
                      }
                    }
                  ],
                  "must_not": [
                    {
                      "exists": {
                        "field": "vendor"
                      }
                    }
                  ]
                }
              }
            }'
        ];
    }

    /**
     * @dataProvider setSegmentFiltersNestedDataProvider
     * @param vo\SystemSegment[] $segments
     * @param array $expected
     * @return void
     */
    public function testSetSegmentFiltersNested(array $segments, array $expected)
    {
        $q = new search\ElasticsearchQuery();
        $q->setSegmentFilters($segments,'products');
        $test = $q->get();

        // Execute search to make sure there is nothing wrong with the query
        $es = new search\Elasticsearch();
        $results = $es->search($test, $es->aliasProducts);

        $this->assertEquals($expected, json_decode(json_encode($test), true));
    }

    public static function setSegmentFiltersNestedDataProvider(): \Generator
    {
        /**
         * Get documents where:
         * - meta.key does not contain 'b2b'
         */
        yield "Nested query in must_not clause with wildcard" => [
            "payload" => [
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'meta.key',
                    'owner'    => 'system',
                    'operator' => 'not contains',
                    'value'    => 'b2b'
                ])
            ],
            "expected" => [
                'aggs' => [],
                'query' => [
                    'bool' => [
                        'filter' =>[],
                        'must' => [],
                        'must_not' => [
                            [
                                'nested' => [
                                    'path' => 'meta',
                                    'query' => [
                                        'bool' => [
                                            'filter' => [
                                                [
                                                    'wildcard' => [
                                                        'meta.key' => '*b2b*',
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        /**
         * Get documents where:
         * - meta.key does not contain 'staff'
         * - and meta.value does not contain 'manager'
         * - and meta.key does not contain 'b2b'
         */
        // This should actually return two nested queries within the "must_not" clause
        // https://github.com/stock2shop/app/pull/2035
        yield "Nested query in must_not clause with multiple wildcards" => [
            "payload" => [
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'meta.key',
                    'owner'    => 'system',
                    'operator' => 'not contains',
                    'value'    => 'staff'
                ]),
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'meta.value',
                    'owner'    => 'system',
                    'operator' => 'not contains',
                    'value'    => 'manager'
                ]),
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'meta.key',
                    'owner'    => 'system',
                    'operator' => 'not contains',
                    'value'    => 'b2b'
                ])
            ],
            "expected" => [
                'aggs' => [],
                'query' => [
                    'bool' => [
                        'filter' => [],
                        'must' => [],
                        'must_not' => [
                            [
                                'nested' => [
                                    'path' => 'meta',
                                    'query' => [
                                        'bool' => [
                                            'filter' => [
                                                [
                                                    'wildcard' => [
                                                        'meta.key' => '*staff*'
                                                    ]
                                                ],
                                                [
                                                    'wildcard' => [
                                                        'meta.value' => '*manager*'
                                                    ]
                                                ],
                                                [
                                                    'wildcard' => [
                                                        'meta.key' => '*b2b*'
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        /**
         * Get documents where:
         * - meta.key equals 'b2b'
         * - and meta.value equals 'true'
         * - and meta.key does not contain 'staff'
         */
        yield "Nested query in must_not clause with wildcard, and nested query inside filter with multiple terms" => [
            "payload" => [
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'meta.key',
                    'owner'    => 'system',
                    'operator' => 'not contains',
                    'value'    => 'staff'
                ]),
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'meta.key',
                    'owner'    => 'system',
                    'operator' => 'equal',
                    'value'    => 'b2b'
                ]),
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'meta.value',
                    'owner'    => 'system',
                    'operator' => 'equal',
                    'value'    => 'true'
                ])
            ],
            "expected" => [
                'aggs' => [],
                'query' => [
                    'bool' => [
                        'filter' => [
                            [
                                'nested' => [
                                    'path' => 'meta',
                                    'query' => [
                                        'bool' => [
                                            'filter' => [
                                                [
                                                    'term' => [
                                                        'meta.key' => 'b2b'
                                                    ]
                                                ],
                                                [
                                                    'term' => [
                                                        'meta.value' => 'true'
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ],
                        'must' => [],
                        'must_not' => [
                            [
                                'nested' => [
                                    'path' => 'meta',
                                    'query' => [
                                        'bool' => [
                                            'filter' => [
                                                [
                                                    'wildcard' => [
                                                        'meta.key' => '*staff*'
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        /**
         * Get documents where:
         * - meta.key contains 'staff'
         * - and meta.value equals 'true'
         * - and meta.key does not contain 'staff'
         */
        yield "Nested query in must_not clause with wildcard, and nested query inside filter with wildcard and term" => [
            "payload" => [
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'meta.key',
                    'owner'    => 'system',
                    'operator' => 'contains',
                    'value'    => 'staff'
                ]),
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'meta.value',
                    'owner'    => 'system',
                    'operator' => 'equal',
                    'value'    => 'true'
                ]),
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'meta.key',
                    'owner'    => 'system',
                    'operator' => 'not contains',
                    'value'    => 'b2b'
                ])
            ],
            "expected" => [
                'aggs' => [],
                'query' => [
                    'bool' => [
                        'filter' => [
                            [
                                'nested' => [
                                    'path' => 'meta',
                                    'query' => [
                                        'bool' => [
                                            'filter' => [
                                                [
                                                    'wildcard' => [
                                                        'meta.key' => '*staff*'
                                                    ]
                                                ],
                                                [
                                                    'term' => [
                                                        'meta.value' => 'true'
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ],
                        'must' => [],
                        'must_not' => [
                            [
                                'nested' => [
                                    'path' => 'meta',
                                    'query' => [
                                        'bool' => [
                                            'filter' => [
                                                [
                                                    'wildcard' => [
                                                        'meta.key' => '*b2b*'
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        /**
         * Get documents where:
         * - meta.key equals 'channel_type'
         * - and meta.value equals 'b2b' or 'b2c' or 'b2e'
         */
        yield "Nested query inside filter with term and lookup" => [
            "payload" => [
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'meta.key',
                    'owner'    => 'system',
                    'operator' => 'equal',
                    'value'    => 'channel_type'
                ]),
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'meta.value',
                    'owner'    => 'system',
                    'operator' => 'lookup',
                    'value'    => 'b2b,b2c,b2e'
                ])
            ],
            "expected" => [
                'aggs' => [],
                'query' => [
                    'bool' => [
                        'filter' => [
                            [
                                'nested' => [
                                    'path' => 'meta',
                                    'query' => [
                                        'bool' => [
                                            'filter' => [
                                                [
                                                    'term' => [
                                                        'meta.key' => 'channel_type'
                                                    ]
                                                ],
                                                [
                                                    'terms' => [
                                                        'meta.value' => [
                                                            'b2b',
                                                            'b2c',
                                                            'b2e'
                                                        ]
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ],
                        'must' => [],
                        'must_not' => []
                    ]
                ]
            ]
        ];

        /**
         * Get documents where:
         * - meta.key equals 'supplier_count'
         * - and meta.value great than '2'
         */
        yield "Nested query inside filter with term and range (gt)" => [
            "payload" => [
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'meta.key',
                    'owner'    => 'system',
                    'operator' => 'equal',
                    'value'    => 'supplier_count'
                ]),
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'meta.value',
                    'owner'    => 'system',
                    'operator' => 'greater than',
                    'value'    => '2'
                ])
            ],
            "expected" => [
                'aggs' => [],
                'query' => [
                    'bool' => [
                        'filter' => [
                            [
                                'nested' => [
                                    'path' => 'meta',
                                    'query' => [
                                        'bool' => [
                                            'filter' => [
                                                [
                                                    'term' => [
                                                        'meta.key' => 'supplier_count'
                                                    ]
                                                ],
                                                [
                                                    'range' => [
                                                        'meta.value' => [
                                                            'gt' => '2'
                                                        ]
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ],
                        'must' => [],
                        'must_not' => []
                    ]
                ]
            ]
        ];

        /**
         * Get documents where:
         * - meta.key equals 'supplier_count'
         * - and meta.value greater than '5'
         * - and meta.value less than '10'
         */
        yield "Nested query inside filter with term and range (gt & lt)" => [
            "payload" => [
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'meta.key',
                    'owner'    => 'system',
                    'operator' => 'equal',
                    'value'    => 'supplier_count'
                ]),
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'meta.value',
                    'owner'    => 'system',
                    'operator' => 'greater than',
                    'value'    => '5'
                ]),
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'meta.value',
                    'owner'    => 'system',
                    'operator' => 'less than',
                    'value'    => '10'
                ])
            ],
            "expected" => [
                'aggs' => [],
                'query' => [
                    'bool' => [
                        'filter' => [
                            [
                                'nested' => [
                                    'path' => 'meta',
                                    'query' => [
                                        'bool' => [
                                            'filter' => [
                                                [
                                                    'term' => [
                                                        'meta.key' => 'supplier_count'
                                                    ]
                                                ],
                                                [
                                                    'range' => [
                                                        'meta.value' => [
                                                            'gt' => '5'
                                                        ]
                                                    ]
                                                ],
                                                [
                                                    'range' => [
                                                        'meta.value' => [
                                                            'lt' => '10'
                                                        ]
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ],
                        'must' => [],
                        'must_not' => []
                    ]
                ]
            ]
        ];
    }

    /**
     * @dataProvider setSegmentFiltersNonNestedDataProvider
     * @param vo\SystemSegment[] $segments
     * @param array $expected
     * @return void
     */
    public function testSetSegmentFiltersNonNested(array $segments, array $expected)
    {
        $q = new search\ElasticsearchQuery();
        $q->setSegmentFilters($segments,'products');
        $test = $q->get();

        // Execute search to make sure there is nothing wrong with the query
        $es = new search\Elasticsearch();
        $results = $es->search($test, $es->aliasProducts);

        $this->assertEquals($expected, json_decode(json_encode($test), true));
    }

    public static function setSegmentFiltersNonNestedDataProvider(): \Generator
    {
        /**
         * Get documents where:
         * - collection does not contain 'health'
         */
        yield "Query in must_not clause with wildcard" => [
            "payload" => [
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'collection',
                    'owner'    => 'system',
                    'operator' => 'not contains',
                    'value'    => 'health'
                ])
            ],
            "expected" => [
                'aggs' => [],
                'query' => [
                    'bool' => [
                        'filter' =>[],
                        'must' => [],
                        'must_not' => [
                            [
                                'wildcard' => [
                                    'collection' => '*health*'
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        /**
         * Get documents where:
         * - collection does not contain 'health'
         * - and product_type does not contain 'books'
         */
        yield "Query in must_not clause with multiple wildcards" => [
            "payload" => [
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'collection',
                    'owner'    => 'system',
                    'operator' => 'not contains',
                    'value'    => 'health'
                ]),
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'product_type',
                    'owner'    => 'system',
                    'operator' => 'not contains',
                    'value'    => 'books'
                ])
            ],
            "expected" => [
                'aggs' => [],
                'query' => [
                    'bool' => [
                        'filter' => [],
                        'must' => [],
                        'must_not' => [
                            [
                                'wildcard' => [
                                    'collection' => '*health*'
                                ]
                            ],
                            [
                                'wildcard' => [
                                    'product_type' => '*books*'
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        /**
         * Get documents where:
         * - product_type equals 'books'
         * - and collection does not contain 'health'
         */
        yield "Query in must_not clause with wildcard, and query inside filter with term" => [
            "payload" => [
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'collection',
                    'owner'    => 'system',
                    'operator' => 'not contains',
                    'value'    => 'health'
                ]),
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'product_type',
                    'owner'    => 'system',
                    'operator' => 'equal',
                    'value'    => 'books'
                ])
            ],
            "expected" => [
                'aggs' => [],
                'query' => [
                    'bool' => [
                        'filter' => [
                            [
                                'term' => [
                                    'product_type' => 'books'
                                ]
                            ]
                        ],
                        'must' => [],
                        'must_not' => [
                            [
                                'wildcard' => [
                                    'collection' => '*health*'
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        /**
         * Get documents where:
         * - collection contains 'health'
         * - and vendor equals 'nike'
         * - and product_type does not contain 'books'
         */
        yield "Query in must_not clause with wildcard, and query inside filter with wildcard and term" => [
            "payload" => [
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'collection',
                    'owner'    => 'system',
                    'operator' => 'contains',
                    'value'    => 'health'
                ]),
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'vendor',
                    'owner'    => 'system',
                    'operator' => 'equal',
                    'value'    => 'nike'
                ]),
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'product_type',
                    'owner'    => 'system',
                    'operator' => 'not contains',
                    'value'    => 'books'
                ])
            ],
            "expected" => [
                'aggs' => [],
                'query' => [
                    'bool' => [
                        'filter' => [
                            [
                                'wildcard' => [
                                    'collection' => '*health*'
                                ]
                            ],
                            [
                                'term' => [
                                    'vendor' => 'nike'
                                ]
                            ]
                        ],
                        'must' => [],
                        'must_not' => [
                            [
                                'wildcard' => [
                                    'product_type' => '*books*'
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        /**
         * Get documents where:
         * - product_type equals 'books'
         * - and collection equals 'health' or 'apparel' or 'electronics'
         */
        yield "Query inside filter with term and lookup" => [
            "payload" => [
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'product_type',
                    'owner'    => 'system',
                    'operator' => 'equal',
                    'value'    => 'books'
                ]),
                new vo\SystemSegment([
                    'type'     => 'products',
                    'key'      => 'collection',
                    'owner'    => 'system',
                    'operator' => 'lookup',
                    'value'    => 'health,apparel,electronics'
                ])
            ],
            "expected" => [
                'aggs' => [],
                'query' => [
                    'bool' => [
                        'filter' => [
                            [
                                'term' => [
                                    'product_type' => 'books'
                                ]
                            ],
                            [
                                'terms' => [
                                    'collection' => [
                                        'health',
                                        'apparel',
                                        'electronics'
                                    ]
                                ]
                            ]
                        ],
                        'must' => [],
                        'must_not' => []
                    ]
                ]
            ]
        ];
    }

    // test building console query
    public function testClientSearchQuery() {

        $payload = '
        {
          "aggs": {
            "channels": {
              "terms": {
                "field": "channels.channel_id",
                "order": {
                  "_term": "asc"
                }
              }
            },
            "collections": {
              "terms": {
                "field": "collection",
                "exclude": "",
                "order": {
                  "_term": "asc"
                },
                "size": 200
              }
            },
            "product_types": {
              "terms": {
                "field": "product_type",
                "exclude": "",
                "order": {
                  "_term": "asc"
                },
                "size": 200
              }
            },
            "vendors": {
              "terms": {
                "field": "vendor",
                "exclude": "",
                "order": {
                  "_term": "asc"
                },
                "size": 200
              }
            },
            "meta": {
              "nested": {
                "path": "meta"
              },
              "aggs": {
                "meta_keys": {
                  "terms": {
                    "field": "meta.key",
                    "exclude": "",
                    "order": {
                      "_term": "asc"
                    },
                    "size": 200
                  }
                }
              }
            }
          },
          "size": 10,
          "from": 0,
          "sort": {
            "_score": {
              "order": "desc"
            }
          }
        }
        ';

        $query = json_decode($payload);
        $q = new search\ElasticsearchQuery($query);
        $q->setTerm("client_id", 5);
        $q->setTerm("active", true);
        $limit = $q->getLimit();
        if(!$limit) {
            $q->setLimit(10);
        } elseif($limit > 100) {
            $q->setLimit(100);
        }
        $results = $q->get();
        $search = json_encode($results);
        $this->assertJson($search, "is json");

        // make sure original is matches resulting query
        // the original query will be missing properties as set by ElasticsearchQuery.
        $query->query = new \stdClass();
        $query->query->bool = new \stdClass();
        $query->query->bool->filter = array();
        $query->query->bool->must = array();
        $query->query->bool->must_not = array();
        $match = new \stdClass();
        $match->term = new \stdClass();
        $match->term->client_id = 5;
        $query->query->bool->filter[] = $match;
        $match = new \stdClass();
        $match->term = new \stdClass();
        $match->term->active = true;
        $query->query->bool->filter[] = $match;
        $this->assertJsonStringEqualsJsonString($search, json_encode($query), "Client search query format");
    }



}
