<?php
namespace tests\v1\stock2shop\dal\channels\shopify;

use tests;
use stock2shop\dal\channels\shopify\ShopifyMetaField;

class ShopifyMetaFieldTest extends tests\TestCase
{
    public function testCreateFromChannelMetaWithValidPrefix()
    {
        $row   = ['meta_value' => 'abc'];
        $key   = 'shopify_product_meta_namespace.key';
        $value = 'meta_value';

        $metaField = ShopifyMetaField::createFromChannelMeta(
            $row,
            ShopifyMetaField::CHANNEL_META_PRODUCT_PREFIX,
            $key,
            $value
        );

        $this->assertInstanceOf(ShopifyMetaField::class, $metaField);
        $this->assertEquals('namespace', $metaField->namespace);
        $this->assertEquals('key', $metaField->key);
        $this->assertEquals('abc', $metaField->value);
    }

    public function testCreateFromChannelMetaWithInvalidFieldName()
    {
        $row   = ['meta_value' => 'abc'];
        $key   = 'shopify_product_meta_namespace.key';
        $value = 'variantmeta_value';

        $metaField = ShopifyMetaField::createFromChannelMeta(
            $row,
            ShopifyMetaField::CHANNEL_META_PRODUCT_PREFIX,
            $key,
            $value
        );

        $this->assertNotInstanceOf(ShopifyMetaField::class, $metaField);
        $this->assertNull($metaField);

    }

    public function testCreateFromChannelMetaWithInvalidPrefix()
    {
        $row   = ['title' => 'test_title'];
        $key   = 'invalid_prefix_namespace.key';
        $value = 'some_value';

        $metaField = ShopifyMetaField::createFromChannelMeta($row, $key, $value, '');

        $this->assertNotInstanceOf(ShopifyMetaField::class, $metaField);
        $this->assertNull($metaField);
    }

    public function testCreateFromChannelMetaWithInvalidKeyFormat()
    {
        $row   = ['collection' => 'test_collection'];
        $key   = 'shopify_product_meta_invalidkeyformat';
        $value = 'collection';

        $metaField = ShopifyMetaField::createFromChannelMeta(
            $row,
            ShopifyMetaField::CHANNEL_META_PRODUCT_PREFIX,
            $key,
            $value
        );

        $this->assertNotInstanceOf(ShopifyMetaField::class, $metaField);
        $this->assertNull($metaField);
    }

    public function testCreateFromChannelMetaWithInvalidValue()
    {
        $row   = ['meta_value' => 'abc'];
        $key   = 'shopify_product_meta_namespace.key';
        $value = 'invalid_value';

        $metaField = ShopifyMetaField::createFromChannelMeta(
            $row,
            ShopifyMetaField::CHANNEL_META_PRODUCT_PREFIX,
            $key,
            $value
        );

        $this->assertNotInstanceOf(ShopifyMetaField::class, $metaField);
        $this->assertNull($metaField);
    }
}