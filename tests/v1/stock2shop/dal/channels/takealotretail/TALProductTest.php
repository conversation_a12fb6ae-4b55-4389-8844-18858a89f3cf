<?php

namespace tests\v1\stock2shop\dal\channels\takealotretail;

use tests;
use stock2shop\dal\channels\takealotretail;
use stock2shop\vo;

class TALProductTest extends tests\TestCase
{
    public function testGetBatch()
    {
        /** @var  vo\ChannelProduct[] $cp */
        $cp = [
            new vo\ChannelProduct([
                'id' => 1,
                'active' => true,
                'meta' => [
                    [
                        'key' => 'dept',
                        'value' => '123'
                    ]
                ],
                'variants' => [
                    [
                        'id' => 1,
                        'active' => true,
                        'barcode' => '1234567891011',
                        'qty' => 1
                    ],
                    [
                        'id' => 2,
                        'active' => true,
                        'barcode' => '',
                        'qty' => 1
                    ],
                    [
                        'id' => 3,
                        'active' => false,
                        'barcode' => '1111111111111',
                        'qty' => 10
                    ]
                ]
            ]),
            new vo\ChannelProduct([
                'id' => 2,
                'active' => false,
                'meta' => [
                    [
                        'key' => 'dept',
                        'value' => '456'
                    ]
                ],
                'variants' => [
                    [
                        'id' => 4,
                        'active' => true,
                        'barcode' => '1234567891012',
                        'qty' => 1
                    ]
                ]
            ])
        ];
        $template = '{
           "barcode": "{{variant.barcode}}",
           "stock": "{{variant.qty}}",
           "supplier_department_id": "{{meta.dept}}"
        }';
        $prods = takealotretail\TALProduct::getBatch($cp, $template);
        $this->assertCount(3, $prods);
        $this->assertEquals('1234567891011', $prods[0]->barcode);
        $this->assertEquals(1, $prods[0]->stock);
        $this->assertEquals('1111111111111', $prods[1]->barcode);
        $this->assertEquals(0, $prods[1]->stock);
        $this->assertEquals('1234567891012', $prods[2]->barcode);
        $this->assertEquals(0, $prods[2]->stock);
        $this->assertEquals('123', $prods[0]->supplier_department_id);
        $this->assertEquals('123', $prods[1]->supplier_department_id);
        $this->assertEquals('456', $prods[2]->supplier_department_id);
    }

    /**
     * @dataProvider hasValidBarcodeDataProvider
     * @param string $barcode
     * @param bool $expected
     * @return void
     */
    public function testHasValidBarcode(array $barcodes, bool $expected)
    {
        foreach ($barcodes as $barcode) {
            $data = [
                'barcode' => $barcode,
                'stock' => '',
                'supplier_department_id' => ''
            ];
            $tal = new takealotretail\TALProduct($data);
            $this->assertEquals($expected, $tal->hasValidBarcode());
        }
    }

    public static function hasValidBarcodeDataProvider(): \Generator
    {
        yield 'Valid barcodes' => [
            [
                'ABC123',
                'AB C12-3',
                'B**********123456789',
                'C1_22',
                '**********'
            ],
            true
        ];

        yield 'Invalid barcodes' => [
            [
                '',
                '0',
                0,
                false,
                null,
                12345
            ],
            false
        ];
    }
}
