<?php

namespace tests\v1\stock2shop\dal\channels\takealotretail;

use tests;
use stock2shop\dal\channels\takealotretail;
use stock2shop\vo;

class talGetBatchTest extends tests\TestCase
{
    public function testMarkSuccess()
    {
        /** @var  vo\ChannelProduct[] $cp */
        $cp       = [
            new vo\ChannelProduct([
                'id'       => 1,
                'active'   => true,
                'variants' => [
                    [
                        'id'      => 1,
                        'active'  => true,
                        'barcode' => '1234567891011',
                        'qty'     => 1
                    ],
                    [
                        'id'      => 3,
                        'active'  => false,
                        'barcode' => '*************',
                        'qty'     => 10
                    ]
                ]
            ]),
            new vo\ChannelProduct([
                'id'       => 2,
                'active'   => false,
                'variants' => [
                    [
                        'id'      => 4,
                        'active'  => true,
                        'barcode' => '1234567891012',
                        'qty'     => 1
                    ]
                ]
            ])
        ];
        $batch    = new takealotretail\TALGetBatch(
            [
                'count'        => 3,
                'complete'     => 2,
                'failed'       => 1,
                'status'       => 'Complete',
                'date_created' => '1234',
                'products'     => [
                    [
                        "barcode"                => "1234567891012",
                        "supplier_department_id" => 11063,
                        "stock"                  => 357,
                        "status"                 => "Complete",
                        "validation_error"       => null
                    ],
                    [
                        "barcode" => "1234567891011",
                        "supplier_department_id" => 11063,
                        "stock"                  => 12,
                        "status"                 => "Complete",
                        "validation_error"       => null
                    ],
                    [
                        "barcode"          => "*************",
                        "supplier_department_id" => 11063,
                        "stock"                  => 1,
                        "status"                 => "Failed",
                        "validation_error" => [
                            "code"    => 2,
                            "message" => "Supplier department x not linked to portal account"
                        ]
                    ]
                ],
            ]
        );
        $template = '{
           "barcode": "{{variant.barcode}}",
           "stock": "{{variant.qty}}",
           "supplier_department_id": "{{meta.dept}}"
        }';
        takealotretail\TALGetBatch::markSuccess($cp, $batch, $template);
        $this->assertTrue($cp[0]->success);
        $this->assertTrue($cp[1]->success);
        $this->assertTrue($cp[0]->variants[0]->success);
        $this->assertFalse($cp[0]->variants[1]->success);
        $this->assertTrue($cp[1]->variants[0]->success);
        $this->assertEquals($cp[0]->id, $cp[0]->channel_product_code);
        $this->assertEquals($cp[1]->id, $cp[1]->channel_product_code);
        $this->assertEquals($cp[0]->variants[0]->barcode, $cp[0]->variants[0]->channel_variant_code);
        $this->assertNotEquals($cp[0]->variants[1]->barcode, $cp[0]->variants[1]->channel_variant_code);
        $this->assertEquals($cp[1]->variants[0]->barcode, $cp[1]->variants[0]->channel_variant_code);
    }


}
