{
  {{# variant}}
  "weight": "{{# calculate}}{{variant.grams}} / 1000 {{/ calculate}}",
  {{/ variant}}
  "attributes": {
    "product_collection": "{{collection}}",
    "dimensions": "{{meta.dimensions.value}}",
    "weight": "{{# calculate}}{{variant.grams}} / 1000 {{/ calculate}}",
    "grouped_qty": "{{# calculate}}{{variant.qty_availability.jhb.qty}} + {{variant.qty_availability.cpt.qty}}{{/ calculate}}"
  },
  "group_price": [
    {
      "cust_group": 4,
      "price": "{{variant.price_tiers.wholesale.price}}",
      "website_id": 0
    }
  ]
}