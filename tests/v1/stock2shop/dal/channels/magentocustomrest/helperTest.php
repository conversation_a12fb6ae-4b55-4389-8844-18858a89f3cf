<?php
namespace tests\v1\stock2shop\dal\channels\magentocustomrest;

use tests;
use stock2shop\dal\channels\magentocustomrest;
use stock2shop\dal\system;
use stock2shop\lib;
use stock2shop\exceptions;
use RedBean_Facade as R;

class helperTest extends tests\TestCase
{
    public function testS2sProductToMagento() {
        R::exec("delete from channel where description like 'testS2sProductToMagento%';");

        // Stub app
        $this->client_id = 21;

        /** @var \RedBean_OODBBean|\stock2shop\dal\system\client $this->client */
        $this->client = R::load("client", $this->client_id);
        if (!lib\Utils::found($this->client)) {
            throw new exceptions\ResourceNotFound("client_id $this->client_id does not exist");
        }
        $_SERVER["REQUEST_METHOD"] = "GET";
        $_SERVER["REMOTE_ADDR"] = "*******";
        $_SERVER["REQUEST_URI"] = "stock2shop";
        $this->app = new \Slim\Slim();
        $this->appName = microtime(true);
        global $config;
        $config["appName"] = $this->appName;
        $this->app->setName($this->appName);
        $this->env = $this->app->environment();
        $this->env["client"] = $this->client;
        $this->env["SCRIPT_NAME"] = "/v1";
        $this->env["session"] = system\session::getUserSession("admin_$this->client_id");

        // data to use
        $productJSON = file_get_contents($config['baseDir'] . '/../../tests/v1/stock2shop/dal/channels/magentocustomrest/helperTestProduct.json');
        $productMapJSON = file_get_contents($config['baseDir'] . '/../../tests/v1/stock2shop/dal/channels/magentocustomrest/helperTestProductMap.json');

        // setup channel with correct meta data
        /** @var \RedBean_OODBBean|\stock2shop\dal\system\channel $channel */
        $channel = R::dispense("channel");
        $channel->client_id = $this->client_id;
        $channel->description = "testS2sProductToMagento" . time();
        $channel->type = "magentocustomrest";
        $channel->active = true;
        R::store($channel);
        $this->env["channel"] = $channel;

        // meta
        $options = new \stdClass();
        $options->channel_id = $channel->id;
        $options->key = "attribute_set";
        $options->value = "4";
        $options->client_id = $this->client_id;
        system\channelmeta::touchChannelMeta($options);
        $options = new \stdClass();
        $options->channel_id = $channel->id;
        $options->key = "tax_class_id";
        $options->value = "1";
        $options->client_id = $this->client_id;
        system\channelmeta::touchChannelMeta($options);
        $options = new \stdClass();
        $options->channel_id = $channel->id;
        $options->key = "product_map_template";
        $options->value = $productMapJSON;
        $options->client_id = $this->client_id;
        system\channelmeta::touchChannelMeta($options);

        // test
        $helper = new magentocustomrest\Helper();
        $product = json_decode($productJSON);
        $magentoProduct = $helper::s2sProductToMagento($product);

        $this->assertEquals("100", $magentoProduct->attributes->dimensions, "Calculated meta");
        $this->assertEquals(0.1, $magentoProduct->attributes->weight, "Calculated weight");
        $this->assertEquals("880.00", $magentoProduct->group_price[0]->price, "Price tier");
        $this->assertEquals(150, $magentoProduct->attributes->grouped_qty, "Qty availability");

        // assert

    }



}
