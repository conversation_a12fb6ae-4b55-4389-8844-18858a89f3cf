<?php

namespace v1\stock2shop\dal\channels\takealot;

use Generator;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Middleware;
use GuzzleHttp\Psr7\Response;
use GuzzleHttp;
use Psr\Http\Message\ResponseInterface;
use ReflectionException;
use stock2shop\dal\channels\takealot\Config;
use stock2shop\dal\oauth2\Constants;
use stock2shop\dal\oauth2\RateLimitRetryMiddleware;
use tests;
use stock2shop\dal\channels\takealot;
use tests\Utils;

class ClientTest extends tests\TestCase
{
    /**
     * @dataProvider testRetryDataProvider
     * @throws ReflectionException
     */
    public function testRetry(array $responses, int $status_code)
    {
        $mock         = new MockHandler($responses);
        $handlerStack = HandlerStack::create($mock);
        $container    = [];
        $history      = Middleware::history($container);
        $handlerStack->push($history);
        $handlerStack->push(new RateLimitRetryMiddleware(
            takealot\Config::RETRY_RESPONSE_CODES,
            Constants::DEFAULT_RETRY_LIMIT,
            Constants::DEFAULT_RETRY_DELAY_MS
        ));
        Config::setClient(new Guzzlehttp\Client([
            'handler' => $handlerStack,
        ]));

        $object = new takealot\Client();
        /** @var ResponseInterface $resp */
        $resp = Utils::invokePrivateMethod(
            $object,
            'processRequest',
            [
                'GET',
                'https://takealot.com?sales?page_size=100&page_number=1&filters=order_id:1234',
                []
            ]
        );
        $this->assertEquals($status_code, $resp->getBody()->getContents());
    }

    public function testRetryDataProvider(): Generator
    {
        yield '429' => [
            'response'    => [
                new Response(429, ['Content-Type' => 'application/json'], json_encode([])),
                new Response(200, ['Content-Type' => 'application/json'], json_encode(429))
            ],
            'status_code' => 429
        ];
        yield '502' => [
            'response'    => [
                new Response(502, ['Content-Type' => 'application/json'], json_encode([502])),
                new Response(200, ['Content-Type' => 'application/json'], json_encode(502))
            ],
            'status_code' => 502
        ];
        yield '503' => [
            'response'    => [
                new Response(503, ['Content-Type' => 'application/json'], json_encode([503])),
                new Response(200, ['Content-Type' => 'application/json'], json_encode(503))
            ],
            'status_code' => 503
        ];
        yield '504' => [
            'response'    => [
                new Response(504, ['Content-Type' => 'application/json'], json_encode([504])),
                new Response(200, ['Content-Type' => 'application/json'], json_encode(504))
            ],
            'status_code' => 504
        ];
        yield '200' => [
            'response'    => [
                new Response(200, ['Content-Type' => 'application/json'], json_encode(200))
            ],
            'status_code' => 200
        ];
    }

}
