<?php
namespace tests\v1\stock2shop\dal\channels\takealot;

use GuzzleHttp\Client;
use tests;
use stock2shop\dal\channels\takealot;
use RedBean_Facade as R;
use tests\Utils;

class orderTransformTest extends tests\TestCase
{
    public function testTransform() {
        // Stub app
        global $config;
        $client_id = 21;
        $_SERVER["REQUEST_METHOD"] = "GET";
        $_SERVER["REMOTE_ADDR"] = "*******";
        $_SERVER["REQUEST_URI"] = "stock2shop";
        $app = new \Slim\Slim();
        $appName = microtime(true);
        $config["appName"] = $appName;
        $app->setName($appName);
        $env = $app->environment();
        $env["client"] = R::load('client', $client_id);

        // setup TAL channel
        /** @var \RedBean_OODBBean|\stock2shop\dal\system\channel $channel */
        $channel = R::dispense("channel");
        $channel->client_id = $client_id;
        $channel->description = "testTakealotOrder";
        $channel->type = "takealot";
        $channel->active = true;
        R::store($channel);

        $env["channel"] = R::load('channel', $channel->id);

        // New lead time order
        $path = $config['baseDir'] . "/../../tests/v1/stock2shop/dal/channels/takealot/orderTransformTestData/orderTransformTestWebhook.json";
        $file = file_get_contents($path);
        $payload = json_decode($file);

        $order_transform = new takealot\OrderTransform();
        $order = $order_transform->transform($payload);

        $this->assertCount(5,$order->system_order->line_items, "5 line items in order");
        $this->assertObjectHasAttribute('system_order', $order, 'order transformed');

        // Check if Address has been parsed for the leadtime order
        $this->assertEquals("Block B, Montague Park Business Estate", $order->system_order->shipping_address->address1);
        $this->assertEquals("Topaz Boulevard, Montague Gardens", $order->system_order->shipping_address->address2);
        $this->assertEquals("Cape Town", $order->system_order->shipping_address->city);
        $this->assertEquals("Western Cape", $order->system_order->shipping_address->province);
        $this->assertEquals("7441", $order->system_order->shipping_address->zip);
        $this->assertEquals("ZA", $order->system_order->shipping_address->country_code);
        $this->assertEquals("South Africa", $order->system_order->shipping_address->country);
        $this->assertEquals("Takealot", $order->system_order->shipping_address->company);

        // Sale Status Changed
        $path = $config['baseDir'] . "/../../tests/v1/stock2shop/dal/channels/takealot/orderTransformTestData/orderTransformTestWebhookSaleChange.json";
        $file = file_get_contents($path);
        $payload2 = json_decode($file);

        $order_transform = new takealot\OrderTransform();
        $order = $order_transform->transform($payload);

        $this->assertObjectHasAttribute('system_order', $order, 'order transformed');


        // remove test channel
        R::trash($channel);

    }

}
