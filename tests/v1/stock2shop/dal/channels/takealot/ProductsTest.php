<?php
namespace tests\v1\stock2shop\dal\channels\takealot;

use tests;
use stock2shop\dal\channels\takealot;

class productsTest extends tests\TestCase
{
    public function testTransformOffer() {

        $product = new \stdClass();
        $product->active = true;
        $product->meta = new \stdClass();
        $product->meta->lead_time = "6";
        $product->variant = new \stdClass();
        $product->variant->active = true;
        $product->variant->sku = 'foo';
        $product->variant->qty = -3;
        $product->variant->price = "169";
        $product->variant->price_tiers = array(
            "retail" =>  "123",
            "wholesale"=> "100"
        );

        $map = '{
            "selling_price": {{variant.price}},
            "rrp": {{variant.price_tiers.retail}},
            "leadtime_days": {{meta.lead_time}},
            "leadtime_stock": [
                {
                    "merchant_warehouse_id": 78853,
                    "quantity": {{variant.qty}}
                }
            ],
            "sku": "{{variant.sku}}"
        }';

        $talProducts = new takealot\Products();
        $offer = $talProducts->transformOffer($product, $map);
        $this->assertEquals('Re-enable', $offer->status_action, 'status set');
        $this->assertEquals(169.00, $offer->selling_price, 'price set');
        $this->assertEquals(0, $offer->rrp, 'rrp set');
        $this->assertEquals(6, $offer->leadtime_days, 'leadtime_days set');
        $this->assertEquals(78853, $offer->leadtime_stock[0]['merchant_warehouse_id'], 'merchant_warehouse_id set');
        $this->assertEquals(0, $offer->leadtime_stock[0]['quantity'], 'leadtime_stock set');
        $this->assertEquals('foo', $offer->sku, 'sku set');

        $product->variant->active = false;
        $product->variant->price = "10";
        $product->variant->qty = "10";
        $offer = $talProducts->transformOffer($product, $map);
        $this->assertEquals('Disable', $offer->status_action, 'status set');
        $this->assertEquals(123.00, $offer->rrp, 'rrp set');
        $this->assertEquals(10.00, $offer->selling_price, 'price set');
        $this->assertEquals(10, $offer->leadtime_stock[0]['quantity'], 'leadtime_stock set');

        // if price is 0 and rrp is set, set price to rrp and remove rrp.
        $product->variant->price = "0";
        $offer = $talProducts->transformOffer($product, $map);
        $this->assertEquals(123, $offer->selling_price, 'rrp set');
        $this->assertEquals(0, $offer->rrp, 'rrp set');
    }



}
