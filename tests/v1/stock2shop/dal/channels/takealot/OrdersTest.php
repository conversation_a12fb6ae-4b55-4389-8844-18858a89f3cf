<?php

namespace tests\v1\stock2shop\dal\channels\takealot;

use Exception;
use GuzzleHttp;
use RedBean_Facade as R;
use stock2shop\dal\channels\takealot\Config;
use stock2shop\dal\channels\takealot\Orders;
use stock2shop\vo;
use tests;

class OrdersTest extends tests\TestCase
{
    const CLIENT_ID = 1;
    const CHANNEL_ID_1 = 2;
    const CHANNEL_ID_2 = 3;
    const SOURCE_ID = 4;

    const CONFIG = [
        'client_id' => self::CLIENT_ID,
        'sources'   => [
            [
                'id'   => self::SOURCE_ID,
                'type' => 'test_source',
                'meta' => [
                    [
                        'key'   => 'create_order_enabled',
                        'value' => false
                    ]
                ]
            ]
        ],
        'channels'  => [
            [
                'id'          => self::CHANNEL_ID_1,
                'description' => 'takealot_1',
                'type'        => 'takealot',
                'meta'        => [
                    [
                        'key'   => 'key',
                        'value' => 'asdf'
                    ]
                ]
            ],
            [
                'id'          => self::CHANNEL_ID_2,
                'description' => 'takealot_2',
                'type'        => 'takealot',
                'meta'        => [
                    [
                        'key'   => 'key',
                        'value' => 'zxcv'
                    ]
                ]
            ]
        ]
    ];

    /**
     * @throws Exception
     */
    public function testGetOrderById()
    {
        tests\DB::makeFromConfig(new vo\Config(self::CONFIG));
        // channel 1
        $channel_1_mock         = new GuzzleHttp\Handler\MockHandler([
            new GuzzleHttp\Psr7\Response(
                200,
                ['Content-Type' => 'application/json'],
                file_get_contents(__DIR__ . '/mocks/fetch_order_1_response.json')
            ),
            new GuzzleHttp\Psr7\Response(
                200,
                ['Content-Type' => 'application/json'],
                file_get_contents(__DIR__ . '/mocks/fetch_order_1_response_empty.json')
            ),
            new GuzzleHttp\Psr7\Response(
                200,
                ['Content-Type' => 'application/json'],
                file_get_contents(__DIR__ . '/mocks/fetch_order_2_response.json')
            ),
            new GuzzleHttp\Psr7\Response(
                200,
                ['Content-Type' => 'application/json'],
                file_get_contents(__DIR__ . '/mocks/fetch_order_2_response_empty.json')
            )
        ]);
        $handlerStack = GuzzleHttp\HandlerStack::create($channel_1_mock);
        $container    = [];
        $history      = Guzzlehttp\Middleware::history($container);
        $handlerStack->push($history);
        Config::setChannel(self::CHANNEL_ID_1);
        Config::setClient(new GuzzleHttp\Client([
            'handler' => $handlerStack,
        ]));

        $orders = new Orders();

        $orders->getOrderById(1);
        $headers = $container[0]['request']->getHeaders();
        $this->assertEquals('Key asdf', $headers['Authorization'][0]);
        $orders->getOrderById(2);
        $headers = $container[1]['request']->getHeaders();
        $this->assertEquals('Key asdf', $headers['Authorization'][0]);

        // channel 2

        $channel_2_mock         = new GuzzleHttp\Handler\MockHandler([
            new GuzzleHttp\Psr7\Response(
                200,
                ['Content-Type' => 'application/json'],
                file_get_contents(__DIR__ . '/mocks/fetch_order_3_response.json')
            ),
            new GuzzleHttp\Psr7\Response(
                200,
                ['Content-Type' => 'application/json'],
                file_get_contents(__DIR__ . '/mocks/fetch_order_3_response_empty.json')
            ),
            new GuzzleHttp\Psr7\Response(
                200,
                ['Content-Type' => 'application/json'],
                file_get_contents(__DIR__ . '/mocks/fetch_order_4_response.json')
            ),
            new GuzzleHttp\Psr7\Response(
                200,
                ['Content-Type' => 'application/json'],
                file_get_contents(__DIR__ . '/mocks/fetch_order_4_response_empty.json')
            )
        ]);
        $handlerStack = GuzzleHttp\HandlerStack::create($channel_2_mock);
        $container    = [];
        $history      = Guzzlehttp\Middleware::history($container);
        $handlerStack->push($history);
        Config::setChannel(self::CHANNEL_ID_2);
        Config::setClient(new GuzzleHttp\Client([
            'handler' => $handlerStack,
        ]));

        $orders->getOrderById(3);
        $headers = $container[0]['request']->getHeaders();
        $this->assertEquals('Key zxcv', $headers['Authorization'][0]);
        $orders->getOrderById(4);
        $headers = $container[1]['request']->getHeaders();
        $this->assertEquals('Key zxcv', $headers['Authorization'][0]);

    }
}