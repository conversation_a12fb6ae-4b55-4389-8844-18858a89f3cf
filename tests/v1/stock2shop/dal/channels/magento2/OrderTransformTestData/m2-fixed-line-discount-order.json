{"billing_address": {"firstname": "<PERSON>", "lastname": "<PERSON><PERSON><PERSON>", "company": "Stock2Shop", "city": "Cape Town", "postcode": "7806", "country_id": "ZA", "telephone": "0218135866", "email": "<EMAIL>", "address_type": "billing", "street": "21 Victoria Avenue\nThe Village", "quote_address_id": "31"}, "customer": [], "line_items": [{"id": "2", "image": "https://magento.test/static/version1698139427/webapi_rest/_view/en_US/Magento_Catalog/images/product/placeholder/image.jpg", "name": "Gaming Mouse Pad - Black", "price": 300, "price_with_discount": 200, "price_with_discount_and_tax": 245, "price_with_tax": 345, "qty": 1, "sku": "GMP-1", "tax_rate": 15, "url": "https://magento.test/gaming-mouse-pad-black.html"}, {"id": "3", "image": "https://magento.test/static/version1698139427/webapi_rest/_view/en_US/Magento_Catalog/images/product/placeholder/image.jpg", "name": "Gaming Mouse Pad - Red", "price": 300, "price_with_discount": 200, "price_with_discount_and_tax": 245, "price_with_tax": 345, "qty": 2, "sku": "GMP-2", "tax_rate": 15, "url": "https://magento.test/gaming-mouse-pad-red.html"}], "payment": {"method": "checkmo", "additional_information": {"method_title": "Check / Money order"}, "amount_ordered": 705, "base_amount_ordered": 705, "shipping_amount": 15, "base_shipping_amount": 15}, "shipping_address": {"firstname": "<PERSON>", "lastname": "<PERSON><PERSON><PERSON>", "company": "Stock2Shop", "city": "Cape Town", "postcode": "7806", "country_id": "ZA", "telephone": "0218135866", "email": "<EMAIL>", "address_type": "shipping", "street": "21 Victoria Avenue\nThe Village", "quote_address_id": "30"}, "visitor": {"http_user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "remote_addr": "**********"}, "applied_rule_ids": "2", "base_currency_code": "ZAR", "base_discount_amount": -300, "base_grand_total": 705, "base_discount_tax_compensation_amount": 0, "base_shipping_amount": 15, "base_shipping_discount_amount": 0, "base_shipping_discount_tax_compensation_amnt": 0, "base_shipping_incl_tax": 15, "base_shipping_tax_amount": 0, "base_subtotal": 900, "base_subtotal_incl_tax": 1035, "base_tax_amount": 90, "base_total_due": 705, "base_to_global_rate": 1, "base_to_order_rate": 1, "coupon_code": "fixedcoupon", "customer_email": "<EMAIL>", "customer_firstname": "<PERSON>", "customer_group_id": 0, "customer_is_guest": 1, "customer_lastname": "<PERSON><PERSON><PERSON>", "customer_note_notify": 1, "discount_amount": -300, "discount_description": "fixedcoupon", "global_currency_code": "ZAR", "grand_total": 705, "discount_tax_compensation_amount": 0, "increment_id": "*********", "is_virtual": 0, "order_currency_code": "ZAR", "quote_id": "11", "remote_ip": "**********", "shipping_amount": 15, "shipping_description": "Flat Rate - Fixed", "shipping_discount_amount": 0, "shipping_discount_tax_compensation_amount": 0, "shipping_incl_tax": 15, "shipping_tax_amount": 0, "store_currency_code": "ZAR", "store_id": 1, "store_to_base_rate": 0, "store_to_order_rate": 0, "subtotal": 900, "subtotal_incl_tax": 1035, "tax_amount": 90, "total_due": 705, "total_qty_ordered": 3, "weight": 0, "shipping_method": "flatrate_flatrate", "coupon_rule_name": "Fixed discount", "state": "new", "status": "pending", "store_name": "Main Website\nMain Website Store\nDefault Store View", "total_item_count": 2, "entity_id": "9", "id": "9", "created_at": "2023-10-26 12:31:48", "updated_at": "2023-10-26 12:31:48"}