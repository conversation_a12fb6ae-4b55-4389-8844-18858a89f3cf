{"billing_address": {"firstname": "<PERSON>", "lastname": "<PERSON><PERSON><PERSON>", "company": "Stock2Shop", "city": "Cape Town", "postcode": "7806", "country_id": "ZA", "telephone": "0218135866", "email": "<EMAIL>", "address_type": "billing", "street": "21 Victoria Avenue\nThe Village", "quote_address_id": "25"}, "customer": [], "line_items": [{"id": "3", "image": "https://magento.test/static/version1698139427/webapi_rest/_view/en_US/Magento_Catalog/images/product/placeholder/image.jpg", "name": "Gaming Mouse Pad - Red", "price": 300, "price_with_discount": 270, "price_with_discount_and_tax": 315, "price_with_tax": 345, "qty": 1, "sku": "GMP-2", "tax_rate": 15, "url": "https://magento.test/gaming-mouse-pad-red.html"}, {"id": "2", "image": "https://magento.test/static/version1698139427/webapi_rest/_view/en_US/Magento_Catalog/images/product/placeholder/image.jpg", "name": "Gaming Mouse Pad - Black", "price": 300, "price_with_discount": 270, "price_with_discount_and_tax": 315, "price_with_tax": 345, "qty": 1, "sku": "GMP-1", "tax_rate": 15, "url": "https://magento.test/gaming-mouse-pad-black.html"}], "payment": {"method": "checkmo", "additional_information": {"method_title": "Check / Money order"}, "amount_ordered": 631, "base_amount_ordered": 631, "shipping_amount": 10, "base_shipping_amount": 10}, "shipping_address": {"firstname": "<PERSON>", "lastname": "<PERSON><PERSON><PERSON>", "company": "Stock2Shop", "city": "Cape Town", "postcode": "7806", "country_id": "ZA", "telephone": "0218135866", "email": "<EMAIL>", "address_type": "shipping", "street": "21 Victoria Avenue\nThe Village", "quote_address_id": "24"}, "visitor": {"http_user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "remote_addr": "**********"}, "applied_rule_ids": "1", "base_currency_code": "ZAR", "base_discount_amount": -60, "base_grand_total": 631, "base_discount_tax_compensation_amount": 0, "base_shipping_amount": 10, "base_shipping_discount_amount": 0, "base_shipping_discount_tax_compensation_amnt": 0, "base_shipping_incl_tax": 10, "base_shipping_tax_amount": 0, "base_subtotal": 600, "base_subtotal_incl_tax": 690, "base_tax_amount": 81, "base_total_due": 631, "base_to_global_rate": 1, "base_to_order_rate": 1, "coupon_code": "PERCENTCOUPON", "customer_email": "<EMAIL>", "customer_firstname": "<PERSON>", "customer_group_id": 0, "customer_is_guest": 1, "customer_lastname": "<PERSON><PERSON><PERSON>", "customer_note_notify": 1, "discount_amount": -60, "discount_description": "PERCENTCOUPON", "global_currency_code": "ZAR", "grand_total": 631, "discount_tax_compensation_amount": 0, "increment_id": "*********", "is_virtual": 0, "order_currency_code": "ZAR", "quote_id": "9", "remote_ip": "**********", "shipping_amount": 10, "shipping_description": "Flat Rate - Fixed", "shipping_discount_amount": 0, "shipping_discount_tax_compensation_amount": 0, "shipping_incl_tax": 10, "shipping_tax_amount": 0, "store_currency_code": "ZAR", "store_id": 1, "store_to_base_rate": 0, "store_to_order_rate": 0, "subtotal": 600, "subtotal_incl_tax": 690, "tax_amount": 81, "total_due": 631, "total_qty_ordered": 2, "weight": 0, "shipping_method": "flatrate_flatrate", "coupon_rule_name": "Percent discount", "state": "new", "status": "pending", "store_name": "Main Website\nMain Website Store\nDefault Store View", "total_item_count": 2, "entity_id": "7", "id": "7", "created_at": "2023-10-26 09:57:40", "updated_at": "2023-10-26 09:57:40"}