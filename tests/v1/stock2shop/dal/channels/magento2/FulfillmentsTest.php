<?php
namespace tests\v1\stock2shop\dal\channels\magento2;

use stock2shop\exceptions\Validation;
use tests;
use stock2shop\dal\channels\magento2;
use stock2shop\dal\channel;
use stock2shop\lib;
use stock2shop\dal\system;
use RedBean_Facade as R;

class fulfillmentsTest extends tests\TestCase
{
    public function testSyncFulfillments() {
        global $config;
        $client_id = 21;
        $entity_id = 872;
        $customer_id = 950;

        // Stub app
        $_SERVER["REQUEST_METHOD"] = "GET";
        $_SERVER["REMOTE_ADDR"] = "1.2.3.4";
        $_SERVER["REQUEST_URI"] = "stock2shop";
        $app = new \Slim\Slim();
        $appName = microtime(true);
        $config["appName"] = $appName;
        $app->setName($appName);
        $env = $app->environment();
        $env["client"] = R::load('client', $client_id);

        // Don't write to stdout
        lib\LogWriter::setupApp();

        // this test requires a order to be in the database
        // TODO rewrite test to create order from scratch

        // cleanup
        R::exec("delete from channel where description like 'testMagento2SyncFulfillments%';");
        R::exec("delete from `order` where `notes` like 'testMagento2SyncFulfillments%'");
        R::exec("delete from ordermeta where `key`='entity_id'");

        // create channel and order and ordermeta
        /** @var \RedBean_OODBBean|\stock2shop\dal\system\channel $channel */
        $channel = R::dispense("channel");
        $channel->client_id = $client_id;
        $channel->description = "testMagento2SyncFulfillments" . time();
        $channel->type = "magento2";
        $channel->active = true;
        R::store($channel);

        R::exec("
            insert into channelmeta (`key`, value, channel_id, client_id) values (?, ?, ?, ?)",
            [
                'url',
                'https://pnpclothing.getmelive.co.za/rest/all/V1/',
                $channel->id,
                $client_id,
            ]
        );

        // create order and line items
        /** @var \RedBean_OODBBean|\stock2shop\dal\system\order $order */
        $order =  R::dispense("order");
        $order->notes = "testMagento2SyncFulfillments" . time();
        $order->client_id = $client_id;
        $order->customer_id = $customer_id;
        R::store($order);

        // fulfillment
        $s2s_fulfillment = '[
          {
            "order_id": '.$order->id.',
            "state": "pending",
            "status": "Awaiting collection",
            "tracking_number": "",
            "tracking_company": "s2s",
            "tracking_url": "http://track.com"
          }]
        ';

        $magentoFulfillment = new magento2\Fulfillments();
        magento2\Config::set('url', 'https://pnpclothing.getmelive.co.za/rest/all/V1/');

        // fulfillment without entity_ids, make sure it is logged
        $response = $magentoFulfillment->syncFulfillments($channel->id, $s2s_fulfillment);
        $logDir = $config["aws"]["cloudwatchlogs"]["logDir"];
        $path =  "$logDir/app.$appName.log";
        $logRow = lib\Utils::tail($path);
        $log = json_decode($logRow, true);
        $this->assertEquals(
            'Magento2 sync_channel_fulfillments failed. Missing entity_id order param',
            $log['message'],
            'missing entity_id'
        );

        // Add in entity id for order
        // order meta
        /** @var \RedBean_OODBBean|\stock2shop\dal\system\ordermeta $ordermeta */
        $ordermeta = R::dispense("ordermeta");
        $ordermeta->client_id = $client_id;
        $ordermeta->order_id = $order->id;
        $ordermeta->key = "entity_id";
        $ordermeta->value = $entity_id;
        R::store($ordermeta);

        // this updates comments on existing order
        $fulfillments = $magentoFulfillment->syncFulfillments($channel->id, $s2s_fulfillment);
        $this->assertObjectHasAttribute("channel_synced", $fulfillments[0], 'channel_synced date');

        // create a fulfillment which has not yet been created on channel
        $ordermeta->value = 1;
        R::store($ordermeta);
        $fulfillments = $magentoFulfillment->syncFulfillments($channel->id, $s2s_fulfillment);

        // this should not sync since there is no tracking code
        $this->assertFalse(isset($fulfillments[0]->channel_synced), 'channel_synced date not set');

        // Try again with a valid track
        $s2s_fulfillment = '[
          {
            "order_id": '.$order->id.',
            "state": "pending",
            "status": "Awaiting collection",
            "tracking_number": "123",
            "tracking_company": "s2s",
            "tracking_url": "http://track.com"
          }]
        ';
        $fulfillments = $magentoFulfillment->syncFulfillments($channel->id, $s2s_fulfillment);
        $this->assertObjectHasAttribute("channel_synced", $fulfillments[0], 'channel_synced date');
    }
}
