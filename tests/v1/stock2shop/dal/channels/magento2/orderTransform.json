{"billing_address": {"entity_id": "1786", "parent_id": "893", "customer_address_id": "587", "region_id": "0", "customer_id": "624", "region": "Gauteng", "postcode": "0157", "lastname": "<PERSON><PERSON><PERSON>", "street": "103 Roscommon Rd, Bronberrik", "city": "Centurion", "email": "<EMAIL>", "telephone": "************", "country_id": "ZA", "firstname": "<PERSON><PERSON>", "address_type": "billing", "company": "Shooting Stuff"}, "customer": {"entity_id": "624", "website_id": "1", "email": "<EMAIL>", "group_id": "5", "increment_id": "*********", "store_id": "0", "created_at": "2017-11-30 06:27:38", "updated_at": "2017-11-30 06:27:38", "is_active": "1", "disable_auto_group_change": "0", "created_in": "shootingstuff.co.za", "firstname": "<PERSON><PERSON>", "lastname": "<PERSON><PERSON><PERSON>", "password_hash": "630a42506beffbaab6c76f14a62cfe09f4a4ea6daba69a2ad0100a668c4aaae3:4Ln0UHavLqY151UbmOAgslO4DvRWaHQz:1", "gender": "1", "failures_num": "0"}, "line_items": [{"id": "2143", "image": "http://*************/media/catalog/product/cache/image/5d5ea6ab71588aa3aba773a353a047c5/c/z/cz-shadow2-9k(20180718).jpg", "name": "CZ Shadow 2 9x19 Comp Pistol (black, blue grips)", "price": 17456.14, "price_with_discount": 17456.14, "price_with_discount_and_tax": 19900, "price_with_tax": 19900, "qty": 1, "sku": "CZ-Shadow2-9k", "tax_rate": 14.000002291457, "url": "http://*************/cz-shadow-2-9x19-competition-pistol-black-with-blue-grips.html"}, {"id": "1614", "image": "http://*************/media/catalog/product/cache/image/5d5ea6ab71588aa3aba773a353a047c5/s/i/sierra_1560.jpg", "name": "Sierra 6mm Cal / .243\" 100gr SBT GameKing", "price": 614.04, "price_with_discount": 614.04, "price_with_discount_and_tax": 700, "price_with_tax": 700, "qty": 1, "sku": "SRA-1560", "tax_rate": 13.999088007296, "url": "http://*************/sierra-6mm-cal-243-100gr-gameking.html"}, {"id": "2867", "image": "http://*************/media/catalog/product/cache/image/5d5ea6ab71588aa3aba773a353a047c5/r/e/redding-80189-_01_.jpg", "name": "Redding Full Length (straight-wall) Steel 3-Die Set - 10/40", "price": 872.81, "price_with_discount": 872.81, "price_with_discount_and_tax": 995, "price_with_tax": 995, "qty": 1, "sku": "RED-80253", "tax_rate": 13.999610453592, "url": "http://*************/redding-full-length-straight-wall-steel-3-die-set-10-40.html"}, {"id": "1452", "image": "http://*************/media/catalog/product/cache/image/5d5ea6ab71588aa3aba773a353a047c5/f/r/frnt_cmj_147_135_124_115gr_rn_9mm_macro_1_4.jpg", "name": "Frontier CMJ Bullets - 9mmP 124gr RN [100]", "price": 118.42, "price_with_discount": 118.42, "price_with_discount_and_tax": 135, "price_with_tax": 135, "qty": 1, "sku": "FMP-J9mmP124RNa", "tax_rate": 14.************, "url": "http://*************/frontier-cmj-bullets-9mm-124gr-rn-sample-pack-of-100.html"}], "payment": {"entity_id": "893", "parent_id": "893", "base_shipping_captured": "0.0000", "shipping_captured": "0.0000", "base_amount_paid": "20245.0000", "amount_paid": "20245.0000", "method": "banktransfer", "additional_information": {"instructions": "Kindly make your payment to:\r\n•\tBank: Standard Bank\r\n•\tAccount #: *********\r\n•\tBranch: Centurion\r\n•\tBranch Code: ********\r\n\r\nUse your Order Number as reference and send proof of payment to: <EMAIL>."}}, "shipping_address": {"entity_id": "1785", "parent_id": "893", "customer_address_id": "587", "region_id": "0", "customer_id": "624", "region": "Gauteng", "postcode": "0157", "lastname": "<PERSON><PERSON><PERSON>", "street": "103 Roscommon Rd, Bronberrik", "city": "Centurion", "email": "<EMAIL>", "telephone": "************", "country_id": "ZA", "firstname": "<PERSON><PERSON>", "address_type": "shipping", "company": "Shooting Stuff"}, "visitor": {"http_user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/68.0.3440.106 Safari/537.36", "remote_addr": "*************"}, "entity_id": "893", "state": "processing", "status": "processing", "protect_code": "ce505f", "shipping_description": "Collect - Self-collect or Own Courier", "is_virtual": "0", "store_id": "1", "customer_id": "624", "base_discount_amount": "0.0000", "base_discount_invoiced": "0.0000", "base_grand_total": "20245.0000", "base_shipping_amount": "0.0000", "base_shipping_invoiced": "0.0000", "base_shipping_tax_amount": "0.0000", "base_subtotal": "17758.7800", "base_subtotal_invoiced": "17758.7800", "base_tax_amount": "2486.2200", "base_tax_invoiced": "2486.2200", "base_to_global_rate": "1.0000", "base_to_order_rate": "1.0000", "base_total_invoiced": "20245.0000", "base_total_invoiced_cost": "0.0000", "base_total_paid": "20245.0000", "base_total_qty_ordered": "4.0000", "discount_amount": "0.0000", "discount_invoiced": "0.0000", "grand_total": "20245.0000", "shipping_amount": "0.0000", "shipping_invoiced": "0.0000", "shipping_tax_amount": "0.0000", "store_to_base_rate": "1.0000", "store_to_order_rate": "1.0000", "subtotal": "17758.7800", "subtotal_invoiced": "17758.7800", "tax_amount": "2486.2200", "tax_invoiced": "2486.2200", "total_invoiced": "20245.0000", "total_paid": "20245.0000", "total_qty_ordered": "4.0000", "customer_is_guest": "0", "customer_note_notify": "0", "billing_address_id": "1786", "customer_group_id": "5", "send_email": "1", "quote_id": "4478", "shipping_address_id": "1785", "base_shipping_discount_amount": "0.0000", "base_subtotal_incl_tax": "20245.0000", "base_total_due": "0.0000", "shipping_discount_amount": "0.0000", "subtotal_incl_tax": "20245.0000", "total_due": "0.0000", "weight": "-76.2000", "increment_id": "*********", "base_currency_code": "ZAR", "customer_email": "<EMAIL>", "customer_firstname": "<PERSON><PERSON>", "customer_lastname": "<PERSON><PERSON><PERSON>", "global_currency_code": "ZAR", "hold_before_state": "processing", "hold_before_status": "processing", "order_currency_code": "ZAR", "shipping_method": "tablerate_tablerate", "store_currency_code": "ZAR", "store_name": "shootingstuff.co.za\r\nShooting St", "created_at": "2017-11-30 08:43:22", "updated_at": "2018-09-11 10:21:11", "total_item_count": "4", "customer_gender": "1", "discount_tax_compensation_invoiced": "0.0000", "base_discount_tax_compensation_invoiced": "0.0000", "shipping_incl_tax": "0.0000", "base_shipping_incl_tax": "0.0000", "paypal_ipn_customer_notified": "0", "ebizmarts_abandonedcart_flag": "0", "mageworx_fee_amount": "0.0000", "base_mageworx_fee_amount": "0.0000", "mageworx_fee_tax_amount": "0.0000", "base_mageworx_fee_tax_amount": "0.0000"}