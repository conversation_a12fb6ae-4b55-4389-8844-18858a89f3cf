<?php
namespace tests\v1\stock2shop\dal\channels\magento2;

use Exception;
use stock2shop\dal\channels\magento2;
use stock2shop\dal\system;
use stock2shop\repository;
use stock2shop\vo;
use tests;

class HelperTest extends tests\TestCase
{
    const CHANNEL_ID = 1;
    const CLIENT_ID = 1;
    const SOURCE_ID = 1;
    private $config = [
        "client_id" => self::CLIENT_ID,
        "channels"  => [
            [
                "id"      => self::CHANNEL_ID,
                "type"    => "trade",
                "sources" => [
                    [
                        "id" => self::SOURCE_ID
                    ]
                ]
            ]
        ],
        "sources"   => [
            [
                "id"    => self::SOURCE_ID,
                "type"  => "sageone",
                "flags" => []
            ]
        ]
    ];

    /**
     * @throws Exception
     */
    public function testSerialiseProductData() {
        $config = new vo\Config($this->config);
        tests\DB::makeFromConfig($config);

        tests\Utils::stubApp();
        $productPatches = tests\DB::makeProducts($config, 1, self::SOURCE_ID);
        $productPatchesRepository = new repository\ProductPatchesRepository(
            $config,
            self::SOURCE_ID,
            $productPatches->patches
        );
        $productPatchesRepository->save();

        $products = system\product::populateProductsFast(
            [
                $productPatches->patches['source_product_code-0']->results->id
            ],
            [
                "user_id" => 0,
                "channel_id" => self::CHANNEL_ID,
                "filter_active_images" => false,
                "filter_active_variants" => false,
                "filter_channel_variants" => false
            ]
        );
        $products = json_decode(json_encode($products));

        $helper = new magento2\Helper();
        $transformed_product = $helper->serialiseProductData($products[0]);
        $this->assertEquals(1.00, $transformed_product->variant->price_tiers->{'tier-0'}, 'Check price_tiers added');
        $this->assertEquals(1, $transformed_product->variant->qty_availability->{'description-0'}, 'Check qty_availability added');
        $this->assertEquals('size', $transformed_product->variant->option1_name, 'Check option1 name');
    }



}
