<?php
namespace tests\v1\stock2shop\dal\channels\magento2;

use tests;
use stock2shop\dal\channels\magento2;
use stock2shop\dal\system;
use stock2shop\lib;
use RedBean_Facade as R;

class orderTransformTest extends tests\TestCase
{
    public function testTransform() {
        R::exec("delete from channel where description like 'testMagento2Order%';");

        // Stub app
        $this->client_id = 21;

        /** @var \RedBean_OODBBean|\stock2shop\dal\system\client $this->client */
        $this->client = R::load("client", $this->client_id);
        $_SERVER["REQUEST_METHOD"] = "GET";
        $_SERVER["REMOTE_ADDR"] = "*******";
        $_SERVER["REQUEST_URI"] = "stock2shop";
        $this->app = new \Slim\Slim();
        $this->appName = microtime(true);
        global $config;
        $config["appName"] = $this->appName;
        $this->app->setName($this->appName);
        $this->env = $this->app->environment();
        $this->env["client"] = $this->client;
        $this->env["SCRIPT_NAME"] = "/v1";
        $this->env["session"] = system\session::getUserSession("admin_$this->client_id");
        // setup channel with correct meta data
        /** @var \RedBean_OODBBean|\stock2shop\dal\system\channel $channel */
        $channel = R::dispense("channel");
        $channel->client_id = $this->client_id;
        $channel->description = "testMagento2Order" . time();
        $channel->type = "magento2";
        $channel->active = true;
        R::store($channel);
        $this->env["channel"] = $channel;
        $orderJSON = file_get_contents($config['baseDir'] . '/../../tests/v1/stock2shop/dal/channels/magento2/orderTransform.json');
        $orderIn = json_decode($orderJSON);
        $orderTransform = new magento2\OrderTransform();
        $orderOut = $orderTransform->transform($orderIn);
        $this->assertEquals("add_order", $orderOut->system_order->instruction, "add order");
    }



}
