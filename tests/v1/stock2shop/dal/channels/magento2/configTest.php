<?php

namespace tests\v1\stock2shop\dal\channels\magento2;

use Exception;
use stock2shop\dal\channels\magento2\Config;
use stock2shop\vo;
use tests;

class configTest extends tests\TestCase
{
    const CLIENT_ID = 1;
    const SOURCE_ID = 2;
    const CHANNEL_ID_1 = 3;
    const CHANNEL_ID_2 = 4;

    const CONFIG = [
        'client_id' => self::CLIENT_ID,
        'sources'   => [
            [
                'id'   => self::SOURCE_ID,
                'type' => 'test_source',
            ]
        ],
        'channels'  => [
            [
                'id'   => self::CHANNEL_ID_1,
                'type' => 'magento2',
                'meta' => [
                    [
                        'key'   => 'test_meta_key',
                        'value' => 'asdf'
                    ],
                    [
                        'key' => 'token',
                        'value' => 'yyy'
                    ]
                ]
            ],
            [
                'id'   => self::CHANNEL_ID_2,
                'type' => 'magento2',
                'meta' => [
                    [
                        'key'   => 'test_meta_key',
                        'value' => 'zxcv'
                    ]
                ]
            ]
        ],
    ];

    /**
     * @return void
     * @throws Exception
     */
    public function testSetSource()
    {
        tests\DB::makeFromConfig(new vo\Config(self::CONFIG));

        $this->assertEquals('xxx', Config::get('token'));
        Config::setChannel(self::CHANNEL_ID_1);
        $this->assertEquals('asdf', Config::get('test_meta_key'));
        $this->assertEquals('yyy', Config::get('token'));
        Config::setChannel(self::CHANNEL_ID_2);
        $this->assertEquals('zxcv', Config::get('test_meta_key'));

        // confirm token value has been reset back to default
        $this->assertEquals('xxx', Config::get('token'));
    }


}
