<?php

namespace tests\v1\stock2shop\dal\channels\woocommerce;

use RedBean_Facade as R;
use stock2shop\dal\channels\woocommerce;
use stock2shop\dal\system\channelmeta;
use tests;

/**
 * Helper Test
 */
final class HelperTest extends tests\TestCase
{
    /**
     * Test Handle Update Error
     * @return void
     */
    public function testHandleUpdateError()
    {
        // Channel description.
        $description = 'woocommerceTestUpdateWooError';

        // Cleanup.
        $this->cleanUp($description);

        // Setup app.
        $app = $this->setupApp();
        $env = $app->environment();

        // Client.
        $client_id  = 21;
        $client = R::load('client', $client_id);
        $env['client']  = $client;

        // Channel
        /** @var \RedBean_OODBBean|\stock2shop\dal\system\channel $channel */
        $channel = R::dispense('channel');
        $channel->client_id   = $client_id;
        $channel->description = $description;
        $channel->type        = 'woocommerce';
        $channel->active      = true;
        R::store($channel);
        $env['channel'] = $channel;

        tests\Utils::addChannelMeta([
            'create_product_enabled' => 'false'
        ], $channel);

        // Invalid product id exception with create_product_enabled = false
        $exception = new \Exception('ERROR: woocommerce_api_invalid_product_id', 500);
        $helper = new woocommerce\Helper();
        $helper->handleUpdateError($exception);

        // Same exception with additional chars added.
        $exception = new \Exception('ERROR: woocommerce_api_invalid_product_id xyz', 500);
        $helper = new woocommerce\Helper();
        $helper->handleUpdateError($exception);

        // Other exception with create_product_enabled = false.
        $exception = new \Exception('ERROR: woocommerce_rest_product_exists', 500);
        $exceptionThrown = false;
        try {
            $helper->handleUpdateError($exception);
        } catch (\Exception $e) {
            $this->assertEquals('ERROR: woocommerce_rest_product_exists', $e->getMessage());
            $exceptionThrown = true;
        }
        $this->assertTrue($exceptionThrown);

        // Invalid product id exception with create_product_enabled = true.
        R::exec(
            "UPDATE channelmeta set value='true' where `key` = 'create_product_enabled' and channel_id=?",
            [$channel->id]
        );
        $exception = new \Exception('ERROR: woocommerce_api_invalid_product_id', 500);
        $exceptionThrown = false;
        try {
            $helper->handleUpdateError($exception);
        } catch (\Exception $e) {
            $this->assertEquals('ERROR: woocommerce_api_invalid_product_id', $e->getMessage());
            $exceptionThrown = true;
        }
        $this->assertFalse($exceptionThrown);

        // Other exception with create_product_enabled = true
        $exception = new \Exception('ERROR: woocommerce_rest_product_exists', 500);
        $exceptionThrown = false;
        try {
            $helper->handleUpdateError($exception);
        } catch (\Exception $e) {
            $this->assertEquals('ERROR: woocommerce_rest_product_exists', $e->getMessage());
            $exceptionThrown = true;
        }
        $this->assertTrue($exceptionThrown);

        // Invalid product id exception with create_product_enabled missing
        R::exec(
            "DELETE FROM channelmeta where `key` = 'create_product_enabled' and channel_id=?",
            [$channel->id]
        );
        $exception = new \Exception('ERROR: woocommerce_api_invalid_product_id', 500);
        $exceptionThrown = false;
        try {
            $helper->handleUpdateError($exception);
        } catch (\Exception $e) {
            $this->assertEquals('ERROR: woocommerce_api_invalid_product_id', $e->getMessage());
            $exceptionThrown = true;
        }
        $this->assertFalse($exceptionThrown);

        // Other exception with create_product_enabled missing
        $exception = new \Exception('ERROR: woocommerce_rest_product_exists', 500);
        $exceptionThrown = false;
        try {
            $helper->handleUpdateError($exception);
        } catch (\Exception $e) {
            $this->assertEquals('ERROR: woocommerce_rest_product_exists', $e->getMessage());
            $exceptionThrown = true;
        }
        $this->assertTrue($exceptionThrown);

        // Cleanup test data.
        $this->cleanUp($description);
    }

    /**
     * Cleanup
     *
     * Removes the channel data for this test.
     *
     * @return void
     */
    private function cleanUp(string $description)
    {
        $channels = R::getAll("select * from channel where description like '%" . $description . "%'");
        foreach ($channels as $channelDelete) {
            $id = $channelDelete['id'];
            R::exec('delete from channel where id = ?;', [$id]);
            R::exec('delete from channelmeta where channel_id = ?;', [$id]);
        }
    }

    /**
     * Setup App
     *
     * This method configures the Slim app object in memory
     * and adds the client and channel data to the
     * application environment.
     *
     * @return \Slim\Slim $app
     */
    private function setupApp(): \Slim\Slim
    {
        // Mock app.
        global $config;
        $_SERVER['REQUEST_METHOD'] = 'GET';
        $_SERVER['REMOTE_ADDR']    = '*******';
        $_SERVER['REQUEST_URI']    = 'stock2shop';
        $app = new \Slim\Slim();
        $app->setName($config['appName']);
        $env = $app->environment();
        return $app;
    }

    /**
     * @dataProvider setWooProductAttributesDataProvider
     *
     * Test Set Woo Product Attributes
     * @return void
     */
    public function testSetWooProductAttributes($s2sProduct, $WooProduct, $result)
    {
        // Call the setWooProductAttributes method
        woocommerce\Helper::setWooProductAttributes($s2sProduct, $WooProduct);

        // Assert that the attributes have been correctly set on the product
        $this->assertEquals($s2sProduct, $result);
    }

    public static function setWooProductAttributesDataProvider(): \Generator
    {
        yield 'with attributes on WooProduct' => [
            (object)[
                "attributes" => [
                    [
                        "name" => "instock_jhb",
                        "slug" => "instock_jhb",
                        "visible" => false,
                        "variation" => false,
                        "options" => [
                            ""
                        ]
                    ],
                    [
                        "name" => "instock_cpt",
                        "slug" => "instock_cpt",
                        "visible" => false,
                        "variation" => false,
                        "options" => [
                            "true"
                        ]
                    ]
                ]
            ],
            (object)[
                "attributes" => [
                    [
                        "name" => "instock_jhb",
                        "slug" => "instock_jhb",
                        "position" => 0,
                        "visible" => false,
                        "variation" => false,
                        "options" => [
                            "boo"
                        ]
                    ],
                    [
                        "name" => "instock_cpt",
                        "slug" => "instock_cpt",
                        "position" => 1,
                        "visible" => false,
                        "variation" => false,
                        "options" => [
                            "true"
                        ]
                    ]
                    ,
                    [
                        "name" => "test",
                        "slug" => "test",
                        "position" => 2,
                        "visible" => false,
                        "variation" => false,
                        "options" => [
                            "works"
                        ]
                    ]
                ]
            ],
            (object)[
                "attributes" => [
                    [
                        "name" => "instock_jhb",
                        "slug" => "instock_jhb",
                        "visible" => false,
                        "variation" => false,
                        "options" => [
                            ""
                        ]
                    ],
                    [
                        "name" => "instock_cpt",
                        "slug" => "instock_cpt",
                        "visible" => false,
                        "variation" => false,
                        "options" => [
                            "true"
                        ]
                    ],
                    [
                        "name" => "test",
                        "slug" => "test",
                        "visible" => false,
                        "variation" => false,
                        "position" => 2,
                        "options" => [
                            "works"
                        ]
                    ]

                ]
            ]
        ];
        yield 'with no attributes on WooProduct' => [
            (object)[
                "attributes" => [
                    [
                        "name" => "instock_jhb",
                        "slug" => "instock_jhb",
                        "visible" => false,
                        "variation" => false,
                        "options" => [
                            ""
                        ]
                    ],
                    [
                        "name" => "instock_cpt",
                        "slug" => "instock_cpt",
                        "visible" => false,
                        "variation" => false,
                        "options" => [
                            "true"
                        ]
                    ]
                ]
            ],
            (object)[
                "attributes" => []
            ],
            (object)[
                "attributes" => [
                    [
                        "name" => "instock_jhb",
                        "slug" => "instock_jhb",
                        "visible" => false,
                        "variation" => false,
                        "options" => [
                            ""
                        ]
                    ],
                    [
                        "name" => "instock_cpt",
                        "slug" => "instock_cpt",
                        "visible" => false,
                        "variation" => false,
                        "options" => [
                            "true"
                        ]
                    ]
                ]
            ]
        ];
        yield 'with no attributes on S2SProduct' => [
            (object)[
                "attributes" => []
            ],
            (object)[
                "attributes" => [
                    [
                        "name" => "instock_jhb",
                        "slug" => "instock_jhb",
                        "position" => 0,
                        "visible" => false,
                        "variation" => false,
                        "options" => [
                            "boo"
                        ]
                    ],
                    [
                        "name" => "instock_cpt",
                        "slug" => "instock_cpt",
                        "position" => 1,
                        "visible" => false,
                        "variation" => false,
                        "options" => [
                            "true"
                        ]
                    ]
                    ,
                    [
                        "name" => "test",
                        "slug" => "test",
                        "position" => 2,
                        "visible" => false,
                        "variation" => false,
                        "options" => [
                            "works"
                        ]
                    ]
                ]
            ],
            (object)[
                "attributes" => [
                    [
                        "name" => "instock_jhb",
                        "slug" => "instock_jhb",
                        "position" => 0,
                        "visible" => false,
                        "variation" => false,
                        "options" => [
                            "boo"
                        ]
                    ],
                    [
                        "name" => "instock_cpt",
                        "slug" => "instock_cpt",
                        "position" => 1,
                        "visible" => false,
                        "variation" => false,
                        "options" => [
                            "true"
                        ]
                    ]
                    ,
                    [
                        "name" => "test",
                        "slug" => "test",
                        "position" => 2,
                        "visible" => false,
                        "variation" => false,
                        "options" => [
                            "works"
                        ]
                    ]
                ]
            ]
        ];
        yield 'with no attributes property on S2SProduct' => [
            (object)[],
            (object)[
                "attributes" => [
                    [
                        "name" => "instock_jhb",
                        "slug" => "instock_jhb",
                        "position" => 0,
                        "visible" => false,
                        "variation" => false,
                        "options" => [
                            "boo"
                        ]
                    ],
                    [
                        "name" => "instock_cpt",
                        "slug" => "instock_cpt",
                        "position" => 1,
                        "visible" => false,
                        "variation" => false,
                        "options" => [
                            "true"
                        ]
                    ]
                    ,
                    [
                        "name" => "test",
                        "slug" => "test",
                        "position" => 2,
                        "visible" => false,
                        "variation" => false,
                        "options" => [
                            "works"
                        ]
                    ]
                ]
            ],
            (object)[
                "attributes" => [
                    [
                        "name" => "instock_jhb",
                        "slug" => "instock_jhb",
                        "position" => 0,
                        "visible" => false,
                        "variation" => false,
                        "options" => [
                            "boo"
                        ]
                    ],
                    [
                        "name" => "instock_cpt",
                        "slug" => "instock_cpt",
                        "position" => 1,
                        "visible" => false,
                        "variation" => false,
                        "options" => [
                            "true"
                        ]
                    ]
                    ,
                    [
                        "name" => "test",
                        "slug" => "test",
                        "position" => 2,
                        "visible" => false,
                        "variation" => false,
                        "options" => [
                            "works"
                        ]
                    ]
                ]
            ]
        ];
    }
}