{"id": 362, "parent_id": 0, "status": "processing", "currency": "USD", "version": "8.0.3", "prices_include_tax": true, "date_created": "2023-09-05T11:42:03", "date_modified": "2023-09-05T11:42:34", "discount_total": "299.80", "discount_tax": "0.00", "shipping_total": "0.00", "shipping_tax": "0.00", "cart_tax": "0.00", "total": "2698.20", "total_tax": "0.00", "customer_id": 1, "order_key": "wc_order_GFBrOFA8iRYwy", "billing": {"first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "company": "", "address_1": "1 sample street", "address_2": "<PERSON><PERSON>", "city": "Hout Bay", "state": "WC", "postcode": "7806", "country": "ZA", "email": "<EMAIL>", "phone": ""}, "shipping": {"first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "company": "", "address_1": "1 sample street", "address_2": "<PERSON><PERSON>", "city": "Hout Bay", "state": "WC", "postcode": "7806", "country": "ZA", "phone": ""}, "payment_method": "cod", "payment_method_title": "Cash on delivery", "transaction_id": "", "customer_ip_address": "**********", "customer_user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "created_via": "store-api", "customer_note": "", "date_completed": null, "date_paid": null, "cart_hash": "5562100805ab86648066ce91d28d832c", "number": "362", "meta_data": [{"id": 5093, "key": "_shipping_hash", "value": "9d4568c009d203ab10e33ea9953a0264"}, {"id": 5094, "key": "_coupons_hash", "value": "009d4be25021754333d309925e6d76c3"}, {"id": 5095, "key": "_fees_hash", "value": "d751713988987e9331980363e24189ce"}, {"id": 5096, "key": "_taxes_hash", "value": "d751713988987e9331980363e24189ce"}, {"id": 5097, "key": "is_vat_exempt", "value": "no"}], "line_items": [{"id": 55, "name": "Call of Duty: Modern Warfare II - PS4", "product_id": 238, "variation_id": 239, "quantity": 2, "tax_class": "", "subtotal": "2998.00", "subtotal_tax": "0.00", "total": "2698.20", "total_tax": "0.00", "taxes": [], "meta_data": [{"id": 383, "key": "selection", "value": "PS4", "display_key": "Selection", "display_value": "PS4"}, {"id": 402, "key": "_reduced_stock", "value": "2", "display_key": "_reduced_stock", "display_value": "2"}], "sku": "testSetInclusivePricing", "price": 1349.1, "image": {"id": 344, "src": "http://host.stock2shop.test:8888/wp-content/uploads/2023/08/82309caa72a63b05e2be7d4033301855-1.jpg"}, "parent_name": "Call of Duty: Modern Warfare II"}], "tax_lines": [], "shipping_lines": [], "fee_lines": [], "coupon_lines": [{"id": 59, "code": "testtotal", "discount": "299.8", "discount_tax": "0", "meta_data": [{"id": 401, "key": "coupon_data", "value": {"id": 351, "code": "testtotal", "amount": "10", "status": "publish", "date_created": {"date": "2023-09-04 07:34:32.000000", "timezone_type": 1, "timezone": "+00:00"}, "date_modified": {"date": "2023-09-05 10:59:07.000000", "timezone_type": 1, "timezone": "+00:00"}, "date_expires": null, "discount_type": "percent", "description": "", "usage_count": 4, "individual_use": false, "product_ids": [], "excluded_product_ids": [], "usage_limit": 0, "usage_limit_per_user": 0, "limit_usage_to_x_items": null, "free_shipping": false, "product_categories": [], "excluded_product_categories": [], "exclude_sale_items": false, "minimum_amount": "", "maximum_amount": "", "email_restrictions": [], "virtual": false, "meta_data": []}, "display_key": "coupon_data", "display_value": {"id": 351, "code": "testtotal", "amount": "10", "status": "publish", "date_created": {"date": "2023-09-04 07:34:32.000000", "timezone_type": 1, "timezone": "+00:00"}, "date_modified": {"date": "2023-09-05 10:59:07.000000", "timezone_type": 1, "timezone": "+00:00"}, "date_expires": null, "discount_type": "percent", "description": "", "usage_count": 4, "individual_use": false, "product_ids": [], "excluded_product_ids": [], "usage_limit": 0, "usage_limit_per_user": 0, "limit_usage_to_x_items": null, "free_shipping": false, "product_categories": [], "excluded_product_categories": [], "exclude_sale_items": false, "minimum_amount": "", "maximum_amount": "", "email_restrictions": [], "virtual": false, "meta_data": []}}]}], "refunds": [], "payment_url": "http://host.stock2shop.test:8888/checkout/order-pay/362/?pay_for_order=true&key=wc_order_GFBrOFA8iRYwy", "is_editable": false, "needs_payment": false, "needs_processing": true, "date_created_gmt": "2023-09-05T11:42:03", "date_modified_gmt": "2023-09-05T11:42:34", "date_completed_gmt": null, "date_paid_gmt": null, "currency_symbol": "$", "_links": {"self": [{"href": "http://host.stock2shop.test:8888/wp-json/wc/v3/orders/362"}], "collection": [{"href": "http://host.stock2shop.test:8888/wp-json/wc/v3/orders"}], "customer": [{"href": "http://host.stock2shop.test:8888/wp-json/wc/v3/customers/1"}]}}