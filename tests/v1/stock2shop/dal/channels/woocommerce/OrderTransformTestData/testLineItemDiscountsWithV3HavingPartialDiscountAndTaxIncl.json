{"id": 115, "parent_id": 0, "status": "pending", "currency": "ZAR", "version": "9.8.4", "prices_include_tax": true, "date_created": "2025-05-28T09:21:10", "date_modified": "2025-05-28T10:51:48", "discount_total": "269.39", "discount_tax": "40.41", "shipping_total": "200.00", "shipping_tax": "30.00", "cart_tax": "363.68", "total": "3018.20", "total_tax": "393.68", "customer_id": 1, "order_key": "wc_order_jAUTPKch7QxqH", "billing": {"first_name": "<PERSON><PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "company": "", "address_1": "Address 1", "address_2": "", "city": "Cape Town", "state": "WC", "postcode": "7785", "country": "ZA", "email": "<EMAIL>", "phone": "0987654321"}, "shipping": {"first_name": "<PERSON><PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "company": "", "address_1": "Address 1", "address_2": "", "city": "Cape Town", "state": "WC", "postcode": "7785", "country": "ZA", "phone": "0987654321"}, "payment_method": "", "payment_method_title": "", "transaction_id": "", "customer_ip_address": "", "customer_user_agent": "", "created_via": "admin", "customer_note": "", "date_completed": null, "date_paid": null, "cart_hash": "", "number": "115", "meta_data": [{"id": 1157, "key": "_wc_order_attribution_source_type", "value": "admin"}], "line_items": [{"id": 146, "name": "Call of Duty: Modern Warfare II - PS4", "product_id": 69, "variation_id": 70, "quantity": 1, "tax_class": "", "subtotal": "1303.48", "subtotal_tax": "195.52", "total": "1173.13", "total_tax": "175.97", "taxes": [{"id": 1, "total": "175.969565", "subtotal": "195.521739"}], "meta_data": [{"id": 1151, "key": "selection", "value": "PS4", "display_key": "Selection", "display_value": "PS4"}], "sku": "COD-MW2-PS4", "price": 1173.130435, "image": {"id": 110, "src": "http://woo.test:8890/wp-content/uploads/2025/05/94a8b5fe8cf5d8f20a189af0348350ba-1.png"}, "parent_name": "Call of Duty: Modern Warfare II"}, {"id": 147, "name": "Call of Duty: Modern Warfare II - PS5", "product_id": 69, "variation_id": 71, "quantity": 1, "tax_class": "", "subtotal": "1390.43", "subtotal_tax": "208.57", "total": "1251.39", "total_tax": "187.71", "taxes": [{"id": 1, "total": "187.708696", "subtotal": "208.565217"}], "meta_data": [{"id": 1161, "key": "selection", "value": "PS5", "display_key": "Selection", "display_value": "PS5"}], "sku": "COD-MW2-PS5", "price": 1251.391305, "image": {"id": 111, "src": "http://woo.test:8890/wp-content/uploads/2025/05/122f1932dffb5bfea0de147eeed61afd-1.png"}, "parent_name": "Call of Duty: Modern Warfare II"}], "tax_lines": [{"id": 148, "rate_code": "ZA-TAX-1", "rate_id": 1, "label": "Tax", "compound": false, "tax_total": "363.68", "shipping_tax_total": "30.00", "rate_percent": 15, "meta_data": []}], "shipping_lines": [{"id": 149, "method_title": "Shipping", "method_id": "", "instance_id": "0", "total": "200.00", "total_tax": "30.00", "taxes": [{"id": 1, "total": "30", "subtotal": ""}], "tax_status": "taxable", "meta_data": []}], "fee_lines": [], "coupon_lines": [{"id": 150, "code": "pd", "discount": "269.39", "discount_tax": "40.41", "meta_data": [{"id": 1175, "key": "coupon_info", "value": "[89,\"pd\",\"percent\",10]", "display_key": "coupon_info", "display_value": "[89,\"pd\",\"percent\",10]"}], "discount_type": "percent", "nominal_amount": 10, "free_shipping": false}], "refunds": [], "payment_url": "http://woo.test:8890/checkout/order-pay/115/?pay_for_order=true&key=wc_order_jAUTPKch7QxqH", "is_editable": true, "needs_payment": true, "needs_processing": true, "date_created_gmt": "2025-05-28T09:21:10", "date_modified_gmt": "2025-05-28T10:51:48", "date_completed_gmt": null, "date_paid_gmt": null, "currency_symbol": "R", "_links": {"self": [{"href": "http://woo.test:8890/wp-json/wc/v3/orders/115", "targetHints": {"allow": ["GET", "POST", "PUT", "PATCH", "DELETE"]}}], "collection": [{"href": "http://woo.test:8890/wp-json/wc/v3/orders"}], "email_templates": [{"embeddable": true, "href": "http://woo.test:8890/wp-json/wc/v3/orders/115/actions/email_templates"}], "customer": [{"href": "http://woo.test:8890/wp-json/wc/v3/customers/1"}]}}