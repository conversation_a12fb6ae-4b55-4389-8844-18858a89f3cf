{"id": 360, "parent_id": 0, "status": "processing", "currency": "USD", "version": "8.0.3", "prices_include_tax": true, "date_created": "2023-09-05T10:45:41", "date_modified": "2023-09-05T10:48:07", "discount_total": "260.70", "discount_tax": "39.10", "shipping_total": "0.00", "shipping_tax": "0.00", "cart_tax": "351.94", "total": "2698.20", "total_tax": "351.94", "customer_id": 1, "order_key": "wc_order_Ydejm2lm5ZXwe", "billing": {"first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "company": "", "address_1": "1 sample street", "address_2": "<PERSON><PERSON>", "city": "Hout Bay", "state": "WC", "postcode": "7806", "country": "ZA", "email": "<EMAIL>", "phone": ""}, "shipping": {"first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "company": "", "address_1": "1 sample street", "address_2": "<PERSON><PERSON>", "city": "Hout Bay", "state": "WC", "postcode": "7806", "country": "ZA", "phone": ""}, "payment_method": "", "payment_method_title": "", "transaction_id": "", "customer_ip_address": "", "customer_user_agent": "", "created_via": "admin", "customer_note": "", "date_completed": null, "date_paid": "2023-09-05T10:48:07", "cart_hash": "", "number": "360", "meta_data": [], "line_items": [{"id": 39, "name": "Call of Duty: Modern Warfare II - XBSX", "product_id": 238, "variation_id": 244, "quantity": 2, "tax_class": "", "subtotal": "2606.96", "subtotal_tax": "391.04", "total": "2346.26", "total_tax": "351.94", "taxes": [{"id": 1, "total": "351.939131", "subtotal": "391.043478"}], "meta_data": [{"id": 288, "key": "selection", "value": "XBSX", "display_key": "Selection", "display_value": "XBSX"}, {"id": 298, "key": "_reduced_stock", "value": "2", "display_key": "_reduced_stock", "display_value": "2"}], "sku": "testSetInclusivePricing", "price": 1173.130435, "image": {"id": 344, "src": "http://host.stock2shop.test:8888/wp-content/uploads/2023/08/82309caa72a63b05e2be7d4033301855-1.jpg"}, "parent_name": "Call of Duty: Modern Warfare II"}], "tax_lines": [{"id": 40, "rate_code": "TAX-1", "rate_id": 1, "label": "Tax", "compound": false, "tax_total": "351.94", "shipping_tax_total": "0.00", "rate_percent": 15, "meta_data": []}], "shipping_lines": [], "fee_lines": [], "coupon_lines": [{"id": 41, "code": "testtotal", "discount": "260.7", "discount_tax": "39.1", "meta_data": [{"id": 297, "key": "coupon_data", "value": {"id": 351, "code": "testtotal", "amount": "10", "status": "publish", "date_created": {"date": "2023-09-04 07:34:32.000000", "timezone_type": 1, "timezone": "+00:00"}, "date_modified": {"date": "2023-09-04 10:27:21.000000", "timezone_type": 1, "timezone": "+00:00"}, "date_expires": null, "discount_type": "percent", "description": "", "usage_count": 2, "individual_use": false, "product_ids": [], "excluded_product_ids": [], "usage_limit": 0, "usage_limit_per_user": 0, "limit_usage_to_x_items": null, "free_shipping": true, "product_categories": [], "excluded_product_categories": [], "exclude_sale_items": false, "minimum_amount": "", "maximum_amount": "", "email_restrictions": [], "virtual": false, "meta_data": []}, "display_key": "coupon_data", "display_value": {"id": 351, "code": "testtotal", "amount": "10", "status": "publish", "date_created": {"date": "2023-09-04 07:34:32.000000", "timezone_type": 1, "timezone": "+00:00"}, "date_modified": {"date": "2023-09-04 10:27:21.000000", "timezone_type": 1, "timezone": "+00:00"}, "date_expires": null, "discount_type": "percent", "description": "", "usage_count": 2, "individual_use": false, "product_ids": [], "excluded_product_ids": [], "usage_limit": 0, "usage_limit_per_user": 0, "limit_usage_to_x_items": null, "free_shipping": true, "product_categories": [], "excluded_product_categories": [], "exclude_sale_items": false, "minimum_amount": "", "maximum_amount": "", "email_restrictions": [], "virtual": false, "meta_data": []}}]}], "refunds": [], "payment_url": "http://host.stock2shop.test:8888/checkout/order-pay/360/?pay_for_order=true&key=wc_order_Ydejm2lm5ZXwe", "is_editable": false, "needs_payment": false, "needs_processing": true, "date_created_gmt": "2023-09-05T10:45:41", "date_modified_gmt": "2023-09-05T10:48:07", "date_completed_gmt": null, "date_paid_gmt": "2023-09-05T10:48:07", "currency_symbol": "$", "_links": {"self": [{"href": "http://host.stock2shop.test:8888/wp-json/wc/v3/orders/360"}], "collection": [{"href": "http://host.stock2shop.test:8888/wp-json/wc/v3/orders"}], "customer": [{"href": "http://host.stock2shop.test:8888/wp-json/wc/v3/customers/1"}]}}