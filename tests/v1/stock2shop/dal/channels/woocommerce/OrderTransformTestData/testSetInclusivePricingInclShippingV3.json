{"id": 355, "parent_id": 0, "status": "processing", "currency": "USD", "version": "8.0.3", "prices_include_tax": true, "date_created": "2023-09-05T05:20:14", "date_modified": "2023-09-05T06:46:10", "discount_total": "0.00", "discount_tax": "0.00", "shipping_total": "250.00", "shipping_tax": "0.00", "cart_tax": "0.00", "total": "450.00", "total_tax": "0.00", "customer_id": 1, "order_key": "wc_order_zzGT1sX1eq6un", "billing": {"first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "company": "", "address_1": "1 sample street", "address_2": "<PERSON><PERSON>", "city": "Hout Bay", "state": "WC", "postcode": "7806", "country": "ZA", "email": "<EMAIL>", "phone": ""}, "shipping": {"first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "company": "f", "address_1": "1 sample street", "address_2": "<PERSON><PERSON>", "city": "Hout Bay", "state": "WC", "postcode": "7806", "country": "ZA", "phone": ""}, "payment_method": "", "payment_method_title": "", "transaction_id": "", "customer_ip_address": "", "customer_user_agent": "", "created_via": "admin", "customer_note": "", "date_completed": null, "date_paid": "2023-09-05T05:22:39", "cart_hash": "", "number": "355", "meta_data": [], "line_items": [{"id": 26, "name": "Acacia Wood and Glass Jar - Medium", "product_id": 150, "variation_id": 151, "quantity": 1, "tax_class": "", "subtotal": "200.00", "subtotal_tax": "0.00", "total": "200.00", "total_tax": "0.00", "taxes": [], "meta_data": [{"id": 187, "key": "selection", "value": "Medium", "display_key": "Selection", "display_value": "Medium"}, {"id": 193, "key": "_reduced_stock", "value": "1", "display_key": "_reduced_stock", "display_value": "1"}], "sku": "testSetInclusivePricingInclShipping", "price": 200, "image": {"id": 311, "src": "http://host.stock2shop.test:8888/wp-content/uploads/2023/08/15b5bf7134c0f126f82cf4f39b8d39a5-1.jpg"}, "parent_name": "Acacia Wood and Glass Jar"}], "tax_lines": [], "shipping_lines": [{"id": 27, "method_title": "Shipping", "method_id": "", "instance_id": "0", "total": "250.00", "total_tax": "0.00", "taxes": [], "meta_data": []}], "fee_lines": [], "coupon_lines": [], "refunds": [], "payment_url": "http://host.stock2shop.test:8888/checkout/order-pay/355/?pay_for_order=true&key=wc_order_zzGT1sX1eq6un", "is_editable": false, "needs_payment": false, "needs_processing": true, "date_created_gmt": "2023-09-05T05:20:14", "date_modified_gmt": "2023-09-05T06:46:10", "date_completed_gmt": null, "date_paid_gmt": "2023-09-05T05:22:39", "currency_symbol": "$", "_links": {"self": [{"href": "http://host.stock2shop.test:8888/wp-json/wc/v3/orders/355"}], "collection": [{"href": "http://host.stock2shop.test:8888/wp-json/wc/v3/orders"}], "customer": [{"href": "http://host.stock2shop.test:8888/wp-json/wc/v3/customers/1"}]}}