{"id": 506, "parent_id": 0, "status": "processing", "currency": "ZAR", "version": "8.2.2", "prices_include_tax": true, "date_created": "2023-12-04T07:30:28", "date_modified": "2023-12-04T07:30:28", "discount_total": "0.00", "discount_tax": "0.00", "shipping_total": "0.00", "shipping_tax": "0.00", "cart_tax": "0.00", "total": "59643.00", "total_tax": "0.00", "customer_id": 1, "order_key": "wc_order_yTWI1NqEpkVhZ", "billing": {"first_name": "<PERSON>", "last_name": "TEST", "company": "TEST", "address_1": "1 test street", "address_2": "fqw", "city": "fq", "state": "WC", "postcode": "7806", "country": "ZA", "email": "<EMAIL>", "phone": "0897087087"}, "shipping": {"first_name": "", "last_name": "", "company": "", "address_1": "", "address_2": "", "city": "", "state": "", "postcode": "", "country": "", "phone": ""}, "payment_method": "cod", "payment_method_title": "Cash on delivery", "transaction_id": "", "customer_ip_address": "************", "customer_user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "created_via": "checkout", "customer_note": "", "date_completed": null, "date_paid": null, "cart_hash": "********************************", "number": "506", "meta_data": [{"id": 6795, "key": "is_vat_exempt", "value": "no"}], "line_items": [{"id": 25, "name": "Test Product without SKU", "product_id": 504, "variation_id": 0, "quantity": 1, "tax_class": "", "subtotal": "99.00", "subtotal_tax": "0.00", "total": "99.00", "total_tax": "0.00", "taxes": [], "meta_data": [], "sku": "", "price": 99, "image": {"id": "", "src": ""}, "parent_name": null}, {"id": 20, "name": "Apple iPad Pro 12.9inch 6th Gen WiFi Silver - 512GB", "product_id": 296, "variation_id": 297, "quantity": 1, "tax_class": "", "subtotal": "31999.00", "subtotal_tax": "0.00", "total": "31999.00", "total_tax": "0.00", "taxes": [], "meta_data": [{"id": 184, "key": "selection", "value": "512GB", "display_key": "Selection", "display_value": "512GB"}, {"id": 241, "key": "_reduced_stock", "value": "1", "display_key": "_reduced_stock", "display_value": "1"}], "sku": "AIP-12-9-6GEN-SILVER-512GB", "price": 31999, "image": {"id": 451, "src": "http://woo.test:8888/wp-content/uploads/2023/11/e190f64d6e772fbf7dfb457ca9ea330b.jpg"}, "parent_name": "Apple iPad Pro 12.9inch 6th Gen WiFi Silver"}, {"id": 21, "name": "Apple iPad Pro 12.9inch 6th Gen WiFi Silver - 128GB", "product_id": 296, "variation_id": 300, "quantity": 1, "tax_class": "", "subtotal": "24999.00", "subtotal_tax": "0.00", "total": "24999.00", "total_tax": "0.00", "taxes": [], "meta_data": [{"id": 194, "key": "selection", "value": "128GB", "display_key": "Selection", "display_value": "128GB"}, {"id": 242, "key": "_reduced_stock", "value": "1", "display_key": "_reduced_stock", "display_value": "1"}], "sku": "AIP-12-9-6GEN-SILVER-128GB", "price": 24999, "image": {"id": 451, "src": "http://woo.test:8888/wp-content/uploads/2023/11/e190f64d6e772fbf7dfb457ca9ea330b.jpg"}, "parent_name": "Apple iPad Pro 12.9inch 6th Gen WiFi Silver"}, {"id": 22, "name": "Activ Play Fishing And Water Play Table", "product_id": 346, "variation_id": 0, "quantity": 1, "tax_class": "", "subtotal": "349.00", "subtotal_tax": "0.00", "total": "349.00", "total_tax": "0.00", "taxes": [], "meta_data": [{"id": 243, "key": "_reduced_stock", "value": "1", "display_key": "_reduced_stock", "display_value": "1"}], "sku": "AVTIV-PF-WPT", "price": 349, "image": {"id": "464", "src": "http://woo.test:8888/wp-content/uploads/2023/11/bfe99b582b450793d96fa8f593005d91.jpg"}, "parent_name": null}, {"id": 23, "name": "Bamboo Polarized Sunglasses", "product_id": 320, "variation_id": 0, "quantity": 1, "tax_class": "", "subtotal": "599.00", "subtotal_tax": "0.00", "total": "599.00", "total_tax": "0.00", "taxes": [], "meta_data": [{"id": 244, "key": "_reduced_stock", "value": "1", "display_key": "_reduced_stock", "display_value": "1"}], "sku": "BPS", "price": 599, "image": {"id": "456", "src": "http://woo.test:8888/wp-content/uploads/2023/11/f844eeb10bf3f21b0864c7c496191d32.jpg"}, "parent_name": null}, {"id": 24, "name": "Call of Duty: Modern Warfare II - PS4", "product_id": 489, "variation_id": 490, "quantity": 1, "tax_class": "", "subtotal": "1499.00", "subtotal_tax": "0.00", "total": "1499.00", "total_tax": "0.00", "taxes": [], "meta_data": [{"id": 222, "key": "selection", "value": "PS4", "display_key": "Selection", "display_value": "PS4"}, {"id": 245, "key": "_reduced_stock", "value": "1", "display_key": "_reduced_stock", "display_value": "1"}], "sku": "COD-MW2-PS4", "price": 1499, "image": {"id": 491, "src": "http://woo.test:8888/wp-content/uploads/2023/11/55a877e802a79fc94e233114baa91ef3.jpg"}, "parent_name": "Call of Duty: Modern Warfare II"}, {"id": 26, "name": "Test Product 2 without SKU", "product_id": 505, "variation_id": 0, "quantity": 1, "tax_class": "", "subtotal": "99.00", "subtotal_tax": "0.00", "total": "99.00", "total_tax": "0.00", "taxes": [], "meta_data": [], "sku": "", "price": 99, "image": {"id": "", "src": ""}, "parent_name": null}], "tax_lines": [], "shipping_lines": [], "fee_lines": [], "coupon_lines": [], "refunds": [], "payment_url": "http://woo.test:8888/checkout/order-pay/506/?pay_for_order=true&key=wc_order_yTWI1NqEpkVhZ", "is_editable": false, "needs_payment": false, "needs_processing": true, "date_created_gmt": "2023-12-04T07:30:28", "date_modified_gmt": "2023-12-04T07:30:28", "date_completed_gmt": null, "date_paid_gmt": null, "currency_symbol": "R", "_links": {"self": [{"href": "http://woo.test:8888/wp-json/wc/v3/orders/506"}], "collection": [{"href": "http://woo.test:8888/wp-json/wc/v3/orders"}], "customer": [{"href": "http://woo.test:8888/wp-json/wc/v3/customers/1"}]}}