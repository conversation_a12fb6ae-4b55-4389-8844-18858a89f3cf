{"id": 358, "parent_id": 0, "status": "processing", "currency": "USD", "version": "8.0.3", "prices_include_tax": true, "date_created": "2023-09-05T07:06:03", "date_modified": "2023-09-05T07:07:23", "discount_total": "0.00", "discount_tax": "0.00", "shipping_total": "200.00", "shipping_tax": "30.00", "cart_tax": "52.17", "total": "630.00", "total_tax": "82.17", "customer_id": 1, "order_key": "wc_order_VOvWIJvEtROPC", "billing": {"first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "company": "", "address_1": "1 sample street", "address_2": "<PERSON><PERSON>", "city": "Hout Bay", "state": "WC", "postcode": "7806", "country": "ZA", "email": "<EMAIL>", "phone": ""}, "shipping": {"first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "company": "", "address_1": "1 sample street", "address_2": "<PERSON><PERSON>", "city": "Hout Bay", "state": "WC", "postcode": "7806", "country": "ZA", "phone": ""}, "payment_method": "", "payment_method_title": "", "transaction_id": "", "customer_ip_address": "", "customer_user_agent": "", "created_via": "admin", "customer_note": "", "date_completed": null, "date_paid": "2023-09-05T07:07:23", "cart_hash": "", "number": "358", "meta_data": [], "line_items": [{"id": 34, "name": "Acacia Wood and Glass Jar - Medium", "product_id": 150, "variation_id": 151, "quantity": 2, "tax_class": "", "subtotal": "347.83", "subtotal_tax": "52.17", "total": "347.83", "total_tax": "52.17", "taxes": [{"id": 1, "total": "52.173913", "subtotal": "52.173913"}], "meta_data": [{"id": 249, "key": "selection", "value": "Medium", "display_key": "Selection", "display_value": "Medium"}, {"id": 261, "key": "_reduced_stock", "value": "2", "display_key": "_reduced_stock", "display_value": "2"}], "sku": "testSetInclusivePricing", "price": 173.913043, "image": {"id": 311, "src": "http://host.stock2shop.test:8888/wp-content/uploads/2023/08/15b5bf7134c0f126f82cf4f39b8d39a5-1.jpg"}, "parent_name": "Acacia Wood and Glass Jar"}], "tax_lines": [{"id": 36, "rate_code": "TAX-1", "rate_id": 1, "label": "Tax", "compound": false, "tax_total": "52.17", "shipping_tax_total": "30.00", "rate_percent": 15, "meta_data": []}], "shipping_lines": [{"id": 35, "method_title": "Shipping", "method_id": "", "instance_id": "0", "total": "200.00", "total_tax": "30.00", "taxes": [{"id": 1, "total": "30", "subtotal": ""}], "meta_data": []}], "fee_lines": [], "coupon_lines": [], "refunds": [], "payment_url": "http://host.stock2shop.test:8888/checkout/order-pay/358/?pay_for_order=true&key=wc_order_VOvWIJvEtROPC", "is_editable": false, "needs_payment": false, "needs_processing": true, "date_created_gmt": "2023-09-05T07:06:03", "date_modified_gmt": "2023-09-05T07:07:23", "date_completed_gmt": null, "date_paid_gmt": "2023-09-05T07:07:23", "currency_symbol": "$", "_links": {"self": [{"href": "http://host.stock2shop.test:8888/wp-json/wc/v3/orders/358"}], "collection": [{"href": "http://host.stock2shop.test:8888/wp-json/wc/v3/orders"}], "customer": [{"href": "http://host.stock2shop.test:8888/wp-json/wc/v3/customers/1"}]}}