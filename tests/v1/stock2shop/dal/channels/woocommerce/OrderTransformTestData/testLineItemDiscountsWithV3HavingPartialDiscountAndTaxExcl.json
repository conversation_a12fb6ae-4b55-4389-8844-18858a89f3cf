{"id": 124, "parent_id": 0, "status": "pending", "currency": "ZAR", "version": "9.8.5", "prices_include_tax": false, "date_created": "2025-06-03T08:08:50", "date_modified": "2025-06-03T08:11:02", "discount_total": "5096.50", "discount_tax": "764.48", "shipping_total": "500.00", "shipping_tax": "75.00", "cart_tax": "6880.28", "total": "53323.78", "total_tax": "6955.28", "customer_id": 1, "order_key": "wc_order_ZcrLWHqi1APj5", "billing": {"first_name": "<PERSON><PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "company": "", "address_1": "Address 1", "address_2": "", "city": "Cape Town", "state": "WC", "postcode": "7785", "country": "ZA", "email": "<EMAIL>", "phone": "0987654321"}, "shipping": {"first_name": "<PERSON><PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "company": "", "address_1": "Address 1", "address_2": "", "city": "Cape Town", "state": "WC", "postcode": "7785", "country": "ZA", "phone": "0987654321"}, "payment_method": "", "payment_method_title": "", "transaction_id": "", "customer_ip_address": "", "customer_user_agent": "", "created_via": "admin", "customer_note": "", "date_completed": null, "date_paid": null, "cart_hash": "", "number": "124", "meta_data": [{"id": 1422, "key": "_wc_order_attribution_source_type", "value": "admin"}], "line_items": [{"id": 184, "name": "Call of Duty: Modern Warfare II - PS4", "product_id": 69, "variation_id": 70, "quantity": 20, "tax_class": "", "subtotal": "29980.00", "subtotal_tax": "4497.00", "total": "26982.00", "total_tax": "4047.30", "taxes": [{"id": 2, "total": "4047.3", "subtotal": "4497"}], "meta_data": [{"id": 1429, "key": "selection", "value": "PS4", "display_key": "Selection", "display_value": "PS4"}], "sku": "COD-MW2-PS4", "price": 1349.1, "image": {"id": 110, "src": "http://woo.test:8890/wp-content/uploads/2025/05/94a8b5fe8cf5d8f20a189af0348350ba-1.png"}, "parent_name": "Call of Duty: Modern Warfare II"}, {"id": 185, "name": "WooCommerce2-Product", "product_id": 63, "variation_id": 0, "quantity": 15, "tax_class": "", "subtotal": "20985.00", "subtotal_tax": "3147.75", "total": "18886.50", "total_tax": "2832.98", "taxes": [{"id": 2, "total": "2832.975", "subtotal": "3147.75"}], "meta_data": [], "sku": "WooCommerce2-Product", "price": 1259.1, "image": {"id": "62", "src": "http://woo.test:8890/wp-content/uploads/2025/05/a3640e12f138a6ae737db53daeda8a40.png"}, "parent_name": null}], "tax_lines": [{"id": 187, "rate_code": "ZA-TAX-1", "rate_id": 2, "label": "Tax", "compound": false, "tax_total": "6880.28", "shipping_tax_total": "75.00", "rate_percent": 15, "meta_data": []}], "shipping_lines": [{"id": 186, "method_title": "Shipping", "method_id": "", "instance_id": "0", "total": "500.00", "total_tax": "75.00", "taxes": [{"id": 2, "total": "75", "subtotal": ""}], "tax_status": "taxable", "meta_data": []}], "fee_lines": [], "coupon_lines": [{"id": 188, "code": "pd", "discount": "5096.5", "discount_tax": "764.48", "meta_data": [{"id": 1452, "key": "coupon_info", "value": "[89,\"pd\",\"percent\",10]", "display_key": "coupon_info", "display_value": "[89,\"pd\",\"percent\",10]"}], "discount_type": "percent", "nominal_amount": 10, "free_shipping": false}], "refunds": [], "payment_url": "http://woo.test:8890/checkout/order-pay/124/?pay_for_order=true&key=wc_order_ZcrLWHqi1APj5", "is_editable": true, "needs_payment": true, "needs_processing": true, "date_created_gmt": "2025-06-03T08:08:50", "date_modified_gmt": "2025-06-03T08:11:02", "date_completed_gmt": null, "date_paid_gmt": null, "currency_symbol": "R", "_links": {"self": [{"href": "http://woo.test:8890/wp-json/wc/v3/orders/124", "targetHints": {"allow": ["GET", "POST", "PUT", "PATCH", "DELETE"]}}], "collection": [{"href": "http://woo.test:8890/wp-json/wc/v3/orders"}], "email_templates": [{"embeddable": true, "href": "http://woo.test:8890/wp-json/wc/v3/orders/124/actions/email_templates"}], "customer": [{"href": "http://woo.test:8890/wp-json/wc/v3/customers/1"}]}}