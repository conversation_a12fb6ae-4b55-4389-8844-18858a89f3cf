{"id": 125, "parent_id": 0, "status": "pending", "currency": "ZAR", "version": "9.8.5", "prices_include_tax": false, "date_created": "2025-06-03T08:11:08", "date_modified": "2025-06-03T08:11:57", "discount_total": "17288.00", "discount_tax": "2593.20", "shipping_total": "250.00", "shipping_tax": "37.50", "cart_tax": "0.00", "total": "287.50", "total_tax": "37.50", "customer_id": 1, "order_key": "wc_order_9tc7pmnTY0OI9", "billing": {"first_name": "<PERSON><PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "company": "", "address_1": "Address 1", "address_2": "", "city": "Cape Town", "state": "WC", "postcode": "7785", "country": "ZA", "email": "<EMAIL>", "phone": "0987654321"}, "shipping": {"first_name": "<PERSON><PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "company": "", "address_1": "Address 1", "address_2": "", "city": "Cape Town", "state": "WC", "postcode": "7785", "country": "ZA", "phone": "0987654321"}, "payment_method": "", "payment_method_title": "", "transaction_id": "", "customer_ip_address": "", "customer_user_agent": "", "created_via": "admin", "customer_note": "", "date_completed": null, "date_paid": null, "cart_hash": "", "number": "125", "meta_data": [{"id": 1462, "key": "_wc_order_attribution_source_type", "value": "admin"}], "line_items": [{"id": 189, "name": "Call of Duty: Modern Warfare II - XBSX", "product_id": 69, "variation_id": 72, "quantity": 5, "tax_class": "", "subtotal": "7495.00", "subtotal_tax": "1124.25", "total": "0.00", "total_tax": "0.00", "taxes": [{"id": 2, "total": "0", "subtotal": "1124.25"}], "meta_data": [{"id": 1462, "key": "selection", "value": "XBSX", "display_key": "Selection", "display_value": "XBSX"}], "sku": "COD-MW2-XBSX", "price": 0, "image": {"id": 112, "src": "http://woo.test:8890/wp-content/uploads/2025/05/d85bac2d31406a6f0b2a9e1ad47aa922-1.png"}, "parent_name": "Call of Duty: Modern Warfare II"}, {"id": 190, "name": "WooCommerce2-Product", "product_id": 63, "variation_id": 0, "quantity": 7, "tax_class": "", "subtotal": "9793.00", "subtotal_tax": "1468.95", "total": "0.00", "total_tax": "0.00", "taxes": [{"id": 2, "total": "0", "subtotal": "1468.95"}], "meta_data": [], "sku": "WooCommerce2-Product", "price": 0, "image": {"id": "62", "src": "http://woo.test:8890/wp-content/uploads/2025/05/a3640e12f138a6ae737db53daeda8a40.png"}, "parent_name": null}], "tax_lines": [{"id": 192, "rate_code": "ZA-TAX-1", "rate_id": 2, "label": "Tax", "compound": false, "tax_total": "0.00", "shipping_tax_total": "37.50", "rate_percent": 15, "meta_data": []}], "shipping_lines": [{"id": 191, "method_title": "Shipping", "method_id": "", "instance_id": "0", "total": "250.00", "total_tax": "37.50", "taxes": [{"id": 2, "total": "37.5", "subtotal": ""}], "tax_status": "taxable", "meta_data": []}], "fee_lines": [], "coupon_lines": [{"id": 193, "code": "fd", "discount": "17288", "discount_tax": "2593.2", "meta_data": [{"id": 1485, "key": "coupon_info", "value": "[104,\"fd\",\"percent\",100,true]", "display_key": "coupon_info", "display_value": "[104,\"fd\",\"percent\",100,true]"}], "discount_type": "percent", "nominal_amount": 100, "free_shipping": true}], "refunds": [], "payment_url": "http://woo.test:8890/checkout/order-pay/125/?pay_for_order=true&key=wc_order_9tc7pmnTY0OI9", "is_editable": true, "needs_payment": true, "needs_processing": true, "date_created_gmt": "2025-06-03T08:11:08", "date_modified_gmt": "2025-06-03T08:11:57", "date_completed_gmt": null, "date_paid_gmt": null, "currency_symbol": "R", "_links": {"self": [{"href": "http://woo.test:8890/wp-json/wc/v3/orders/125", "targetHints": {"allow": ["GET", "POST", "PUT", "PATCH", "DELETE"]}}], "collection": [{"href": "http://woo.test:8890/wp-json/wc/v3/orders"}], "email_templates": [{"embeddable": true, "href": "http://woo.test:8890/wp-json/wc/v3/orders/125/actions/email_templates"}], "customer": [{"href": "http://woo.test:8890/wp-json/wc/v3/customers/1"}]}}