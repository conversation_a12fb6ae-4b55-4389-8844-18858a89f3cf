<?php
namespace tests\v1\stock2shop\dal\channels\woocommerce;

use Exception;
use RedBean_Facade as R;
use stock2shop\dal\channels\woocommerce;
use stock2shop\repository;
use stock2shop\vo;
use tests;

class productVariantTest extends tests\TestCase
{
    const CLIENT_ID     = 1;
    const SOURCE_ID     = 1;
    const CHANNEL_ID    = 1;
    private $config = [
        'client_id' => self::CLIENT_ID,
        'sources'   => [
            [
                'id'    => self::SOURCE_ID,
                'type'  => vo\Config::CONNECTOR_TYPE_SAGEONE
            ]
        ],
        'channels'  => [
            [
                'id'      => self::CHANNEL_ID,
                'meta'    => [
                    [
                        'key'   => 'product_map',
                        'value' => '{
                            "title": "{{product.title}}",
                            "description": "{{product.body_html}}",
                            "enable_html_description": true,
                            "variations": [{
                                "sku": "{{variant.sku}}",
                                "regular_price": "{{variant.price}}",
                                "stock_quantity": "{{variant.qty}}"
                            }]
                        }'
                    ]
                ],
                'type'    => vo\Config::CONNECTOR_TYPE_WOOCOMMERCE,
                'sources' => [
                    [
                        'id' => self::SOURCE_ID
                    ]
                ]
            ]
        ]
    ];

    /**
     * @return void
     * @throws Exception
     */
    public function testGetWooVariantToUpdate() {
        $config = new vo\Config($this->config);

        tests\DB::makeFromConfig($config);

        $app = tests\Utils::stubApp();

        $productPatches = tests\DB::makeProducts($config, 1, self::SOURCE_ID);
        $productPatchesRepository = new repository\ProductPatchesRepository(
            $config,
            self::SOURCE_ID,
            $productPatches->patches
        );
        $productPatchesRepository->save();

        $variant_id = $productPatches->patches['source_product_code-0']->results->variants[0]->id;

        $channelBean = R::load('channel', self::CHANNEL_ID);
        $env = $app->environment();
        $env['client']  = $channelBean;
        $env['channel'] = R::load('channel', self::CHANNEL_ID);

        $productVariant = new woocommerce\ProductVariant();

        // Example of an existing woo product with flags for price and title
        R::exec("
            insert into flag (`table`, `column`, code, channel_id, client_id)
            values (?, ?, ?, ?, ?)",
            ['variant', 'price', 'channel', self::CHANNEL_ID, self::CLIENT_ID]
        );
        R::exec("
            insert into flag (`table`, `column`, code, channel_id, client_id)
            values (?, ?, ?, ?, ?)",
            ['product', 'title', 'channel', self::CHANNEL_ID, self::CLIENT_ID]
        );

        $existing = json_decode(json_encode([
            'products' => [
                [
                    'id' => 2,
                    'parent' => [
                        'id' => 1
                    ]
                ]
            ]
        ]));

        // parent with flags
        $woo_product = $productVariant->getWooVariantToUpdate($existing, $variant_id, $channelBean);
        $this->assertEquals(1, $woo_product->products[0]->id, 'product id set');
        $this->assertEquals(2, $woo_product->products[0]->variations[0]->id, 'variant id set');
        $this->assertObjectHasAttribute('stock_quantity', $woo_product->products[0]->variations[0], 'qty set');
        $this->assertObjectNotHasAttribute('title', $woo_product->products[0], 'no title set');
        $this->assertObjectNotHasAttribute('regular_price', $woo_product->products[0]->variations[0], 'no price set');

        // no parent with flags
        $existing = json_decode(json_encode([
            'products' => [
                [
                    'id'        => 2,
                    'parent'    => []
                ]
            ]
        ]));
        $woo_product = $productVariant->getWooVariantToUpdate($existing, $variant_id, $channelBean);
        $this->assertEquals(2, $woo_product->products[0]->id, 'product id set');
        $this->assertEquals(2, $woo_product->products[0]->variations[0]->id, 'variant id set');

        // no parent no flags
        R::exec("update flag set code = ? where `column` = ? and channel_id = ?",
            ['system', 'price', self::CHANNEL_ID]
        );
        R::exec("update flag set code = ? where `column` = ? and channel_id = ?",
            ['system', 'title', self::CHANNEL_ID]
        );
        $woo_product = $productVariant->getWooVariantToUpdate($existing, $variant_id, $channelBean);
        $this->assertObjectHasAttribute('regular_price', $woo_product->products[0]->variations[0], 'price set');

        // no existing product
        $woo_product = $productVariant->getWooVariantToUpdate(false, $variant_id, $channelBean);
        $this->assertTrue(!isset($woo_product->products[0]->id), 'product id not set');
        $this->assertTrue(!isset($woo_product->products[0]->variations[0]->id), 'variant id not set');
    }
}
