<?php

namespace tests\v1\stock2shop\dal\channels\woocommerce;

use tests;
use stock2shop\dal\channels\woocommerce\OrderTransform;
use stock2shop\dal\system;
use stock2shop\lib\Utils;
use RedBean_Facade as R;

final class OrderTransformTest extends tests\TestCase
{
    const PRICE_DELTA = 0.01;

    private function initTransformTest($testName, $options = []): array
    {
        $client_id = 21;

        // Clear old data
        $this->clearData($testName);

        // Create channel
        list($app, $client, $source, $channel) = tests\Utils::makeSageOneSourceWoocommerceChannel($testName);

        // Channel meta
        if (array_key_exists("meta", $options)) {
            tests\Utils::addChannelMeta($options["meta"], $channel);
        }


        // Create product and variant
        list($product, $variant) = tests\Utils::makeSimpleProduct($source, $testName, 'Foo', 0);

        // create price variants
        /** @var \RedBean_OODBBean|system\pricevariant $pricevariant */
        $pricevariant             = R::dispense("pricevariant");
        $pricevariant->client_id  = $client_id;
        $pricevariant->variant_id = $variant->id;
        $pricevariant->tier       = $testName . 'wholesale';
        $pricevariant->price      = 45.89;
        R::store($pricevariant);

        /** @var \RedBean_OODBBean|system\pricevariant $pricevariant */
        $pricevariant             = R::dispense("pricevariant");
        $pricevariant->client_id  = $client_id;
        $pricevariant->variant_id = $variant->id;
        $pricevariant->tier       = $testName . 'retail';
        $pricevariant->price      = 79.99;
        R::store($pricevariant);

        return [$app, $source, $channel, $product, $variant];
    }

    /**
     * Load order json by file name
     * @param $testFilename
     * @return mixed
     */
    private function loadOrderJson($testFilename)
    {
        global $config;
        $orderData = file_get_contents(
            $config['baseDir'] . "/../../tests/v1/stock2shop/dal/channels/woocommerce/OrderTransformTestData/$testFilename"
        );
        return json_decode($orderData);
    }

    /**
     * Test to link channel variant when it does not exist
     * This is only valid for the legacy webhook
     *
     * @return void
     */
    public function testOrderLinkItemsOnSku()
    {
        // Initiate test
        list($app, $source, $channel, $product, $variant) = $this->initTransformTest($this->getName(), [
            "meta" => [
                "order_link_items_on_sku" => "true",
                "queue_fulfill_order" => "false",
                "order_use_system_price" => "false"
            ]
        ]);

        //==========================
        // Get order data v3 legacy
        //==========================

        $orderData = $this->loadOrderJson("ChannelOrder.json");

        $transform = new OrderTransform();
        $result    = $transform->transform($orderData);

        $this->assertTrue(isset($result->system_order));
        $this->assertTrue(isset($result->system_order->line_items));
        $this->assertTrue(is_array($result->system_order->line_items));
        $this->assertTrue(isset($result->system_order->payment_details));
        $this->assertTrue(is_object($result->system_order->payment_details));
        $this->assertTrue(isset($result->system_order->billing_address));
        $this->assertTrue(is_object($result->system_order->billing_address));
        $this->assertTrue(isset($result->system_order->shipping_address));
        $this->assertTrue(is_object($result->system_order->shipping_address));
        $this->assertEquals(250.00, (float)$result->system_order->line_items[0]->subtotal);
        $this->assertEquals(0.00, (float)$result->system_order->line_items[0]->subtotal_tax);

        // confirm channel variant was created
        $slots       = R::genSlots($result->system_order->line_items);
        $variant_ids = array_column($result->system_order->line_items, 'variant_id');
        array_push($variant_ids, $channel->id);
        $channel_variant = R::getRow(
            "select * from channel_variant where channel_variant_code in($slots) and channel_id = ? order by id",
            $variant_ids
        );
        $this->assertTrue(Utils::found($channel_variant));

        // confirm channel product was created
        $product_ids = array_column($result->system_order->line_items, 'product_id');
        array_push($product_ids, $channel->id);
        $channel_product = R::getRow(
            "select * from channel_product where channel_product_code in($slots) and channel_id = ? order by id",
            $product_ids
        );
        $this->assertTrue(Utils::found($channel_product));
    }

    /**
     * Test setting inclusive pricing on system order
     * This is only done for the latest webhook version
     *
     * @dataProvider transformInclusiveDataProvider
     *
     *
     *
     * @return void
     * @throws \Exception
     */
    public function testTransformInclusivePricing(
        $testName,
        $meta,
        $payload,
        $price_incl,
        $price,
        $total,
        $tax_price,
        $total_tax,
        $shipping_lines
    )
    {
        //Initiate test
        $this->initTransformTest($testName, $meta);

        $transform = new OrderTransform();
        $result    = $transform->transform($payload);

        // Assert inclusive flag
        $this->assertTrue($result->system_order->has_taxes_incl);

        // Prices
        $this->assertEquals($price_incl, (float)$result->system_order->line_items[0]->price_incl);
        $this->assertEquals($price, (float)$result->system_order->line_items[0]->price);
        $this->assertEquals($total, (float)$result->system_order->line_items[0]->total);

        // Taxes
        $this->assertEquals($tax_price, (float)$result->system_order->line_items[0]->tax_lines[0]->price);
        $this->assertEquals($total_tax, (float)$result->system_order->line_items[0]->total_tax);

        $this->assertEquals($shipping_lines, empty($result->system_order->shipping_lines));
    }

    /**
     * @param $testName
     * @return void
     */
    private function clearData($testName)
    {
        // Clear old data
        $channels = R::getAll("SELECT * from channel where description like '" . $testName . "%'");
        foreach ($channels as $channel) {
            R::exec("delete from channelmeta where channel_id = ?;", [$channel['id']]);
            R::exec("delete from channel_source where channel_id = ?;", [$channel['id']]);
        }
        R::exec("delete from pricevariant where tier like '" . $testName . "%';");
        R::exec("delete from variant where source_variant_code like '" . $testName . "%';");
        R::exec("delete from product where source_product_code like '" . $testName . "%';");
        R::exec("delete from source where description like '" . $testName . "%';");
        R::exec("delete from channel where description like '" . $testName . "%';");

        // There is some logic in e.g. linkVariant in www/v1/stock2shop/dal/channels/woocommerce/Helper.php
        // that expects to only have a single master source per client.
        R::exec("update source set active=0 where client_id=21 and source_id IS NULL and id != 57;");
    }

    /**
     * Test to ensue line_items with blank SKU's are removed
     *
     * @return void
     */
    public function testWooBundle()
    {
        // Initiate test
        list($app, $source, $channel, $product, $variant) = $this->initTransformTest($this->getName(), [
            "meta" => [
                "api_webhook_version" => "3",
                "order_link_items_on_sku" => "false",
                "queue_fulfill_order" => "false",
                "order_use_system_price" => "false"
            ]
        ]);

        //==========================
        // Get order data
        //==========================

        $orderData = $this->loadOrderJson("testWooBundleV3.json");

        $transform = new OrderTransform();
        $result    = $transform->transform($orderData);

        $this->assertTrue(isset($result->system_order), "Expected system order to be set");
        $this->assertTrue(isset($result->system_order->line_items), "Expected line items to be set");
        $this->assertTrue(is_array($result->system_order->line_items), "Expected line items to be an array");
        $this->assertCount(5, $result->system_order->line_items, "Expected 5 line items");
        $this->assertContains("AIP-12-9-6GEN-SILVER-512GB", array_column($result->system_order->line_items, "sku"));
        $this->assertContains("AIP-12-9-6GEN-SILVER-128GB", array_column($result->system_order->line_items, "sku"));
        $this->assertContains("AVTIV-PF-WPT", array_column($result->system_order->line_items, "sku"));
        $this->assertContains("BPS", array_column($result->system_order->line_items, "sku"));
        $this->assertContains("COD-MW2-PS4", array_column($result->system_order->line_items, "sku"));
    }

    function transformInclusiveDataProvider(): \Generator
    {
        $meta = [
            "meta" => [
                "queue_fulfill_order" => "false",
                "order_use_system_price" => "false",
                "api_webhook_version" => "3"
            ]
        ];

        // Assert pricing with no tax | no shipping | no discount
        // Latest webhook
        yield [
            "testSetInclusivePricing",
            $meta,
            $this->loadOrderJson("testSetInclusivePricingV3.json"),
            1499.00,
            1499.00,
            2998.00,
            0.00,
            0.00,
            true
        ];

        // Assert pricing with no tax | shipping | no discount
        // Latest webhook
        yield [
            "testSetInclusivePricing",
            $meta,
            $this->loadOrderJson("testSetInclusivePricingInclShippingV3.json"),
            200.00,
            200.00,
            200.00,
            0.00,
            0.00,
            false
        ];

        // Assert pricing with tax | shipping | no discount
        // Latest webhook
        yield [
            "testSetInclusivePricing",
            $meta,
            $this->loadOrderJson("testSetInclusivePricingInclTaxInclShippingV3.json"),
            200.00,
            173.915,
            347.83,
            26.085,
            52.17,
            false
        ];

        // Assert pricing with tax | no shipping | no discount
        // Latest webhook
        yield [
            "testSetInclusivePricing",
            $meta,
            $this->loadOrderJson("testSetInclusivePricingInclTaxV3.json"),
            1499,
            1303.48,
            2606.96,
            195.52,
            391.04,
            true
        ];

        // Assert pricing with tax | discount | no shipping
        // Latest webhook
        yield [
            "testSetInclusivePricing",
            $meta,
            $this->loadOrderJson("testSetInclusivePricingInclTaxInclDiscountV3.json"),
            1499.00,
            1303.48,
            2346.26,
            195.52,
            351.94,
            true
        ];

        // Assert pricing with tax | discount | shipping
        // Latest webhook
        yield [
            "testSetInclusivePricing",
            $meta,
            $this->loadOrderJson("testSetInclusivePricingInclTaxInclDiscountInclShippingV3.json"),
            1499.00,
            1303.48,
            2346.26,
            195.52,
            351.94,
            false
        ];

        // Assert pricing with no tax | discount | no shipping
        // Latest webhook
        yield [
            "testSetInclusivePricing",
            $meta,
            $this->loadOrderJson("testSetInclusivePricingInclDiscountV3.json"),
            1499.00,
            1499,
            2698.20,
            0.00,
            0.00,
            true
        ];

        // Assert pricing with no tax | discount | shipping
        // Latest webhook
        yield [
            "testSetInclusivePricing",
            $meta,
            $this->loadOrderJson("testSetInclusivePricingInclDiscountInclShippingV3.json"),
            1499.00,
            1499,
            2698.20,
            0.00,
            0.00,
            false
        ];
    }

    /**
     * @dataProvider nonIntTaxRateDataProvider
     * @param $testName
     * @param $meta
     * @param $payload
     * @return void
     * @throws \stock2shop\exceptions\ResourceNotFound
     */
    public function testNonIntTaxRate($testName, $meta, $payload)
    {
        // Initiate test
        $this->initTransformTest($this->getName(), $meta);

        $transform = new OrderTransform();
        $result    = $transform->transform($payload);

        $this->assertEquals(14.5, (float)$result->system_order->line_items[0]->tax_lines[0]->rate, "Incorrect tax rate");
    }

    public function nonIntTaxRateDataProvider(): \Generator
    {

        // Assert non integer tax rate
        // Legacy webhook
        yield [
            "testNonIntTaxRate",
            [
                "meta" => [
                    "order_link_items_on_sku" => "true"
                ]
            ],
            $this->loadOrderJson("testNonIntTaxRate.json")
        ];

        // Latest webhook
        yield [
            "testNonIntTaxRateV3",
            [
                "meta" => [
                    "order_link_items_on_sku" => "true",
                    "api_webhook_version" => "3"
                ]
            ],
            $this->loadOrderJson("testNonIntTaxRateV3.json")
        ];
    }

    public function orderRemoveUnlinkedItemsGenerator(): \Generator
    {
        yield 'Meta set to false with products incorrectly linked' => [
            "file_name"        => "testOrderRemoveUnlinkedItems.json",
            "create_products" => [],
            "channel_meta"     => [
                "order_remove_unlinked_items" => "false"
            ],
            "expected"         => []
        ];
        yield 'Meta set to true with products incorrectly linked' => [
            "file_name"                => "testOrderRemoveUnlinkedItems.json",
            "create_products"         => [],
            "channel_meta"             => [
                "order_remove_unlinked_items" => "true"
            ],
            "expected_line_items_skus" => [
                "0" => "JSON-Product",
            ]
        ];
        yield 'Meta set to true with products incorrectly linked but found in database' => [
            "file_name"                => "testOrderRemoveUnlinkedItems.json",
            "create_products"         => [
                'title' => 'JSON-Product',
                'sku'   => 'JSON-Product',
            ],
            "channel_meta"             => [
                "order_remove_unlinked_items" => "true"
            ],
            "expected_line_items_skus" => [
                "0" => "JSON-Product",
            ]
        ];
    }

    /**
     * @dataProvider orderRemoveUnlinkedItemsGenerator
     */
    public function testOrderRemoveUnlinkedItems(
        string $file_name,
        array $create_products,
        array $channel_meta,
        array $expected_line_items_skus
    ) {
        // Initiate test
        list($app, $source, $channel, $product, $variant) = $this->initTransformTest(
            $this->getName(),
            [
                "meta" => $channel_meta
            ]
        );

        $orderData = $this->loadOrderJson($file_name);

        if (empty($expected_line_items_skus)) {
            // assert if an exception is raised
            $this->expectException(\Exception::class);
            $this->expectExceptionMessage("Woocommerce SKU JSON-Product is not found in Stock2Shop");
        }

        if (!empty($create_products)) {
            list($product, $variant) = tests\Utils::makeSimpleProduct(
                $source,
                $create_products['sku'],
                $create_products['title'],
                0
            );
        }

        $transform              = new OrderTransform();
        $transformedOrder       = $transform->transform($orderData);
        $array_transformed_order = json_decode(json_encode($transformedOrder), true);

        if (!empty($expected_line_items_skus)) {
            $this->assertEquals($expected_line_items_skus, array_column(
                $array_transformed_order['system_order']['line_items'],
                'sku'
            ));

            // assert that both the product_id and variant_id are set to null
            $this->assertNull($array_transformed_order['system_order']['line_items'][0]['product_id']);
            $this->assertNull($array_transformed_order['system_order']['line_items'][0]['variant_id']);
        }
    }

    /**
     * Test case tests:
     * - Latest webhook transform
     * - Line item discount
     * - Partial discount
     * - Tax Inclusive
     * @return void 
     */
    public function testOrderTransformLatestWithLineItemDiscountPartialDiscountInclTax()
    {
        // Initiate test
        list($app, $source, $channel, $product, $variant) = $this->initTransformTest(
            $this->getName(),
            [
                "meta" => [
                    "api_webhook_version"                   => "3",
                    "order_code_field"                      => "number",
                    OrderTransform::META_LINE_ITEM_DISCOUNT => "true"
                ]
            ]
        );

        $orderData = $this->loadOrderJson("testLineItemDiscountsWithV3HavingPartialDiscountAndTaxIncl.json");

        $transform               = new OrderTransform();
        $transformedOrder        = $transform->transform($orderData);

        // order level discount assertions
        $this->assertEqualsWithDelta(0, $transformedOrder->system_order->total_discount, self::PRICE_DELTA);
        $this->assertEqualsWithDelta(0, $transformedOrder->system_order->total_discount_incl, self::PRICE_DELTA);

        // assert the line item discount/price information
        $this->assertCount(2, $transformedOrder->system_order->line_items);
        $this->assertEquals(1303.48, $transformedOrder->system_order->line_items[0]->price);
        $this->assertEquals(1499, $transformedOrder->system_order->line_items[0]->price_incl);
        $this->assertEquals(130.35, $transformedOrder->system_order->line_items[0]->total_discount);
        $this->assertEquals(149.9, $transformedOrder->system_order->line_items[0]->total_discount_incl);
        $this->assertCount(1, $transformedOrder->system_order->line_items[0]->tax_lines);
        $this->assertEquals(15, $transformedOrder->system_order->line_items[0]->tax_lines[0]->rate);
        $this->assertEquals(195.52, round($transformedOrder->system_order->line_items[0]->tax_lines[0]->price, 2));
        $this->assertEquals("taxed", $transformedOrder->system_order->line_items[0]->tax_lines[0]->code);

        $this->assertEquals(1390.43, $transformedOrder->system_order->line_items[1]->price);
        $this->assertEquals(1599, $transformedOrder->system_order->line_items[1]->price_incl);
        $this->assertEquals(139.04, $transformedOrder->system_order->line_items[1]->total_discount);
        $this->assertEquals(159.9, $transformedOrder->system_order->line_items[1]->total_discount_incl);
        $this->assertCount(1, $transformedOrder->system_order->line_items[1]->tax_lines);
        $this->assertEquals(15, $transformedOrder->system_order->line_items[1]->tax_lines[0]->rate);
        $this->assertEquals(208.57, round($transformedOrder->system_order->line_items[1]->tax_lines[0]->price, 2));
        $this->assertEquals("taxed", $transformedOrder->system_order->line_items[1]->tax_lines[0]->code);
    }


    /**
     * Test case tests:
     * - Latest webhook transform
     * - Line item discount
     * - Full discount
     * - Tax Inclusive
     * @return void 
     */
    public function testOrderTransformLatestWithLineItemDiscountFullDiscountInclTax()
    {
        // Initiate test
        list($app, $source, $channel, $product, $variant) = $this->initTransformTest(
            $this->getName(),
            [
                "meta" => [
                    "api_webhook_version"                   => "3",
                    "order_code_field"                      => "number",
                    OrderTransform::META_LINE_ITEM_DISCOUNT => "true"
                ]
            ]
        );

        $orderData = $this->loadOrderJson("testLineItemDiscountsWithV3HavingAFullDiscountAndTaxIncl.json");

        $transform               = new OrderTransform();
        $transformedOrder        = $transform->transform($orderData);

        // order level discount assertions
        $this->assertEqualsWithDelta(0, $transformedOrder->system_order->total_discount, self::PRICE_DELTA);
        $this->assertEqualsWithDelta(0, $transformedOrder->system_order->total_discount_incl, self::PRICE_DELTA);

        // assert the line item discount/price information
        $this->assertCount(2, $transformedOrder->system_order->line_items);
        $this->assertEquals(1390.43, round($transformedOrder->system_order->line_items[0]->price, 2));
        $this->assertEquals(1599, $transformedOrder->system_order->line_items[0]->price_incl);
        $this->assertEquals(1390.43, round($transformedOrder->system_order->line_items[0]->total_discount, 2));
        $this->assertEquals(1599, $transformedOrder->system_order->line_items[0]->total_discount_incl);
        $this->assertCount(1, $transformedOrder->system_order->line_items[0]->tax_lines);
        $this->assertEquals(15, $transformedOrder->system_order->line_items[0]->tax_lines[0]->rate);
        $this->assertEquals(208.57, round($transformedOrder->system_order->line_items[0]->tax_lines[0]->price, 2));
        $this->assertEquals("taxed", $transformedOrder->system_order->line_items[0]->tax_lines[0]->code);

        $this->assertEquals(1303.48, round($transformedOrder->system_order->line_items[1]->price, 2));
        $this->assertEquals(1499, $transformedOrder->system_order->line_items[1]->price_incl);
        $this->assertEquals(1303.48, round($transformedOrder->system_order->line_items[1]->total_discount, 2));
        $this->assertEquals(1499, $transformedOrder->system_order->line_items[1]->total_discount_incl);
        $this->assertCount(1, $transformedOrder->system_order->line_items[1]->tax_lines);
        $this->assertEquals(15, $transformedOrder->system_order->line_items[1]->tax_lines[0]->rate);
        $this->assertEquals(195.52, $transformedOrder->system_order->line_items[1]->tax_lines[0]->price);
        $this->assertEquals("taxed", $transformedOrder->system_order->line_items[1]->tax_lines[0]->code);
    }

    /**
     * Test case tests:
     * - Latest webhook transform
     * - Line item discount
     * - Partial discount
     * - Tax Exclusive
     * @return void 
     */
    public function testOrderTransformLatestWithLineItemDiscountPartialDiscountExclTax()
    {
        // Initiate test
        list($app, $source, $channel, $product, $variant) = $this->initTransformTest(
            $this->getName(),
            [
                "meta" => [
                    "api_webhook_version"                   => "3",
                    "order_code_field"                      => "number",
                    OrderTransform::META_LINE_ITEM_DISCOUNT => "true"
                ]
            ]
        );

        $orderData = $this->loadOrderJson("testLineItemDiscountsWithV3HavingPartialDiscountAndTaxExcl.json");

        $transform               = new OrderTransform();
        $transformedOrder        = $transform->transform($orderData);

        // order level discount assertions
        $this->assertEqualsWithDelta(0, $transformedOrder->system_order->total_discount, self::PRICE_DELTA);
        $this->assertEqualsWithDelta(0, $transformedOrder->system_order->total_discount_incl, self::PRICE_DELTA);

        // assert the line item discount/price information
        $this->assertCount(2, $transformedOrder->system_order->line_items);
        $this->assertEquals(1499, $transformedOrder->system_order->line_items[0]->price);
        $this->assertEquals(1723.85, $transformedOrder->system_order->line_items[0]->price_incl);
        $this->assertEquals(149.9, $transformedOrder->system_order->line_items[0]->total_discount);
        $this->assertEqualsWithDelta(172.39, $transformedOrder->system_order->line_items[0]->total_discount_incl, self::PRICE_DELTA);
        $this->assertCount(1, $transformedOrder->system_order->line_items[0]->tax_lines);
        $this->assertEquals(15, $transformedOrder->system_order->line_items[0]->tax_lines[0]->rate);
        $this->assertEquals(224.85, $transformedOrder->system_order->line_items[0]->tax_lines[0]->price);
        $this->assertEquals("taxed", $transformedOrder->system_order->line_items[0]->tax_lines[0]->code);

        $this->assertEquals(1399, $transformedOrder->system_order->line_items[1]->price);
        $this->assertEquals(1608.85, $transformedOrder->system_order->line_items[1]->price_incl);
        $this->assertEquals(139.9, $transformedOrder->system_order->line_items[1]->total_discount);
        $this->assertEqualsWithDelta(160.88, $transformedOrder->system_order->line_items[1]->total_discount_incl, self::PRICE_DELTA);
        $this->assertCount(1, $transformedOrder->system_order->line_items[1]->tax_lines);
        $this->assertEquals(15, $transformedOrder->system_order->line_items[1]->tax_lines[0]->rate);
        $this->assertEquals(209.85, $transformedOrder->system_order->line_items[1]->tax_lines[0]->price);
        $this->assertEquals("taxed", $transformedOrder->system_order->line_items[1]->tax_lines[0]->code);
    }

    /**
     * Test case tests:
     * - Latest webhook transform
     * - Line item discount
     * - Full discount
     * - Tax Exclusive
     * @return void 
     */
    public function testOrderTransformLatestWithLineItemDiscountFullDiscountExclTax()
    {
        // Initiate test
        list($app, $source, $channel, $product, $variant) = $this->initTransformTest(
            $this->getName(),
            [
                "meta" => [
                    "api_webhook_version"                   => "3",
                    "order_code_field"                      => "number",
                    OrderTransform::META_LINE_ITEM_DISCOUNT => "true"
                ]
            ]
        );

        $orderData = $this->loadOrderJson("testLineItemDiscountsWithV3HavingAFullDiscountAndTaxExcl.json");

        $transform               = new OrderTransform();
        $transformedOrder        = $transform->transform($orderData);

        // order level discount assertions
        $this->assertEquals(0, $transformedOrder->system_order->total_discount);
        $this->assertEquals(0, $transformedOrder->system_order->total_discount_incl);

        // assert the line item discount/price information
        $this->assertCount(2, $transformedOrder->system_order->line_items);
        $this->assertEquals(1499, $transformedOrder->system_order->line_items[0]->price);
        $this->assertEquals(1723.85, $transformedOrder->system_order->line_items[0]->price_incl);
        $this->assertEquals(1499, $transformedOrder->system_order->line_items[0]->total_discount);
        $this->assertEqualsWithDelta(1723.85, $transformedOrder->system_order->line_items[0]->total_discount_incl, self::PRICE_DELTA);
        $this->assertCount(1, $transformedOrder->system_order->line_items[0]->tax_lines);
        $this->assertEquals(15, $transformedOrder->system_order->line_items[0]->tax_lines[0]->rate);
        $this->assertEquals(224.85, $transformedOrder->system_order->line_items[0]->tax_lines[0]->price);
        $this->assertEquals("taxed", $transformedOrder->system_order->line_items[0]->tax_lines[0]->code);

        $this->assertEquals(1399, $transformedOrder->system_order->line_items[1]->price);
        $this->assertEquals(1608.85, $transformedOrder->system_order->line_items[1]->price_incl);
        $this->assertEquals(1399, $transformedOrder->system_order->line_items[1]->total_discount);
        $this->assertEqualsWithDelta(1608.85, $transformedOrder->system_order->line_items[1]->total_discount_incl, self::PRICE_DELTA);
        $this->assertCount(1, $transformedOrder->system_order->line_items[1]->tax_lines);
        $this->assertEquals(15, $transformedOrder->system_order->line_items[1]->tax_lines[0]->rate);
        $this->assertEquals(209.85, $transformedOrder->system_order->line_items[1]->tax_lines[0]->price);
        $this->assertEquals("taxed", $transformedOrder->system_order->line_items[1]->tax_lines[0]->code);
    }

    /**
     * Test case tests:
     * - Legacy webhook transform
     * - Line item discount
     * - Full discount
     * - Tax Inclusive
     * @return void 
     */
    public function testOrderTransformLegacyWithLineItemDiscountFullDiscountInclTax()
    {
        // Initiate test
        list($app, $source, $channel, $product, $variant) = $this->initTransformTest(
            $this->getName(),
            [
                "meta" => [
                    "order_link_items_on_sku"               => "true",
                    "order_code_field"                      => "order_number",
                    OrderTransform::META_LINE_ITEM_DISCOUNT => "true"
                ]
            ]
        );

        $orderData = $this->loadOrderJson("testLineItemDiscountsWithLegacyHavingAFullDiscountAndTaxIncl.json");

        // create products
        foreach (["COD-MW2-PS5", "COD-MW2-PS4", "COD-MW2-XBSX"] as $sku) {
            list($product, $variant) = tests\Utils::makeSimpleProduct($source, $sku, $sku, 0);
        }

        $transform               = new OrderTransform();
        $transformedOrder        = $transform->transform($orderData);

        // order level discount assertions
        $this->assertEqualsWithDelta(0, $transformedOrder->system_order->total_discount, self::PRICE_DELTA);

        // assert the line item discount/price information
        $this->assertCount(2, $transformedOrder->system_order->line_items);
        $this->assertEquals(1390.43, round($transformedOrder->system_order->line_items[0]->price, 2));
        $this->assertEquals(1390.43, round($transformedOrder->system_order->line_items[0]->total_discount, 2));
        $this->assertEquals(1599, $transformedOrder->system_order->line_items[0]->total_discount_incl);
        $this->assertCount(1, $transformedOrder->system_order->line_items[0]->tax_lines);
        $this->assertEquals(15, $transformedOrder->system_order->line_items[0]->tax_lines[0]->rate);
        $this->assertEquals(208.57, round($transformedOrder->system_order->line_items[0]->tax_lines[0]->price, 2));
        $this->assertEquals("taxed", $transformedOrder->system_order->line_items[0]->tax_lines[0]->code);

        $this->assertEquals(1303.48, $transformedOrder->system_order->line_items[1]->price);
        $this->assertEquals(1303.48, $transformedOrder->system_order->line_items[1]->total_discount);
        $this->assertEquals(1499, $transformedOrder->system_order->line_items[1]->total_discount_incl);
        $this->assertCount(1, $transformedOrder->system_order->line_items[1]->tax_lines);
        $this->assertEquals(15, $transformedOrder->system_order->line_items[1]->tax_lines[0]->rate);
        $this->assertEquals(195.52, round($transformedOrder->system_order->line_items[1]->tax_lines[0]->price, 2));
        $this->assertEquals("taxed", $transformedOrder->system_order->line_items[1]->tax_lines[0]->code);
    }

    /**
     * Test case tests:
     * - Legacy webhook transform
     * - Line item discount
     * - Partial discount
     * - Tax Inclusive
     * @return void 
     */
    public function testOrderTransformLegacyWithLineItemDiscountPartialDiscountInclTax()
    {
        // Initiate test
        list($app, $source, $channel, $product, $variant) = $this->initTransformTest(
            $this->getName(),
            [
                "meta" => [
                    "order_link_items_on_sku"               => "true",
                    "order_code_field"                      => "order_number",
                    OrderTransform::META_LINE_ITEM_DISCOUNT => "true"
                ]
            ]
        );

        $orderData = $this->loadOrderJson("testLineItemDiscountsWithLegacyHavingPartialDiscountAndTaxIncl.json");

        // create products
        foreach (["COD-MW2-PS5", "COD-MW2-PS4", "COD-MW2-XBSX"] as $sku) {
            list($product, $variant) = tests\Utils::makeSimpleProduct($source, $sku, $sku, 0);
        }

        $transform               = new OrderTransform();
        $transformedOrder        = $transform->transform($orderData);

        // order level discount assertions
        $this->assertEqualsWithDelta(0, $transformedOrder->system_order->total_discount, self::PRICE_DELTA);

        // assert the line item discount/price information
        $this->assertCount(2, $transformedOrder->system_order->line_items);
        $this->assertEquals(1303.48, round($transformedOrder->system_order->line_items[0]->price, 2));
        $this->assertEquals(130.35, round($transformedOrder->system_order->line_items[0]->total_discount, 2));
        $this->assertEquals(149.9, $transformedOrder->system_order->line_items[0]->total_discount_incl);
        $this->assertCount(1, $transformedOrder->system_order->line_items[0]->tax_lines);
        $this->assertEquals(15, $transformedOrder->system_order->line_items[0]->tax_lines[0]->rate);
        $this->assertEquals(195.52, round($transformedOrder->system_order->line_items[0]->tax_lines[0]->price, 2));
        $this->assertEquals("taxed", $transformedOrder->system_order->line_items[0]->tax_lines[0]->code);

        $this->assertEquals(1390.43, $transformedOrder->system_order->line_items[1]->price);
        $this->assertEquals(139.04, $transformedOrder->system_order->line_items[1]->total_discount);
        $this->assertEquals(159.9, $transformedOrder->system_order->line_items[1]->total_discount_incl);
        $this->assertCount(1, $transformedOrder->system_order->line_items[1]->tax_lines);
        $this->assertEquals(15, $transformedOrder->system_order->line_items[1]->tax_lines[0]->rate);
        $this->assertEquals(208.57, round($transformedOrder->system_order->line_items[1]->tax_lines[0]->price, 2));
        $this->assertEquals("taxed", $transformedOrder->system_order->line_items[1]->tax_lines[0]->code);
    }

    /**
     * Test case tests:
     * - Legacy webhook transform
     * - Line item discount
     * - Full discount
     * - Tax Exclusive
     * @return void 
     */
    public function testOrderTransformLegacyWithLineItemDiscountFullDiscountExclTax()
    {
        // Initiate test
        list($app, $source, $channel, $product, $variant) = $this->initTransformTest(
            $this->getName(),
            [
                "meta" => [
                    "order_link_items_on_sku"               => "true",
                    "order_code_field"                      => "order_number",
                    OrderTransform::META_LINE_ITEM_DISCOUNT => "true"
                ]
            ]
        );

        $orderData = $this->loadOrderJson("testLineItemDiscountsWithLegacyHavingAFullDiscountAndTaxExcl.json");

        // create products
        foreach (["COD-MW2-PS5", "COD-MW2-PS4", "COD-MW2-XBSX"] as $sku) {
            list($product, $variant) = tests\Utils::makeSimpleProduct($source, $sku, $sku, 0);
        }

        $transform               = new OrderTransform();
        $transformedOrder        = $transform->transform($orderData);

        // order level discount assertions
        $this->assertEquals(0, $transformedOrder->system_order->total_discount);

        // assert the line item discount/price information
        $this->assertCount(2, $transformedOrder->system_order->line_items);
        $this->assertEquals(1499, round($transformedOrder->system_order->line_items[0]->price, 2));
        $this->assertEquals(1499, $transformedOrder->system_order->line_items[0]->total_discount);
        $this->assertEquals(1723.85, $transformedOrder->system_order->line_items[0]->total_discount_incl);
        $this->assertCount(1, $transformedOrder->system_order->line_items[0]->tax_lines);
        $this->assertEquals(15, $transformedOrder->system_order->line_items[0]->tax_lines[0]->rate);
        $this->assertEquals(224.85, round($transformedOrder->system_order->line_items[0]->tax_lines[0]->price, 2));
        $this->assertEquals("taxed", $transformedOrder->system_order->line_items[0]->tax_lines[0]->code);

        $this->assertEquals(1399, $transformedOrder->system_order->line_items[1]->price);
        $this->assertEquals(1399, $transformedOrder->system_order->line_items[1]->total_discount);
        $this->assertEquals(1723.85, $transformedOrder->system_order->line_items[0]->total_discount_incl);
        $this->assertCount(1, $transformedOrder->system_order->line_items[1]->tax_lines);
        $this->assertEquals(15, $transformedOrder->system_order->line_items[1]->tax_lines[0]->rate);
        $this->assertEquals(209.85, round($transformedOrder->system_order->line_items[1]->tax_lines[0]->price, 2));
        $this->assertEquals("taxed", $transformedOrder->system_order->line_items[1]->tax_lines[0]->code);
    }

    /**
     * Test case tests:
     * - Legacy webhook transform
     * - Line item discount
     * - Partial discount
     * - Tax Exclusive
     * @return void 
     */
    public function testOrderTransformLegacyWithLineItemDiscountPartialDiscountExclTax()
    {
        // Initiate test
        list($app, $source, $channel, $product, $variant) = $this->initTransformTest(
            $this->getName(),
            [
                "meta" => [
                    "order_link_items_on_sku"               => "true",
                    "order_code_field"                      => "order_number",
                    OrderTransform::META_LINE_ITEM_DISCOUNT => "true"
                ]
            ]
        );

        $orderData = $this->loadOrderJson("testLineItemDiscountsWithLegacyHavingPartialDiscountAndTaxExcl.json");

        // create products
        foreach (["COD-MW2-PS5", "COD-MW2-PS4", "COD-MW2-XBSX"] as $sku) {
            list($product, $variant) = tests\Utils::makeSimpleProduct($source, $sku, $sku, 0);
        }

        $transform               = new OrderTransform();
        $transformedOrder        = $transform->transform($orderData);

        // order level discount assertions
        $this->assertEquals(0, $transformedOrder->system_order->total_discount);

        // assert the line item discount/price information
        $this->assertCount(2, $transformedOrder->system_order->line_items);
        $this->assertEquals(1499, round($transformedOrder->system_order->line_items[0]->price, 2));
        $this->assertEquals(149.9, $transformedOrder->system_order->line_items[0]->total_discount);
        $this->assertEquals(172.39, round($transformedOrder->system_order->line_items[0]->total_discount_incl, 2));
        $this->assertCount(1, $transformedOrder->system_order->line_items[0]->tax_lines);
        $this->assertEquals(15, $transformedOrder->system_order->line_items[0]->tax_lines[0]->rate);
        $this->assertEquals(224.85, round($transformedOrder->system_order->line_items[0]->tax_lines[0]->price, 2));
        $this->assertEquals("taxed", $transformedOrder->system_order->line_items[0]->tax_lines[0]->code);

        $this->assertEquals(1399, $transformedOrder->system_order->line_items[1]->price);
        $this->assertEquals(139.9, $transformedOrder->system_order->line_items[1]->total_discount);
        $this->assertEquals(160.88, round($transformedOrder->system_order->line_items[1]->total_discount_incl, 2));
        $this->assertCount(1, $transformedOrder->system_order->line_items[1]->tax_lines);
        $this->assertEquals(15, $transformedOrder->system_order->line_items[1]->tax_lines[0]->rate);
        $this->assertEquals(209.85, round($transformedOrder->system_order->line_items[1]->tax_lines[0]->price, 2));
        $this->assertEquals("taxed", $transformedOrder->system_order->line_items[1]->tax_lines[0]->code);
    }
}
