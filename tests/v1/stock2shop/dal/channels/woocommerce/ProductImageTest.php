<?php

namespace tests\v1\stock2shop\dal\channels\woocommerce;

use RedBean_Facade as R;
use stock2shop\dal\channels\woocommerce;
use tests;
use tests\Utils;

final class ProductImageTest extends tests\TestCase
{

    public function testAddProductImageWithProxy(){

        $testName = $this->getName();
        $client_id = 21;

        //create channel
        /** @var \RedBean_OODBBean|\stock2shop\dal\system\channel $channel */

        // Setup app.
        $app = Utils::stubApp();
        $env = $app->environment();

        // Client.
        $client = R::load('client', $client_id);
        $env['client']  = $client;

        // Source
        /** @var \RedBean_OODBBean|\stock2shop\dal\system\source $source */
        $source = R::dispense('source');
        $source->client_id   = $client_id;
        $source->description = $testName;
        $source->type        = 'sageone';
        $source->active      = true;
        R::store($source);
        $env['source'] = $source;

        // Channel
        /** @var \RedBean_OODBBean|\stock2shop\dal\system\channel $channel */
        $channel = R::dispense('channel');
        $channel->client_id   = $client_id;
        $channel->description = $testName;
        $channel->type        = 'woocommerce';
        $channel->active      = true;
        R::store($channel);
        $env['channel'] = $channel;

        // Channel meta.
        Utils::addChannelMeta([
            'consumer_key' => 'ck_123',
            'consumer_secret' => 'cs_123',
            'api_url' => 'https://example.com/wc-api/v3',
            'authentication' => 'query_params',
            'create_image_enabled' => 'true'
        ], $channel);

        //create product
        /** @var \RedBean_OODBBean|\stock2shop\dal\system\product $product */
        $product = R::dispense('product');
        $product->client_id = $client_id;
        $product->source_id = $source->id;
        $product->active = true;
        $product->title = $testName;
        $product->source_product_code = $testName;
        /** @var \RedBean_OODBBean|\stock2shop\dal\system\channel_product $channel_product */
        $channel_product = $product->link(
            "channel_product", array(
                "channel_product_code" => '123',
                "client_id" => $client_id
            )
        );
        $channel_product->channel = $channel;
        $channel_product->active = true;
        R::store($product);

        //create product image
        /** @var \RedBean_OODBBean|\stock2shop\dal\system\image $productImage */
        $productImage = R::dispense('image');
        $productImage->client_id = $client_id;
        $productImage->storage_code = 'abc123.jpg';
        $productImage->src = 'someS3Image.jpg';
        $productImage->product_id = $product->id;
        R::store($productImage);

        //create variant
        /** @var \RedBean_OODBBean|\stock2shop\dal\system\variant $variant */
        $variant = R::dispense('variant');
        $variant->client_id = $client_id;
        $variant->active = true;
        $variant->source_variant_code = $testName . 'source_variant_code';
        $variant->sku = $testName . 'sku';
        $variant->price = 100;
        $variant->qty = 100;
        $variant->grams = 0;
        $variant->product_id = $product->id;
        $variant->image_id = $productImage->id;
        R::store($variant);

        // Create mock proxy
        $Proxy = $this->getMockBuilder(woocommerce\Proxy::class)
                      ->setConstructorArgs(["products/123"]);

        $productImage = new woocommerce\ProductImage();

        // This is the Stub used by getMockBuilder in teh response of proxy
        $body = '{"product":{"images":[{"id":"100","src":"someS3Image.jpg","title":"","position":0}]}}';
        $Proxy = $Proxy->getMock();

        // Proxy response test
        $Proxy->expects($this->once())
            ->method('putRaw')
            ->willReturn(json_decode($body));

        $addProductImage = $productImage->addProductImageWithProxy(
            '123',
            '{"image":{"src":"https://somes3server.com/someS3Image.jpg","product_id":'.$product->id.',"variant_id":'.$variant->id.',"storage_code":"someS3Image.jpg"}}',
            $Proxy
        );

        // Assert image ID has been set
        $this->assertEquals(
            100,
            $addProductImage->image->id
        );
    }
}