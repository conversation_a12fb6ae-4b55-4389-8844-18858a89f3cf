<?php

namespace tests\v1\stock2shop\dal\channels\woocommerce;

use stock2shop\dal\channels\woocommerce;
use stock2shop\exceptions\UnprocessableEntity;
use stock2shop\vo;
use tests;

class ProductTransformTest extends tests\TestCase
{
    /**
     * @dataProvider setAttributesSlugDataProvider
     * @param array $payload
     * @param string $expected
     * @return void
     * @throws UnprocessableEntity
     */
    public function testSetAttributesSlug(array $payload, string $expected)
    {
        $channel = new vo\Channel([
            "id" => 1,
            "meta" => [
                [
                    "key" => "set_attributes_slug",
                    "value" => $payload['set_attributes_slug']
                ]
            ]
        ]);
        $transform = new woocommerce\ProductTransform();
        $transform->setAttributesSlug($payload['attribute'], $channel);

        $this->assertEquals(
            $payload['set_attributes_slug'] === "true",
            array_key_exists('slug', $payload['attribute'])
        );
        if ($payload['set_attributes_slug'] === "true") {
            $this->assertEquals($expected, $payload['attribute']['slug']);
        }
    }

    public static function setAttributesSlugDataProvider(): \Generator
    {
        yield "Ignore slug" => [
            [
                "set_attributes_slug" => "false",
                "attribute" => [
                    "name" => "Foo Bar"
                ]
            ],
            ""
        ];

        yield "Slug with spaces and hyphen" => [
            [
                "set_attributes_slug" => "true",
                "attribute" => [
                    "name" => "Foo Bar - Baz"
                ]
            ],
            "foo_bar_-_baz"
        ];

        yield "Slug with multiple spaces & hyphen" => [
            [
                "set_attributes_slug" => "true",
                "attribute" => [
                    "name" => "Foo    - Bar"
                ]
            ],
            "foo_-_bar"
        ];

        yield "Slug with special characters" => [
            [
                "set_attributes_slug" => "true",
                "attribute" => [
                    "name" => "Foo!@#$%^&*()+{}:\"'<>/\_Bar"
                ]
            ],
            "foo_bar"
        ];
    }
}
