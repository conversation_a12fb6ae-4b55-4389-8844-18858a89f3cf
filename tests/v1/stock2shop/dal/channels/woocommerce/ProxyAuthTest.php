<?php

namespace tests\v1\stock2shop\dal\channels\woocommerce;

use stock2shop\dal\channels\woocommerce;
use tests;

final class ProxyAuthTest extends tests\TestCase
{

    public function testBasicAuth(){

        $this->init('basic');

        $proxy = new woocommerce\Proxy('products');

        tests\Utils::invokePrivateMethod($proxy, 'setUrlAndMethodAndAuth', ['GET']);

        $this->assertEquals('https://ck_123:<EMAIL>/wc-api/v3/products', $proxy->url);
    }

    public function testBasicAuthWithParams(){

        $this->init('basic');

        $proxy = new woocommerce\Proxy('products', ["filter[sku]" => "BARBIE"]);

        tests\Utils::invokePrivateMethod($proxy, 'setUrlAndMethodAndAuth', ['GET']);

        $this->assertEquals('https://ck_123:<EMAIL>/wc-api/v3/products?filter%5Bsku%5D=BARBIE', $proxy->url);
    }

    public function testQueryParamsAuth(){

        $this->init('query_params');

        $proxy = new woocommerce\Proxy('products');

        tests\Utils::invokePrivateMethod($proxy, 'setUrlAndMethodAndAuth', ['GET']);

        $this->assertEquals('https://example.com/wc-api/v3/products?consumer_key=ck_123&consumer_secret=cs_123', $proxy->url);
    }

    public function testQueryParamsWithParams(){

        $this->init('query_params');

        $proxy = new woocommerce\Proxy('products', ["filter[sku]" => "BARBIE"]);

        tests\Utils::invokePrivateMethod($proxy, 'setUrlAndMethodAndAuth', ['GET']);

        $this->assertEquals('https://example.com/wc-api/v3/products?filter%5Bsku%5D=BARBIE&consumer_key=ck_123&consumer_secret=cs_123', $proxy->url);
    }

    public function testOAuth1Auth(){

        $this->init('oauth1');

        $proxy = new woocommerce\Proxy('products');

        $proxy->nonce = "123";
        $proxy->timestamp = 123;

        tests\Utils::invokePrivateMethod($proxy, 'setUrlAndMethodAndAuth', ['GET']);

        $this->assertEquals('https://example.com/wc-api/v3/products?'.
            'oauth_consumer_key=ck_123&oauth_nonce=123&oauth_signature_method=HMAC-SHA1&'.
            'oauth_timestamp=123&oauth_version=1.0&'.
            'oauth_signature=joHn69mHZ0po7Ak1Enf7JypdzAo%3D', $proxy->url);
    }

    public function testOAuth1WithParams(){

        $this->init('oauth1');

        $proxy = new woocommerce\Proxy('products', ["filter[sku]" => "BARBIE"]);

        $proxy->nonce = "123";
        $proxy->timestamp = 123;

        tests\Utils::invokePrivateMethod($proxy, 'setUrlAndMethodAndAuth', ['GET']);

        $this->assertEquals('https://example.com/wc-api/v3/products?filter%5Bsku%5D=BARBIE&'.
            'oauth_consumer_key=ck_123&oauth_nonce=123&oauth_signature_method=HMAC-SHA1&'.
            'oauth_timestamp=123&oauth_version=1.0&oauth_signature=PgQIGB4FUISiiK%2FvhFGlVuAMIqU%3D', $proxy->url);
    }

    public function testOAuth1PUT(){

        $this->init('oauth1');

        $proxy = new woocommerce\Proxy('products');

        $proxy->nonce = "123";
        $proxy->timestamp = 123;

        tests\Utils::invokePrivateMethod($proxy, 'setUrlAndMethodAndAuth', ['PUT']);

        $this->assertEquals('https://example.com/wc-api/v3/products?'.
            'oauth_consumer_key=ck_123&oauth_nonce=123&oauth_signature_method=HMAC-SHA1&'.
            'oauth_timestamp=123&oauth_version=1.0&'.
            'oauth_signature=jtBeiucoLVGXpStW%2Fx4DHUFNJLE%3D', $proxy->url);
    }

    public function testOAuth1GeneratesNonceAndTimestamp(){

        $this->init('oauth1');

        $proxy = new woocommerce\Proxy('products', ["filter[sku]" => "BARBIE"]);

        tests\Utils::invokePrivateMethod($proxy, 'setUrlAndMethodAndAuth', ['GET']);

        $query = parse_url($proxy->url, PHP_URL_QUERY);
        parse_str($query, $params);

        $this->assertArrayHasKey('oauth_nonce', $params);
        $this->assertArrayHasKey('oauth_timestamp', $params);
        $this->assertLessThan(1000, time()-$params['oauth_timestamp']);
        $this->assertGreaterThan(8, strlen($params['oauth_signature']));
        $this->assertEquals($params['filter']['sku'], "BARBIE");

    }

    /**
     * @param string $auth
     * @return void
     * @throws \stock2shop\exceptions\BadRequest
     * @throws \stock2shop\exceptions\Forbidden
     * @throws \stock2shop\exceptions\ResourceNotFound
     */
    private function init(string $auth)
    {
        list($app, $client, $source, $channel) = tests\Utils::makeSageOneSourceWoocommerceChannel($this->getName());

        // Channel meta.
        tests\Utils::addChannelMeta([
            'consumer_key' => 'ck_123',
            'consumer_secret' => 'cs_123',
            'api_url' => 'https://example.com/wc-api/v3',
            'authentication' => $auth,
        ], $channel);
    }
}
