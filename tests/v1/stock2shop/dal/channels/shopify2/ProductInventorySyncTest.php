<?php

namespace tests\v1\stock2shop\dal\channels\shopify2;

use GuzzleHttp\Client;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Middleware;
use GuzzleHttp\Psr7\Response;

use stock2shop\dal\channels\shopify2\products\InventorySetQuantitiesInput;
use stock2shop\dal\channel\ProductsSync;
use stock2shop\dal\channels\shopify2\products\SyncResults;
use stock2shop\dal\channels\shopify2\lib\Mutate;
use stock2shop\dal\channels\shopify2\Configuration;
use tests\{DB,Generator};
use stock2shop\dal\channels\shopify2\products\ProductVariant;
use stock2shop\dal\channels\shopify2\Utils;
use stock2shop\vo\{Config};
use stock2shop\repository\ChannelProductsRepository;
use stock2shop\repository\ProductsRepository;

use stock2shop\dal\channels\shopify2;
use stock2shop\dal\connector\Factory;


class ProductInventorySyncTest  extends \tests\TestCase
{

    const SHOPIFY_LOCATION_ID = '************';
    const NUM_SKUS = 1;
    const TEST_SKU = 'COD-MW2-PS4';

    const ACCESS_TOKEN = '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';
    const SOURCE_ID = 1;
    const CHANNEL_ID = 2;
    const FULFILLMENTSERVICE_ID = 3;
    const CLIENT_ID = 1;

    // this config should be valid for our amazon test developer account.
    const CONFIG = [
        'client_id'           => self::CLIENT_ID,
        'meta'                => [
            [
                'key'   => 'notifications',
                'value' => '{"user":{"name":"Mr Me","email":"<EMAIL>"}}'
            ]
        ],
        'sources'             => [
            [
                'id'   => self::SOURCE_ID,
                'type' => Config::CONNECTOR_TYPE_TEST_SOURCE
            ]
        ],
        'channels'            => [
            [
                'id'          => self::CHANNEL_ID,
                'description' => 'shopify2',
                'type'        => Config::CONNECTOR_TYPE_SHOPIFY2,
                'meta'        => [
                    [
                        'key'   => shopify2\Configuration::DOMAIN,
                        'value' => 'xyz'
                    ]
                ]
            ]
        ],
        'fulfillmentservices' => [
            [
                'id'          => self::FULFILLMENTSERVICE_ID,
                'description' => 'test_fulfillmentservice',
                'type'        => 'test_fulfillmentservice'
            ]
        ]
    ];
    /** @var Response[] */
    private $responses = [];


    protected function init(array $additionalChannelMeta = []): Config
    {
        $data = self::CONFIG;
        foreach ($additionalChannelMeta as $k => $v) {
            $data['channels'][0]['meta'][] = [
                'key'   => $k,
                'value' => $v
            ];
        }
        $config = new Config($data);

        // insert secret for access token
        DB::makeSecrets($config, Config::CONNECTOR_CHANNEL, self::CHANNEL_ID, [
            'access_token' => self::ACCESS_TOKEN
        ]);

        Factory::clear();
        Factory::initConnector($config, Config::CONNECTOR_CHANNEL, self::CHANNEL_ID);
        shopify2\Configuration::getInstance(true)->init(
            $config,
            Config::CONNECTOR_CHANNEL,
            self::CHANNEL_ID
        );
        $client = $this->getMockGuzzleClient();

        $configuration = Configuration::getInstance();
        $configuration->setDomain('s2s-svdh');
        $configuration->setGuzzleClient($client);
        $configuration->setUseStockOnHandBucket(true);

        $config = DB::makeFromConfig($config);

        return $config;
    }

    private function getMockGuzzleClient(): Client {

        $responses = [
            new Response(200, [], file_get_contents(__DIR__ . '/lib/mocks/product_variants.json')),
            new Response(200, [], file_get_contents(__DIR__ . '/lib/mocks/inventory_update_response.json'))
        ];
        $mock         = new MockHandler($responses);
        $handlerStack = HandlerStack::create($mock);
        $history      = Middleware::history($this->responses);
        $handlerStack->push($history);
        $client = new Client([
            'handler' => $handlerStack,
        ]);
        return $client;
    }

    public function testProductInventoryUseStockOnHandBucket()
    {

        $config = $this->init([
            Configuration::PRODUCT_FIELD_LOCATION_PREFIX . self::SHOPIFY_LOCATION_ID => 'qty_on_hand'
        ]);
        $channelProducts = Generator::generateChannelProducts($config, self::NUM_SKUS, self::SOURCE_ID, self::CHANNEL_ID);
        $channelProducts[0]['variants'][0]['sku'] = self::TEST_SKU;
        foreach ($channelProducts as &$product) {
            foreach ($product['variants'] as &$variant) {
                $variant['qty'] = 3;
                $variant['qty_availability'] = [
                    [
                        'description' => 'on_hand',
                        'qty'         => 50
                    ],
                    [
                        'description' => 'committed_ecomm',
                        'qty'         => 5
                    ],
                    [
                        'description' => 'committed_other',
                        'qty'         => 9
                    ],
                    [
                        'description' => 'ecomm_available',
                        'qty'         => 41
                    ]                        
                ];
            }
        }

        DB::makeProducts($config, 2, self::SOURCE_ID, $channelProducts);

        $pRepo = new ProductsRepository($config);
        $pRepo->loadFromSourceProductCodes(self::SOURCE_ID, [
            'source_product_code-0']);
        $systemProducts = $pRepo->system_products;

        $variantToAssert = $systemProducts['source_product_code-0']->variants[0];
        $this->assertObjectHasAttribute('qty_availability', $variantToAssert);
        $this->assertCount(4, $variantToAssert->qty_availability);
        $qtyToSend = 0;
        foreach ($variantToAssert->qty_availability as $qty) {
            if ($qty->description === 'on_hand') {
                $qtyToSend = $qty->qty;
                break;
            }
        }
        $this->assertEquals(50, $qtyToSend);

        $cpRepo = new ChannelProductsRepository($config);
        $cpRepo->loadFromSystemProducts(
            [self::CHANNEL_ID],
            $systemProducts
        );
        $updatedChannelProjects = $cpRepo->getChannelProducts();
        $createdSkus = [];
        foreach ($updatedChannelProjects as $cp) {
            foreach ($cp->variants as $variant) {
                $createdSkus[] = $variant->sku;
            }
        }        

        $productSync = new ProductsSync($updatedChannelProjects, $config, self::CHANNEL_ID);
        $syncResults = new SyncResults($productSync->channel_products);
        Utils::initShopifyAPI();
        $p = ProductVariant::getBySkus($createdSkus, self::NUM_SKUS);
        $syncResults->setShopifyProductVariants($p);

        $variants = $syncResults->getShopifyProductVariants();

        $input = InventorySetQuantitiesInput::make($productSync, $variants);
        $this->assertEquals('on_hand', $input->name);
        $this->assertEquals(50, $input->quantities[0]->quantity);
        $mutatedInput = Mutate::inventorySetQuantities($input);
        $syncResults->setResultsFromInventorySetQuantities($mutatedInput);

        $this->assertEquals(200, $this->responses[0]['response']->getStatusCode());
        $this->assertEquals(200, $this->responses[1]['response']->getStatusCode());
    }
}
