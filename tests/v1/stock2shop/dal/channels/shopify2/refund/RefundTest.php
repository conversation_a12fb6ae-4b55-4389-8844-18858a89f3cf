<?php

namespace tests\v1\stock2shop\dal\channels\shopify2\refund;

use Exception;
use stock2shop\businesslogic\OrderLogic;
use stock2shop\dal\channel\RefundPost;
use stock2shop\dal\channels\shopify2\lib\models;
use stock2shop\dal\channels\shopify2\refund\Transform;
use stock2shop\exceptions\ServerError;
use stock2shop\exceptions\UnprocessableEntity;
use stock2shop\exceptions\Validation;
use stock2shop\vo;
use tests\DB;
use tests\Generator;
use tests\v1\stock2shop\dal\channels\shopify2\TestCase;

class RefundTest extends TestCase
{
    /**
     * @dataProvider setOutGoingRefundDataProvider
     * @return void
     * @throws Exception
     */
    public function testSetOutgoingRefund(RefundPost $post, models\Order $shopify_order)
    {
        Transform::setDoneFromSuggestedRefund($post, $shopify_order);

        $this->assertEquals(199.95, $post->channelRefund->refund->line_items[0]->unit_amount_incl);
        $this->assertEquals(199.95, $post->channelRefund->refund->line_items[0]->subtotal_incl);
        $this->assertEquals(26.08, $post->channelRefund->refund->line_items[0]->subtotal_tax);

        $this->assertCount(1, $post->channelRefund->refund->transactions);
        $this->assertEquals(199.95, $post->channelRefund->refund->transactions[0]->amount);
        $this->assertEquals(
            $shopify_order->suggestedRefund->suggestedTransactions[0]->paymentDetails->bin,
            $post->channelRefund->refund->transactions[0]->card_bin
        );
        $this->assertEquals(
            $shopify_order->suggestedRefund->suggestedTransactions[0]->gateway,
            $post->channelRefund->refund->transactions[0]->gateway
        );
        $this->assertEquals(
            $shopify_order->suggestedRefund->suggestedTransactions[0]->paymentDetails->number,
            $post->channelRefund->refund->transactions[0]->payment_code);
        $this->assertEquals(
            $shopify_order->suggestedRefund->suggestedTransactions[0]->paymentDetails->paymentMethodName,
            $post->channelRefund->refund->transactions[0]->payment_method
        );
        if (count($shopify_order->suggestedRefund->refundLineItems) == 2) {
            $this->assertEquals(
                $shopify_order->suggestedRefund->shipping->amountSet->shopMoney,
                $post->channelRefund->refund->line_items[1]->unit_amount_incl
            );
        }
    }

    /**
     * @throws UnprocessableEntity
     * @throws ServerError
     * @throws Validation
     */
    public function setOutGoingRefundDataProvider(): \Generator
    {
        yield 'no shipping' => $this->getRefundTestData(false);
        yield 'with shipping' => $this->getRefundTestData(true);
    }

    /**
     * @throws UnprocessableEntity
     * @throws ServerError
     * @throws Validation
     * @throws Exception
     */
    private function getRefundTestData(bool $refund_shipping): array
    {
        $config = DB::makeFromConfig(new vo\Config(self::CONFIG));
        $order  = DB::makeOrder($config, Generator::generateOrder($config, self::CHANNEL_ID));
        self::addRefundToOrder($order, $refund_shipping);
        $post = new RefundPost(
            $config,
            $order,
            new vo\ChannelRefund(OrderLogic::getRefundToPostToChannel($order))
        );

        if ($refund_shipping) {
            $shopifyOrder = new models\Order(json_decode(
                file_get_contents(__DIR__ . '/mocks/refund_with_shipping.json'),
                true
            ));
        } else {
            $shopifyOrder = new models\Order(json_decode(
                file_get_contents(__DIR__ . '/mocks/refund_no_shipping.json'),
                true
            ));
        }


        return [$post, $shopifyOrder];
    }
}