<?php

namespace tests\v1\stock2shop\dal\channels\shopify2;

use Generator;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Middleware;
use GuzzleHttp\Psr7\Response;
use stock2shop\dal\channels\shopify2\Client;
use stock2shop\dal\channels\shopify2\lib\Api;
use stock2shop\dal\channels\shopify2\ClientAuthMiddleware;
use stock2shop\dal\oauth2\Constants;
use stock2shop\dal\oauth2\RateLimitRetryMiddleware;
use stock2shop\dal\channels\takealot;
use tests;

class ClientTest extends tests\TestCase
{
    const TEST_SHOPIFY_DOMAIN = 'tester';

    /**
     * @dataProvider retryMiddlewareDataProvider
     * @throws GuzzleException
     */
    public function testRetryMiddleware(array $responses, int $http_response_code)
    {

        // create new mock handler to simulate responses
        $mock         = new MockHandler($responses);
        $handlerStack = HandlerStack::create($mock);

        // create container to store history of requests
        $container = [];
        $history   = Middleware::history($container);

        // push history to handler stack
        $handlerStack->push($history);
        $handlerStack->push(new RateLimitRetryMiddleware(
            takealot\Config::RETRY_RESPONSE_CODES,
            Constants::DEFAULT_RETRY_LIMIT,
            Constants::DEFAULT_RETRY_DELAY_MS
        ));

        // create new client with handler stack and retry middleware - no auth middleware
        $client = new Client(new ClientAuthMiddleware(''), $handlerStack);

        // make request to shopify2 (mocked) and record response
        $response = $client->request(
            'POST',
            sprintf(
                Api::BASE_URL_TEMPLATE . Api::GRAPHQL_API_ROUTE,
                self::TEST_SHOPIFY_DOMAIN,
                Api::API_VERSION_2024_10
            ),
            [
                'json' => ['query' => '']
            ]
        );

        // assert that the response status code is equal to the expected status code
        $this->assertEquals($http_response_code, $response->getStatusCode());
    }

    /** 
     * @return Generator 
     */
    public function retryMiddlewareDataProvider(): Generator
    {
        yield '200' => [
            'response'    => [
                new Response(200, ['Content-Type' => 'application/json'], json_encode(200))
            ],
            'status_code' => 200
        ];

        yield '429' => [
            'response'    => [
                new Response(429, ['Content-Type' => 'application/json'], json_encode([429])),
                new Response(200, ['Content-Type' => 'application/json'], json_encode([200]))
            ],
            'status_code' => 200
        ];

        yield '502' => [
            'response'    => [
                new Response(502, ['Content-Type' => 'application/json'], json_encode([502])),
                new Response(200, ['Content-Type' => 'application/json'], json_encode([200]))
            ],
            'status_code' => 200
        ];

        yield '503' => [
            'response'    => [
                new Response(503, ['Content-Type' => 'application/json'], json_encode([503])),
                new Response(200, ['Content-Type' => 'application/json'], json_encode([200]))
            ],
            'status_code' => 200
        ];

        yield '504' => [
            'response'    => [
                new Response(504, ['Content-Type' => 'application/json'], json_encode([504])),
                new Response(200, ['Content-Type' => 'application/json'], json_encode([200]))
            ],
            'status_code' => 200
        ];

        yield '522' => [
            'response'    => [
                new Response(522, ['Content-Type' => 'application/json'], json_encode([522])),
                new Response(200, ['Content-Type' => 'application/json'], json_encode([200]))
            ],
            'status_code' => 200
        ];
    }
}