<?php

namespace tests\v1\stock2shop\dal\channels\shopify2\lib;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Response;
use stock2shop\dal\channels\shopify2\lib\Api;
use stock2shop\dal\channels\shopify2\lib\Query;
use stock2shop\lib\Utils;
use tests;

class ApiTest extends tests\TestCase
{
    /**
     * @throws GuzzleException
     */
    public function testUnhandledPaginationWithoutException()
    {
        // Response indicates no more data available via pagination
        $response_data = file_get_contents(__DIR__ . '/mocks/order_with_return.json');
        $mockHandler = new MockHandler([
            new Response(200, ['Content-Type' => 'application/json'], $response_data)
        ]);
        $handlerStack = HandlerStack::create($mockHandler);
        ;
        $client = new Client([
            'handler' => $handlerStack
        ]);
        Api::init($client, 'test');

        // Request should run without any exceptions
        Query::order('123');
    }

    /**
     * @throws GuzzleException
     */
    public function testUnhandledPaginationWithException()
    {
        $response_data = Utils::jsonDecode(file_get_contents(__DIR__ . '/mocks/order_with_return.json'));
        // Response indicates more data available via pagination
        $response_data['data']['returns']['nodes'][0]['returnLineItems']['pageInfo']['hasNextPage'] = true;

        $mockHandler = new MockHandler([
            new Response(200, ['Content-Type' => 'application/json'], Utils::jsonEncode($response_data))
        ]);
        $handlerStack = HandlerStack::create($mockHandler);
        ;
        $client = new Client([
            'handler' => $handlerStack
        ]);
        Api::init($client, 'test');

        $this->expectExceptionMessage("Shopify has more data available for key 'returnLineItems', but pagination not currently implemented.");
        Query::order('123');
    }

}