{"data": {"order": {"fullyPaid": true, "id": "gid://shopify/Order/5758835130506", "createdAt": "2024-11-15T16:12:13Z", "currencyCode": "ZAR", "customerAcceptsMarketing": false, "name": "#1200", "note": "2024-11-15 16:13 UTC\nReturnGO, RMA5298245:\nRequest APPROVED (AUTO-APPROVE)\n\n2024-11-15 16:14 UTC\nReturnGO, RMA5298245:\nShipment RECEIVED\nBy #272013\n\n2024-11-15 16:15 UTC\nReturnGO, RMA5298245:\nExchange order #1201 released\nBy #272013\n\n2024-11-15 16:16 UTC\nReturnGO, RMA5298245:\nRequest DONE\nBy #272013", "poNumber": null, "taxExempt": false, "taxesIncluded": true, "taxLines": [{"rate": 0.15, "title": "VAT", "priceSet": {"shopMoney": {"amount": "156.0", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "156.0", "currencyCode": "ZAR"}}}], "lineItems": {"pageInfo": {"hasNextPage": false, "startCursor": "eyJsYXN0X2lkIjoxNDM0Nzc5MTIzNzI1OCwibGFzdF92YWx1ZSI6MTQzNDc3OTEyMzcyNTh9"}, "nodes": [{"name": "Levis 2", "quantity": 4, "sku": "380050125OS-", "taxable": true, "title": "Levis 2", "taxLines": [{"rate": 0.15, "title": "VAT", "priceSet": {"shopMoney": {"amount": "156.0", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "156.0", "currencyCode": "ZAR"}}}], "discountedUnitPriceSet": {"shopMoney": {"amount": "299.0", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "299.0", "currencyCode": "ZAR"}}, "id": "gid://shopify/LineItem/14347791237258", "isGiftCard": false, "originalUnitPriceSet": {"shopMoney": {"amount": "299.0", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "299.0", "currencyCode": "ZAR"}}, "totalDiscountSet": {"shopMoney": {"amount": "0.0", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "0.0", "currencyCode": "ZAR"}}, "discountAllocations": [], "discountedTotalSet": {"shopMoney": {"amount": "1196.0", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "1196.0", "currencyCode": "ZAR"}}, "discountedUnitPriceAfterAllDiscountsSet": {"shopMoney": {"amount": "299.0", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "299.0", "currencyCode": "ZAR"}}}]}, "billingAddress": {"address1": "Address", "address2": null, "city": "Cape Town", "company": null, "country": "South Africa", "countryCodeV2": "ZA", "firstName": "<PERSON>", "lastName": "<PERSON>", "latitude": null, "longitude": null, "name": "Test User", "phone": null, "province": "Western Cape", "provinceCode": "WC", "zip": "7777"}, "shippingAddress": {"address1": "Address", "address2": null, "city": "Cape Town", "company": null, "country": "South Africa", "countryCodeV2": "ZA", "firstName": "<PERSON>", "lastName": "<PERSON>", "latitude": -33.922087, "longitude": 18.4231418, "name": "Test User", "phone": null, "province": "Western Cape", "provinceCode": "WC", "zip": "7777"}, "customer": {"email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "phone": "+27123456789", "id": "gid://shopify/Customer/7136610254986", "tags": []}, "discountCodes": [], "fulfillmentOrders": {"pageInfo": {"hasNextPage": false, "startCursor": "eyJsYXN0X2lkIjo2ODU2ODAwMDc1OTE0LCJsYXN0X3ZhbHVlIjoiNjg1NjgwMDA3NTkxNCJ9"}, "nodes": [{"id": "gid://shopify/FulfillmentOrder/6856800075914", "status": "CLOSED", "fulfillments": {"pageInfo": {"hasNextPage": false, "startCursor": "eyJsYXN0X2lkIjo0NzgyNjczMTAwOTM4LCJsYXN0X3ZhbHVlIjoiNDc4MjY3MzEwMDkzOCJ9"}, "nodes": [{"id": "gid://shopify/Fulfillment/4782673100938", "status": "SUCCESS", "trackingInfo": []}]}, "lineItems": {"pageInfo": {"hasNextPage": false, "startCursor": "eyJsYXN0X2lkIjoxNDQ5OTA0MzYzOTQzNCwibGFzdF92YWx1ZSI6MTQ0OTkwNDM2Mzk0MzR9"}, "nodes": [{"id": "gid://shopify/FulfillmentOrderLineItem/14499043639434", "remainingQuantity": 0, "sku": "380050125OS-", "totalQuantity": 4}]}, "supportedActions": []}]}, "totalDiscountsSet": {"shopMoney": {"amount": "0.0", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "0.0", "currencyCode": "ZAR"}}, "shippingLines": {"pageInfo": {"hasNextPage": false, "startCursor": "eyJsYXN0X2lkIjo0ODExODk4MTU5MjQyLCJsYXN0X3ZhbHVlIjoiNDgxMTg5ODE1OTI0MiJ9"}, "nodes": [{"code": "Standard", "title": "Standard", "taxLines": [], "deliveryCategory": null, "originalPriceSet": {"shopMoney": {"amount": "0.0", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "0.0", "currencyCode": "ZAR"}}, "discountedPriceSet": {"shopMoney": {"amount": "0.0", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "0.0", "currencyCode": "ZAR"}}}]}, "refunds": [], "returns": {"pageInfo": {"hasNextPage": false, "startCursor": "eyJsYXN0X2lkIjo1ODI3MzYyOTU0LCJsYXN0X3ZhbHVlIjoiNTgyNzM2Mjk1NCJ9"}, "nodes": [{"id": "gid://shopify/Return/5827362954", "name": "#1200-R1", "returnLineItems": {"pageInfo": {"hasNextPage": false, "startCursor": "eyJsYXN0X2lkIjo3ODA1ODI5MjU4LCJsYXN0X3ZhbHVlIjoiNzgwNTgyOTI1OCJ9"}, "nodes": [{"id": "gid://shopify/ReturnLineItem/7805829258", "quantity": 1, "returnReason": "OTHER", "returnReasonNote": "RMA#5298245 Resolution: Variant exchange. Return reason: Changed my mind. Answers:  NEW > Levi<PERSON> 2."}]}, "status": "CLOSED"}]}, "totalRefundedSet": {"shopMoney": {"amount": "0.0", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "0.0", "currencyCode": "ZAR"}}, "totalRefundedShippingSet": {"shopMoney": {"amount": "0.0", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "0.0", "currencyCode": "ZAR"}}, "transactions": [{"authorizationCode": "53433", "createdAt": "2024-11-15T16:12:10Z", "gateway": "bogus", "id": "gid://shopify/OrderTransaction/6781178314890", "kind": "SALE", "paymentId": "rJGYiI0exItHDntyFTKW9pujU", "processedAt": "2024-11-15T16:12:10Z", "receiptJson": "{\"paid_amount\":\"1196.00\"}", "status": "SUCCESS", "amountSet": {"shopMoney": {"amount": "1196.0", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "1196.0", "currencyCode": "ZAR"}}, "parentTransaction": null, "paymentDetails": {"avsResultCode": null, "bin": "1", "company": "<PERSON><PERSON>", "cvvResultCode": null, "expirationMonth": 12, "expirationYear": 2034, "name": "Test User", "number": "•••• •••• •••• 1", "paymentMethodName": "card", "wallet": null}}], "currentTotalPriceSet": {"shopMoney": {"amount": "598.0", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "598.0", "currencyCode": "ZAR"}}, "currentTotalTaxSet": {"shopMoney": {"amount": "78.0", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "78.0", "currencyCode": "ZAR"}}, "currentTotalDiscountsSet": {"shopMoney": {"amount": "0.0", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "0.0", "currencyCode": "ZAR"}}, "customAttributes": []}}, "extensions": {"cost": {"requestedQueryCost": 369, "actualQueryCost": 36, "throttleStatus": {"maximumAvailable": 2000, "currentlyAvailable": 1964, "restoreRate": 100}}}}