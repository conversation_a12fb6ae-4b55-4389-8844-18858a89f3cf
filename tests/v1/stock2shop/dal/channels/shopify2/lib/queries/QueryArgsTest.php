<?php

namespace tests\v1\stock2shop\dal\channels\shopify2\lib\queries;

use Exception;
use tests;
use stock2shop\dal\channels\shopify2;

class QueryArgsTest extends tests\TestCase
{
    /**
     * @dataProvider dataProviderToGraphqlUsingConstructor
     * @throws Exception
     */
    public function testToGraphqlUsingConstructor(string $expected, array $data)
    {
        $args = new shopify2\lib\queries\QueryArgs($data);
        $this->assertEquals($expected, $args->toGraphql());
    }

    /**
     * @throws Exception
     */
    public function dataProviderToGraphqlUsingConstructor(): array
    {
        return [
            [
                'expected' => 'first: 10',
                'data'     => [],
            ],
            [
                'expected' => 'first: 100',
                'data'     => [
                    'first' => 100,
                ],
            ],
            [
                'expected' => 'first: 100, after: "asfdafa"',
                'data'     => [
                    'first' => 100,
                    'after' => 'asfdafa',
                ],
            ],
            [
                'expected' => 'first: 10, query: "(foo:\\"bar\\") OR (baz:\\"quz\\")"',
                'data'     => [
                    'queries' => [
                        [
                            "term"  => "foo",
                            "value" => "bar",
                        ],
                        [
                            "term"  => "baz",
                            "value" => "quz",
                        ]
                    ]
                ],
            ],
            [
                'expected' => 'first: 10, keys: ["foo","sl\\\\\\\\ash"], namespace: "escape\\\\\\\\slash"',
                'data'     => [
                    'terms' => [
                        "keys"      => ["foo", "sl\ash"],
                        "namespace" => "escape\slash",
                    ]
                ],
            ],
        ];
    }

    /**
     * @throws Exception
     */
    public function testToGraphqlUsing()
    {
        $args = new shopify2\lib\queries\QueryArgs();
        $args->setTerm('foo', 'bar');
        $args->setTerm('bar', 'bax');
        $this->assertEquals('first: 10, foo: "bar", bar: "bax"', $args->toGraphql());

        $args = new shopify2\lib\queries\QueryArgs();
        $args->setQuery('foo', 'bar');
        $args->setQuery('bar', 'bax');
        $this->assertEquals('first: 10, query: "(foo:\\"bar\\") OR (bar:\\"bax\\")"', $args->toGraphql());
    }


}
