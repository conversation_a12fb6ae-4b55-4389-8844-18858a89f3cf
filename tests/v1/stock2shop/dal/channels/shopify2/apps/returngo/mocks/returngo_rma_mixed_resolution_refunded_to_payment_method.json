{"rmaSummary": {"rmaId": "6149518", "status": "Done", "order_name": "LSSA-43532", "events": [{"eventName": "RMA_CREATED", "eventDate": "2025-02-01T16:05:13+00:00", "triggeredVia": "Return Portal", "storeLocationId": "online", "userDetails": {"userFirstName": null, "userLastName": null, "userEmailAddress": null, "userRole": null}}, {"eventName": "RMA_APPROVED", "eventDate": "2025-02-04T09:47:26+00:00", "triggeredVia": "Dashboard", "storeLocationId": "online", "userDetails": {"userFirstName": null, "userLastName": null, "userEmailAddress": null, "userRole": null}}]}, "orderDetails": {"orderId": "5995910266969", "orderName": null}, "shipments": [{"shipmentId": "5947781", "status": "Received", "carrierName": "DPD", "trackingNumber": "OPT-117372861", "trackingURL": "https://optimise.parcelninja.com/shipment/track/OPT-117372861", "labelURL": "https://sellerportal.dpworld.com/api/file-download?link=5614259", "customerAddress": {"name": "<PERSON><PERSON><PERSON>", "street1": "309 Umhlanga Rocks Drive", "street2": "Glass House Office Park", "city": "Umhlanga", "state": "NL", "zip": "4319", "country": "ZA", "phone": "0789699680", "email": "<EMAIL>"}, "returnAddress": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "street1": "Ashworth Logistics Park, Building C", "street2": "18 Laneshaw Street", "city": "Johannesburg", "state": "Gauteng", "zip": "1609", "country": "ZA", "phone": "", "email": "<EMAIL>"}, "isOutbound": false}], "items": [{"shopLineItemId": "14515697614937", "shopProductId": "7906074919001", "variantId": "42140477620313", "returnedItemId": "********", "fullName": "LEVI’S® MEN'S SKINNY TAPER  - BLACK - 34 / 34 / Black", "productName": "LEVI’S® MEN'S SKINNY TAPER  - BLACK", "variantName": "34 / 34 / Black", "image": "https://cdn.shopify.com/s/files/1/0589/0873/7625/files/e2bd1b9cd23514a9a5ebe9577bf2200a_600x600.jpg?v=1737576917", "sku": "A544600003434", "barcode": "5401132311678", "paidPrice": {"amount": 549, "currency": "ZAR", "shopAmount": 549, "shopCurrency": "ZAR"}, "paidTax": {"amount": 71.61, "currency": "ZAR", "shopAmount": 71.61, "shopCurrency": "ZAR"}, "taxIncluded": true, "returnReason": "Does not fit", "resolutionName": "Variant exchange", "policyRuleName": "Variant Exchange | Non Sale Items | First Request", "isAutoApprove": false, "resolutionType": "InstantVariantExchange", "restockingFee": 0, "returnFee": 0, "shipmentFee": 0, "shipmentId": "5947781", "extendedReason": " NEW > 38 / 32 / Black <br />\n > Too small / tight <br />\n > Yes", "returnMethod": {"type": "Ship with any carrier", "name": "SFS CourierGuy"}, "itemValidationStatusResponse": {"returnItemId": null, "isRestockable": false, "isReturned": false, "nonRestockableReason": null, "nonReturnedReason": null, "storeLocationId": null, "lastValidationDate": null, "lastValidatedBy": null, "thirdPartyName": null, "userDetails": {"userFirstName": null, "userLastName": null, "userEmailAddress": null, "userRole": null}}, "exchangeItems": [{"productId": null, "variantId": "42140477522009", "quantity": 1, "sku": "A544600003832", "itemPriceMethod": "CatalogPrice", "freeExchangePriceRange": {"min": 0, "max": 110}}]}, {"shopLineItemId": "14515697647705", "shopProductId": "7634568708185", "variantId": "41298087280729", "returnedItemId": "********", "fullName": "LEVI'S® MEN'S 510™ SKINNY JEANS - BLUE - 36 / 32 / Blue", "productName": "LEVI'S® MEN'S 510™ SKINNY JEANS - BLUE", "variantName": "36 / 32 / Blue", "image": "https://cdn.shopify.com/s/files/1/0589/0873/7625/files/510tm-skinny-fit-jeans-blue-1_600x600.jpg?v=1714397113", "sku": "A405500283632", "barcode": "5401132981710", "paidPrice": {"amount": 599, "currency": "ZAR", "shopAmount": 599, "shopCurrency": "ZAR"}, "paidTax": {"amount": 78.13, "currency": "ZAR", "shopAmount": 78.13, "shopCurrency": "ZAR"}, "taxIncluded": true, "returnReason": "Does not fit", "resolutionName": "Refund to payment method", "policyRuleName": "Refund to Payment Method", "isAutoApprove": false, "resolutionType": "RefundToPaymentMethod", "restockingFee": 0, "returnFee": 0, "shipmentFee": 0, "shipmentId": "5947781", "extendedReason": " > Too small / tight <br />\n > Yes", "returnMethod": {"type": "Ship with any carrier", "name": "SFS CourierGuy"}, "itemValidationStatusResponse": {"returnItemId": null, "isRestockable": false, "isReturned": false, "nonRestockableReason": null, "nonReturnedReason": null, "storeLocationId": null, "lastValidationDate": null, "lastValidatedBy": null, "thirdPartyName": null, "userDetails": {"userFirstName": null, "userLastName": null, "userEmailAddress": null, "userRole": null}}, "exchangeItems": []}], "comments": [{"commentType": "merchant", "triggeredBy": "<PERSON><PERSON><PERSON><PERSON>", "htmlText": "The RMA error was resolved by levis-sa.myshopify.com", "datetime": "2025-04-30T15:22:08+00:00"}, {"commentType": "error", "triggeredBy": "", "htmlText": "Refund Failed. Reason: Platform returned status: FAILURE RMA: 6149518", "datetime": "2025-04-08T14:34:13+00:00"}, {"commentType": "system", "triggeredBy": "<PERSON><PERSON><PERSON><PERSON>", "htmlText": "Request DONE", "datetime": "2025-04-08T11:34:22+00:00"}, {"commentType": "system", "triggeredBy": "<PERSON><PERSON><PERSON><PERSON>", "htmlText": "Completed Shopify return LSSA-43532-R1 for order LSSA-43532", "datetime": "2025-04-08T11:34:22+00:00"}, {"commentType": "system", "triggeredBy": "<PERSON><PERSON><PERSON><PERSON>", "htmlText": "Shipment <strong>RECEIVED</strong>", "datetime": "2025-04-08T11:34:18+00:00"}, {"commentType": "system", "triggeredBy": "<PERSON><PERSON><PERSON><PERSON>", "htmlText": "Total refund amount: R1,148.00<br>Transaction IDs: 8627101, 8627102", "datetime": "2025-04-08T11:34:13+00:00"}, {"commentType": "merchant", "triggeredBy": "<PERSON><PERSON><PERSON><PERSON>", "htmlText": "No stock, waiting on customer", "datetime": "2025-03-07T13:45:29+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 5947781 was updated", "datetime": "2025-02-04T09:48:37+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 5947781 was updated", "datetime": "2025-02-04T09:47:53+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 5947781 was updated", "datetime": "2025-02-04T09:47:51+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 5947781 was updated", "datetime": "2025-02-04T09:47:44+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 5947781 was updated", "datetime": "2025-02-04T09:47:42+00:00"}, {"commentType": "system", "triggeredBy": "#267963", "htmlText": "Request <strong>APPROVED</strong>", "datetime": "2025-02-04T09:47:32+00:00"}, {"commentType": "system", "triggeredBy": "#267963", "htmlText": "Created & approved Shopify return LSSA-43532-R1 for order LSSA-43532", "datetime": "2025-02-04T09:47:31+00:00"}, {"commentType": "system", "triggeredBy": "", "htmlText": "Request SUBMITTED", "datetime": "2025-02-01T16:05:15+00:00"}], "exchangeOrders": [{"draftOrderId": "************", "orderId": "", "orderName": "#D1167", "type": "Exchange"}], "cancellationFee": 0, "transactions": [{"id": "8627101", "type": "RefundToPaymentMethod", "externalReferenceId": "************", "externalReferenceType": "PlatformRefundId", "amount": {"amount": 274.5, "currency": "ZAR", "shopAmount": 274.5, "shopCurrency": "ZAR"}, "status": "Failed", "processor": "Shopify", "bankAccountDetails": {"iban": null, "firstName": null, "lastName": null, "branchName": null, "branchAddress": null, "comment": null}, "returnItemIds": ["********"], "createdAt": "2025-04-08T11:34:13+00:00", "lastUpdated": "2025-04-08T14:34:13+00:00"}, {"id": "8627102", "type": "RefundToPaymentMethod", "externalReferenceId": "************", "externalReferenceType": "PlatformRefundId", "amount": {"amount": 873.5, "currency": "ZAR", "shopAmount": 873.5, "shopCurrency": "ZAR"}, "status": "Failed", "processor": "Shopify", "bankAccountDetails": {"iban": null, "firstName": null, "lastName": null, "branchName": null, "branchAddress": null, "comment": null}, "returnItemIds": ["********"], "createdAt": "2025-04-08T11:34:13+00:00", "lastUpdated": "2025-04-08T14:34:13+00:00"}], "createdAt": "2025-02-01T16:05:13+00:00", "lastUpdated": "2025-04-08T11:34:22+00:00"}