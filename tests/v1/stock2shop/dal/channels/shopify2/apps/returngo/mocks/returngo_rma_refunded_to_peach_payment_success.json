{"rmaSummary": {"rmaId": "6839152", "status": "Done", "order_name": "LSSA-47177", "events": [{"eventName": "RMA_CREATED", "eventDate": "2025-04-16T07:43:14+00:00", "triggeredVia": "Return Portal", "storeLocationId": "online", "userDetails": {"userFirstName": null, "userLastName": null, "userEmailAddress": null, "userRole": null}}, {"eventName": "RMA_APPROVED", "eventDate": "2025-04-22T08:15:16+00:00", "triggeredVia": "Dashboard", "storeLocationId": "online", "userDetails": {"userFirstName": null, "userLastName": null, "userEmailAddress": null, "userRole": null}}]}, "orderDetails": {"orderId": "6187553325145", "orderName": null}, "shipments": [{"shipmentId": "6621449", "status": "Received", "carrierName": "CourierGuy", "trackingNumber": "OPT-643755424", "trackingURL": "https://optimise.parcelninja.com/shipment/track/OPT-643755424", "labelURL": "https://sellerportal.dpworld.com/api/file-download?link=null", "customerAddress": {"name": "<PERSON><PERSON><PERSON>", "street1": "317 President street,MOGWADI", "street2": "Boere Saal hall", "city": "Polokwane", "state": "LP", "zip": "0715", "country": "ZA", "phone": "+27824001637", "email": "<EMAIL>"}, "returnAddress": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "street1": "Ashworth Logistics Park, Building C", "street2": "18 Laneshaw Street", "city": "Johannesburg", "state": "Gauteng", "zip": "1609", "country": "ZA", "phone": "", "email": "<EMAIL>"}, "isOutbound": false}], "items": [{"shopLineItemId": "14867565641817", "shopProductId": "7806216208473", "variantId": "41906167677017", "returnedItemId": "********", "fullName": "LEVI'S® MEN'S RELAXED FIT GRAPH<PERSON> HOODIE - BLACK - L / - / Black", "productName": "LEVI'S® MEN'S RELAXED FIT G<PERSON>P<PERSON><PERSON> HOODIE - BLACK", "variantName": "L / - / Black", "image": "https://cdn.shopify.com/s/files/1/0589/0873/7625/files/adb85ae2743c081aededeab2f8dc8c68_600x600.jpg?v=1727109146", "sku": "A91140009L-", "barcode": "5401180206742", "paidPrice": {"amount": 549, "currency": "ZAR", "shopAmount": 549, "shopCurrency": "ZAR"}, "paidTax": {"amount": 71.6, "currency": "ZAR", "shopAmount": 71.6, "shopCurrency": "ZAR"}, "taxIncluded": true, "returnReason": "Does not fit", "resolutionName": "Variant exchange", "policyRuleName": "Variant Exchange | Non Sale Items | First Request", "isAutoApprove": false, "resolutionType": "InstantVariantExchange", "restockingFee": 0, "returnFee": 0, "shipmentFee": 0, "shipmentId": "6621449", "extendedReason": " NEW > XL / - / Black <br />\n > Too small / tight <br />\n > Yes", "returnMethod": {"type": "Ship with any carrier", "name": "SFS CourierGuy"}, "itemValidationStatusResponse": {"returnItemId": null, "isRestockable": false, "isReturned": false, "nonRestockableReason": null, "nonReturnedReason": null, "storeLocationId": null, "lastValidationDate": null, "lastValidatedBy": null, "thirdPartyName": null, "userDetails": {"userFirstName": null, "userLastName": null, "userEmailAddress": null, "userRole": null}}, "exchangeItems": [{"productId": null, "variantId": "41906168004697", "quantity": 1, "sku": "A91140009XL-", "itemPriceMethod": "CatalogPrice", "freeExchangePriceRange": {"min": 0, "max": 110}}]}], "comments": [{"commentType": "system", "triggeredBy": "<PERSON><PERSON><PERSON><PERSON>", "htmlText": "Completed Shopify return LSSA-47177-R1 for order LSSA-47177", "datetime": "2025-05-19T14:52:31+00:00"}, {"commentType": "system", "triggeredBy": "<PERSON><PERSON><PERSON><PERSON>", "htmlText": "Request DONE", "datetime": "2025-05-19T14:52:31+00:00"}, {"commentType": "system", "triggeredBy": "<PERSON><PERSON><PERSON><PERSON>", "htmlText": "Total refund amount: R549.00<br>Transaction ID: 9271901", "datetime": "2025-05-15T10:25:47+00:00"}, {"commentType": "merchant", "triggeredBy": "<PERSON><PERSON><PERSON><PERSON>", "htmlText": "No stock, waiting on customer", "datetime": "2025-05-05T09:09:06+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6621449 was updated", "datetime": "2025-04-25T22:40:49+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6621449 was updated", "datetime": "2025-04-25T22:39:25+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6621449 was updated", "datetime": "2025-04-25T11:03:58+00:00"}, {"commentType": "system", "triggeredBy": "Automation", "htmlText": "Shipment <strong>RECEIVED</strong>", "datetime": "2025-04-25T11:03:57+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6621449 was updated", "datetime": "2025-04-25T05:14:51+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6621449 was updated", "datetime": "2025-04-23T16:58:50+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6621449 was updated", "datetime": "2025-04-23T14:38:58+00:00"}, {"commentType": "system", "triggeredBy": "Automation", "htmlText": "Shipment <strong>IN TRANSIT</strong>", "datetime": "2025-04-23T14:38:57+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6621449 was updated", "datetime": "2025-04-22T08:17:48+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6621449 was updated", "datetime": "2025-04-22T08:15:55+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6621449 was updated", "datetime": "2025-04-22T08:15:53+00:00"}, {"commentType": "system", "triggeredBy": "<PERSON>", "htmlText": "Request <strong>APPROVED</strong>", "datetime": "2025-04-22T08:15:20+00:00"}, {"commentType": "system", "triggeredBy": "<PERSON>", "htmlText": "Created & approved Shopify return LSSA-47177-R1 for order LSSA-47177", "datetime": "2025-04-22T08:15:20+00:00"}, {"commentType": "system", "triggeredBy": "", "htmlText": "Request SUBMITTED", "datetime": "2025-04-16T07:43:15+00:00"}], "exchangeOrders": [{"draftOrderId": "************", "orderId": "", "orderName": "#D1418", "type": "Exchange"}], "cancellationFee": 0, "transactions": [{"id": "9271901", "type": "RefundToPaymentMethod", "externalReferenceId": "************", "externalReferenceType": "PlatformRefundId", "amount": {"amount": 549, "currency": "ZAR", "shopAmount": 549, "shopCurrency": "ZAR"}, "status": "Success", "processor": "Shopify", "bankAccountDetails": {"iban": null, "firstName": null, "lastName": null, "branchName": null, "branchAddress": null, "comment": null}, "returnItemIds": ["********"], "createdAt": "2025-05-15T10:25:46+00:00", "lastUpdated": "2025-05-15T13:25:46+00:00"}], "createdAt": "2025-04-16T07:43:14+00:00", "lastUpdated": "2025-05-19T14:52:30+00:00"}