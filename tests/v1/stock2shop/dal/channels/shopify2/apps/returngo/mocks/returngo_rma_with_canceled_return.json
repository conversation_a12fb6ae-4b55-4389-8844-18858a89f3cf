{"rmaSummary": {"rmaId": "6487530", "status": "Done", "order_name": "LSSA-45187", "events": [{"eventName": "RMA_CREATED", "eventDate": "2025-03-13T13:23:02+00:00", "triggeredVia": "Return Portal", "storeLocationId": "online", "userDetails": {"userFirstName": null, "userLastName": null, "userEmailAddress": null, "userRole": null}}, {"eventName": "RMA_APPROVED", "eventDate": "2025-03-13T13:32:08+00:00", "triggeredVia": "Dashboard", "storeLocationId": "online", "userDetails": {"userFirstName": null, "userLastName": null, "userEmailAddress": null, "userRole": null}}]}, "orderDetails": {"orderId": "6096901865561", "orderName": null}, "shipments": [{"shipmentId": "6277201", "status": "Received", "carrierName": "CourierGuy", "trackingNumber": "OPT-091414621", "trackingURL": "https://optimise.parcelninja.com/shipment/track/OPT-091414621", "labelURL": "https://sellerportal.dpworld.com/api/file-download?link=null", "customerAddress": {"name": "Carrie<PERSON><PERSON>", "street1": "21 Popham Street Waves Edge", "street2": "14B Cape On Popham", "city": "Cape Town", "state": "WC", "zip": "6820", "country": "ZA", "phone": "0810340745", "email": "<EMAIL>"}, "returnAddress": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "street1": "Ashworth Logistics Park, Building C", "street2": "18 Laneshaw Street", "city": "Johannesburg", "state": "Gauteng", "zip": "1609", "country": "ZA", "phone": "", "email": "<EMAIL>"}, "isOutbound": false}], "items": [{"shopLineItemId": "14713466880089", "shopProductId": "7806001053785", "variantId": "41905882562649", "returnedItemId": "********", "fullName": "LEVI’S® WOMEN’S 501® ORIGINAL LIGHTWEIGHT CROPPED JEANS - MED INDIGO - WORN IN - 30 / 28 / Med Indigo - Worn In", "productName": "LEVI’S® WOMEN’S 501® ORIGINAL LIGHTWEIGHT CROPPED JEANS - MED INDIGO - WORN IN", "variantName": "30 / 28 / Med Indigo - Worn In", "image": "https://cdn.shopify.com/s/files/1/0589/0873/7625/files/a68c0492c13010d50d96f597d3187d52_600x600.jpg?v=1732088731", "sku": "0013S00013028", "barcode": "5401180353514", "paidPrice": {"amount": 399, "currency": "ZAR", "shopAmount": 399, "shopCurrency": "ZAR"}, "paidTax": {"amount": 52.04, "currency": "ZAR", "shopAmount": 52.04, "shopCurrency": "ZAR"}, "taxIncluded": true, "returnReason": "Does not fit", "resolutionName": "Refund to payment method", "policyRuleName": "Refund to Payment Method", "isAutoApprove": false, "resolutionType": "RefundToPaymentMethod", "restockingFee": 0, "returnFee": 0, "shipmentFee": 0, "shipmentId": "6277201", "extendedReason": " > Too large / loose <br />\n > Yes", "returnMethod": {"type": "Ship with any carrier", "name": "SFS CourierGuy"}, "itemValidationStatusResponse": {"returnItemId": null, "isRestockable": false, "isReturned": false, "nonRestockableReason": null, "nonReturnedReason": null, "storeLocationId": null, "lastValidationDate": null, "lastValidatedBy": null, "thirdPartyName": null, "userDetails": {"userFirstName": null, "userLastName": null, "userEmailAddress": null, "userRole": null}}, "exchangeItems": []}], "comments": [{"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6277201 was updated", "datetime": "2025-05-12T11:08:02+00:00"}, {"commentType": "system", "triggeredBy": "", "htmlText": "Completed Shopify return LSSA-45187-R2 for order LSSA-45187", "datetime": "2025-05-05T11:09:28+00:00"}, {"commentType": "system", "triggeredBy": "Automation", "htmlText": "Request DONE", "datetime": "2025-05-05T11:09:27+00:00"}, {"commentType": "system", "triggeredBy": "<PERSON>", "htmlText": "Total refund amount: R399.00<br>Transaction ID: 9099628", "datetime": "2025-05-05T08:09:26+00:00"}, {"commentType": "system", "triggeredBy": "<PERSON>", "htmlText": "Created & approved Shopify return LSSA-45187-R2 for order LSSA-45187", "datetime": "2025-05-05T07:58:51+00:00"}, {"commentType": "system", "triggeredBy": "<PERSON>", "htmlText": "Request <strong>REOPENED</strong>", "datetime": "2025-05-05T07:58:50+00:00"}, {"commentType": "system", "triggeredBy": "<PERSON>", "htmlText": "Canceled Shopify return LSSA-45187-R1 for order LSSA-45187", "datetime": "2025-04-25T11:57:27+00:00"}, {"commentType": "system", "triggeredBy": "<PERSON>", "htmlText": "Request <strong>CANCELED</strong> (reason: )", "datetime": "2025-04-25T11:57:27+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6277201 was updated", "datetime": "2025-03-20T21:00:48+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6277201 was updated", "datetime": "2025-03-20T10:39:50+00:00"}, {"commentType": "system", "triggeredBy": "Automation", "htmlText": "Shipment <strong>RECEIVED</strong>", "datetime": "2025-03-20T10:39:49+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6277201 was updated", "datetime": "2025-03-20T05:15:51+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6277201 was updated", "datetime": "2025-03-18T14:08:49+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6277201 was updated", "datetime": "2025-03-18T14:08:09+00:00"}, {"commentType": "system", "triggeredBy": "Automation", "htmlText": "Shipment <strong>IN TRANSIT</strong>", "datetime": "2025-03-18T14:08:08+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6277201 was updated", "datetime": "2025-03-13T13:33:47+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6277201 was updated", "datetime": "2025-03-13T13:32:51+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6277201 was updated", "datetime": "2025-03-13T13:32:49+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6277201 was updated", "datetime": "2025-03-13T13:32:23+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6277201 was updated", "datetime": "2025-03-13T13:32:22+00:00"}, {"commentType": "system", "triggeredBy": "#267963", "htmlText": "Request <strong>APPROVED</strong>", "datetime": "2025-03-13T13:32:12+00:00"}, {"commentType": "system", "triggeredBy": "#267963", "htmlText": "Created & approved Shopify return LSSA-45187-R1 for order LSSA-45187", "datetime": "2025-03-13T13:32:11+00:00"}, {"commentType": "system", "triggeredBy": "", "htmlText": "Request SUBMITTED", "datetime": "2025-03-13T13:23:03+00:00"}], "exchangeOrders": [], "cancellationFee": 0, "transactions": [{"id": "9099628", "type": "RefundToPaymentMethod", "externalReferenceId": "************", "externalReferenceType": "PlatformRefundId", "amount": {"amount": 399, "currency": "ZAR", "shopAmount": 399, "shopCurrency": "ZAR"}, "status": "Success", "processor": "Shopify", "bankAccountDetails": {"iban": null, "firstName": null, "lastName": null, "branchName": null, "branchAddress": null, "comment": null}, "returnItemIds": ["********"], "createdAt": "2025-05-05T08:09:26+00:00", "lastUpdated": "2025-05-05T11:09:26+00:00"}], "createdAt": "2025-03-13T13:23:02+00:00", "lastUpdated": "2025-05-05T11:09:27+00:00"}