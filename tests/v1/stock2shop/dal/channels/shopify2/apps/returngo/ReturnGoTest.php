<?php

namespace tests\v1\stock2shop\dal\channels\shopify2\apps\returngo;

use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Response;
use stock2shop\dal\channels\shopify2\apps\returngo\ReturnGo;
use stock2shop\dal\channels\shopify2\lib\models\Order;
use stock2shop\dal\channels\shopify2\order\TransformArgs;
use stock2shop\dal\connector;
use stock2shop\dal\returngo\lib\models\RMA;
use stock2shop\dal\returngo\lib\models\RMAItem;
use stock2shop\exceptions\NotImplemented;
use stock2shop\exceptions\ServerError;
use stock2shop\exceptions\UnprocessableEntity;
use stock2shop\exceptions\Validation;
use stock2shop\lib\Utils;
use stock2shop\repository\ConfigRepository;
use stock2shop\vo;
use tests;

class ReturnGoTest extends tests\TestCase
{
    const CLIENT_ID = 1;
    const CHANNEL_ID = 2;

    const ADDITIONAL_CHANNEL_META = [
        [
            'key'   => ReturnGo::META_APP_RETURNGO_ENABLED,
            'value' => 'true'
        ]
    ];

    const ADDITIONAL_SECRETS = [
        ReturnGo::SECRET_RETURNGO_API_KEY_KEY => 'api_key_value'
    ];

    /**
     * @throws UnprocessableEntity
     * @throws ServerError
     * @throws Validation
     */
    private function setUpTest()
    {
        $conf                        = tests\v1\stock2shop\dal\channels\shopify2\TestCase::CONFIG;
        $conf['channels'][0]['meta'] = array_merge($conf['channels'][0]['meta'], self::ADDITIONAL_CHANNEL_META);
        tests\DB::makeFromConfig(new vo\Config($conf));
        $cRepo = new ConfigRepository(self::CLIENT_ID);
        $cRepo->load();
        tests\DB::makeSecrets(
            $cRepo->config,
            vo\Config::CONNECTOR_CHANNEL,
            self::CHANNEL_ID,
            self::ADDITIONAL_SECRETS);

        connector\Factory::initConnector($cRepo->config, vo\Config::CONNECTOR_CHANNEL, self::CHANNEL_ID);
    }

    /**
     * @param array $rma_list
     * @param array|null $existing_refunds use original getRefundsByChannelRefundCodes implementation if null
     * @return TransformArgs
     */
    private function getMockTransformArgs(
        array $rma_list,
        array $existing_refunds = null): TransformArgs
    {
        $mockBuilder = $this->getMockBuilder(TransformArgs::class)
            ->disableOriginalConstructor();

        if (is_null($existing_refunds)) {
            $mockBuilder->setMethods(['getReturnGoRMAs']);
        }

        $args = $mockBuilder->getMock();
        $args->method('getReturnGoRMAs')->willReturn($rma_list);

        return $args;
    }

    /**
     * @param string $shopify_file_name
     * @param string $rma_file_name
     * @return array
     * @throws GuzzleException
     * @throws UnprocessableEntity
     * @throws Validation
     */
    private function getRMAs(string $shopify_file_name, string $rma_file_name): array
    {
        $shopify_order_data = file_get_contents(__DIR__ . "/mocks/${shopify_file_name}.json");
        $rma_data           = file_get_contents(__DIR__ . "/mocks/${rma_file_name}.json");
        $mockHandler        = new MockHandler([
            new Response(200, ['Content-Type', 'application/json'], $rma_data)
        ]);
        $client             = new Client(['handler' => HandlerStack::create($mockHandler)]);

        $shopifyOrder = new Order(Utils::jsonDecode($shopify_order_data));
        return ReturnGo::getRMAs($shopifyOrder, $client);
    }

    public function testGetRMAs()
    {
        $this->setUpTest();

        $rma_list = $this->getRMAs(
            'shopify_order_with_return',
            'returngo_rma_multiple_refunds');

        $this->assertCount(1, $rma_list);
        $this->assertEquals('5218219', $rma_list[0]->rmaSummary->rmaId);
        $this->assertEquals('Done', $rma_list[0]->rmaSummary->status);

        // TODO add more asserts
    }

    /**
     * @throws UnprocessableEntity
     * @throws ServerError
     * @throws NotImplemented
     * @throws Validation
     * @throws GuzzleException
     * @throws Exception
     */
    public function testGetRmaWithoutRmaIds()
    {
        $this->setUpTest();

        $shopify_order_data = file_get_contents(__DIR__ . "/mocks/shopify_order_with_return.json");
        $rma_data           = file_get_contents(__DIR__ . "/mocks/returngo_rma_multiple_refunds.json");

        $mockHandler = new MockHandler([
            new Response(200, ['Content-Type', 'application/json'], $rma_data)
        ]);
        $client      = new Client(['handler' => HandlerStack::create($mockHandler)]);

        $shopifyOrder = new Order(Utils::jsonDecode($shopify_order_data));
        foreach ($shopifyOrder->returns[0]->returnLineItems as $item) {
            $item->returnReasonNote = 'Test';
        }

        // No rma ids found in shopify return data
        $this->assertEmpty(ReturnGo::getRMAs($shopifyOrder, $client));
    }

    /**
     * @throws ServerError
     * @throws UnprocessableEntity
     * @throws NotImplemented
     * @throws Validation
     * @throws GuzzleException
     * @throws Exception
     */
    public function testGetRmaNotFound()
    {
        $this->setUpTest();

        $shopify_order_data = file_get_contents(__DIR__ . '/mocks/shopify_order_with_return.json');

        $mockHandler  = new MockHandler([
            new Response(404, ['Content-Type', 'application/json'], Utils::jsonEncode([
                'message' => 'RMA not found'
            ]))
        ]);
        $client       = new Client(
            [
                'http_errors' => false,
                'handler'     => HandlerStack::create($mockHandler)
            ]);
        $shopifyOrder = new Order(Utils::jsonDecode($shopify_order_data));

        // RMA not found
        $this->assertEmpty(ReturnGo::getRMAs($shopifyOrder, $client));
    }

    /**
     * @throws UnprocessableEntity
     * @throws GuzzleException
     * @throws Validation
     * @throws Exception
     */
    public function testSetRefundWhenRefundingAnExchangeOrder()
    {
        $this->setUpTest();
        $config = new vo\Config(['client_id' => self::CLIENT_ID, 'channel_id' => self::CHANNEL_ID]);

        // create original order inside system
        tests\DB::makeOrder(
            $config,
            Utils::jsonDecode(
                file_get_contents(__DIR__ . '/mocks/system_order_that_has_been_exchanged.json')
            )
        );

        // create exchange order inside system
        tests\DB::makeOrder(
            $config,
            Utils::jsonDecode(
                file_get_contents(__DIR__ . '/mocks/system_order_exchange_order.json')
            )
        );

        // sync refunded exchange order
        $channel_order_data = file_get_contents(__DIR__ . '/mocks/channel_order_with_refund_on_original_exchange_order.json');
        $shopify_order_data = file_get_contents(__DIR__ . '/mocks/shopify_order_with_refund_on_original_exchange_order.json');

        $channelOrder          = new vo\ChannelOrder(Utils::jsonDecode($channel_order_data));
        $shopifyOrder          = new Order(Utils::jsonDecode($shopify_order_data));
        $shopifyOrder->refunds = [];

        $rma_list = $this->getRMAs(
            'shopify_order_with_refund_on_original_exchange_order',
            'returngo_rma_refunded_exchange_order'
        );

        ReturnGo::setRefunds($channelOrder, $shopifyOrder, $this->getMockTransformArgs($rma_list));

        $refunds = $channelOrder->refunds;

        $this->assertCount(1, $refunds);
        $this->assertEquals(2337, $refunds[0]->amount_incl);
        $this->assertEquals($refunds[0]->meta[1]->key, ReturnGo::REFUND_META_RETURNGO_TRANSACTION_COMPLETED_ON_ANOTHER_ORDER);
        $this->assertEquals($refunds[0]->meta[1]->value, '909061914812');

        $this->assertEmpty($refunds[0]->transactions);

        foreach ($refunds[0]->line_items as $item) {
            $this->assertEquals($item->subtotal_incl, 779);
            $this->assertEquals($item->unit_amount_incl, 779);

            // since it's an exchange order it has zero tax (This is synced this way from ReturnGo)
            // For LEVIs we are manually adding the tax on their CustomSourceHook file that sends the order data to SAP.
            // We think that there should be tax on the refunded exchange order coming from ReturnGo.
            $this->assertEquals($item->subtotal_tax, 0);
        }
    }

    /**
     * @throws UnprocessableEntity
     * @throws ServerError
     * @throws NotImplemented
     * @throws Validation
     * @throws GuzzleException
     * @throws Exception
     */
    public function testSetRefundsWithStoreCreditAndCreditCardRefunds()
    {
        $this->setUpTest();

        $channel_order_data = file_get_contents(__DIR__ . '/mocks/channel_order.json');
        $shopify_order_data = file_get_contents(__DIR__ . '/mocks/shopify_order_with_return.json');

        $channelOrder = new vo\ChannelOrder(Utils::jsonDecode($channel_order_data));
        $shopifyOrder = new Order(Utils::jsonDecode($shopify_order_data));

        $rma_list = $this->getRMAs(
            'shopify_order_with_return',
            'returngo_rma_multiple_refunds');

        ReturnGo::setRefunds($channelOrder, $shopifyOrder, $this->getMockTransformArgs($rma_list, []));
        $refunds = $channelOrder->refunds;
        $this->assertCount(1, $refunds);
        $this->assertEquals($channelOrder->channel_id, $refunds[0]->channel_id);
        $this->assertEquals('#1157-R1', $refunds[0]->channel_refund_code);
        $this->assertEquals(vo\Refund::CONNECTOR_STATE_COMPLETE, $refunds[0]->channel_refund_state);
        $this->assertEquals(399.80, $refunds[0]->amount_incl);

        // Product lines
        $this->assertEquals(vo\RefundItem::createArray([
            [
                'code'             => 'item',
                'meta'             => [
                    [
                        'key'   => sprintf('returngo_qty_%s', ReturnGo::RESOLUTION_TYPE_INSTANT_VARIANT_EXCHANGE),
                        'value' => 2
                    ],
                    [
                        'key'   => sprintf('returngo_qty_%s', ReturnGo::RESOLUTION_TYPE_REFUND_TO_STORE_CREDIT),
                        'value' => 2
                    ],
                    [
                        'key'   => sprintf('returngo_qty_%s', ReturnGo::RESOLUTION_TYPE_REFUND_TO_PAYMENT_METHOD),
                        'value' => 2
                    ]
                ],
                'sku'              => 'SKU003',
                'title'            => 'Product A',
                'qty'              => 6,
                'unit_amount_incl' => 99.95,
                'subtotal_incl'    => 599.7, // (99.95 - 0 discount) * 6
                'subtotal_tax'     => 78.24
            ]
        ]), $refunds[0]->getItemLines());

        $this->assertEquals(vo\Transaction::createArray([
            [
                'amount'                   => 199.90,
                'card_bin'                 => '1',
                'card_last_4_digits'       => '•••• •••• •••• 1',
                'channel_transaction_code' => '6767992733834',
                'completed_date'           => '2024-11-06 15:25:35',
                'gateway'                  => 'bogus',
                'is_store_credit'          => false,
                'kind'                     => 'credit',
                'payment_code'             => '#1157.2',
                'payment_method'           => 'card',
                'state'                    => 'complete',
                'status'                   => 'SUCCESS'
            ],
            [
                'amount'                   => 199.90,
                'channel_transaction_code' => '68AEPOD4',
                'completed_date'           => '2024-11-06 15:38:40',
                'gateway'                  => 'returngo',
                'is_store_credit'          => true,
                'kind'                     => 'credit',
                'payment_code'             => '68AEPOD4',
                'payment_method'           => 'RefundToStoreCredit',
                'state'                    => 'complete',
                'status'                   => 'Done'
            ]
        ]), $refunds[0]->transactions);

        $this->assertTotalsAndCounts($shopifyOrder, $refunds, $rma_list);

        // Assert that the correct meta was set on the refund
        $this->assertCount(2, $refunds[0]->meta);
        $this->assertEquals(ReturnGo::REFUND_META_RETURNGO_RMA_ID_KEY, $refunds[0]->meta[0]->key);
        $this->assertEquals('5218219', $refunds[0]->meta[0]->value);
        $this->assertEquals(ReturnGo::REFUND_META_RETURNGO_EXCHANGE_ORDER_NAMES_KEY, $refunds[0]->meta[1]->key);
        $this->assertEquals('#1158', $refunds[0]->meta[1]->value);
    }

    /**
     * ReturnGO incorrectly links refunds created directly on Shopify to the RMA return.
     * This test ensures we throw an appropriate exception in this scenario.
     * @throws UnprocessableEntity
     * @throws ServerError
     * @throws NotImplemented
     * @throws Validation
     * @throws GuzzleException
     * @throws Exception
     */
    public function testSetRefundsShopifyRefundIncorrectlyLinkedToRmaReturn()
    {
        $this->setUpTest();

        $channel_order_data = file_get_contents(__DIR__ . '/mocks/channel_order.json');
        $shopify_order_data = file_get_contents(__DIR__ . '/mocks/shopify_order_with_shopify_and_returngo_refunds.json');

        $channelOrder = new vo\ChannelOrder(Utils::jsonDecode($channel_order_data));
        $channelOrder->refunds[0]->channel_refund_code = '873335619722';
        $shopifyOrder = new Order(Utils::jsonDecode($shopify_order_data));

        $rma_list = $this->getRMAs(
            'shopify_order_with_shopify_and_returngo_refunds',
            'returngo_rma_multiple_refunds');

        // 873335619722 refers to Shopify refund linked to this RMA
        $rma_list[0]->transactions[2]->externalReferenceId = '873335619722';
        // 873377398922 refers to Shopify refund that isn't linked to this RMA, but is part of the Shopify order
        $rma_list[0]->transactions[3]->externalReferenceId = '873377398922';

        $existing_refunds = [
            'order_123' => [
                new vo\Refund([
                    'channel_refund_code' => '873377398922'
                ])
            ]
        ];

        $this->expectExceptionMessage('Refunding an order from both ReturnGO and Shopify not currently supported.');
        ReturnGo::setRefunds($channelOrder, $shopifyOrder, $this->getMockTransformArgs($rma_list, $existing_refunds));
    }

    /**
     * We do not support refunding the same RMA item in multiple parts.
     * This test ensures we throw an appropriate exception in this scenario.
     * @throws UnprocessableEntity
     * @throws ServerError
     * @throws NotImplemented
     * @throws Validation
     * @throws GuzzleException
     * @throws Exception
     */
    public function testSetRefundsRepeatedTransactionForRmaItem()
    {
        $this->setUpTest();

        $channel_order_data = file_get_contents(__DIR__ . '/mocks/channel_order.json');
        $shopify_order_data = file_get_contents(__DIR__ . '/mocks/shopify_order_with_return.json');

        $channelOrder = new vo\ChannelOrder(Utils::jsonDecode($channel_order_data));
        $shopifyOrder = new Order(Utils::jsonDecode($shopify_order_data));

        $rma_list = $this->getRMAs(
            'shopify_order_with_return',
            'returngo_rma_multiple_refunds');

        // This results in the same transaction type referencing the same RMA item id twice, which we don't support
        $rma_list[0]->transactions[3]->returnItemIds = $rma_list[0]->transactions[2]->returnItemIds;

        $this->expectExceptionMessage('Multiple transactions of the type RefundToPaymentMethod associated with returnedItemId 22425175.');
        ReturnGo::setRefunds($channelOrder, $shopifyOrder, $this->getMockTransformArgs($rma_list, []));
    }

    /**
     * @throws UnprocessableEntity
     * @throws ServerError
     * @throws NotImplemented
     * @throws Validation
     * @throws GuzzleException
     * @throws Exception
     */
    public function testSetRefundsWithMultipleCreditCardRefunds()
    {
        $this->setUpTest();

        $channel_order_data = file_get_contents(__DIR__ . '/mocks/channel_order_with_multiple_credit_card_refunds.json');
        $shopify_order_data = file_get_contents(__DIR__ . '/mocks/shopify_order_with_multiple_credit_card_refunds.json');

        $channelOrder = new vo\ChannelOrder(Utils::jsonDecode($channel_order_data));
        $shopifyOrder = new Order(Utils::jsonDecode($shopify_order_data));

        $rma_list = $this->getRMAs(
            'shopify_order_with_multiple_credit_card_refunds',
            'returngo_rma_multiple_credit_card_refunds');

        ReturnGo::setRefunds($channelOrder, $shopifyOrder, $this->getMockTransformArgs($rma_list, []));

        $refunds = $channelOrder->refunds;

        $this->assertCount(1, $refunds);
        $this->assertEquals($channelOrder->channel_id, $refunds[0]->channel_id);
        $this->assertEquals('#1206-R1', $refunds[0]->channel_refund_code);
        $this->assertEquals(vo\Refund::CONNECTOR_STATE_COMPLETE, $refunds[0]->channel_refund_state);
        $this->assertEquals(3150.99, $refunds[0]->amount_incl);

        $this->assertEquals(vo\RefundMeta::createArray([
            [
                'key'   => ReturnGo::REFUND_META_RETURNGO_RMA_ID_KEY,
                'value' => '5331615'
            ]
        ]), $refunds[0]->meta);

        // Product lines
        $this->assertEquals(vo\RefundItem::createArray([
            [
                'code'             => 'item',
                'meta'             => [
                    [
                        'key'   => sprintf('returngo_qty_%s', ReturnGo::RESOLUTION_TYPE_REFUND_TO_PAYMENT_METHOD),
                        'value' => 4
                    ]
                ],
                'sku'              => 'SKU001',
                'title'            => 'Product X',
                'qty'              => 4,
                'unit_amount_incl' => 599.0,
                'subtotal_incl'    => 2356.0, // (599 - 10 discount) * 4
                'subtotal_tax'     => 307.32
            ],
            [
                'code'             => 'item',
                'meta'             => [
                    [
                        'key'   => sprintf('returngo_qty_%s', ReturnGo::RESOLUTION_TYPE_REFUND_TO_PAYMENT_METHOD),
                        'value' => 4
                    ]
                ],
                'sku'              => 'SKU002',
                'title'            => 'Product Y',
                'qty'              => 4,
                'unit_amount_incl' => 199.0,
                'subtotal_incl'    => 796.0, // (199 - 0 discount) * 4
                'subtotal_tax'     => 103.84
            ]
        ]), $refunds[0]->getItemLines());

        $this->assertEquals(vo\Transaction::createArray([
            [
                'amount'                   => 2355.51,
                'card_bin'                 => '1',
                'card_last_4_digits'       => '•••• •••• •••• 1',
                'channel_transaction_code' => '6786918219914',
                'completed_date'           => '2024-11-19 14:45:31',
                'gateway'                  => 'bogus',
                'is_store_credit'          => false,
                'kind'                     => 'credit',
                'payment_code'             => '#1206.2',
                'payment_method'           => 'card',
                'state'                    => 'complete',
                'status'                   => 'SUCCESS'
            ],
            [
                'amount'                   => 795.49,
                'card_bin'                 => '1',
                'card_last_4_digits'       => '•••• •••• •••• 1',
                'channel_transaction_code' => '6786918678666',
                'completed_date'           => '2024-11-19 14:45:55',
                'gateway'                  => 'bogus',
                'is_store_credit'          => false,
                'kind'                     => 'credit',
                'payment_code'             => '#1206.3',
                'payment_method'           => 'card',
                'state'                    => 'complete',
                'status'                   => 'SUCCESS'
            ]
        ]), $refunds[0]->transactions);

        $this->assertTotalsAndCounts($shopifyOrder, $refunds, $rma_list);
    }

    /**
     * @throws UnprocessableEntity
     * @throws ServerError
     * @throws NotImplemented
     * @throws Validation
     * @throws GuzzleException
     * @throws Exception
     */
    public function testSetRefundsWithExchangeAndCreditCardRefund()
    {
        $this->setUpTest();

        $channel_order_data = file_get_contents(__DIR__ . '/mocks/channel_order_with_credit_card_refund.json');
        $shopify_order_data = file_get_contents(__DIR__ . '/mocks/shopify_order_with_exchange_and_credit_card_return.json');

        $channelOrder = new vo\ChannelOrder(Utils::jsonDecode($channel_order_data));
        $shopifyOrder = new Order(Utils::jsonDecode($shopify_order_data));

        $rma_list = $this->getRMAs(
            'shopify_order_with_exchange_and_credit_card_return',
            'returngo_rma_exchange_credit_card_refund');

        ReturnGo::setRefunds($channelOrder, $shopifyOrder, $this->getMockTransformArgs($rma_list, []));
        $refunds = $channelOrder->refunds;

        $this->assertCount(1, $refunds);
        $this->assertEquals($channelOrder->channel_id, $refunds[0]->channel_id);
        $this->assertEquals('#1187-R1', $refunds[0]->channel_refund_code);
        $this->assertEquals(vo\Refund::CONNECTOR_STATE_COMPLETE, $refunds[0]->channel_refund_state);
        $this->assertEquals(2016, $refunds[0]->amount_incl);

        $this->assertEquals(vo\RefundMeta::createArray([
            [
                'key'   => ReturnGo::REFUND_META_RETURNGO_RMA_ID_KEY,
                'value' => '5276945'
            ],
            [
                'key'   => ReturnGo::REFUND_META_RETURNGO_EXCHANGE_ORDER_NAMES_KEY,
                'value' => '#1188'
            ]
        ]), $refunds[0]->meta);

        // Product lines
        $this->assertEquals(vo\RefundItem::createArray([
            [
                'code'             => 'item',
                'meta'             => [
                    [
                        'key'   => sprintf('returngo_qty_%s', ReturnGo::RESOLUTION_TYPE_INSTANT_VARIANT_EXCHANGE),
                        'value' => 2
                    ],
                    [
                        'key'   => sprintf('returngo_qty_%s', ReturnGo::RESOLUTION_TYPE_REFUND_TO_PAYMENT_METHOD),
                        'value' => 2
                    ]
                ],
                'sku'              => 'SKU001',
                'title'            => 'Product Y',
                'qty'              => 4,
                'unit_amount_incl' => 399.0,
                'subtotal_incl'    => 1596.0,
                'subtotal_tax'     => 208.16
            ],
            [
                'code'             => 'item',
                'meta'             => [
                    [
                        'key'   => sprintf('returngo_qty_%s', ReturnGo::RESOLUTION_TYPE_INSTANT_VARIANT_EXCHANGE),
                        'value' => 2
                    ],
                    [
                        'key'   => sprintf('returngo_qty_%s', ReturnGo::RESOLUTION_TYPE_REFUND_TO_PAYMENT_METHOD),
                        'value' => 2
                    ]
                ],
                'sku'              => 'SKU002',
                'title'            => 'Product X',
                'qty'              => 4,
                'unit_amount_incl' => 599.0,
                'subtotal_incl'    => 2356.0,
                'subtotal_tax'     => 307.32
            ]
        ]), $refunds[0]->getItemLines());

        // No shipping lines
        $this->assertEmpty($refunds[0]->getShippingLines());

        // Discrepancy lines
        $discrepancyLines = $refunds[0]->getDiscrepancyLines();
        $this->assertEquals(vo\RefundItem::createArray([
            [
                'code'             => 'discrep',
                'meta'             => [
                    [
                        'key'   => 'returngo_discrepancy_amount_InstantVariantExchange',
                        'value' => '-1976'
                    ],
                    [
                        'key'   => 'returngo_fee_InstantVariantExchange',
                        'value' => '5'
                    ],
                    [
                        'key'   => 'returngo_discrepancy_amount_RefundToPaymentMethod',
                        'value' => '40'
                    ],
                    [
                        'key'   => 'returngo_fee_RefundToPaymentMethod',
                        'value' => '5'
                    ]
                ],
                'title'            => 'Refund discrepancy',
                'unit_amount_incl' => -1936.0,
                'subtotal_incl'    => -1936.0,
                'subtotal_tax'     => 0
            ]
        ]), $discrepancyLines);

        $this->assertEquals(vo\Transaction::createArray([
            [
                'amount'                   => 2016.0,
                'card_bin'                 => '1',
                'card_last_4_digits'       => '•••• •••• •••• 1',
                'channel_transaction_code' => '6777710903434',
                'completed_date'           => '2024-11-13 12:39:59',
                'gateway'                  => 'bogus',
                'is_store_credit'          => false,
                'kind'                     => 'credit',
                'payment_code'             => '#1187.2',
                'payment_method'           => 'card',
                'state'                    => vo\Transaction::STATE_COMPLETE,
                'status'                   => 'SUCCESS'
            ]

        ]), $refunds[0]->transactions);

        $this->assertTotalsAndCounts($shopifyOrder, $refunds, $rma_list);

        // Assert that the correct meta was set on the refund
        $this->assertCount(2, $refunds[0]->meta);
        $this->assertEquals(ReturnGo::REFUND_META_RETURNGO_RMA_ID_KEY, $refunds[0]->meta[0]->key);
        $this->assertEquals('5276945', $refunds[0]->meta[0]->value);
        $this->assertEquals(ReturnGo::REFUND_META_RETURNGO_EXCHANGE_ORDER_NAMES_KEY, $refunds[0]->meta[1]->key);
        $this->assertEquals('#1188', $refunds[0]->meta[1]->value);
    }

    /**
     * @param Order $shopifyOrder
     * @param array<vo\Refund> $refunds
     * @param RMA[] $rma_list
     * @return void
     * @throws Exception
     */
    private function assertTotalsAndCounts(Order $shopifyOrder, array $refunds, array $rma_list)
    {
        $rma_data_array = $rma_list[0];

        // Qty check - Total qty must equal total rma item count
        $total_item_qty = Utils::sumInt($refunds[0]->line_items,
            function (vo\RefundItem $item) {
                return $item->code === vo\RefundItem::CODE_ITEM ? $item->qty : 0;
            });
        $this->assertEquals(count($rma_data_array->items), $total_item_qty);

        $discrepancyLines = $refunds[0]->getDiscrepancyLines();

        $total_discrepancies    = 0;
        $total_fees_from_meta   = 0;
        $positive_discrepancies = 0;
        foreach ($discrepancyLines[0]->meta as $discrepancy_meta) {
            if (strpos($discrepancy_meta->key, 'returngo_discrepancy_amount') !== false) {
                $total_discrepancies += (float)$discrepancy_meta->value;
                if ($discrepancy_meta->value > 0) {
                    $positive_discrepancies += $discrepancy_meta->value;
                }
            } else {
                $total_fees_from_meta += (float)$discrepancy_meta->value;
            }
        }
        $this->assertEquals($discrepancyLines[0]->subtotal_incl, $total_discrepancies);

        // Shipping check - Shipping should equal total positive discrepancies + total fees
        if ($positive_discrepancies > 0) {
            $this->assertEquals($shopifyOrder->shippingLines[0]->discountedPriceSet->shopMoney->amount,
                $positive_discrepancies + $total_fees_from_meta);
        }

        // Fees checks - Total fees from meta should equal total fees from rma items
        $total_fees_from_payload_data = Utils::sumFloat($rma_data_array->items,
            function (RMAItem $item) {
                return
                    $item->restockingFee + $item->returnFee + $item->shipmentFee;
            });
        $this->assertEquals($total_fees_from_payload_data, $total_fees_from_meta);

        // Total amount check - Item subtotals should add up to total transaction amount
        $product_item_subtotal_total = Utils::sumFloat($refunds[0]->line_items,
            function (vo\RefundItem $item) {
                return $item->subtotal_incl;
            });
        $transactions_total          = Utils::sumFloat($refunds[0]->transactions,
            function (vo\Transaction $item) {
                return $item->amount;
            });
        $this->assertEqualsWithDelta($product_item_subtotal_total, $transactions_total, ReturnGo::DIFF_CHECK_PRECISION);
    }

    /**
     * @throws UnprocessableEntity
     * @throws ServerError
     * @throws NotImplemented
     * @throws Validation
     * @throws GuzzleException
     * @throws Exception
     */
    public function testSetRefundsInProgress()
    {
        $this->setUpTest();

        $channel_order_data = file_get_contents(__DIR__ . '/mocks/channel_order_with_credit_card_refund.json');
        $shopify_order_data = file_get_contents(__DIR__ . '/mocks/shopify_order_with_exchange_and_credit_card_return.json');

        $channelOrder = new vo\ChannelOrder(Utils::jsonDecode($channel_order_data));
        $shopifyOrder = new Order(Utils::jsonDecode($shopify_order_data));

        $rma_list                        = $this->getRMAs(
            'shopify_order_with_exchange_and_credit_card_return',
            'returngo_rma_exchange_credit_card_refund');
        $rma_list[0]->rmaSummary->status = 'Pending';

        $this->assertCount(1, $channelOrder->refunds);

        ReturnGo::setRefunds($channelOrder, $shopifyOrder, $this->getMockTransformArgs($rma_list, []));

        // Shopify refund has been removed, since it's linked to an RMA that hasn't been marked as done yet
        $this->assertEmpty($channelOrder->refunds);
    }

    /**
     * @throws UnprocessableEntity
     * @throws ServerError
     * @throws NotImplemented
     * @throws Validation
     * @throws GuzzleException
     * @throws Exception
     */
    public function testSetRefundsInProgressThatHasInvoicePaymentTransaction()
    {
        $this->setUpTest();

        $channel_order_data = file_get_contents(__DIR__ . '/mocks/channel_order_2.json');
        $shopify_order_data = file_get_contents(__DIR__ . '/mocks/shopify_order_with_in_progress_rma.json');

        $channelOrder = new vo\ChannelOrder(Utils::jsonDecode($channel_order_data));
        $shopifyOrder = new Order(Utils::jsonDecode($shopify_order_data));

        $rma_list = $this->getRMAs(
            'shopify_order_with_in_progress_rma',
            'returngo_rma_exchange_status_received'
        );
        ReturnGo::setRefunds($channelOrder, $shopifyOrder, $this->getMockTransformArgs($rma_list, []));

        // Shopify refund has been removed, since it's linked to an RMA that hasn't been marked as done yet
        $this->assertEmpty($channelOrder->refunds);
    }

    /**
     * @throws UnprocessableEntity
     * @throws GuzzleException
     */
    public function testSetRefundsFromReturnsWithQuantityNotExceedingOrderedQuantity()
    {
        $this->setUpTest();

        $channel_order_data = file_get_contents(__DIR__ . '/mocks/channel_order_with_return_2.json');
        $shopify_order_data = file_get_contents(__DIR__ . '/mocks/shopify_order_with_return_2.json');

        $channelOrder = new vo\ChannelOrder(Utils::jsonDecode($channel_order_data));
        $shopifyOrder = new Order(Utils::jsonDecode($shopify_order_data));

        $rma_list                        = $this->getRMAs(
            'shopify_order_with_return_2',
            'returngo_rma_exchange_credit_card_refund');

        /** @var vo\Refund $returnRefund */
//        list($channelOrder, $shopifyOrder, $returnRefund) = $this->setUpTest(true);
        // Ordered qty = 5
        // Refund 1 qty = 4
        // Refund 2 qty = 1
        // Diff = 0
//        $returnRefund->line_items[0]->qty = 4;

        // No exception
        ReturnGo::setRefunds($channelOrder, $shopifyOrder, $this->getMockTransformArgs($rma_list, []));
    }

    /**
     * @throws GuzzleException
     * @throws UnprocessableEntity
     */
    public function testSetRefundsFromReturnsWithQuantityExceedingOrderedQuantity()
    {
        $this->setUpTest();

        $channel_order_data = file_get_contents(__DIR__ . '/mocks/channel_order_with_return_2.json');
        $shopify_order_data = file_get_contents(__DIR__ . '/mocks/shopify_order_with_return_2.json');

        $channelOrder = new vo\ChannelOrder(Utils::jsonDecode($channel_order_data));
        $shopifyOrder = new Order(Utils::jsonDecode($shopify_order_data));

        $rma_list                        = $this->getRMAs(
            'shopify_order_with_return_2',
            'returngo_rma_exchange_credit_card_refund');

        // Add additional unit
        $rma_list[0]->items[] = $rma_list[0]->items[0];

        // Ordered qty = 5
        // Refund 1 qty = 5
        // Refund 2 qty = 1
        // Qty exceeded by 1

        $this->expectExceptionMessage('Refunded quantity for SKU sku001 exceeds ordered quantity by 1.');

        // No exception
        ReturnGo::setRefunds($channelOrder, $shopifyOrder, $this->getMockTransformArgs($rma_list, []));
    }

    /**
     * @throws GuzzleException
     * @throws UnprocessableEntity
     */
    public function testSetRefundsFromReturnsWithAmountNotExceedingOrderedAmount()
    {
        $this->setUpTest();

        $channel_order_data = file_get_contents(__DIR__ . '/mocks/channel_order_with_return_2.json');
        $shopify_order_data = file_get_contents(__DIR__ . '/mocks/shopify_order_with_return_2.json');

        $channelOrder = new vo\ChannelOrder(Utils::jsonDecode($channel_order_data));
        $shopifyOrder = new Order(Utils::jsonDecode($shopify_order_data));

        $rma_list                        = $this->getRMAs(
            'shopify_order_with_return_2',
            'returngo_rma_exchange_credit_card_refund');

        // Add additional unit
        $channelOrder->refunds[0]->transactions[0]->amount += 1936;
        $rma_list[0]->transactions[0]->amount->shopAmount += 1936;

        // Total order transaction amount = 4990
        // Refund 1 total = 2016 + 1936
        // Refund 2 total = 1038
        // Diff = 0

        // No exception
        ReturnGo::setRefunds($channelOrder, $shopifyOrder, $this->getMockTransformArgs($rma_list, []));
    }

    /**
     * @throws UnprocessableEntity
     * @throws GuzzleException
     */
    public function testSetRefundsFromReturnsWithAmountExceedingOrderedAmount()
    {
        $this->setUpTest();

        $channel_order_data = file_get_contents(__DIR__ . '/mocks/channel_order_with_return_2.json');
        $shopify_order_data = file_get_contents(__DIR__ . '/mocks/shopify_order_with_return_2.json');

        $channelOrder = new vo\ChannelOrder(Utils::jsonDecode($channel_order_data));
        $shopifyOrder = new Order(Utils::jsonDecode($shopify_order_data));

        $rma_list                        = $this->getRMAs(
            'shopify_order_with_return_2',
            'returngo_rma_exchange_credit_card_refund');

        // Add additional unit
        $channelOrder->refunds[0]->transactions[0]->amount += 1936.019;
        $rma_list[0]->transactions[0]->amount->shopAmount += 1936.019;

        // Total order transaction amount = 4990
        // Refund 1 total = 2016 + 1936.019
        // Refund 2 total = 1038
        // Amount exceeded by 0.02

        $this->expectExceptionMessage('Total refunded amount exceeds total order amount by 0.02.');

        ReturnGo::setRefunds($channelOrder, $shopifyOrder, $this->getMockTransformArgs($rma_list, []));
    }

    /**
     * This test case occurs when there is an order that the customer placed, having a payment method of `peach`.
     * 
     * When the order is refunded to the OriginalPaymentMethod, the refund is set to peach payments
     * But because there is a delay in it being processed, i.e it's not immediate, Shopify adds in additional
     * discrepancy lines.
     * 
     * We are testing the functionality that removes this added refund to the shopifyOrder if:
     * 
     * - All lines are discrepancy lines on the refund
     * - The total amount on the refund is less than a really small number (it will be removed if the total is zero)
     * 
     * This refund failed initially, but was reattempted.
     * 
     * @throws UnprocessableEntity
     * @throws GuzzleException
     * @throws Validation
     * @throws Exception
     */
    public function testSetRefundsWithReturnThatHasBeenRefundedToPeachPaymentsAndReattempt()
    {
        $this->setUpTest();

        // sync refunded exchange order
        $channel_order_data = file_get_contents(
            __DIR__ . '/mocks/channel_order_refunded_to_peach_payment_and_reattempt.json'
        );
        $shopify_order_data = file_get_contents(
            __DIR__ . '/mocks/shopify_order_refunded_to_peach_payment_and_reattempt.json'
        );

        $channelOrder = new vo\ChannelOrder(Utils::jsonDecode($channel_order_data));
        $shopifyOrder = new Order(Utils::jsonDecode($shopify_order_data));

        $rma_list = $this->getRMAs(
            'shopify_order_refunded_to_peach_payment_and_reattempt',
            'returngo_rma_refunded_to_peach_payment_and_reattempt'
        );

        $this->expectException(Exception::class);
        $this->expectExceptionMessage(
            "Discrepancy between refund item and transaction amount totals of 649.00 for Refund LSSA-43397-R1. [Refund has failed transaction]"
        );

        ReturnGo::setRefunds(
            $channelOrder,
            $shopifyOrder,
            $this->getMockTransformArgs($rma_list)
        );
    }

    /**
     * This order has been paid through peach payments and it was successful.
     * This order has an exchange (orderB), but this test case just tests orderA's sync
     * 
     * @throws UnprocessableEntity
     * @throws GuzzleException
     * @throws Validation
     * @throws Exception
     */
    public function testSetRefundsWithReturnThatHasBeenRefundedToPeachPayments()
    {
        $this->setUpTest();
        $channel_order_data = file_get_contents(
            __DIR__ . '/mocks/channel_order_refund_peach_payment_order.json'
        );
        $shopify_order_data = file_get_contents(
            __DIR__ . '/mocks/shopify_order_refund_peach_payment_order.json'
        );

        $channelOrder = new vo\ChannelOrder(Utils::jsonDecode($channel_order_data));
        $shopifyOrder = new Order(Utils::jsonDecode($shopify_order_data));

        $rma_list = $this->getRMAs(
            'shopify_order_refund_peach_payment_order',
            'returngo_rma_refund_peach_payment_order'
        );

        ReturnGo::setRefunds(
            $channelOrder,
            $shopifyOrder,
            $this->getMockTransformArgs($rma_list)
        );

        $refunds = $channelOrder->refunds;

        $this->assertCount(2, $refunds);

        // the refund has a transaction because we could not detect any discrepancies
        $this->assertCount(1, $refunds[0]->transactions);
        $this->assertCount(2, $refunds[0]->getDiscrepancyLines());
        $this->assertCount(0, $refunds[0]->getItemLines());
        $this->assertCount(0, $refunds[0]->meta);
        $this->assertEquals(0, $refunds[0]->amount_incl);

        $this->assertCount(0, $refunds[1]->transactions);
        $this->assertCount(1, $refunds[1]->getDiscrepancyLines());
        $this->assertCount(1, $refunds[1]->getItemLines());
        $this->assertCount(2, $refunds[1]->meta);
        $this->assertEquals(0, $refunds[1]->amount_incl);

        // Assert that the correct meta was set on the refund
        $this->assertEmpty($refunds[0]->meta);
        
        $this->assertCount(2, $refunds[1]->meta);
        $this->assertEquals(ReturnGo::REFUND_META_RETURNGO_RMA_ID_KEY, $refunds[1]->meta[0]->key);
        $this->assertEquals('6769990', $refunds[1]->meta[0]->value);
        $this->assertEquals(ReturnGo::REFUND_META_RETURNGO_EXCHANGE_ORDER_NAMES_KEY, $refunds[1]->meta[1]->key);
        $this->assertEquals('LSSA-47714', $refunds[1]->meta[1]->value);
    }

    /**
     * So this test case tests refunding an exchange order (orderB) whose orderA was paid with peach payments.)
     * 
     * The refund on ReturnGo is not complete, but this does NOT affect the syncing of this exchange order because we
     * do not sync any transaction for the exchange order (right now)
     * @return void 
     */
    public function testSetRefundsWithRefundedExchangeOrderWhoseOrderAWasPaidWithPeach()
    {
        $this->setUpTest();

        // sync refunded exchange order
        $channel_order_data = file_get_contents(
            __DIR__ . '/mocks/channel_order_refunded_exchange_order_paid_originally_with_peach.json'
        );
        $shopify_order_data = file_get_contents(
            __DIR__ . '/mocks/shopify_order_refunded_exchange_order_paid_originally_with_peach.json'
        );

        $channelOrder = new vo\ChannelOrder(Utils::jsonDecode($channel_order_data));
        $shopifyOrder = new Order(Utils::jsonDecode($shopify_order_data));

        $rma_list = $this->getRMAs(
            'shopify_order_refunded_exchange_order_paid_originally_with_peach',
            'returngo_rma_refunded_exchange_order_paid_originally_with_peach'
        );

        $this->expectException(Exception::class);
        $this->expectExceptionMessage(
            'RMA 7054664 is marked as done, but has an incomplete transaction 936008679513.'
        );

        ReturnGo::setRefunds(
            $channelOrder,
            $shopifyOrder,
            $this->getMockTransformArgs($rma_list)
        );

        $refunds = $channelOrder->refunds;

        $this->assertCount(1, $refunds);
        $this->assertCount(0, $refunds[0]->transactions);

        // assert lines
        $this->assertCount(0, $refunds[0]->getDiscrepancyLines());
        $this->assertCount(1, $refunds[0]->getItemLines());

        // assert meta values
        $this->assertCount(2, $refunds[0]->meta);
        $this->assertEquals(
            ReturnGo::REFUND_META_RETURNGO_TRANSACTION_COMPLETED_ON_ANOTHER_ORDER,
            $refunds[0]->meta[1]->key
        );
        $this->assertEquals("936008679513", $refunds[0]->meta[1]->value);

        $this->assertEquals(ReturnGo::REFUND_META_RETURNGO_RMA_ID_KEY, $refunds[0]->meta[0]->key);
        $this->assertEquals('5276945', $refunds[0]->meta[0]->value);

        // assert refund total
        $this->assertEquals(1799, $refunds[0]->amount_incl);
    }

    /**
     * This is an Refund that has a successful peach transaction, but it fails anyway because the ResolutionType
     * that the customer requested (`InstantVariantExchange`)
     * differs from the Resolution that was made (`RefundToPaymentMethod`).
     * 
     * @throws UnprocessableEntity
     * @throws GuzzleException
     * @throws Validation
     * @throws Exception
     */
    public function testSetRefundsWithReturnThatHasBeenRefundedToPeachPaymentWithSuccessfulTransaction()
    {
        $this->setUpTest();

        // sync refunded exchange order
        $channel_order_data = file_get_contents(
            __DIR__ . '/mocks/channel_order_refunded_to_peach_payment_success.json'
        );
        $shopify_order_data = file_get_contents(
            __DIR__ . '/mocks/shopify_order_refunded_to_peach_payment_success.json'
        );

        $channelOrder = new vo\ChannelOrder(Utils::jsonDecode($channel_order_data));
        $shopifyOrder = new Order(Utils::jsonDecode($shopify_order_data));

        $rma_list = $this->getRMAs(
            'shopify_order_refunded_to_peach_payment_success',
            'returngo_rma_refunded_to_peach_payment_success'
        );

        ReturnGo::setRefunds(
            $channelOrder,
            $shopifyOrder,
            $this->getMockTransformArgs($rma_list)
        );

        $refunds = $channelOrder->refunds;

        $this->assertCount(1, $refunds);
        $this->assertEquals(549, $refunds[0]->amount_incl);

        $this->assertCount(1, $refunds[0]->transactions);
        $this->assertEquals(549, $refunds[0]->transactions[0]->amount);
        $this->assertEquals("zuueFFgVH5sk8E2NCTk6zWoX6", $refunds[0]->transactions[0]->payment_code);
        $this->assertNull($refunds[0]->transactions[0]->payment_method);

        $this->assertCount(1, $refunds[0]->line_items);
        $this->assertEquals("A91140009L-", $refunds[0]->line_items[0]->sku);
        $this->assertEquals(549, $refunds[0]->line_items[0]->subtotal_incl);
        $this->assertEquals(71.6, $refunds[0]->line_items[0]->subtotal_tax);
        $this->assertCount(1, $refunds[0]->line_items[0]->meta);
        $this->assertEquals("returngo_qty_RefundToPaymentMethod", $refunds[0]->line_items[0]->meta[0]->key);
        $this->assertEquals("1", $refunds[0]->line_items[0]->meta[0]->value);

        // this order's exchange order is still in draft so the `returngo_exchange_order_names`
        // meta is not included
        $this->assertCount(1, $refunds[0]->meta);
        $this->assertEquals(ReturnGo::REFUND_META_RETURNGO_RMA_ID_KEY, $refunds[0]->meta[0]->key);
        $this->assertEquals('6839152', $refunds[0]->meta[0]->value);
    }

    /**
     * This test case is for a refund that has a ResolutionType of `InstantVariantExchange`, but the actual Resolution
     * that was done is a `RefundToPaymentMethod`.
     * 
     * This creates a transaction inside the RMA and results in discrepancies.
     * We need to ignore the `InstantVariantExchange` on the line and replace it with `RefundToPaymentMethod` ResolutionType
     * 
     * @return void 
     */
    public function testSetRefundsWithReturnHavingExchangeButWasRefundedToPaymentMethod()
    {
        $this->setUpTest();

        // sync refunded exchange order
        $channel_order_data = file_get_contents(
            __DIR__ . '/mocks/channel_order_exchange_with_refund_payment_transaction.json'
        );
        $shopify_order_data = file_get_contents(
            __DIR__ . '/mocks/shopify_order_exchange_with_refund_payment_transaction.json'
        );

        $channelOrder = new vo\ChannelOrder(Utils::jsonDecode($channel_order_data));
        $shopifyOrder = new Order(Utils::jsonDecode($shopify_order_data));

        $rma_list = $this->getRMAs(
            'shopify_order_exchange_with_refund_payment_transaction',
            'returngo_rma_exchange_with_refund_payment_transaction'
        );

        ReturnGo::setRefunds(
            $channelOrder,
            $shopifyOrder,
            $this->getMockTransformArgs($rma_list)
        );

        $refunds = $channelOrder->refunds;

        $this->assertCount(1, $refunds);
        $this->assertEquals(1799, $refunds[0]->amount_incl);

        $this->assertCount(1, $refunds[0]->transactions);
        $this->assertEquals(1799, $refunds[0]->transactions[0]->amount);
        $this->assertEquals("zSswbzaQknjibMMmVCoWusrC4", $refunds[0]->transactions[0]->payment_code);
        $this->assertNull($refunds[0]->transactions[0]->payment_method);

        $this->assertCount(1, $refunds[0]->line_items);
        $this->assertEquals("0038W0000XL-", $refunds[0]->line_items[0]->sku);
        $this->assertEquals(1799, $refunds[0]->line_items[0]->subtotal_incl);
        $this->assertEquals(234.65, $refunds[0]->line_items[0]->subtotal_tax);

        $this->assertCount(1, $refunds[0]->line_items[0]->meta);
        $this->assertEquals("returngo_qty_RefundToPaymentMethod", $refunds[0]->line_items[0]->meta[0]->key);
        $this->assertEquals("1", $refunds[0]->line_items[0]->meta[0]->value);

        // this order's exchange order is still in draft so the `returngo_exchange_order_names`
        // meta is not included
        $this->assertCount(1, $refunds[0]->meta);
        $this->assertEquals(ReturnGo::REFUND_META_RETURNGO_RMA_ID_KEY, $refunds[0]->meta[0]->key);
        $this->assertEquals('6903762', $refunds[0]->meta[0]->value);
    }

    /**
     * This test case tests syncing a refund that has a Mixed RMA.
     * 
     * The mixed RMA has two resolutionTypes:
     * 1. `RefundToPaymentMethod`
     * 2. `InstantVariantExchange`
     * 
     * However, instead of releasing the exchange, a `RefundToPaymentMethod` was done (on both items)
     * 
     * We need to ignore the `InstantVariantExchange` on the line
     * and replace it with `RefundToPaymentMethod` ResolutionType
     * 
     * @return void
     */
    public function testSetRefundsWithMixedRMAContainingExchangeAndRefundButWasFullyRefunded()
    {
        $this->setUpTest();

        // sync refunded exchange order
        $channel_order_data = file_get_contents(
            __DIR__ . '/mocks/channel_order_mixed_resolution_refunded_to_payment_method.json'
        );
        $shopify_order_data = file_get_contents(
            __DIR__ . '/mocks/shopify_order_mixed_resolution_refunded_to_payment_method.json'
        );

        $channelOrder = new vo\ChannelOrder(Utils::jsonDecode($channel_order_data));
        $shopifyOrder = new Order(Utils::jsonDecode($shopify_order_data));

        $rma_list = $this->getRMAs(
            'shopify_order_mixed_resolution_refunded_to_payment_method',
            'returngo_rma_mixed_resolution_refunded_to_payment_method'
        );

        // So, this fails for now because we do not actually know if the transactions associated to these lines
        // have been processed or not.
        // However, once we have a method to flag whether these are actually complete
        // then we will be able to by pass these errors.
        $this->expectException(Exception::class);
        $this->expectExceptionMessage(
            'Discrepancy between refund item and transaction amount totals of 1148.00 for Refund LSSA-43532-R1. [Refund has failed transaction]'
        );

        ReturnGo::setRefunds(
            $channelOrder,
            $shopifyOrder,
            $this->getMockTransformArgs($rma_list)
        );
    }

    /**
     * This test case tests syncing a refund that has a Mixed RMA.
     * 
     * The mixed RMA has two resolutionTypes:
     * 1. `RefundToStoreCredit`
     * 2. `InstantVariantExchange`
     * 
     * However, instead of releasing the exchange, and refunding the store credit
     * a `RefundToPaymentMethod` was done
     * 
     * We need to ignore the `InstantVariantExchange` and the `RefundToStoreCredit` on the lines
     * and replace it with `RefundToPaymentMethod` ResolutionType
     * 
     * @return void
     */
    public function testSetRefundsWithMixedRMAContainingExchangeAndStoreCreditButWasFullyRefunded()
    {
        $this->setUpTest();

        // sync refunded exchange order
        $channel_order_data = file_get_contents(
            __DIR__ . '/mocks/channel_order_exchange_and_refund_to_store_credit_refunded_to_payment_method.json'
        );
        $shopify_order_data = file_get_contents(
            __DIR__ . '/mocks/shopify_order_exchange_and_refund_to_store_credit_refunded_to_payment_method.json'
        );

        $channelOrder = new vo\ChannelOrder(Utils::jsonDecode($channel_order_data));
        $shopifyOrder = new Order(Utils::jsonDecode($shopify_order_data));

        $rma_list = $this->getRMAs(
            'shopify_order_exchange_and_refund_to_store_credit_refunded_to_payment_method',
            'returngo_rma_exchange_and_refund_to_store_credit_refunded_to_payment_method'
        );

        ReturnGo::setRefunds(
            $channelOrder,
            $shopifyOrder,
            $this->getMockTransformArgs($rma_list)
        );

        $refunds = $channelOrder->refunds;

        $this->assertCount(1, $refunds);
        $this->assertEquals(1648, $refunds[0]->amount_incl);

        $this->assertCount(1, $refunds[0]->transactions);
        $this->assertEquals(1648, $refunds[0]->transactions[0]->amount);
        $this->assertEquals("zPp1ZfzBelcB8IYgLfHtCJ9mC", $refunds[0]->transactions[0]->payment_code);
        $this->assertNull($refunds[0]->transactions[0]->payment_method);

        $this->assertCount(2, $refunds[0]->line_items);
        $this->assertEquals(2883313113634, $refunds[0]->line_items[0]->sku);
        $this->assertEquals(849, $refunds[0]->line_items[0]->subtotal_incl);
        $this->assertEquals(110.74, $refunds[0]->line_items[0]->subtotal_tax);
        $this->assertCount(1, $refunds[0]->line_items[0]->meta);
        $this->assertEquals("returngo_qty_RefundToPaymentMethod", $refunds[0]->line_items[0]->meta[0]->key);
        $this->assertEquals("1", $refunds[0]->line_items[0]->meta[0]->value);

        $this->assertEquals("A405600613634", $refunds[0]->line_items[1]->sku);
        $this->assertEquals(799, $refunds[0]->line_items[1]->subtotal_incl);
        $this->assertEquals(104.22, $refunds[0]->line_items[1]->subtotal_tax);
        $this->assertCount(1, $refunds[0]->line_items[1]->meta);
        $this->assertEquals("returngo_qty_RefundToPaymentMethod", $refunds[0]->line_items[1]->meta[0]->key);
        $this->assertEquals("1", $refunds[0]->line_items[1]->meta[0]->value);

        // this order's exchange order is still in draft so the `returngo_exchange_order_names`
        // meta is not included
        $this->assertCount(1, $refunds[0]->meta);
        $this->assertEquals(ReturnGo::REFUND_META_RETURNGO_RMA_ID_KEY, $refunds[0]->meta[0]->key);
        $this->assertEquals('6997640', $refunds[0]->meta[0]->value);
    }

    /**
     * This is the same test case as above, but I slightly adjusted the RMA to have multiple exchangeOrders
     * I.e one is a draft (`#D` prefix) and the other one not - to see how the code handles this scenario.
     * 
     * NOTE that this was not yet replicated in reality, but since exchangeOrders is an array, we can
     * assume that it might have multiple objects.
     * 
     * @return void
     */
    public function testSetRefundsWithRMAHavingMultipleExchangeOrders()
    {
        $this->setUpTest();

        // sync refunded exchange order
        $channel_order_data = file_get_contents(
            __DIR__ . '/mocks/channel_order_exchange_and_refund_to_store_credit_refunded_to_payment_method.json'
        );
        $shopify_order_data = file_get_contents(
            __DIR__ . '/mocks/shopify_order_exchange_and_refund_to_store_credit_refunded_to_payment_method.json'
        );

        $channelOrder = new vo\ChannelOrder(Utils::jsonDecode($channel_order_data));
        $shopifyOrder = new Order(Utils::jsonDecode($shopify_order_data));

        $rma_list = $this->getRMAs(
            'shopify_order_exchange_and_refund_to_store_credit_refunded_to_payment_method',
            'returngo_rma_exchange_with_multiple_exchange_orders'
        );

        ReturnGo::setRefunds(
            $channelOrder,
            $shopifyOrder,
            $this->getMockTransformArgs($rma_list)
        );

        $refunds = $channelOrder->refunds;

        $this->assertCount(1, $refunds);
        $this->assertEquals(1648, $refunds[0]->amount_incl);

        $this->assertCount(1, $refunds[0]->transactions);
        $this->assertEquals(1648, $refunds[0]->transactions[0]->amount);
        $this->assertEquals("zPp1ZfzBelcB8IYgLfHtCJ9mC", $refunds[0]->transactions[0]->payment_code);
        $this->assertNull($refunds[0]->transactions[0]->payment_method);

        $this->assertCount(2, $refunds[0]->line_items);
        $this->assertEquals(2883313113634, $refunds[0]->line_items[0]->sku);
        $this->assertEquals(849, $refunds[0]->line_items[0]->subtotal_incl);
        $this->assertEquals(110.74, $refunds[0]->line_items[0]->subtotal_tax);
        $this->assertCount(1, $refunds[0]->line_items[0]->meta);
        $this->assertEquals("returngo_qty_RefundToPaymentMethod", $refunds[0]->line_items[0]->meta[0]->key);
        $this->assertEquals("1", $refunds[0]->line_items[0]->meta[0]->value);

        $this->assertEquals("A405600613634", $refunds[0]->line_items[1]->sku);
        $this->assertEquals(799, $refunds[0]->line_items[1]->subtotal_incl);
        $this->assertEquals(104.22, $refunds[0]->line_items[1]->subtotal_tax);
        $this->assertCount(1, $refunds[0]->line_items[1]->meta);
        $this->assertEquals("returngo_qty_RefundToPaymentMethod", $refunds[0]->line_items[1]->meta[0]->key);
        $this->assertEquals("1", $refunds[0]->line_items[1]->meta[0]->value);

        // So, because this order has at least 1 exchange order that is not in draft
        // we have added the meta
        $this->assertCount(2, $refunds[0]->meta);
        $this->assertEquals(ReturnGo::REFUND_META_RETURNGO_RMA_ID_KEY, $refunds[0]->meta[0]->key);
        $this->assertEquals('6997640', $refunds[0]->meta[0]->value);
        $this->assertEquals(ReturnGo::REFUND_META_RETURNGO_EXCHANGE_ORDER_NAMES_KEY, $refunds[0]->meta[1]->key);
        $this->assertEquals('LSSA-TEST-1234', $refunds[0]->meta[1]->value);
    }

    /**
     * This test case is for a refund that has a ResolutionType of `InstantVariantExchange`, but the actual Resolution
     * that was done is a `RefundToStoreCredit`.
     * 
     * This creates a transaction inside the RMA and results in discrepancies.
     * We need to ignore the `InstantVariantExchange` on the line
     * and replace it with `RefundToStoreCredit` ResolutionType
     * 
     * @return void 
     */
    public function testSetRefundsWithReturnHavingExchangeButWasRefundedToStoreCredit()
    {
        $this->setUpTest();

        // sync refunded exchange order
        $channel_order_data = file_get_contents(
            __DIR__ . '/mocks/channel_order_exchange_refunded_to_store_credit.json'
        );
        $shopify_order_data = file_get_contents(
            __DIR__ . '/mocks/shopify_order_exchange_refunded_to_store_credit.json'
        );

        $channelOrder = new vo\ChannelOrder(Utils::jsonDecode($channel_order_data));
        $shopifyOrder = new Order(Utils::jsonDecode($shopify_order_data));

        $rma_list = $this->getRMAs(
            'shopify_order_exchange_refunded_to_store_credit',
            'returngo_rma_exchange_refunded_to_store_credit'
        );

        ReturnGo::setRefunds(
            $channelOrder,
            $shopifyOrder,
            $this->getMockTransformArgs($rma_list)
        );

        $refunds = $channelOrder->refunds;

        $this->assertCount(1, $refunds);
        $this->assertEquals(699, $refunds[0]->amount_incl);

        $this->assertCount(1, $refunds[0]->transactions);
        $this->assertEquals(699, $refunds[0]->transactions[0]->amount);
        $this->assertEquals("OETRU6HT", $refunds[0]->transactions[0]->payment_code);
        $this->assertEquals("RefundToStoreCredit", $refunds[0]->transactions[0]->payment_method);

        $this->assertCount(1, $refunds[0]->line_items);
        $this->assertEquals("A693800183432", $refunds[0]->line_items[0]->sku);
        $this->assertEquals(699, $refunds[0]->line_items[0]->subtotal_incl);
        $this->assertEquals(91.17, $refunds[0]->line_items[0]->subtotal_tax);
        $this->assertCount(1, $refunds[0]->line_items[0]->meta);
        $this->assertEquals("returngo_qty_RefundToStoreCredit", $refunds[0]->line_items[0]->meta[0]->key);
        $this->assertEquals("1", $refunds[0]->line_items[0]->meta[0]->value);

        // this order's exchange order is still in draft so the `returngo_exchange_order_names`
        // meta is not included
        $this->assertCount(1, $refunds[0]->meta);
        $this->assertEquals(ReturnGo::REFUND_META_RETURNGO_RMA_ID_KEY, $refunds[0]->meta[0]->key);
        $this->assertEquals('6838865', $refunds[0]->meta[0]->value);
    }

    /**
     * This tests syncing a Refund whose RMA has been cancelled then reopened at a later stage.
     * I.e there exists two returns on the Shopify object, one having the status of `CANCELED` and the other
     * `CLOSED`
     * 
     * We will ONLY be considering the CLOSED returns. 
     * 
     * @throws UnprocessableEntity
     * @throws GuzzleException
     * @throws Validation
     * @throws Exception
     */
    public function testSetRefundsWithReturnThatHasCancelledThenReopened()
    {
        $this->setUpTest();
        $channel_order_data = file_get_contents(
            __DIR__ . '/mocks/channel_order_with_canceled_return.json'
        );
        $shopify_order_data = file_get_contents(
            __DIR__ . '/mocks/shopify_order_with_canceled_return.json'
        );

        $channelOrder = new vo\ChannelOrder(Utils::jsonDecode($channel_order_data));
        $shopifyOrder = new Order(Utils::jsonDecode($shopify_order_data));

        $rma_list = $this->getRMAs(
            'shopify_order_with_canceled_return',
            'returngo_rma_with_canceled_return'
        );

        ReturnGo::setRefunds(
            $channelOrder,
            $shopifyOrder,
            $this->getMockTransformArgs($rma_list)
        );

        $refunds = $channelOrder->refunds;

        $this->assertCount(1, $refunds);

        // the refund has a transaction because we could not detect any discrepancies
        $this->assertCount(1, $refunds[0]->transactions);
        $this->assertCount(0, $refunds[0]->getDiscrepancyLines());
        $this->assertCount(1, $refunds[0]->getItemLines());
        $this->assertCount(1, $refunds[0]->meta);
        $this->assertEquals(399, $refunds[0]->amount_incl);

        $this->assertEquals("0013S00013028", $refunds[0]->line_items[0]->sku);
        $this->assertEquals(399, $refunds[0]->line_items[0]->unit_amount_incl);
        $this->assertEquals(399, $refunds[0]->line_items[0]->subtotal_incl);
        $this->assertEquals(52.04, $refunds[0]->line_items[0]->subtotal_tax);
        $this->assertCount(1, $refunds[0]->line_items[0]->meta);

        // Assert that the correct meta was set on the refund
        $this->assertCount(1, $refunds[0]->meta);
        $this->assertEquals(ReturnGo::REFUND_META_RETURNGO_RMA_ID_KEY, $refunds[0]->meta[0]->key);
        $this->assertEquals('6487530', $refunds[0]->meta[0]->value);
    }
    
}