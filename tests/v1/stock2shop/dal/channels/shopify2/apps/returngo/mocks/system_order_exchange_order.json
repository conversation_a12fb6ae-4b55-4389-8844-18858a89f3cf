{"created": "2025-04-11 11:02:23.000000", "channel_order_code": "LSSA-QA-1070", "modified": "2025-04-11 11:02:23.000000", "channel_id": 2201, "client_id": 1054, "customer_id": null, "notes": "Exchanged via ReturnGO RMA6790076 Original order: LSSA-QA-1069", "status": "processing", "total_discount": 0, "ordered_date": "2025-04-11 11:01:56.000000", "has_taxes_incl": 1, "total_discount_incl": 0, "state": "processing", "is_inventory_adjusted": 1, "customer": {"id": null, "created": "2025-02-25 09:58:21.000000", "channel_customer_code": "8373954969788", "modified": "2025-04-17 11:17:16.000000", "channel_id": 2201, "client_id": 1054, "last_name": "<PERSON>", "first_name": "Lit", "email": "<EMAIL>", "accepts_marketing": 0, "active": 1, "hash": "b0845a332dc41d7ffa868741df7172cb"}, "history": [], "billing_address": {"id": 11472685, "created": "2025-04-11 11:02:23.000000", "address1": "Lente<PERSON>ur", "address2": "Test Apartment", "city": "Cape Town", "company": "Test Company", "country": "South Africa", "first_name": "Lit", "last_name": "<PERSON>", "phone": "+27 65 111 1111", "province": "Western Cape", "zip": "7786", "country_code": "ZA", "province_code": "WC", "modified": "2025-04-11 11:02:23.000000", "client_id": "1054"}, "shipping_address": {"id": 11472684, "created": "2025-04-11 11:02:23.000000", "address1": "Lente<PERSON>ur", "address2": "Test Apartment", "city": "Cape Town", "company": "Test Company", "country": "South Africa", "first_name": "Lit", "last_name": "<PERSON>", "phone": "+27 65 111 1111", "province": "Western Cape", "zip": "7786", "country_code": "ZA", "province_code": "WC", "modified": "2025-04-11 11:02:23.000000", "client_id": "1054"}, "line_items": [{"id": 29363042, "created": "2025-04-11 11:02:23.000000", "barcode": null, "price": 677.39, "qty": 1, "sku": "A544700182832", "modified": "2025-04-11 11:02:23.000000", "order_id": 6293934, "source_id": null, "variant_id": null, "title": "501  ORIGINAL ZA ON THE BORDERLINE", "grams": null, "code": "item", "total_discount": 677.39, "client_id": "1054", "price_incl": 779, "total_discount_incl": 779, "product_id": null, "source_variant_code": null, "tax_lines": [{"id": 30897905, "created": "2025-04-11 11:02:23.000000", "price": 0, "rate": 15, "title": "VAT", "code": "taxed", "modified": "2025-04-11 11:02:23.000000", "orderitem_id": "29363042", "client_id": "1054"}], "fulfillments": [{"id": 2967456, "created": "2025-04-11 11:02:23.000000", "active": 1, "status": null, "qty": 1, "fulfilled_qty": 0, "modified": "2025-04-14 08:44:08.000000", "fulfillment_id": 926566, "orderitem_id": 29363042, "client_id": "1054"}], "sub_total": 0, "sub_total_display": "0.00", "tax": 0, "tax_display": "0.00", "tax_per_unit": 0, "tax_per_unit_display": "0.00", "total": 0, "total_display": "0.00", "price_display": "677.39", "total_discount_display": "677.39", "totals_incl": {"sub_total": 0, "sub_total_display": "0.00", "tax": 0, "tax_display": "0.00", "tax_per_unit": 0, "tax_per_unit_display": "0.00", "total": 0, "total_display": "0.00", "price": 779, "price_display": "779.00", "total_discount": 779, "total_discount_display": "779.00"}}, {"id": 29363043, "created": "2025-04-11 11:02:23.000000", "barcode": null, "price": 677.39, "qty": 1, "sku": "A544700183032", "modified": "2025-04-11 11:02:23.000000", "order_id": 6293934, "source_id": null, "variant_id": null, "title": "501  ORIGINAL ZA ON THE BORDERLINE", "grams": null, "code": "item", "total_discount": 677.39, "client_id": "1054", "price_incl": 779, "total_discount_incl": 779, "product_id": null, "source_variant_code": null, "tax_lines": [{"id": 30897906, "created": "2025-04-11 11:02:23.000000", "price": 0, "rate": 15, "title": "VAT", "code": "taxed", "modified": "2025-04-11 11:02:23.000000", "orderitem_id": "29363043", "client_id": "1054"}], "fulfillments": [{"id": 2967457, "created": "2025-04-11 11:02:23.000000", "active": 1, "status": null, "qty": 1, "fulfilled_qty": 0, "modified": "2025-04-14 08:44:08.000000", "fulfillment_id": 926566, "orderitem_id": 29363043, "client_id": "1054"}], "sub_total": 0, "sub_total_display": "0.00", "tax": 0, "tax_display": "0.00", "tax_per_unit": 0, "tax_per_unit_display": "0.00", "total": 0, "total_display": "0.00", "price_display": "677.39", "total_discount_display": "677.39", "totals_incl": {"sub_total": 0, "sub_total_display": "0.00", "tax": 0, "tax_display": "0.00", "tax_per_unit": 0, "tax_per_unit_display": "0.00", "total": 0, "total_display": "0.00", "price": 779, "price_display": "779.00", "total_discount": 779, "total_discount_display": "779.00"}}, {"id": 29363044, "created": "2025-04-11 11:02:23.000000", "barcode": null, "price": 677.39, "qty": 1, "sku": "A544700183232", "modified": "2025-04-11 11:02:23.000000", "order_id": 6293934, "source_id": null, "variant_id": null, "title": "501  ORIGINAL ZA ON THE BORDERLINE", "grams": null, "code": "item", "total_discount": 677.39, "client_id": "1054", "price_incl": 779, "total_discount_incl": 779, "product_id": null, "source_variant_code": null, "tax_lines": [{"id": 30897907, "created": "2025-04-11 11:02:23.000000", "price": 0, "rate": 15, "title": "VAT", "code": "taxed", "modified": "2025-04-11 11:02:23.000000", "orderitem_id": "29363044", "client_id": "1054"}], "fulfillments": [{"id": 2967458, "created": "2025-04-11 11:02:23.000000", "active": 1, "status": null, "qty": 1, "fulfilled_qty": 0, "modified": "2025-04-14 08:44:08.000000", "fulfillment_id": 926566, "orderitem_id": 29363044, "client_id": "1054"}], "sub_total": 0, "sub_total_display": "0.00", "tax": 0, "tax_display": "0.00", "tax_per_unit": 0, "tax_per_unit_display": "0.00", "total": 0, "total_display": "0.00", "price_display": "677.39", "total_discount_display": "677.39", "totals_incl": {"sub_total": 0, "sub_total_display": "0.00", "tax": 0, "tax_display": "0.00", "tax_per_unit": 0, "tax_per_unit_display": "0.00", "total": 0, "total_display": "0.00", "price": 779, "price_display": "779.00", "total_discount": 779, "total_discount_display": "779.00"}}], "shipping_lines": [], "transactions": [], "fulfillments": [{"id": 926566, "created": "2025-04-11 11:02:23.000000", "active": 1, "status": null, "tracking_number": null, "tracking_company": null, "tracking_url": null, "notes": null, "modified": "2025-04-14 08:44:08.000000", "order_id": 6293934, "fulfillmentservice_id": 188, "fulfillmentservice_order_code": null, "client_id": "1054", "state": "error", "channel_synced": null, "channel_fulfillment_code": null, "channel_fulfillment_state": null}], "sub_total": 0, "sub_total_display": "0.00", "shipping_total": 0, "shipping_total_display": "0.00", "shipping_tax": 0, "shipping_tax_display": "0.00", "tax": 0, "tax_display": "0.00", "total_discount_display": "0.00", "total": 0, "total_display": "0.00", "totals_incl": {"sub_total": 0, "sub_total_display": "0.00", "shipping_total": 0, "shipping_total_display": "0.00", "shipping_tax": 0, "shipping_tax_display": "0.00", "tax": 0, "tax_display": "0.00", "total_discount": 0, "total_discount_display": "0.00", "total": 0, "total_display": "0.00"}}