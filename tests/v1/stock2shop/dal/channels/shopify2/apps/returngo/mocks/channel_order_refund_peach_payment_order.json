{"billing_address": {"id": null, "created": null, "modified": null, "address1": "Nottingham road", "address2": "Roslyn farm", "city": "Escourt", "company": null, "country": "South Africa", "country_code": "ZA", "first_name": "<PERSON>dis<PERSON><PERSON>", "last_name": "<PERSON>", "phone": "0609199106", "province": "KwaZulu-Natal", "province_code": "NL", "zip": "3280"}, "customer": {"channel_customer_code": "8155753250905", "accepts_marketing": true, "email": "<EMAIL>", "first_name": "<PERSON>dis-ann", "last_name": "Le roux"}, "instruction": "add_order", "line_items": [{"barcode": null, "grams": null, "price": 1564.*********, "price_incl": 1799, "qty": 1, "sku": "0038V0000L-", "tax_lines": [{"code": "taxed", "title": "VAT", "price": 234.65, "rate": 15}], "title": "LEVI'S® WOMEN'S BAGGY JUMPSUIT - MED INDIGO - WORN IN", "total_discount": 0, "total_discount_incl": 0}], "params": [], "shipping_address": {"id": null, "created": null, "modified": null, "address1": "R708 & Tandjiesberg roads", "address2": "Farm Matoane", "city": "Clocolan", "company": null, "country": "South Africa", "country_code": "ZA", "first_name": "<PERSON>dis<PERSON><PERSON>", "last_name": "<PERSON>", "phone": "0609199106", "province": "Free State", "province_code": "FS", "zip": "9735"}, "shipping_lines": [{"price": 0, "price_incl": 0, "tax_lines": [], "title": "Standard Shipping", "total_discount": 0, "total_discount_incl": 0}], "transactions": [{"amount": 1799, "amount_calculation_method": null, "auth_code": null, "card_bin": null, "card_last_4_digits": null, "channel_transaction_code": "7882245636185", "completed_date": "2025-04-06 17:12:24", "created": null, "gateway": "peach", "is_store_credit": false, "kind": "debit", "modified": null, "origin_connector_id": 2201, "origin_connector_kind": "channel", "order_id": null, "payment_code": "ri9jvkJLUi3mbkyr48MMHgTq9", "payment_method": null, "refund_identifier": null, "rrn": null, "stan": null, "state": "complete", "status": "SUCCESS", "transaction_identifier": null}], "channel_id": 2201, "channel_order_code": "LSSA-46915", "has_taxes_incl": true, "meta": [], "notes": "2025-04-09 16:44 UTC\nReturnGO, RMA6769990:\nRequest SUBMITTED\n\n2025-04-10 12:03 UTC\nReturnGO, RMA6769990:\nRequest APPROVED\nBy #267963\n\n2025-04-14 11:21 UTC\nReturnGO, RMA6769990:\nShipment IN TRANSIT (AUTOMATIC)\n\n2025-04-15 12:15 UTC\nReturnGO, RMA6769990:\nShipment RECEIVED (AUTOMATIC)\n\n2025-04-24 10:35 UTC\nReturnGO, RMA6769990:\nExchange order LSSA-47714 released\nBy <PERSON>\n\n2025-04-24 10:35 UTC\nReturnGO, RMA6769990:\nRequest DONE\nBy <PERSON>\n\n2025-05-15 10:13 UTC\nReturnGO\nR1,799.00 was refunded via RMA7054664 of order LSSA-47714 (exchange)\nBy <PERSON><PERSON><PERSON><PERSON>", "refunds": [{"amount_incl": 0, "channel_id": 2201, "channel_refund_code": "936008679513", "channel_refund_state": "complete", "channel_created_date": "2025-05-15 10:13:51", "client_id": null, "created": null, "fulfillment_created_date": null, "fulfillment_service_id": null, "fulfillment_refund_code": null, "fulfillment_refund_state": null, "line_items": [{"client_id": null, "code": "discrep", "created": null, "meta": [], "modified": null, "refund_item_identifier": null, "refund_identifier": null, "sku": null, "title": "REFUND_DISCREPANCY", "qty": null, "unit_amount_incl": 1799, "subtotal_incl": 1799, "subtotal_tax": 0}, {"client_id": null, "code": "discrep", "created": null, "meta": [], "modified": null, "refund_item_identifier": null, "refund_identifier": null, "sku": null, "title": "REFUND_DISCREPANCY", "qty": null, "unit_amount_incl": -1799, "subtotal_incl": -1799, "subtotal_tax": 0}], "meta": [], "modified": null, "notes": null, "order_id": null, "origin_connector_kind": null, "origin_connector_id": null, "refund_identifier": null, "source_id": null, "source_refund_code": null, "source_refund_state": null, "source_created_date": null, "transactions": [{"amount": 1799, "amount_calculation_method": null, "auth_code": null, "card_bin": null, "card_last_4_digits": null, "channel_transaction_code": "7975097761881", "completed_date": "2025-05-15 10:13:50", "created": null, "gateway": "peach", "is_store_credit": false, "kind": "credit", "modified": null, "origin_connector_id": null, "origin_connector_kind": null, "order_id": null, "payment_code": "z0ak9pScIsBrkswMTMBz1AdyG", "payment_method": null, "refund_identifier": null, "rrn": null, "stan": null, "state": "error", "status": "FAILURE", "transaction_identifier": null}]}], "ordered_date": "2025-04-06 17:13:43", "total_discount": 0, "total_discount_incl": 0}