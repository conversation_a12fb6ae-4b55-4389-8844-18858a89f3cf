{"rmaSummary": {"rmaId": "6997640", "status": "Done", "order_name": "LSSA-47915", "events": [{"eventName": "RMA_CREATED", "eventDate": "2025-05-02T16:16:04+00:00", "triggeredVia": "Return Portal", "storeLocationId": "online", "userDetails": {"userFirstName": null, "userLastName": null, "userEmailAddress": null, "userRole": null}}, {"eventName": "RMA_APPROVED", "eventDate": "2025-05-05T07:07:55+00:00", "triggeredVia": "Dashboard", "storeLocationId": "online", "userDetails": {"userFirstName": null, "userLastName": null, "userEmailAddress": null, "userRole": null}}]}, "orderDetails": {"orderId": "*************", "orderName": null}, "shipments": [{"shipmentId": "6776005", "status": "Received", "carrierName": "CourierGuy", "trackingNumber": "OPT-*********", "trackingURL": "https://optimise.parcelninja.com/shipment/track/OPT-*********", "labelURL": "https://sellerportal.dpworld.com/api/file-download?link=null", "customerAddress": {"name": "<PERSON>", "street1": "Standard Bank Centre, 1 Kingsmead Way, Stamford Hill, Durban, 4001", "street2": "Floor 2, South West Quadrant, Dolphin Coast", "city": "Durban", "state": "NL", "zip": "4001", "country": "ZA", "phone": "**********", "email": "Marcus<PERSON><EMAIL>"}, "returnAddress": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "street1": "Ashworth Logistics Park, Building C", "street2": "18 Laneshaw Street", "city": "Johannesburg", "state": "Gauteng", "zip": "1609", "country": "ZA", "phone": "", "email": "Marcus<PERSON><EMAIL>"}, "isOutbound": false}], "items": [{"shopLineItemId": "**************", "shopProductId": "*************", "variantId": "**************", "returnedItemId": "********", "fullName": "LEVI'S® MEN'S 512™ SLIM TAPER JEANS - MED INDIGO - WORN IN - 36 / 34 / Med Indigo - Worn In", "productName": "LEVI'S® MEN'S 512™ SLIM TAPER JEANS - MED INDIGO - WORN IN", "variantName": "36 / 34 / Med Indigo - Worn In", "image": "https://cdn.shopify.com/s/files/1/0589/0873/7625/files/1e79aadec4cbe769a33d4fc8eaedfd27_600x600.jpg?v=1744199628", "sku": "2883313113634", "barcode": "5401180188475", "paidPrice": {"amount": 849, "currency": "ZAR", "shopAmount": 849, "shopCurrency": "ZAR"}, "paidTax": {"amount": 110.74, "currency": "ZAR", "shopAmount": 110.74, "shopCurrency": "ZAR"}, "taxIncluded": true, "returnReason": "Does not fit", "resolutionName": "Variant exchange", "policyRuleName": "Variant Exchange | Non Sale Items | First Request", "isAutoApprove": false, "resolutionType": "InstantVariantExchange", "restockingFee": 0, "returnFee": 0, "shipmentFee": 0, "shipmentId": "6776005", "extendedReason": " NEW > 34 / 32 / Med Indigo - Worn In <br />\n > Too large / loose <br />\n > Yes", "returnMethod": {"type": "Ship with any carrier", "name": "SFS CourierGuy"}, "itemValidationStatusResponse": {"returnItemId": null, "isRestockable": false, "isReturned": false, "nonRestockableReason": null, "nonReturnedReason": null, "storeLocationId": null, "lastValidationDate": null, "lastValidatedBy": null, "thirdPartyName": null, "userDetails": {"userFirstName": null, "userLastName": null, "userEmailAddress": null, "userRole": null}}, "exchangeItems": [{"productId": null, "variantId": "42084643962969", "quantity": 1, "sku": "2883313113432", "itemPriceMethod": "CatalogPriceTimeOfPurchase", "freeExchangePriceRange": {"min": 0, "max": 110}}]}, {"shopLineItemId": "14926752284761", "shopProductId": "7883697487961", "variantId": "42050579562585", "returnedItemId": "********", "fullName": "LEVI'S® MEN'S 512™ SLIM TAPER JEANS - LIGHT INDIGO - WORN IN - 36 / 34 / Light Indigo - Worn In", "productName": "LEVI'S® MEN'S 512™ SLIM TAPER JEANS - LIGHT INDIGO - WORN IN", "variantName": "36 / 34 / Light Indigo - Worn In", "image": "https://cdn.shopify.com/s/files/1/0589/0873/7625/files/155310a2043d023f112e553237941ee7_600x600.jpg?v=1738651958", "sku": "A405600613634", "barcode": "5401180356928", "paidPrice": {"amount": 799, "currency": "ZAR", "shopAmount": 799, "shopCurrency": "ZAR"}, "paidTax": {"amount": 104.22, "currency": "ZAR", "shopAmount": 104.22, "shopCurrency": "ZAR"}, "taxIncluded": true, "returnReason": "Does not fit", "resolutionName": "Refund to store credit", "policyRuleName": "Refund to Store credit", "isAutoApprove": false, "resolutionType": "RefundToStoreCredit", "restockingFee": 0, "returnFee": 0, "shipmentFee": 0, "shipmentId": "6776005", "extendedReason": " > Too large / loose ; Other <br />\n > Yes", "returnMethod": {"type": "Ship with any carrier", "name": "SFS CourierGuy"}, "itemValidationStatusResponse": {"returnItemId": null, "isRestockable": false, "isReturned": false, "nonRestockableReason": null, "nonReturnedReason": null, "storeLocationId": null, "lastValidationDate": null, "lastValidatedBy": null, "thirdPartyName": null, "userDetails": {"userFirstName": null, "userLastName": null, "userEmailAddress": null, "userRole": null}}, "exchangeItems": []}], "comments": [{"commentType": "system", "triggeredBy": "<PERSON><PERSON><PERSON><PERSON>", "htmlText": "Completed Shopify return LSSA-47915-R1 for order LSSA-47915", "datetime": "2025-05-09T13:55:15+00:00"}, {"commentType": "system", "triggeredBy": "<PERSON><PERSON><PERSON><PERSON>", "htmlText": "Request DONE", "datetime": "2025-05-09T13:55:15+00:00"}, {"commentType": "system", "triggeredBy": "<PERSON><PERSON><PERSON><PERSON>", "htmlText": "Total refund amount: R1,648.00<br>Transaction IDs: 9176334, 9176335", "datetime": "2025-05-09T13:55:10+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6776005 was updated", "datetime": "2025-05-07T19:59:07+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6776005 was updated", "datetime": "2025-05-07T12:16:20+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6776005 was updated", "datetime": "2025-05-07T10:45:59+00:00"}, {"commentType": "system", "triggeredBy": "Automation", "htmlText": "Shipment <strong>RECEIVED</strong>", "datetime": "2025-05-07T10:45:58+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6776005 was updated", "datetime": "2025-05-07T05:37:58+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6776005 was updated", "datetime": "2025-05-06T15:53:52+00:00"}, {"commentType": "merchant", "triggeredBy": "<PERSON><PERSON><PERSON><PERSON>", "htmlText": "Refund to payment method", "datetime": "2025-05-06T14:59:25+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6776005 was updated", "datetime": "2025-05-06T13:09:07+00:00"}, {"commentType": "system", "triggeredBy": "Automation", "htmlText": "Shipment <strong>IN TRANSIT</strong>", "datetime": "2025-05-06T13:09:06+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6776005 was updated", "datetime": "2025-05-05T16:12:49+00:00"}, {"commentType": "merchant", "triggeredBy": "<PERSON>", "htmlText": "RO-10000010714", "datetime": "2025-05-05T07:10:24+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6776005 was updated", "datetime": "2025-05-05T07:08:54+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6776005 was updated", "datetime": "2025-05-05T07:08:54+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6776005 was updated", "datetime": "2025-05-05T07:08:08+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6776005 was updated", "datetime": "2025-05-05T07:08:07+00:00"}, {"commentType": "system", "triggeredBy": "<PERSON>", "htmlText": "Request <strong>APPROVED</strong>", "datetime": "2025-05-05T07:08:00+00:00"}, {"commentType": "system", "triggeredBy": "<PERSON>", "htmlText": "Created & approved Shopify return LSSA-47915-R1 for order LSSA-47915", "datetime": "2025-05-05T07:07:59+00:00"}, {"commentType": "system", "triggeredBy": "", "htmlText": "Request SUBMITTED", "datetime": "2025-05-02T16:16:05+00:00"}], "exchangeOrders": [{"draftOrderId": "************", "orderId": "", "orderName": "#D1464", "type": "Exchange"}, {"draftOrderId": "************", "orderId": "", "orderName": "LSSA-TEST-1234", "type": "Exchange"}], "cancellationFee": 0, "transactions": [{"id": "9176334", "type": "RefundToPaymentMethod", "externalReferenceId": "************", "externalReferenceType": "PlatformRefundId", "amount": {"amount": 424.5, "currency": "ZAR", "shopAmount": 424.5, "shopCurrency": "ZAR"}, "status": "Success", "processor": "Shopify", "bankAccountDetails": {"iban": null, "firstName": null, "lastName": null, "branchName": null, "branchAddress": null, "comment": null}, "returnItemIds": ["********"], "createdAt": "2025-05-09T13:55:09+00:00", "lastUpdated": "2025-05-09T16:55:14+00:00"}, {"id": "9176335", "type": "RefundToPaymentMethod", "externalReferenceId": "************", "externalReferenceType": "PlatformRefundId", "amount": {"amount": 1223.5, "currency": "ZAR", "shopAmount": 1223.5, "shopCurrency": "ZAR"}, "status": "Success", "processor": "Shopify", "bankAccountDetails": {"iban": null, "firstName": null, "lastName": null, "branchName": null, "branchAddress": null, "comment": null}, "returnItemIds": ["********"], "createdAt": "2025-05-09T13:55:09+00:00", "lastUpdated": "2025-05-09T16:55:14+00:00"}], "createdAt": "2025-05-02T16:16:04+00:00", "lastUpdated": "2025-05-09T13:55:15+00:00"}