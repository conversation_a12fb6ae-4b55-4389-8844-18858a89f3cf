{"created": "2025-04-11 10:55:23.000000", "channel_order_code": "LSSA-QA-1069", "modified": "2025-04-11 10:55:23.000000", "channel_id": 2, "client_id": 1, "customer_id": null, "notes": "", "status": "processing", "total_discount": 0, "ordered_date": "2025-04-11 10:54:38.000000", "has_taxes_incl": 1, "total_discount_incl": 0, "state": "processing", "is_inventory_adjusted": 1, "customer": {"id": null, "created": "2025-02-25 09:58:21.000000", "channel_customer_code": "8373954969788", "modified": "2025-04-17 11:17:16.000000", "channel_id": 2, "client_id": 1, "last_name": "<PERSON>", "first_name": "Lit", "email": "<EMAIL>", "accepts_marketing": 0, "active": 1, "hash": "b0845a332dc41d7ffa868741df7172cb"}, "history": [], "billing_address": {"id": 11472644, "created": "2025-04-11 10:55:23.000000", "address1": "Lente<PERSON>ur", "address2": "Test Apartment", "city": "Cape Town", "company": "Test Company", "country": "South Africa", "first_name": "Lit", "last_name": "<PERSON>", "phone": "+27 65 111 1111", "province": "Western Cape", "zip": "7786", "country_code": "ZA", "province_code": "WC", "modified": "2025-04-11 10:55:23.000000", "client_id": "1054"}, "shipping_address": {"id": 11472643, "created": "2025-04-11 10:55:23.000000", "address1": "Lente<PERSON>ur", "address2": "Test Apartment", "city": "Cape Town", "company": "Test Company", "country": "South Africa", "first_name": "Lit", "last_name": "<PERSON>", "phone": "+27 65 111 1111", "province": "Western Cape", "zip": "7786", "country_code": "ZA", "province_code": "WC", "modified": "2025-04-11 10:55:23.000000", "client_id": "1054"}, "line_items": [{"id": 29362906, "created": "2025-04-11 10:55:23.000000", "barcode": null, "price": 677.39, "qty": 1, "sku": "A544700183430", "modified": "2025-04-11 10:55:23.000000", "order_id": 6293909, "source_id": null, "variant_id": null, "title": "501  ORIGINAL ZA ON THE BORDERLINE", "grams": null, "code": "item", "total_discount": 0, "client_id": "1054", "price_incl": 779, "total_discount_incl": 0, "product_id": null, "source_variant_code": null, "tax_lines": [{"id": 30897751, "created": "2025-04-11 10:55:23.000000", "price": 101.61, "rate": 15, "title": "VAT", "code": "taxed", "modified": "2025-04-11 10:55:23.000000", "orderitem_id": "29362906", "client_id": "1054"}], "fulfillments": [{"id": 2967429, "created": "2025-04-11 10:55:23.000000", "active": 1, "status": null, "qty": 1, "fulfilled_qty": 0, "modified": "2025-04-14 08:45:23.000000", "fulfillment_id": 926560, "orderitem_id": 29362906, "client_id": "1054"}], "sub_total": 677.39, "sub_total_display": "677.39", "tax": 101.61, "tax_display": "101.61", "tax_per_unit": 101.61, "tax_per_unit_display": "101.61", "total": 779, "total_display": "779.00", "price_display": "677.39", "total_discount_display": "0.00", "totals_incl": {"sub_total": 779, "sub_total_display": "779.00", "tax": 101.61, "tax_display": "101.61", "tax_per_unit": 101.61, "tax_per_unit_display": "101.61", "total": 779, "total_display": "779.00", "price": 779, "price_display": "779.00", "total_discount": 0, "total_discount_display": "0.00"}}, {"id": 29362907, "created": "2025-04-11 10:55:23.000000", "barcode": null, "price": 677.39, "qty": 1, "sku": "A544700183432", "modified": "2025-04-11 10:55:23.000000", "order_id": 6293909, "source_id": null, "variant_id": null, "title": "501  ORIGINAL ZA ON THE BORDERLINE", "grams": null, "code": "item", "total_discount": 0, "client_id": "1054", "price_incl": 779, "total_discount_incl": 0, "product_id": null, "source_variant_code": null, "tax_lines": [{"id": 30897752, "created": "2025-04-11 10:55:23.000000", "price": 101.61, "rate": 15, "title": "VAT", "code": "taxed", "modified": "2025-04-11 10:55:23.000000", "orderitem_id": "29362907", "client_id": "1054"}], "fulfillments": [{"id": 2967430, "created": "2025-04-11 10:55:23.000000", "active": 1, "status": null, "qty": 1, "fulfilled_qty": 0, "modified": "2025-04-14 08:45:23.000000", "fulfillment_id": 926560, "orderitem_id": 29362907, "client_id": "1054"}], "sub_total": 677.39, "sub_total_display": "677.39", "tax": 101.61, "tax_display": "101.61", "tax_per_unit": 101.61, "tax_per_unit_display": "101.61", "total": 779, "total_display": "779.00", "price_display": "677.39", "total_discount_display": "0.00", "totals_incl": {"sub_total": 779, "sub_total_display": "779.00", "tax": 101.61, "tax_display": "101.61", "tax_per_unit": 101.61, "tax_per_unit_display": "101.61", "total": 779, "total_display": "779.00", "price": 779, "price_display": "779.00", "total_discount": 0, "total_discount_display": "0.00"}}, {"id": 29362908, "created": "2025-04-11 10:55:23.000000", "barcode": null, "price": 677.39, "qty": 1, "sku": "A544700183434", "modified": "2025-04-11 10:55:23.000000", "order_id": 6293909, "source_id": null, "variant_id": null, "title": "501  ORIGINAL ZA ON THE BORDERLINE", "grams": null, "code": "item", "total_discount": 0, "client_id": "1054", "price_incl": 779, "total_discount_incl": 0, "product_id": null, "source_variant_code": null, "tax_lines": [{"id": 30897753, "created": "2025-04-11 10:55:23.000000", "price": 101.61, "rate": 15, "title": "VAT", "code": "taxed", "modified": "2025-04-11 10:55:23.000000", "orderitem_id": "29362908", "client_id": "1054"}], "fulfillments": [{"id": 2967431, "created": "2025-04-11 10:55:23.000000", "active": 1, "status": null, "qty": 1, "fulfilled_qty": 0, "modified": "2025-04-14 08:45:23.000000", "fulfillment_id": 926560, "orderitem_id": 29362908, "client_id": "1054"}], "sub_total": 677.39, "sub_total_display": "677.39", "tax": 101.61, "tax_display": "101.61", "tax_per_unit": 101.61, "tax_per_unit_display": "101.61", "total": 779, "total_display": "779.00", "price_display": "677.39", "total_discount_display": "0.00", "totals_incl": {"sub_total": 779, "sub_total_display": "779.00", "tax": 101.61, "tax_display": "101.61", "tax_per_unit": 101.61, "tax_per_unit_display": "101.61", "total": 779, "total_display": "779.00", "price": 779, "price_display": "779.00", "total_discount": 0, "total_discount_display": "0.00"}}], "shipping_lines": [], "refunds": [{"amount_incl": 0, "channel_id": 2, "channel_refund_code": "LSSA-QA-1069-R1", "channel_refund_state": "complete", "channel_created_date": "2025-04-11 10:56:12.000000", "client_id": 1, "created": "2025-04-11 11:02:24.996479", "fulfillment_created_date": null, "fulfillment_service_id": null, "fulfillment_refund_code": null, "fulfillment_refund_state": null, "line_items": [{"client_id": 1, "code": "discrep", "created": "2025-04-11 11:02:24.997101", "meta": [{"key": "returngo_discrepancy_amount_InstantVariantExchange", "refund_item_identifier": "6TA0NPVNC5L2IWOLWAK7QYLGXWOG0ZKH", "value": "-2337"}], "modified": "2025-04-16 13:29:12.307133", "refund_item_identifier": "6TA0NPVNC5L2IWOLWAK7QYLGXWOG0ZKH", "refund_identifier": "XKWR4MQ8BAHGNKSY71KOWK5NF0FZ1WLD", "sku": null, "title": "Refund discrepancy", "qty": null, "unit_amount_incl": -2337, "subtotal_incl": -2337, "subtotal_tax": 0}, {"client_id": 1, "code": "item", "created": "2025-04-11 11:02:24.997089", "meta": [{"key": "returngo_qty_InstantVariantExchange", "refund_item_identifier": "9217OQPI5D0HE9O08BDSD0634M43OQW7", "value": "1"}], "modified": "2025-04-16 13:29:12.307141", "refund_item_identifier": "9217OQPI5D0HE9O08BDSD0634M43OQW7", "refund_identifier": "XKWR4MQ8BAHGNKSY71KOWK5NF0FZ1WLD", "sku": "A544700183432", "title": "501  ORIGINAL ZA ON THE BORDERLINE", "qty": 1, "unit_amount_incl": 779, "subtotal_incl": 779, "subtotal_tax": 101.61}, {"client_id": 1, "code": "item", "created": "2025-04-11 11:02:24.997095", "meta": [{"key": "returngo_qty_InstantVariantExchange", "refund_item_identifier": "AYKA2RZ15GICXR2C9KL435Q4RVHIEXLN", "value": "1"}], "modified": "2025-04-16 13:29:12.307147", "refund_item_identifier": "AYKA2RZ15GICXR2C9KL435Q4RVHIEXLN", "refund_identifier": "XKWR4MQ8BAHGNKSY71KOWK5NF0FZ1WLD", "sku": "A544700183434", "title": "501  ORIGINAL ZA ON THE BORDERLINE", "qty": 1, "unit_amount_incl": 779, "subtotal_incl": 779, "subtotal_tax": 101.61}, {"client_id": 1, "code": "item", "created": "2025-04-11 11:02:24.997078", "meta": [{"key": "returngo_qty_InstantVariantExchange", "refund_item_identifier": "YB3IU7HKSO1S311SRDZ02TPNQ68FK6K4", "value": "1"}], "modified": "2025-04-16 13:29:12.307153", "refund_item_identifier": "YB3IU7HKSO1S311SRDZ02TPNQ68FK6K4", "refund_identifier": "XKWR4MQ8BAHGNKSY71KOWK5NF0FZ1WLD", "sku": "A544700183430", "title": "501  ORIGINAL ZA ON THE BORDERLINE", "qty": 1, "unit_amount_incl": 779, "subtotal_incl": 779, "subtotal_tax": 101.61}], "meta": [{"key": "returngo_exchange_order_names", "refund_identifier": "XKWR4MQ8BAHGNKSY71KOWK5NF0FZ1WLD", "value": "LSSA-QA-1070"}, {"key": "returngo_rma_id", "refund_identifier": "XKWR4MQ8BAHGNKSY71KOWK5NF0FZ1WLD", "value": "6790076"}], "modified": "2025-04-16 13:29:12.306516", "notes": null, "order_id": 6293909, "origin_connector_kind": "channel", "origin_connector_id": 2201, "refund_identifier": "XKWR4MQ8BAHGNKSY71KOWK5NF0FZ1WLD", "source_id": 1641, "source_refund_code": null, "source_refund_state": "error", "source_created_date": null, "transactions": []}, {"amount_incl": 2337, "channel_id": 2, "channel_refund_code": "909061914812", "channel_refund_state": "complete", "channel_created_date": "2025-04-16 13:24:49.000000", "client_id": 1, "created": "2025-04-16 13:28:06.887735", "fulfillment_created_date": null, "fulfillment_service_id": null, "fulfillment_refund_code": null, "fulfillment_refund_state": null, "line_items": [{"client_id": 1, "code": "discrep", "created": "2025-04-16 13:28:06.888304", "meta": [], "modified": "2025-04-16 13:29:12.307159", "refund_item_identifier": "IUK00T1SB24TI8HVE68EHH21IZ0CQDA4", "refund_identifier": "0D7NLHNPZU39LCOSHFQ4M222D53ZAQX9", "sku": null, "title": "REFUND_DISCREPANCY", "qty": null, "unit_amount_incl": 2337, "subtotal_incl": 2337, "subtotal_tax": 0}], "meta": [], "modified": "2025-04-16 13:29:12.306525", "notes": null, "order_id": 6293909, "origin_connector_kind": "channel", "origin_connector_id": 2201, "refund_identifier": "0D7NLHNPZU39LCOSHFQ4M222D53ZAQX9", "source_id": 1641, "source_refund_code": null, "source_refund_state": "error", "source_created_date": null, "transactions": [{"amount": 2337, "amount_calculation_method": null, "auth_code": null, "card_bin": null, "card_last_4_digits": null, "channel_transaction_code": "7150263468220", "completed_date": "2025-04-16 13:24:49.000000", "created": "2025-04-16 13:28:06.889924", "gateway": "manual", "is_store_credit": false, "kind": "credit", "modified": "2025-04-16 13:29:12.308715", "origin_connector_id": 2201, "origin_connector_kind": "channel", "order_id": 6293909, "payment_code": "LSSA-QA-1069.2", "payment_method": null, "refund_identifier": "0D7NLHNPZU39LCOSHFQ4M222D53ZAQX9", "rrn": null, "stan": null, "state": "complete", "status": "SUCCESS", "transaction_identifier": "RJJYALN7LQ7E60NN4YE3AIQYJ1RNU55C"}]}], "transactions": [{"amount": 2337, "amount_calculation_method": null, "auth_code": null, "card_bin": null, "card_last_4_digits": null, "channel_transaction_code": "7142082871484", "completed_date": "2025-04-11 10:54:38.000000", "created": "2025-04-11 10:55:22.739910", "gateway": "manual", "is_store_credit": false, "kind": "debit", "modified": "2025-04-11 10:55:22.739919", "origin_connector_id": 2201, "origin_connector_kind": "channel", "order_id": 6293909, "payment_code": "rFoiKo8BIYPjkxmHaqLynpL7I", "payment_method": null, "refund_identifier": null, "rrn": null, "stan": null, "state": "complete", "status": "SUCCESS", "transaction_identifier": "9804UBGIGB067O9WYY8EHW27OSOOWN22"}], "fulfillments": [{"id": 926560, "created": "2025-04-11 10:55:23.000000", "active": 1, "status": null, "tracking_number": null, "tracking_company": null, "tracking_url": null, "notes": null, "modified": "2025-04-14 08:45:23.000000", "order_id": 6293909, "fulfillmentservice_id": 188, "fulfillmentservice_order_code": null, "client_id": "1054", "state": "error", "channel_synced": null, "channel_fulfillment_code": null, "channel_fulfillment_state": null}], "sub_total": 2032.17, "sub_total_display": "2 032.17", "shipping_total": 0, "shipping_total_display": "0.00", "shipping_tax": 0, "shipping_tax_display": "0.00", "tax": 304.83, "tax_display": "304.83", "total_discount_display": "0.00", "total": 2337, "total_display": "2 337.00", "totals_incl": {"sub_total": 2337, "sub_total_display": "2 337.00", "shipping_total": 0, "shipping_total_display": "0.00", "shipping_tax": 0, "shipping_tax_display": "0.00", "tax": 304.83, "tax_display": "304.83", "total_discount": 0, "total_discount_display": "0.00", "total": 2337, "total_display": "2 337.00"}}