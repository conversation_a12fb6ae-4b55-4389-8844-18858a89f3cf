{"billingAddress": {"address1": "Address", "address2": null, "city": "Cape Town", "company": null, "country": "South Africa", "countryCodeV2": "ZA", "firstName": "Test", "lastName": "User", "name": null, "phone": null, "province": "Western Cape", "provinceCode": "WC", "zip": "7777"}, "createdAt": "2024-11-06T15:19:52Z", "customAttributes": [], "customer": {"displayName": null, "email": "<EMAIL>", "firstName": "Test", "id": "gid://shopify/Customer/7136610254986", "lastName": "User", "phone": "+27123456789"}, "customerAcceptsMarketing": false, "discountCodes": [], "fullyPaid": true, "id": "gid://shopify/Order/5748525990026", "lineItems": [{"id": "gid://shopify/LineItem/14326239887498", "title": "Product A", "quantity": 10, "originalUnitPriceSet": {"shopMoney": {"amount": 99.95, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 99.95, "currencyCode": "ZAR"}}, "sku": "SKU003", "taxable": true, "taxLines": [{"priceSet": {"shopMoney": {"amount": 130.37, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 130.37, "currencyCode": "ZAR"}}, "rate": 0.15, "title": "VAT"}], "totalDiscountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}], "name": "#1157", "note": "2024-11-06 15:23 UTC\nReturnGO, RMA5218219:\nRequest SUBMITTED\n\n2024-11-06 15:23 UTC\nReturnGO, RMA5218219:\nRequest APPROVED\nBy #272013\n\n2024-11-06 15:23 UTC\nReturnGO, RMA5218219:\nExchange order #1158 released\nBy #272013\n\n2024-11-06 15:24 UTC\nReturnGO, RMA5218219:\nCredited R 199.90\nBy #272013\n\n2024-11-06 15:25 UTC\nReturnGO, RMA5218219:\nREFUNDED R 199.90\nBy #272013\n\n2024-11-06 15:38 UTC\nReturnGO, RMA5218219:\nShipment RECEIVED\nBy #272013\n\n2024-11-06 15:38 UTC\nReturnGO, RMA5218219:\nRequest DONE\nBy #272013", "refunds": [{"createdAt": "2024-11-06T15:25:36Z", "id": "gid://shopify/Refund/873002533002", "note": null, "orderAdjustments": [], "updatedAt": "2024-11-06T15:25:36Z", "refundLineItems": [{"id": "gid://shopify/RefundLineItem/371908051082", "lineItem": {"id": null, "title": "Product A", "quantity": 10, "originalUnitPriceSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "sku": "SKU003", "taxable": null, "taxLines": [], "totalDiscountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}, "priceSet": {"shopMoney": {"amount": 99.95, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 99.95, "currencyCode": "ZAR"}}, "quantity": 1, "restockType": "NO_RESTOCK", "restocked": false, "subtotalSet": {"shopMoney": {"amount": 99.95, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 99.95, "currencyCode": "ZAR"}}, "totalTaxSet": {"shopMoney": {"amount": 13.04, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 13.04, "currencyCode": "ZAR"}}}, {"id": "gid://shopify/RefundLineItem/371908083850", "lineItem": {"id": null, "title": "Product A", "quantity": 10, "originalUnitPriceSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "sku": "SKU003", "taxable": null, "taxLines": [], "totalDiscountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}, "priceSet": {"shopMoney": {"amount": 99.95, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 99.95, "currencyCode": "ZAR"}}, "quantity": 1, "restockType": "NO_RESTOCK", "restocked": false, "subtotalSet": {"shopMoney": {"amount": 99.95, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 99.95, "currencyCode": "ZAR"}}, "totalTaxSet": {"shopMoney": {"amount": 13.04, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 13.04, "currencyCode": "ZAR"}}}], "refundShippingLines": [], "return": {"id": "gid://shopify/Return/**********", "name": "#1157-R1", "status": "CLOSED"}, "transactions": [{"accountNumber": null, "amountSet": {"shopMoney": {"amount": 199.9, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 199.9, "currencyCode": "ZAR"}}, "authorizationCode": null, "createdAt": "2024-11-06T15:25:35Z", "gateway": "bogus", "id": "gid://shopify/OrderTransaction/*************", "kind": "REFUND", "parentTransaction": {"accountNumber": null, "amountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "authorizationCode": null, "createdAt": null, "gateway": null, "id": "gid://shopify/OrderTransaction/*************", "kind": null, "parentTransaction": null, "paymentDetails": {"avsResultCode": null, "bin": null, "company": null, "cvvResultCode": null, "expirationMonth": null, "expirationYear": null, "name": null, "number": null, "paymentMethodName": null}, "paymentId": null, "processedAt": null, "receiptJson": null, "status": null}, "paymentDetails": {"avsResultCode": null, "bin": "1", "company": "<PERSON><PERSON>", "cvvResultCode": null, "expirationMonth": 12, "expirationYear": 2034, "name": "Test User", "number": "•••• •••• •••• 1", "paymentMethodName": "card"}, "paymentId": "#1157.2", "processedAt": "2024-11-06T15:25:35Z", "receiptJson": "{\"paid_amount\":\"199.90\"}", "status": "SUCCESS"}], "totalRefundedSet": {"shopMoney": {"amount": 199.9, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 199.9, "currencyCode": "ZAR"}}}], "returns": [{"id": "gid://shopify/Return/**********", "name": "#1157-R1", "returnLineItems": [{"id": "gid://shopify/ReturnLineItem/7715422346", "fulfillmentLineItem": {"id": null, "lineItem": {"id": null, "title": null, "quantity": null, "originalUnitPriceSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "sku": "SKU003", "taxable": null, "taxLines": [], "totalDiscountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}}}, "quantity": 1, "returnReason": "OTHER", "returnReasonNote": "RMA#5218219 Resolution: Variant exchange. Return reason: Changed my mind. Answers:  NEW > Product A."}, {"id": "gid://shopify/ReturnLineItem/7715455114", "fulfillmentLineItem": {"id": null, "lineItem": {"id": null, "title": null, "quantity": null, "originalUnitPriceSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "sku": "SKU003", "taxable": null, "taxLines": [], "totalDiscountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}}}, "quantity": 1, "returnReason": "OTHER", "returnReasonNote": "RMA#5218219 Resolution: Variant exchange. Return reason: Changed my mind. Answers:  NEW > Product A."}, {"id": "gid://shopify/ReturnLineItem/7715487882", "fulfillmentLineItem": {"id": null, "lineItem": {"id": null, "title": null, "quantity": null, "originalUnitPriceSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "sku": "SKU003", "taxable": null, "taxLines": [], "totalDiscountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}}}, "quantity": 1, "returnReason": "OTHER", "returnReasonNote": "RMA#5218219 Resolution: Refund to store credit. Return reason: Changed my mind. "}, {"id": "gid://shopify/ReturnLineItem/7715520650", "fulfillmentLineItem": {"id": null, "lineItem": {"id": null, "title": null, "quantity": null, "originalUnitPriceSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "sku": "SKU003", "taxable": null, "taxLines": [], "totalDiscountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}}}, "quantity": 1, "returnReason": "OTHER", "returnReasonNote": "RMA#5218219 Resolution: Refund to store credit. Return reason: Changed my mind. "}, {"id": "gid://shopify/ReturnLineItem/7715553418", "fulfillmentLineItem": {"id": null, "lineItem": {"id": null, "title": null, "quantity": null, "originalUnitPriceSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "sku": "SKU003", "taxable": null, "taxLines": [], "totalDiscountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}}}, "quantity": 1, "returnReason": "OTHER", "returnReasonNote": "RMA#5218219 Resolution: Refund to store credit. Return reason: Changed my mind. "}, {"id": "gid://shopify/ReturnLineItem/7715586186", "fulfillmentLineItem": {"id": null, "lineItem": {"id": null, "title": null, "quantity": null, "originalUnitPriceSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "sku": "SKU003", "taxable": null, "taxLines": [], "totalDiscountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}}}, "quantity": 1, "returnReason": "OTHER", "returnReasonNote": "RMA#5218219 Resolution: Refund to store credit. Return reason: Changed my mind. "}], "status": "CLOSED"}], "shippingAddress": {"address1": "Address", "address2": null, "city": "Cape Town", "company": null, "country": "South Africa", "countryCodeV2": "ZA", "firstName": "Test", "lastName": "User", "name": null, "phone": null, "province": "Western Cape", "provinceCode": "WC", "zip": "7777"}, "shippingLines": [{"title": "Standard", "code": "Standard", "originalPriceSet": {"shopMoney": {"amount": 50, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 50, "currencyCode": "ZAR"}}, "taxLines": [], "discountedPriceSet": {"shopMoney": {"amount": 50, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 50, "currencyCode": "ZAR"}}}], "suggestedRefund": {"amountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "refundLineItems": [], "shipping": {"amountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "maximumRefundableSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}}, "suggestedTransactions": []}, "taxesIncluded": true, "taxLines": [{"priceSet": {"shopMoney": {"amount": 130.37, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 130.37, "currencyCode": "ZAR"}}, "rate": 0.15, "title": "VAT"}], "totalDiscountsSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}, "fulfillmentOrders": [{"id": "gid://shopify/FulfillmentOrder/6845726228618", "status": "CLOSED", "supportedActions": [], "fulfillments": [{"id": "gid://shopify/Fulfillment/*************", "status": "SUCCESS", "trackingInfo": []}], "lineItems": [{"id": "gid://shopify/FulfillmentOrderLineItem/**************", "remainingQuantity": 0, "sku": "SKU003", "totalQuantity": 10}]}], "totalRefundedSet": {"shopMoney": {"amount": 199.9, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 199.9, "currencyCode": "ZAR"}}, "totalRefundedShippingSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}, "transactions": [{"accountNumber": null, "amountSet": {"shopMoney": {"amount": 1049.5, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 1049.5, "currencyCode": "ZAR"}}, "authorizationCode": "53433", "createdAt": "2024-11-06T15:19:51Z", "gateway": "bogus", "id": "gid://shopify/OrderTransaction/*************", "kind": "SALE", "parentTransaction": null, "paymentDetails": {"avsResultCode": null, "bin": "1", "company": "<PERSON><PERSON>", "cvvResultCode": null, "expirationMonth": 12, "expirationYear": 2034, "name": "Test User", "number": "•••• •••• •••• 1", "paymentMethodName": "card"}, "paymentId": "rEiL6t8Hzo3dXeWMJZvinjycS", "processedAt": "2024-11-06T15:19:51Z", "receiptJson": "{\"paid_amount\":\"1049.50\"}", "status": "SUCCESS"}, {"accountNumber": null, "amountSet": {"shopMoney": {"amount": 199.9, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 199.9, "currencyCode": "ZAR"}}, "authorizationCode": null, "createdAt": "2024-11-06T15:25:35Z", "gateway": "bogus", "id": "gid://shopify/OrderTransaction/*************", "kind": "REFUND", "parentTransaction": {"accountNumber": null, "amountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "authorizationCode": null, "createdAt": null, "gateway": null, "id": "gid://shopify/OrderTransaction/*************", "kind": null, "parentTransaction": null, "paymentDetails": {"avsResultCode": null, "bin": null, "company": null, "cvvResultCode": null, "expirationMonth": null, "expirationYear": null, "name": null, "number": null, "paymentMethodName": null}, "paymentId": null, "processedAt": null, "receiptJson": null, "status": null}, "paymentDetails": {"avsResultCode": null, "bin": "1", "company": "<PERSON><PERSON>", "cvvResultCode": null, "expirationMonth": 12, "expirationYear": 2034, "name": "Test User", "number": "•••• •••• •••• 1", "paymentMethodName": "card"}, "paymentId": "#1157.2", "processedAt": "2024-11-06T15:25:35Z", "receiptJson": "{\"paid_amount\":\"199.90\"}", "status": "SUCCESS"}]}