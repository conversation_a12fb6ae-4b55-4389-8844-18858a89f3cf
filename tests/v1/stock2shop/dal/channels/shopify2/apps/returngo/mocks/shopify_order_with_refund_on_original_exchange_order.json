{"billingAddress": {"address1": "Lente<PERSON>ur", "address2": "Test Apartment", "city": "Cape Town", "company": "Test Company", "country": "South Africa", "countryCodeV2": "ZA", "firstName": "Lit", "lastName": "<PERSON>", "name": null, "phone": "+27 65 111 1111", "province": "Western Cape", "provinceCode": "WC", "zip": "7786"}, "createdAt": "2025-04-11T11:01:56Z", "sourceName": "3712961", "sourceIdentifier": null, "customAttributes": [{"key": "returngo_rma_id", "value": "6790076"}, {"key": "returngo_uuid", "value": "8a2a804d-8ce4-41e8-b951-934aeb81639d"}], "customer": {"displayName": null, "email": "<EMAIL>", "firstName": "Lit", "id": "gid://shopify/Customer/8373954969788", "lastName": "<PERSON>", "phone": "+27651111111"}, "customerAcceptsMarketing": false, "discountCodes": [], "fullyPaid": true, "id": "gid://shopify/Order/5799992426684", "lineItems": [{"id": "gid://shopify/LineItem/13994879680700", "discountAllocations": [{"allocatedAmountSet": {"shopMoney": {"amount": 779, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 779, "currencyCode": "ZAR"}}, "discountApplication": {"allocationMethod": "EACH", "index": 0, "targetSelection": "EXPLICIT"}}], "title": "501  ORIGINAL ZA ON THE BORDERLINE", "quantity": 1, "originalUnitPriceSet": {"shopMoney": {"amount": 779, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 779, "currencyCode": "ZAR"}}, "sku": "A544700182832", "taxable": true, "taxLines": [{"priceSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}, "rate": 0.15, "title": "VAT"}], "totalDiscountSet": {"shopMoney": {"amount": 779, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 779, "currencyCode": "ZAR"}}}, {"id": "gid://shopify/LineItem/13994879713468", "discountAllocations": [{"allocatedAmountSet": {"shopMoney": {"amount": 779, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 779, "currencyCode": "ZAR"}}, "discountApplication": {"allocationMethod": "EACH", "index": 1, "targetSelection": "EXPLICIT"}}], "title": "501  ORIGINAL ZA ON THE BORDERLINE", "quantity": 1, "originalUnitPriceSet": {"shopMoney": {"amount": 779, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 779, "currencyCode": "ZAR"}}, "sku": "A544700183032", "taxable": true, "taxLines": [{"priceSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}, "rate": 0.15, "title": "VAT"}], "totalDiscountSet": {"shopMoney": {"amount": 779, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 779, "currencyCode": "ZAR"}}}, {"id": "gid://shopify/LineItem/13994879746236", "discountAllocations": [{"allocatedAmountSet": {"shopMoney": {"amount": 779, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 779, "currencyCode": "ZAR"}}, "discountApplication": {"allocationMethod": "EACH", "index": 2, "targetSelection": "EXPLICIT"}}], "title": "501  ORIGINAL ZA ON THE BORDERLINE", "quantity": 1, "originalUnitPriceSet": {"shopMoney": {"amount": 779, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 779, "currencyCode": "ZAR"}}, "sku": "A544700183232", "taxable": true, "taxLines": [{"priceSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}, "rate": 0.15, "title": "VAT"}], "totalDiscountSet": {"shopMoney": {"amount": 779, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 779, "currencyCode": "ZAR"}}}], "name": "LSSA-QA-1070", "note": "Exchanged via ReturnGO RMA6790076 Original order: LSSA-QA-1069\n\n2025-04-16 13:24 UTC\nReturnGO, RMA6841705:\nRequest SUBMITTED, original order LSSA-QA-1069\n\n2025-04-16 13:24 UTC\nReturnGO, RMA6841705:\nRequest APPROVED\nBy Nahum Delport\n\n2025-04-16 13:24 UTC\nReturnGO, RMA6841705:\nShipment RECEIVED\nBy Nahum Delport\n\n2025-04-16 13:24 UTC\nReturnGO, RMA6841705:\nRequest DONE\nBy Nahum Delport", "refunds": [], "returns": [{"id": "gid://shopify/Return/16255844540", "name": "LSSA-QA-1070-R1", "returnLineItems": [{"id": "gid://shopify/ReturnLineItem/25836617916", "quantity": 1, "returnReason": "OTHER", "returnReasonNote": "RMA#6841705 Resolution: Refund to payment method. Return reason: Does not fit. Answers:  > Other."}, {"id": "gid://shopify/ReturnLineItem/25836650684", "quantity": 1, "returnReason": "OTHER", "returnReasonNote": "RMA#6841705 Resolution: Refund to payment method. Return reason: Does not fit. Answers:  > Other."}, {"id": "gid://shopify/ReturnLineItem/25836683452", "quantity": 1, "returnReason": "OTHER", "returnReasonNote": "RMA#6841705 Resolution: Refund to payment method. Return reason: Does not fit. Answers:  > Other."}], "status": "CLOSED"}], "shippingAddress": {"address1": "Lente<PERSON>ur", "address2": "Test Apartment", "city": "Cape Town", "company": "Test Company", "country": "South Africa", "countryCodeV2": "ZA", "firstName": "Lit", "lastName": "<PERSON>", "name": null, "phone": "+27 65 111 1111", "province": "Western Cape", "provinceCode": "WC", "zip": "7786"}, "shippingLines": [], "suggestedRefund": {"amountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "refundLineItems": [], "shipping": {"amountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "maximumRefundableSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}}, "suggestedTransactions": []}, "taxesIncluded": true, "taxLines": [], "totalDiscountsSet": {"shopMoney": {"amount": 2337, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 2337, "currencyCode": "ZAR"}}, "fulfillmentOrders": [{"id": "gid://shopify/FulfillmentOrder/6891096604860", "status": "CLOSED", "supportedActions": [], "fulfillments": [{"id": "gid://shopify/Fulfillment/5318293323964", "status": "SUCCESS", "trackingInfo": []}], "lineItems": [{"id": "gid://shopify/FulfillmentOrderLineItem/14184013168828", "remainingQuantity": 0, "sku": "A544700182832", "totalQuantity": 1}, {"id": "gid://shopify/FulfillmentOrderLineItem/14184013201596", "remainingQuantity": 0, "sku": "A544700183032", "totalQuantity": 1}, {"id": "gid://shopify/FulfillmentOrderLineItem/14184013234364", "remainingQuantity": 0, "sku": "A544700183232", "totalQuantity": 1}]}], "totalRefundedSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}, "totalRefundedShippingSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}, "transactions": [], "discountApplications": [{"allocationMethod": "EACH", "index": 0, "targetSelection": "EXPLICIT", "targetType": "LINE_ITEM", "code": null}, {"allocationMethod": "EACH", "index": 1, "targetSelection": "EXPLICIT", "targetType": "LINE_ITEM", "code": null}, {"allocationMethod": "EACH", "index": 2, "targetSelection": "EXPLICIT", "targetType": "LINE_ITEM", "code": null}]}