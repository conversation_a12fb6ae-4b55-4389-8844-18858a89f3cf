{"id": 6300910, "created": "2025-04-14 08:26:11.000000", "channel_order_code": "LSSA-QA-1071", "modified": "2025-04-14 08:26:11.000000", "channel_id": 2201, "client_id": 1054, "customer_id": 10778840, "notes": "", "status": "processing", "total_discount": 0, "ordered_date": "2025-04-14 08:25:49.000000", "has_taxes_incl": 1, "total_discount_incl": 0, "state": "processing", "is_inventory_adjusted": 1, "customer": {"id": 10778840, "created": "2025-02-25 09:58:21.000000", "channel_customer_code": "8373954969788", "modified": "2025-04-15 11:01:07.000000", "channel_id": 2201, "client_id": 1054, "last_name": "<PERSON>", "first_name": "Lit", "email": "<EMAIL>", "accepts_marketing": 0, "active": 1, "hash": "b0845a332dc41d7ffa868741df7172cb"}, "history": [{"id": 18389202, "created": "2025-04-14 08:26:11.000000", "modified": "2025-04-14 08:26:11.000000", "order_id": 6300910, "instruction": "sync_order", "client_id": "1054", "storage_code": "1054-2201-0/2025-04/14/08/26/11-*********-orderhistory-sync_order-6300910.json"}, {"id": 18389203, "created": "2025-04-14 08:26:11.000000", "modified": "2025-04-14 08:26:11.000000", "order_id": 6300910, "instruction": "add_order", "client_id": "1054", "storage_code": "1054-2201-0/2025-04/14/08/26/11-*********-orderhistory-add_order-6300910.json"}, {"id": 18389204, "created": "2025-04-14 08:26:13.000000", "modified": "2025-04-14 08:26:13.000000", "order_id": 6300910, "instruction": "add_order", "client_id": "1054", "storage_code": "1054-2201-0/2025-04/14/08/26/12-*********-orderhistory-add_order-6300910.json"}, {"id": 18389661, "created": "2025-04-14 08:43:21.000000", "modified": "2025-04-14 08:43:21.000000", "order_id": 6300910, "instruction": "add_order", "client_id": "1054", "storage_code": "1054-2201-0/2025-04/14/08/43/21-*********-orderhistory-add_order-6300910.json"}], "billing_address": {"id": 11483395, "created": "2025-04-14 08:26:11.000000", "address1": "Lente<PERSON>ur", "address2": "Test Apartment", "city": "Cape Town", "company": "Test Company", "country": "South Africa", "first_name": "Lit", "last_name": "<PERSON>", "phone": "+27 65 111 1111", "province": "Western Cape", "zip": "7786", "country_code": "ZA", "province_code": "WC", "modified": "2025-04-14 08:26:11.000000", "client_id": "1054"}, "shipping_address": {"id": 11483394, "created": "2025-04-14 08:26:11.000000", "address1": "Lente<PERSON>ur", "address2": "Test Apartment", "city": "Cape Town", "company": "Test Company", "country": "South Africa", "first_name": "Lit", "last_name": "<PERSON>", "phone": "+27 65 111 1111", "province": "Western Cape", "zip": "7786", "country_code": "ZA", "province_code": "WC", "modified": "2025-04-14 08:26:11.000000", "client_id": "1054"}, "line_items": [{"id": 29400469, "created": "2025-04-14 08:26:11.000000", "barcode": null, "price": 677.39, "qty": 1, "sku": "A544700183630", "modified": "2025-04-14 08:26:11.000000", "order_id": 6300910, "source_id": null, "variant_id": null, "title": "501  ORIGINAL ZA ON THE BORDERLINE", "grams": null, "code": "item", "total_discount": 0, "client_id": "1054", "price_incl": 779, "total_discount_incl": 0, "product_id": null, "source_variant_code": null, "tax_lines": [{"id": 30937052, "created": "2025-04-14 08:26:11.000000", "price": 101.6, "rate": 15, "title": "VAT", "code": "taxed", "modified": "2025-04-14 08:26:11.000000", "orderitem_id": "29400469", "client_id": "1054"}], "fulfillments": [{"id": 2973738, "created": "2025-04-14 08:26:11.000000", "active": 1, "status": null, "qty": 1, "fulfilled_qty": 0, "modified": "2025-04-14 08:43:23.000000", "fulfillment_id": 927977, "orderitem_id": 29400469, "client_id": "1054"}], "sub_total": 677.39, "sub_total_display": "677.39", "tax": 101.61, "tax_display": "101.61", "tax_per_unit": 101.61, "tax_per_unit_display": "101.61", "total": 779, "total_display": "779.00", "price_display": "677.39", "total_discount_display": "0.00", "totals_incl": {"sub_total": 779, "sub_total_display": "779.00", "tax": 101.61, "tax_display": "101.61", "tax_per_unit": 101.61, "tax_per_unit_display": "101.61", "total": 779, "total_display": "779.00", "price": 779, "price_display": "779.00", "total_discount": 0, "total_discount_display": "0.00"}}], "shipping_lines": [{"id": 29400470, "created": "2025-04-14 08:26:11.000000", "barcode": null, "price": 52.17, "qty": 1, "sku": null, "modified": "2025-04-14 08:26:11.000000", "order_id": 6300910, "source_id": null, "variant_id": null, "title": "Standard Shipping", "grams": null, "code": "ship", "total_discount": 0, "client_id": "1054", "price_incl": 60, "total_discount_incl": 0, "product_id": null, "source_variant_code": null, "tax_lines": [{"id": 30937053, "created": "2025-04-14 08:26:11.000000", "price": 7.83, "rate": 15, "title": "VAT", "code": "taxed", "modified": "2025-04-14 08:26:11.000000", "orderitem_id": "29400470", "client_id": "1054"}], "fulfillments": [], "sub_total": 52.17, "sub_total_display": "52.17", "tax": 7.83, "tax_display": "7.83", "tax_per_unit": 7.83, "tax_per_unit_display": "7.83", "total": 60, "total_display": "60.00", "price_display": "52.17", "total_discount_display": "0.00", "totals_incl": {"sub_total": 60, "sub_total_display": "60.00", "tax": 7.83, "tax_display": "7.83", "tax_per_unit": 7.83, "tax_per_unit_display": "7.83", "total": 60, "total_display": "60.00", "price": 60, "price_display": "60.00", "total_discount": 0, "total_discount_display": "0.00"}}], "transactions": [{"amount": 839, "amount_calculation_method": null, "auth_code": null, "card_bin": null, "card_last_4_digits": null, "channel_transaction_code": "7146859757756", "completed_date": "2025-04-14 08:25:48.000000", "created": "2025-04-14 08:26:11.120361", "gateway": "manual", "is_store_credit": false, "kind": "debit", "modified": "2025-04-14 08:26:11.120370", "origin_connector_id": 2201, "origin_connector_kind": "channel", "order_id": 6300910, "payment_code": "rooneRdS3KhTOPfZ1qTGyj47U", "payment_method": null, "refund_identifier": null, "rrn": null, "stan": null, "state": "complete", "status": "SUCCESS", "transaction_identifier": "AQMKL55PFKRXXPJNYUYJ4VKPDXBF0AQ7"}], "fulfillments": [{"id": 927977, "created": "2025-04-14 08:26:11.000000", "active": 1, "status": null, "tracking_number": null, "tracking_company": null, "tracking_url": null, "notes": null, "modified": "2025-04-14 08:43:23.000000", "order_id": 6300910, "fulfillmentservice_id": 188, "fulfillmentservice_order_code": null, "client_id": "1054", "state": "error", "channel_synced": null, "channel_fulfillment_code": null, "channel_fulfillment_state": null}], "sub_total": 677.39, "sub_total_display": "677.39", "shipping_total": 52.17, "shipping_total_display": "52.17", "shipping_tax": 7.83, "shipping_tax_display": "7.83", "tax": 109.43, "tax_display": "109.43", "total_discount_display": "0.00", "total": 838.99, "total_display": "838.99", "totals_incl": {"sub_total": 779, "sub_total_display": "779.00", "shipping_total": 60, "shipping_total_display": "60.00", "shipping_tax": 7.83, "shipping_tax_display": "7.83", "tax": 109.43, "tax_display": "109.43", "total_discount": 0, "total_discount_display": "0.00", "total": 839, "total_display": "839.00"}}