{"billingAddress": {"address1": "21 Popham Street Waves Edge", "address2": "14B Cape On Popham", "city": "Cape Town", "company": "Pelican", "country": "South Africa", "countryCodeV2": "ZA", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON>", "name": null, "phone": "+27810340745", "province": "Western Cape", "provinceCode": "WC", "zip": "6820"}, "createdAt": "2025-03-11T09:51:59Z", "sourceName": "web", "sourceIdentifier": null, "customAttributes": [{"key": "analyzely_sse", "value": "{\"analyzelyClientId\":\"*********.1741686082\",\"analyzelySessionId\":\"1741686082\"}"}], "customer": {"displayName": null, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>n", "id": "gid://shopify/Customer/7241061072985", "lastName": "<PERSON>", "phone": "+27810340745"}, "customerAcceptsMarketing": true, "discountCodes": [], "fullyPaid": true, "id": "gid://shopify/Order/6096901865561", "lineItems": [{"id": "gid://shopify/LineItem/14713466880089", "discountAllocations": [], "title": "LEVI’S® WOMEN’S 501® ORIGINAL LIGHTWEIGHT CROPPED JEANS - MED INDIGO - WORN IN", "quantity": 1, "originalUnitPriceSet": {"shopMoney": {"amount": 399, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 399, "currencyCode": "ZAR"}}, "sku": "0013S00013028", "taxable": true, "taxLines": [{"priceSet": {"shopMoney": {"amount": 52.04, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 52.04, "currencyCode": "ZAR"}}, "rate": 0.15, "title": "VAT"}], "totalDiscountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}], "name": "LSSA-45187", "note": "2025-03-13 13:23 UTC\nReturnGO, RMA6487530:\nRequest SUBMITTED\n\n2025-03-13 13:32 UTC\nReturnGO, RMA6487530:\nRequest APPROVED\nBy #267963\n\n2025-03-18 14:08 UTC\nReturnGO, RMA6487530:\nShipment IN TRANSIT (AUTOMATIC)\n\n2025-03-20 10:39 UTC\nReturnGO, RMA6487530:\nShipment RECEIVED (AUTOMATIC)\n\n2025-04-25 11:57 UTC\nReturnGO, RMA6487530:\nRequest CANCELED (reason: )\nBy <PERSON>\n\n2025-05-05 07:58 UTC\nReturnGO, RMA6487530:\nRequest REOPENED\nBy <PERSON>\n\n2025-05-05 08:09 UTC\nReturnGO, RMA6487530:\nTotal refund amount: R399.00\nTransaction ID: 9099628\nBy Adrian Louw\n\n2025-05-05 11:09 UTC\nReturnGO, RMA6487530:\nRequest DONE", "refunds": [{"createdAt": "2025-05-05T08:09:25Z", "id": "gid://shopify/Refund/935186989145", "note": null, "orderAdjustments": [{"amountSet": {"shopMoney": {"amount": 399, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 399, "currencyCode": "ZAR"}}, "id": "gid://shopify/OrderAdjustment/323578724441", "reason": "REFUND_DISCREPANCY", "taxAmountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}, {"amountSet": {"shopMoney": {"amount": -399, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": -399, "currencyCode": "ZAR"}}, "id": "gid://shopify/OrderAdjustment/323578757209", "reason": "PENDING_REFUND_DISCREPANCY", "taxAmountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}], "updatedAt": "2025-05-05T08:09:28Z", "refundLineItems": [{"id": "gid://shopify/RefundLineItem/483103604825", "lineItem": {"id": null, "discountAllocations": [], "title": "LEVI’S® WOMEN’S 501® ORIGINAL LIGHTWEIGHT CROPPED JEANS - MED INDIGO - WORN IN", "quantity": 1, "originalUnitPriceSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "sku": "0013S00013028", "taxable": null, "taxLines": [], "totalDiscountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}, "priceSet": {"shopMoney": {"amount": 399, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 399, "currencyCode": "ZAR"}}, "quantity": 1, "restockType": "NO_RESTOCK", "restocked": false, "subtotalSet": {"shopMoney": {"amount": 399, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 399, "currencyCode": "ZAR"}}, "totalTaxSet": {"shopMoney": {"amount": 52.04, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 52.04, "currencyCode": "ZAR"}}}], "refundShippingLines": [], "return": {"id": "gid://shopify/Return/***********", "name": "LSSA-45187-R2", "status": "CLOSED"}, "transactions": [{"accountNumber": null, "amountSet": {"shopMoney": {"amount": 399, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 399, "currencyCode": "ZAR"}}, "authorizationCode": null, "createdAt": "2025-05-05T08:09:24Z", "gateway": "Peach Payments", "id": "gid://shopify/OrderTransaction/*************", "kind": "REFUND", "parentTransaction": {"accountNumber": null, "amountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "authorizationCode": null, "createdAt": null, "gateway": null, "id": "gid://shopify/OrderTransaction/*************", "kind": null, "parentTransaction": null, "paymentDetails": {"avsResultCode": null, "bin": null, "company": null, "cvvResultCode": null, "expirationMonth": null, "expirationYear": null, "name": null, "number": null, "paymentMethodName": null}, "paymentId": null, "processedAt": null, "receiptJson": null, "status": null}, "paymentDetails": {"avsResultCode": null, "bin": null, "company": null, "cvvResultCode": null, "expirationMonth": null, "expirationYear": null, "name": null, "number": null, "paymentMethodName": null}, "paymentId": "zVl1MfdTICj2I1wZenWRevG07", "processedAt": "2025-05-05T08:09:24Z", "receiptJson": "{\"refund_id\":\"zVl1MfdTICj2I1wZenWRevG07\"}", "status": "SUCCESS"}], "totalRefundedSet": {"shopMoney": {"amount": 399, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 399, "currencyCode": "ZAR"}}}], "returns": [{"id": "gid://shopify/Return/17794564185", "name": "LSSA-45187-R1", "returnLineItems": [{"id": "gid://shopify/ReturnLineItem/33268138073", "quantity": 1, "returnReason": "OTHER", "returnReasonNote": "RMA#6487530 Resolution: Refund to payment method. Return reason: Does not fit. Answers:  > Too large / loose \n > Yes."}], "status": "CANCELED"}, {"id": "gid://shopify/Return/***********", "name": "LSSA-45187-R2", "returnLineItems": [{"id": "gid://shopify/ReturnLineItem/36163911769", "quantity": 1, "returnReason": "OTHER", "returnReasonNote": "RMA#6487530 Resolution: Refund to payment method. Return reason: Does not fit. Answers:  > Too large / loose \n > Yes."}], "status": "CLOSED"}], "shippingAddress": {"address1": "21 Popham Street Waves Edge", "address2": "14B Cape On Popham", "city": "Cape Town", "company": "Pelican", "country": "South Africa", "countryCodeV2": "ZA", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON>", "name": null, "phone": "+27810340745", "province": "Western Cape", "provinceCode": "WC", "zip": "6820"}, "shippingLines": [{"title": "Standard Shipping", "code": "Standard Shipping", "originalPriceSet": {"shopMoney": {"amount": 60, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 60, "currencyCode": "ZAR"}}, "taxLines": [], "discountedPriceSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}], "suggestedRefund": {"amountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "refundLineItems": [], "shipping": {"amountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "maximumRefundableSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}}, "suggestedTransactions": []}, "taxesIncluded": true, "taxLines": [{"priceSet": {"shopMoney": {"amount": 52.04, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 52.04, "currencyCode": "ZAR"}}, "rate": 0.15, "title": "VAT"}], "totalDiscountsSet": {"shopMoney": {"amount": 60, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 60, "currencyCode": "ZAR"}}, "fulfillmentOrders": [{"id": "gid://shopify/FulfillmentOrder/7143368425561", "status": "CLOSED", "supportedActions": [], "fulfillments": [{"id": "gid://shopify/Fulfillment/5538074787929", "status": "SUCCESS", "trackingInfo": [{"company": "CourierGuy", "number": "PNJ63014399", "url": "https://store.parcelninja.com/tracking.aspx?WaybillNo=PNJ63014399"}]}], "lineItems": [{"id": "gid://shopify/FulfillmentOrderLineItem/**************", "remainingQuantity": 0, "sku": "0013S00013028", "totalQuantity": 1}]}], "totalRefundedSet": {"shopMoney": {"amount": 399, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 399, "currencyCode": "ZAR"}}, "totalRefundedShippingSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}, "transactions": [{"accountNumber": null, "amountSet": {"shopMoney": {"amount": 399, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 399, "currencyCode": "ZAR"}}, "authorizationCode": null, "createdAt": "2025-03-11T09:50:18Z", "gateway": "Peach Payments", "id": "gid://shopify/OrderTransaction/*************", "kind": "SALE", "parentTransaction": null, "paymentDetails": {"avsResultCode": null, "bin": null, "company": null, "cvvResultCode": null, "expirationMonth": null, "expirationYear": null, "name": null, "number": null, "paymentMethodName": null}, "paymentId": "rDkOObJ6mIQ2o58yXlxk0bvrz", "processedAt": "2025-03-11T09:50:18Z", "receiptJson": "{\"payment_id\":\"rDkOObJ6mIQ2o58yXlxk0bvrz\"}", "status": "SUCCESS"}, {"accountNumber": null, "amountSet": {"shopMoney": {"amount": 399, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 399, "currencyCode": "ZAR"}}, "authorizationCode": null, "createdAt": "2025-05-05T08:09:24Z", "gateway": "Peach Payments", "id": "gid://shopify/OrderTransaction/*************", "kind": "REFUND", "parentTransaction": {"accountNumber": null, "amountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "authorizationCode": null, "createdAt": null, "gateway": null, "id": "gid://shopify/OrderTransaction/*************", "kind": null, "parentTransaction": null, "paymentDetails": {"avsResultCode": null, "bin": null, "company": null, "cvvResultCode": null, "expirationMonth": null, "expirationYear": null, "name": null, "number": null, "paymentMethodName": null}, "paymentId": null, "processedAt": null, "receiptJson": null, "status": null}, "paymentDetails": {"avsResultCode": null, "bin": null, "company": null, "cvvResultCode": null, "expirationMonth": null, "expirationYear": null, "name": null, "number": null, "paymentMethodName": null}, "paymentId": "zVl1MfdTICj2I1wZenWRevG07", "processedAt": "2025-05-05T08:09:24Z", "receiptJson": "{\"refund_id\":\"zVl1MfdTICj2I1wZenWRevG07\"}", "status": "SUCCESS"}], "discountApplications": [{"allocationMethod": "EACH", "index": 0, "targetSelection": "ALL", "targetType": "SHIPPING_LINE", "code": null}]}