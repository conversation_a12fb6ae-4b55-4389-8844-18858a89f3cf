{"rmaSummary": {"rmaId": "6838865", "status": "Done", "order_name": "LSSA-47218", "events": [{"eventName": "RMA_CREATED", "eventDate": "2025-04-16T06:53:43+00:00", "triggeredVia": "Return Portal", "storeLocationId": "online", "userDetails": {"userFirstName": null, "userLastName": null, "userEmailAddress": null, "userRole": null}}, {"eventName": "RMA_APPROVED", "eventDate": "2025-04-17T07:25:10+00:00", "triggeredVia": "Dashboard", "storeLocationId": "online", "userDetails": {"userFirstName": null, "userLastName": null, "userEmailAddress": null, "userRole": null}}]}, "orderDetails": {"orderId": "6191014051929", "orderName": null}, "shipments": [{"shipmentId": "6621163", "status": "Received", "carrierName": "CourierGuy", "trackingNumber": "OPT-110620926", "trackingURL": "https://optimise.parcelninja.com/shipment/track/OPT-110620926", "labelURL": "https://sellerportal.dpworld.com/api/file-download?link=null", "customerAddress": {"name": "<PERSON><PERSON><PERSON>", "street1": "Burgersfort Moroke 259", "street2": "Temple of worship", "city": "Burgersfort", "state": "LP", "zip": "1154", "country": "ZA", "phone": "0826897231", "email": "<EMAIL>"}, "returnAddress": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "street1": "Ashworth Logistics Park, Building C", "street2": "18 Laneshaw Street", "city": "Johannesburg", "state": "Gauteng", "zip": "1609", "country": "ZA", "phone": "", "email": "<EMAIL>"}, "isOutbound": false}], "items": [{"shopLineItemId": "14880935936089", "shopProductId": "7635283574873", "variantId": "41302223978585", "returnedItemId": "********", "fullName": "LEVI'S® WOMEN'S 720 HIGH-RISE SUPER SKINNY JEANS - DARK INDIGO - FLAT FINISH - 34 / 32 / Dark Indigo - Flat Finish", "productName": "LEVI'S® WOMEN'S 720 HIGH-RISE SUPER SKINNY JEANS - DARK INDIGO - FLAT FINISH", "variantName": "34 / 32 / Dark Indigo - Flat Finish", "image": "https://cdn.shopify.com/s/files/1/0589/0873/7625/files/720-high-rise-super-skinny-jeans-dark-indigo-flat-finish-1_600x600.jpg?v=1714397031", "sku": "A693800183432", "barcode": "5401132875613", "paidPrice": {"amount": 699, "currency": "ZAR", "shopAmount": 699, "shopCurrency": "ZAR"}, "paidTax": {"amount": 91.17, "currency": "ZAR", "shopAmount": 91.17, "shopCurrency": "ZAR"}, "taxIncluded": true, "returnReason": "Does not fit", "resolutionName": "Variant exchange", "policyRuleName": "Variant Exchange | Non Sale Items | First Request", "isAutoApprove": false, "resolutionType": "InstantVariantExchange", "restockingFee": 0, "returnFee": 0, "shipmentFee": 0, "shipmentId": "6621163", "extendedReason": " NEW > 32 / 32 / Dark Indigo - Flat Finish <br />\n > Too large / loose <br />\n > Yes", "returnMethod": {"type": "Ship with any carrier", "name": "SFS CourierGuy"}, "itemValidationStatusResponse": {"returnItemId": null, "isRestockable": false, "isReturned": false, "nonRestockableReason": null, "nonReturnedReason": null, "storeLocationId": null, "lastValidationDate": null, "lastValidatedBy": null, "thirdPartyName": null, "userDetails": {"userFirstName": null, "userLastName": null, "userEmailAddress": null, "userRole": null}}, "exchangeItems": [{"productId": null, "variantId": "41330813894745", "quantity": 1, "sku": "A693800183232", "itemPriceMethod": "CatalogPrice", "freeExchangePriceRange": {"min": 0, "max": 110}}]}], "comments": [{"commentType": "system", "triggeredBy": "<PERSON><PERSON><PERSON><PERSON>", "htmlText": "Completed Shopify return LSSA-47218-R1 for order LSSA-47218", "datetime": "2025-05-09T15:00:33+00:00"}, {"commentType": "system", "triggeredBy": "<PERSON><PERSON><PERSON><PERSON>", "htmlText": "Request DONE", "datetime": "2025-05-09T15:00:32+00:00"}, {"commentType": "system", "triggeredBy": "<PERSON><PERSON><PERSON><PERSON>", "htmlText": "Credited R699.00<br>Transaction ID: 9177500", "datetime": "2025-05-09T15:00:22+00:00"}, {"commentType": "merchant", "triggeredBy": "<PERSON><PERSON><PERSON><PERSON>", "htmlText": "No stock, waiting on customer", "datetime": "2025-05-02T10:29:26+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6621163 was updated", "datetime": "2025-04-30T19:32:55+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6621163 was updated", "datetime": "2025-04-30T11:20:08+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6621163 was updated", "datetime": "2025-04-30T11:04:51+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6621163 was updated", "datetime": "2025-04-30T10:46:10+00:00"}, {"commentType": "system", "triggeredBy": "Automation", "htmlText": "Shipment <strong>RECEIVED</strong>", "datetime": "2025-04-30T10:46:09+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6621163 was updated", "datetime": "2025-04-30T05:20:49+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6621163 was updated", "datetime": "2025-04-25T15:20:57+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6621163 was updated", "datetime": "2025-04-25T12:25:53+00:00"}, {"commentType": "system", "triggeredBy": "Automation", "htmlText": "Shipment <strong>IN TRANSIT</strong>", "datetime": "2025-04-25T12:25:52+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6621163 was updated", "datetime": "2025-04-24T15:41:53+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6621163 was updated", "datetime": "2025-04-24T15:40:56+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6621163 was updated", "datetime": "2025-04-24T15:40:55+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6621163 was updated", "datetime": "2025-04-24T15:40:15+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6621163 was updated", "datetime": "2025-04-24T15:40:14+00:00"}, {"commentType": "system", "triggeredBy": "#267963", "htmlText": "Request <strong>APPROVED</strong>", "datetime": "2025-04-17T07:25:14+00:00"}, {"commentType": "system", "triggeredBy": "#267963", "htmlText": "Created & approved Shopify return LSSA-47218-R1 for order LSSA-47218", "datetime": "2025-04-17T07:25:13+00:00"}, {"commentType": "system", "triggeredBy": "", "htmlText": "Request SUBMITTED", "datetime": "2025-04-16T06:53:45+00:00"}, {"commentType": "error", "triggeredBy": "", "htmlText": "Failed to upload media file d46af3-836f-efa4-ad73-05b80c6276b2.jpeg from follow-up question. Product case ID: ********, follow-up question ID: 176495", "datetime": "2025-04-16T06:53:45+00:00"}], "exchangeOrders": [{"draftOrderId": "************", "orderId": "", "orderName": "#D1416", "type": "Exchange"}], "cancellationFee": 0, "transactions": [{"id": "9177500", "type": "RefundToStoreCredit", "externalReferenceId": "OETRU6HT", "externalReferenceType": "StoreCreditCode", "amount": {"amount": 699, "currency": "ZAR", "shopAmount": 699, "shopCurrency": "ZAR"}, "status": "Success", "processor": "Shopify", "bankAccountDetails": {"iban": null, "firstName": null, "lastName": null, "branchName": null, "branchAddress": null, "comment": null}, "returnItemIds": ["********"], "createdAt": "2025-05-09T15:00:21+00:00", "lastUpdated": "2025-05-09T15:56:42+00:00"}], "createdAt": "2025-04-16T06:53:43+00:00", "lastUpdated": "2025-05-09T15:00:32+00:00"}