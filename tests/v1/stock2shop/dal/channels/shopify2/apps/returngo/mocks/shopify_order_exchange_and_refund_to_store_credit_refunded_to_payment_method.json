{"billingAddress": {"address1": "Standard Bank Centre, 1 Kingsmead Way, Stamford Hill, Durban, 4001", "address2": "Floor 2, South West Quadrant, Dolphin Coast", "city": "Durban", "company": "Standard Bank", "country": "South Africa", "countryCodeV2": "ZA", "firstName": "<PERSON>", "lastName": "<PERSON>", "name": null, "phone": "**********", "province": "KwaZulu-Natal", "provinceCode": "NL", "zip": "4001"}, "createdAt": "2025-04-26T19:30:02Z", "sourceName": "web", "sourceIdentifier": null, "customAttributes": [{"key": "analyzely_sse", "value": "{\"analyzelyClientId\":\"*********.**********\",\"analyzelySessionId\":\"**********\"}"}], "customer": {"displayName": null, "email": "Marcus<PERSON><EMAIL>", "firstName": "<PERSON>", "id": "gid://shopify/Customer/*************", "lastName": "<PERSON>", "phone": null}, "customerAcceptsMarketing": false, "discountCodes": [], "fullyPaid": true, "id": "gid://shopify/Order/*************", "lineItems": [{"id": "gid://shopify/LineItem/**************", "discountAllocations": [], "title": "LEVI'S® MEN'S 510™ SKINNY JEANS - DARK INDIGO - WORN IN", "quantity": 1, "originalUnitPriceSet": {"shopMoney": {"amount": 799, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 799, "currencyCode": "ZAR"}}, "sku": "A405500483434", "taxable": true, "taxLines": [{"priceSet": {"shopMoney": {"amount": 104.21, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 104.21, "currencyCode": "ZAR"}}, "rate": 0.15, "title": "VAT"}], "totalDiscountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}, {"id": "gid://shopify/LineItem/14926752251993", "discountAllocations": [], "title": "LEVI'S® MEN'S 512™ SLIM TAPER JEANS - MED INDIGO - WORN IN", "quantity": 1, "originalUnitPriceSet": {"shopMoney": {"amount": 849, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 849, "currencyCode": "ZAR"}}, "sku": "*************", "taxable": true, "taxLines": [{"priceSet": {"shopMoney": {"amount": 110.74, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 110.74, "currencyCode": "ZAR"}}, "rate": 0.15, "title": "VAT"}], "totalDiscountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}, {"id": "gid://shopify/LineItem/14926752284761", "discountAllocations": [], "title": "LEVI'S® MEN'S 512™ SLIM TAPER JEANS - LIGHT INDIGO - WORN IN", "quantity": 1, "originalUnitPriceSet": {"shopMoney": {"amount": 799, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 799, "currencyCode": "ZAR"}}, "sku": "A405600613634", "taxable": true, "taxLines": [{"priceSet": {"shopMoney": {"amount": 104.22, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 104.22, "currencyCode": "ZAR"}}, "rate": 0.15, "title": "VAT"}], "totalDiscountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}], "name": "LSSA-47915", "note": "2025-05-02 16:16 UTC\nReturnGO, RMA6997640:\nRequest SUBMITTED\n\n2025-05-05 07:07 UTC\nReturnGO, RMA6997640:\nRequest APPROVED\nBy <PERSON>\n\n2025-05-06 13:09 UTC\nReturnGO, RMA6997640:\nShipment IN TRANSIT (AUTOMATIC)\n\n2025-05-07 10:45 UTC\nReturnGO, RMA6997640:\nShipment RECEIVED (AUTOMATIC)\n\n2025-05-09 13:55 UTC\nReturnGO, RMA6997640:\nTotal refund amount: R1,648.00\nTransaction IDs: 9176334, 9176335\nBy <PERSON><PERSON><PERSON><PERSON>\n\n2025-05-09 13:55 UTC\nReturnGO, RMA6997640:\nRequest DONE\nBy <PERSON><PERSON><PERSON><PERSON>", "refunds": [{"createdAt": "2025-05-09T13:55:09Z", "id": "gid://shopify/Refund/935564476505", "note": null, "orderAdjustments": [{"amountSet": {"shopMoney": {"amount": 1648, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 1648, "currencyCode": "ZAR"}}, "id": "gid://shopify/OrderAdjustment/323900276825", "reason": "REFUND_DISCREPANCY", "taxAmountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}, {"amountSet": {"shopMoney": {"amount": -1648, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": -1648, "currencyCode": "ZAR"}}, "id": "gid://shopify/OrderAdjustment/323900309593", "reason": "PENDING_REFUND_DISCREPANCY", "taxAmountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}], "updatedAt": "2025-05-09T13:55:16Z", "refundLineItems": [{"id": "gid://shopify/RefundLineItem/483664003161", "lineItem": {"id": null, "discountAllocations": [], "title": "LEVI'S® MEN'S 512™ SLIM TAPER JEANS - MED INDIGO - WORN IN", "quantity": 1, "originalUnitPriceSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "sku": "*************", "taxable": null, "taxLines": [], "totalDiscountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}, "priceSet": {"shopMoney": {"amount": 849, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 849, "currencyCode": "ZAR"}}, "quantity": 1, "restockType": "NO_RESTOCK", "restocked": false, "subtotalSet": {"shopMoney": {"amount": 849, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 849, "currencyCode": "ZAR"}}, "totalTaxSet": {"shopMoney": {"amount": 110.74, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 110.74, "currencyCode": "ZAR"}}}, {"id": "gid://shopify/RefundLineItem/483664035929", "lineItem": {"id": null, "discountAllocations": [], "title": "LEVI'S® MEN'S 512™ SLIM TAPER JEANS - LIGHT INDIGO - WORN IN", "quantity": 1, "originalUnitPriceSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "sku": "A405600613634", "taxable": null, "taxLines": [], "totalDiscountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}, "priceSet": {"shopMoney": {"amount": 799, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 799, "currencyCode": "ZAR"}}, "quantity": 1, "restockType": "NO_RESTOCK", "restocked": false, "subtotalSet": {"shopMoney": {"amount": 799, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 799, "currencyCode": "ZAR"}}, "totalTaxSet": {"shopMoney": {"amount": 104.22, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 104.22, "currencyCode": "ZAR"}}}], "refundShippingLines": [], "return": {"id": "gid://shopify/Return/***********", "name": "LSSA-47915-R1", "status": "CLOSED"}, "transactions": [{"accountNumber": null, "amountSet": {"shopMoney": {"amount": 1648, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 1648, "currencyCode": "ZAR"}}, "authorizationCode": null, "createdAt": "2025-05-09T13:55:08Z", "gateway": "Peach Payments", "id": "gid://shopify/OrderTransaction/*************", "kind": "REFUND", "parentTransaction": {"accountNumber": null, "amountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "authorizationCode": null, "createdAt": null, "gateway": null, "id": "gid://shopify/OrderTransaction/*************", "kind": null, "parentTransaction": null, "paymentDetails": {"avsResultCode": null, "bin": null, "company": null, "cvvResultCode": null, "expirationMonth": null, "expirationYear": null, "name": null, "number": null, "paymentMethodName": null}, "paymentId": null, "processedAt": null, "receiptJson": null, "status": null}, "paymentDetails": {"avsResultCode": null, "bin": null, "company": null, "cvvResultCode": null, "expirationMonth": null, "expirationYear": null, "name": null, "number": null, "paymentMethodName": null}, "paymentId": "zPp1ZfzBelcB8IYgLfHtCJ9mC", "processedAt": "2025-05-09T13:55:08Z", "receiptJson": "{\"refund_id\":\"zPp1ZfzBelcB8IYgLfHtCJ9mC\"}", "status": "SUCCESS"}], "totalRefundedSet": {"shopMoney": {"amount": 1648, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 1648, "currencyCode": "ZAR"}}}], "returns": [{"id": "gid://shopify/Return/***********", "name": "LSSA-47915-R1", "returnLineItems": [{"id": "gid://shopify/ReturnLineItem/36163616857", "quantity": 1, "returnReason": "OTHER", "returnReasonNote": "RMA#6997640 Resolution: Variant exchange. Return reason: Does not fit. Answers:  NEW > 34 / 32 / Med Indigo - Worn In \n > Too large / loose \n > Yes."}, {"id": "gid://shopify/ReturnLineItem/***********", "quantity": 1, "returnReason": "OTHER", "returnReasonNote": "RMA#6997640 Resolution: Refund to store credit. Return reason: Does not fit. Answers:  > Too large / loose ; Other \n > Yes."}], "status": "CLOSED"}], "shippingAddress": {"address1": "Standard Bank Centre, 1 Kingsmead Way, Stamford Hill, Durban, 4001", "address2": "Floor 2, South West Quadrant, Dolphin Coast", "city": "Durban", "company": "Standard Bank", "country": "South Africa", "countryCodeV2": "ZA", "firstName": "<PERSON>", "lastName": "<PERSON>", "name": null, "phone": "**********", "province": "KwaZulu-Natal", "provinceCode": "NL", "zip": "4001"}, "shippingLines": [{"title": "Standard Shipping", "code": "Standard Shipping", "originalPriceSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}, "taxLines": [{"priceSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}, "rate": 0.15, "title": "VAT"}], "discountedPriceSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}], "suggestedRefund": {"amountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "refundLineItems": [], "shipping": {"amountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "maximumRefundableSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}}, "suggestedTransactions": []}, "taxesIncluded": true, "taxLines": [{"priceSet": {"shopMoney": {"amount": 319.17, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 319.17, "currencyCode": "ZAR"}}, "rate": 0.15, "title": "VAT"}], "totalDiscountsSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}, "fulfillmentOrders": [{"id": "gid://shopify/FulfillmentOrder/7266068660313", "status": "CLOSED", "supportedActions": [], "fulfillments": [{"id": "gid://shopify/Fulfillment/5658928349273", "status": "SUCCESS", "trackingInfo": [{"company": "CourierGuy", "number": "PNJ63071298", "url": "https://store.parcelninja.com/tracking.aspx?WaybillNo=PNJ63071298"}]}], "lineItems": [{"id": "gid://shopify/FulfillmentOrderLineItem/15141783535705", "remainingQuantity": 0, "sku": "A405500483434", "totalQuantity": 1}, {"id": "gid://shopify/FulfillmentOrderLineItem/15141*********", "remainingQuantity": 0, "sku": "*************", "totalQuantity": 1}, {"id": "gid://shopify/FulfillmentOrderLineItem/**************", "remainingQuantity": 0, "sku": "A405600613634", "totalQuantity": 1}]}], "totalRefundedSet": {"shopMoney": {"amount": 1648, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 1648, "currencyCode": "ZAR"}}, "totalRefundedShippingSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}, "transactions": [{"accountNumber": null, "amountSet": {"shopMoney": {"amount": 2447, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 2447, "currencyCode": "ZAR"}}, "authorizationCode": null, "createdAt": "2025-04-26T19:26:43Z", "gateway": "Peach Payments", "id": "gid://shopify/OrderTransaction/*************", "kind": "SALE", "parentTransaction": null, "paymentDetails": {"avsResultCode": null, "bin": null, "company": null, "cvvResultCode": null, "expirationMonth": null, "expirationYear": null, "name": null, "number": null, "paymentMethodName": null}, "paymentId": "rtsldOIZwVj8av5kzjyMidmYM", "processedAt": "2025-04-26T19:26:43Z", "receiptJson": "{\"payment_id\":\"rtsldOIZwVj8av5kzjyMidmYM\"}", "status": "SUCCESS"}, {"accountNumber": null, "amountSet": {"shopMoney": {"amount": 1648, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 1648, "currencyCode": "ZAR"}}, "authorizationCode": null, "createdAt": "2025-05-09T13:55:08Z", "gateway": "Peach Payments", "id": "gid://shopify/OrderTransaction/*************", "kind": "REFUND", "parentTransaction": {"accountNumber": null, "amountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "authorizationCode": null, "createdAt": null, "gateway": null, "id": "gid://shopify/OrderTransaction/*************", "kind": null, "parentTransaction": null, "paymentDetails": {"avsResultCode": null, "bin": null, "company": null, "cvvResultCode": null, "expirationMonth": null, "expirationYear": null, "name": null, "number": null, "paymentMethodName": null}, "paymentId": null, "processedAt": null, "receiptJson": null, "status": null}, "paymentDetails": {"avsResultCode": null, "bin": null, "company": null, "cvvResultCode": null, "expirationMonth": null, "expirationYear": null, "name": null, "number": null, "paymentMethodName": null}, "paymentId": "zPp1ZfzBelcB8IYgLfHtCJ9mC", "processedAt": "2025-05-09T13:55:08Z", "receiptJson": "{\"refund_id\":\"zPp1ZfzBelcB8IYgLfHtCJ9mC\"}", "status": "SUCCESS"}], "discountApplications": []}