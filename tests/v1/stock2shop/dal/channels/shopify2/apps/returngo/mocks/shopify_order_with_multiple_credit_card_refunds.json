{"billingAddress": {"address1": "Address", "address2": null, "city": "Cape Town", "company": null, "country": "South Africa", "countryCodeV2": "ZA", "firstName": "Test", "lastName": "User", "name": null, "phone": null, "province": "Western Cape", "provinceCode": "WC", "zip": "7777"}, "createdAt": "2024-11-19T14:42:12Z", "customAttributes": [], "customer": {"displayName": null, "email": "<EMAIL>", "firstName": "Test", "id": "gid://shopify/Customer/7136610254986", "lastName": "User", "phone": "+27123456789"}, "customerAcceptsMarketing": false, "discountCodes": [], "fullyPaid": true, "id": "gid://shopify/Order/5762736423050", "lineItems": [{"id": "gid://shopify/LineItem/14355616563338", "title": "Product X", "quantity": 5, "originalUnitPriceSet": {"shopMoney": {"amount": 599, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 599, "currencyCode": "ZAR"}}, "sku": "SKU001", "taxable": true, "taxLines": [{"priceSet": {"shopMoney": {"amount": 384.13, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 384.13, "currencyCode": "ZAR"}}, "rate": 0.15, "title": "VAT"}], "totalDiscountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}, {"id": "gid://shopify/LineItem/14355616596106", "title": "Product Y", "quantity": 5, "originalUnitPriceSet": {"shopMoney": {"amount": 199, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 199, "currencyCode": "ZAR"}}, "sku": "SKU002", "taxable": true, "taxLines": [{"priceSet": {"shopMoney": {"amount": 129.78, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 129.78, "currencyCode": "ZAR"}}, "rate": 0.15, "title": "VAT"}], "totalDiscountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}], "name": "#1206", "note": "2024-11-19 14:44 UTC\nReturnGO, RMA5331615:\nRequest SUBMITTED\n\n2024-11-19 14:45 UTC\nReturnGO, RMA5331615:\nRequest APPROVED\nBy #272013\n\n2024-11-19 14:45 UTC\nReturnGO, RMA5331615:\nTotal refund amount: R 2,355.51\nTransaction IDs: 6458057, 6458058, 6458059, 6458060\nBy #272013\n\n2024-11-19 14:45 UTC\nReturnGO, RMA5331615:\nTotal refund amount: R 795.49\nTransaction IDs: 6458065, 6458066, 6458067, 6458068\nBy #272013\n\n2024-11-19 14:46 UTC\nReturnGO, RMA5331615:\nShipment RECEIVED\nBy #272013\n\n2024-11-19 14:46 UTC\nReturnGO, RMA5331615:\nRequest DONE\nBy #272013", "refunds": [{"createdAt": "2024-11-19T14:45:31Z", "id": "gid://shopify/Refund/873641115786", "note": null, "orderAdjustments": [{"amountSet": {"shopMoney": {"amount": 0.49, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0.49, "currencyCode": "ZAR"}}, "id": "gid://shopify/OrderAdjustment/260705681546", "reason": "REFUND_DISCREPANCY", "taxAmountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}], "updatedAt": "2024-11-19T14:45:31Z", "refundLineItems": [{"id": "gid://shopify/RefundLineItem/372686848138", "lineItem": {"id": null, "title": "Product X", "quantity": 5, "originalUnitPriceSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "sku": "SKU001", "taxable": null, "taxLines": [], "totalDiscountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}, "priceSet": {"shopMoney": {"amount": 599, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 599, "currencyCode": "ZAR"}}, "quantity": 1, "restockType": "NO_RESTOCK", "restocked": false, "subtotalSet": {"shopMoney": {"amount": 589, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 589, "currencyCode": "ZAR"}}, "totalTaxSet": {"shopMoney": {"amount": 76.82, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 76.82, "currencyCode": "ZAR"}}}, {"id": "gid://shopify/RefundLineItem/372686880906", "lineItem": {"id": null, "title": "Product X", "quantity": 5, "originalUnitPriceSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "sku": "SKU001", "taxable": null, "taxLines": [], "totalDiscountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}, "priceSet": {"shopMoney": {"amount": 599, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 599, "currencyCode": "ZAR"}}, "quantity": 1, "restockType": "NO_RESTOCK", "restocked": false, "subtotalSet": {"shopMoney": {"amount": 589, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 589, "currencyCode": "ZAR"}}, "totalTaxSet": {"shopMoney": {"amount": 76.82, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 76.82, "currencyCode": "ZAR"}}}, {"id": "gid://shopify/RefundLineItem/372686913674", "lineItem": {"id": null, "title": "Product X", "quantity": 5, "originalUnitPriceSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "sku": "SKU001", "taxable": null, "taxLines": [], "totalDiscountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}, "priceSet": {"shopMoney": {"amount": 599, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 599, "currencyCode": "ZAR"}}, "quantity": 1, "restockType": "NO_RESTOCK", "restocked": false, "subtotalSet": {"shopMoney": {"amount": 589, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 589, "currencyCode": "ZAR"}}, "totalTaxSet": {"shopMoney": {"amount": 76.83, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 76.83, "currencyCode": "ZAR"}}}, {"id": "gid://shopify/RefundLineItem/372686946442", "lineItem": {"id": null, "title": "Product X", "quantity": 5, "originalUnitPriceSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "sku": "SKU001", "taxable": null, "taxLines": [], "totalDiscountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}, "priceSet": {"shopMoney": {"amount": 599, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 599, "currencyCode": "ZAR"}}, "quantity": 1, "restockType": "NO_RESTOCK", "restocked": false, "subtotalSet": {"shopMoney": {"amount": 589, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 589, "currencyCode": "ZAR"}}, "totalTaxSet": {"shopMoney": {"amount": 76.83, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 76.83, "currencyCode": "ZAR"}}}], "refundShippingLines": [], "return": {"id": "gid://shopify/Return/**********", "name": "#1206-R1", "status": "CLOSED"}, "transactions": [{"accountNumber": null, "amountSet": {"shopMoney": {"amount": 2355.51, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 2355.51, "currencyCode": "ZAR"}}, "authorizationCode": null, "createdAt": "2024-11-19T14:45:31Z", "gateway": "bogus", "id": "gid://shopify/OrderTransaction/*************", "kind": "REFUND", "parentTransaction": {"accountNumber": null, "amountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "authorizationCode": null, "createdAt": null, "gateway": null, "id": "gid://shopify/OrderTransaction/*************", "kind": null, "parentTransaction": null, "paymentDetails": {"avsResultCode": null, "bin": null, "company": null, "cvvResultCode": null, "expirationMonth": null, "expirationYear": null, "name": null, "number": null, "paymentMethodName": null}, "paymentId": null, "processedAt": null, "receiptJson": null, "status": null}, "paymentDetails": {"avsResultCode": null, "bin": "1", "company": "<PERSON><PERSON>", "cvvResultCode": null, "expirationMonth": 12, "expirationYear": 2034, "name": "Test User", "number": "•••• •••• •••• 1", "paymentMethodName": "card"}, "paymentId": "#1206.2", "processedAt": "2024-11-19T14:45:31Z", "receiptJson": "{\"paid_amount\":\"2355.51\"}", "status": "SUCCESS"}], "totalRefundedSet": {"shopMoney": {"amount": 2355.51, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 2355.51, "currencyCode": "ZAR"}}}, {"createdAt": "2024-11-19T14:45:55Z", "id": "gid://shopify/Refund/873641181322", "note": null, "orderAdjustments": [{"amountSet": {"shopMoney": {"amount": 0.51, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0.51, "currencyCode": "ZAR"}}, "id": "gid://shopify/OrderAdjustment/260705747082", "reason": "REFUND_DISCREPANCY", "taxAmountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}], "updatedAt": "2024-11-19T14:45:56Z", "refundLineItems": [{"id": "gid://shopify/RefundLineItem/372686979210", "lineItem": {"id": null, "title": "Product Y", "quantity": 5, "originalUnitPriceSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "sku": "SKU002", "taxable": null, "taxLines": [], "totalDiscountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}, "priceSet": {"shopMoney": {"amount": 199, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 199, "currencyCode": "ZAR"}}, "quantity": 1, "restockType": "NO_RESTOCK", "restocked": false, "subtotalSet": {"shopMoney": {"amount": 199, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 199, "currencyCode": "ZAR"}}, "totalTaxSet": {"shopMoney": {"amount": 25.95, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 25.95, "currencyCode": "ZAR"}}}, {"id": "gid://shopify/RefundLineItem/372687011978", "lineItem": {"id": null, "title": "Product Y", "quantity": 5, "originalUnitPriceSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "sku": "SKU002", "taxable": null, "taxLines": [], "totalDiscountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}, "priceSet": {"shopMoney": {"amount": 199, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 199, "currencyCode": "ZAR"}}, "quantity": 1, "restockType": "NO_RESTOCK", "restocked": false, "subtotalSet": {"shopMoney": {"amount": 199, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 199, "currencyCode": "ZAR"}}, "totalTaxSet": {"shopMoney": {"amount": 25.95, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 25.95, "currencyCode": "ZAR"}}}, {"id": "gid://shopify/RefundLineItem/372687044746", "lineItem": {"id": null, "title": "Product Y", "quantity": 5, "originalUnitPriceSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "sku": "SKU002", "taxable": null, "taxLines": [], "totalDiscountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}, "priceSet": {"shopMoney": {"amount": 199, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 199, "currencyCode": "ZAR"}}, "quantity": 1, "restockType": "NO_RESTOCK", "restocked": false, "subtotalSet": {"shopMoney": {"amount": 199, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 199, "currencyCode": "ZAR"}}, "totalTaxSet": {"shopMoney": {"amount": 25.96, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 25.96, "currencyCode": "ZAR"}}}, {"id": "gid://shopify/RefundLineItem/372687077514", "lineItem": {"id": null, "title": "Product Y", "quantity": 5, "originalUnitPriceSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "sku": "SKU002", "taxable": null, "taxLines": [], "totalDiscountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}, "priceSet": {"shopMoney": {"amount": 199, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 199, "currencyCode": "ZAR"}}, "quantity": 1, "restockType": "NO_RESTOCK", "restocked": false, "subtotalSet": {"shopMoney": {"amount": 199, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 199, "currencyCode": "ZAR"}}, "totalTaxSet": {"shopMoney": {"amount": 25.96, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 25.96, "currencyCode": "ZAR"}}}], "refundShippingLines": [], "return": {"id": "gid://shopify/Return/**********", "name": "#1206-R1", "status": "CLOSED"}, "transactions": [{"accountNumber": null, "amountSet": {"shopMoney": {"amount": 795.49, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 795.49, "currencyCode": "ZAR"}}, "authorizationCode": null, "createdAt": "2024-11-19T14:45:55Z", "gateway": "bogus", "id": "gid://shopify/OrderTransaction/*************", "kind": "REFUND", "parentTransaction": {"accountNumber": null, "amountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "authorizationCode": null, "createdAt": null, "gateway": null, "id": "gid://shopify/OrderTransaction/*************", "kind": null, "parentTransaction": null, "paymentDetails": {"avsResultCode": null, "bin": null, "company": null, "cvvResultCode": null, "expirationMonth": null, "expirationYear": null, "name": null, "number": null, "paymentMethodName": null}, "paymentId": null, "processedAt": null, "receiptJson": null, "status": null}, "paymentDetails": {"avsResultCode": null, "bin": "1", "company": "<PERSON><PERSON>", "cvvResultCode": null, "expirationMonth": 12, "expirationYear": 2034, "name": "Test User", "number": "•••• •••• •••• 1", "paymentMethodName": "card"}, "paymentId": "#1206.3", "processedAt": "2024-11-19T14:45:55Z", "receiptJson": "{\"paid_amount\":\"795.49\"}", "status": "SUCCESS"}], "totalRefundedSet": {"shopMoney": {"amount": 795.49, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 795.49, "currencyCode": "ZAR"}}}], "returns": [{"id": "gid://shopify/Return/**********", "name": "#1206-R1", "returnLineItems": [{"id": "gid://shopify/ReturnLineItem/7844331658", "quantity": 1, "returnReason": "OTHER", "returnReasonNote": "RMA#5331615 Resolution: Refund to payment method. Return reason: Changed my mind. "}, {"id": "gid://shopify/ReturnLineItem/7844364426", "quantity": 1, "returnReason": "OTHER", "returnReasonNote": "RMA#5331615 Resolution: Refund to payment method. Return reason: Changed my mind. "}, {"id": "gid://shopify/ReturnLineItem/7844397194", "quantity": 1, "returnReason": "OTHER", "returnReasonNote": "RMA#5331615 Resolution: Refund to payment method. Return reason: Changed my mind. "}, {"id": "gid://shopify/ReturnLineItem/7844429962", "quantity": 1, "returnReason": "OTHER", "returnReasonNote": "RMA#5331615 Resolution: Refund to payment method. Return reason: Changed my mind. "}, {"id": "gid://shopify/ReturnLineItem/7844462730", "quantity": 1, "returnReason": "OTHER", "returnReasonNote": "RMA#5331615 Resolution: Refund to payment method. Return reason: Changed my mind. "}, {"id": "gid://shopify/ReturnLineItem/7844495498", "quantity": 1, "returnReason": "OTHER", "returnReasonNote": "RMA#5331615 Resolution: Refund to payment method. Return reason: Changed my mind. "}, {"id": "gid://shopify/ReturnLineItem/7844528266", "quantity": 1, "returnReason": "OTHER", "returnReasonNote": "RMA#5331615 Resolution: Refund to payment method. Return reason: Changed my mind. "}, {"id": "gid://shopify/ReturnLineItem/7844561034", "quantity": 1, "returnReason": "OTHER", "returnReasonNote": "RMA#5331615 Resolution: Refund to payment method. Return reason: Changed my mind. "}], "status": "CLOSED"}], "shippingAddress": {"address1": "Address", "address2": null, "city": "Cape Town", "company": null, "country": "South Africa", "countryCodeV2": "ZA", "firstName": "Test", "lastName": "User", "name": null, "phone": null, "province": "Western Cape", "provinceCode": "WC", "zip": "7777"}, "shippingLines": [{"title": "Standard", "code": "Standard", "originalPriceSet": {"shopMoney": {"amount": 50, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 50, "currencyCode": "ZAR"}}, "taxLines": [], "discountedPriceSet": {"shopMoney": {"amount": 50, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 50, "currencyCode": "ZAR"}}}], "suggestedRefund": {"amountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "refundLineItems": [], "shipping": {"amountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "maximumRefundableSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}}, "suggestedTransactions": []}, "taxesIncluded": true, "taxLines": [{"priceSet": {"shopMoney": {"amount": 513.91, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 513.91, "currencyCode": "ZAR"}}, "rate": 0.15, "title": "VAT"}], "totalDiscountsSet": {"shopMoney": {"amount": 50, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 50, "currencyCode": "ZAR"}}, "fulfillmentOrders": [{"id": "gid://shopify/FulfillmentOrder/6860963020938", "status": "CLOSED", "supportedActions": [], "fulfillments": [{"id": "gid://shopify/Fulfillment/4786281578634", "status": "SUCCESS", "trackingInfo": []}], "lineItems": [{"id": "gid://shopify/FulfillmentOrderLineItem/**************", "remainingQuantity": 0, "sku": "SKU001", "totalQuantity": 5}, {"id": "gid://shopify/FulfillmentOrderLineItem/**************", "remainingQuantity": 0, "sku": "SKU002", "totalQuantity": 5}]}], "totalRefundedSet": {"shopMoney": {"amount": 3151, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 3151, "currencyCode": "ZAR"}}, "totalRefundedShippingSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}, "transactions": [{"accountNumber": null, "amountSet": {"shopMoney": {"amount": 3990, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 3990, "currencyCode": "ZAR"}}, "authorizationCode": "53433", "createdAt": "2024-11-19T14:42:10Z", "gateway": "bogus", "id": "gid://shopify/OrderTransaction/*************", "kind": "SALE", "parentTransaction": null, "paymentDetails": {"avsResultCode": null, "bin": "1", "company": "<PERSON><PERSON>", "cvvResultCode": null, "expirationMonth": 12, "expirationYear": 2034, "name": "Test User", "number": "•••• •••• •••• 1", "paymentMethodName": "card"}, "paymentId": "r7tDItXGtEYIZddQe0rl1vxEm", "processedAt": "2024-11-19T14:42:10Z", "receiptJson": "{\"paid_amount\":\"3990.00\"}", "status": "SUCCESS"}, {"accountNumber": null, "amountSet": {"shopMoney": {"amount": 2355.51, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 2355.51, "currencyCode": "ZAR"}}, "authorizationCode": null, "createdAt": "2024-11-19T14:45:31Z", "gateway": "bogus", "id": "gid://shopify/OrderTransaction/*************", "kind": "REFUND", "parentTransaction": {"accountNumber": null, "amountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "authorizationCode": null, "createdAt": null, "gateway": null, "id": "gid://shopify/OrderTransaction/*************", "kind": null, "parentTransaction": null, "paymentDetails": {"avsResultCode": null, "bin": null, "company": null, "cvvResultCode": null, "expirationMonth": null, "expirationYear": null, "name": null, "number": null, "paymentMethodName": null}, "paymentId": null, "processedAt": null, "receiptJson": null, "status": null}, "paymentDetails": {"avsResultCode": null, "bin": "1", "company": "<PERSON><PERSON>", "cvvResultCode": null, "expirationMonth": 12, "expirationYear": 2034, "name": "Test User", "number": "•••• •••• •••• 1", "paymentMethodName": "card"}, "paymentId": "#1206.2", "processedAt": "2024-11-19T14:45:31Z", "receiptJson": "{\"paid_amount\":\"2355.51\"}", "status": "SUCCESS"}, {"accountNumber": null, "amountSet": {"shopMoney": {"amount": 795.49, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 795.49, "currencyCode": "ZAR"}}, "authorizationCode": null, "createdAt": "2024-11-19T14:45:55Z", "gateway": "bogus", "id": "gid://shopify/OrderTransaction/*************", "kind": "REFUND", "parentTransaction": {"accountNumber": null, "amountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "authorizationCode": null, "createdAt": null, "gateway": null, "id": "gid://shopify/OrderTransaction/*************", "kind": null, "parentTransaction": null, "paymentDetails": {"avsResultCode": null, "bin": null, "company": null, "cvvResultCode": null, "expirationMonth": null, "expirationYear": null, "name": null, "number": null, "paymentMethodName": null}, "paymentId": null, "processedAt": null, "receiptJson": null, "status": null}, "paymentDetails": {"avsResultCode": null, "bin": "1", "company": "<PERSON><PERSON>", "cvvResultCode": null, "expirationMonth": 12, "expirationYear": 2034, "name": "Test User", "number": "•••• •••• •••• 1", "paymentMethodName": "card"}, "paymentId": "#1206.3", "processedAt": "2024-11-19T14:45:55Z", "receiptJson": "{\"paid_amount\":\"795.49\"}", "status": "SUCCESS"}]}