{"rmaSummary": {"rmaId": "6174828", "status": "Done", "order_name": "LSSA-43397", "events": [{"eventName": "RMA_CREATED", "eventDate": "2025-02-04T16:07:29+00:00", "triggeredVia": "Return Portal", "storeLocationId": "online", "userDetails": {"userFirstName": null, "userLastName": null, "userEmailAddress": null, "userRole": null}}, {"eventName": "RMA_APPROVED", "eventDate": "2025-02-05T08:20:16+00:00", "triggeredVia": "Dashboard", "storeLocationId": "online", "userDetails": {"userFirstName": null, "userLastName": null, "userEmailAddress": null, "userRole": null}}]}, "orderDetails": {"orderId": "5994829054041", "orderName": null}, "shipments": [{"shipmentId": "5972466", "status": "Received", "carrierName": "CourierGuy", "trackingNumber": "OPT-003000009", "trackingURL": "https://optimise.parcelninja.com/shipment/track/OPT-003000009", "labelURL": "https://sellerportal.dpworld.com/api/file-download?link=5614490", "customerAddress": {"name": "<PERSON><PERSON><PERSON>", "street1": "124 agapanthus street Lentegeur", "street2": "", "city": "Cape Town", "state": "WC", "zip": "7785", "country": "ZA", "phone": "0813303968", "email": "<EMAIL>"}, "returnAddress": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "street1": "Ashworth Logistics Park, Building C", "street2": "18 Laneshaw Street", "city": "Johannesburg", "state": "Gauteng", "zip": "1609", "country": "ZA", "phone": "", "email": "<EMAIL>"}, "isOutbound": false}], "items": [{"shopLineItemId": "14513192403033", "shopProductId": "7806002004057", "variantId": "41905887707225", "returnedItemId": "********", "fullName": "LEVI'S® WOMEN'S WEDGIE STRAIGHT JEANS - LIGHT INDIGO - WORN IN - 30 / 28 / Light Indigo - Worn In", "productName": "LEVI'S® WOMEN'S WEDGIE STRAIGHT JEANS - LIGHT INDIGO - WORN IN", "variantName": "30 / 28 / Light Indigo - Worn In", "image": "https://cdn.shopify.com/s/files/1/0589/0873/7625/files/633516177156c30ff80f40b0587b9a79_600x600.jpg?v=1727104912", "sku": "A893700123028", "barcode": "5401180361823", "paidPrice": {"amount": 649, "currency": "ZAR", "shopAmount": 649, "shopCurrency": "ZAR"}, "paidTax": {"amount": 84.65, "currency": "ZAR", "shopAmount": 84.65, "shopCurrency": "ZAR"}, "taxIncluded": true, "returnReason": "Does not fit", "resolutionName": "Refund to payment method", "policyRuleName": "Refund to Payment Method", "isAutoApprove": false, "resolutionType": "RefundToPaymentMethod", "restockingFee": 0, "returnFee": 0, "shipmentFee": 0, "shipmentId": "5972466", "extendedReason": " > Too large / loose <br />\n > Yes", "returnMethod": {"type": "Ship with any carrier", "name": "SFS CourierGuy"}, "itemValidationStatusResponse": {"returnItemId": null, "isRestockable": false, "isReturned": false, "nonRestockableReason": null, "nonReturnedReason": null, "storeLocationId": null, "lastValidationDate": null, "lastValidatedBy": null, "thirdPartyName": null, "userDetails": {"userFirstName": null, "userLastName": null, "userEmailAddress": null, "userRole": null}}, "exchangeItems": []}], "comments": [{"commentType": "merchant", "triggeredBy": "<PERSON><PERSON><PERSON><PERSON>", "htmlText": "The RMA error was resolved by levis-sa.myshopify.com", "datetime": "2025-02-18T12:00:17+00:00"}, {"commentType": "error", "triggeredBy": "", "htmlText": "Refund Failed. Reason: Platform returned status: FAILURE RMA: 6174828", "datetime": "2025-02-17T12:55:32+00:00"}, {"commentType": "system", "triggeredBy": "<PERSON><PERSON><PERSON><PERSON>", "htmlText": "Completed Shopify return LSSA-43397-R1 for order LSSA-43397", "datetime": "2025-02-17T09:55:37+00:00"}, {"commentType": "system", "triggeredBy": "<PERSON><PERSON><PERSON><PERSON>", "htmlText": "Request DONE", "datetime": "2025-02-17T09:55:37+00:00"}, {"commentType": "system", "triggeredBy": "<PERSON><PERSON><PERSON><PERSON>", "htmlText": "Total refund amount: R649.00<br>Transaction ID: 7905521", "datetime": "2025-02-17T09:55:33+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 5972466 was updated", "datetime": "2025-02-14T12:59:31+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 5972466 was updated", "datetime": "2025-02-12T19:15:54+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 5972466 was updated", "datetime": "2025-02-12T10:41:49+00:00"}, {"commentType": "system", "triggeredBy": "Automation", "htmlText": "Shipment <strong>RECEIVED</strong>", "datetime": "2025-02-12T10:41:48+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 5972466 was updated", "datetime": "2025-02-12T05:25:48+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 5972466 was updated", "datetime": "2025-02-10T15:14:54+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 5972466 was updated", "datetime": "2025-02-10T14:22:52+00:00"}, {"commentType": "system", "triggeredBy": "Automation", "htmlText": "Shipment <strong>IN TRANSIT</strong>", "datetime": "2025-02-10T14:22:52+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 5972466 was updated", "datetime": "2025-02-05T08:22:48+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 5972466 was updated", "datetime": "2025-02-05T08:21:49+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 5972466 was updated", "datetime": "2025-02-05T08:21:47+00:00"}, {"commentType": "system", "triggeredBy": "#267963", "htmlText": "Request <strong>APPROVED</strong>", "datetime": "2025-02-05T08:20:20+00:00"}, {"commentType": "system", "triggeredBy": "#267963", "htmlText": "Created & approved Shopify return LSSA-43397-R1 for order LSSA-43397", "datetime": "2025-02-05T08:20:19+00:00"}, {"commentType": "system", "triggeredBy": "", "htmlText": "Request SUBMITTED", "datetime": "2025-02-04T16:07:30+00:00"}], "exchangeOrders": [], "cancellationFee": 0, "transactions": [{"id": "7905521", "type": "RefundToPaymentMethod", "externalReferenceId": "************", "externalReferenceType": "PlatformRefundId", "amount": {"amount": 649, "currency": "ZAR", "shopAmount": 649, "shopCurrency": "ZAR"}, "status": "Failed", "processor": "Shopify", "bankAccountDetails": {"iban": null, "firstName": null, "lastName": null, "branchName": null, "branchAddress": null, "comment": null}, "returnItemIds": ["********"], "createdAt": "2025-02-17T09:55:32+00:00", "lastUpdated": "2025-02-17T12:55:32+00:00"}], "createdAt": "2025-02-04T16:07:29+00:00", "lastUpdated": "2025-02-17T09:55:36+00:00"}