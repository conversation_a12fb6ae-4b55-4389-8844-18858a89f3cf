{"billingAddress": {"address1": "309 Umhlanga Rocks Drive", "address2": "Glass House Office Park", "city": "Umhlanga", "company": "<PERSON>ne Elevators", "country": "South Africa", "countryCodeV2": "ZA", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Mthembu", "name": null, "phone": "0789699680", "province": "KwaZulu-Natal", "provinceCode": "NL", "zip": "4319"}, "createdAt": "2025-01-26T13:51:11Z", "sourceName": "web", "sourceIdentifier": null, "customAttributes": [{"key": "analyzely_sse", "value": "{\"analyzelyClientId\":\"1408621758.1737715799\",\"analyzelySessionId\":\"1737899075\"}"}, {"key": "FoxKit CartGoal", "value": "true"}], "customer": {"displayName": null, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "id": "gid://shopify/Customer/7242311794777", "lastName": "Mthembu", "phone": "+27789699680"}, "customerAcceptsMarketing": true, "discountCodes": [], "fullyPaid": true, "id": "gid://shopify/Order/5995910266969", "lineItems": [{"id": "gid://shopify/LineItem/14515697614937", "discountAllocations": [], "title": "LEVI’S® MEN'S SKINNY TAPER  - BLACK", "quantity": 1, "originalUnitPriceSet": {"shopMoney": {"amount": 549, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 549, "currencyCode": "ZAR"}}, "sku": "A544600003434", "taxable": true, "taxLines": [{"priceSet": {"shopMoney": {"amount": 71.61, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 71.61, "currencyCode": "ZAR"}}, "rate": 0.15, "title": "VAT"}], "totalDiscountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}, {"id": "gid://shopify/LineItem/14515697647705", "discountAllocations": [], "title": "LEVI'S® MEN'S 510™ SKINNY JEANS - BLUE", "quantity": 1, "originalUnitPriceSet": {"shopMoney": {"amount": 599, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 599, "currencyCode": "ZAR"}}, "sku": "A405500283632", "taxable": true, "taxLines": [{"priceSet": {"shopMoney": {"amount": 78.13, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 78.13, "currencyCode": "ZAR"}}, "rate": 0.15, "title": "VAT"}], "totalDiscountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}], "name": "LSSA-43532", "note": "2025-02-01 16:05 UTC\nReturnGO, RMA6149518:\nRequest SUBMITTED\n\n2025-02-04 09:47 UTC\nReturnGO, RMA6149518:\nRequest APPROVED\nBy #267963\n\n2025-04-08 11:34 UTC\nReturnGO, RMA6149518:\nTotal refund amount: R1,148.00\nTransaction IDs: 8627101, 8627102\nBy <PERSON><PERSON><PERSON>\n\n2025-04-08 11:34 UTC\nReturnGO, RMA6149518:\nShipment RECEIVED\nBy <PERSON><PERSON><PERSON><PERSON>\n\n2025-04-08 11:34 UTC\nReturnGO, RMA6149518:\nRequest DONE\nBy <PERSON>-<PERSON><PERSON>", "refunds": [{"createdAt": "2025-04-08T11:34:12Z", "id": "gid://shopify/Refund/932730306649", "note": null, "orderAdjustments": [{"amountSet": {"shopMoney": {"amount": 1148, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 1148, "currencyCode": "ZAR"}}, "id": "gid://shopify/OrderAdjustment/321458241625", "reason": "REFUND_DISCREPANCY", "taxAmountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}], "updatedAt": "2025-04-08T11:34:13Z", "refundLineItems": [{"id": "gid://shopify/RefundLineItem/480235487321", "lineItem": {"id": null, "discountAllocations": [], "title": "LEVI’S® MEN'S SKINNY TAPER  - BLACK", "quantity": 1, "originalUnitPriceSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "sku": "A544600003434", "taxable": null, "taxLines": [], "totalDiscountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}, "priceSet": {"shopMoney": {"amount": 549, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 549, "currencyCode": "ZAR"}}, "quantity": 1, "restockType": "NO_RESTOCK", "restocked": false, "subtotalSet": {"shopMoney": {"amount": 549, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 549, "currencyCode": "ZAR"}}, "totalTaxSet": {"shopMoney": {"amount": 71.61, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 71.61, "currencyCode": "ZAR"}}}, {"id": "gid://shopify/RefundLineItem/480235520089", "lineItem": {"id": null, "discountAllocations": [], "title": "LEVI'S® MEN'S 510™ SKINNY JEANS - BLUE", "quantity": 1, "originalUnitPriceSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "sku": "A405500283632", "taxable": null, "taxLines": [], "totalDiscountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}, "priceSet": {"shopMoney": {"amount": 599, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 599, "currencyCode": "ZAR"}}, "quantity": 1, "restockType": "NO_RESTOCK", "restocked": false, "subtotalSet": {"shopMoney": {"amount": 599, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 599, "currencyCode": "ZAR"}}, "totalTaxSet": {"shopMoney": {"amount": 78.13, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 78.13, "currencyCode": "ZAR"}}}], "refundShippingLines": [], "return": {"id": "gid://shopify/Return/***********", "name": "LSSA-43532-R1", "status": "CLOSED"}, "transactions": [{"accountNumber": null, "amountSet": {"shopMoney": {"amount": 1148, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 1148, "currencyCode": "ZAR"}}, "authorizationCode": null, "createdAt": "2025-04-08T11:34:07Z", "gateway": "Peach Payments", "id": "gid://shopify/OrderTransaction/*************", "kind": "REFUND", "parentTransaction": {"accountNumber": null, "amountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "authorizationCode": null, "createdAt": null, "gateway": null, "id": "gid://shopify/OrderTransaction/*************", "kind": null, "parentTransaction": null, "paymentDetails": {"avsResultCode": null, "bin": null, "company": null, "cvvResultCode": null, "expirationMonth": null, "expirationYear": null, "name": null, "number": null, "paymentMethodName": null}, "paymentId": null, "processedAt": null, "receiptJson": null, "status": null}, "paymentDetails": {"avsResultCode": null, "bin": null, "company": null, "cvvResultCode": null, "expirationMonth": null, "expirationYear": null, "name": null, "number": null, "paymentMethodName": null}, "paymentId": "XcAxVGvOFjWHWN5Ux7ngjlK6", "processedAt": "2025-04-08T11:34:07Z", "receiptJson": "{\"refund_id\":\"XcAxVGvOFjWHWN5Ux7ngjlK6\"}", "status": "FAILURE"}], "totalRefundedSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}], "returns": [{"id": "gid://shopify/Return/***********", "name": "LSSA-43532-R1", "returnLineItems": [{"id": "gid://shopify/ReturnLineItem/31474057305", "quantity": 1, "returnReason": "OTHER", "returnReasonNote": "RMA#6149518 Resolution: Variant exchange. Return reason: Does not fit. Answers:  NEW > 38 / 32 / Black \n > Too small / tight \n > Yes."}, {"id": "gid://shopify/ReturnLineItem/31474090073", "quantity": 1, "returnReason": "OTHER", "returnReasonNote": "RMA#6149518 Resolution: Refund to payment method. Return reason: Does not fit. Answers:  > Too small / tight \n > Yes."}], "status": "CLOSED"}], "shippingAddress": {"address1": "309 Umhlanga Rocks Drive", "address2": "Glass House Office Park", "city": "Umhlanga", "company": "<PERSON>ne Elevators", "country": "South Africa", "countryCodeV2": "ZA", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Mthembu", "name": null, "phone": "0789699680", "province": "KwaZulu-Natal", "provinceCode": "NL", "zip": "4319"}, "shippingLines": [{"title": "Standard Shipping", "code": "Standard Shipping", "originalPriceSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}, "taxLines": [], "discountedPriceSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}], "suggestedRefund": {"amountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "refundLineItems": [], "shipping": {"amountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "maximumRefundableSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}}, "suggestedTransactions": []}, "taxesIncluded": true, "taxLines": [{"priceSet": {"shopMoney": {"amount": 149.74, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 149.74, "currencyCode": "ZAR"}}, "rate": 0.15, "title": "VAT"}], "totalDiscountsSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}, "fulfillmentOrders": [{"id": "gid://shopify/FulfillmentOrder/7036377399385", "status": "CLOSED", "supportedActions": [], "fulfillments": [{"id": "gid://shopify/Fulfillment/5442751955033", "status": "SUCCESS", "trackingInfo": [{"company": "DPD", "number": "PNJ62964274", "url": "https://store.parcelninja.com/tracking.aspx?WaybillNo=PNJ62964274"}]}], "lineItems": [{"id": "gid://shopify/FulfillmentOrderLineItem/**************", "remainingQuantity": 0, "sku": "A544600003434", "totalQuantity": 1}, {"id": "gid://shopify/FulfillmentOrderLineItem/**************", "remainingQuantity": 0, "sku": "A405500283632", "totalQuantity": 1}]}], "totalRefundedSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}, "totalRefundedShippingSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}, "transactions": [{"accountNumber": null, "amountSet": {"shopMoney": {"amount": 1148, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 1148, "currencyCode": "ZAR"}}, "authorizationCode": null, "createdAt": "2025-01-26T13:51:10Z", "gateway": "Peach Payments", "id": "gid://shopify/OrderTransaction/*************", "kind": "SALE", "parentTransaction": null, "paymentDetails": {"avsResultCode": null, "bin": null, "company": null, "cvvResultCode": null, "expirationMonth": null, "expirationYear": null, "name": null, "number": null, "paymentMethodName": null}, "paymentId": "rFjLFjzMOTrdAEY6o2eGdemW4", "processedAt": "2025-01-26T13:51:10Z", "receiptJson": "{\"payment_id\":\"rFjLFjzMOTrdAEY6o2eGdemW4\"}", "status": "SUCCESS"}, {"accountNumber": null, "amountSet": {"shopMoney": {"amount": 1148, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 1148, "currencyCode": "ZAR"}}, "authorizationCode": null, "createdAt": "2025-04-08T11:34:07Z", "gateway": "Peach Payments", "id": "gid://shopify/OrderTransaction/*************", "kind": "REFUND", "parentTransaction": {"accountNumber": null, "amountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "authorizationCode": null, "createdAt": null, "gateway": null, "id": "gid://shopify/OrderTransaction/*************", "kind": null, "parentTransaction": null, "paymentDetails": {"avsResultCode": null, "bin": null, "company": null, "cvvResultCode": null, "expirationMonth": null, "expirationYear": null, "name": null, "number": null, "paymentMethodName": null}, "paymentId": null, "processedAt": null, "receiptJson": null, "status": null}, "paymentDetails": {"avsResultCode": null, "bin": null, "company": null, "cvvResultCode": null, "expirationMonth": null, "expirationYear": null, "name": null, "number": null, "paymentMethodName": null}, "paymentId": "XcAxVGvOFjWHWN5Ux7ngjlK6", "processedAt": "2025-04-08T11:34:07Z", "receiptJson": "{\"refund_id\":\"XcAxVGvOFjWHWN5Ux7ngjlK6\"}", "status": "FAILURE"}], "discountApplications": [{"allocationMethod": "EACH", "index": 0, "targetSelection": "ALL", "targetType": "SHIPPING_LINE", "code": null}]}