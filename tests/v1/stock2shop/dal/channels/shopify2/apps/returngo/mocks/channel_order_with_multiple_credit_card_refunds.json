{"billing_address": {"id": null, "created": null, "modified": null, "address1": "Address", "address2": null, "city": "Cape Town", "company": null, "country": "South Africa", "country_code": "ZA", "first_name": "Test", "last_name": "User", "phone": "+27123456789", "province": "Western Cape", "province_code": "WC", "zip": "7777"}, "customer": {"channel_customer_code": "7136610254986", "accepts_marketing": false, "email": "<EMAIL>", "first_name": "Test", "last_name": "User"}, "instruction": "add_order", "line_items": [{"barcode": null, "grams": null, "price": 520.86956521739, "price_incl": 599, "qty": 5, "sku": "SKU001", "tax_lines": [{"code": "taxed", "title": "VAT", "price": 384.13, "rate": 15}], "title": "Product X", "total_discount": 0, "total_discount_incl": 0}, {"barcode": null, "grams": null, "price": 173.04347826087, "price_incl": 199, "qty": 5, "sku": "SKU002", "tax_lines": [{"code": "taxed", "title": "VAT", "price": 129.78, "rate": 15}], "title": "Product Y", "total_discount": 0, "total_discount_incl": 0}], "params": [], "shipping_address": {"id": null, "created": null, "modified": null, "address1": "Address", "address2": null, "city": "Cape Town", "company": null, "country": "South Africa", "country_code": "ZA", "first_name": "Test", "last_name": "User", "phone": "+27123456789", "province": "Western Cape", "province_code": "WC", "zip": "7777"}, "shipping_lines": [{"price": 50, "price_incl": 50, "tax_lines": [], "title": "Standard", "total_discount": 0, "total_discount_incl": 0}], "transactions": [{"amount": 3990, "amount_calculation_method": null, "auth_code": "53433", "card_bin": "1", "card_last_4_digits": "•••• •••• •••• 1", "channel_transaction_code": "6786913796234", "completed_date": "2024-11-19 14:42:10", "created": null, "gateway": "bogus", "is_store_credit": false, "kind": "debit", "modified": null, "origin_connector_id": 2, "origin_connector_kind": "channel", "order_id": null, "payment_code": "r7tDItXGtEYIZddQe0rl1vxEm", "payment_method": "card", "refund_identifier": null, "rrn": null, "stan": null, "state": "complete", "status": "SUCCESS", "transaction_identifier": null}], "channel_id": 2, "channel_order_code": "#1206", "has_taxes_incl": true, "meta": [], "notes": "2024-11-19 14:44 UTC\nReturnGO, RMA5331615:\nRequest SUBMITTED\n\n2024-11-19 14:45 UTC\nReturnGO, RMA5331615:\nRequest APPROVED\nBy #272013\n\n2024-11-19 14:45 UTC\nReturnGO, RMA5331615:\nTotal refund amount: R 2,355.51\nTransaction IDs: 6458057, 6458058, 6458059, 6458060\nBy #272013\n\n2024-11-19 14:45 UTC\nReturnGO, RMA5331615:\nTotal refund amount: R 795.49\nTransaction IDs: 6458065, 6458066, 6458067, 6458068\nBy #272013\n\n2024-11-19 14:46 UTC\nReturnGO, RMA5331615:\nShipment RECEIVED\nBy #272013\n\n2024-11-19 14:46 UTC\nReturnGO, RMA5331615:\nRequest DONE\nBy #272013", "refunds": [{"amount_incl": 2355.51, "channel_id": 2, "channel_refund_code": "873641115786", "channel_refund_state": "complete", "channel_created_date": "2024-11-19 14:45:31", "client_id": null, "created": null, "fulfillment_created_date": null, "fulfillment_service_id": null, "fulfillment_refund_code": null, "fulfillment_refund_state": null, "line_items": [{"client_id": null, "code": "item", "created": null, "meta": [{"key": "restocked", "refund_item_identifier": null, "value": "false"}, {"key": "restockType", "refund_item_identifier": null, "value": "NO_RESTOCK"}], "modified": null, "refund_item_identifier": null, "refund_identifier": null, "sku": "SKU001", "title": "Product X", "qty": 1, "unit_amount_incl": 599, "subtotal_incl": 589, "subtotal_tax": 76.82}, {"client_id": null, "code": "item", "created": null, "meta": [{"key": "restocked", "refund_item_identifier": null, "value": "false"}, {"key": "restockType", "refund_item_identifier": null, "value": "NO_RESTOCK"}], "modified": null, "refund_item_identifier": null, "refund_identifier": null, "sku": "SKU001", "title": "Product X", "qty": 1, "unit_amount_incl": 599, "subtotal_incl": 589, "subtotal_tax": 76.82}, {"client_id": null, "code": "item", "created": null, "meta": [{"key": "restocked", "refund_item_identifier": null, "value": "false"}, {"key": "restockType", "refund_item_identifier": null, "value": "NO_RESTOCK"}], "modified": null, "refund_item_identifier": null, "refund_identifier": null, "sku": "SKU001", "title": "Product X", "qty": 1, "unit_amount_incl": 599, "subtotal_incl": 589, "subtotal_tax": 76.83}, {"client_id": null, "code": "item", "created": null, "meta": [{"key": "restocked", "refund_item_identifier": null, "value": "false"}, {"key": "restockType", "refund_item_identifier": null, "value": "NO_RESTOCK"}], "modified": null, "refund_item_identifier": null, "refund_identifier": null, "sku": "SKU001", "title": "Product X", "qty": 1, "unit_amount_incl": 599, "subtotal_incl": 589, "subtotal_tax": 76.83}, {"client_id": null, "code": "discrep", "created": null, "meta": [], "modified": null, "refund_item_identifier": null, "refund_identifier": null, "sku": null, "title": "REFUND_DISCREPANCY", "qty": null, "unit_amount_incl": -0.49, "subtotal_incl": -0.49, "subtotal_tax": 0}], "meta": [], "modified": null, "notes": null, "order_id": null, "origin_connector_kind": null, "origin_connector_id": null, "refund_identifier": null, "source_id": null, "source_refund_code": null, "source_refund_state": null, "source_created_date": null, "transactions": [{"amount": 2355.51, "amount_calculation_method": null, "auth_code": null, "card_bin": "1", "card_last_4_digits": "•••• •••• •••• 1", "channel_transaction_code": "6786918219914", "completed_date": "2024-11-19 14:45:31", "created": null, "gateway": "bogus", "is_store_credit": false, "kind": "credit", "modified": null, "origin_connector_id": null, "origin_connector_kind": null, "order_id": null, "payment_code": "#1206.2", "payment_method": "card", "refund_identifier": null, "rrn": null, "stan": null, "state": "complete", "status": "SUCCESS", "transaction_identifier": null}]}, {"amount_incl": 795.49, "channel_id": 2, "channel_refund_code": "873641181322", "channel_refund_state": "complete", "channel_created_date": "2024-11-19 14:45:55", "client_id": null, "created": null, "fulfillment_created_date": null, "fulfillment_service_id": null, "fulfillment_refund_code": null, "fulfillment_refund_state": null, "line_items": [{"client_id": null, "code": "item", "created": null, "meta": [{"key": "restocked", "refund_item_identifier": null, "value": "false"}, {"key": "restockType", "refund_item_identifier": null, "value": "NO_RESTOCK"}], "modified": null, "refund_item_identifier": null, "refund_identifier": null, "sku": "SKU002", "title": "Product Y", "qty": 1, "unit_amount_incl": 199, "subtotal_incl": 199, "subtotal_tax": 25.95}, {"client_id": null, "code": "item", "created": null, "meta": [{"key": "restocked", "refund_item_identifier": null, "value": "false"}, {"key": "restockType", "refund_item_identifier": null, "value": "NO_RESTOCK"}], "modified": null, "refund_item_identifier": null, "refund_identifier": null, "sku": "SKU002", "title": "Product Y", "qty": 1, "unit_amount_incl": 199, "subtotal_incl": 199, "subtotal_tax": 25.95}, {"client_id": null, "code": "item", "created": null, "meta": [{"key": "restocked", "refund_item_identifier": null, "value": "false"}, {"key": "restockType", "refund_item_identifier": null, "value": "NO_RESTOCK"}], "modified": null, "refund_item_identifier": null, "refund_identifier": null, "sku": "SKU002", "title": "Product Y", "qty": 1, "unit_amount_incl": 199, "subtotal_incl": 199, "subtotal_tax": 25.96}, {"client_id": null, "code": "item", "created": null, "meta": [{"key": "restocked", "refund_item_identifier": null, "value": "false"}, {"key": "restockType", "refund_item_identifier": null, "value": "NO_RESTOCK"}], "modified": null, "refund_item_identifier": null, "refund_identifier": null, "sku": "SKU002", "title": "Product Y", "qty": 1, "unit_amount_incl": 199, "subtotal_incl": 199, "subtotal_tax": 25.96}, {"client_id": null, "code": "discrep", "created": null, "meta": [], "modified": null, "refund_item_identifier": null, "refund_identifier": null, "sku": null, "title": "REFUND_DISCREPANCY", "qty": null, "unit_amount_incl": -0.51, "subtotal_incl": -0.51, "subtotal_tax": 0}], "meta": [], "modified": null, "notes": null, "order_id": null, "origin_connector_kind": null, "origin_connector_id": null, "refund_identifier": null, "source_id": null, "source_refund_code": null, "source_refund_state": null, "source_created_date": null, "transactions": [{"amount": 795.49, "amount_calculation_method": null, "auth_code": null, "card_bin": "1", "card_last_4_digits": "•••• •••• •••• 1", "channel_transaction_code": "6786918678666", "completed_date": "2024-11-19 14:45:55", "created": null, "gateway": "bogus", "is_store_credit": false, "kind": "credit", "modified": null, "origin_connector_id": null, "origin_connector_kind": null, "order_id": null, "payment_code": "#1206.3", "payment_method": "card", "refund_identifier": null, "rrn": null, "stan": null, "state": "complete", "status": "SUCCESS", "transaction_identifier": null}]}], "ordered_date": "2024-11-19 14:42:12", "total_discount": 43.478260869565, "total_discount_incl": 50}