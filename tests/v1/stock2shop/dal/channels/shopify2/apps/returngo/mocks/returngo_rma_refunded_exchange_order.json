{"rmaSummary": {"rmaId": "6841705", "status": "Done", "order_name": "LSSA-QA-1070", "events": [{"eventName": "RMA_CREATED", "eventDate": "2025-04-16T13:24:12+00:00", "triggeredVia": "Return Portal", "storeLocationId": "online"}, {"eventName": "RMA_APPROVED", "eventDate": "2025-04-16T13:24:35+00:00", "triggeredVia": "Dashboard", "storeLocationId": "online"}]}, "orderDetails": {"orderId": "5799992426684", "order_name": "LSSA-QA-1070"}, "shipments": [{"shipmentId": "6623958", "status": "Received", "customerAddress": {"name": "<PERSON><PERSON>", "street1": "Lente<PERSON>ur", "street2": "Test Apartment", "city": "Cape Town", "state": "WC", "zip": "7786", "country": "ZA", "phone": "+27987654321", "email": "<EMAIL>"}, "returnAddress": {"name": "", "street1": "", "street2": "", "city": "", "zip": "", "country": "IN", "phone": "", "email": "<EMAIL>"}, "isOutbound": false}], "items": [{"shopLineItemId": "13994879680700", "shopProductId": "8683800199356", "variantId": "44446123229372", "returnedItemId": "30778858", "fullName": "501  ORIGINAL ZA ON THE BORDERLINE - 28 / 32 / Dark Indigo - Worn In", "productName": "501  ORIGINAL ZA ON THE BORDERLINE", "variantName": "28 / 32 / Dark Indigo - Worn In", "image": "https://cdn.shopify.com/s/files/1/0678/1314/6812/files/6a02ccaf466410a67bd53b37a81e2423_9fc15730-ee87-4e0d-9cf5-9e383af5f2a0_600x600.jpg?v=1736761404", "sku": "A544700182832", "barcode": "5401180164592", "paidPrice": {"amount": 779, "currency": "ZAR", "shopAmount": 779, "shopCurrency": "ZAR"}, "paidTax": {"amount": 0, "currency": "ZAR", "shopAmount": 0, "shopCurrency": "ZAR"}, "taxIncluded": true, "returnReason": "Does not fit", "resolutionName": "Refund to payment method", "policyRuleName": "Refund to Payment Method", "isAutoApprove": 0, "resolutionType": "RefundToPaymentMethod", "restockingFee": 0, "returnFee": 0, "shipmentFee": 0, "shipmentId": "6623958", "extendedReason": " > Other", "returnMethod": {"type": "Ship with any carrier", "name": "Ship with any carrier"}, "itemValidationStatusResponse": {"isRestockable": false, "isReturned": false, "nonRestockableReason": null, "nonReturnedReason": null, "storeLocationId": null, "lastValidationDate": null, "lastValidatedBy": null, "thirdPartyName": null, "userDetails": null}, "exchangeItems": [], "followUpQuestions": [{"type": "TextArray", "name": "Why?", "answer": {"textArrayAnswer": ["Other"]}}]}, {"shopLineItemId": "13994879713468", "shopProductId": "8683800199356", "variantId": "44446123294908", "returnedItemId": "30778859", "fullName": "501  ORIGINAL ZA ON THE BORDERLINE - 30 / 32 / Dark Indigo - Worn In", "productName": "501  ORIGINAL ZA ON THE BORDERLINE", "variantName": "30 / 32 / Dark Indigo - Worn In", "image": "https://cdn.shopify.com/s/files/1/0678/1314/6812/files/6a02ccaf466410a67bd53b37a81e2423_9fc15730-ee87-4e0d-9cf5-9e383af5f2a0_600x600.jpg?v=1736761404", "sku": "A544700183032", "barcode": "5401180164608", "paidPrice": {"amount": 779, "currency": "ZAR", "shopAmount": 779, "shopCurrency": "ZAR"}, "paidTax": {"amount": 0, "currency": "ZAR", "shopAmount": 0, "shopCurrency": "ZAR"}, "taxIncluded": true, "returnReason": "Does not fit", "resolutionName": "Refund to payment method", "policyRuleName": "Refund to Payment Method", "isAutoApprove": 0, "resolutionType": "RefundToPaymentMethod", "restockingFee": 0, "returnFee": 0, "shipmentFee": 0, "shipmentId": "6623958", "extendedReason": " > Other", "returnMethod": {"type": "Ship with any carrier", "name": "Ship with any carrier"}, "itemValidationStatusResponse": {"isRestockable": false, "isReturned": false, "nonRestockableReason": null, "nonReturnedReason": null, "storeLocationId": null, "lastValidationDate": null, "lastValidatedBy": null, "thirdPartyName": null, "userDetails": null}, "exchangeItems": [], "followUpQuestions": [{"type": "TextArray", "name": "Why?", "answer": {"textArrayAnswer": ["Other"]}}]}, {"shopLineItemId": "13994879746236", "shopProductId": "8683800199356", "variantId": "44446123360444", "returnedItemId": "30778860", "fullName": "501  ORIGINAL ZA ON THE BORDERLINE - 32 / 32 / Dark Indigo - Worn In", "productName": "501  ORIGINAL ZA ON THE BORDERLINE", "variantName": "32 / 32 / Dark Indigo - Worn In", "image": "https://cdn.shopify.com/s/files/1/0678/1314/6812/files/6a02ccaf466410a67bd53b37a81e2423_9fc15730-ee87-4e0d-9cf5-9e383af5f2a0_600x600.jpg?v=1736761404", "sku": "A544700183232", "barcode": "5401180164615", "paidPrice": {"amount": 779, "currency": "ZAR", "shopAmount": 779, "shopCurrency": "ZAR"}, "paidTax": {"amount": 0, "currency": "ZAR", "shopAmount": 0, "shopCurrency": "ZAR"}, "taxIncluded": true, "returnReason": "Does not fit", "resolutionName": "Refund to payment method", "policyRuleName": "Refund to Payment Method", "isAutoApprove": 0, "resolutionType": "RefundToPaymentMethod", "restockingFee": 0, "returnFee": 0, "shipmentFee": 0, "shipmentId": "6623958", "extendedReason": " > Other", "returnMethod": {"type": "Ship with any carrier", "name": "Ship with any carrier"}, "itemValidationStatusResponse": {"isRestockable": false, "isReturned": false, "nonRestockableReason": null, "nonReturnedReason": null, "storeLocationId": null, "lastValidationDate": null, "lastValidatedBy": null, "thirdPartyName": null, "userDetails": null}, "exchangeItems": [], "followUpQuestions": [{"type": "TextArray", "name": "Why?", "answer": {"textArrayAnswer": ["Other"]}}]}], "comments": [{"commentType": "system", "triggeredBy": "Automation", "htmlText": "Request DONE", "datetime": "2025-04-16T13:24:51+00:00"}, {"commentType": "system", "triggeredBy": "Nahum Delport", "htmlText": "Completed Shopify return LSSA-QA-1070-R1 for order LSSA-QA-1070", "datetime": "2025-04-16T13:24:51+00:00"}, {"commentType": "system", "triggeredBy": "Nahum Delport", "htmlText": "Total refund amount: R 2,337.00<br>Transaction IDs: 8794168, 8794170, 8794172", "datetime": "2025-04-16T13:24:50+00:00"}, {"commentType": "system", "triggeredBy": "Nahum Delport", "htmlText": "Shipment <strong>RECEIVED</strong>", "datetime": "2025-04-16T13:24:42+00:00"}, {"commentType": "system", "triggeredBy": "Nahum Delport", "htmlText": "Request <strong>APPROVED</strong>", "datetime": "2025-04-16T13:24:37+00:00"}, {"commentType": "system", "triggeredBy": "Nahum Delport", "htmlText": "Created & approved Shopify return LSSA-QA-1070-R1 for order LSSA-QA-1070", "datetime": "2025-04-16T13:24:36+00:00"}, {"commentType": "system", "triggeredBy": "", "htmlText": "Request SUBMITTED, original order LSSA-QA-1069", "datetime": "2025-04-16T13:24:13+00:00"}], "exchangeOrders": [], "cancellationFee": 0, "transactions": [{"id": 8794168, "type": "RefundToPaymentMethod", "externalReferenceId": "909061914812", "externalReferenceType": "PlatformRefundId", "amount": {"amount": 779, "currency": "ZAR", "shopAmount": 779, "shopCurrency": "ZAR"}, "status": "Success", "processor": "Shopify", "returnItemIds": ["30778858"], "createdAt": "2025-04-16T13:24:50+00:00", "lastUpdated": "2025-04-16T13:24:50+00:00"}, {"id": 8794170, "type": "RefundToPaymentMethod", "externalReferenceId": "909061914812", "externalReferenceType": "PlatformRefundId", "amount": {"amount": 779, "currency": "ZAR", "shopAmount": 779, "shopCurrency": "ZAR"}, "status": "Success", "processor": "Shopify", "returnItemIds": ["30778859"], "createdAt": "2025-04-16T13:24:50+00:00", "lastUpdated": "2025-04-16T13:24:50+00:00"}, {"id": 8794172, "type": "RefundToPaymentMethod", "externalReferenceId": "909061914812", "externalReferenceType": "PlatformRefundId", "amount": {"amount": 779, "currency": "ZAR", "shopAmount": 779, "shopCurrency": "ZAR"}, "status": "Success", "processor": "Shopify", "returnItemIds": ["30778860"], "createdAt": "2025-04-16T13:24:50+00:00", "lastUpdated": "2025-04-16T13:24:50+00:00"}], "createdAt": "2025-04-16T13:24:12+00:00", "lastUpdated": "2025-04-16T13:24:51+00:00", "isOutOfPolicy": 0}