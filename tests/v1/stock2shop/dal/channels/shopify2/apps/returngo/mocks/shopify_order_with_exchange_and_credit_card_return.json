{"billingAddress": {"address1": "Address", "address2": null, "city": "Cape Town", "company": null, "country": "South Africa", "countryCodeV2": "ZA", "firstName": "Test", "lastName": "User", "name": null, "phone": null, "province": "Western Cape", "provinceCode": "WC", "zip": "7777"}, "createdAt": "2024-11-13T12:32:46Z", "customAttributes": [], "customer": {"displayName": null, "email": "<EMAIL>", "firstName": "Test", "id": "gid://shopify/Customer/7136610254986", "lastName": "User", "phone": "+27123456789"}, "customerAcceptsMarketing": false, "discountCodes": [], "fullyPaid": true, "id": "gid://shopify/Order/5755992506506", "lineItems": [{"id": "gid://shopify/LineItem/14341754159242", "title": "Product Y", "quantity": 5, "originalUnitPriceSet": {"shopMoney": {"amount": 399, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 399, "currencyCode": "ZAR"}}, "sku": "SKU001", "taxable": true, "taxLines": [{"priceSet": {"shopMoney": {"amount": 260.22, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 260.22, "currencyCode": "ZAR"}}, "rate": 0.15, "title": "VAT"}], "totalDiscountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}, {"id": "gid://shopify/LineItem/14341754192010", "title": "Product X", "quantity": 5, "originalUnitPriceSet": {"shopMoney": {"amount": 599, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 599, "currencyCode": "ZAR"}}, "sku": "SKU002", "taxable": true, "taxLines": [{"priceSet": {"shopMoney": {"amount": 384.13, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 384.13, "currencyCode": "ZAR"}}, "rate": 0.15, "title": "VAT"}], "totalDiscountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}], "name": "#1187", "note": "2024-11-13 12:38 UTC\nReturnGO, RMA5276945:\nRequest SUBMITTED\n\n2024-11-13 12:38 UTC\nReturnGO, RMA5276945:\nRequest APPROVED\nBy #272013\n\n2024-11-13 12:39 UTC\nReturnGO, RMA5276945:\nExchange order #1188 released\nBy #272013\n\n2024-11-13 12:40 UTC\nReturnGO, RMA5276945:\nTotal refund amount: R 2,016.00\nTransaction IDs: 6381409, 6381410, 6381411, 6381412\nBy #272013\n\n2024-11-13 12:40 UTC\nReturnGO, RMA5276945:\nShipment RECEIVED\nBy #272013\n\n2024-11-13 12:40 UTC\nReturnGO, RMA5276945:\nRequest DONE\nBy #272013", "refunds": [{"createdAt": "2024-11-13T12:39:59Z", "id": "gid://shopify/Refund/873335619722", "note": null, "orderAdjustments": [{"amountSet": {"shopMoney": {"amount": 1936, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 1936, "currencyCode": "ZAR"}}, "id": "gid://shopify/OrderAdjustment/260478173322", "reason": "REFUND_DISCREPANCY", "taxAmountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}], "updatedAt": "2024-11-13T12:40:00Z", "refundLineItems": [{"id": "gid://shopify/RefundLineItem/372320469130", "lineItem": {"id": null, "title": "Product X", "quantity": 5, "originalUnitPriceSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "sku": "SKU002", "taxable": null, "taxLines": [], "totalDiscountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}, "priceSet": {"shopMoney": {"amount": 599, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 599, "currencyCode": "ZAR"}}, "quantity": 1, "restockType": "NO_RESTOCK", "restocked": false, "subtotalSet": {"shopMoney": {"amount": 589, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 589, "currencyCode": "ZAR"}}, "totalTaxSet": {"shopMoney": {"amount": 76.83, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 76.83, "currencyCode": "ZAR"}}}, {"id": "gid://shopify/RefundLineItem/372320501898", "lineItem": {"id": null, "title": "Product X", "quantity": 5, "originalUnitPriceSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "sku": "SKU002", "taxable": null, "taxLines": [], "totalDiscountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}, "priceSet": {"shopMoney": {"amount": 599, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 599, "currencyCode": "ZAR"}}, "quantity": 1, "restockType": "NO_RESTOCK", "restocked": false, "subtotalSet": {"shopMoney": {"amount": 589, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 589, "currencyCode": "ZAR"}}, "totalTaxSet": {"shopMoney": {"amount": 76.83, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 76.83, "currencyCode": "ZAR"}}}, {"id": "gid://shopify/RefundLineItem/372320534666", "lineItem": {"id": null, "title": "Product Y", "quantity": 5, "originalUnitPriceSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "sku": "SKU001", "taxable": null, "taxLines": [], "totalDiscountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}, "priceSet": {"shopMoney": {"amount": 399, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 399, "currencyCode": "ZAR"}}, "quantity": 1, "restockType": "NO_RESTOCK", "restocked": false, "subtotalSet": {"shopMoney": {"amount": 399, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 399, "currencyCode": "ZAR"}}, "totalTaxSet": {"shopMoney": {"amount": 52.04, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 52.04, "currencyCode": "ZAR"}}}, {"id": "gid://shopify/RefundLineItem/372320567434", "lineItem": {"id": null, "title": "Product Y", "quantity": 5, "originalUnitPriceSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "sku": "SKU001", "taxable": null, "taxLines": [], "totalDiscountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}, "priceSet": {"shopMoney": {"amount": 399, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 399, "currencyCode": "ZAR"}}, "quantity": 1, "restockType": "NO_RESTOCK", "restocked": false, "subtotalSet": {"shopMoney": {"amount": 399, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 399, "currencyCode": "ZAR"}}, "totalTaxSet": {"shopMoney": {"amount": 52.05, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 52.05, "currencyCode": "ZAR"}}}], "refundShippingLines": [], "return": {"id": "gid://shopify/Return/**********", "name": "#1187-R1", "status": "CLOSED"}, "transactions": [{"accountNumber": null, "amountSet": {"shopMoney": {"amount": 2016, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 2016, "currencyCode": "ZAR"}}, "authorizationCode": null, "createdAt": "2024-11-13T12:39:59Z", "gateway": "bogus", "id": "gid://shopify/OrderTransaction/*************", "kind": "REFUND", "parentTransaction": {"accountNumber": null, "amountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "authorizationCode": null, "createdAt": null, "gateway": null, "id": "gid://shopify/OrderTransaction/*************", "kind": null, "parentTransaction": null, "paymentDetails": {"avsResultCode": null, "bin": null, "company": null, "cvvResultCode": null, "expirationMonth": null, "expirationYear": null, "name": null, "number": null, "paymentMethodName": null}, "paymentId": null, "processedAt": null, "receiptJson": null, "status": null}, "paymentDetails": {"avsResultCode": null, "bin": "1", "company": "<PERSON><PERSON>", "cvvResultCode": null, "expirationMonth": 12, "expirationYear": 2034, "name": "Test User", "number": "•••• •••• •••• 1", "paymentMethodName": "card"}, "paymentId": "#1187.2", "processedAt": "2024-11-13T12:39:59Z", "receiptJson": "{\"paid_amount\":\"2016.00\"}", "status": "SUCCESS"}], "totalRefundedSet": {"shopMoney": {"amount": 2016, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 2016, "currencyCode": "ZAR"}}}], "returns": [{"id": "gid://shopify/Return/**********", "name": "#1187-R1", "returnLineItems": [{"id": "gid://shopify/ReturnLineItem/7782334602", "fulfillmentLineItem": {"id": null, "lineItem": {"id": null, "title": null, "quantity": null, "originalUnitPriceSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "sku": "SKU001", "taxable": null, "taxLines": [], "totalDiscountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}}}, "quantity": 1, "returnReason": "OTHER", "returnReasonNote": "RMA#5276945 Resolution: Variant exchange. Return reason: Changed my mind. Answers:  NEW > Product Y."}, {"id": "gid://shopify/ReturnLineItem/7782367370", "fulfillmentLineItem": {"id": null, "lineItem": {"id": null, "title": null, "quantity": null, "originalUnitPriceSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "sku": "SKU001", "taxable": null, "taxLines": [], "totalDiscountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}}}, "quantity": 1, "returnReason": "OTHER", "returnReasonNote": "RMA#5276945 Resolution: Variant exchange. Return reason: Changed my mind. Answers:  NEW > Product Y."}, {"id": "gid://shopify/ReturnLineItem/7782400138", "fulfillmentLineItem": {"id": null, "lineItem": {"id": null, "title": null, "quantity": null, "originalUnitPriceSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "sku": "SKU001", "taxable": null, "taxLines": [], "totalDiscountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}}}, "quantity": 1, "returnReason": "OTHER", "returnReasonNote": "RMA#5276945 Resolution: Refund to payment method. Return reason: Changed my mind. "}, {"id": "gid://shopify/ReturnLineItem/7782432906", "fulfillmentLineItem": {"id": null, "lineItem": {"id": null, "title": null, "quantity": null, "originalUnitPriceSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "sku": "SKU001", "taxable": null, "taxLines": [], "totalDiscountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}}}, "quantity": 1, "returnReason": "OTHER", "returnReasonNote": "RMA#5276945 Resolution: Refund to payment method. Return reason: Changed my mind. "}, {"id": "gid://shopify/ReturnLineItem/7782465674", "fulfillmentLineItem": {"id": null, "lineItem": {"id": null, "title": null, "quantity": null, "originalUnitPriceSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "sku": "SKU002", "taxable": null, "taxLines": [], "totalDiscountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}}}, "quantity": 1, "returnReason": "OTHER", "returnReasonNote": "RMA#5276945 Resolution: Variant exchange. Return reason: Changed my mind. Answers:  NEW > Product X."}, {"id": "gid://shopify/ReturnLineItem/7782498442", "fulfillmentLineItem": {"id": null, "lineItem": {"id": null, "title": null, "quantity": null, "originalUnitPriceSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "sku": "SKU002", "taxable": null, "taxLines": [], "totalDiscountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}}}, "quantity": 1, "returnReason": "OTHER", "returnReasonNote": "RMA#5276945 Resolution: Variant exchange. Return reason: Changed my mind. Answers:  NEW > Product X."}, {"id": "gid://shopify/ReturnLineItem/7782531210", "fulfillmentLineItem": {"id": null, "lineItem": {"id": null, "title": null, "quantity": null, "originalUnitPriceSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "sku": "SKU002", "taxable": null, "taxLines": [], "totalDiscountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}}}, "quantity": 1, "returnReason": "OTHER", "returnReasonNote": "RMA#5276945 Resolution: Refund to payment method. Return reason: Changed my mind. "}, {"id": "gid://shopify/ReturnLineItem/7782563978", "fulfillmentLineItem": {"id": null, "lineItem": {"id": null, "title": null, "quantity": null, "originalUnitPriceSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "sku": "SKU002", "taxable": null, "taxLines": [], "totalDiscountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}}}, "quantity": 1, "returnReason": "OTHER", "returnReasonNote": "RMA#5276945 Resolution: Refund to payment method. Return reason: Changed my mind. "}], "status": "CLOSED"}], "shippingAddress": {"address1": "Address", "address2": null, "city": "Cape Town", "company": null, "country": "South Africa", "countryCodeV2": "ZA", "firstName": "Test", "lastName": "User", "name": null, "phone": null, "province": "Western Cape", "provinceCode": "WC", "zip": "7777"}, "shippingLines": [{"title": "Standard", "code": "Standard", "originalPriceSet": {"shopMoney": {"amount": 50, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 50, "currencyCode": "ZAR"}}, "taxLines": [], "discountedPriceSet": {"shopMoney": {"amount": 50, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 50, "currencyCode": "ZAR"}}}], "suggestedRefund": {"amountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "refundLineItems": [], "shipping": {"amountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "maximumRefundableSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}}, "suggestedTransactions": []}, "taxesIncluded": true, "taxLines": [{"priceSet": {"shopMoney": {"amount": 644.35, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 644.35, "currencyCode": "ZAR"}}, "rate": 0.15, "title": "VAT"}], "totalDiscountsSet": {"shopMoney": {"amount": 50, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 50, "currencyCode": "ZAR"}}, "fulfillmentOrders": [{"id": "gid://shopify/FulfillmentOrder/6853810716810", "status": "CLOSED", "supportedActions": [], "fulfillments": [{"id": "gid://shopify/Fulfillment/4779741544586", "status": "SUCCESS", "trackingInfo": []}], "lineItems": [{"id": "gid://shopify/FulfillmentOrderLineItem/**************", "remainingQuantity": 0, "sku": "SKU001", "totalQuantity": 5}, {"id": "gid://shopify/FulfillmentOrderLineItem/**************", "remainingQuantity": 0, "sku": "SKU002", "totalQuantity": 5}]}], "totalRefundedSet": {"shopMoney": {"amount": 2016, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 2016, "currencyCode": "ZAR"}}, "totalRefundedShippingSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}, "transactions": [{"accountNumber": null, "amountSet": {"shopMoney": {"amount": 4990, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 4990, "currencyCode": "ZAR"}}, "authorizationCode": "53433", "createdAt": "2024-11-13T12:32:45Z", "gateway": "bogus", "id": "gid://shopify/OrderTransaction/*************", "kind": "SALE", "parentTransaction": null, "paymentDetails": {"avsResultCode": null, "bin": "1", "company": "<PERSON><PERSON>", "cvvResultCode": null, "expirationMonth": 12, "expirationYear": 2034, "name": "Test User", "number": "•••• •••• •••• 1", "paymentMethodName": "card"}, "paymentId": "rLzRIHFCsN3vyqCz6lltYzGat", "processedAt": "2024-11-13T12:32:45Z", "receiptJson": "{\"paid_amount\":\"4990.00\"}", "status": "SUCCESS"}, {"accountNumber": null, "amountSet": {"shopMoney": {"amount": 2016, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 2016, "currencyCode": "ZAR"}}, "authorizationCode": null, "createdAt": "2024-11-13T12:39:59Z", "gateway": "bogus", "id": "gid://shopify/OrderTransaction/*************", "kind": "REFUND", "parentTransaction": {"accountNumber": null, "amountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "authorizationCode": null, "createdAt": null, "gateway": null, "id": "gid://shopify/OrderTransaction/*************", "kind": null, "parentTransaction": null, "paymentDetails": {"avsResultCode": null, "bin": null, "company": null, "cvvResultCode": null, "expirationMonth": null, "expirationYear": null, "name": null, "number": null, "paymentMethodName": null}, "paymentId": null, "processedAt": null, "receiptJson": null, "status": null}, "paymentDetails": {"avsResultCode": null, "bin": "1", "company": "<PERSON><PERSON>", "cvvResultCode": null, "expirationMonth": 12, "expirationYear": 2034, "name": "Test User", "number": "•••• •••• •••• 1", "paymentMethodName": "card"}, "paymentId": "#1187.2", "processedAt": "2024-11-13T12:39:59Z", "receiptJson": "{\"paid_amount\":\"2016.00\"}", "status": "SUCCESS"}]}