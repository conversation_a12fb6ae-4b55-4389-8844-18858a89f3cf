{"id": 5803429429436, "adminGraphqlApiId": "gid://shopify/Order/5803429429436", "appId": 1354745, "browserIp": "**************", "buyerAcceptsMarketing": false, "cancelReason": null, "cancelledAt": null, "cartToken": null, "checkoutId": 37364491550908, "checkoutToken": "546b5651baa0229dacde0e3df3c60263", "clientDetails": {"acceptLanguage": null, "browserHeight": null, "browserIp": "**************", "browserWidth": null, "sessionHash": null, "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********"}, "closedAt": null, "company": null, "confirmationNumber": "CDV2CNIHJ", "confirmed": true, "contactEmail": "<EMAIL>", "createdAt": "2025-04-14T04:25:49-04:00", "currency": "ZAR", "currentShippingPriceSet": {"shopMoney": {"amount": "60.00", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "60.00", "currencyCode": "ZAR"}}, "currentSubtotalPrice": "779.00", "currentSubtotalPriceSet": {"shopMoney": {"amount": "779.00", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "779.00", "currencyCode": "ZAR"}}, "currentTotalAdditionalFeesSet": null, "currentTotalDiscounts": "0.00", "currentTotalDiscountsSet": {"shopMoney": {"amount": "0.00", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "0.00", "currencyCode": "ZAR"}}, "currentTotalDutiesSet": null, "currentTotalPrice": "839.00", "currentTotalPriceSet": {"shopMoney": {"amount": "839.00", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "839.00", "currencyCode": "ZAR"}}, "currentTotalTax": "109.43", "currentTotalTaxSet": {"shopMoney": {"amount": "109.43", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "109.43", "currencyCode": "ZAR"}}, "customerLocale": "en", "deviceId": null, "discountCodes": [], "dutiesIncluded": false, "email": "<EMAIL>", "estimatedTaxes": false, "financialStatus": "paid", "fulfillmentStatus": "fulfilled", "landingSite": null, "landingSiteRef": null, "locationId": null, "merchantBusinessEntityId": "MTY3ODEzMTQ2ODEy", "merchantOfRecordAppId": null, "name": "LSSA-QA-1071", "note": "2025-04-14 08:28 UTC\nReturnGO, RMA6814965:\nRequest SUBMITTED\n\n2025-04-14 08:28 UTC\nReturnGO, RMA6814965:\nRequest APPROVED\nBy Nahum Delport", "noteAttributes": [], "number": 71, "orderNumber": 1071, "orderStatusUrl": "https://levis-sa-development.myshopify.com/***********/orders/33917b023996ca2efefd3408b7a49aa5/authenticate?key=46289399e85d9185d71f101ad8abb59c", "originalTotalAdditionalFeesSet": null, "originalTotalDutiesSet": null, "paymentGatewayNames": ["manual"], "phone": "+***********", "poNumber": null, "presentmentCurrency": "ZAR", "processedAt": "2025-04-14T04:25:48-04:00", "reference": null, "referringSite": null, "sourceIdentifier": null, "sourceName": "shopify_draft_order", "sourceUrl": null, "subtotalPrice": "779.00", "subtotalPriceSet": {"shopMoney": {"amount": "779.00", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "779.00", "currencyCode": "ZAR"}}, "tags": "Return requested via ReturnGO", "taxExempt": false, "taxLines": [{"price": "109.43", "rate": 0.15, "title": "VAT", "priceSet": {"shopMoney": {"amount": "109.43", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "109.43", "currencyCode": "ZAR"}}, "channelLiable": false}], "taxesIncluded": true, "test": false, "token": "33917b023996ca2efefd3408b7a49aa5", "totalCashRoundingPaymentAdjustmentSet": {"shopMoney": {"amount": "0.00", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "0.00", "currencyCode": "ZAR"}}, "totalCashRoundingRefundAdjustmentSet": {"shopMoney": {"amount": "0.00", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "0.00", "currencyCode": "ZAR"}}, "totalDiscounts": "0.00", "totalDiscountsSet": {"shopMoney": {"amount": "0.00", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "0.00", "currencyCode": "ZAR"}}, "totalLineItemsPrice": "779.00", "totalLineItemsPriceSet": {"shopMoney": {"amount": "779.00", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "779.00", "currencyCode": "ZAR"}}, "totalOutstanding": "0.00", "totalPrice": "839.00", "totalPriceSet": {"shopMoney": {"amount": "839.00", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "839.00", "currencyCode": "ZAR"}}, "totalShippingPriceSet": {"shopMoney": {"amount": "60.00", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "60.00", "currencyCode": "ZAR"}}, "totalTax": "109.43", "totalTaxSet": {"shopMoney": {"amount": "109.43", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "109.43", "currencyCode": "ZAR"}}, "totalTipReceived": "0.00", "totalWeight": 1, "updatedAt": "2025-04-14T04:28:51-04:00", "userId": 89654362300, "billingAddress": {"firstName": "Lit", "address1": "Lente<PERSON>ur", "phone": "+27 65 111 1111", "city": "Cape Town", "zip": "7786", "province": "Western Cape", "country": "South Africa", "lastName": "<PERSON>", "address2": "Test Apartment", "company": "Test Company", "latitude": -34.0283893, "longitude": 18.6145938, "name": "<PERSON><PERSON>", "countryCode": "ZA", "provinceCode": "WC"}, "customer": {"id": 8373954969788, "email": "<EMAIL>", "createdAt": "2025-02-25T04:44:33-05:00", "updatedAt": "2025-04-14T04:25:49-04:00", "firstName": "Lit", "lastName": "<PERSON>", "state": "disabled", "note": null, "verifiedEmail": true, "multipassIdentifier": null, "taxExempt": false, "phone": "+***********", "currency": "ZAR", "taxExemptions": [], "adminGraphqlApiId": "gid://shopify/Customer/8373954969788", "defaultAddress": {"id": 9012195950780, "customerId": 8373954969788, "firstName": "Lit", "lastName": "<PERSON>", "company": "Test Company", "address1": "Lente<PERSON>ur", "address2": "Test Apartment", "city": "Cape Town", "province": "Western Cape", "country": "South Africa", "zip": "7786", "phone": "+27 65 111 1111", "name": "<PERSON><PERSON>", "provinceCode": "WC", "countryCode": "ZA", "countryName": "South Africa", "default": true}}, "discountApplications": [], "fulfillments": [{"id": 5315764224188, "adminGraphqlApiId": "gid://shopify/Fulfillment/5315764224188", "createdAt": "2025-04-14T04:26:13-04:00", "locationId": 74621059260, "name": "LSSA-QA-1071.1", "orderId": 5803429429436, "originAddress": [], "receipt": [], "service": "manual", "shipmentStatus": null, "status": "success", "trackingCompany": null, "trackingNumber": null, "trackingNumbers": [], "trackingUrl": null, "trackingUrls": [], "updatedAt": "2025-04-14T04:26:13-04:00", "lineItems": [{"id": 14003337330876, "adminGraphqlApiId": "gid://shopify/LineItem/14003337330876", "attributedStaffs": [], "currentQuantity": 1, "fulfillableQuantity": 0, "fulfillmentService": "manual", "fulfillmentStatus": "fulfilled", "giftCard": false, "grams": 1, "name": "501  ORIGINAL ZA ON THE BORDERLINE - 36 / 30 / Dark Indigo - Worn In", "price": "779.00", "priceSet": {"shopMoney": {"amount": "779.00", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "779.00", "currencyCode": "ZAR"}}, "productExists": true, "productId": 8683800199356, "properties": [], "quantity": 1, "requiresShipping": true, "sku": "A544700183630", "taxable": true, "title": "501  ORIGINAL ZA ON THE BORDERLINE", "totalDiscount": "0.00", "totalDiscountSet": {"shopMoney": {"amount": "0.00", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "0.00", "currencyCode": "ZAR"}}, "variantId": **************, "variantInventoryManagement": "shopify", "variantTitle": "36 / 30 / Dark Indigo - Worn In", "vendor": "Levi’s ®", "taxLines": [{"channelLiable": false, "price": "101.60", "priceSet": {"shopMoney": {"amount": "101.60", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "101.60", "currencyCode": "ZAR"}}, "rate": 0.15, "title": "VAT"}], "duties": [], "discountAllocations": []}]}], "lineItems": [{"id": 14003337330876, "adminGraphqlApiId": "gid://shopify/LineItem/14003337330876", "attributedStaffs": [], "currentQuantity": 1, "fulfillableQuantity": 0, "fulfillmentService": "manual", "fulfillmentStatus": "fulfilled", "giftCard": false, "grams": 1, "name": "501  ORIGINAL ZA ON THE BORDERLINE - 36 / 30 / Dark Indigo - Worn In", "price": "779.00", "priceSet": {"shopMoney": {"amount": "779.00", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "779.00", "currencyCode": "ZAR"}}, "productExists": true, "productId": 8683800199356, "properties": [], "quantity": 1, "requiresShipping": true, "salesLineItemGroupId": null, "sku": "A544700183630", "taxable": true, "title": "501  ORIGINAL ZA ON THE BORDERLINE", "totalDiscount": "0.00", "totalDiscountSet": {"shopMoney": {"amount": "0.00", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "0.00", "currencyCode": "ZAR"}}, "variantId": **************, "variantInventoryManagement": "shopify", "variantTitle": "36 / 30 / Dark Indigo - Worn In", "vendor": "Levi’s ®", "taxLines": [{"channelLiable": false, "price": "101.60", "priceSet": {"shopMoney": {"amount": "101.60", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "101.60", "currencyCode": "ZAR"}}, "rate": 0.15, "title": "VAT"}], "duties": [], "discountAllocations": []}], "paymentTerms": null, "refunds": [], "shippingAddress": {"firstName": "Lit", "address1": "Lente<PERSON>ur", "phone": "+27 65 111 1111", "city": "Cape Town", "zip": "7786", "province": "Western Cape", "country": "South Africa", "lastName": "<PERSON>", "address2": "Test Apartment", "company": "Test Company", "latitude": -34.0283893, "longitude": 18.6145938, "name": "<PERSON><PERSON>", "countryCode": "ZA", "provinceCode": "WC"}, "shippingLines": [{"id": 4827259437244, "carrierIdentifier": null, "code": "Standard Shipping", "currentDiscountedPriceSet": {"shopMoney": {"amount": "60.00", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "60.00", "currencyCode": "ZAR"}}, "discountedPrice": "60.00", "discountedPriceSet": {"shopMoney": {"amount": "60.00", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "60.00", "currencyCode": "ZAR"}}, "isRemoved": false, "phone": null, "price": "60.00", "priceSet": {"shopMoney": {"amount": "60.00", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "60.00", "currencyCode": "ZAR"}}, "requestedFulfillmentServiceId": null, "source": "shopify", "title": "Standard Shipping", "taxLines": [{"channelLiable": false, "price": "7.83", "priceSet": {"shopMoney": {"amount": "7.83", "currencyCode": "ZAR"}, "presentmentMoney": {"amount": "7.83", "currencyCode": "ZAR"}}, "rate": 0.15, "title": "VAT"}], "discountAllocations": []}], "returns": [{"id": "gid://shopify/Return/5853708426", "name": "LSSA-QA-1071-R1", "returnLineItems": [{"id": "gid://shopify/Return/5853708426", "quantity": 1, "returnReason": "OTHER", "returnReasonNote": "RMA#5331615 Resolution: Refund to payment method. Return reason: Changed my mind. "}]}]}