{"billing_address": {"id": null, "created": null, "modified": null, "address1": "Lente<PERSON>ur", "address2": "Test Apartment", "city": "Cape Town", "company": "Test Company", "country": "South Africa", "country_code": "ZA", "first_name": "Lit", "last_name": "<PERSON>", "phone": "+27 65 111 1111", "province": "Western Cape", "province_code": "WC", "zip": "7786"}, "customer": {"channel_customer_code": "8373954969788", "accepts_marketing": false, "email": "<EMAIL>", "first_name": "Lit", "last_name": "<PERSON>"}, "instruction": null, "line_items": [{"barcode": null, "grams": null, "price": 677.39130434783, "price_incl": 779, "qty": 1, "sku": "A544700182832", "tax_lines": [{"code": "taxed", "title": "VAT", "price": 0, "rate": 15}], "title": "501  ORIGINAL ZA ON THE BORDERLINE", "total_discount": 677.39130434783, "total_discount_incl": 779}, {"barcode": null, "grams": null, "price": 677.39130434783, "price_incl": 779, "qty": 1, "sku": "A544700183032", "tax_lines": [{"code": "taxed", "title": "VAT", "price": 0, "rate": 15}], "title": "501  ORIGINAL ZA ON THE BORDERLINE", "total_discount": 677.39130434783, "total_discount_incl": 779}, {"barcode": null, "grams": null, "price": 677.39130434783, "price_incl": 779, "qty": 1, "sku": "A544700183232", "tax_lines": [{"code": "taxed", "title": "VAT", "price": 0, "rate": 15}], "title": "501  ORIGINAL ZA ON THE BORDERLINE", "total_discount": 677.39130434783, "total_discount_incl": 779}], "params": [], "shipping_address": {"id": null, "created": null, "modified": null, "address1": "Lente<PERSON>ur", "address2": "Test Apartment", "city": "Cape Town", "company": "Test Company", "country": "South Africa", "country_code": "ZA", "first_name": "Lit", "last_name": "<PERSON>", "phone": "+27 65 111 1111", "province": "Western Cape", "province_code": "WC", "zip": "7786"}, "shipping_lines": [], "transactions": [], "channel_id": 2, "channel_order_code": "LSSA-QA-1070", "has_taxes_incl": true, "meta": [], "notes": "Exchanged via ReturnGO RMA6790076 Original order: LSSA-QA-1069\n\n2025-04-16 13:24 UTC\nReturnGO, RMA6841705:\nRequest SUBMITTED, original order LSSA-QA-1069\n\n2025-04-16 13:24 UTC\nReturnGO, RMA6841705:\nRequest APPROVED\nBy Nahum Delport\n\n2025-04-16 13:24 UTC\nReturnGO, RMA6841705:\nShipment RECEIVED\nBy Nahum Delport\n\n2025-04-16 13:24 UTC\nReturnGO, RMA6841705:\nRequest DONE\nBy Nahum Delport", "refunds": [], "ordered_date": "2025-04-11 11:01:56", "total_discount": 0, "total_discount_incl": 0}