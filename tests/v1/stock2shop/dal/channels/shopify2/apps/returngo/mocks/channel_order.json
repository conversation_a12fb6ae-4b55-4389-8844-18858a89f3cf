{"billing_address": {"id": null, "created": null, "modified": null, "address1": "Address", "address2": null, "city": "Cape Town", "company": null, "country": "South Africa", "country_code": "ZA", "first_name": "Test", "last_name": "User", "phone": "+27123456789", "province": "Western Cape", "province_code": "WC", "zip": "7777"}, "customer": {"channel_customer_code": "7136610254986", "accepts_marketing": false, "email": "<EMAIL>", "first_name": "Test", "last_name": "User"}, "instruction": "add_order", "line_items": [{"barcode": null, "grams": null, "price": 86.913043478261, "price_incl": 99.95, "qty": 10, "sku": "SKU003", "tax_lines": [{"code": "taxed", "title": "VAT", "price": 130.37, "rate": 15}], "title": "Product A", "total_discount": 0, "total_discount_incl": 0}], "params": [], "shipping_address": {"id": null, "created": null, "modified": null, "address1": "Address", "address2": null, "city": "Cape Town", "company": null, "country": "South Africa", "country_code": "ZA", "first_name": "Test", "last_name": "User", "phone": "+27123456789", "province": "Western Cape", "province_code": "WC", "zip": "7777"}, "shipping_lines": [{"price": 50, "price_incl": 50, "tax_lines": [], "title": "Standard", "total_discount": 0, "total_discount_incl": 0}], "transactions": [{"amount": 1049.5, "amount_calculation_method": null, "auth_code": "53433", "card_bin": "1", "card_last_4_digits": "•••• •••• •••• 1", "channel_transaction_code": "6767986835594", "completed_date": "2024-11-06 15:19:51", "created": null, "gateway": "bogus", "is_store_credit": false, "kind": "debit", "modified": null, "origin_connector_id": 2, "origin_connector_kind": "channel", "order_id": null, "payment_code": "rEiL6t8Hzo3dXeWMJZvinjycS", "payment_method": "card", "refund_identifier": null, "rrn": null, "stan": null, "state": "complete", "status": "SUCCESS", "transaction_identifier": null}], "channel_id": 2, "channel_order_code": "#1157", "has_taxes_incl": true, "meta": [], "notes": "2024-11-06 15:23 UTC\nReturnGO, RMA5218219:\nRequest SUBMITTED\n\n2024-11-06 15:23 UTC\nReturnGO, RMA5218219:\nRequest APPROVED\nBy #272013\n\n2024-11-06 15:23 UTC\nReturnGO, RMA5218219:\nExchange order #1158 released\nBy #272013\n\n2024-11-06 15:24 UTC\nReturnGO, RMA5218219:\nCredited R 199.90\nBy #272013\n\n2024-11-06 15:25 UTC\nReturnGO, RMA5218219:\nREFUNDED R 199.90\nBy #272013\n\n2024-11-06 15:38 UTC\nReturnGO, RMA5218219:\nShipment RECEIVED\nBy #272013\n\n2024-11-06 15:38 UTC\nReturnGO, RMA5218219:\nRequest DONE\nBy #272013", "refunds": [{"amount_incl": 199.9, "channel_id": 2, "channel_refund_code": "873002533002", "channel_refund_state": "complete", "channel_created_date": "2024-11-06 15:25:36", "client_id": null, "created": null, "fulfillment_created_date": null, "fulfillment_service_id": null, "fulfillment_refund_code": null, "fulfillment_refund_state": null, "line_items": [{"client_id": null, "code": "item", "created": null, "meta": [{"key": "restocked", "refund_item_identifier": null, "value": "false"}, {"key": "restockType", "refund_item_identifier": null, "value": "NO_RESTOCK"}], "modified": null, "refund_item_identifier": null, "refund_identifier": null, "sku": "SKU003", "title": "Product A", "qty": 1, "unit_amount_incl": 99.95, "subtotal_incl": 99.95, "subtotal_tax": 13.04}, {"client_id": null, "code": "item", "created": null, "meta": [{"key": "restocked", "refund_item_identifier": null, "value": "false"}, {"key": "restockType", "refund_item_identifier": null, "value": "NO_RESTOCK"}], "modified": null, "refund_item_identifier": null, "refund_identifier": null, "sku": "SKU003", "title": "Product A", "qty": 1, "unit_amount_incl": 99.95, "subtotal_incl": 99.95, "subtotal_tax": 13.04}], "meta": [], "modified": null, "notes": null, "order_id": null, "origin_connector_kind": null, "origin_connector_id": null, "refund_identifier": null, "source_id": null, "source_refund_code": null, "source_refund_state": null, "source_created_date": null, "transactions": [{"amount": 199.9, "amount_calculation_method": null, "auth_code": null, "card_bin": "1", "card_last_4_digits": "•••• •••• •••• 1", "channel_transaction_code": "6767992733834", "completed_date": "2024-11-06 15:25:35", "created": null, "gateway": "bogus", "is_store_credit": false, "kind": "credit", "modified": null, "origin_connector_id": null, "origin_connector_kind": null, "order_id": null, "payment_code": "#1157.2", "payment_method": "card", "refund_identifier": null, "rrn": null, "stan": null, "state": "complete", "status": "SUCCESS", "transaction_identifier": null}]}], "ordered_date": "2024-11-06 15:19:52", "total_discount": 0, "total_discount_incl": 0}