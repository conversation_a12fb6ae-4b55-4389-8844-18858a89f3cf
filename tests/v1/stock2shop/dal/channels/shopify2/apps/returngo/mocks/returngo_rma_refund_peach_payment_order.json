{"rmaSummary": {"rmaId": "6769990", "status": "Done", "order_name": "LSSA-46915", "events": [{"eventName": "RMA_CREATED", "eventDate": "2025-04-09T16:44:56+00:00", "triggeredVia": "Return Portal", "storeLocationId": "online", "userDetails": {"userFirstName": null, "userLastName": null, "userEmailAddress": null, "userRole": null}}, {"eventName": "RMA_APPROVED", "eventDate": "2025-04-10T12:03:03+00:00", "triggeredVia": "Dashboard", "storeLocationId": "online", "userDetails": {"userFirstName": null, "userLastName": null, "userEmailAddress": null, "userRole": null}}]}, "orderDetails": {"orderId": "6174750310489", "orderName": null}, "shipments": [{"shipmentId": "6553789", "status": "Received", "carrierName": "CourierGuy", "trackingNumber": "OPT-691477739", "trackingURL": "https://optimise.parcelninja.com/shipment/track/OPT-691477739", "labelURL": "https://sellerportal.dpworld.com/api/file-download?link=null", "customerAddress": {"name": "<PERSON>dis<PERSON><PERSON>", "street1": "R708 & Tandjiesberg roads", "street2": "Farm Matoane", "city": "Clocolan", "state": "FS", "zip": "9735", "country": "ZA", "phone": "0609199106", "email": "<EMAIL>"}, "returnAddress": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "street1": "Ashworth Logistics Park, Building C", "street2": "18 Laneshaw Street", "city": "Johannesburg", "state": "Gauteng", "zip": "1609", "country": "ZA", "phone": "", "email": "<EMAIL>"}, "isOutbound": false}], "items": [{"shopLineItemId": "14842428883033", "shopProductId": "7937912668249", "variantId": "42272229032025", "returnedItemId": "30402981", "fullName": "LEVI'S® WOMEN'S BAGGY JUMPSUIT - MED INDIGO - WORN IN - L / - / Med Indigo - Worn In", "productName": "LEVI'S® WOMEN'S BAGGY JUMPSUIT - MED INDIGO - WORN IN", "variantName": "L / - / Med Indigo - Worn In", "image": "https://cdn.shopify.com/s/files/1/0589/0873/7625/files/7851458b38b8b20d72f6410f9ced6aef_600x600.jpg?v=1743577196", "sku": "0038V0000L-", "barcode": "5401180673230", "paidPrice": {"amount": 1799, "currency": "ZAR", "shopAmount": 1799, "shopCurrency": "ZAR"}, "paidTax": {"amount": 234.65, "currency": "ZAR", "shopAmount": 234.65, "shopCurrency": "ZAR"}, "taxIncluded": true, "returnReason": "Does not fit", "resolutionName": "Variant exchange", "policyRuleName": "Variant Exchange | Non Sale Items | First Request", "isAutoApprove": false, "resolutionType": "InstantVariantExchange", "restockingFee": 0, "returnFee": 0, "shipmentFee": 0, "shipmentId": "6553789", "extendedReason": " NEW > M / - / Med Indigo - Worn In <br />\n > Too large / loose <br />\n > Yes", "returnMethod": {"type": "Ship with any carrier", "name": "SFS CourierGuy"}, "itemValidationStatusResponse": {"returnItemId": null, "isRestockable": false, "isReturned": false, "nonRestockableReason": null, "nonReturnedReason": null, "storeLocationId": null, "lastValidationDate": null, "lastValidatedBy": null, "thirdPartyName": null, "userDetails": {"userFirstName": null, "userLastName": null, "userEmailAddress": null, "userRole": null}}, "exchangeItems": [{"productId": null, "variantId": "42272229097561", "quantity": 1, "sku": "0038V0000M-", "itemPriceMethod": "CatalogPrice", "freeExchangePriceRange": {"min": 0, "max": 110}}]}], "comments": [{"commentType": "system", "triggeredBy": "<PERSON>", "htmlText": "Completed Shopify return LSSA-46915-R1 for order LSSA-46915", "datetime": "2025-04-24T10:35:31+00:00"}, {"commentType": "system", "triggeredBy": "Automation", "htmlText": "Request DONE", "datetime": "2025-04-24T10:35:30+00:00"}, {"commentType": "system", "triggeredBy": "<PERSON>", "htmlText": "Exchange order LSSA-47714 released (original order: LSSA-46915) ", "datetime": "2025-04-24T10:35:30+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6553789 was updated", "datetime": "2025-04-16T09:50:16+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6553789 was updated", "datetime": "2025-04-15T21:26:59+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6553789 was updated", "datetime": "2025-04-15T12:15:51+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6553789 was updated", "datetime": "2025-04-15T12:15:49+00:00"}, {"commentType": "system", "triggeredBy": "Automation", "htmlText": "Shipment <strong>RECEIVED</strong>", "datetime": "2025-04-15T12:15:48+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6553789 was updated", "datetime": "2025-04-15T05:07:48+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6553789 was updated", "datetime": "2025-04-14T12:10:49+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6553789 was updated", "datetime": "2025-04-14T11:21:51+00:00"}, {"commentType": "system", "triggeredBy": "Automation", "htmlText": "Shipment <strong>IN TRANSIT</strong>", "datetime": "2025-04-14T11:21:50+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6553789 was updated", "datetime": "2025-04-10T12:04:49+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6553789 was updated", "datetime": "2025-04-10T12:03:50+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6553789 was updated", "datetime": "2025-04-10T12:03:49+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6553789 was updated", "datetime": "2025-04-10T12:03:18+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6553789 was updated", "datetime": "2025-04-10T12:03:15+00:00"}, {"commentType": "system", "triggeredBy": "#267963", "htmlText": "Request <strong>APPROVED</strong>", "datetime": "2025-04-10T12:03:08+00:00"}, {"commentType": "system", "triggeredBy": "#267963", "htmlText": "Created & approved Shopify return LSSA-46915-R1 for order LSSA-46915", "datetime": "2025-04-10T12:03:07+00:00"}, {"commentType": "system", "triggeredBy": "", "htmlText": "Request SUBMITTED", "datetime": "2025-04-09T16:44:58+00:00"}], "exchangeOrders": [{"draftOrderId": "978533613657", "orderId": "6211533144153", "orderName": "LSSA-47714", "type": "Exchange"}], "cancellationFee": 0, "transactions": [], "createdAt": "2025-04-09T16:44:56+00:00", "lastUpdated": "2025-04-24T10:35:30+00:00"}