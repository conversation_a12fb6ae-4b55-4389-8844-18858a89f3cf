{"billingAddress": {"address1": "Burgersfort Moroke 259", "address2": "Temple of worship", "city": "Burgersfort", "company": null, "country": "South Africa", "countryCodeV2": "ZA", "firstName": "Mo<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "name": null, "phone": "0826897231", "province": "Limpopo", "provinceCode": "LP", "zip": "1154"}, "createdAt": "2025-04-13T03:48:13Z", "sourceName": "web", "sourceIdentifier": null, "customAttributes": [{"key": "analyzely_sse", "value": "{\"analyzelyClientId\":\"*********.1743911381\",\"analyzelySessionId\":\"1744515562\"}"}], "customer": {"displayName": null, "email": "<EMAIL>", "firstName": "Mo<PERSON><PERSON>", "id": "gid://shopify/Customer/8161738227801", "lastName": "<PERSON><PERSON><PERSON>", "phone": null}, "customerAcceptsMarketing": true, "discountCodes": [], "fullyPaid": true, "id": "gid://shopify/Order/6191014051929", "lineItems": [{"id": "gid://shopify/LineItem/14880935936089", "discountAllocations": [], "title": "LEVI'S® WOMEN'S 720 HIGH-RISE SUPER SKINNY JEANS - DARK INDIGO - FLAT FINISH", "quantity": 1, "originalUnitPriceSet": {"shopMoney": {"amount": 699, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 699, "currencyCode": "ZAR"}}, "sku": "A693800183432", "taxable": true, "taxLines": [{"priceSet": {"shopMoney": {"amount": 91.17, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 91.17, "currencyCode": "ZAR"}}, "rate": 0.15, "title": "VAT"}], "totalDiscountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}], "name": "LSSA-47218", "note": "2025-04-16 06:53 UTC\nReturnGO, RMA6838865:\nRequest SUBMITTED\n\n2025-04-17 07:25 UTC\nReturnGO, RMA6838865:\nRequest APPROVED\nBy #267963\n\n2025-04-25 12:25 UTC\nReturnGO, RMA6838865:\nShipment IN TRANSIT (AUTOMATIC)\n\n2025-04-30 10:46 UTC\nReturnGO, RMA6838865:\nShipment RECEIVED (AUTOMATIC)\n\n2025-05-09 15:00 UTC\nReturnGO, RMA6838865:\nCredited R699.00\nTransaction ID: 9177500\nBy <PERSON>-<PERSON><PERSON>\n\n2025-05-09 15:00 UTC\nReturnGO, RMA6838865:\nRequest DONE\nBy <PERSON><PERSON><PERSON><PERSON>", "refunds": [], "returns": [{"id": "gid://shopify/Return/18853658713", "name": "LSSA-47218-R1", "returnLineItems": [{"id": "gid://shopify/ReturnLineItem/35231793241", "quantity": 1, "returnReason": "OTHER", "returnReasonNote": "RMA#6838865 Resolution: Variant exchange. Return reason: Does not fit. Answers:  NEW > 32 / 32 / Dark Indigo - Flat Finish \n > Too large / loose \n > Yes."}], "status": "CLOSED"}], "shippingAddress": {"address1": "Burgersfort Moroke 259", "address2": "Temple of worship", "city": "Burgersfort", "company": null, "country": "South Africa", "countryCodeV2": "ZA", "firstName": "Mo<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "name": null, "phone": "0826897231", "province": "Limpopo", "provinceCode": "LP", "zip": "1154"}, "shippingLines": [{"title": "Standard Shipping", "code": "Standard Shipping", "originalPriceSet": {"shopMoney": {"amount": 60, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 60, "currencyCode": "ZAR"}}, "taxLines": [{"priceSet": {"shopMoney": {"amount": 7.83, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 7.83, "currencyCode": "ZAR"}}, "rate": 0.15, "title": "VAT"}], "discountedPriceSet": {"shopMoney": {"amount": 60, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 60, "currencyCode": "ZAR"}}}], "suggestedRefund": {"amountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "refundLineItems": [], "shipping": {"amountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "maximumRefundableSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}}, "suggestedTransactions": []}, "taxesIncluded": true, "taxLines": [{"priceSet": {"shopMoney": {"amount": 99, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 99, "currencyCode": "ZAR"}}, "rate": 0.15, "title": "VAT"}], "totalDiscountsSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}, "fulfillmentOrders": [{"id": "gid://shopify/FulfillmentOrder/7240967782489", "status": "CLOSED", "supportedActions": [], "fulfillments": [{"id": "gid://shopify/Fulfillment/5632815136857", "status": "SUCCESS", "trackingInfo": [{"company": "CourierGuy", "number": "PNJ63055847", "url": "https://store.parcelninja.com/tracking.aspx?WaybillNo=PNJ63055847"}]}], "lineItems": [{"id": "gid://shopify/FulfillmentOrderLineItem/**************", "remainingQuantity": 0, "sku": "A693800183432", "totalQuantity": 1}]}], "totalRefundedSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}, "totalRefundedShippingSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}, "transactions": [{"accountNumber": null, "amountSet": {"shopMoney": {"amount": 759, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 759, "currencyCode": "ZAR"}}, "authorizationCode": null, "createdAt": "2025-04-13T03:46:26Z", "gateway": "Peach Payments", "id": "gid://shopify/OrderTransaction/*************", "kind": "SALE", "parentTransaction": null, "paymentDetails": {"avsResultCode": null, "bin": null, "company": null, "cvvResultCode": null, "expirationMonth": null, "expirationYear": null, "name": null, "number": null, "paymentMethodName": null}, "paymentId": "rPeOMuBXmdAVv2hEeVY3RWLHe", "processedAt": "2025-04-13T03:46:26Z", "receiptJson": "{\"payment_id\":\"rPeOMuBXmdAVv2hEeVY3RWLHe\"}", "status": "SUCCESS"}], "discountApplications": []}