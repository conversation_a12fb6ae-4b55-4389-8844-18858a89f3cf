{"rmaSummary": {"rmaId": "7054664", "status": "Done", "order_name": "LSSA-47714", "events": [{"eventName": "RMA_CREATED", "eventDate": "2025-05-08T10:05:34+00:00", "triggeredVia": "Return Portal", "storeLocationId": "online", "userDetails": {"userFirstName": null, "userLastName": null, "userEmailAddress": null, "userRole": null}}, {"eventName": "RMA_APPROVED", "eventDate": "2025-05-09T08:22:48+00:00", "triggeredVia": "Dashboard", "storeLocationId": "online", "userDetails": {"userFirstName": null, "userLastName": null, "userEmailAddress": null, "userRole": null}}]}, "orderDetails": {"orderId": "6211533144153", "orderName": null}, "shipments": [{"shipmentId": "6831723", "status": "Received", "carrierName": "CourierGuy", "trackingNumber": "OPT-296825817", "trackingURL": "https://optimise.parcelninja.com/shipment/track/OPT-296825817", "labelURL": "https://sellerportal.dpworld.com/api/file-download?link=null", "customerAddress": {"name": "<PERSON>dis<PERSON><PERSON>", "street1": "R708 & Tandjiesberg roads", "street2": "Farm Matoane", "city": "Clocolan", "state": "FS", "zip": "9735", "country": "ZA", "phone": "+27609199106", "email": "<EMAIL>"}, "returnAddress": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "street1": "Ashworth Logistics Park, Building C", "street2": "18 Laneshaw Street", "city": "Johannesburg", "state": "Gauteng", "zip": "1609", "country": "ZA", "phone": "", "email": "<EMAIL>"}, "isOutbound": false}], "items": [{"shopLineItemId": "14919401603161", "shopProductId": "7937912668249", "variantId": "42272229097561", "returnedItemId": "********", "fullName": "LEVI'S® WOMEN'S BAGGY JUMPSUIT - MED INDIGO - WORN IN - M / - / Med Indigo - Worn In", "productName": "LEVI'S® WOMEN'S BAGGY JUMPSUIT - MED INDIGO - WORN IN", "variantName": "M / - / Med Indigo - Worn In", "image": "https://cdn.shopify.com/s/files/1/0589/0873/7625/files/7851458b38b8b20d72f6410f9ced6aef_600x600.jpg?v=1743577196", "sku": "0038V0000M-", "barcode": "5401180673247", "paidPrice": {"amount": 1799, "currency": "ZAR", "shopAmount": 1799, "shopCurrency": "ZAR"}, "paidTax": {"amount": 0, "currency": "ZAR", "shopAmount": 0, "shopCurrency": "ZAR"}, "taxIncluded": true, "returnReason": "Does not fit", "resolutionName": "Refund to payment method", "policyRuleName": "Refund to Payment Method", "isAutoApprove": false, "resolutionType": "RefundToPaymentMethod", "restockingFee": 0, "returnFee": 0, "shipmentFee": 0, "shipmentId": "6831723", "extendedReason": " > Too large / loose <br />\n > Yes", "returnMethod": {"type": "Ship with any carrier", "name": "SFS CourierGuy"}, "itemValidationStatusResponse": {"returnItemId": null, "isRestockable": false, "isReturned": false, "nonRestockableReason": null, "nonReturnedReason": null, "storeLocationId": null, "lastValidationDate": null, "lastValidatedBy": null, "thirdPartyName": null, "userDetails": {"userFirstName": null, "userLastName": null, "userEmailAddress": null, "userRole": null}}, "exchangeItems": []}], "comments": [{"commentType": "system", "triggeredBy": "<PERSON><PERSON><PERSON><PERSON>", "htmlText": "Completed Shopify return LSSA-47714-R1 for order LSSA-47714", "datetime": "2025-05-15T10:14:04+00:00"}, {"commentType": "system", "triggeredBy": "<PERSON><PERSON><PERSON><PERSON>", "htmlText": "Request DONE", "datetime": "2025-05-15T10:14:04+00:00"}, {"commentType": "system", "triggeredBy": "<PERSON><PERSON><PERSON><PERSON>", "htmlText": "Total refund amount: R1,799.00<br>Transaction ID: 9271743", "datetime": "2025-05-15T10:13:52+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6831723 was updated", "datetime": "2025-05-14T19:04:15+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6831723 was updated", "datetime": "2025-05-14T12:03:21+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6831723 was updated", "datetime": "2025-05-14T10:47:50+00:00"}, {"commentType": "system", "triggeredBy": "Automation", "htmlText": "Shipment <strong>RECEIVED</strong>", "datetime": "2025-05-14T10:47:50+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6831723 was updated", "datetime": "2025-05-14T05:31:56+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6831723 was updated", "datetime": "2025-05-12T13:31:51+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6831723 was updated", "datetime": "2025-05-12T12:22:58+00:00"}, {"commentType": "system", "triggeredBy": "Automation", "htmlText": "Shipment <strong>IN TRANSIT</strong>", "datetime": "2025-05-12T12:22:57+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6831723 was updated", "datetime": "2025-05-09T08:26:51+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6831723 was updated", "datetime": "2025-05-09T08:23:50+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6831723 was updated", "datetime": "2025-05-09T08:23:49+00:00"}, {"commentType": "system", "triggeredBy": "ReturnGO API", "htmlText": "Shipment with ID: 6831723 was updated", "datetime": "2025-05-09T08:23:00+00:00"}, {"commentType": "system", "triggeredBy": "#267963", "htmlText": "Request <strong>APPROVED</strong>", "datetime": "2025-05-09T08:22:51+00:00"}, {"commentType": "system", "triggeredBy": "#267963", "htmlText": "Created & approved Shopify return LSSA-47714-R1 for order LSSA-47714", "datetime": "2025-05-09T08:22:50+00:00"}, {"commentType": "system", "triggeredBy": "", "htmlText": "Request SUBMITTED, original order LSSA-46915", "datetime": "2025-05-08T10:05:35+00:00"}], "exchangeOrders": [], "cancellationFee": 0, "transactions": [{"id": "9271743", "type": "RefundToPaymentMethod", "externalReferenceId": "************", "externalReferenceType": "PlatformRefundId", "amount": {"amount": 1799, "currency": "ZAR", "shopAmount": 1799, "shopCurrency": "ZAR"}, "status": "Requested", "processor": "Shopify", "bankAccountDetails": {"iban": null, "firstName": null, "lastName": null, "branchName": null, "branchAddress": null, "comment": null}, "returnItemIds": ["********"], "createdAt": "2025-05-15T10:13:52+00:00", "lastUpdated": "2025-05-15T10:13:52+00:00"}], "createdAt": "2025-05-08T10:05:34+00:00", "lastUpdated": "2025-05-15T10:14:03+00:00"}