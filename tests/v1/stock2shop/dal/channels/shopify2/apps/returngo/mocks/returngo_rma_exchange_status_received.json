{"rmaSummary": {"rmaId": "6814965", "status": "Approved", "order_name": "LSSA-QA-1071", "events": [{"eventName": "RMA_CREATED", "eventDate": "2025-04-14T08:28:21+00:00", "triggeredVia": "Return Portal", "storeLocationId": "online"}, {"eventName": "RMA_APPROVED", "eventDate": "2025-04-14T08:28:47+00:00", "triggeredVia": "Dashboard", "storeLocationId": "online"}]}, "orderDetails": {"orderId": "5803429429436", "order_name": "LSSA-QA-1071"}, "shipments": [{"shipmentId": "6597822", "status": "Pending", "customerAddress": {"name": "<PERSON><PERSON>", "street1": "Lente<PERSON>ur", "street2": "Test Apartment", "city": "Cape Town", "state": "WC", "zip": "7786", "country": "ZA", "phone": "+27 65 111 1111", "email": "<EMAIL>"}, "returnAddress": {"name": "", "street1": "", "street2": "", "city": "", "zip": "", "country": "IN", "phone": "", "email": "<EMAIL>"}, "isOutbound": false}], "items": [{"shopLineItemId": "14003337330876", "shopProductId": "8683800199356", "variantId": "44446123524284", "returnedItemId": "30640336", "fullName": "501  ORIGINAL ZA ON THE BORDERLINE - 36 / 30 / Dark Indigo - Worn In", "productName": "501  ORIGINAL ZA ON THE BORDERLINE", "variantName": "36 / 30 / Dark Indigo - Worn In", "image": "https://cdn.shopify.com/s/files/1/0678/1314/6812/files/6a02ccaf466410a67bd53b37a81e2423_9fc15730-ee87-4e0d-9cf5-9e383af5f2a0_600x600.jpg?v=1736761404", "sku": "A544700183630", "barcode": "5401180362752", "paidPrice": {"amount": 779, "currency": "ZAR", "shopAmount": 779, "shopCurrency": "ZAR"}, "paidTax": {"amount": 101.6, "currency": "ZAR", "shopAmount": 101.6, "shopCurrency": "ZAR"}, "taxIncluded": true, "returnReason": "Does not fit", "resolutionName": "Variant exchange", "policyRuleName": "Variant Exchange | Sale Items | First Request", "isAutoApprove": 0, "resolutionType": "InstantVariantExchange", "restockingFee": 0, "returnFee": 0, "shipmentFee": 0, "shipmentId": "6597822", "extendedReason": " NEW > 30 / 32 / Dark Indigo - Worn In <br />\n > Other", "returnMethod": {"type": "Ship with any carrier", "name": "Ship with any carrier"}, "itemValidationStatusResponse": {"isRestockable": false, "isReturned": false, "nonRestockableReason": null, "nonReturnedReason": null, "storeLocationId": null, "lastValidationDate": null, "lastValidatedBy": null, "thirdPartyName": null, "userDetails": null}, "exchangeItems": [{"variantId": "44446123294908", "quantity": 1, "sku": "A544700183032", "itemPriceMethod": "CatalogPriceTimeOfPurchase", "freeExchangePriceRange": {"min": 0, "max": 110}}], "followUpQuestions": [{"type": "TextArray", "name": "Why?", "answer": {"textArrayAnswer": ["Other"]}}]}], "comments": [{"commentType": "error", "triggeredBy": "Nahum Delport", "htmlText": "Release Exchange Failed: Invoice can't be <NAME_EMAIL>. Verify this email address and try again.", "datetime": "2025-04-14T08:28:57+00:00"}, {"commentType": "system", "triggeredBy": "Nahum Delport", "htmlText": "Created & approved Shopify return LSSA-QA-1071-R1 for order LSSA-QA-1071", "datetime": "2025-04-14T08:28:51+00:00"}, {"commentType": "system", "triggeredBy": "Nahum Delport", "htmlText": "Request <strong>APPROVED</strong>", "datetime": "2025-04-14T08:28:51+00:00"}, {"commentType": "system", "triggeredBy": "", "htmlText": "Request SUBMITTED", "datetime": "2025-04-14T08:28:22+00:00"}], "exchangeOrders": [{"draftOrderId": "997077582012", "orderId": "", "orderName": "#D43", "type": "Exchange"}], "cancellationFee": 0, "transactions": [{"id": 8743693, "type": "InvoicePayment", "externalReferenceId": "997077582012", "externalReferenceType": "InvoicePaymentId", "amount": {"amount": 100, "currency": "ZAR", "shopAmount": 100, "shopCurrency": "ZAR"}, "status": "Requested", "processor": "Shopify", "returnItemIds": [], "createdAt": "2025-04-14T08:28:49+00:00", "lastUpdated": "2025-04-14T08:28:49+00:00"}], "createdAt": "2025-04-14T08:28:21+00:00", "lastUpdated": "2025-04-14T08:28:51+00:00", "isOutOfPolicy": 0}