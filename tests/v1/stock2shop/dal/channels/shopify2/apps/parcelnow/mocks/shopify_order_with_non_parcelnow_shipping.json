{"billingAddress": {"address1": "1 Shipper Street", "address2": null, "city": "Cape Town", "company": null, "country": "South Africa", "countryCodeV2": "ZA", "firstName": "<PERSON>", "lastName": "Smit", "name": null, "phone": "0731234567", "province": "Western Cape", "provinceCode": "WC", "zip": "8000"}, "createdAt": "2025-06-20T08:18:37Z", "sourceName": "web", "sourceIdentifier": null, "customAttributes": [], "customer": {"displayName": null, "email": "<EMAIL>", "firstName": "<PERSON>", "id": "gid://shopify/Customer/8184874303806", "lastName": "<PERSON><PERSON><PERSON>", "phone": null}, "customerAcceptsMarketing": true, "discountCodes": [], "fullyPaid": true, "id": "gid://shopify/Order/6403587506494", "lineItems": [{"id": "gid://shopify/LineItem/15765605744958", "discountAllocations": [{"allocatedAmountSet": {"shopMoney": {"amount": 145.98, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 145.98, "currencyCode": "ZAR"}}, "discountApplication": {"allocationMethod": "EACH", "index": 0, "targetSelection": "ENTITLED"}}], "title": "The Multi-location Snowboard", "quantity": 2, "originalUnitPriceSet": {"shopMoney": {"amount": 729.95, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 729.95, "currencyCode": "ZAR"}}, "sku": "SNW-ML1", "taxable": true, "taxLines": [{"priceSet": {"shopMoney": {"amount": 171.38, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 171.38, "currencyCode": "ZAR"}}, "rate": 0.15, "title": "VAT"}], "totalDiscountSet": {"shopMoney": {"amount": 145.98, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 145.98, "currencyCode": "ZAR"}}}], "name": "CHRIS-S2S1056", "note": null, "refunds": [], "returns": [], "shippingAddress": {"address1": "1 Shipper Street", "address2": null, "city": "Cape Town", "company": null, "country": "South Africa", "countryCodeV2": "ZA", "firstName": "<PERSON>", "lastName": "Smit", "name": null, "phone": "0731234567", "province": "Western Cape", "provinceCode": "WC", "zip": "8000"}, "shippingLines": [{"title": "Free Shipping", "code": "Free Shipping", "source": "shopify", "originalPriceSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}, "taxLines": [{"priceSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}, "rate": 0.15, "title": "VAT"}], "discountedPriceSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}], "suggestedRefund": {"amountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "refundLineItems": [], "shipping": {"amountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "maximumRefundableSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}}, "suggestedTransactions": []}, "taxesIncluded": true, "taxLines": [{"priceSet": {"shopMoney": {"amount": 171.38, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 171.38, "currencyCode": "ZAR"}}, "rate": 0.15, "title": "VAT"}], "totalDiscountsSet": {"shopMoney": {"amount": 145.98, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 145.98, "currencyCode": "ZAR"}}, "fulfillmentOrders": [], "totalRefundedSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}, "totalRefundedShippingSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}, "transactions": [{"accountNumber": null, "amountSet": {"shopMoney": {"amount": 1313.92, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 1313.92, "currencyCode": "ZAR"}}, "authorizationCode": "53433", "createdAt": "2025-06-20T08:18:35Z", "gateway": "bogus", "id": "gid://shopify/OrderTransaction/*************", "kind": "SALE", "parentTransaction": null, "paymentDetails": {"avsResultCode": null, "bin": "1", "company": "<PERSON><PERSON>", "cvvResultCode": null, "expirationMonth": 12, "expirationYear": 2027, "name": "<PERSON>", "number": "•••• •••• •••• 1", "paymentMethodName": "card"}, "paymentId": "rYMK0is4ALYF16cYqQf0urN1A", "processedAt": "2025-06-20T08:18:35Z", "receiptJson": "{\"paid_amount\":\"1313.92\"}", "status": "SUCCESS"}], "discountApplications": [{"allocationMethod": "EACH", "index": 0, "targetSelection": "ENTITLED", "targetType": "LINE_ITEM", "code": null}]}