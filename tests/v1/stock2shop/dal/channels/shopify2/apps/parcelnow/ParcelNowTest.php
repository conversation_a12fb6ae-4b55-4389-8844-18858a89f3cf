<?php

namespace tests\v1\stock2shop\dal\channels\shopify2\apps\parcelnow;

use stock2shop\dal\channels\shopify2\apps\parcelnow\ParcelNow;
use stock2shop\dal\channels\shopify2\lib\models\Order;
use stock2shop\dal\channels\shopify2\Configuration;
use stock2shop\exceptions\ServerError;
use stock2shop\exceptions\UnprocessableEntity;
use stock2shop\lib\Utils;
use stock2shop\vo;
use tests;

class ParcelNowTest extends tests\TestCase
{
    const CLIENT_ID               = 1;
    const CHANNEL_ID              = 2;
    const ADDITIONAL_CHANNEL_META = [
        [
            'key'   => ParcelNow::META_APP_PARCELNOW_ENABLED,
            'value' => 'true'
        ]
    ];

    /**
     * @param string $channel_order_data_filename
     * @param string $shopify_order_data_filename
     * @param array|null $additionalMeta
     * @return array
     * @throws ServerError
     * @throws UnprocessableEntity
     */
    private function setUpTest(
        string $channel_order_data_filename,
        string $shopify_order_data_filename,
        array $additionalMeta = null
    ): array {
        $conf                        = tests\v1\stock2shop\dal\channels\shopify2\TestCase::CONFIG;
        $conf['channels'][0]['meta'] = array_merge(
            $conf['channels'][0]['meta'],
            $additionalMeta ?? self::ADDITIONAL_CHANNEL_META
        );
        $config                      = new vo\Config($conf);
        tests\DB::makeFromConfig($config);
        Configuration::getInstance(true)->init($config, vo\Config::CONNECTOR_CHANNEL, self::CHANNEL_ID);

        $channel_order_data = file_get_contents(__DIR__ . '/mocks/' . $channel_order_data_filename);
        $shopify_order_data = file_get_contents(__DIR__ . '/mocks/' . $shopify_order_data_filename);

        return [
            new vo\ChannelOrder(Utils::jsonDecode($channel_order_data)),
            new Order(Utils::jsonDecode($shopify_order_data))
        ];
    }

    public function testParcelNowOrderWithoutParcelNowAddress()
    {
        list($channelOrder, $shopifyOrder) = $this->setUpTest(
            'channel_order_with_parcelnow_shipping.json',
            'shopify_order_with_parcelnow_shipping_and_no_address.json'
        );

        $shipping_address_before_set = clone $channelOrder->shipping_address;

        ParcelNow::setShippingAddressOrOrderState($channelOrder, $shopifyOrder);

        // Shipping address remains unchanged
        $this->assertEquals($shipping_address_before_set->address1, $channelOrder->shipping_address->address1);
        $this->assertEquals($shipping_address_before_set->address2, $channelOrder->shipping_address->address2);
        $this->assertEquals($shipping_address_before_set->city, $channelOrder->shipping_address->city);
        $this->assertEquals($shipping_address_before_set->province, $channelOrder->shipping_address->province);
        $this->assertEquals($shipping_address_before_set->zip, $channelOrder->shipping_address->zip);
        $this->assertEquals('0731234567', $channelOrder->shipping_address->phone);

        // It is a parcelnow order, so until we have a valid address, we set the order instruction to 'sync_order'
        $this->assertEquals(vo\ChannelOrder::INSTRUCTION_SYNC, $channelOrder->instruction);
    }

    public function testParcelNowOrderWithParcelNowAddress()
    {
        list($channelOrder, $shopifyOrder) = $this->setUpTest(
            'channel_order_with_parcelnow_shipping.json',
            'shopify_order_with_parcelnow_shipping_and_address.json'
        );

        $parcelnowAddress = ParcelNow::getParcelNowAddress($shopifyOrder->customAttributes[0]->value);

        ParcelNow::setShippingAddressOrOrderState($channelOrder, $shopifyOrder);

        // Shipping address remains unchanged
        $this->assertEquals(ParcelNow::getParcelNowName(), $channelOrder->shipping_address->first_name);
        $this->assertEquals($parcelnowAddress->address1, $channelOrder->shipping_address->address1);
        $this->assertEquals($parcelnowAddress->address2, $channelOrder->shipping_address->address2);
        $this->assertEquals($parcelnowAddress->city, $channelOrder->shipping_address->city);
        $this->assertEquals($parcelnowAddress->province, $channelOrder->shipping_address->province);
        $this->assertEquals($parcelnowAddress->zip, $channelOrder->shipping_address->zip);
        $this->assertEquals(ParcelNow::PARCEL_CONTACT_NUMBER, $channelOrder->shipping_address->phone);

        // Customer name and email are set to ParcelNow defaults
        $this->assertEquals(ParcelNow::PARCELNOW_NAME_DEFAULT, $channelOrder->customer->first_name);
        $this->assertEquals(ParcelNow::PARCELNOW_EMAIL_DEFAULT, $channelOrder->customer->email);

        // Confirm all meta data is set correctly
        $this->assertEquals(ParcelNow::generateShippingAddress($shopifyOrder), $channelOrder->params[ParcelNow::META_ORIGINAL_SHIPPING_ADDRESS]);
        $this->assertEquals($shopifyOrder->customer->email, $channelOrder->params[ParcelNow::META_ORIGINAL_CUSTOMER_EMAIL]);
        $this->assertEquals(ParcelNow::generateCustomerName($shopifyOrder), $channelOrder->params[ParcelNow::META_ORIGINAL_CUSTOMER_NAME]);

        // It is a parcelnow order so the original order instruction remains
        $this->assertEquals(vo\ChannelOrder::INSTRUCTION_ADD, $channelOrder->instruction);
    }

    public function testNonParcelNowOrder()
    {
        list($channelOrder, $shopifyOrder) = $this->setUpTest(
            'channel_order_with_non_parcelnow_shipping.json',
            'shopify_order_with_non_parcelnow_shipping.json'
        );

        $shipping_address_before_set = clone $channelOrder->shipping_address;

        ParcelNow::setShippingAddressOrOrderState($channelOrder, $shopifyOrder);

        // Shipping address remains unchanged
        $this->assertEquals($shipping_address_before_set->address1, $channelOrder->shipping_address->address1);
        $this->assertEquals($shipping_address_before_set->address2, $channelOrder->shipping_address->address2);
        $this->assertEquals($shipping_address_before_set->city, $channelOrder->shipping_address->city);
        $this->assertEquals($shipping_address_before_set->province, $channelOrder->shipping_address->province);
        $this->assertEquals($shipping_address_before_set->zip, $channelOrder->shipping_address->zip);
        $this->assertEquals('0731234567', $channelOrder->shipping_address->phone);
    }

    public function testParcelNowOrderWithInvalidAddress()
    {
        list($channelOrder, $shopifyOrder) = $this->setUpTest(
            'channel_order_with_parcelnow_shipping.json',
            'shopify_order_with_parcelnow_shipping_and_invalid_address.json'
        );

        $exception = false;
        try {
            ParcelNow::setShippingAddressOrOrderState($channelOrder, $shopifyOrder);
        } catch (UnprocessableEntity $e) {
            $exception = true;
        }
        $this->assertTrue($exception, 'Expected exception to be thrown due to invalid ParcelNow address.');
    }

    public function testParcelNowOrderWithParcelNowNotConfigured()
    {
        $additionalMeta = [
            [
                'key'   => ParcelNow::META_APP_PARCELNOW_ENABLED,
                'value' => 'false'
            ]
        ];
        list($channelOrder, $shopifyOrder) = $this->setUpTest(
            'channel_order_with_parcelnow_shipping.json',
            'shopify_order_with_parcelnow_shipping_and_address.json',
            $additionalMeta
        );

        $shipping_address_before_set = clone $channelOrder->shipping_address;

        ParcelNow::setShippingAddressOrOrderState($channelOrder, $shopifyOrder);

        // Shipping address remains unchanged
        $this->assertEquals($shipping_address_before_set->address1, $channelOrder->shipping_address->address1);
        $this->assertEquals($shipping_address_before_set->address2, $channelOrder->shipping_address->address2);
        $this->assertEquals($shipping_address_before_set->city, $channelOrder->shipping_address->city);
        $this->assertEquals($shipping_address_before_set->province, $channelOrder->shipping_address->province);
        $this->assertEquals($shipping_address_before_set->zip, $channelOrder->shipping_address->zip);
        $this->assertEquals('0731234567', $channelOrder->shipping_address->phone);
    }
}
