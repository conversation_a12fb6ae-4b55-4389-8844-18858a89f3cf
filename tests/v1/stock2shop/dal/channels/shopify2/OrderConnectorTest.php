<?php

namespace tests\v1\stock2shop\dal\channels\shopify2;

use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Response;
use stock2shop\dal\channel\TransformOrderArgs;
use stock2shop\dal\channels\shopify2;
use stock2shop\dal\system;
use stock2shop\vo;
use Throwable;

class OrderConnectorTest  extends TestCase
{


    /**
     * @throws Exception
     * @throws Throwable
     */
    public function testTransformOrderIncl() {

        foreach ($this->getTaxInclOrderFileNames() as $file) {
            $this->initShopify2Test();
            $getOrderResponse = file_get_contents(__DIR__ . '/../shopify/OrderTransformTestData/'. $file);

            // guzzle client mock
            $mock         = new MockHandler([
                new Response(200, ['Content-Type' => 'application/json'], $getOrderResponse),
            ]);
            $handlerStack = HandlerStack::create($mock);
            $guzzleClient = new Client(['handler' => $handlerStack]);
            shopify2\Configuration::getInstance()->setGuzzleClient($guzzleClient);

            // test order
            $args         = new TransformOrderArgs(
                ['id' => '123', 'admin_graphql_api_id' => 'gid://shopify/Order/123'],
                new vo\Config(self::CONFIG),
                self::CHANNEL_ID
            );
            $con          = new shopify2\OrdersConnector();
            $channelOrder = $con->transformOrder($args);

            // calc all totals
            $obj = new \stdClass();
            $obj->system_order = $channelOrder;
            system\order::calculateTotals($obj);
            $systemOrder = vo\SystemOrder::createFromJSON(json_encode($obj->system_order));
            $this->assertOrderTotals(
                json_decode($getOrderResponse, true)['data']['order'],
                $systemOrder,
                $file
            );
        }
    }

    /**
     * @throws Throwable
     */
    public function testTransformOrderPos()
    {
        foreach ($this->getPosOrderTestFileNames() as $file) {
            $this->initShopify2Test();
            $getOrderResponse = file_get_contents(__DIR__ . '/../shopify/OrderTransformTestData/' . $file);

            // guzzle client mock
            $mock         = new MockHandler([
                new Response(200, ['Content-Type' => 'application/json'], $getOrderResponse),
            ]);
            $handlerStack = HandlerStack::create($mock);
            $guzzleClient = new Client(['handler' => $handlerStack]);
            shopify2\Configuration::getInstance()->setGuzzleClient($guzzleClient);

            // test order
            $args         = new TransformOrderArgs(
                ['id' => '123', 'admin_graphql_api_id' => 'gid://shopify/Order/123'],
                new vo\Config(self::CONFIG),
                self::CHANNEL_ID
            );
            $con          = new shopify2\OrdersConnector();
            $channelOrder = $con->transformOrder($args);

            // calc all totals
            $obj               = new \stdClass();
            $obj->system_order = $channelOrder;
            system\order::calculateTotals($obj);
            $systemOrder = vo\SystemOrder::createFromJSON(json_encode($obj->system_order));
            $this->assertOrderTotals(
                json_decode($getOrderResponse, true)['data']['order'],
                $systemOrder,
                $file
            );

            // check if the pos customer details are set correctly.
            $this->assertPosOrderCustomerDetails(
                json_decode($getOrderResponse, true)['data']['order'],
                $channelOrder,
                $file
            );
        }
    }

    /**
     * This currently checks exception since we have not added feature for excluding orders yet.
     * @throws Exception
     * @throws Throwable
     */
    public function testTransformOrderExcl() {

        foreach ($this->getTaxExclOrderFileNames() as $file) {
            $this->initShopify2Test();
            $getOrderResponse = file_get_contents(__DIR__ . '/../shopify/OrderTransformTestData/'. $file);

            // guzzle client mock
            $mock         = new MockHandler([
                new Response(200, ['Content-Type' => 'application/json'], $getOrderResponse),
            ]);
            $handlerStack = HandlerStack::create($mock);
            $guzzleClient = new Client(['handler' => $handlerStack]);
            shopify2\Configuration::getInstance()->setGuzzleClient($guzzleClient);

            // test order
            $args         = new TransformOrderArgs(
                ['id' => '123', 'admin_graphql_api_id' => 'gid://shopify/Order/123'],
                new vo\Config(self::CONFIG),
                self::CHANNEL_ID
            );
            $con          = new shopify2\OrdersConnector();
            $channelOrder = $con->transformOrder($args);

            // calc all totals
            $obj = new \stdClass();
            $obj->system_order = $channelOrder;
            system\order::calculateTotals($obj);
            $systemOrder = vo\SystemOrder::createFromJSON(json_encode($obj->system_order));
            $this->assertOrderTotalsExcl(
                json_decode($getOrderResponse, true)['data']['order'],
                $systemOrder,
                $file
            );
        }
    }

    public function assertPosOrderCustomerDetails(array $webhook, vo\ChannelOrder $channelOrder, string $file)
    {
        $this->assertEquals($webhook['sourceIdentifier'], $channelOrder->customer->channel_customer_code, $file);
        $this->assertEquals($webhook['sourceName'], $channelOrder->customer->first_name, $file);
    }

    /**
     * @param array $webhook
     * @param vo\SystemOrder $systemOrder
     * @param string $file
     * @return void
     */
    public function assertOrderTotals(array $webhook, vo\SystemOrder $systemOrder, string $file) {
        $this->assertEquals($webhook['name'], $systemOrder->channel_order_code, $file);
        $this->assertTrue($systemOrder->has_taxes_incl);
        $this->assertEquals(
            $webhook['currentTotalPriceSet']['shopMoney']['amount'],
            $systemOrder->totals_incl->total,
            $file
        );
        $this->assertEquals(
            $webhook['currentTotalTaxSet']['shopMoney']['amount'],
            $systemOrder->totals_incl->tax,
            $file
        );
    }

    /**
     * Consider doAssortedOrdersChecks in channels/shopify/OrderTransformTest.php.
     * The same test data is used for the shopify2 channel,
     * and the test logic must be similar
     * @param array $webhook
     * @param vo\SystemOrder $systemOrder
     * @param string $file
     * @return void
     * @throws Exception
     */
    public function assertOrderTotalsExcl(array $webhook, vo\SystemOrder $systemOrder, string $file) {
        $expectedTax = $webhook['currentTotalTaxSet']['shopMoney']['amount'];
        $expectedSubTotal = 0;
        $expectedTotal = $webhook['currentTotalPriceSet']['shopMoney']['amount'];

        $this->assertEquals($webhook['name'], $systemOrder->channel_order_code, $file);

        // The has_taxes_incl property is set
        // if the shopify order taxes_included=true
        $this->assertFalse($systemOrder->has_taxes_incl);

        if ($file == "1804-08-order-no-shipping-or-discounts-total-R965.02-excl-gql.json") {
            // Match hardcoded totals from testOrderWithPricesExclTax,
            // see channels/shopify/OrderTransformTest.php
            $expectedTax = 125.87;
            $expectedSubTotal = 839.15;
            $expectedTotal = 965.02;
        } else if ($file == "1804-12-discount-split-across-many-qty-excl-gql.json") {
            // Fails due to rounding error.
            // This stub is also skipped for shopify channel, see
            // https://github.com/stock2shop/app/issues/1872
            $this->assertTrue(true);
            return;
        }
        $this->assertEquals(
            $expectedTax,
            $systemOrder->tax,
            "$file: total tax"
        );
        if ($expectedSubTotal > 0) {
            // Only check this for certain stubs,
            // does not correspond to a property on the webhook?
            $this->assertEquals(
                $expectedSubTotal,
                $systemOrder->sub_total,
                "$file: subtotal calculation"
            );
        }
        $this->assertEquals(
            $expectedTotal,
            $systemOrder->total,
            "$file: total calculation"
        );
    }

    private function getTaxInclOrderFileNames(): array {
        return [
            '1804-03-partial-discount-taxed-and-untaxed-incl-gql.json',
            '1804-05-partial-discount-incl-gql.json',
            '1804-07-order-no-shipping-or-discounts-total-R965.00-incl-gql.json',
            '1804-09-discount-on-shipping-incl-gql.json',
            '1804-11-discount-split-across-many-qty-incl-gql.json',
            '1804-13-tax-exempt-shipping-incl-gql.json',
            '1804-15-3xR150-items-tax-calc-incl-gql.json',
            'order-with-double-freeby-discount-1749-gql.json',
        ];
    }

    private function getTaxExclOrderFileNames(): array {
        return [
            '1804-04-partial-discount-taxed-and-untaxed-excl-gql.json',
            '1804-06-partial-discount-excl-gql.json',
            '1804-08-order-no-shipping-or-discounts-total-R965.02-excl-gql.json',
            '1804-10-discount-on-shipping-excl-gql.json',
            '1804-12-discount-split-across-many-qty-excl-gql.json',
            '1804-14-tax-exempt-shipping-excl-gql.json'
        ];
    }

    /**
     * https://github.com/stock2shop/app/issues/1871
     * @return string[]
     */
    private function getBrokenOrderTestFileNames(): array {
        return [
            '1804-01-discount-on-tax-free-incl-gql.json',
            '1804-02-discount-on-tax-free-excl-gql.json'
        ];
    }

    /**
     * @return string[]
     */
    private function getPosOrderTestFileNames(): array {
        return [
            '1804-16-pos-order-partial-discount-taxed-and-untaxed-incl-gql.json',
            '1804-16-post-order-partial-discount-incl-gql.json'
        ];
    }
}
