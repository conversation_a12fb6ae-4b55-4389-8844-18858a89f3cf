<?php

namespace tests\v1\stock2shop\dal\channels\shopify2;

use Exception;
use stock2shop\dal\channels\shopify2;
use stock2shop\dal\connector\Factory;
use tests;
use stock2shop\vo;

class TestCase extends tests\TestCase
{
    const ACCESS_TOKEN = 'xyz';
    const SOURCE_ID = 1;
    const CHANNEL_ID = 2;
    const FULFILLMENTSERVICE_ID = 3;
    const CLIENT_ID = 1;

    // this config should be valid for our amazon test developer account.
    const CONFIG = [
        'client_id'           => self::CLIENT_ID,
        'meta'                => [
            [
                'key'   => 'notifications',
                'value' => '{"user":{"name":"Mr Me","email":"<EMAIL>"}}'
            ]
        ],
        'sources'             => [
            [
                'id'   => self::SOURCE_ID,
                'type' => 'test_source'
            ]
        ],
        'channels'            => [
            [
                'id'          => self::CHANNEL_ID,
                'description' => 'shopify2',
                'type'        => 'shopify2',
                'meta'        => [
                    [
                        'key'   => shopify2\Configuration::DOMAIN,
                        'value' => 'xyz'
                    ]
                ]
            ]
        ],
        'fulfillmentservices' => [
            [
                'id'          => self::FULFILLMENTSERVICE_ID,
                'description' => 'test_fulfillmentservice',
                'type'        => 'test_fulfillmentservice'
            ]
        ]
    ];

    /**
     * @throws Exception
     */
    protected function initShopify2Test(array $additionalChannelMeta = [], $additionalSecrets = []): vo\Config
    {
        $data = self::CONFIG;
        foreach ($additionalChannelMeta as $k => $v) {
            $data['channels'][0]['meta'][] = [
                'key'   => $k,
                'value' => $v
            ];
        }
        $config = new vo\Config($data);

        // insert secret for access token
        tests\DB::makeSecrets($config, vo\Config::CONNECTOR_CHANNEL, self::CHANNEL_ID, [
            'access_token' => self::ACCESS_TOKEN
        ]);

        Factory::clear();
        Factory::initConnector($config, vo\Config::CONNECTOR_CHANNEL, self::CHANNEL_ID);
        shopify2\Configuration::getInstance(true)->init(
            $config,
            vo\Config::CONNECTOR_CHANNEL,
            self::CHANNEL_ID
        );
        foreach ($additionalSecrets as $k => $v) {
            shopify2\Configuration::getInstance()->getSecretsRepository()->set($k, $v);
        }
        return $config;
    }

    protected function addRefundToOrder(vo\SystemOrder $order, bool $refund_shipping = false)
    {
        $refund_item      = new vo\RefundItem();
        $refund_item->sku = $order->line_items[0]->sku;

        $refund                       = new vo\Refund();
        $refund->client_id            = self::CLIENT_ID;
        $refund->channel_id           = self::CHANNEL_ID;
        $refund->channel_refund_state = $refund::CONNECTOR_STATE_PROCESSING;
        $refund->line_items[]         = $refund_item;

        if ($refund_shipping) {
            $refund_item                     = new vo\RefundItem();
            $refund_item->sku                = $order->shipping_lines[0]->title;
            $refund_item->code               = $refund_item::CODE_SHIPPING;
            $refund->line_items[] = $refund_item;
        }

        $order->refunds[] = $refund;
    }
}
