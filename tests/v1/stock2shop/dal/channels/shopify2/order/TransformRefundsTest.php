<?php

namespace tests\v1\stock2shop\dal\channels\shopify2\order;

use Exception;
use GuzzleHttp\Exception\GuzzleException;
use stock2shop\dal\channels\shopify2\lib\models\Order;
use stock2shop\dal\channels\shopify2\order\TransformRefunds;
use stock2shop\exceptions\UnprocessableEntity;
use stock2shop\lib\Utils;
use tests\v1\stock2shop\dal\channels\shopify2\TestCase;
use stock2shop\vo;

class TransformRefundsTest extends TestCase
{
    /**
     * @throws GuzzleException
     * @throws UnprocessableEntity
     * @throws Exception
     */
    public function testSetRefundsWithSingleRefundDiscrepancyReason()
    {
        self::initShopify2Test();

        $channel_order_data = file_get_contents(__DIR__ . '/transformTestData/channel_order_before_refund.json');
        $shopify_order_data = file_get_contents(__DIR__ . '/transformTestData/shopify_order_with_refund.json');

        $channelOrder = new vo\ChannelOrder(Utils::jsonDecode($channel_order_data));
        $shopifyOrder = new Order(Utils::jsonDecode($shopify_order_data));

        TransformRefunds::setRefunds($channelOrder, $shopifyOrder);

        $this->assertCount(1, $channelOrder->refunds);

        $this->assertEquals('874695393418', $channelOrder->refunds[0]->channel_refund_code);
        $refund_items = $channelOrder->refunds[0]->getItemLines();
        $this->assertCount(2, $refund_items);
        foreach ($refund_items as $item) {
            $this->assertEquals(2, $item->qty);
        }
        $this->assertEquals('SKU002', $channelOrder->refunds[0]->line_items[0]->sku);
        $this->assertEquals('SKU001', $channelOrder->refunds[0]->line_items[1]->sku);

        // Only refunded R30 of R50 shipping
        $shipping_lines = $channelOrder->refunds[0]->getShippingLines();
        $this->assertCount(1, $shipping_lines);
        $this->assertEquals(50, $shipping_lines[0]->unit_amount_incl);
        $this->assertEquals(30, $shipping_lines[0]->subtotal_incl);
        $this->assertEquals(3.92, $shipping_lines[0]->subtotal_tax);
        $this->assertEqualsWithDelta(
            $shipping_lines[0]->subtotal_tax,
            $shipping_lines[0]->subtotal_incl * 15/115,
            TransformRefunds::DIFF_CHECK_PRECISION);

        // Refunded R200 less than suggested amount
        $discrepancy_lines = $channelOrder->refunds[0]->getDiscrepancyLines();
        $this->assertCount(1, $discrepancy_lines);
        $this->assertEquals(-200, $discrepancy_lines[0]->unit_amount_incl);
        $this->assertEquals(-200, $discrepancy_lines[0]->subtotal_incl);
        $this->assertEquals(0, $discrepancy_lines[0]->subtotal_tax);

        // assert totals
        $total_subtotal_incl = Utils::sumFloat($channelOrder->refunds[0]->line_items,
            function (vo\RefundItem $item) {
                return $item->subtotal_incl;
            });
        $this->assertEquals($channelOrder->refunds[0]->amount_incl, $total_subtotal_incl);
        $this->assertEquals($channelOrder->refunds[0]->amount_incl, $channelOrder->refunds[0]->transactions[0]->amount);
    }

    public function testSetRefundsWithMultipleRefundDiscrepancyReasons()
    {
        self::initShopify2Test();

        $channel_order_data = file_get_contents(__DIR__ . '/transformTestData/channel_order_before_refund_2.json');
        $shopify_order_data = file_get_contents(
            __DIR__ .
            '/transformTestData/shopify_order_with_pending_discrepancy_refund.json'
        );

        $channelOrder = new vo\ChannelOrder(Utils::jsonDecode($channel_order_data));
        $shopifyOrder = new Order(Utils::jsonDecode($shopify_order_data));

        TransformRefunds::setRefunds($channelOrder, $shopifyOrder);

        $this->assertCount(1, $channelOrder->refunds);

        $this->assertEquals('932827234393', $channelOrder->refunds[0]->channel_refund_code);
        $refund_items = $channelOrder->refunds[0]->getItemLines();
        $this->assertCount(1, $refund_items);
        foreach ($refund_items as $item) {
            $this->assertEquals(1, $item->qty);
        }
        $this->assertEquals('ABC', $channelOrder->refunds[0]->line_items[0]->sku);

        $shipping_lines = $channelOrder->refunds[0]->getShippingLines();
        $this->assertCount(0, $shipping_lines);

        // Refunded R200 less than suggested amount
        $discrepancy_lines = $channelOrder->refunds[0]->getDiscrepancyLines();
        $this->assertCount(2, $discrepancy_lines);
        $this->assertEquals(-339.15, $discrepancy_lines[0]->unit_amount_incl);
        $this->assertEquals(-339.15, $discrepancy_lines[0]->subtotal_incl);
        $this->assertEquals(0, $discrepancy_lines[0]->subtotal_tax);
        $this->assertEquals(339.15, $discrepancy_lines[1]->unit_amount_incl);
        $this->assertEquals(339.15, $discrepancy_lines[1]->subtotal_incl);
        $this->assertEquals(0, $discrepancy_lines[1]->subtotal_tax);

        // assert totals
        $total_subtotal_incl = Utils::sumFloat(
            $channelOrder->refunds[0]->line_items,
            function (vo\RefundItem $item) {
                return $item->subtotal_incl;
            }
        );
        $this->assertEquals($channelOrder->refunds[0]->amount_incl, $total_subtotal_incl);
        $this->assertEquals(
            $channelOrder->refunds[0]->amount_incl,
            $channelOrder->refunds[0]->transactions[0]->amount
        );
    }
}
