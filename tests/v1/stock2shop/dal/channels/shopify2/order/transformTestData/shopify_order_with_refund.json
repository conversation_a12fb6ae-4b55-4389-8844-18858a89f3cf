{"billingAddress": {"address1": "Address", "address2": null, "city": "Cape Town", "company": null, "country": "South Africa", "countryCodeV2": "ZA", "firstName": "test", "lastName": "user", "name": null, "phone": null, "province": "Western Cape", "provinceCode": "WC", "zip": "7777"}, "createdAt": "2024-12-05T15:52:47Z", "customAttributes": [], "customer": {"displayName": null, "email": "<EMAIL>", "firstName": "test", "id": "gid://shopify/Customer/7136610254986", "lastName": "user", "phone": "+27763327963"}, "customerAcceptsMarketing": false, "discountCodes": [], "fullyPaid": true, "id": "gid://shopify/Order/5786484310154", "lineItems": [{"id": "gid://shopify/LineItem/14407233732746", "discountAllocations": [], "title": "Product Y", "quantity": 3, "originalUnitPriceSet": {"shopMoney": {"amount": 199, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 199, "currencyCode": "ZAR"}}, "sku": "SKU002", "taxable": true, "taxLines": [{"priceSet": {"shopMoney": {"amount": 77.87, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 77.87, "currencyCode": "ZAR"}}, "rate": 0.15, "title": "VAT"}], "totalDiscountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}, {"id": "gid://shopify/LineItem/14407233765514", "discountAllocations": [], "title": "Product X", "quantity": 3, "originalUnitPriceSet": {"shopMoney": {"amount": 599, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 599, "currencyCode": "ZAR"}}, "sku": "SKU001", "taxable": true, "taxLines": [{"priceSet": {"shopMoney": {"amount": 234.39, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 234.39, "currencyCode": "ZAR"}}, "rate": 0.15, "title": "VAT"}], "totalDiscountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}], "name": "#1246", "note": null, "refunds": [{"createdAt": "2024-12-05T15:54:08Z", "id": "gid://shopify/Refund/874695393418", "note": "Partial shipping and discrepancy", "orderAdjustments": [{"amountSet": {"shopMoney": {"amount": 200, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 200, "currencyCode": "ZAR"}}, "id": "gid://shopify/OrderAdjustment/261467111562", "reason": "REFUND_DISCREPANCY", "taxAmountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}], "updatedAt": "2024-12-05T15:54:08Z", "refundLineItems": [{"id": "gid://shopify/RefundLineItem/373992095882", "lineItem": {"id": null, "discountAllocations": [], "title": "Product Y", "quantity": 3, "originalUnitPriceSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "sku": "SKU002", "taxable": null, "taxLines": [], "totalDiscountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}, "priceSet": {"shopMoney": {"amount": 199, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 199, "currencyCode": "ZAR"}}, "quantity": 2, "restockType": "NO_RESTOCK", "restocked": false, "subtotalSet": {"shopMoney": {"amount": 398, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 398, "currencyCode": "ZAR"}}, "totalTaxSet": {"shopMoney": {"amount": 51.91, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 51.91, "currencyCode": "ZAR"}}}, {"id": "gid://shopify/RefundLineItem/373992128650", "lineItem": {"id": null, "discountAllocations": [], "title": "Product X", "quantity": 3, "originalUnitPriceSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "sku": "SKU001", "taxable": null, "taxLines": [], "totalDiscountSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}}, "priceSet": {"shopMoney": {"amount": 599, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 599, "currencyCode": "ZAR"}}, "quantity": 2, "restockType": "NO_RESTOCK", "restocked": false, "subtotalSet": {"shopMoney": {"amount": 1198, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 1198, "currencyCode": "ZAR"}}, "totalTaxSet": {"shopMoney": {"amount": 156.26, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 156.26, "currencyCode": "ZAR"}}}], "refundShippingLines": [{"id": "gid://shopify/RefundShippingLine/21690122378", "shippingLine": {"title": "Standard", "code": "Standard", "originalPriceSet": {"shopMoney": {"amount": 50, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 50, "currencyCode": "ZAR"}}, "taxLines": [{"priceSet": {"shopMoney": {"amount": 6.52, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 6.52, "currencyCode": "ZAR"}}, "rate": 0.15, "title": "VAT"}], "discountedPriceSet": {"shopMoney": {"amount": 50, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 50, "currencyCode": "ZAR"}}}, "subtotalAmountSet": {"shopMoney": {"amount": 26.08, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 26.08, "currencyCode": "ZAR"}}, "taxAmountSet": {"shopMoney": {"amount": 3.92, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 3.92, "currencyCode": "ZAR"}}}], "return": {"id": null, "name": null, "status": null}, "transactions": [{"accountNumber": null, "amountSet": {"shopMoney": {"amount": 1426, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 1426, "currencyCode": "ZAR"}}, "authorizationCode": null, "createdAt": "2024-12-05T15:54:08Z", "gateway": "bogus", "id": "gid://shopify/OrderTransaction/*************", "kind": "REFUND", "parentTransaction": {"accountNumber": null, "amountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "authorizationCode": null, "createdAt": null, "gateway": null, "id": "gid://shopify/OrderTransaction/*************", "kind": null, "parentTransaction": null, "paymentDetails": {"avsResultCode": null, "bin": null, "company": null, "cvvResultCode": null, "expirationMonth": null, "expirationYear": null, "name": null, "number": null, "paymentMethodName": null}, "paymentId": null, "processedAt": null, "receiptJson": null, "status": null}, "paymentDetails": {"avsResultCode": null, "bin": "1", "company": "<PERSON><PERSON>", "cvvResultCode": null, "expirationMonth": 12, "expirationYear": 2034, "name": "test user", "number": "•••• •••• •••• 1", "paymentMethodName": "card"}, "paymentId": "#1246.2", "processedAt": "2024-12-05T15:54:08Z", "receiptJson": "{\"paid_amount\":\"1426.00\"}", "status": "SUCCESS"}], "totalRefundedSet": {"shopMoney": {"amount": 1426, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 1426, "currencyCode": "ZAR"}}}], "returns": [], "shippingAddress": {"address1": "Address", "address2": null, "city": "Cape Town", "company": null, "country": "South Africa", "countryCodeV2": "ZA", "firstName": "test", "lastName": "user", "name": null, "phone": null, "province": "Western Cape", "provinceCode": "WC", "zip": "7777"}, "shippingLines": [{"title": "Standard", "code": "Standard", "originalPriceSet": {"shopMoney": {"amount": 50, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 50, "currencyCode": "ZAR"}}, "taxLines": [{"priceSet": {"shopMoney": {"amount": 6.52, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 6.52, "currencyCode": "ZAR"}}, "rate": 0.15, "title": "VAT"}], "discountedPriceSet": {"shopMoney": {"amount": 50, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 50, "currencyCode": "ZAR"}}}], "suggestedRefund": {"amountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "refundLineItems": [], "shipping": {"amountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "maximumRefundableSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}}, "suggestedTransactions": []}, "taxesIncluded": true, "taxLines": [{"priceSet": {"shopMoney": {"amount": 318.78, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 318.78, "currencyCode": "ZAR"}}, "rate": 0.15, "title": "VAT"}], "totalDiscountsSet": {"shopMoney": {"amount": 0, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 0, "currencyCode": "ZAR"}}, "fulfillmentOrders": [{"id": "gid://shopify/FulfillmentOrder/6887702921354", "status": "OPEN", "supportedActions": [{"action": "CREATE_FULFILLMENT"}, {"action": "MOVE"}, {"action": "HOLD"}, {"action": "SPLIT"}], "fulfillments": [], "lineItems": [{"id": "gid://shopify/FulfillmentOrderLineItem/**************", "remainingQuantity": 1, "sku": "SKU002", "totalQuantity": 1}, {"id": "gid://shopify/FulfillmentOrderLineItem/**************", "remainingQuantity": 1, "sku": "SKU001", "totalQuantity": 1}]}], "totalRefundedSet": {"shopMoney": {"amount": 1426, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 1426, "currencyCode": "ZAR"}}, "totalRefundedShippingSet": {"shopMoney": {"amount": 30, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 30, "currencyCode": "ZAR"}}, "transactions": [{"accountNumber": null, "amountSet": {"shopMoney": {"amount": 2444, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 2444, "currencyCode": "ZAR"}}, "authorizationCode": "53433", "createdAt": "2024-12-05T15:52:46Z", "gateway": "bogus", "id": "gid://shopify/OrderTransaction/*************", "kind": "SALE", "parentTransaction": null, "paymentDetails": {"avsResultCode": null, "bin": "1", "company": "<PERSON><PERSON>", "cvvResultCode": null, "expirationMonth": 12, "expirationYear": 2034, "name": "test user", "number": "•••• •••• •••• 1", "paymentMethodName": "card"}, "paymentId": "rQGp8P4vFDheaHw0pmmIHRxf5", "processedAt": "2024-12-05T15:52:46Z", "receiptJson": "{\"paid_amount\":\"2444.00\"}", "status": "SUCCESS"}, {"accountNumber": null, "amountSet": {"shopMoney": {"amount": 1426, "currencyCode": "ZAR"}, "presentmentMoney": {"amount": 1426, "currencyCode": "ZAR"}}, "authorizationCode": null, "createdAt": "2024-12-05T15:54:08Z", "gateway": "bogus", "id": "gid://shopify/OrderTransaction/*************", "kind": "REFUND", "parentTransaction": {"accountNumber": null, "amountSet": {"shopMoney": {"amount": null, "currencyCode": null}, "presentmentMoney": {"amount": null, "currencyCode": null}}, "authorizationCode": null, "createdAt": null, "gateway": null, "id": "gid://shopify/OrderTransaction/*************", "kind": null, "parentTransaction": null, "paymentDetails": {"avsResultCode": null, "bin": null, "company": null, "cvvResultCode": null, "expirationMonth": null, "expirationYear": null, "name": null, "number": null, "paymentMethodName": null}, "paymentId": null, "processedAt": null, "receiptJson": null, "status": null}, "paymentDetails": {"avsResultCode": null, "bin": "1", "company": "<PERSON><PERSON>", "cvvResultCode": null, "expirationMonth": 12, "expirationYear": 2034, "name": "test user", "number": "•••• •••• •••• 1", "paymentMethodName": "card"}, "paymentId": "#1246.2", "processedAt": "2024-12-05T15:54:08Z", "receiptJson": "{\"paid_amount\":\"1426.00\"}", "status": "SUCCESS"}]}