<?php

namespace tests\v1\stock2shop\dal\channels\shopify2\order;

use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Response;
use stock2shop\dal\channels\shopify2;
use stock2shop\dal\payments\peach\Payment;
use stock2shop\lib\Utils;
use stock2shop\vo;
use tests\v1\stock2shop\dal\channels\shopify2\TestCase;
use Throwable;

class TransformTransactionTest extends TestCase
{

    /**
     * @throws Exception
     * @throws Throwable
     */
    public function testTransformTransactionsWithPeachPaymentGatewayNoMeta()
    {

        $this->initShopify2Test(
            [
                Payment::PEACH_PAYMENT_BASE_URL_KEY => 'https://peach.com'
            ],
            [
                Payment::PEACH_ENTITY_ID_KEY => 'abc',
                Payment::PEACH_SECRET_KEY    => 'xyz'
            ]
        );
        $response = file_get_contents(__DIR__ . '/transformTestData/get-order-response-gateway-peach.json');

        // guzzle client mock
        $mock         = new MockHandler([
            new Response(200, ['Content-Type' => 'application/json'], $response),
        ]);
        $handlerStack = HandlerStack::create($mock);
        $guzzleClient = new Client(['handler' => $handlerStack]);
        shopify2\Configuration::getInstance()->setGuzzleClient($guzzleClient);

        $now = Utils::getMySqlDateMicroseconds();
        $co                         = new vo\ChannelOrder([]);
        $shopifyOrder               = new shopify2\lib\models\Order();
        $trans                      = new shopify2\lib\models\OrderTransaction();
        $trans->id                  = 'gid://shopify/Order/6192004104364';
        $trans->kind                = shopify2\lib\models\OrderTransaction::KIND_SALE;
        $trans->gateway             = 'Peach payments';
        $trans->paymentId           = 'rwgEqwIr6qd7lMOUWIhDHjrmD';
        $trans->status              = shopify2\lib\models\OrderTransaction::STATE_SUCCESS;
        $shopifyOrder->transactions = [$trans];
        shopify2\order\TransformTransactions::setOrderTransactions($co, $shopifyOrder);

        // check transactions
        $transaction = $co->transactions[0];
        $this->assertEquals('2024-07-24T12:39:18Z', $transaction->completed_date);
        $this->assertEquals(vo\Transaction::KIND_DEBIT, $transaction->kind);
        $this->assertEquals('CARD', $transaction->payment_method);
        $this->assertEquals('860002', $transaction->stan);
        $this->assertEquals(vo\Transaction::STATE_COMPLETE, $transaction->state);
        $this->assertEquals('006887', $transaction->auth_code);
        $this->assertEquals('372905055935', $transaction->rrn);
        $this->assertEquals('567890******1234', $transaction->getMaskedCardNumber());

    }

    /**
     * @throws Exception
     * @throws Throwable
     */
    public function testTransformTransactionsWithPeachPaymentGatewayErrorStates()
    {
        $this->initShopify2Test();

        // No need to mock a Guzzle client since we're testing a failure state
        // and don't expect setPaymentOnTransactionV2 to be called
        $co                         = new vo\ChannelOrder([]);
        $shopifyOrder               = new shopify2\lib\models\Order();

        $trans                      = new shopify2\lib\models\OrderTransaction();
        $trans->id                  = 'gid://shopify/Order/6192004104364';
        $trans->kind                = shopify2\lib\models\OrderTransaction::KIND_SALE;
        $trans->gateway             = 'Peach payments';
        $trans->paymentId           = 'rwgEqwIr6qd7lMOUWIhDHjrmD';
        $trans->processedAt         = '2024-01-02T01:23:45';
        $trans->status              = shopify2\lib\models\OrderTransaction::STATE_ERROR;

        $shopifyOrder->transactions[] = $trans;

        $trans                      = new shopify2\lib\models\OrderTransaction();
        $trans->id                  = 'gid://shopify/Order/6192004104365';
        $trans->kind                = shopify2\lib\models\OrderTransaction::KIND_SALE;
        $trans->gateway             = 'Peach payments';
        $trans->paymentId           = 'rwgEqwIr6qd7lMOUWIhDHjrmE';
        $trans->processedAt         = '2024-01-02T01:23:45';
        $trans->status              = shopify2\lib\models\OrderTransaction::STATE_FAILURE;

        $shopifyOrder->transactions[] = $trans;
        shopify2\order\TransformTransactions::setOrderTransactions($co, $shopifyOrder);

        // check transactions
        foreach ($co->transactions as $transaction) {
            $this->assertEquals(vo\Transaction::KIND_DEBIT, $transaction->kind);
            $this->assertEquals(vo\Transaction::STATE_ERROR, $transaction->state);

            // Verify that payment fields aren't set since setPaymentOnTransactionV2
            // should not be called when state is ERROR
            $this->assertEquals('2024-01-02 01:23:45', $transaction->completed_date);
            $this->assertNull($transaction->stan);
            $this->assertNull($transaction->auth_code);
            $this->assertNull($transaction->rrn);
            $this->assertNull($transaction->getMaskedCardNumber());
        }
    }
}
