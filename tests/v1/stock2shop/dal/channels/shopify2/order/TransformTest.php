<?php

namespace tests\v1\stock2shop\dal\channels\shopify2\order;

use Exception;
use stock2shop\dal\channels\shopify2;
use stock2shop\dal\channels\shopify2\apps\paxi\Paxi;
use stock2shop\dal\channels\shopify2\Configuration;
use stock2shop\dal\payments\peach\Payment;
use stock2shop\helpers\MetaManager;
use tests\v1\stock2shop\dal\channels\shopify2\TestCase;
use Throwable;

class TransformTest extends TestCase
{


    /**
     * @throws Exception
     * @throws Throwable
     */
    public function testTransform()
    {

        $this->initShopify2Test(
            [
                Payment::PEACH_PAYMENT_BASE_URL_KEY => 'https://peach.com'
            ],
            [
                Payment::PEACH_ENTITY_ID_KEY => 'abc',
                Payment::PEACH_SECRET_KEY => 'xyz'
            ]
        );
        $order = file_get_contents(__DIR__ . '/transformTestData/shopify-order-all-props.json');
        $shopifyOrder               = new shopify2\lib\models\Order(json_decode($order, true));
        $co = shopify2\order\Transform::toChannelOrder(
            $shopifyOrder,
            new shopify2\order\TransformArgs($shopifyOrder));

        // todo some assertions
        $this->assertEquals('3001044', $co->channel_order_code);

        // check shipping code meta
        $this->assertNotEmpty($co->shipping_lines);
        $this->assertNotEmpty($co->meta);
        $mm = new MetaManager([$co->meta]);
        $this->assertEquals('on', $mm->getValue('shopify_shipping_code'));
    }

    public function testTransformProvinceCodeNoFlag()
    {

        $this->initShopify2Test(
            [
                Payment::PEACH_PAYMENT_BASE_URL_KEY => 'https://peach.com'
            ],
            [
                Payment::PEACH_ENTITY_ID_KEY => 'abc',
                Payment::PEACH_SECRET_KEY => 'xyz'
            ]
        );
        $order = file_get_contents(__DIR__ . '/transformTestData/shopify-order-all-props.json');
        $shopifyOrder               = new shopify2\lib\models\Order(json_decode($order, true));
        $shopifyOrder->shippingAddress->provinceCode = 'NL';
        $shopifyOrder->billingAddress->provinceCode = 'NL';
        $co = shopify2\order\Transform::toChannelOrder(
            $shopifyOrder,
            new shopify2\order\TransformArgs($shopifyOrder)
        );
        $this->assertEquals('NL', $co->shipping_address->province_code);
        $this->assertEquals('NL', $co->billing_address->province_code);
    }

    public function testTransformProvinceCodeWithFlag()
    {

        $this->initShopify2Test(
            [
                Payment::PEACH_PAYMENT_BASE_URL_KEY => 'https://peach.com',
                Configuration::META_UPDATE_PROVINCE_CODE_FROM_NL_TO_KZN => 'true'
            ],
            [
                Payment::PEACH_ENTITY_ID_KEY => 'abc',
                Payment::PEACH_SECRET_KEY => 'xyz'
            ]
        );
        $order = file_get_contents(__DIR__ . '/transformTestData/shopify-order-all-props.json');
        $shopifyOrder               = new shopify2\lib\models\Order(json_decode($order, true));
        $shopifyOrder->shippingAddress->provinceCode = 'NL';
        $shopifyOrder->billingAddress->provinceCode = 'NL';
        $co = shopify2\order\Transform::toChannelOrder(
            $shopifyOrder,
            new shopify2\order\TransformArgs($shopifyOrder)
        );
        $this->assertEquals('KZN', $co->shipping_address->province_code);
    }

    public function testTransformOrderWithMeta()
    {

        $metafieldsToAdd = Configuration::getInstance()->getMetafields();

        // Check if the metafields fall back to the original ones if not set
        $this->assertCount(0, $metafieldsToAdd);
        $this->initShopify2Test(
            [
                Payment::PEACH_PAYMENT_BASE_URL_KEY => 'https://peach.com',
                Configuration::META_METAFIELDS_KEY => 'poNumber,does_not_exist',
                Paxi::META_APP_PAXI_ENABLED => 'true'
            ],
            [
                Payment::PEACH_ENTITY_ID_KEY => 'abc',
                Payment::PEACH_SECRET_KEY => 'xyz'
            ]
        );
        $metafieldsToAdd = Configuration::getInstance()->getMetafields();
        $this->assertCount(2, $metafieldsToAdd);
        $this->assertEquals('poNumber', $metafieldsToAdd[0]);
        $this->assertEquals('does_not_exist', $metafieldsToAdd[1]);
        $order = file_get_contents(__DIR__ . '/transformTestData/shopify_order_with_meta.json');
        $shopifyOrder               = new shopify2\lib\models\Order(json_decode($order, true));
        $co = shopify2\order\Transform::toChannelOrder(
            $shopifyOrder,
            new shopify2\order\TransformArgs($shopifyOrder)
        );

        $this->assertEmpty($co->shipping_lines);
        $this->assertCount(1, $co->meta);
        $mm = new MetaManager([$co->meta]);
        $this->assertNotEmpty($mm->getValue('paxi_address'));
    }
}
