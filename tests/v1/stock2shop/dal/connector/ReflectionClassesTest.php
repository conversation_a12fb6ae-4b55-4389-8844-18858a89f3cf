<?php

namespace tests\v1\stock2shop\dal\connector;

use ReflectionClass;
use ReflectionException;
use stock2shop\dal\connector\ConnectorConfiguration;
use stock2shop\dal\connector\ReflectionClasses;
use stock2shop\exceptions\UnprocessableEntity;
use stock2shop\vo;
use stock2shop\workflows\dependencies\DependencyInterface;
use stock2shop\workflows\dependencies\InboundCreated;
use tests\TestCase;
use tests\v1\stock2shop\dal\connector\hooks\DependencyTestHook;

class ReflectionClassesTest extends TestCase
{

    /**
     * @throws ReflectionException
     * @throws UnprocessableEntity
     */
    public function testGet()
    {
        $conf = ConnectorConfiguration::getInstance();
        $conf->setClientConfig(new vo\Config(['client_id' =>  1]));
        $action = ReflectionClasses::getClass(
            ReflectionClasses::NAMESPACE_WORKFLOW_DEPENDENCIES,
            InboundCreated::class,
            DependencyInterface::class
        );
        $this->assertInstanceOf(ReflectionClass::class, $action);
        $this->assertEquals(InboundCreated::class, $action->getName());
    }

    /**
     * @throws ReflectionException
     */
    public function testGetCustomDir()
    {
        $conf = ConnectorConfiguration::getInstance();
        $conf->setCustomClasses([new ReflectionClass(DependencyTestHook::class)]);
        $action = ReflectionClasses::getClass(
            ReflectionClasses::NAMESPACE_WORKFLOW_DEPENDENCIES,
            'DependencyTestHook',
            DependencyInterface::class
        );
        $this->assertInstanceOf(ReflectionClass::class, $action);
        $this->assertEquals(DependencyTestHook::class, $action->getName());
    }

    /**
     * @throws ReflectionException
     */
    public function testGetInvalidClass()
    {
        $conf = ConnectorConfiguration::getInstance();
        $conf->setCustomClasses([new ReflectionClass(DependencyTestHook::class)]);
        $this->expectException(ReflectionException::class);
        $action = ReflectionClasses::getClass(
            ReflectionClasses::NAMESPACE_WORKFLOW_DEPENDENCIES,
            'DoesNotExist',
            DependencyInterface::class
        );
    }

}
