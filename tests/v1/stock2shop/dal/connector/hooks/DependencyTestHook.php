<?php
namespace tests\v1\stock2shop\dal\connector\hooks;

use stock2shop\dal\connector\hooks\HookInterface;
use stock2shop\vo\Config;
use stock2shop\workflows\dependencies\DependencyContextInterface;
use stock2shop\workflows\dependencies\DependencyInterface;

class DependencyTestHook implements HookInterface, DependencyInterface
{

    public static function getConnectorIDs(): array
    {
        return [1];
    }

    public static function getConnectorKind(): string
    {
        return Config::CONNECTOR_FULFILLMENTSERVICE;
    }

    public function getDependencyString(): string
    {
        // used for testing
        return '';
    }

    public function getName(): string
    {
        // used for testing
        return '';
    }

    public function isMet(DependencyContextInterface $context): bool
    {
        // used for testing
        return true;
    }
}