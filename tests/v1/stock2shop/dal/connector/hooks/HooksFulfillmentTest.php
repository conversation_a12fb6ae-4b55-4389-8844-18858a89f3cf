<?php

namespace tests\v1\stock2shop\dal\connector\hooks;

use stock2shop\dal\connector\hooks\Hook;
use stock2shop\dal\connector\hooks\HookParcelNinjaBeforePostOutboundFulfillmentArgs;
use stock2shop\dal\connector\hooks\HookParcelNinjaBeforePostOutboundFulfillmentInterface;
use stock2shop\vo;

/**
 * Example of a custom class
 */
class HooksFulfillmentTest extends Hook implements HookParcelNinjaBeforePostOutboundFulfillmentInterface
{
    public static function getConnectorIDs(): array
    {
        return [1,2];
    }

    public static function getConnectorKind(): string
    {
        return vo\Config::CONNECTOR_FULFILLMENTSERVICE;
    }

    public static function beforePostOutboundFulfillment(HookParcelNinjaBeforePostOutboundFulfillmentArgs $beforePostArgs)
    {

    }
}
