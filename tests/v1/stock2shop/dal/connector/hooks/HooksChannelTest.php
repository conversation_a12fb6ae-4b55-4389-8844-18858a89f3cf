<?php

namespace tests\v1\stock2shop\dal\connector\hooks;

use stock2shop\dal\connector\hooks\Hook;
use stock2shop\dal\connector\hooks\HookShopify2TransformOrderArgs;
use stock2shop\dal\connector\hooks\HookShopify2TransformOrderInterface;
use stock2shop\vo;

/**
 * Example of a custom class
 */
class HooksChannelTest extends Hook implements HookShopify2TransformOrderInterface
{
    public static function getConnectorIDs(): array
    {
        return [1,2];
    }

    public static function getConnectorKind(): string
    {
        return vo\Config::CONNECTOR_CHANNEL;
    }

    public static function transformOrder(HookShopify2TransformOrderArgs $transformArgs)
    {
        $transformArgs->channelOrder->channel_order_code = 'foo';
    }


}
