<?php

namespace tests\v1\stock2shop\dal\connector\hooks;

use Exception;
use ReflectionException;
use stock2shop\dal\connector;
use stock2shop\dal\channels;
use stock2shop\dal\connector\hooks\HookShopify2TransformOrderInterface;
use stock2shop\exceptions\UnprocessableEntity;
use stock2shop\vo;
use tests\TestCase;

class HookTest extends TestCase
{
    /**
     * @throws UnprocessableEntity
     * @throws Exception
     */
    public function testGetCustomClientClasses()
    {
        $config        = new vo\Config([
            'client_id' => 1056,
            'channels'  => [
                [
                    'id'   => 2192,
                    'type' => 'test_channel'
                ]
            ]
        ]);
        $configuration = connector\ConnectorConfiguration::getInstance(true);
        $configuration->init($config, vo\Config::CONNECTOR_CHANNEL, 2192);
        $classes = $configuration->getCustomClasses();
        $this->assertCount(1, $classes);
        // check it inherits correct interface
        $interfaces = $classes[0]->getInterfaceNames();
        $this->assertTrue(in_array(connector\hooks\HookInterface::class, $interfaces));
        $this->assertTrue(in_array(connector\hooks\HookShopify2TransformOrderInterface::class, $interfaces));
    }

    /**
     * @throws UnprocessableEntity
     * @throws Exception
     */
    public function testGetCustomClientClassesEmpty()
    {
        $config        = new vo\Config([
            'client_id' => 1,
            'channels'  => [
                [
                    'id'   => 1,
                    'type' => 'test_channel'
                ]
            ]
        ]);
        $configuration = connector\ConnectorConfiguration::getInstance(true);
        $configuration->init($config, vo\Config::CONNECTOR_CHANNEL, 1);
        $classes = $configuration->getCustomClasses();
        $this->assertCount(0, $classes);
    }

    /**
     * @throws UnprocessableEntity
     * @throws Exception
     */
    public function testGetCustomClientClassesOverride()
    {
        $config        = new vo\Config([
            'client_id' => 1,
            'channels'  => [
                [
                    'id'   => 1,
                    'type' => 'test_channel'
                ]
            ]
        ]);
        $configuration = connector\ConnectorConfiguration::getInstance(true);
        $reflection    = new \ReflectionClass(
            'tests\v1\stock2shop\dal\connector\hooks\HooksChannelTest'
        );
        $configuration->setCustomClasses([$reflection]);
        $configuration->init($config, vo\Config::CONNECTOR_CHANNEL, 1);
        $classes    = $configuration->getCustomClasses();
        $interfaces = $classes[0]->getInterfaceNames();
        $this->assertCount(1, $classes);
        $this->assertTrue(in_array(HookShopify2TransformOrderInterface::class, $interfaces));
    }


    /**
     * @throws ReflectionException
     * @throws UnprocessableEntity
     * @throws Exception
     */
    public function testExecuteWithWrapper()
    {
        $config        = new vo\Config([
            'client_id' => 1,
            'channels'  => [
                [
                    'id'   => 1,
                    'type' => 'test_channel'
                ]
            ]
        ]);
        $configuration = connector\ConnectorConfiguration::getInstance(true);
        $configuration->setCustomClasses([
            new \ReflectionClass(
                'tests\v1\stock2shop\dal\connector\hooks\HooksChannelTest'
            )
        ]);
        $configuration->init($config, vo\Config::CONNECTOR_CHANNEL, 1);

        $channelOrder = new vo\ChannelOrder(['channel_id' => 1]);
        connector\hooks\Hook::execute(
            HookShopify2TransformOrderInterface::class,
            'transformOrder',
            [
                new connector\hooks\HookShopify2TransformOrderArgs(
                    $config,
                    $channelOrder,
                    new channels\shopify2\lib\models\Order()
                )
            ]
        );
        $this->assertEquals('foo', $channelOrder->channel_order_code);
    }
}
