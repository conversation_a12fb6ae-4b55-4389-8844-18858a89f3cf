<?php

namespace tests\v1\stock2shop\dal\connector;

use Exception;
use stock2shop\dal\connector\ConnectorConfiguration;
use stock2shop\dal\channels\amazon;
use stock2shop\exceptions\ServerError;
use stock2shop\exceptions\UnprocessableEntity;
use stock2shop\vo;
use tests\DB;
use tests\TestCase;

class ConnectorConfigurationTest extends TestCase
{
    const CLIENT_ID = 1;
    const CHANNEL_ID = 2;
    const CONFIG = [
        'client_id' => self::CLIENT_ID,
        'channels'  => [
            [
                'id'          => self::CHANNEL_ID,
                'description' => 'amazon',
                'type'        => 'amazon',
                'meta'        => [
                    [
                        'key'   => amazon\Configuration::IDENTIFIER_FIELD,
                        'value' => 'barcode'
                    ]
                ]
            ]
        ]
    ];

    /**
     * @throws Exception
     */
    public function testConfigurationIsSingleton()
    {

        $conf = amazon\Configuration::getInstance(true);
        $conf->init(new vo\Config(self::CONFIG), vo\Config::CONNECTOR_CHANNEL, self::CHANNEL_ID);
        $conf2 = ConnectorConfiguration::getInstance();
        $conf2->setConnectorId(99);
        $amz = amazon\Configuration::getInstance();
        $this->assertEquals($conf->getConnectorId(), $conf2->getConnectorId());
        $this->assertEquals($conf->getConnectorId(), $amz->getConnectorId());
    }

    /**
     * @throws Exception
     */
    public function testConfigurationLoading()
    {
        $amazonConf = amazon\Configuration::getInstance(true);
        $amazonConf->init(new vo\Config(self::CONFIG), 'channel', self::CHANNEL_ID);
        $conf = ConnectorConfiguration::getInstance();
        $this->assertEquals(
            $conf->getMetaRepository()->meta->getValue(amazon\Configuration::IDENTIFIER_FIELD),
            $amazonConf->getIdentifierField()
        );
    }

    /**
     * @throws Exception
     */
    public function testGetTokens() {
        $config = new vo\Config(self::CONFIG);
        $conf = amazon\Configuration::getInstance(true);
        DB::makeFromConfig($config);
        $conf->init($config, vo\Config::CONNECTOR_CHANNEL, self::CHANNEL_ID);
        $webhook = $conf->getUserWebhookToken();
        $this->assertEquals('webhook_' . self::CLIENT_ID, $webhook);
    }


}
