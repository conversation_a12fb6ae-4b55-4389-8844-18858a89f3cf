{
    // See https://go.microsoft.com/fwlink/?LinkId=733558
    // for the documentation about the tasks.json format
    "version": "2.0.0",
    "inputs": [
        {
            "id": "clientID",
            "description": "Enter the Client ID (used for DB name and dump filename, e.g., client_abc)",
            "type": "promptString",
            "default": ""
        },
        {
            "id": "dumpFilename",
            "description": "Enter the filename for the DB dump (e.g., dump.sql)",
            "type": "promptString",
            "default": "stock2shop_dump.sql"
        },
        {
            "id": "searchTerm",
            "description": "Enter the term to search for in config files",
            "type": "promptString",
            "default": ""
        },
        {
            "id": "packageName",
            "description": "Enter the package name (e.g., partner, evolution, odbc, omniaccounts, sapone, iqretail)",
            "type": "promptString",
            "default": ""
        },
        {
            "id": "testPath",
            "description": "Enter the path to the test file or directory (relative to package)",
            "type": "promptString",
            "default": "tests"
        },
        {
            "id": "setupCommand",
            "description": "Enter the setup client command",
            "type": "promptString",
            "default": ""
        },
        {
            "id": "branchName",
            "description": "Enter the branch name",
            "type": "promptString",
            "default": ""
        }
    ],
    "tasks": [
        {
            "label": "Start Local Environment",
            "type": "shell",
            // Runs up.sh and then injects aliases into container, like the s2sup alias
            "command": "./dev-setup/up.sh && ./s2salias.sh",
            "problemMatcher": [],
            "detail": "Starts services via Docker Compose and injects s2s aliases into the app container.",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            },
            "options": {
                "cwd": "${workspaceFolder}"
            }
        },
        {
            "label": "Stop Local Environment",
            "type": "shell",
            "command": "./dev-setup/down.sh",
            "problemMatcher": [],
            "detail": "Runs the script to stop all local development services.",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            },
            "options": {
                "cwd": "${workspaceFolder}"
            }
        },
        {
            "label": "Reset Local Environment",
            "type": "shell",
            "command": "./dev-setup/reset.sh",
            "problemMatcher": [],
            "detail": "Runs the script to reset the local development environment.",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            },
            "options": {
                "cwd": "${workspaceFolder}"
            }
        },
        {
            "label": "Run Tests (inside container)",
            "type": "shell",
            // Note: This replicates the alias logic, adjust if paths or commands differ slightly
            "command": "docker exec s2s-app bash -c 'cd /var/www/stock2shop/app/tests && ./up.sh && php /var/www/stock2shop/app/www/v1/cron/elasticSearchReIndex.php --index_prefix customers && php /var/www/stock2shop/app/www/v1/cron/elasticSearchReIndex.php --index_prefix orders && php /var/www/stock2shop/app/www/v1/cron/elasticSearchReIndex.php --index_prefix products && php /var/www/stock2shop/app/www/v1/cron/elasticSearchReIndex.php --index_prefix ymm && export S2S_TEST_MODE=offline && /var/www/stock2shop/app/tests/phpunit.phar /var/www/stock2shop/app/tests/ && ./down.sh'",
            "problemMatcher": [],
            "detail": "Runs the main test suite sequence inside the s2s-app container.",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            }
        },
        {
            "label": "Re-index Products (inside container)",
            "type": "shell",
            "command": "docker exec s2s-app bash -c 'php /var/www/stock2shop/app/www/v1/cron/elasticSearchReIndex.php --index_prefix products'",
            "problemMatcher": [],
            "detail": "Runs Elasticsearch product re-indexing inside the s2s-app container.",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            }
        },
        {
            "label": "Enable Xdebug (inside container)",
            "type": "shell",
            "command": "docker exec s2s-app bash -c '/var/www/stock2shop/app/tests/xdebug.up.sh'",
            "problemMatcher": [],
            "detail": "Enables Xdebug inside the s2s-app container.",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            }
        },
        {
            "label": "Disable Xdebug (inside container)",
            "type": "shell",
            "command": "docker exec s2s-app bash -c '/var/www/stock2shop/app/tests/xdebug.down.sh'",
            "problemMatcher": [],
            "detail": "Disables Xdebug inside the s2s-app container.",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            }
        },
        {
            "label": "Load Client DB Dump",
            "type": "shell",
            "command": "mysql -h 127.0.0.1 -P 3306 -u root -pasdf ${input:clientID} < /Users/<USER>/Documents/Stock2Shop/S2S_Database_dumps/${input:clientID}.sql",
            "problemMatcher": [],
            "detail": "Loads a specified client DB dump from the host into the local MySQL container.",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            }
        },
        // --- New tasks from .zshrc ---
        {
            "label": "Update Core Apps (Git)",
            "type": "shell",
            // Runs git checkout master && git pull in core app directories
            "command": "(cd ./b2b && git checkout master && git pull) && (cd ./console && git checkout master && git pull) && (cd ./sim && git checkout master && git pull) && (cd ./app && git checkout master && git pull)",
            "problemMatcher": [],
            "detail": "Runs 'git checkout master && git pull' in b2b, console, sim, and app directories.",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            },
            "options": {
                "cwd": "${workspaceFolder}"
            }
        },
        {
            "label": "Login to DB (mysqlsh)",
            "type": "shell",
            "command": "mysqlsh --sqlc --host=127.0.0.1 --port=3306 -u root -pasdf",
            "problemMatcher": [],
            "detail": "Connects to the local MySQL container using mysqlsh.",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            }
        },
        {
            "label": "Dump DB",
            "type": "shell",
            // Dumps to a file in the workspace root
            "command": "docker exec mysql /usr/bin/mysqldump -u root --password=asdf stock2shop > ${workspaceFolder}/${input:dumpFilename}",
            "problemMatcher": [],
            "detail": "Dumps the 'stock2shop' database from the mysql container to a specified file.",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            }
        },
        {
            "label": "Login to s2s-app Container",
            "type": "shell",
            "command": "docker exec -it s2s-app bash",
            "problemMatcher": [],
            "detail": "Opens an interactive bash shell inside the s2s-app container.",
            "presentation": {
                "reveal": "always",
                "panel": "new" // 'new' panel is often better for interactive shells
            }
        },
        {
            "label": "Search Config",
            "type": "shell",
            "command": "grep -r '${input:searchTerm}' /Users/<USER>/Documents/Stock2Shop/Pycharmprojects/apifact/repos/config || true", // Add || true to prevent grep error code from failing task if no match
            "problemMatcher": [],
            "detail": "Searches for a term in the PycharmProjects config directory.",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            }
        },
        {
            "label": "Setup Test Firewall (inside container)",
            "type": "shell",
            "command": "docker exec -it s2s-app /root/stock2shop/app/tests/up.sh",
            "problemMatcher": [],
            "detail": "Runs the test firewall setup script inside the s2s-app container.",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            }
        },
        {
            "label": "Start Woo Environment",
            "type": "shell",
            "command": "./dev-setup/woo-up.sh",
            "problemMatcher": [],
            "detail": "Starts the WooCommerce development environment.",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            },
            "options": {
                "cwd": "${workspaceFolder}"
            }
        },
        {
            "label": "Init Woo Environment",
            "type": "shell",
            "command": "./dev-setup/woo-init.sh",
            "problemMatcher": [],
            "detail": "Initializes the WooCommerce development environment.",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            },
            "options": {
                "cwd": "${workspaceFolder}"
            }
        },
        {
            "label": "Stop Woo Environment",
            "type": "shell",
            "command": "./dev-setup/woo-down.sh",
            "problemMatcher": [],
            "detail": "Stops the WooCommerce development environment.",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            },
            "options": {
                "cwd": "${workspaceFolder}"
            }
        },
        {
            "label": "Update Apifact Core",
            "type": "shell",
            "command": "cd ~/Documents/Stock2Shop/PycharmProjects/apifact/repos && git checkout master && git pull",
            "problemMatcher": [],
            "detail": "Updates the main Apifact repository (checkout master and pull).",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            }
        },
        {
            "label": "Update Apifact Package",
            "type": "shell",
            "command": "cd ~/Documents/Stock2Shop/PycharmProjects/apifact/repos/connector/packages/gomedia_${input:packageName} && git checkout master && git pull",
            "problemMatcher": [],
            "detail": "Updates a specific Apifact package (checkout master and pull).",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            }
        },
        {
            "label": "Generate Client Files",
            "type": "shell",
            "command": "python3 ~/Documents/Stock2Shop/PycharmProjects/apifact/repos/script/setupClientTemplate.py ${input:clientID}",
            "problemMatcher": [],
            "detail": "Generates client template files using setupClientTemplate.py script.",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            }
        },
        {
            "label": "Update Billing Scripts",
            "type": "shell",
            "command": "cd ~/Documents/Stock2Shop/PycharmProjects/s2s-billing-data-scripts && git checkout master && git pull",
            "problemMatcher": [],
            "detail": "Updates the billing scripts repository (checkout master and pull).",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            }
        },
        // --- Additional recommended tasks ---
        {
            "label": "Run Apifact Package Tests",
            "type": "shell",
            "command": "cd ~/Documents/Stock2Shop/PycharmProjects/apifact/repos/connector/packages/gomedia_${input:packageName} && python -m pytest ${input:testPath} -v",
            "problemMatcher": [],
            "detail": "Runs tests for a specific Apifact package.",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            }
        },
        {
            "label": "Setup Client (Remote)",
            "type": "shell",
            "command": "~/Documents/Stock2Shop/PycharmProjects/apifact/repos/script/remoteSetupClient.sh ~/.ssh/id_rsa ${input:setupCommand}",
            "problemMatcher": [],
            "detail": "Sets up a client on a remote server using SSH key.",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            }
        },
        {
            "label": "Create App Branch",
            "type": "shell",
            "command": "cd ~/stock2shop/app && git checkout master && git pull && git checkout -b ${input:branchName}",
            "problemMatcher": [],
            "detail": "Creates a new branch in the app repository based on master.",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            }
        },
        {
            "label": "Load DB with stock2shop- prefix",
            "type": "shell",
            "command": "cd ~/Documents/Stock2Shop/S2S_Database_dumps && mysqlsh --sqlc --host=127.0.0.1 --port=3306 -u root -pasdf <<< \"drop database stock2shop; create database stock2shop; use stock2shop; source stock2shop-${input:clientID}.sql;\"",
            "problemMatcher": [],
            "detail": "Loads a DB dump with the 'stock2shop-' prefix into the stock2shop database.",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            }
        },
        {
            "label": "Re-index All (inside container)",
            "type": "shell",
            "command": "docker exec s2s-app bash -c 'php /var/www/stock2shop/app/www/v1/cron/elasticSearchReIndex.php --index_prefix products && php /var/www/stock2shop/app/www/v1/cron/elasticSearchReIndex.php --index_prefix customers && php /var/www/stock2shop/app/www/v1/cron/elasticSearchReIndex.php --index_prefix orders && php /var/www/stock2shop/app/www/v1/cron/elasticSearchReIndex.php --index_prefix ymm'",
            "problemMatcher": [],
            "detail": "Runs re-indexing for all indices (products, customers, orders, ymm) inside the s2s-app container.",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            }
        },
        {
            "label": "Re-index Customers (inside container)",
            "type": "shell",
            "command": "docker exec s2s-app bash -c 'php /var/www/stock2shop/app/www/v1/cron/elasticSearchReIndex.php --index_prefix customers'",
            "problemMatcher": [],
            "detail": "Runs Elasticsearch customer re-indexing inside the s2s-app container.",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            }
        },
        {
            "label": "Re-index Orders (inside container)",
            "type": "shell",
            "command": "docker exec s2s-app bash -c 'php /var/www/stock2shop/app/www/v1/cron/elasticSearchReIndex.php --index_prefix orders'",
            "problemMatcher": [],
            "detail": "Runs Elasticsearch order re-indexing inside the s2s-app container.",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            }
        },
        {
            "label": "Re-index YMM (inside container)",
            "type": "shell",
            "command": "docker exec s2s-app bash -c 'php /var/www/stock2shop/app/www/v1/cron/elasticSearchReIndex.php --index_prefix ymm'",
            "problemMatcher": [],
            "detail": "Runs Elasticsearch YMM re-indexing inside the s2s-app container.",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            }
        },
        {
            "label": "Start Magento Environment",
            "type": "shell",
            "command": "./dev-setup/magento-up.sh",
            "problemMatcher": [],
            "detail": "Starts the Magento development environment.",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            },
            "options": {
                "cwd": "${workspaceFolder}"
            }
        },
        {
            "label": "Init Magento Environment",
            "type": "shell",
            "command": "./dev-setup/magento-init.sh",
            "problemMatcher": [],
            "detail": "Initializes the Magento development environment.",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            },
            "options": {
                "cwd": "${workspaceFolder}"
            }
        },
        {
            "label": "Reset Magento Environment",
            "type": "shell",
            "command": "./dev-setup/magento-reset.sh",
            "problemMatcher": [],
            "detail": "Resets the Magento development environment.",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            },
            "options": {
                "cwd": "${workspaceFolder}"
            }
        },
        {
            "label": "Stop Magento Environment",
            "type": "shell",
            "command": "./dev-setup/magento-down.sh",
            "problemMatcher": [],
            "detail": "Stops the Magento development environment.",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            },
            "options": {
                "cwd": "${workspaceFolder}"
            }
        }
    ]
} 