/*
 *  /MathJax-v2/localization/sco/HTML-CSS.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Localization.addTranslation("sco","HTML-CSS",{version:"2.7.9",isLoaded:true,strings:{LoadWebFont:"Laidin wab font %1",CantLoadWebFont:"Canna laid wab font %1",FirefoxCantLoadWebFont:"Firefox canna laid wab fonts fae ae remote host",CantFindFontUsing:"Canna fynd ae valid font uisin %1",WebFontsNotAvailable:"Wab fonts no available. Uisin eimage fonts instead"}});MathJax.Ajax.loadComplete("[MathJax]/localization/sco/HTML-CSS.js");
