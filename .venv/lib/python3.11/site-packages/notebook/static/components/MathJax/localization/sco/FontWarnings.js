/*
 *  /MathJax-v2/localization/sco/FontWarnings.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Localization.addTranslation("sco","FontWarnings",{version:"2.7.9",isLoaded:true,strings:{webFont:"MathJax is uisin wab-based fonts tae displey the mathematics oan this page.  Thir tak time tae doonlaid, sae the page wid render faster gif ye installed maths fonts directlie in yer system's font fauder.",imageFonts:"MathJax is uisin its eemage fonts insteid o local or wab-based fonts. This will render slawer than uisual, n the mathematics micht no prent at the ful resolution o yer prenter.",noFonts:"MathJax is onable tae locate ae font tae uise tae displey its mathematics, n eemage fonts ar no available, sae it is fallin back oan generic Unicode chairacters in hopes that yer brouser will be able tae displey thaim. Some chairacters michtna shaw up properlie, or possiblie no at aw.",webFonts:"Maist modern brousers permit fonts tae be doonlaided ower the wab. Updatin til ae mair recent version o yer brouser (or chyngin brousers) coud impruiv the qualitie o the mathematics oan this page.",fonts:"MathJax can uise either the [STIX fonts](%1) or the [MathJax TeX fonts](%2).  Doonlaid n install yin o thae fonts tae impruiv yer MathJax experience.",STIXPage:"This page is designed tae uise the [STIX fonts](%1).  Doonlaid n install thae fonts tae impruiv yer MathJax experience.",TeXPage:"This page is designed tae uise the [MathJax TeX fonts](%1).  Doonlaid n install thae fonts tae impruiv yer MathJax experiance."}});MathJax.Ajax.loadComplete("[MathJax]/localization/sco/FontWarnings.js");
