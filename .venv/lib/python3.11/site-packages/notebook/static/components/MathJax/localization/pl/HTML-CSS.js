/*
 *  /MathJax-v2/localization/pl/HTML-CSS.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Localization.addTranslation("pl","HTML-CSS",{version:"2.7.9",isLoaded:true,strings:{LoadWebFont:"\u0141aduj\u0119 czcionk\u0119 %1",CantLoadWebFont:"Nie mo\u017Cna za\u0142adowa\u0107 czcionki %1",FirefoxCantLoadWebFont:"Firefox nie mo\u017Ce za\u0142adowa\u0107 czcionek ze zdalnego hosta",CantFindFontUsing:"Nie mo\u017Cna znale\u017A\u0107 w\u0142a\u015Bciwej czcionki u\u017Cywaj\u0105c %1",WebFontsNotAvailable:"Czcionki internetowe nie dost\u0119pne - zamiast tego u\u017Cywane s\u0105 czcionki obrazkowe"}});MathJax.Ajax.loadComplete("[MathJax]/localization/pl/HTML-CSS.js");
