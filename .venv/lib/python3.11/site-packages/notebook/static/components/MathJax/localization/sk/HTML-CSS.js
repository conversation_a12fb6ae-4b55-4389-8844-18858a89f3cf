/*
 *  /MathJax-v2/localization/sk/HTML-CSS.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Localization.addTranslation("sk","HTML-CSS",{version:"2.7.9",isLoaded:true,strings:{LoadWebFont:"Na\u010D\u00EDtava sa webov\u00FD font %1",CantLoadWebFont:"Nepodarilo sa na\u010D\u00EDta\u0165 webov\u00FD font %1",FirefoxCantLoadWebFont:"Firefox nedok\u00E1\u017Ee na\u010D\u00EDta\u0165 webov\u00E9 fonty zo vzdialen\u00E9ho hostite\u013Ea",CantFindFontUsing:"Nepodarilo sa n\u00E1js\u0165 platn\u00FD font pomocou %1",WebFontsNotAvailable:"Webov\u00E9 fonty nie s\u00FA k dispoz\u00EDcii. Namiesto toho s\u00FA pou\u017Eit\u00E9 obr\u00E1zkov\u00E9 fonty."}});MathJax.Ajax.loadComplete("[MathJax]/localization/sk/HTML-CSS.js");
