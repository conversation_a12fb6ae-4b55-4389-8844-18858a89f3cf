/*
 *  /MathJax-v2/localization/ja/HTML-CSS.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Localization.addTranslation("ja","HTML-CSS",{version:"2.7.9",isLoaded:true,strings:{LoadWebFont:"Web \u30D5\u30A9\u30F3\u30C8 %1 \u3092\u8AAD\u307F\u8FBC\u307F\u4E2D",CantLoadWebFont:"Web \u30D5\u30A9\u30F3\u30C8 %1 \u3092\u8AAD\u307F\u8FBC\u3081\u307E\u305B\u3093",FirefoxCantLoadWebFont:"Firefox \u306F\u3001\u30EA\u30E2\u30FC\u30C8 \u30DB\u30B9\u30C8\u306E Web \u30D5\u30A9\u30F3\u30C8\u3092\u8AAD\u307F\u8FBC\u3081\u307E\u305B\u3093",CantFindFontUsing:"%1 \u3067\u6709\u52B9\u306A\u30D5\u30A9\u30F3\u30C8\u304C\u898B\u3064\u304B\u308A\u307E\u305B\u3093",WebFontsNotAvailable:"Web \u30D5\u30A9\u30F3\u30C8\u3092\u5229\u7528\u3067\u304D\u307E\u305B\u3093\u3002\u4EE3\u308F\u308A\u306B\u753B\u50CF\u30D5\u30A9\u30F3\u30C8\u3092\u4F7F\u7528\u3057\u307E\u3059"}});MathJax.Ajax.loadComplete("[MathJax]/localization/ja/HTML-CSS.js");
