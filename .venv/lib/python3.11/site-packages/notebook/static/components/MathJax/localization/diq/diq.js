/*
 *  /MathJax-v2/localization/diq/diq.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Localization.addTranslation("diq",null,{menuTitle:"Zazaki",version:"2.7.9",isLoaded:true,domains:{_:{version:"2.7.9",isLoaded:true,strings:{MathProcessingError:"X\u0131raba kar\u00EA matematiki",MathError:"Xetay matematiki",LoadFile:"%1 Bar keri",Loading:"Bar beno",LoadFailed:"Dosyay bar nebi: %1",ProcessMath:"Matematik neq\u0131\u015F kerden: %1%%",Processing:"Kar\u00EAno...",TypesetMath:"Tewr\u00EA eyar\u00EA matematiki:%1",Typesetting:"Eyar\u00EA tewri",MathJaxNotSupported:"Rov\u0131tera \u015F\u0131ma MathJax'i n\u00EAgurweyne na"}},FontWarnings:{},"HTML-CSS":{},HelpDialog:{},MathML:{},MathMenu:{},TeX:{}},plural:function(a){return 1},number:function(a){return a}});MathJax.Ajax.loadComplete("[MathJax]/localization/diq/diq.js");
