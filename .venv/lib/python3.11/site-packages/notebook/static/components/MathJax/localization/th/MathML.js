/*
 *  /MathJax-v2/localization/th/MathML.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Localization.addTranslation("th","MathML",{version:"2.7.9",isLoaded:true,strings:{BadMglyph:"mglyph \u0E1C\u0E34\u0E14\u0E1E\u0E25\u0E32\u0E14: %1",BadMglyphFont:"\u0E41\u0E1A\u0E1A\u0E2D\u0E31\u0E01\u0E29\u0E23\u0E1C\u0E34\u0E14\u0E1E\u0E25\u0E32\u0E14: %1",MathPlayer:"MathJax \u0E44\u0E21\u0E48\u0E2A\u0E32\u0E21\u0E32\u0E23\u0E16\u0E15\u0E31\u0E49\u0E07\u0E04\u0E48\u0E32 MathPlayer \u0E44\u0E14\u0E49\n\n\u0E16\u0E49\u0E32\u0E17\u0E48\u0E32\u0E19\u0E44\u0E21\u0E48\u0E44\u0E14\u0E49\u0E15\u0E34\u0E14\u0E15\u0E31\u0E49\u0E07 MathPlayer \u0E02\u0E2D\u0E43\u0E2B\u0E49\u0E14\u0E33\u0E40\u0E19\u0E34\u0E19\u0E01\u0E32\u0E23\u0E01\u0E48\u0E2D\u0E19\n\u0E21\u0E34\u0E09\u0E30\u0E19\u0E31\u0E49\u0E19\u0E01\u0E32\u0E23\u0E15\u0E31\u0E49\u0E07\u0E04\u0E48\u0E32\u0E04\u0E27\u0E32\u0E21\u0E1B\u0E25\u0E2D\u0E14\u0E20\u0E31\u0E22\u0E08\u0E30\u0E01\u0E35\u0E14\u0E01\u0E31\u0E19\u0E21\u0E34\u0E43\u0E2B\u0E49\u0E15\u0E31\u0E27\u0E04\u0E27\u0E1A\u0E04\u0E38\u0E21 ActiveX\n\u0E17\u0E33\u0E07\u0E32\u0E19\u0E44\u0E14\u0E49 \u0E40\u0E02\u0E49\u0E32\u0E44\u0E1B\u0E17\u0E35\u0E48\u0E40\u0E21\u0E19\u0E39 Tools \u0E41\u0E25\u0E49\u0E27\u0E40\u0E25\u0E37\u0E2D\u0E01 Internet Options \u0E08\u0E32\u0E01\u0E19\u0E31\u0E49\u0E19\u0E40\u0E25\u0E37\u0E2D\u0E01\n\u0E41\u0E17\u0E47\u0E1A Security \u0E41\u0E25\u0E49\u0E27\u0E40\u0E25\u0E37\u0E2D\u0E01 Custom Level \u0E01\u0E48\u0E2D\u0E19\u0E08\u0E30\u0E17\u0E33\u0E40\u0E04\u0E23\u0E37\u0E48\u0E2D\u0E07\u0E2B\u0E21\u0E32\u0E22\u0E16\u0E39\u0E01\u0E17\u0E35\u0E48\n'Run ActiveX Controls' \u0E41\u0E25\u0E30 'Binary and script behaviors'\n\n\u0E13 \u0E02\u0E13\u0E30\u0E19\u0E35\u0E49\u0E17\u0E48\u0E32\u0E19\u0E08\u0E30\u0E40\u0E2B\u0E47\u0E19\u0E02\u0E49\u0E2D\u0E04\u0E27\u0E32\u0E21\u0E1C\u0E34\u0E14\u0E1E\u0E25\u0E32\u0E14\u0E41\u0E17\u0E19\u0E17\u0E35\u0E48\u0E08\u0E30\u0E40\u0E1B\u0E47\u0E19\u0E2A\u0E21\u0E01\u0E32\u0E23\u0E04\u0E13\u0E34\u0E15\u0E28\u0E32\u0E2A\u0E15\u0E23\u0E4C",CantCreateXMLParser:"MathJax \u0E44\u0E21\u0E48\u0E2A\u0E32\u0E21\u0E32\u0E23\u0E16\u0E2A\u0E23\u0E49\u0E32\u0E07\u0E15\u0E31\u0E27\u0E41\u0E1A\u0E48\u0E07\u0E41\u0E25\u0E30\u0E2D\u0E48\u0E32\u0E19 XML \u0E2A\u0E33\u0E2B\u0E23\u0E31\u0E1A\u0E43\u0E0A\u0E49\u0E43\u0E19 MathML \u0E42\u0E1B\u0E23\u0E14\u0E15\u0E23\u0E27\u0E08\u0E2A\u0E2D\u0E1A\u0E27\u0E48\u0E32\n\u0E01\u0E32\u0E23\u0E15\u0E31\u0E49\u0E07\u0E04\u0E48\u0E32\u0E04\u0E27\u0E32\u0E21\u0E1B\u0E25\u0E2D\u0E14\u0E20\u0E31\u0E22 'Script ActiveX controls marked safe for scripting'\n\u0E16\u0E39\u0E01\u0E40\u0E1B\u0E34\u0E14\u0E2D\u0E22\u0E39\u0E48 (\u0E40\u0E02\u0E49\u0E32\u0E44\u0E1B\u0E17\u0E35\u0E48\u0E40\u0E21\u0E19\u0E39 Tools \u0E40\u0E25\u0E37\u0E2D\u0E01 Internet Options \u0E08\u0E32\u0E01\u0E19\u0E31\u0E49\u0E19\u0E44\u0E1B\u0E17\u0E35\u0E48\u0E41\u0E17\u0E47\u0E1A Security \n\u0E41\u0E25\u0E49\u0E27\u0E40\u0E25\u0E37\u0E2D\u0E01 Custom Level \u0E40\u0E1E\u0E37\u0E48\u0E2D\u0E14\u0E33\u0E40\u0E19\u0E34\u0E19\u0E01\u0E32\u0E23\u0E15\u0E48\u0E2D\u0E44\u0E1B).\n\n\u0E2A\u0E21\u0E01\u0E32\u0E23 MathML \u0E08\u0E30\u0E44\u0E21\u0E48\u0E2A\u0E32\u0E21\u0E32\u0E23\u0E16\u0E16\u0E39\u0E01\u0E1B\u0E23\u0E30\u0E21\u0E27\u0E25\u0E1C\u0E25\u0E44\u0E14\u0E49\u0E42\u0E14\u0E22 MathJax",UnknownNodeType:"\u0E0A\u0E19\u0E34\u0E14\u0E42\u0E2B\u0E19\u0E14\u0E44\u0E21\u0E48\u0E17\u0E23\u0E32\u0E1A: %1",UnexpectedTextNode:"\u0E42\u0E2B\u0E19\u0E14\u0E02\u0E49\u0E2D\u0E04\u0E27\u0E32\u0E21\u0E17\u0E35\u0E48\u0E44\u0E21\u0E48\u0E23\u0E39\u0E49\u0E08\u0E31\u0E01: %1",ErrorParsingMathML:"\u0E01\u0E32\u0E23\u0E41\u0E1A\u0E48\u0E07\u0E15\u0E2D\u0E19\u0E41\u0E25\u0E30\u0E2D\u0E48\u0E32\u0E19\u0E04\u0E33\u0E2A\u0E31\u0E48\u0E07 (parsing) MathML \u0E1C\u0E34\u0E14\u0E1E\u0E25\u0E32\u0E14",ParsingError:"\u0E01\u0E32\u0E23\u0E41\u0E1A\u0E48\u0E07\u0E15\u0E2D\u0E19\u0E41\u0E25\u0E30\u0E2D\u0E48\u0E32\u0E19\u0E04\u0E33\u0E2A\u0E31\u0E48\u0E07 MathML: %1 \u0E1C\u0E34\u0E14\u0E1E\u0E25\u0E32\u0E14",MathMLSingleElement:"MathML \u0E15\u0E49\u0E2D\u0E07\u0E2D\u0E22\u0E39\u0E48\u0E20\u0E32\u0E22\u0E43\u0E15\u0E49 \u003Cmath\u003E \u0E40\u0E1E\u0E35\u0E22\u0E07\u0E2D\u0E31\u0E19\u0E40\u0E14\u0E35\u0E22\u0E27\u0E08\u0E30\u0E0B\u0E49\u0E33\u0E0B\u0E49\u0E2D\u0E19\u0E44\u0E21\u0E48\u0E44\u0E14\u0E49",MathMLRootElement:"MathML \u0E15\u0E49\u0E2D\u0E07\u0E2D\u0E22\u0E39\u0E48\u0E20\u0E32\u0E22\u0E43\u0E15\u0E49 \u003Cmath\u003E \u0E44\u0E21\u0E48\u0E43\u0E0A\u0E48 %1"}});MathJax.Ajax.loadComplete("[MathJax]/localization/th/MathML.js");
