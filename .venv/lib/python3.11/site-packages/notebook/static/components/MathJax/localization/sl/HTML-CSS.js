/*
 *  /MathJax-v2/localization/sl/HTML-CSS.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Localization.addTranslation("sl","HTML-CSS",{version:"2.7.9",isLoaded:true,strings:{LoadWebFont:"Nalagam spletni font %1",CantLoadWebFont:"Spletne pisave %1 ni mogo\u010De nalo\u017Eiti",FirefoxCantLoadWebFont:"Firefox ne more nalo\u017Eiti spletnih pisav na oddaljenem gostitelju.",CantFindFontUsing:"Z uporabo %1 ne morem najti veljavne pisave.",WebFontsNotAvailable:"Web-Fonts niso na razpolago. Namesto njih uporabljam slikovne pisave."}});MathJax.Ajax.loadComplete("[MathJax]/localization/sl/HTML-CSS.js");
