/*
 *  /MathJax-v2/localization/uk/HTML-CSS.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Localization.addTranslation("uk","HTML-CSS",{version:"2.7.9",isLoaded:true,strings:{LoadWebFont:"\u0417\u0430\u0432\u0430\u043D\u0442\u0430\u0436\u0435\u043D\u043D\u044F \u0432\u0435\u0431 \u0448\u0440\u0438\u0444\u0442\u0456\u0432 %1",CantLoadWebFont:"\u041D\u0435 \u0432\u0434\u0430\u043B\u043E\u0441\u044F \u0437\u0430\u0432\u0430\u043D\u0442\u0430\u0436\u0438\u0442\u0438 \u0432\u0435\u0431 \u0448\u0440\u0438\u0444\u0442 %1",FirefoxCantLoadWebFont:"Firefox \u043D\u0435 \u043C\u043E\u0436\u0435 \u0437\u0430\u0432\u0430\u043D\u0442\u0430\u0436\u0438\u0442\u0438 \u0432\u0435\u0431 \u0448\u0440\u0438\u0444\u0442\u0438 \u0437 \u0432\u0456\u0434\u0434\u0430\u043B\u0435\u043D\u043E\u0433\u043E \u0445\u043E\u0441\u0442\u0443",CantFindFontUsing:"\u041D\u0435 \u0432\u0434\u0430\u043B\u043E\u0441\u044F \u0437\u043D\u0430\u0439\u0442\u0438 \u043F\u0440\u0430\u0432\u0438\u043B\u044C\u043D\u0438\u0439 \u0448\u0440\u0438\u0444\u0442 \u0437\u0430 \u0434\u043E\u043F\u043E\u043C\u043E\u0433\u043E\u044E %1",WebFontsNotAvailable:"\u0412\u0435\u0431-\u0448\u0440\u0438\u0444\u0442\u0438 \u043D\u0435\u0434\u043E\u0441\u0442\u0443\u043F\u043D\u0456. \u0421\u043B\u0456\u0434 \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0432\u0430\u0442\u0438 \u0448\u0440\u0438\u0444\u0442\u0438 -\u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u043D\u044F."}});MathJax.Ajax.loadComplete("[MathJax]/localization/uk/HTML-CSS.js");
