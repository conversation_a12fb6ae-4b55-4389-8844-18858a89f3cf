/*
 *  /MathJax-v2/localization/sv/HTML-CSS.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Localization.addTranslation("sv","HTML-CSS",{version:"2.7.9",isLoaded:true,strings:{LoadWebFont:"L\u00E4ser in webbtypsnittet %1",CantLoadWebFont:"Kan inte l\u00E4sa in webbtypsnittet %1",FirefoxCantLoadWebFont:"Firefox kan inte l\u00E4sa in webbteckensnitt fr\u00E5n en fj\u00E4rrv\u00E4rd",CantFindFontUsing:"Kan inte hitta ett giltigt teckensnitt med hj\u00E4lp av %1",WebFontsNotAvailable:"Webbtypsnitt \u00E4r inte tillg\u00E4ngliga. Anv\u00E4nder bildtypsnitt ist\u00E4llet"}});MathJax.Ajax.loadComplete("[MathJax]/localization/sv/HTML-CSS.js");
