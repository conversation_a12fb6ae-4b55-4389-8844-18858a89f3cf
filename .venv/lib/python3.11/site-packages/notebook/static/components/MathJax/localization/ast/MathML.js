/*
 *  /MathJax-v2/localization/ast/MathML.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Localization.addTranslation("ast","MathML",{version:"2.7.9",isLoaded:true,strings:{BadMglyph:"Mglyph incorreutu: %1",BadMglyphFont:"Tipograf\u00EDa incorreuta: %1",MathPlayer:"MathJax nun pudo configurar MathPlayer.\n\nSi MathPlayer nun ta instal\u00E1u, necesitar\u00E1 instalalu primero. D'otra manera, la so configuraci\u00F3n de segurid\u00E1 pue torgar la execuci\u00F3n de controles ActiveX. Use l'elementu Opciones d'Internet baxo'l men\u00FA Ferramientes y seleicione la lling\u00FCeta Segurid\u00E1, darr\u00E9u calque nel bot\u00F3n Nivel Personaliz\u00E1u. Compruebe que les preferencies pa 'Executar controles ActiveX' y 'Comportamientos de binariu y script' tan activaes.\n\nAngua\u00F1o ver\u00E1 mensaxes d'error en llugar de f\u00F3rmules matem\u00E1tiques.",CantCreateXMLParser:"MathJax nun pue crear un analizador XML pa MathML. Compruebe que\nla configuraci\u00F3n de segurid\u00E1 \u00ABControles de script ActiveX marcaos como seguros pa scripting\u00BB\nta activada (use l'elementu Opciones d'Internet del men\u00FA Ferramientes,\ny seleicione'l panel Segurid\u00E1, y darr\u00E9u calque nel bot\u00F3n Nivel personaliz\u00E1u pa comprobalo).\n\nMathJax nun podr\u00E1 procesar les ecuaciones MathML",UnknownNodeType:"Tipu de nuedu desconoc\u00EDu: %1",UnexpectedTextNode:"Nuedu de testu inesper\u00E1u: %1",ErrorParsingMathML:"Error al analizar MathML",ParsingError:"Error al analizar MathML: %1",MathMLSingleElement:"MathML tien de tar form\u00E1u por un \u00FAnicu elementu",MathMLRootElement:"MathML tien de tar form\u00E1u por un elementu \u003Cmath\u003E, non por %1"}});MathJax.Ajax.loadComplete("[MathJax]/localization/ast/MathML.js");
