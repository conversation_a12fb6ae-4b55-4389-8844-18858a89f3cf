/*
 *  /MathJax-v2/localization/lki/HelpDialog.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Localization.addTranslation("lki","HelpDialog",{version:"2.7.9",isLoaded:true,strings:{Help:"\u0631\u0627\u0647\u0646\u0645\u0627\u06CC MathJax",MathJax:"*MathJax* \u06CC\u06A9 \u06A9\u062A\u0627\u0628\u062E\u0627\u0646\u0647\u0654 \u062C\u0627\u0648\u0627\u0627\u0633\u06A9\u0631\u06CC\u067E\u062A\u06CC \u0627\u0633\u062A \u06A9\u0647 \u0627\u062C\u0627\u0632\u0647 \u0645\u06CC\u200C\u062F\u0647\u062F \u06A9\u0647 \u0646\u0648\u06CC\u0633\u0646\u062F\u0647\u200C\u0647\u0627\u06CC \u0635\u0641\u062D\u0647 \u0631\u06CC\u0627\u0636\u06CC\u0627\u062A \u0631\u0627 \u062F\u0631\u0648\u0646 \u0635\u0641\u062D\u0647\u200C\u0647\u0627\u06CC \u0642\u0631\u0627\u0631 \u062F\u0647\u0646\u062F.  \u0628\u0647 \u0639\u0646\u0648\u0627\u0646 \u062E\u0648\u0627\u0646\u0646\u062F\u0647\u060C \u0634\u0645\u0627 \u0646\u06CC\u0627\u0632 \u0646\u062F\u0627\u0631\u06CC\u062F \u06A9\u0627\u0631\u06CC \u0627\u0646\u062C\u0627\u0645 \u062F\u0647\u06CC\u062F \u06A9\u0647 \u0627\u06CC\u0646 \u0627\u062A\u0641\u0627\u0642 \u0628\u06CC\u0641\u062A\u062F.",Browsers:"*\u0645\u0631\u0648\u0631\u06AF\u0631\u0647\u0627*: MathJax \u0628\u0627 \u0647\u0645\u0647\u0654 \u0645\u0631\u0648\u0631\u06AF\u0631\u0647\u0627\u06CC \u0631\u0648\u0632 \u0634\u0627\u0645\u0644 \u0627\u06CC\u0646\u062A\u0631\u0646\u062A \u0627\u06A9\u0633\u067E\u0644\u0648\u0631\u0631 \u06F6 \u0628\u0647 \u0628\u0627\u0644\u0627\u060C \u0641\u0627\u06CC\u0631\u0641\u0627\u06A9\u0633 \u06F3 \u0628\u0647 \u0628\u0627\u0644\u0627\u060C \u06A9\u0631\u0648\u0645 \u06F0.\u06F2 \u0628\u0647 \u0628\u0627\u0644\u0627\u060C \u0633\u0627\u0641\u0627\u0631\u0633\u06CC \u06F2 \u0628\u0647 \u0628\u0627\u0644\u0627\u060C \u0627\u067E\u0631\u0627\u06CC \u06F9.\u06F6 \u0628\u0647 \u0628\u0627\u0644\u0627 \u0648 \u0627\u06A9\u062B\u0631 \u0645\u0631\u0648\u0631\u06AF\u0631\u0647\u0627\u06CC \u062A\u0644\u0641\u0646 \u0647\u0645\u0631\u0627\u0647 \u06A9\u0627\u0631 \u0645\u06CC\u200C\u06A9\u0646\u062F.",Menu:"*\u0645\u0646\u0648\u06CC \u0631\u06CC\u0627\u0636\u06CC*: MathJax \u06CC\u06A9 \u0645\u0646\u0648\u06CC \u0645\u062A\u0646\u06CC \u0628\u0647 \u0645\u0639\u0627\u062F\u0644\u0627\u062A \u0645\u06CC\u200C\u0627\u0641\u0632\u0627\u06CC\u062F. \u0628\u0627 \u06A9\u0644\u06CC\u06A9 \u0631\u0627\u0633\u062A \u06CC\u0627 \u06A9\u0644\u06CC\u06A9 \u0628\u0647 \u0647\u0645\u0631\u0627\u0647 \u0645\u0647\u0627\u0631 (CTRL) \u062F\u0631 \u0647\u0631 \u0645\u0639\u0627\u062F\u0644\u0647\u200C\u0627\u06CC \u0645\u06CC\u200C\u062A\u0648\u0627\u0646\u06CC\u062F \u0628\u0647 \u0627\u06CC\u0646 \u0645\u0646\u0648 \u062F\u0633\u062A \u0628\u06CC\u0627\u0628\u06CC\u062F.",ShowMath:"*\u0646\u0645\u0627\u06CC\u0634 \u0631\u06CC\u0627\u0636\u06CC \u0628\u0647 \u0639\u0646\u0648\u0627\u0646* \u0627\u062C\u0627\u0632\u0647 \u0645\u06CC\u200C\u062F\u0647\u062F \u06A9\u0647 \u0634\u0645\u0627 \u0645\u0646\u0628\u0639 \u0646\u0634\u0627\u0646\u0647\u200C\u06AF\u0630\u0627\u0631\u06CC \u0641\u0631\u0645\u0648\u0644 \u0631\u0627 \u0628\u0631\u0627\u06CC \u0631\u0648\u0646\u0648\u0634\u062A \u0648 \u0686\u0633\u067E\u0627\u0646\u062F\u0646 \u0628\u0628\u06CC\u0646\u06CC\u062F (\u0628\u0647 \u0639\u0646\u0648\u0627\u0646 MathML \u06CC\u0627 \u062F\u0631 \u0642\u0627\u0644\u0628 \u0627\u0635\u0644\u06CC \u0622\u0646).",Settings:"*\u062A\u0646\u0638\u06CC\u0645\u0627\u062A* \u0627\u062C\u0627\u0632\u0647 \u0645\u06CC\u200C\u062F\u0647\u062F \u0628\u0647 \u0642\u0627\u0628\u0644\u06CC\u062A\u200C\u0647\u0627\u06CC MathJax \u0627\u0632 \u062C\u0645\u0644\u0647\u0654 \u0627\u0646\u062F\u0627\u0632\u0647\u0654 \u0645\u0639\u0627\u062F\u0644\u0647\u200C\u0647\u0627 \u0648 \u0645\u06A9\u0627\u0646\u06CC\u0632\u0645 \u0627\u0633\u062A\u0641\u0627\u062F\u0647\u200C\u0634\u062F\u0647 \u0628\u0631\u0627\u06CC \u0646\u0645\u0627\u06CC\u0634 \u0645\u0639\u0627\u062F\u0644\u0647\u200C\u0647\u0627 \u06A9\u0646\u062A\u0631\u0644 \u062F\u0627\u0634\u062A\u0647 \u0628\u0627\u0634\u062F.",Language:"*\u0632\u0628\u0627\u0646* \u0627\u062C\u0627\u0632\u0647 \u0645\u06CC\u200C\u062F\u0647\u062F \u0634\u0645\u0627 \u0632\u0628\u0627\u0646 \u0627\u0633\u062A\u0641\u0627\u062F\u0647\u200C\u0634\u062F\u0647 \u062A\u0648\u0633\u0637 MathJax \u0628\u0631\u0627\u06CC \u0645\u0646\u0648\u0647\u0627 \u0648 \u067E\u06CC\u063A\u0627\u0645\u200C\u0647\u0627\u06CC \u062E\u0637\u0627 \u0631\u0627 \u0627\u0646\u062A\u062E\u0627\u0628 \u06A9\u0646\u06CC\u062F.",Zoom:"*\u06A9\u0648\u0686\u06A9/\u0628\u0632\u0631\u06AF\u200C\u0646\u0645\u0627\u06CC\u06CC \u0631\u06CC\u0627\u0636\u06CC*: \u0627\u06AF\u0631 \u0634\u0645\u0627 \u0628\u0627 \u062E\u0648\u0627\u0646\u062F\u0646 \u06CC\u06A9 \u0645\u0639\u0627\u062F\u0644\u0647 \u0645\u0634\u06A9\u0644\u06CC \u062F\u0627\u0631\u06CC\u062F\u060C MathJax \u0645\u06CC\u200C\u062A\u0648\u0627\u0646\u062F \u0628\u0627 \u0628\u0632\u0631\u06AF\u200C\u0646\u0645\u0627\u06CC\u06CC \u0628\u0647 \u062F\u06CC\u062F\u0646 \u0634\u0645\u0627 \u06A9\u0645\u06A9 \u06A9\u0646\u062F.",Accessibilty:"*\u062F\u0633\u062A\u06CC\u0627\u0628\u06CC\u200C\u067E\u0630\u06CC\u0631\u06CC*: MathJax \u0628\u0647\u200C\u0635\u0648\u0631\u062A \u062E\u0648\u062F\u06A9\u0627\u0631 \u0628\u0627 \u0635\u0641\u062D\u0647\u200C\u0647\u0627\u06CC\u06CC \u06A9\u0647 \u0631\u06CC\u0627\u0636\u06CC\u0627\u062A \u0631\u0627 \u0628\u0631\u0627\u06CC \u0642\u0627\u0628\u0644 \u062F\u0633\u062A\u0631\u0633 \u0628\u0631\u0627\u06CC \u06A9\u0633\u0627\u0646\u06CC \u06A9\u0647 \u0645\u0634\u06A9\u0644 \u0628\u06CC\u0646\u0627\u06CC\u06CC \u062F\u0627\u0631\u0646\u062F \u0639\u0645\u0644 \u0645\u06CC\u200C\u06A9\u0646\u062F.",Fonts:"*\u0642\u0644\u0645\u200C\u0647\u0627*: MathJax \u0627\u0632 \u0642\u0644\u0645\u200C\u0647\u0627\u06CC \u0645\u0634\u062E\u0635\u06CC \u0627\u06AF\u0631 \u0628\u0631 \u0631\u0648\u06CC \u0631\u0627\u06CC\u0627\u0646\u0647\u0654 \u0634\u0645\u0627 \u0646\u0635\u0628 \u0628\u0627\u0634\u0646\u062F \u0627\u0633\u062A\u0641\u0627\u062F\u0647 \u0645\u06CC\u200C\u06A9\u0646\u062F\u061B \u062F\u0631 \u063A\u06CC\u0631 \u0627\u06CC\u0646 \u0635\u0648\u0631\u062A \u0627\u0632 \u0642\u0644\u0645\u200C\u0647\u0627\u06CC \u0628\u0631 \u067E\u0627\u06CC\u0647\u0654 \u0648\u0628 \u0627\u0633\u062A\u0641\u0627\u062F\u0647 \u062E\u0648\u0627\u0647\u062F \u06A9\u0631\u062F.  \u0627\u06AF\u0631\u0686\u0647 \u0646\u06CC\u0627\u0632\u06CC \u0646\u06CC\u0633\u062A\u060C \u0642\u0644\u0645\u200C\u0647\u0627\u06CC \u0646\u0635\u0628\u200C\u0634\u062F\u0647 \u0628\u0647\u200C\u0635\u0648\u0631\u062A \u0645\u062D\u0644\u06CC \u0646\u0645\u0627\u06CC\u0634 \u0631\u0627 \u0633\u0631\u06CC\u0639\u062A\u0631 \u062E\u0648\u0627\u0647\u062F \u06A9\u0631\u062F. \u0645\u0627 \u0646\u0635\u0628 [\u0642\u0644\u0645\u200C\u0647\u0627\u06CC STIX](%1) \u0631\u0627 \u067E\u06CC\u0634\u0646\u0647\u0627\u062F \u0645\u06CC\u200C\u06A9\u0646\u06CC\u0645."}});MathJax.Ajax.loadComplete("[MathJax]/localization/lki/HelpDialog.js");
