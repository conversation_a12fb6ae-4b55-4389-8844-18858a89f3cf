/*
 *  /MathJax-v2/localization/pl/MathMenu.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Localization.addTranslation("pl","MathMenu",{version:"2.7.9",isLoaded:true,strings:{Show:"Poka\u017C wzory jako",MathMLcode:"Kod MathML",OriginalMathML:"Oryginalny MathML",TeXCommands:"Polecenia TeX",AsciiMathInput:"Wej\u015Bcie AsciiMathML",Original:"Oryginalny formularz",ErrorMessage:"Komunikat o b\u0142\u0119dzie",Annotation:"Adnotacja",TeX:"TeX",StarMath:"StarMath",Maple:"Maple",ContentMathML:"Zawarto\u015B\u0107 MathML",OpenMath:"OpenMath",texHints:"Poka\u017C wskaz\u00F3wki TeX w MathML",Settings:"Ustawienia wzor\u00F3w",ZoomTrigger:"Zwi\u0119kszanie zoomu",Hover:"poprzez najechanie mysz\u0105",Click:"poprzez klikni\u0119cie",DoubleClick:"poprzez dwukrotnie klikni\u0119cie",NoZoom:"Bez zoomu",TriggerRequires:"Aktywacja wymaga:",Option:"Option",Alt:"Alt",Command:"Command",Control:"Ctrl",Shift:"Shift",ZoomFactor:"Wsp\u00F3\u0142czynnik powi\u0119kszenia",Renderer:"Renderowanie wzor\u00F3w",MPHandles:"Obs\u0142u\u017C MathPlayer",MenuEvents:"Zdarzenia menu",MouseEvents:"Zdarzenia myszy",MenuAndMouse:"Zdarzenia myszy i menu",FontPrefs:"Ustawienia czcionek",ForHTMLCSS:"Dla HTML-CSS:",Auto:"Automatycznie",TeXLocal:"TeX (lokalny)",TeXWeb:"TeX (www)",TeXImage:"TeX (obraz)",STIXLocal:"STIX (lokalny)",ContextMenu:"Menu kontekstowe",Browser:"Przegl\u0105darka",Scale:"Skalowanie wszystkich wzor\u00F3w...",Discoverable:"Podkre\u015Bl po najechaniu kursora",Locale:"J\u0119zyk",LoadLocale:"Pobierz z URL...",About:"O MathJax",Help:"Pomoc MathJax",localTeXfonts:"U\u017Cyj lokalnej czcionki TeX",webTeXfonts:"U\u017Cyj internetowej czcionki TeX",imagefonts:"U\u017Cyj czcionki obrazkowej",localSTIXfonts:"U\u017Cyj lokalnej czcionki STIX",webSVGfonts:"U\u017Cyj internetowej czcionki SVG",genericfonts:"U\u017Cyj generowanej czcionki unicode",wofforotffonts:"czcionki WOFF lub OTF",eotffonts:"czcionki EOT",svgfonts:"czcionki SVG",WebkitNativeMMLWarning:"Twoja przegl\u0105darka nie obs\u0142uguje MathML, wi\u0119c zmiana wyj\u015Bcia do MathML mo\u017Ce spowodowa\u0107, \u017Ce strona stanie si\u0119 niemo\u017Cliwa do odczytania.",MSIENativeMMLWarning:"Program Internet Explorer wymaga wtyczki MathPlayer do procesu wy\u015Bwietlania MathML.",OperaNativeMMLWarning:"Wsparcie dla MathML w Operze jest ograniczone. W zwi\u0105zku z tym zmiana wyj\u015Bcia na MathML mo\u017Ce spowodowa\u0107, \u017Ce niekt\u00F3re strony b\u0119d\u0105 niemo\u017Cliwe do odczytania.",SafariNativeMMLWarning:"MathML zaimplementowany w twojej przegl\u0105darce nie obs\u0142uguje wszystkich mo\u017Cliwo\u015Bci MathJax, wi\u0119c cz\u0119\u015B\u0107 wyra\u017Cen mo\u017Ce nie renderowa\u0107 si\u0119 poprawnie.",FirefoxNativeMMLWarning:"MathML zaimplementowany w twojej przegl\u0105darce nie obs\u0142uguje wszystkich mo\u017Cliwo\u015Bci MathJax, wi\u0119c cz\u0119\u015B\u0107 wyra\u017Ce\u0144 mo\u017Ce nie renderowa\u0107 si\u0119 poprawnie.",MSIESVGWarning:"SVG nie jest zaimplementowane w Internet Explorerze do wersji 9 lub podczas emulowania IE8 lub poni\u017Cej, wi\u0119c zmiana wyj\u015Bcia do SVG mo\u017Ce spowodowa\u0107, \u017Ce strona stanie si\u0119 niemo\u017Cliwa do odczytania.",LoadURL:"Za\u0142aduj t\u0142umaczenie z tego URL:",BadURL:'Adres URL powinien by\u0107 dla pliku JavaScript, kt\u00F3ry definiuje dane t\u0142umaczenie MathJax. Pliki JavaScript powinny ko\u0144czy\u0107 si\u0119 ".js"',BadData:"Nie mo\u017Cna za\u0142adowa\u0107 danych t\u0142umacze\u0144 z  %1",SwitchAnyway:"Na pewno zmieni\u0107 renderer ?\n\n(Naci\u015Bnij OK a\u017Ceby zmieni\u0107, lub CANCEL aby kontynuowa\u0107 z aktualnym rendererem)",ScaleMath:"Skaluj wszystkie wzory matematyczne (por\u00F3wnane do otaczaj\u0105cego tekstu) przez",NonZeroScale:"Warto\u015B\u0107 nie powinna by\u0107 zerowa",PercentScale:"Warto\u015B\u0107 powinna by\u0107 w procentach (na przyk\u0142ad 120%%)",IE8warning:"Ta opcja wy\u0142\u0105czy obs\u0142ug\u0119 menu i powi\u0119kszania w MathJax, ale mo\u017Cesz klikn\u0105\u0107 z Altem na wz\u00F3r, aby otworzy\u0107 menu MathJax.\n\nCzy na pewno chcesz zmieni\u0107 ustawienia MathPlayer?",IE9warning:"Menu kontekstowe MathJax zostanie wy\u0142\u0105czone, ale mo\u017Cesz klikn\u0105\u0107 z Altem na wz\u00F3r, aby otworzy\u0107 menu.",NoOriginalForm:"Brak wzor\u00F3w w oryginalnej postaci",Close:"Zamknij",EqSource:"\u0179r\u00F3d\u0142o wzoru MathJax",STIXWeb:"STIX (www)",AsanaMathWeb:"Asana Math (www)",GyrePagellaWeb:"Gyre Pagella (www)",GyreTermesWeb:"Gyre Termes (www)",LatinModernWeb:"Latin Modern (www)",NeoEulerWeb:"Neo Euler (www)",CloseAboutDialog:"Zamknij okno o MathJax",FastPreview:"Szybki podgl\u0105d strony",AssistiveMML:"Asystuj\u0105cy MathML",InTabOrder:"Zawarty w kolejno\u015Bci stron"}});MathJax.Ajax.loadComplete("[MathJax]/localization/pl/MathMenu.js");
