# Copyright (c) Jupyter Development Team.
# Distributed under the terms of the Modified BSD License.

from traitlets import <PERSON><PERSON>, <PERSON><PERSON>, List

from .utils import setup

from ..widget import Widget

from ..._version import __control_protocol_version__

# A widget with simple traits
class SimpleWidget(Widget):
    a = Bool().tag(sync=True)
    b = <PERSON><PERSON>(<PERSON><PERSON>(), <PERSON><PERSON>(), <PERSON><PERSON>(), default_value=(<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>alse)).tag(
        sync=True
    )
    c = List(Bool()).tag(sync=True)


def test_empty_send_state():
    w = SimpleWidget()
    w.send_state([])
    assert w.comm.messages == []


def test_empty_hold_sync():
    w = SimpleWidget()
    with w.hold_sync():
        pass
    assert w.comm.messages == []
