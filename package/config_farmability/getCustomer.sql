SELECT
    convert(varchar, cl.Account)   AS 'source_customer_code',

    CASE
        WHEN CHARINDEX(' ', cl.Contact_Person) > 0 THEN RTRIM(ISNULL(LEFT(cl.Contact_Person, CHARINDEX(' ', cl.Contact_Person) - 1), ''))
        ELSE RTRIM(ISNULL(cl.Contact_Person, ''))
    END                            AS 'first_name',

    CASE
        WHEN CHARINDEX(' ', cl.Contact_Person) > 0 THEN LTRIM(RTRIM(ISNULL(RIGHT(cl.Contact_Person, LEN(cl.Contact_Person) - CHARINDEX(' ', cl.Contact_Person) + 1), '')))
        ELSE ''
    END                            AS 'last_name',

    cl.EMail                       AS 'email',

    CASE
        WHEN cl.On_Hold = 1 THEN 'false'
        ELSE 'true'
    END                            AS 'customer_active',

    0                              AS 'accepts_marketing',
    cl.Physical1                   AS 'address.address1', -- or Address?
    cl.Physical2                   AS 'address.address2',
    cl.Physical3                   AS 'address.city',
    ''                             AS 'address.country',
    ''                             AS 'address.country_code',
    ''                             AS 'address.province',
    ''                             AS 'address.province_code',
    cl.PhysicalPC                  AS 'address.zip',
    cl.Name                        AS 'address.company',
    cl.Telephone                   AS 'address.phone',
    ''                             AS 'meta_show_online',
    ''                             AS 'meta_display_name',
    ''                             AS 'meta_credit_limit',
    ''                             AS 'meta_branch',
    ''                             AS 'meta_balance',
    ''                             AS 'price_tier',
    ''                             AS 'qty_availability'

FROM Client cl WITH (NOLOCK)
WHERE RTRIM(ISNULL(cl.EMail, '')) <> '' AND cl.Account = '%(source_customer_code)s';