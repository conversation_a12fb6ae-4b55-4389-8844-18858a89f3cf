select *
from (
SELECT
    row_number() OVER (ORDER BY inv.StockCode)        AS n,

    RTRIM(LTRIM(inv2.WoProductCode))                  AS "source_product_code",
    RTRIM(LTRIM(inv.StockCode))                       AS "source_variant_code",
    RTRIM(LTRIM(inv2.WoProductTitle))                 AS "title",
    CASE WHEN RTRIM(LTRIM(ISNULL(inv2.WoCategory, ''))) <> '' AND RTRIM(LTRIM(ISNULL(inv2.WoCategory, ''))) <> ''
        THEN CONCAT(inv2.WoCategory, '>', inv2.WoSubCat)
        ELSE inv2.WoCategory
    END                                               AS "collection",
    ''                                                AS "product_type",
    ''                                                AS "vendor",
    RTRIM(LTRIM(inv2.WoVariantSku))                   AS "variants.sku",
    RTRIM(LTRIM(inv2.WoVariantDescSku))               AS "body_html",
    CAST(ISNULL(inv.Mass, 0) AS INTEGER)              AS "variants.weight",
    RTRIM(LTRIM(inv.AlternateKey1))                   AS "variants.barcode",
	RTRIM(LTRIM(inv2.WoVariant1Name))				  AS "variants.option1_name",
	RTRIM(LTRIM(inv2.WoVariant1Value))				  AS "variants.option1_value",
	RTRIM(LTRIM(inv2.WoVariant2Name))				  AS "variants.option2_name",
	RTRIM(LTRIM(inv2.WoVariant2Value))				  AS "variants.option2_value",
	RTRIM(LTRIM(inv2.WoVariant3Name))				  AS "variants.option3_name",
	RTRIM(LTRIM(inv2.WoVariant3Value))				  AS "variants.option3_value",
    CASE
       WHEN LOWER(RTRIM(ISNULL(inv2.WoActive, ''))) = 'active' THEN 'true'
        ELSE 'false'
    END                                               AS "product_active",
    'true'                                            AS "variants.inventory_management",
    ''                                                AS "tags",

    -- Prices
    'DF'                                              AS "default_price_tier",
    STUFF((
      SELECT
        ',' + CONVERT(VARCHAR, LTRIM(RTRIM(price_tiers.PriceCode))) + '|' +
              CONVERT(VARCHAR, CAST(ISNULL(price_tiers.SellingPrice, 0) AS FLOAT))
      FROM InvPrice price_tiers WITH (NOLOCK)
      WHERE price_tiers.StockCode = inv.StockCode
      ORDER BY LTRIM(RTRIM(price_tiers.PriceCode))
    for xml path('')),1,1,'')
                                                      AS "csv_price_tiers",

    'SY'                                              AS "default_qty_availability",
    STUFF((
      SELECT
        ',' + CONVERT(VARCHAR, LTRIM(RTRIM(whse.Warehouse))) + '|' +
              CONVERT(VARCHAR, CONVERT(INTEGER, COALESCE(whse.QtyOnHand, 0) - COALESCE(whse.QtyAllocated, 0)))
      FROM InvWarehouse whse WITH (NOLOCK)
      WHERE whse.StockCode = inv.StockCode
      ORDER BY LTRIM(RTRIM(whse.Warehouse))
    for xml path('')),1,1,'')
                                                      AS "csv_qty_availability",

    inv.ConvFactAltUom                                AS "meta_conv_fact_alt_uom",
    inv.StockUom                                      AS "meta_stock_uom",
    inv.AlternateUom                                  AS "meta_alternate_uom"

FROM InvMaster inv WITH (NOLOCK)

    -- Base Data
    LEFT JOIN [InvMaster+] inv2 WITH (nolock)
    ON inv.StockCode = inv2.StockCode

    WHERE LOWER(RTRIM(ISNULL(inv2.WoActive, ''))) in ('active', 'delete')
	AND RTRIM(LTRIM(ISNULL(inv2.WoProductTitle, ''))) <> ''
	AND RTRIM(LTRIM(ISNULL(inv2.WoProductCode, ''))) <> ''
	AND RTRIM(LTRIM(ISNULL(inv2.WoVariantSku, ''))) <> ''

) as rows

where
n > '%(audit_lower_limit)n'
and n <= '%(audit_upper_limit)n'