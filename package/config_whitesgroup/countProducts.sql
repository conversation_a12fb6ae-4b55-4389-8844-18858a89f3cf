select count(*) as count
FROM InvMaster inv WITH (NOLOCK)

    -- Base Data
    LEFT JOIN [InvMaster+] inv2 WITH (nolock)
    ON inv.StockCode = inv2.StockCode

    WHERE LOWER(RTRIM(ISNULL(inv2.WoActive, ''))) in ('active', 'delete')
	AND RTRIM(LTRIM(ISNULL(inv2.WoProductTitle, ''))) <> ''
	AND RTRIM(LTRIM(ISNULL(inv2.WoProductCode, ''))) <> ''
	AND RTRIM(LTRIM(ISNULL(inv2.WoVariantSku, ''))) <> ''
;