SELECT
    rtrim(i.ItemCode)         AS "source_product_code",
    rtrim(i.GUID)             AS "source_variant_code",
    rtrim(i.Description)      AS "title",
    rtrim(i.ItemCode)         AS "variants.sku",
    rtrim(ic.ICDesc)          AS "collection",
    'n/a'                     AS "product_type",
    ''                        AS "vendor",
    ''                        AS "variants.option1",
    ''                        AS "variants.option2",
    ''                        AS "variants.option3",
    ''                        AS "body_html",
    rtrim(i.NettMass)         AS "variants.weight",
    rtrim(i.Barcode)          AS "variants.barcode",

    -- Warehouse 1 qty
    (
      (
        (wh1.OpeningQty + wh1.QtyBuyLast + wh1.QtyAdjustLast - wh1.QtySellLast) +
        (wh1.QtyBuyThis01 + wh1.QtyBuyThis02 + wh1.QtyBuyThis03 + wh1.QtyBuyThis04 +
            wh1.QtyBuyThis05 + wh1.QtyBuyThis06 + wh1.QtyBuyThis07 + wh1.QtyBuyThis08 +
            wh1.QtyBuyThis09 + wh1.QtyBuyThis10 + wh1.QtyBuyThis11 + wh1.QtyBuyThis12 +
            wh1.QtyBuyThis13) +
        (wh1.QtyAdjustThis01 + wh1.QtyAdjustThis02 + wh1.QtyAdjustThis03 +
            wh1.QtyAdjustThis04 + wh1.QtyAdjustThis05 + wh1.QtyAdjustThis06 +
            wh1.QtyAdjustThis07 + wh1.QtyAdjustThis08 + wh1.QtyAdjustThis09 +
            wh1.QtyAdjustThis10 + wh1.QtyAdjustThis11 + wh1.QtyAdjustThis12 +
            wh1.QtyAdjustThis13) -
        (wh1.QtySellThis01 + wh1.QtySellThis02 + wh1.QtySellThis03 + wh1.QtySellThis04 +
            wh1.QtySellThis05 + wh1.QtySellThis06 + wh1.QtySellThis07 + wh1.QtySellThis08 +
            wh1.QtySellThis09 + wh1.QtySellThis10 + wh1.QtySellThis11 + wh1.QtySellThis12 +
            wh1.QtySellThis13)
      )
    )                         as "qty_on_hand",

    -- Note: Below counts for sales orders and invoices
    COALESCE((
      select
        sum(QtyLeft)

      from HistoryLines hl where
        (hl.Documenttype = 102 or hl.Documenttype = 103) and
        hl.ItemCode = i.ItemCode and
        hl.MultiStore = wh1.StoreCode
    ), 0)                     as "qty_on_sales_order",

    -- Warehouse 1 prices
    -- Note that price can be set by warehouse (store)
    wh1.SellExcl01            as "variants.retail_price",
    wh1.SellExcl02            as "variants.dealer_price",
    wh1.SellExcl03            as "variants.distribution_price",
    wh1.SellExcl05            as "variants.wholesale_price",
    CASE
        WHEN LOWER(RTRIM(i.UserDefText01)) = 'yes' THEN 'true'
        ELSE 'false'
    END                                  as "product_active",
    ''                                   as "tags",
    rtrim(ig.Description)                as "inventory_group",
    'true'                               as "variants.inventory_management",
    ltrim(rtrim(isnull(i.Picture, '')))  as "picture",

    CASE
        WHEN LOWER(RTRIM(i.UserDefText02)) = 'yes' THEN 'true'
        ELSE 'false'
    END                       AS "meta_sync_yuppiechef",

    CASE
        WHEN LOWER(LTRIM(i.UserDefText03)) = 'yes' THEN 'true'
        ELSE 'false'
    END                       AS "meta_sync_b2b",

    i.UnitSize                AS "meta_unit_size",

    CASE
        WHEN LTRIM(RTRIM(ISNULL(i.Picture, ''))) <> '' THEN 'yes'
        ELSE 'no'
    END                       AS "meta_has_image"

FROM Inventory i

    -- Warehouse 1
    left join MultiStoreTrn wh1 on
      wh1.ItemCode = i.ItemCode and
      wh1.StoreCode = '%(multi_store)s'
    left join InventoryGroups ig on
      ig.InvGroup = wh1.InvGroup

    -- Attribute info
    left join InventoryCategory ic on
      i.Category = ic.ICCode

WHERE  rtrim(i.ItemCode) = '%(sku)s'
;