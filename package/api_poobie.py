import json
import imp
from .controllers import base_push as base

class Api(base.Api):
    _configDir = "config_poobie"

    def reload(self):
        imp.reload(base)
        base.Api.reload(self)
        response = {
            "status": True,
            "data": {
                "reload": True
            }
        }
        return json.dumps(response, indent=self._indent)

    def _afterProductTransform(self, params, result, source_product):
        """
        Hook method that gets called after a product has been transformed.
        For default behavior just return the passed "source_product"
        """
        from collections import OrderedDict

        source_product_result = source_product

        # Add warehouses
        # Warehouse 001
        qty_on_hand = result["qty_on_hand"]
        qty_on_sales_order = result["qty_on_sales_order"]
        qty = qty_on_hand
        if qty_on_sales_order is not None:
            qty = qty_on_hand - qty_on_sales_order
        if qty is None or int(qty) < 0:
            qty = 0

        availability = OrderedDict()
        availability["qty"] = int(qty)
        availability["description"] = '001'
        source_product_result["product"]["variants"]["qty_availability"].append(availability)

        # Warehouse 002
        qty_on_hand = result["qty_on_hand_002"]
        qty_on_sales_order = result["qty_on_sales_order_002"]
        qty = qty_on_hand
        if qty_on_sales_order is not None:
            qty = qty_on_hand - qty_on_sales_order
        if qty is None or int(qty) < 0:
            qty = 0

        availability = OrderedDict()
        availability["qty"] = int(qty)
        availability["description"] = '002'
        source_product_result["product"]["variants"]["qty_availability"].append(availability)

        # Warehouse 003
        qty_on_hand = result["qty_on_hand_003"]
        qty_on_sales_order = result["qty_on_sales_order_003"]
        qty = qty_on_hand
        if qty_on_sales_order is not None:
            qty = qty_on_hand - qty_on_sales_order
        if qty is None or int(qty) < 0:
            qty = 0

        availability = OrderedDict()
        availability["qty"] = int(qty)
        availability["description"] = '003'
        source_product_result["product"]["variants"]["qty_availability"].append(availability)

        # Warehouse 004
        qty_on_hand = result["qty_on_hand_004"]
        qty_on_sales_order = result["qty_on_sales_order_004"]
        qty = qty_on_hand
        if qty_on_sales_order is not None:
            qty = qty_on_hand - qty_on_sales_order
        if qty is None or int(qty) < 0:
            qty = 0

        availability = OrderedDict()
        availability["qty"] = int(qty)
        availability["description"] = '004'
        source_product_result["product"]["variants"]["qty_availability"].append(availability)

        # Warehouse 005
        qty_on_hand = result["qty_on_hand_005"]
        qty_on_sales_order = result["qty_on_sales_order_005"]
        qty = qty_on_hand
        if qty_on_sales_order is not None:
            qty = qty_on_hand - qty_on_sales_order
        if qty is None or int(qty) < 0:
            qty = 0

        availability = OrderedDict()
        availability["qty"] = int(qty)
        availability["description"] = '005'
        source_product_result["product"]["variants"]["qty_availability"].append(availability)

        # Warehouse 006
        qty_on_hand = result["qty_on_hand_006"]
        qty_on_sales_order = result["qty_on_sales_order_006"]
        qty = qty_on_hand
        if qty_on_sales_order is not None:
            qty = qty_on_hand - qty_on_sales_order
        if qty is None or int(qty) < 0:
            qty = 0

        availability = OrderedDict()
        availability["qty"] = int(qty)
        availability["description"] = '006'
        source_product_result["product"]["variants"]["qty_availability"].append(availability)

        # Add price tiers for warehouse 002
        price_tier = OrderedDict()
        price_tier["tier"] = "retail_002"
        price_tier["price"] = result["variants.retail_price_002"]
        source_product_result["product"]["variants"]["price_tiers"].append(price_tier)

        price_tier = OrderedDict()
        price_tier["tier"] = "dealer_002"
        price_tier["price"] = result["variants.dealer_price_002"]
        source_product_result["product"]["variants"]["price_tiers"].append(price_tier)

        price_tier = OrderedDict()
        price_tier["tier"] = "distribution_002"
        price_tier["price"] = result["variants.distribution_price_002"]
        source_product_result["product"]["variants"]["price_tiers"].append(price_tier)

        price_tier = OrderedDict()
        price_tier["tier"] = "wholesale_002"
        price_tier["price"] = result["variants.wholesale_price_002"]
        source_product_result["product"]["variants"]["price_tiers"].append(price_tier)

        # special price
        special = 0
        if result["meta_special_price"] and result["meta_special_price"] is not None:
            special = float(result["meta_special_price"].replace(",", "."))
        price_tier = OrderedDict()
        price_tier["tier"] = "special_price"
        price_tier["price"] = special
        source_product_result["product"]["variants"]["price_tiers"].append(price_tier)

        return source_product_result
