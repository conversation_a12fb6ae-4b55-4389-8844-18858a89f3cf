select
    CardCode                                                                   as 'source_customer_code',
    CardName                                                                   as 'card_name',
    E_Mail                                                                     as 'email',
    'true'                                                                     as 'customer_active',
    1                                                                          as 'accepts_marketing',
    MailAddres                                                                 as 'address.address1', -- or Address?
    ''                                                                         as 'address.address2',
    City                                                                       as 'address.city',
    Country                                                                    as 'address.country',
    Country                                                                    as 'address.country_code',
    County                                                                     as 'address.province',
    State1                                                                     as 'address.province_code',
    ZipCode                                                                    as 'address.zip',
    ''                                                                         as 'address.company',
    CntctPrsn                                                                  as 'address.contact_person',
    Phone1                                                                     as 'address.phone',
    ListNum                                                                    as 'price_tier',
    ''                                                                         as 'qty_availability',
    ocrd."Balance"                                                             as "meta_balance",
    ocrd."CreditLine"                                                          as "meta_credit"

from ocrd
where CardCode = '%(source_customer_code)s';
