select
    -- There must only be one result row per ItemCode!
    oitm.U_ARGNS_MOD + '-' + oitm.U_ARGNS_COL
                                as "source_product_code",
    oitm.ItemCode               as "source_variant_code",
    am.U_ModDesc                as "title",
    oitb.ItmsGrpNam             as "collection",
    ag.Name                     as "product_type",
    case oitm.U_Website
        when 'Y' then 'b2b'
        else ''
    end                         as "vendor",
    'size'                      as "variants.option1_name",
    oitm.U_ARGNS_SIZE           as "variants.option1_value",
    'colour'                    as "variants.option2_name",
    ac.U_ColDesc                as "variants.option2_value",
    oitm.UserText               as "body_html",
    0                           as "variants.weight",
    oitm.CodeBars               as "variants.barcode",

    -- All price tiers
    '1'                         as "default_price_tier",
    stuff(
    (select
    convert(varchar, itm1.PriceList) + '|' +
    convert(varchar, itm1.Price) + ','
    from itm1
    where itm1.ItemCode = oitm.ItemCode
    order by itm1.PriceList
    for xml path('')),1,0,'')
                                as "csv_price_tiers",

    -- All qty availability
    'CPT'                       as "default_qty_availability",
    stuff(
    (select
    convert(varchar, oitw.WhsCode) + '|' +
    convert(varchar, oitw.OnHand-oitw.IsCommited)+','
    from oitw
    where oitw.ItemCode = oitm.ItemCode
    order by oitw.WhsCode
    for xml path('')),1,0,'')
                                as "csv_qty_availability",

    case lower(oitm.InvntItem)
        when 'Y' then 'true'
        else 'false'
    end                         as "variants.inventory_management",

    case CONCAT(oitm.U_Website, oitm.U_Online)
        when 'Y' then 'true'
		    when 'YY' then 'true'
		    when 'YN' then 'true'
        when 'NY' then 'true'
        else 'false'
    end                         as "product_active",

    case lower(oitm.U_Online)
        when 'y' then 'b2c'
        else ''
    end                         as "tags",

    oitb.ItmsGrpCod             as "meta_item_group_code",
    oitb.ItmsGrpNam             as "meta_item_group_name",

	  ac.U_ColDesc                as "meta_colour",

    case oitm.U_Online
        when 'Y' then 'true'
        else ''
    end                         as "meta_b2c",
    case oitm.U_Website
        when 'Y' then 'true'
        else ''
    end                         as "meta_b2b",

    -- Special prices for all periods and volumes
    stuff(
    (SELECT
         ',order_0|entity_customer|key_customer_code|value_' + convert(varchar, T0.ObjCode) + '|type_discount_channel_user~' +
         convert(varchar, T1.Discount)
     FROM OEDG T0 INNER JOIN EDG1 T1 ON T0.[AbsEntry] = T1.[AbsEntry]
     WHERE T1.ObjKey = convert(varchar, oitb.ItmsGrpCod)
       and T1.ObjType = 52
       and T1.Discount > 0
     order by T1.ObjKey
    for xml path('')),1,0,'')
                                as 'csv_contract_customer_product_group'

    -- Special prices for period join ospp with spp1
    -- Special prices for volume join ospp with spp2

from oitm with(nolock)
INNER JOIN oitb with(nolock) on oitm.ItmsGrpCod = oitb.ItmsGrpCod
INNER JOIN [@ARGNS_MODEL] am with(nolock) on oitm.U_ARGNS_MOD = am.U_ModCode
INNER JOIN [@ARGNS_MODELGRP] ag with(nolock) on oitm.U_ARGNS_M_GROUP = ag.Code
LEFT JOIN [@ARGNS_COLOR] ac with(nolock) on oitm.U_ARGNS_COL = ac.U_ColCode

where oitm.ItemCode = '%(source_variant_code)s';


