SELECT

    oitm.U_ARGNS_MOD + '-' + oitm.U_ARGNS_COL + '-' + ai.U_File
                                               AS "source_image_code",
    oitm.U_ARGNS_MOD + '-' + oitm.U_ARGNS_COL  AS "source_product_code",
    oitm.ItemCode                              AS "source_variant_code",
    ai.U_Path                                  AS "filename"

from oitm with(nolock)
INNER JOIN oitb with(nolock) on oitm.ItmsGrpCod = oitb.ItmsGrpCod
INNER JOIN [@ARGNS_MODEL] am with(nolock) on oitm.U_ARGNS_MOD = am.U_ModCode
INNER JOIN [@ARGNS_MODELGRP] ag with(nolock) on oitm.U_ARGNS_M_GROUP = ag.Code
INNER JOIN [@ARGNS_MODEL_COLOR] ac with(nolock) on oitm.U_ARGNS_COL = ac.U_ColCode and oitm.U_ARGNS_MOD = ac.U_ModCode
INNER JOIN [@ARGNS_MODEL_IMG] ai with(nolock) on am.Code = ai.Code and ac.LineId = ai.LineId
WHERE oitm.ItemCode = '%(source_variant_code)s' AND not U_Online is null
