{"dbName": "local.db", "dsn": "Driver={SQL Server};Server=SWAGGSQL;Database=SBK_SWAGG_LIVE;Uid=sa;Pwd=**********;", "dsnNoDb": "Driver={SQL Server};Server=SWAGGSQL;Uid=sa;Pwd=**********;", "server": "SWAGGSQL", "companyDb": "SBK_SWAGG_LIVE", "userName": "<PERSON><PERSON><PERSON>", "password": "9536", "dbServerType": "MSSQL2014", "dbUserName": "sa", "dbPassword": "**********", "licenseServer": "SWAGGSQL:30000", "audit_limit": 500, "audit_image_limit": 100, "image_path": "E:\\SAPB1\\Attachment\\", "push": {"source_id": 548, "limit": 100, "image_limit": 7, "token": "1286f388d481352972aa2ecfab28720cf6b56f7b", "writeLog": "http://localhost:1337/writeLog", "getProducts": "http://localhost:1337/sapone/getProductsBatch", "getCustomers": "http://localhost:1337/sapone/getCustomersBatch", "getImages": "http://localhost:1337/sapone/getImagesBatch", "countProducts": "http://localhost:1337/sapone/countProducts", "countCustomers": "http://localhost:1337/sapone/countCustomers", "countImages": "http://localhost:1337/sapone/countImages", "getMeta": "http://localhost:1337/sapone/getMeta", "setMeta": "http://localhost:1337/sapone/setMeta", "productsQueue": "https://app.stock2shop.com/v1/products/queue", "customersQueue": "https://app.stock2shop.com/v1/customers/queue", "imagesQueue": "https://app.stock2shop.com/v1/images/queue"}}