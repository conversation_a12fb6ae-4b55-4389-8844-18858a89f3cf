select
    -- Column must be named "count", it is used by push.exe
    (
    select
        count(*) as product_count
    from oitm with(nolock)
    INNER JOIN oitb with(nolock) on oitm.ItmsGrpCod = oitb.ItmsGrpCod
    INNER JOIN [@ARGNS_MODEL] am with(nolock) on oitm.U_ARGNS_MOD = am.U_ModCode
    INNER JOIN [@ARGNS_MODELGRP] ag with(nolock) on oitm.U_ARGNS_M_GROUP = ag.Code
    LEFT JOIN [@ARGNS_COLOR] ac with(nolock) on oitm.U_ARGNS_COL = ac.U_ColCode
    )
                                                as count,

    (
    select
        count(*) as product_count
    from oitm with(nolock)
    INNER JOIN oitb with(nolock) on oitm.ItmsGrpCod = oitb.ItmsGrpCod
    INNER JOIN [@ARGNS_MODEL] am with(nolock) on oitm.U_ARGNS_MOD = am.U_ModCode
    INNER JOIN [@ARGNS_MODELGRP] ag with(nolock) on oitm.U_ARGNS_M_GROUP = ag.Code
    LEFT JOIN [@ARGNS_COLOR] ac with(nolock) on oitm.U_ARGNS_COL = ac.U_ColCode
    where oitm.U_Online = 'Y' or oitm.U_Website = 'Y'
    )
                                                as active,

    (
    select
        count(*) as product_count
    from oitm with(nolock)
    INNER JOIN oitb with(nolock) on oitm.ItmsGrpCod = oitb.ItmsGrpCod
    INNER JOIN [@ARGNS_MODEL] am with(nolock) on oitm.U_ARGNS_MOD = am.U_ModCode
    INNER JOIN [@ARGNS_MODELGRP] ag with(nolock) on oitm.U_ARGNS_M_GROUP = ag.Code
    LEFT JOIN [@ARGNS_COLOR] ac with(nolock) on oitm.U_ARGNS_COL = ac.U_ColCode
    where oitm.U_Online <> 'Y' AND oitm.U_Website <> 'Y'
    )
                                                as inactive,

    (
    select
        count(*) as product_count
    from oitm with(nolock)
    INNER JOIN oitb with(nolock) on oitm.ItmsGrpCod = oitb.ItmsGrpCod
    INNER JOIN [@ARGNS_MODEL] am with(nolock) on oitm.U_ARGNS_MOD = am.U_ModCode
    INNER JOIN [@ARGNS_MODELGRP] ag with(nolock) on oitm.U_ARGNS_M_GROUP = ag.Code
    LEFT JOIN [@ARGNS_COLOR] ac with(nolock) on oitm.U_ARGNS_COL = ac.U_ColCode
    )
                                                as total;

;