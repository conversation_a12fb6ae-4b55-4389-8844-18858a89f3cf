{"dbName": "local.db", "dsn": "Driver={SQL Server};Server=AUT-DC-SQL01;Database=SysproCompanyN;Uid=Apifact_Integration;Pwd=****************;}", "audit_limit": 1000, "push": {"source_id": 1136, "limit": 500, "token": "V2GQE84WKSBS3NBE968S85BGGQ8VDXCOVU9ZX5MX", "writeLog": "http://localhost:13337/writeLog", "getProducts": "http://localhost:13337/odbc/getProductsBatch", "getCustomers": "http://localhost:13337/odbc/getCustomersBatch", "countProducts": "http://localhost:13337/odbc/countProducts", "countCustomers": "http://localhost:13337/odbc/countCustomers", "getMeta": "http://localhost:13337/odbc/getMeta", "setMeta": "http://localhost:13337/odbc/setMeta", "productsQueue": "https://app.stock2shop.com/v1/products/queue", "customersQueue": "https://app.stock2shop.com/v1/customers/queue"}}