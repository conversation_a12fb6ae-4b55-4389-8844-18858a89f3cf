SELECT *
from (
SELECT
    row_number() over (order by inv.StockCode)        AS n,

    RTRIM(LTRIM(inv.StockCode))                       AS "source_product_code",
    RTRIM(LTRIM(inv.StockCode))                       AS "source_variant_code",
    RTRIM(LTRIM(inv.Description))                     AS "title",
    RTRIM(LTRIM(spcd.Description))                    AS "collection",
    RTRIM(LTRIM(inv.UserField3))                      AS "product_type",
    RTRIM(LTRIM(inv.UserField1))                      AS "vendor",
    RTRIM(LTRIM(inv.StockCode))                       AS "variants.sku",
    inv.LongDesc                                      AS "body_html",
    CAST(ISNULL(inv.Mass, 0) AS INTEGER)              AS "variants.weight",
    ''                                                AS "variants.barcode",
    CASE RTRIM(LTRIM(LOWER(inv.DrawOfficeNum)))
         when '1' then 'true'
         else 'false'
     END                                              AS "product_active",
    'true'                                            AS "variants.inventory_management",
     RTRIM(LTRIM(inv.UserField4))                     AS "tags",

    -- Prices
    'R'                                               AS "default_price_tier",
    STUFF((
      SELECT
        ',' + CONVERT(VARCHAR, LTRIM(RTRIM(price_tiers.PriceCode))) + '|' +
              CONVERT(VARCHAR, CAST(ISNULL(price_tiers.SellingPrice, 0) AS FLOAT))
      FROM InvPrice price_tiers WITH (NOLOCK)
      WHERE price_tiers.StockCode = inv.StockCode
      ORDER BY LTRIM(RTRIM(price_tiers.PriceCode))
    for xml path('')),1,1,'')
                                                      AS "csv_price_tiers",

    '34'                                              AS "default_qty_availability",
    STUFF((
      SELECT
        ',' + CONVERT(VARCHAR, LTRIM(RTRIM(whse.Warehouse))) + '|' +
        CONVERT(VARCHAR,
            CASE
            WHEN CONVERT(INTEGER, COALESCE(whse.QtyOnHand, 0) - COALESCE(whse.QtyAllocated, 0)) < 0
                THEN 0
            ELSE CONVERT(INTEGER, COALESCE(whse.QtyOnHand, 0) - COALESCE(whse.QtyAllocated, 0))
            END)
      FROM InvWarehouse whse WITH (NOLOCK)
      WHERE whse.StockCode = inv.StockCode
      ORDER BY LTRIM(RTRIM(whse.Warehouse))
    for xml path('')),1,1,'')
                                                      AS "csv_qty_availability",
    RTRIM(LTRIM(inv2.OnlineShopSync))                 AS "meta_channelsync",
    LTRIM(RTRIM(inv.StockUom))                        AS "meta_uom"

FROM InvMaster inv WITH (NOLOCK)

    LEFT JOIN SalProductClassDes spcd WITH (NOLOCK)
    ON inv.ProductClass = spcd.ProductClass

    INNER JOIN [InvMaster+] inv2 WITH (nolock)
    ON inv2.StockCode = inv.StockCode


WHERE RTRIM(LTRIM(LOWER(inv.DrawOfficeNum))) in ('1', '2')
) as rows
where
n > '%(audit_lower_limit)n'
and n <= '%(audit_upper_limit)n'