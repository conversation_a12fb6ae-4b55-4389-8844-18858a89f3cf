SELECT
    st.Code                                                             AS 'source_product_code',
    st.StockLink                                                        AS 'source_variant_code',
    st.Description_2                                                    AS 'title',
    (
        SELECT sc.cCategoryName
        FROM _etblStockCategories sc WITH (NOLOCK)
        INNER JOIN _etblStockDetails sd WITH (NOLOCK)
        ON sc.idStockCategories = sd.ItemCategoryID
        WHERE sd.StockID = st.StockLink AND sd.WhseID = st.WhseItem
    )                                                                   AS 'collection',
    (
        SELECT grp.StGroup
        FROM GrpTbl grp WITH (NOLOCK)
        INNER JOIN _etblStockDetails sd WITH (NOLOCK)
        ON sd.StockID = st.StockLink
        AND grp.idGrpTbl = sd.GroupID
        AND sd.WhseID = st.WhseItem
    )                                                                   AS 'product_type',
    ''                                                                  AS 'vendor',
    st.Code                                                             AS 'variants.sku',
    ''                                                                  AS 'variants.option1_name',
    ''                                                                  AS 'variants.option1_value',
    ''                                                                  AS 'variants.option2_name',
    ''                                                                  AS 'variants.option2_value',
    ''                                                                  AS 'variants.option3_name',
    ''                                                                  AS 'variants.option3_value',
    ''                                                                  AS 'body_html',
    0                                                                   AS 'variants.weight',
    ''                                                                  AS 'variants.barcode',
    'true'                                                              AS 'variants.inventory_management',
    CASE ltrim(rtrim(lower(st.ulIIWebSync)))
        WHEN 'active' THEN 'true'
        ELSE 'false'
    END                                                                 AS 'product_active',
    ''                                                                  AS 'tags',

    'Cash Customer Price List'                                          AS 'default_price_tier',
    STUFF((
        SELECT
            ',' + CONVERT(VARCHAR, price_names.cName) + '|' +
            CONVERT(VARCHAR, price.fExclPrice)
        FROM dbo._etblPriceListPrices price WITH (NOLOCK)
        INNER JOIN _etblPriceListName price_names WITH (NOLOCK)
            ON price.iPriceListNameID = price_names.IDPriceListName
            AND price.iStockID = st.StockLink
            AND price.iWarehouseID = 0
        ORDER BY price_names.cName
        for xml path('')),1,1,'')
                                                                        AS 'csv_price_tiers',

    'Mstr'                                                              AS 'default_qty_availability',
  -- SELECT all warehouses AND SUM all bins
  STUFF((
    SELECT
      CONVERT(VARCHAR, whse_mst.Code) + '|' +
      CONVERT(VARCHAR, ISNULL(sq.QtyOnHand, 0)) + ','
    FROM (
      SELECT ssq.StockID, ssq.WhseID,
        SUM(QtyOnHand) AS QtyOnHand
      FROM _etblStockQtys ssq
      GROUP BY ssq.StockID, ssq.WhseID
    ) sq

    LEFT JOIN WhseMst whse_mst WITH (NOLOCK)
      ON whse_mst.WhseLink = 1
    WHERE sq.StockID = st.StockLink
    ORDER BY whse_mst.Code
    for xml path('')),1,0,'')
                                                                        AS 'csv_qty_availability',

    -- List of images for this product
    -- Enables automatic pulling of NEW images WHEN image IS added
    ISNULL(
        STUFF((
            SELECT ',' + CONVERT(VARCHAR, i.idInvImage)
            FROM _etblInvImages i WITH (NOLOCK)
            WHERE st.StockLink = i.iStockLink
            ORDER BY i.idInvImage
            FOR XML PATH ('')), 1, 1, ''),
        ''
    )
                                                                        AS 'meta_image_id_csv',
    grp.StGroup                                                         AS 'meta_item_group',
    grp.idGrpTbl                                                        AS 'meta_item_group_code',
    st.StockLink                                                        AS 'meta_item_code'

FROM StkItem st WITH (NOLOCK)

INNER JOIN _etblStockDetails sd WITH (NOLOCK)
	ON sd.StockID = st.StockLink
	AND sd.WhseID = st.WhseItem

LEFT JOIN GrpTbl grp WITH (NOLOCK) ON grp.idGrpTbl = sd.GroupID

WHERE ltrim(rtrim(lower(st.ulIIWebSync))) in ('active','delete')
;