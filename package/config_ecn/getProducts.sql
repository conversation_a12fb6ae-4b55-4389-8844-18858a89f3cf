select *
from (
select
    row_number() over (order by oitm.ItemCode) as n,

    -- There must only be one result row per ItemCode!
    oitm.ItemCode               as "source_product_code",
    oitm.ItemCode               as "source_variant_code",
    oitm.ItemName               as "title",
    oitb.ItmsGrpNam             as "collection",
    oitm.FrgnName               as "product_type",
    oitm.U_B2B_Brand            as "vendor",
    ''                          as "variants.option1",
    ''                          as "variants.option2",
    ''                          as "variants.option3",
    ''                          as "body_html",
    0                           as "variants.weight",
    oitm.CodeBars               as "variants.barcode",

    -- TODO Celeste will confirm pricing structure
	-- All price tiers
    '4'                         as "default_price_tier",
    stuff(
    (select
    convert(varchar, itm1.PriceList) + '|' +
    convert(varchar, itm1.Price) + ','
    from itm1 WITH (nolock)
    where itm1.ItemCode = oitm.ItemCode
    order by itm1.PriceList
    for xml path('')),1,0,'')
                                as "csv_price_tiers",

    -- All qty availability
	-- Main warehouse INDJHB JHB001 JHBDIR
    oitm.DfltWH                 as "default_qty_availability",
    stuff(
    (select
    convert(varchar, oitw.WhsCode) + '|' +
    convert(varchar, oitw.OnHand-oitw.IsCommited)+','
    from oitw WITH (nolock)
    where oitw.ItemCode = oitm.ItemCode
    order by oitw.WhsCode
    for xml path('')),1,0,'')
                                as "csv_qty_availability",

    CASE LOWER(U_Non_Stock_Item)
      WHEN 'y' THEN 'false'
      ELSE 'true'
    END                         as "variants.inventory_management",

    case oitm.U_B2B
        when 1 then 'true'
        when 2 then 'false'
        else 'false'
    end                         as "product_active",

    ''                          as "tags",

    oitb.ItmsGrpCod             as "meta_item_group_code",
    oitb.ItmsGrpNam             as "meta_item_group_name",
    oitm.FrgnName               as "meta_foreign_name",
    oitm.U_B2B_Solution         as "meta_solution",
    oitm.DfltWH                 as "meta_warehouse_code",

    -- Discount for this product group for everyone
    COALESCE((
    SELECT
        case when DATEADD(day, DATEDIFF(day,0,GETDATE()),0) <= T0.ValidTo
        then T1.[Discount]
        else '0'
        end
      FROM OEDG T0 WITH (NOLOCK) INNER JOIN EDG1 T1 ON T0.[AbsEntry] = T1.[AbsEntry]
      WHERE
      T1.[ObjKey] = convert(varchar, oitm.ItmsGrpCod)
     and T1.ObjType = 52 -- Discount Groups
     and T0.Type = 'A'
    ), 0)                           as 'meta_discount',

     -- Discount per customer for product groups
    stuff(
    (SELECT
        convert(varchar, T0.ObjCode) + '|' +
        case when DATEADD(day, DATEDIFF(day,0,GETDATE()),0) <= T0.ValidTo
        then convert(varchar, T1.[Discount])+','
        else '0,'
        end
      FROM OEDG T0 WITH (NOLOCK) INNER JOIN EDG1 T1 ON T0.[AbsEntry] = T1.[AbsEntry]
      WHERE
      T1.[ObjKey] = convert(varchar, oitm.ItmsGrpCod)
      and T1.ObjType = 52 -- Discount Groups
	    and T0.Type = 'S'
      and T1.Discount > 0
      order by T0.ObjCode
      for xml path('')),1,0,'')       as 'csv_discount_customer_groups',

       -- Discount per customer for specific item
    stuff(
    (SELECT
        convert(varchar, T0.ObjCode) + '|' +
        case when DATEADD(day, DATEDIFF(day,0,GETDATE()),0) <= T0.ValidTo
        then convert(varchar, T1.[Discount])+','
        else '0,'
        end
      FROM OEDG T0 WITH (NOLOCK) INNER JOIN EDG1 T1 ON T0.[AbsEntry] = T1.[AbsEntry]
      WHERE
      T1.[ObjKey] = convert(varchar, oitm.ItemCode)
      and T1.ObjType = 4 -- Specific customer
	    and T0.Type = 'S'
      order by T0.ObjCode
      for xml path('')),1,0,'')       as 'csv_discount_customer',

    -- Special prices for all periods and volumes
    stuff(
    (select
    convert(varchar, ospp.CardCode) + '|' +
    convert(varchar, ospp.Discount)+','
    from ospp WITH (nolock)
    where ospp.ItemCode = oitm.ItemCode
    -- and ospp.Discount > 0
    order by ospp.ItemCode
    for xml path('')),1,0,'')
                                as 'csv_special_prices'

    -- Special prices for period join ospp with spp1
    -- Special prices for volume join ospp with spp2

from oitm WITH (nolock)
join oitb on oitm.ItmsGrpCod = oitb.ItmsGrpCod
where (
    U_B2B = 1 or
    U_B2B = 2
)

) as rows
where
n > '%(audit_lower_limit)n'
and n <= '%(audit_upper_limit)n'


