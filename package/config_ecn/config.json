{"dbName": "local.db", "dsn": "Driver={SQL Server};Server=SQL-TESTER\\MSSQLSERVER14;Database=ECN_S2S;Uid=Stock2Shop;Pwd=*******;", "dsnNoDb": "Driver={SQL Server Native Client 11.0};Server=serverName\\dbInstance;Uid=user;Pwd=****;", "server": "SQL-TESTER\\MSSQLSERVER14", "companyDb": "ECN_S2S", "userName": "ChrSoh00", "****word": "Ecn@123", "dbServerType": "MSSQL2014", "dbUserName": "sa", "dbPassword": "Paz@123$", "licenseServer": "**************", "audit_limit": 2000, "push": {"source_id": 640, "limit": 500, "token": "fa24d1df16aed55eba68b025445802edeede663f", "writeLog": "http://localhost:1337/writeLog", "getProducts": "http://localhost:1337/sapone/getProductsBatch", "getCustomers": "http://localhost:1337/sapone/getCustomersBatch", "countProducts": "http://localhost:1337/sapone/countProducts", "countCustomers": "http://localhost:1337/sapone/countCustomers", "getMeta": "http://localhost:1337/sapone/getMeta", "setMeta": "http://localhost:1337/sapone/setMeta", "productsQueue": "https://app.stock2shop.com/v1/products/queue", "customersQueue": "https://app.stock2shop.com/v1/customers/queue"}}