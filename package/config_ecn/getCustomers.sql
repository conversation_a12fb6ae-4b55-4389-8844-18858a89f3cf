select *
from (
select
    row_number() over (order by ocrd.CardCode) as n,

    ocpr.CardCode + '-' + convert(varchar, ocpr.CntctCode)   as 'source_customer_code',
    ocrd.CardName    as 'card_name',
    ocpr.FirstName   as 'first_name',
    ocpr.LastName    as 'last_name',
    ocpr.E_MailL     as 'email',
    case lower(ocpr.Notes2)
        when 'y' then 'false'
        else 'true'
    end        as "customer_active",

    1               as 'accepts_marketing',
    crd1.Block      as 'address.address1',
    crd1.Address2   as 'address.address2',
    crd1.City       as 'address.city',
    crd1.Country    as 'address.country',
    crd1.Country    as 'address.country_code',
    crd1.County     as 'address.province',
    crd1.State      as 'address.province_code',
    crd1.ZipCode    as 'address.zip',
    ocrd.CardName   as 'address.company',
    ocrd.CntctPrsn  as 'address.contact_person',
    ocrd.Phone1     as 'address.phone',

    -- XML addresses
    (
      SELECT
          crd1.Address    as 'address_code',
          CASE crd1.AdresType
          WHEN 'B' THEN 'billing'
          WHEN 'S' THEN 'shipping'
          END
                          as 'type',
          coalesce(crd1.Block, '')      as 'address1',
          coalesce(crd1.Address2, '')   as 'address2',
          coalesce(crd1.City, '')       as 'city',
          coalesce(crd1.Country, '')    as 'country',
          coalesce(crd1.Country, '')    as 'country_code',
          coalesce(crd1.County, '')     as 'province',
          coalesce(crd1.State, '')      as 'province_code',
          coalesce(crd1.ZipCode, '')    as 'zip',
          coalesce(ocrd.Phone1, '')     as 'phone',
          coalesce(ocrd.CardName, '')   as 'company',
          coalesce(ocpr.FirstName, '')  as 'first_name',
          coalesce(ocpr.LastName, '')   as 'last_name'
       FROM crd1
       WHERE crd1.CardCode=ocpr.CardCode and crd1.AdresType in ('S','B')
       ORDER BY crd1.Address
       FOR xml PATH ('address'), root ('addresses')
    )                                   as 'xml_addresses',

    ocrd.GroupCode                      as 'meta_customer_group_code',
	  case when ocrd.VatStatus = 'N' then '0'
        else '15'
    end
	                                    as 'meta_tax_rate',
    ocrd.U_CrossBorder
                                        as 'meta_cross_border',
    ocrd.U_CrossBorderD
                                        as 'meta_cross_border_delivery',
    CASE lower(ocrd.U_CrossBorder)
      WHEN 'y' THEN 'oQuotations'
      ELSE 'oOrders'
    END
                                        as 'meta_order_document_type',
    ocpr.CardCode                       as 'meta_card_code',
    ocrd.SlpCode                        as 'meta_sales_rep_code',
	  OSLP.SlpName                      as 'meta_sales_rep',
    ocrd.Balance                        as 'meta_balance',
    'R' + convert(varchar ,FORMAT(ocrd.Balance, 'N', 'en-us'))
                                        as 'meta_balance_display',
    ocrd.CreditLine                     as 'meta_credit',
    'R' + convert(varchar ,FORMAT(ocrd.CreditLine, 'N', 'en-us'))
                                        as 'meta_credit_display',

    -- Discount groups
    stuff(
    (select
    convert(varchar, ospg.ObjKey) + '|' + -- oitb.ItmsGrpCod
    convert(varchar, ospg.Discount) + ','
    from ospg with (nolock)
    where ospg.CardCode = ocrd.CardCode
    and ospg.ObjType = 52 -- Discount Groups
    and ospg.Discount > 0
    order by ospg.ObjKey
    for xml path('')),1,0,'')
                                        as 'csv_discount_groups',
               -- For customers

    ListNum                             as 'price_tier',
    ''                                  as 'qty_availability',
    crd1.U_SalesProgram                 as 'meta_sales_program',
    crd1.U_BU                           as 'meta_BU',
    OHEM.U_ENP_CostingCode              as 'meta_CostingCode',
    OHEM.U_ENP_CostingCode2             as 'meta_CostingCode2',
    OHEM.U_ENP_CostingCode3             as 'meta_CostingCode3'


from ocpr with (nolock)
INNER JOIN ocrd WITH (NOLOCK) on ocrd.CardCode=ocpr.CardCode
LEFT JOIN crd1 WITH (NOLOCK) on crd1.CardCode=ocpr.CardCode and crd1.Address = ocrd.ShipToDef and crd1.AdresType='S'
LEFT JOIN OSLP WITH (NOLOCK) on ocrd.SlpCode = OSLP.SlpCode
LEFT JOIN OHEM WITH (NOLOCK) on ocrd.SlpCode = OHEM.SalesPrson
where
lower(ocpr.Notes1) = 'y'
and CardType = 'C'
and datalength(ocpr.E_MailL) <> 0
and ocpr.E_MailL is not null

) as rows
where
n > '%(audit_lower_limit)n'
and n <= '%(audit_upper_limit)n'