select count(*) as count
from ocpr with (nolock)
INNER JOIN ocrd WITH (NOLOCK) on ocrd.CardCode=ocpr.CardCode
LEFT JOIN crd1 WITH (NOLOCK) on crd1.CardCode=ocpr.CardCode and crd1.Address = ocrd.ShipToDef and crd1.AdresType='S'
LEFT JOIN OSLP WITH (NOLOCK) on ocrd.SlpCode = OSLP.SlpCode
LEFT JOIN OHEM WITH (NOLOCK) on ocrd.SlpCode = OHEM.SalesPrson
where
lower(ocpr.Notes1) = 'y'
and CardType = 'C'
and datalength(ocpr.E_MailL) <> 0
and ocpr.E_MailL is not null
;