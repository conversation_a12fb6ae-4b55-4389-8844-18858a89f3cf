SELECT
    RTRIM(i.ItemCode)         AS "source_product_code",
    RTRIM(i.ItemCode)         AS "source_variant_code",
    RTRIM(i.Description)      AS "title",
    RTRIM(i.ItemCode)         AS "variants.sku",
    RTRIM(ic.ICDesc)          AS "collection",
    RTRIM(ig.Description)     AS "product_type",
    ''                        AS "vendor",
    ''                        AS "variants.option1",
    ''                        AS "variants.option2",
    ''                        AS "variants.option3",
    ''                        AS "body_html",
    RTRIM(i.NettMass)         AS "variants.weight",
    RTRIM(i.Barcode)          AS "variants.barcode",

    -- Warehouse 1 qty
    (
      (
        (wh1.OpeningQty + wh1.QtyBuyLast + wh1.QtyAdjustLast - wh1.QtySellLast) +
        (wh1.QtyBuyThis01 + wh1.QtyBuyThis02 + wh1.QtyBuyThis03 + wh1.QtyBuyThis04 +
            wh1.QtyBuyThis05 + wh1.QtyBuyThis06 + wh1.QtyBuyThis07 + wh1.QtyBuyThis08 +
            wh1.QtyBuyThis09 + wh1.QtyBuyThis10 + wh1.QtyBuyThis11 + wh1.QtyBuyThis12 +
            wh1.QtyBuyThis13) +
        (wh1.QtyAdjustThis01 + wh1.QtyAdjustThis02 + wh1.QtyAdjustThis03 +
            wh1.QtyAdjustThis04 + wh1.QtyAdjustThis05 + wh1.QtyAdjustThis06 +
            wh1.QtyAdjustThis07 + wh1.QtyAdjustThis08 + wh1.QtyAdjustThis09 +
            wh1.QtyAdjustThis10 + wh1.QtyAdjustThis11 + wh1.QtyAdjustThis12 +
            wh1.QtyAdjustThis13) -
        (wh1.QtySellThis01 + wh1.QtySellThis02 + wh1.QtySellThis03 + wh1.QtySellThis04 +
            wh1.QtySellThis05 + wh1.QtySellThis06 + wh1.QtySellThis07 + wh1.QtySellThis08 +
            wh1.QtySellThis09 + wh1.QtySellThis10 + wh1.QtySellThis11 + wh1.QtySellThis12 +
            wh1.QtySellThis13)
      )
    )                         AS "qty_on_hand",
    (
      SELECT
        SUM(QtyLeft)

      FROM HistoryLines hl WHERE
        hl.Documenttype = 102 AND
        hl.ItemCode = i.ItemCode AND
        hl.MultiStore = wh1.StoreCode
    )                         AS "qty_on_sales_order",

    -- Batches
--    -(ISNULL(U.SalesOrder, 0) + ISNULL(U.BatchQty, 0))
--                              AS "qty_on_sales_order",

    -- Note that price can be SET BY warehouse (store)
    '01_Incl'                      AS "default_price_tier",
    (
    '01_Incl|' + CAST(CAST(wh1.SellIncl01 AS DECIMAL (10,2)) AS VARCHAR(30)) + ',' +
    '02_Incl|' + CAST(CAST(wh1.SellIncl02 AS DECIMAL (10,2)) AS VARCHAR(30)) + ',' +
    '03_Incl|' + CAST(CAST(wh1.SellIncl03 AS DECIMAL (10,2)) AS VARCHAR(30)) + ',' +
    '04_Incl|' + CAST(CAST(wh1.SellIncl04 AS DECIMAL (10,2)) AS VARCHAR(30)) + ',' +
    '05_Incl|' + CAST(CAST(wh1.SellIncl05 AS DECIMAL (10,2)) AS VARCHAR(30)) + ',' +
    '06_Incl|' + CAST(CAST(wh1.SellIncl06 AS DECIMAL (10,2)) AS VARCHAR(30)) + ',' +
    '07_Incl|' + CAST(CAST(wh1.SellIncl07 AS DECIMAL (10,2)) AS VARCHAR(30)) + ',' +
    '08_Incl|' + CAST(CAST(wh1.SellIncl08 AS DECIMAL (10,2)) AS VARCHAR(30)) + ',' +
    '09_Incl|' + CAST(CAST(wh1.SellIncl09 AS DECIMAL (10,2)) AS VARCHAR(30)) + ',' +
    '10_Incl|' + CAST(CAST(wh1.SellIncl10 AS DECIMAL (10,2)) AS VARCHAR(30)) + ',' +
    'special_Incl|' + CAST(CAST(wh1.SpecialPriceIncl AS DECIMAL (10,2)) AS VARCHAR(30)) + ',' +
    '01_Excl|' + CAST(CAST(wh1.SellExcl01 AS DECIMAL (10,2)) AS VARCHAR(30)) + ',' +
    '02_Excl|' + CAST(CAST(wh1.SellExcl02 AS DECIMAL (10,2)) AS VARCHAR(30)) + ',' +
    '03_Excl|' + CAST(CAST(wh1.SellExcl03 AS DECIMAL (10,2)) AS VARCHAR(30)) + ',' +
    '04_Excl|' + CAST(CAST(wh1.SellExcl04 AS DECIMAL (10,2)) AS VARCHAR(30)) + ',' +
    '05_Excl|' + CAST(CAST(wh1.SellExcl05 AS DECIMAL (10,2)) AS VARCHAR(30)) + ',' +
    '06_Excl|' + CAST(CAST(wh1.SellExcl06 AS DECIMAL (10,2)) AS VARCHAR(30)) + ',' +
    '07_Excl|' + CAST(CAST(wh1.SellExcl07 AS DECIMAL (10,2)) AS VARCHAR(30)) + ',' +
    '08_Excl|' + CAST(CAST(wh1.SellExcl08 AS DECIMAL (10,2)) AS VARCHAR(30)) + ',' +
    '09_Excl|' + CAST(CAST(wh1.SellExcl09 AS DECIMAL (10,2)) AS VARCHAR(30)) + ',' +
    '10_Excl|' + CAST(CAST(wh1.SellExcl10 AS DECIMAL (10,2)) AS VARCHAR(30)) + ',' +
    'special_Excl|' + CAST(CAST(wh1.SpecialPriceExcl AS DECIMAL (10,2)) AS VARCHAR(30)))
                              AS "csv_price_tiers",
    CASE
        WHEN rtrim(i.UserDefNum01) = 1 THEN 'true'
        ELSE 'false'
    END                       AS "product_active",
    ''                        AS "tags",
    'true'                    AS "variants.inventory_management",
    i.Picture                 AS "picture",
    RTRIM(i.UserDefNum01)     AS "meta_user_def_num_1",
    RTRIM(i.UserDefNum02)     AS "meta_user_def_num_2",
    RTRIM(i.UserDefNum03)     AS "meta_user_def_num_3",
    RTRIM(i.UserDefText01)    AS "meta_user_def_text_1",
    RTRIM(i.UserDefText02)    AS "meta_user_def_text_2",
    RTRIM(i.UserDefText03)    AS "meta_user_def_text_3"

FROM Inventory i

    -- Warehouse 1
    LEFT JOIN MultiStoreTrn wh1 ON
      wh1.ItemCode = i.ItemCode AND
      wh1.StoreCode = '%(multi_store)s'
    LEFT JOIN InventoryGroups ig ON
      ig.InvGroup = wh1.InvGroup

    -- Attribute info
    LEFT JOIN InventoryCategory ic ON
      i.Category = ic.ICCode

    -- Batches
--    LEFT JOIN Unposted U ON
--      i.ItemCode = U.ItemCode AND '%(multi_store)s' = U.StoreCode

WHERE RTRIM(i.ItemCode) = '%(sku)s'
;