select count(*) as product_count
from Inventory i

    -- Warehouse 1
    left join MultiStoreTrn wh1 on
      wh1.ItemCode = i.ItemCode and
      wh1.StoreCode = '%(multi_store)s'
    left join InventoryGroups ig on
      ig.InvGroup = wh1.InvGroup

    -- Attribute info
    left join InventoryCategory ic on
      i.Category = ic.ICCode

    LEFT JOIN Unposted U on i.ItemCode = U.ItemCode AND '%(multi_store)s' = U.StoreCode

    WHERE wh1.StoreCode = '%(multi_store)s' AND RTRIM(i.UserDefText01) <> '0';