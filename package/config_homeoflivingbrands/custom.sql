select
    ITM.ITMREF_0                               as "source_product_code",
    ITM.ITMREF_0                               as "source_variant_code",
    ISNULL(IDS.TEXTE_0,ITM.ITMDES1_0)          as "title",
    ISNULL(IDV.TEXTE_0,ITM.TCLCOD_0)           as "collection",
    ISNULL(CAT.TEXTE_0,ITM.TSICOD_0)           as "product_type",
    ISNULL(BRD.TEXTE_0,ITM.TSICOD_2)           as "vendor",
    ITM.ITMREF_0                               as "variants.sku",
    ''                                         as "body_html",
    0                                          as "variants.weight",
    ''                                         as "variants.barcode",
    cast(ISNULL(((STK.PHYSTO_0 - STK.SALSTO_0) - STK.GLOALL_0),0) as int)
                                               as "variants.qty",
    cast(ISNULL(SP1.PRI_0,0) as float)         as "variants.retail_price",
    'true'                                     as "variants.inventory_management",

    CASE
        WHEN ITM.TSICOD_3 = 'A' THEN 'true'
        WHEN ITM.TSICOD_3 = 'D' AND cast(ISNULL(((STK.PHYSTO_0 - STK.SALSTO_0) - STK.GLOALL_0),0) as int) > 0
                 THEN 'true'
                 ELSE 'false'
            END                                as "product_active",
    ''                                         as "tags",
    ISNULL(IDV.TEXTE_0,ITM.TCLCOD_0)           as "meta_division",
    ISNULL(CAT.TEXTE_0,ITM.TSICOD_0)           as "meta_category",
    ISNULL(SCT.TEXTE_0,ITM.TSICOD_1)           as "meta_sub_category",
    ISNULL(BRD.TEXTE_0,ITM.TSICOD_2)           as "meta_brand",
    ISNULL(SP2.PRI_0,0)                        as "meta_was",               -- Previous price on ECOM price list
    ISNULL(SP1.PRI_0,0)                        as "meta_now",               -- Current price on ECOM price list
    ISNULL(convert(varchar,SP1.PLISTRDAT_0, 112),'19000101')
                                               as "meta_now_start_date",    -- Price List Start Date
    ISNULL(convert(varchar,SP1.PLIENDDAT_0, 112),'19000101')
                                               as "meta_now_end_date"      -- Price List End Date

from HOLBLIVE.ITMMASTER ITM

LEFT JOIN   HOLBLIVE.ATEXTRA    IDS ON  ITM.ITMREF_0 = IDS.IDENT1_0
                                    AND IDS.CODFIC_0 = 'ITMMASTER'
                                    AND IDS.ZONE_0   = 'DES1AXX'
                                    AND IDS.LANGUE_0 = 'ENG'
LEFT JOIN   HOLBLIVE.ITMCATEG   ITG ON  ITM.TCLCOD_0 = ITG.TCLCOD_0
                                    AND ITG.STOFCY_0 = ''
LEFT JOIN   HOLBLIVE.ATEXTRA    IDV ON  ITM.TCLCOD_0 = IDV.IDENT1_0
                                    AND IDV.CODFIC_0 = 'ITMCATEG'
                                    AND IDV.ZONE_0   = 'TCLAXX'
                                    AND IDV.LANGUE_0 = 'ENG'
LEFT JOIN   HOLBLIVE.ATEXTRA    CAT ON  ITM.TSICOD_0 = CAT.IDENT2_0
                                    AND CAT.CODFIC_0 = 'ATABDIV'
                                    AND CAT.IDENT1_0 = '20'
                                    AND CAT.ZONE_0   = 'LNGDES'
                                    AND CAT.LANGUE_0 = 'ENG'
LEFT JOIN   HOLBLIVE.ATEXTRA    SCT ON  ITM.TSICOD_1 = SCT.IDENT2_0
                                    AND SCT.CODFIC_0 = 'ATABDIV'
                                    AND SCT.IDENT1_0 = '21'
                                    AND SCT.ZONE_0   = 'LNGDES'
                                    AND SCT.LANGUE_0 = 'ENG'
LEFT JOIN   HOLBLIVE.ATEXTRA    BRD ON  ITM.TSICOD_2 = BRD.IDENT2_0
                                    AND BRD.CODFIC_0 = 'ATABDIV'
                                    AND BRD.IDENT1_0 = '22'
                                    AND BRD.ZONE_0   = 'LNGDES'
                                    AND BRD.LANGUE_0 = 'ENG'
LEFT JOIN   HOLBLIVE.ITMMVT     STK on ITM.ITMREF_0 = STK.ITMREF_0
                                    AND STK.STOFCY_0 = 'ROODE'

LEFT JOIN       (SELECT      PLICRI1_0
                       ,PLISTRDAT_0
                       ,PLIENDDAT_0
                       ,PRI_0
                       ,ROW_NUMBER() OVER(PARTITION BY PLICRI1_0 ORDER BY PLISTRDAT_0 DESC, CREDATTIM_0 DESC) AS LSTORD
            FROM        HOLBLIVE.SPRICLIST
            WHERE       PLI_0 = 'P20'
            AND         PLICRI2_0 = 'RSP'
           )                    SP1 ON  ITM.ITMREF_0 = SP1.PLICRI1_0
                                    AND SP1.LSTORD = 1
LEFT JOIN  (SELECT      PLICRI1_0
                       ,PLISTRDAT_0
                       ,PLIENDDAT_0
                       ,PRI_0
                       ,ROW_NUMBER() OVER(PARTITION BY PLICRI1_0 ORDER BY PLISTRDAT_0 DESC, CREDATTIM_0 DESC) AS LSTORD
            FROM        HOLBLIVE.SPRICLIST
            WHERE       PLI_0 = 'P20'
            AND         PLICRI2_0 = 'RSP'
           )                    SP2 ON  ITM.ITMREF_0 = SP2.PLICRI1_0
                                    AND SP1.LSTORD = 2
WHERE ITM.ITMREF_0 = '857485';