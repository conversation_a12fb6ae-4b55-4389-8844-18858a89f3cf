{"dbName": "local.db", "dsn": "Driver={SQL Server Native Client 11.0};Server=HOLB-SAGE-SQL01.holb.azure\\SAGEX3;Database=x3dat;Uid=Stock2Shop_Reader;Pwd=**************;", "audit_limit": 1000, "push": {"source_id": 1260, "limit": 500, "token": "be9d0a6df5e7d15234f97f65bf4e307d57b9b06f", "writeLog": "http://localhost:13337/writeLog", "getProducts": "http://localhost:13337/odbc/getProductsBatch", "getCustomers": "http://localhost:13337/odbc/getCustomersBatch", "countProducts": "http://localhost:13337/odbc/countProducts", "countCustomers": "http://localhost:13337/odbc/countCustomers", "getMeta": "http://localhost:13337/odbc/getMeta", "setMeta": "http://localhost:13337/odbc/setMeta", "productsQueue": "https://app.stock2shop.com/v1/products/queue", "customersQueue": "https://app.stock2shop.com/v1/customers/queue"}}