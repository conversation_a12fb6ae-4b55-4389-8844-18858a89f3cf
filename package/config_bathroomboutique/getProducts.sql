SELECT
    rtrim(i.ItemCode)         AS "source_product_code",
    rtrim(i.ItemCode)         AS "source_variant_code",
    rtrim(i.Description)      AS "title",
    rtrim(i.ItemCode)         AS "variants.sku",
    rtrim(ic.ICDesc)          AS "collection",
    rtrim(ig.Description)     AS "product_type",
    ''                        AS "vendor",
    ''                        AS "variants.option1",
    ''                        AS "variants.option2",
    ''                        AS "variants.option3",
    ''                        AS "body_html",
    rtrim(i.NettMass)         AS "variants.weight",
    rtrim(i.Barcode)          AS "variants.barcode",

    -- Only Title products has their Qty divided by unitsize
    CASE
      WHEN ic.iccode BETWEEN '164' AND '224' THEN (
        FLOOR(((
          (wh1.OpeningQty + wh1.QtyBuyLast + wh1.QtyAdjustLast - wh1.QtySellLast) +
          (wh1.QtyBuyThis01 + wh1.QtyBuyThis02 + wh1.QtyBuyThis03 + wh1.QtyBuyThis04 +
              wh1.QtyBuyThis05 + wh1.QtyBuyThis06 + wh1.QtyBuyThis07 + wh1.QtyBuyThis08 +
              wh1.QtyBuyThis09 + wh1.QtyBuyThis10 + wh1.QtyBuyThis11 + wh1.QtyBuyThis12 +
              wh1.QtyBuyThis13) +
          (wh1.QtyAdjustThis01 + wh1.QtyAdjustThis02 + wh1.QtyAdjustThis03 +
              wh1.QtyAdjustThis04 + wh1.QtyAdjustThis05 + wh1.QtyAdjustThis06 +
              wh1.QtyAdjustThis07 + wh1.QtyAdjustThis08 + wh1.QtyAdjustThis09 +
              wh1.QtyAdjustThis10 + wh1.QtyAdjustThis11 + wh1.QtyAdjustThis12 +
              wh1.QtyAdjustThis13) -
          (wh1.QtySellThis01 + wh1.QtySellThis02 + wh1.QtySellThis03 + wh1.QtySellThis04 +
              wh1.QtySellThis05 + wh1.QtySellThis06 + wh1.QtySellThis07 + wh1.QtySellThis08 +
              wh1.QtySellThis09 + wh1.QtySellThis10 + wh1.QtySellThis11 + wh1.QtySellThis12 +
              wh1.QtySellThis13)
        ) - 50) / CAST(i.UnitSize AS DECIMAL(3,2)))
      )
      ELSE (
        FLOOR((
          (wh1.OpeningQty + wh1.QtyBuyLast + wh1.QtyAdjustLast - wh1.QtySellLast) +
          (wh1.QtyBuyThis01 + wh1.QtyBuyThis02 + wh1.QtyBuyThis03 + wh1.QtyBuyThis04 +
              wh1.QtyBuyThis05 + wh1.QtyBuyThis06 + wh1.QtyBuyThis07 + wh1.QtyBuyThis08 +
              wh1.QtyBuyThis09 + wh1.QtyBuyThis10 + wh1.QtyBuyThis11 + wh1.QtyBuyThis12 +
              wh1.QtyBuyThis13) +
          (wh1.QtyAdjustThis01 + wh1.QtyAdjustThis02 + wh1.QtyAdjustThis03 +
              wh1.QtyAdjustThis04 + wh1.QtyAdjustThis05 + wh1.QtyAdjustThis06 +
              wh1.QtyAdjustThis07 + wh1.QtyAdjustThis08 + wh1.QtyAdjustThis09 +
              wh1.QtyAdjustThis10 + wh1.QtyAdjustThis11 + wh1.QtyAdjustThis12 +
              wh1.QtyAdjustThis13) -
          (wh1.QtySellThis01 + wh1.QtySellThis02 + wh1.QtySellThis03 + wh1.QtySellThis04 +
              wh1.QtySellThis05 + wh1.QtySellThis06 + wh1.QtySellThis07 + wh1.QtySellThis08 +
              wh1.QtySellThis09 + wh1.QtySellThis10 + wh1.QtySellThis11 + wh1.QtySellThis12 +
              wh1.QtySellThis13)
        ) - 4)
      )
    END                      AS "qty_on_hand",

    -- Batches
   -(ISNULL(U.SalesOrder, 0) + ISNULL(U.BatchQty, 0))
                             AS "qty_on_sales_order",

    -- Note that price can be SET BY warehouse (store)
    '07'                     AS "default_price_tier",

    -- Only title products have their prices multiplied by UnitSize
    CASE
      WHEN ic.iccode BETWEEN '164' AND '224' THEN (
      (
        '01|' + REPLACE(CAST((wh1.SellExcl01 * CAST(i.UnitSize AS DECIMAL(3,2))) AS VARCHAR(30)), ',', '.') + ',' +
        '02|' + REPLACE(CAST((wh1.SellExcl02 * CAST(i.UnitSize AS DECIMAL(3,2))) AS VARCHAR(30)), ',', '.') + ',' +
        '03|' + REPLACE(CAST((wh1.SellExcl03 * CAST(i.UnitSize AS DECIMAL(3,2))) AS VARCHAR(30)), ',', '.') + ',' +
        '04|' + REPLACE(CAST((wh1.SellExcl04 * CAST(i.UnitSize AS DECIMAL(3,2))) AS VARCHAR(30)), ',', '.') + ',' +
        '05|' + REPLACE(CAST((wh1.SellExcl05 * CAST(i.UnitSize AS DECIMAL(3,2))) AS VARCHAR(30)), ',', '.') + ',' +
        '06|' + REPLACE(CAST((wh1.SellExcl06 * CAST(i.UnitSize AS DECIMAL(3,2))) AS VARCHAR(30)), ',', '.') + ',' +
        '07|' + REPLACE(CAST((wh1.SellExcl07 * CAST(i.UnitSize AS DECIMAL(3,2))) AS VARCHAR(30)), ',', '.') + ',' +
        '08|' + REPLACE(CAST((wh1.SellExcl08 * CAST(i.UnitSize AS DECIMAL(3,2))) AS VARCHAR(30)), ',', '.') + ',' +
        '09|' + REPLACE(CAST((wh1.SellExcl09 * CAST(i.UnitSize AS DECIMAL(3,2))) AS VARCHAR(30)), ',', '.') + ',' +
        '10|' + REPLACE(CAST((wh1.SellExcl10 * CAST(i.UnitSize AS DECIMAL(3,2))) AS VARCHAR(30)), ',', '.') + ',' +
        'special|' + REPLACE(CAST((wh1.SpecialPriceExcl * CAST(i.UnitSize AS DECIMAL(3,2))) AS VARCHAR(30)), ',', '.') + ',' +
        '01_incl|' + REPLACE(CAST((wh1.SellIncl01 * CAST(i.UnitSize AS DECIMAL(3,2))) AS VARCHAR(30)), ',', '.') + ',' +
        '02_incl|' + REPLACE(CAST((wh1.SellIncl02 * CAST(i.UnitSize AS DECIMAL(3,2))) AS VARCHAR(30)), ',', '.') + ',' +
        '03_incl|' + REPLACE(CAST((wh1.SellIncl03 * CAST(i.UnitSize AS DECIMAL(3,2))) AS VARCHAR(30)), ',', '.') + ',' +
        '04_incl|' + REPLACE(CAST((wh1.SellIncl04 * CAST(i.UnitSize AS DECIMAL(3,2))) AS VARCHAR(30)), ',', '.') + ',' +
        '05_incl|' + REPLACE(CAST((wh1.SellIncl05 * CAST(i.UnitSize AS DECIMAL(3,2))) AS VARCHAR(30)), ',', '.') + ',' +
        '06_incl|' + REPLACE(CAST((wh1.SellIncl06 * CAST(i.UnitSize AS DECIMAL(3,2))) AS VARCHAR(30)), ',', '.') + ',' +
        '07_incl|' + REPLACE(CAST((wh1.SellIncl07 * CAST(i.UnitSize AS DECIMAL(3,2))) AS VARCHAR(30)), ',', '.') + ',' +
        '08_incl|' + REPLACE(CAST((wh1.SellIncl08 * CAST(i.UnitSize AS DECIMAL(3,2))) AS VARCHAR(30)), ',', '.') + ',' +
        '09_incl|' + REPLACE(CAST((wh1.SellIncl09 * CAST(i.UnitSize AS DECIMAL(3,2))) AS VARCHAR(30)), ',', '.') + ',' +
        '10_incl|' + REPLACE(CAST((wh1.SellIncl10 * CAST(i.UnitSize AS DECIMAL(3,2))) AS VARCHAR(30)), ',', '.') + ',' +
        'special_incl|' + REPLACE(CAST((wh1.SpecialPriceIncl * CAST(i.UnitSize AS DECIMAL(3,2))) AS VARCHAR(30)), ',', '.'))
      )
      ELSE (
      (
        '01|' + REPLACE(CAST(wh1.SellExcl01 AS VARCHAR(30)), ',', '.') + ',' +
        '02|' + REPLACE(CAST(wh1.SellExcl02 AS VARCHAR(30)), ',', '.') + ',' +
        '03|' + REPLACE(CAST(wh1.SellExcl03 AS VARCHAR(30)), ',', '.') + ',' +
        '04|' + REPLACE(CAST(wh1.SellExcl04 AS VARCHAR(30)), ',', '.') + ',' +
        '05|' + REPLACE(CAST(wh1.SellExcl05 AS VARCHAR(30)), ',', '.') + ',' +
        '06|' + REPLACE(CAST(wh1.SellExcl06 AS VARCHAR(30)), ',', '.') + ',' +
        '07|' + REPLACE(CAST(wh1.SellExcl07 AS VARCHAR(30)), ',', '.') + ',' +
        '08|' + REPLACE(CAST(wh1.SellExcl08 AS VARCHAR(30)), ',', '.') + ',' +
        '09|' + REPLACE(CAST(wh1.SellExcl09 AS VARCHAR(30)), ',', '.') + ',' +
        '10|' + REPLACE(CAST(wh1.SellExcl10 AS VARCHAR(30)), ',', '.') + ',' +
        'special|' + REPLACE(CAST(wh1.SpecialPriceExcl AS VARCHAR(30)), ',', '.') + ',' +
        '01_incl|' + REPLACE(CAST(wh1.SellIncl01 AS VARCHAR(30)), ',', '.') + ',' +
        '02_incl|' + REPLACE(CAST(wh1.SellIncl02 AS VARCHAR(30)), ',', '.') + ',' +
        '03_incl|' + REPLACE(CAST(wh1.SellIncl03 AS VARCHAR(30)), ',', '.') + ',' +
        '04_incl|' + REPLACE(CAST(wh1.SellIncl04 AS VARCHAR(30)), ',', '.') + ',' +
        '05_incl|' + REPLACE(CAST(wh1.SellIncl05 AS VARCHAR(30)), ',', '.') + ',' +
        '06_incl|' + REPLACE(CAST(wh1.SellIncl06 AS VARCHAR(30)), ',', '.') + ',' +
        '07_incl|' + REPLACE(CAST(wh1.SellIncl07 AS VARCHAR(30)), ',', '.') + ',' +
        '08_incl|' + REPLACE(CAST(wh1.SellIncl08 AS VARCHAR(30)), ',', '.') + ',' +
        '09_incl|' + REPLACE(CAST(wh1.SellIncl09 AS VARCHAR(30)), ',', '.') + ',' +
        '10_incl|' + REPLACE(CAST(wh1.SellIncl10 AS VARCHAR(30)), ',', '.') + ',' +
        'special_incl|' + REPLACE(CAST(wh1.SpecialPriceIncl AS VARCHAR(30)), ',', '.'))
      )
    END                      AS "csv_price_tiers",
    CASE
        WHEN i.UserDefNum02 = 1 THEN 'true'
        ELSE 'false'
    END                       AS "product_active",
    ''                        AS "tags",
    'true'                    AS "variants.inventory_management",
    i.Picture                 AS "picture",
    rtrim(i.UserDefText01)    AS "meta_user_def_text_1",
    rtrim(i.UserDefText02)    AS "meta_user_def_text_2",
    rtrim(i.UserDefText03)    AS "meta_user_def_text_3",
    CASE
      WHEN ic.iccode BETWEEN '164' AND '224' THEN (
        CAST(i.UnitSize AS DECIMAL(3,2))
      )
      ELSE 0
    END                               AS "meta_unit_size"

FROM Inventory i

    -- Warehouse 1
    LEFT JOIN MultiStoreTrn wh1 ON
      wh1.ItemCode = i.ItemCode AND
      wh1.StoreCode = '%(multi_store)s'
    LEFT JOIN InventoryGroups ig ON
      ig.InvGroup = wh1.InvGroup

    -- Attribute info
    LEFT JOIN InventoryCategory ic ON
      i.Category = ic.ICCode

    -- Batches
   LEFT JOIN Unposted U ON
     i.ItemCode = U.ItemCode AND '%(multi_store)s' = U.StoreCode

WHERE i.Physical = 1 
    AND wh1.StoreCode = '%(multi_store)s'
    AND RTRIM(i.UserDefNum02) IN (1,2)
;