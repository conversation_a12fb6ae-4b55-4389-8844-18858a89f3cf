SELECT
    convert(varchar, ar.Customer)            AS 'source_customer_code',
    ar.Name                                  AS 'first_name',
    ar.ShortName                             AS 'last_name',
    ar.Email                                 AS 'email',

    CASE LTRIM(RTRIM(LOWER(ar2.S2sActive)))
        WHEN 'active' THEN 'true'
        ELSE 'false'
    END                                      AS 'customer_active',

    0                                        AS 'accepts_marketing',
    ar.ShipToAddr1                           AS 'address.address1',
    ar.ShipToAddr2 +
    CASE LTRIM(RTRIM(ar.ShipToAddr3Loc))
        WHEN '' THEN ''
        ELSE ', ' + ar.ShipToAddr3Loc
    END                                      AS 'address.address2',
    ar.ShipToAddr3                           AS 'address.city',
    ar.ShipToAddr5                           AS 'address.country',
    ''                                       AS 'address.country_code',
    ar.ShipToAddr4                           AS 'address.province',
    ''                                       AS 'address.province_code',
    ar.ShipPostalCode                        AS 'address.zip',
    ar.Name                                  AS 'address.company',
    ar.Telephone                             AS 'address.phone',
    ar.CreditLimit                           AS 'meta_credit_limit',
    ar.Branch                                AS 'meta_branch',
    ''                                       AS 'price_tier',

    -- set available qty based on branch
    CASE lower(ar.Branch)
        WHEN 'b1' THEN 'B1'
        WHEN 'c1' THEN 'C1'
        WHEN 'd1' THEN 'D1'
        WHEN 'g1' THEN 'G1'
        WHEN 'j1' THEN 'J1'
        WHEN 'l1' THEN 'L1'
        WHEN 'p1' THEN 'P1'
        WHEN 't1' THEN 'T1'
        
        ELSE ''
    END                                      AS 'qty_availability',

    -- for contract pricing
    ar.BuyingGroup1                          AS 'meta_buygroup',
    
    -- for buy group segmenting
    'value_' + LOWER(ar.BuyingGroup1)  + '|' AS 'segment|source|products|meta.key|contains',

    -- for branch segmenting
    LOWER(ar.Branch)                         AS 'segment|source|products|tags|contains',

    CASE
    -- Account customers = OnAccount
    -- COD customers = Adumo
    WHEN LTRIM(RTRIM(LOWER(ar2.S2sActive))) = 'active' AND ar.TermsCode = '00' THEN
        '[{
            "method": "AdumoVirtual",
            "description": "Credit Card (Adumo)",
            "iss": "Sprint Packaging",
            "auid": "67FCCEBE-E217-4E71-B094-DC23830C6DF3",
            "cuid": "F66D167A-5A06-4438-BF24-B30C239D0AF4",
            "jwt_secret": "f40084c8-a89d-4893-b8a7-382032d16550",
            "url_staging": "https://staging-apiv2.adumoonline.com/product/payment/v1/initialisevirtual",
            "url_production": "https://apiv2.adumoonline.com/product/payment/v1/initialisevirtual",
            "in_production": true,
            "currency": "zar"
        }]'
    ELSE
        '[{
            "method": "OnAccount",
            "description": "On Account"
        }]'
    END
                                            AS 'meta_payment_methods'

FROM ArCustomer ar WITH (nolock)
 LEFT JOIN [ArCustomer+] ar2 WITH (nolock)
 ON ar2.Customer = ar.Customer

WHERE ISNULL(ar.Email, '') <> ''
AND convert(varchar, ar.Customer) = '%(source_customer_code)s'
;