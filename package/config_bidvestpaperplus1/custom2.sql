SELECT

    RTRIM(LTRIM(inv.StockCode))                AS "source_product_code",
    RTRIM(LTRIM(inv.StockCode))                AS "source_variant_code",
    inv.Description                            AS "title",
    inv2.AnalysisB                             AS "collection",
    ''                                         AS "product_type",
    ''                                         AS "vendor",
    ''                                         AS "variants.option1",
    ''                                         AS "variants.option2",
    ''                                         AS "variants.option3",
    RTRIM(LTRIM(inv.StockCode))                AS "variants.sku",

    CASE inv.LongDesc
      WHEN ' '
        THEN inv.Description
      ELSE inv.LongDesc
    END                                        AS "body_html",

    0                                          AS "variants.weight",
    ''                                         AS "variants.barcode",
    'A'                                        AS "default_price_tier",
    STUFF((
      SELECT
        ',' + CONVERT(VARCHAR, LTRIM(RTRIM(price_tiers.PriceCode))) + '|' +
              CONVERT(VARCHAR, CAST(ISNULL(price_tiers.SellingPrice, 0) AS FLOAT))
      FROM InvPrice price_tiers WITH (NOLOCK)
      WHERE price_tiers.StockCode = RTRIM(LTRIM(inv.StockCode))
      ORDER BY LTRIM(RTRIM(price_tiers.PriceCode))
    for xml path('')),1,1,'')
                                               AS "csv_price_tiers",
/*
    CONTRACT PRICING:
    A contract consists of 6 parts, these are:-
       Order:    The hierarchy (order) in which to load the contracts. The first successful contract will be used
       Entity:   The entity the contract will apply to. For example, a customer would have a contract with entity "product" to apply to the product.
       Key:      The corresponding entities key to match on. In the above example, this would be "category"
       Type:     Discount or fixed. Is this contract a fixed price or discount from some other price.
       Value:    The value of the contract, in the case of type discount, this would be a percentage expressed as an integer. In the case of a fixed price, it would be the price,
    e.g. order_0|entity_product|key_product_code|value_123|type_discount~10
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    Available ContractTypes:
      B = Buying Group
      C = Customer
    Available PriceMethods:
      C = Coded Selling Price
      D = List less chain discount
      F = Fixed price
      K = Markup list price by pct
      L = List less percentage
      M = Mark-up cost by percentage
      P = Price code less chain
      Q = Quantity discount breaks
      U = Coded less unit discount

    Contract: Customer contract percentage
    Note: discount can apply to either the default price tier or channel price tier
    Requirements:
      Product.meta_stock_code

    Contract: Customer contract fixed
    Requirements:
      Product.meta_stock_code

    Contract: Buying group contract fixed
    Requirements:
      Customer.meta_customer_group
*/
    STUFF((
      SELECT
          ',order_0|entity_customer|key_buygroup|value_' + convert(VARCHAR, ltrim(rtrim(sor.CustomerBuyGrp))) + '|type_fixed~' +
          convert(VARCHAR, coalesce(sor.FixedPrice, 0))
      FROM SorContractPrice sor WITH (NOLOCK)
      WHERE sor.StockCode = inv.StockCode
      AND (DATEADD(day, DATEDIFF(day,0,GETDATE()),0) <= ExpiryDate or ExpiryDate is NULL)
      AND DATEADD(day, DATEDIFF(day,0,GETDATE()),0) >= StartDate
      AND sor.ContractType='B'
      AND sor.PriceMethod='F'
      ORDER BY LTRIM(RTRIM(sor.CustomerBuyGrp))
    for xml path('')),1,1,'')
                                                      AS "csv_contract_customer_fixed",                                               

    CASE LOWER(inv2.OnlineSync)
      WHEN 'active'
        THEN 'true'
      ELSE 'false'
    END                                        AS "product_active"

FROM InvMaster inv WITH (NOLOCK)
LEFT JOIN [InvMaster+] inv2 WITH (NOLOCK)
ON inv2.StockCode = inv.StockCode

WHERE LOWER(inv2.OnlineSync) IN ('active','delete');