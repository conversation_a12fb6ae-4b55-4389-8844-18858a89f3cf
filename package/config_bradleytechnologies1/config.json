{"dbName": "local.db", "dsn": "Driver={SQL Server};Server=SQL2\\SYSPRO8;Database=SysproCompanyB;Uid=Stock2Shop1;Pwd=*******$;", "audit_limit": 1000, "push": {"source_id": 785, "limit": 500, "token": "U7BSQ9TSHM15H6RBCYBG2WMRX8NRKGBKBC57SZDO", "writeLog": "http://localhost:13337/writeLog", "getProducts": "http://localhost:13337/bradtech1/getProductsBatch", "getCustomers": "http://localhost:13337/bradtech1/getCustomersBatch", "countProducts": "http://localhost:13337/bradtech1/countProducts", "countCustomers": "http://localhost:13337/bradtech1/countCustomers", "getMeta": "http://localhost:13337/bradtech1/getMeta", "setMeta": "http://localhost:13337/bradtech1/setMeta", "productsQueue": "https://app.stock2shop.com/v1/products/queue", "customersQueue": "https://app.stock2shop.com/v1/customers/queue"}}