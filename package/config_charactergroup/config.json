{"dbName": "local.db", "dsn": "Driver={SQL Server Native Client 11.0};Server=SYSPRO\\SYSPRO;Database=SysproCompanyK;Uid=S2S;Pwd=******;", "audit_limit": 1000, "push": {"source_id": 928, "limit": 500, "token": "DN7S20NOBPCDRYLR9HGG17VXGR85MV77V718UV0J", "writeLog": "http://localhost:1337/writeLog", "getProducts": "http://localhost:1337/odbc/getProductsBatch", "getCustomers": "http://localhost:1337/odbc/getCustomersBatch", "countProducts": "http://localhost:1337/odbc/countProducts", "countCustomers": "http://localhost:1337/odbc/countCustomers", "getMeta": "http://localhost:1337/odbc/getMeta", "setMeta": "http://localhost:1337/odbc/setMeta", "productsQueue": "https://app.stock2shop.com/v1/products/queue", "customersQueue": "https://app.stock2shop.com/v1/customers/queue"}}