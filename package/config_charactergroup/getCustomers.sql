select *
from (
select
    row_number() over (order by ar.Customer) as n,

    convert(varchar, ar.Customer)            as 'source_customer_code',

    convert(varchar, ar.Customer)            as 'first_name',
    ar.Name                                  as 'last_name',
    ar.Email                                 as 'email',
    'true'                                   as 'customer_active',
    1                                        as 'accepts_marketing',
    ar.ShipToAddr1                           as 'address.address2',
    ar.ShipToAddr2 +
    CASE LTRIM(RTRIM(ar.ShipToAddr3Loc))
    WHEN '' THEN ''
    ELSE ', ' + ar.ShipToAddr3Loc
    END                                      as 'address.address1',
    ar.ShipToAddr3                           as 'address.city',
    ar.ShipToAddr5                           as 'address.country',
    ''                                       as 'address.country_code',
    ar.ShipToAddr4                           as 'address.province',
    ''                                       as 'address.province_code',
    ar.ShipPostalCode                        as 'address.zip',
    ar.Name                                  as 'address.company',
    ar.Telephone                             as 'address.phone',
    ar.BuyingGroup1                          as 'meta_customer_group',
    ar.<PERSON>Instrs                        as 'meta_shipping_instructions',
    ar.Salesperson                           as 'meta_sales_person',
    ar.CreditLimit                           as 'meta_credit_limit',
    ar.<PERSON>                                as 'meta_branch',
    ''                                       as 'meta_terms',
    COALESCE(di.DiscountPct1, 0)             as 'meta_discount',
    ar.Area                                  as 'meta_area',
    'R' + convert(VARCHAR,convert(MONEY, arb.CurrentBalance1), 1)
                                             as 'meta_balance',
    'R' + convert(VARCHAR,convert(MONEY, arb.Val120daysInv), 1)
                                             as 'meta_balance_120',
    'R' + convert(VARCHAR,convert(MONEY, arb.Val90daysInv), 1)
                                             as 'meta_balance_90',
    'R' + convert(VARCHAR,convert(MONEY, arb.Val60daysInv), 1)
                                             as 'meta_balance_60',
    'R' + convert(VARCHAR,convert(MONEY, arb.Val30daysInv), 1)
                                             as 'meta_balance_30',
    'R' + convert(VARCHAR,convert(MONEY, arb.ValCurrentInv), 1)
                                             as 'meta_balance_current',
    'R' + convert(VARCHAR,convert(MONEY, ar.CreditLimit - arb.CurrentBalance1), 1)
                                             as 'meta_credit_available',

    ar.PriceCode                             as 'price_tier',
    ar.SalesWarehouse                        as 'qty_availability',

    STUFF((
      SELECT
      ',' + CONVERT(VARCHAR, LTRIM(RTRIM(sor.StockCode))) + '|' +
      CASE WHEN (DATEADD(day, DATEDIFF(day,0,GETDATE()),0) <= ExpiryDate or ExpiryDate is NULL) AND DATEADD(day, DATEDIFF(day,0,GETDATE()),0) >= StartDate
        THEN CONVERT(VARCHAR, LTRIM(RTRIM(sor.FixedPriceCode))) + '-' + CONVERT(VARCHAR, COALESCE(sor.Discount1, 0))
        ELSE '0-0'
      END
      FROM SorContractPrice sor WITH (NOLOCK)
      WHERE sor.CustomerBuyGrp = ar.Customer
      AND sor.ContractType='C'
      AND sor.PriceMethod in ('P', 'C')
      ORDER BY LTRIM(RTRIM(sor.StockCode))
    for xml path('')),1,1,'')
                                             AS "csv_product_code",

    STUFF((
      SELECT
      ',' + CONVERT(VARCHAR, LTRIM(RTRIM(sor.StockCode))) + '|' +

      CASE WHEN (DATEADD(day, DATEDIFF(day,0,GETDATE()),0) <= ExpiryDate or ExpiryDate is NULL) AND DATEADD(day, DATEDIFF(day,0,GETDATE()),0) >= StartDate
        THEN CONVERT(VARCHAR, COALESCE(sor.FixedPrice, 0))
        ELSE '0'
      END
      FROM SorContractPrice sor WITH (NOLOCK)
      WHERE sor.CustomerBuyGrp = ar.Customer
      AND sor.ContractType='C'
      AND sor.PriceMethod = 'F'
      ORDER BY LTRIM(RTRIM(sor.StockCode))
    for xml path('')),1,1,'')
                                             AS "csv_product_code_fixed"


FROM ArCustomer ar WITH (nolock)
 INNER JOIN ArCustomerBal arb WITH (nolock)
 ON arb.Customer = ar.Customer
 LEFT JOIN [ArCustomer+] ar2 WITH (nolock)
 ON ar2.Customer = ar.Customer
LEFT JOIN [TblSoDiscount] di WITH (nolock)
 ON di.DiscountCode = ar.LineDiscCode

  WHERE ISNULL(ar.Email, '') <> ''
  -- waiting web active from arcustomer + from client

) as rows
where
n > '%(audit_lower_limit)n'
and n <= '%(audit_upper_limit)n'