select count(*) as count
FROM InvMaster inv WITH (NOLOCK)

    -- Base Data
    LEFT JOIN [InvMaster+] inv2 WITH (nolock)
    ON inv.StockCode = inv2.StockCode

    LEFT JOIN SalProductClassDes pcl WITH (nolock)
    ON inv.ProductClass = pcl.ProductClass

    LEFT JOIN [vwAdmFormValProduct] fprod WITH (nolock)
    ON inv2.Product = fprod.Item

    LEFT JOIN [vwAdmFormValFabrication] ffab WITH (nolock)
    ON inv2.Fabrication = ffab.Item

    LEFT JOIN [vwAdmFormValBaseColour] fbc WITH (nolock)
    ON inv2.BaseColour = fbc.Item

    LEFT JOIN [vwAdmFormValGender] fgen WITH (nolock)
    ON inv2.Gender = fgen.Item

    LEFT JOIN [vwAdmFormVAlProductCategory] fpc WITH (nolock)
    ON inv2.ProductCategory = fpc.Item

    LEFT JOIN [vwAdmFormVAlAgeGroup] fag WITH (nolock)
    ON inv2.AgeGroup = fag.Item

    LEFT JOIN [vwAdmFormVAlCharacter] char1 WITH (nolock)
    ON inv2.Character1 = char1.Item

    WHERE RTRIM(LTRIM(inv2.WebStockCode)) <> '' AND LOWER(RTRIM(ISNULL(inv2.WebActive, ''))) in ('active', 'delete')
    ;