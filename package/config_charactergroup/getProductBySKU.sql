SELECT
    row_number() OVER (ORDER BY inv.StockCode)        AS n,


    RTRIM(LTRIM(inv2.WebStockCode))                   AS "source_product_code",

    RTRIM(LTRIM(inv.StockCode))                       AS "source_variant_code",
    RTRIM(LTRIM(inv2.WebDescription))                 AS "title",
    RTRIM(LTRIM(pcl.Description))                     AS "collection",
    RTRIM(LTRIM(fpc.Description))                     AS "product_type",
    RTRIM(LTRIM(ISNULL(char1.Description, '')))       AS "vendor",
    RTRIM(LTRIM(inv.StockCode))                       AS "variants.sku",
    RTRIM(LTRIM(inv2.WebLongDescription))             AS "body_html",
    CAST(ISNULL(inv.Mass, 0) AS INTEGER)              AS "variants.weight",
    RTRIM(LTRIM(inv.DrawOfficeNum))                   AS "variants.barcode",
	'Size'                                            AS "variants.option1_name",
	 RTRIM(LTRIM(inv2.WebSizeOption))                 AS "variants.option1_value",
    CASE
       WHEN LOWER(RTRIM(ISNULL(inv2.WebActive, ''))) = 'active' THEN 'true'
        ELSE 'false'
    END                                               AS "product_active",
    'true'                                            AS "variants.inventory_management",
    CONCAT(
        RTRIM(LTRIM(ISNULL(fprod.Description, ''))), ',',
        RTRIM(LTRIM(ISNULL(ffab.Description, ''))), ',',
        RTRIM(LTRIM(ISNULL(fbc.Description, ''))), ',',
        RTRIM(LTRIM(ISNULL(fgen.Description, ''))), ',',
        RTRIM(LTRIM(ISNULL(fag.Description, ''))), ',',
        RTRIM(LTRIM(ISNULL(char1.Description, ''))), ',',
        RTRIM(LTRIM(ISNULL(fpc.Description, ''))), ',',
        RTRIM(LTRIM(ISNULL(pcl.Description, '')))
    )
                                                      AS "tags",

    -- Prices
    'A'                                               AS "default_price_tier",
    STUFF((
      SELECT
        ',' + CONVERT(VARCHAR, LTRIM(RTRIM(price_tiers.PriceCode))) + '|' +
              CONVERT(VARCHAR, CAST(ISNULL(price_tiers.SellingPrice, 0) AS FLOAT))
      FROM InvPrice price_tiers WITH (NOLOCK)
      WHERE price_tiers.StockCode = inv.StockCode
      ORDER BY LTRIM(RTRIM(price_tiers.PriceCode))
    for xml path('')),1,1,'')
                                                      AS "csv_price_tiers",

    'K_KTJB'                                          AS "default_qty_availability",


	CONCAT(
        STUFF((
          SELECT
            ',K_' + CONVERT(VARCHAR, LTRIM(RTRIM(whse.Warehouse))) + '|' +
                  CONVERT(VARCHAR, CONVERT(INTEGER, COALESCE(whse.QtyOnHand, 0) - COALESCE(whse.QtyAllocated, 0)))
          FROM InvWarehouse whse WITH (NOLOCK)
          WHERE whse.StockCode = inv.StockCode
          ORDER BY LTRIM(RTRIM(whse.Warehouse))
        for xml path('')),1,1,''),
        ',',
        STUFF((
          SELECT
            ',T_' + CONVERT(VARCHAR, LTRIM(RTRIM(whse.Warehouse))) + '|' +
                  CONVERT(VARCHAR, CONVERT(INTEGER, COALESCE(whse.QtyOnHand, 0) - COALESCE(whse.QtyAllocated, 0)))
          FROM [SysproCompanyT].[dbo].InvWarehouse whse WITH (NOLOCK)
          WHERE whse.StockCode = inv.StockCode
          ORDER BY LTRIM(RTRIM(whse.Warehouse))
        for xml path('')),1,1,''),
        ',',
        'K_KTMP|', ISNULL(inv2.StockOnHand, '0')
	)

                                                      AS "csv_qty_availability",
    RTRIM(LTRIM(inv2.StockImageUrl))                  AS "meta_image1",

    RTRIM(LTRIM(fgen.Description))                    AS "meta_gender",
    RTRIM(LTRIM(ffab.Description))                    AS "meta_fabrication",
    RTRIM(LTRIM(fpc.Description))                     AS "meta_product_category",
    RTRIM(LTRIM(fbc.Description))                     AS "meta_base",
    RTRIM(LTRIM(fprod.Description))                   AS "meta_lin"


FROM InvMaster inv WITH (NOLOCK)

    -- Base Data
    LEFT JOIN [InvMaster+] inv2 WITH (nolock)
    ON inv.StockCode = inv2.StockCode

    LEFT JOIN SalProductClassDes pcl WITH (nolock)
    ON inv.ProductClass = pcl.ProductClass

    LEFT JOIN [vwAdmFormValProduct] fprod WITH (nolock)
    ON inv2.Product = fprod.Item

    LEFT JOIN [vwAdmFormValFabrication] ffab WITH (nolock)
    ON inv2.Fabrication = ffab.Item

    LEFT JOIN [vwAdmFormValBaseColour] fbc WITH (nolock)
    ON inv2.BaseColour = fbc.Item

    LEFT JOIN [vwAdmFormValGender] fgen WITH (nolock)
    ON inv2.Gender = fgen.Item

    LEFT JOIN [vwAdmFormVAlProductCategory] fpc WITH (nolock)
    ON inv2.ProductCategory = fpc.Item

    LEFT JOIN [vwAdmFormVAlAgeGroup] fag WITH (nolock)
    ON inv2.AgeGroup = fag.Item

    LEFT JOIN [vwAdmFormVAlCharacter] char1 WITH (nolock)
    ON inv2.Character1 = char1.Item

    WHERE

    RTRIM(LTRIM(inv.StockCode)) = '%(sku)s';