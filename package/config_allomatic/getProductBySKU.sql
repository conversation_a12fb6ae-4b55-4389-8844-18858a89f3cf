SELECT
  s.STOCKCODE           AS "source_product_code",
  s.ID                  AS "source_variant_code",
  CASE s.EXTRADTLS2
    WHEN '1' THEN 'true'
    ELSE 'false'
  END                   AS "product_active",
  s.DESCRIPTION         AS "title",
  s.EXTRADTLS4          AS "collection",
  s.EXTRADTLS4          AS "product_type",
  s.EXTRADTLS3          AS "vendor",
  ''                    AS "body_html",
  s.EXTRADTLS5          AS "tags",
  s.STOCKCODE           AS "variants.sku",
  s.WEIGHT              AS "variants.weight",
  s.BARCODE             AS "variants.barcode",
  'true'                AS "variants.inventory_management",
  'SELLINGPRICE1'       AS "default_price_tier",
  ROUND(s.SELLINGPRICE1*1.15,2)
                        AS "price_tier_SELLINGPRICE1",
  s.SELLINGPRICE2       AS "price_tier_SELLINGPRICE2",
  s.SELLINGPRICE3       AS "price_tier_SELLINGPRICE3",
  (
   SELECT OQUANTITY
   FROM calc_stockwarehouse_levels(s.STOCKCODE,1, NULL)
  )                     AS "variants.qty",
  s.EXTRADTLS1          AS "meta_exde_1",-- Not in use
  s.EXTRADTLS6          AS "meta_part",-- Part
  s.EXTRADTLS7          AS "meta_extrad7",
  s.EXTRADTLS8          AS "meta_extrad8",

  s.ID,
  s.STOCKCODE,
  s.PACK,
  s.SELLINGPRICE1MARKUP,
  s.SELLINGPRICE2MARKUP,
  s.SELLINGPRICE3MARKUP,
  s.DISCOUNT1,
  s.DISCOUNT2,
  s.DISCOUNT3,
  s.DISCOUNT4,
  s.DISCOUNT5,
  s.EXTRADTLS1,
  s.EXTRADTLS2,
  s.EXTRADTLS3,
  s.EXTRADTLS4,
  s.EXTRADTLS5,
  s.EXTRADTLS6,
  s.EXTRADTLS7,
  s.EXTRADTLS8
FROM STK_STOCKITEM s
  LEFT JOIN STK_STOCKCATEGORY sc ON sc.STCKCTGRYCDE = s.CATEGORY
  LEFT JOIN STK_STOCKSUBCATEGORY ss ON ss.CODE = s.SUBCATEGORY
  LEFT JOIN STK_PRODUCTGROUPS sg ON sg.PRDCTGRPCDE = s.PRODUCTGROUP
WHERE s.STOCKCODE = '%(sku)s'
;