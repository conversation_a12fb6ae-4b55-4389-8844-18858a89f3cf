select
    convert(varchar, ar.Customer)            as 'source_customer_code',

    convert(varchar, ar.Customer)            AS 'first_name',
    ar.Name                                  AS 'last_name',
    ar.Email                                 AS 'email',
    'true'                                   AS 'customer_active',
    1                                        AS 'accepts_marketing',
    ar.ShipToAddr1                           AS 'address.address2',
    ar.ShipToAddr2 +
    CASE LTRIM(RTRIM(ar.ShipToAddr3Loc))
    WHEN '' THEN ''
    ELSE ', ' + ar.ShipToAddr3Loc
    END                                      AS 'address.address1',
    ar.ShipToAddr3                           AS 'address.city',
    ar.ShipToAddr5                           AS 'address.country',
    ''                                       AS 'address.country_code',
    ar.ShipToAddr4                           AS 'address.province',
    ''                                       AS 'address.province_code',
    ar.ShipPostalCode                        AS 'address.zip',
    ar.Name                                  AS 'address.company',
    ar.Telephone                             AS 'address.phone',
    ar.BuyingGroup1                          AS 'meta_buying_group1',
    ar.ShippingInstrs                        AS 'meta_shipping_instructions',
    ar.Salesperson                           AS 'meta_sales_person',
    sp.Name                                  AS 'meta_sales_person_name',
    ar.CreditLimit                           AS 'meta_credit_limit',
    ar.Branch                                AS 'meta_branch',
    ''                                       AS 'meta_terms',
    COALESCE(di.DiscountPct1, 0)             AS 'meta_discount',
    ar.Area                                  AS 'meta_area',
    'R' + convert(VARCHAR,convert(MONEY, arb.CurrentBalance1), 1)
                                             AS 'meta_balance',
    'R' + convert(VARCHAR,convert(MONEY, arb.Val120daysInv), 1)
                                             AS 'meta_balance_120',
    'R' + convert(VARCHAR,convert(MONEY, arb.Val90daysInv), 1)
                                             AS 'meta_balance_90',
    'R' + convert(VARCHAR,convert(MONEY, arb.Val60daysInv), 1)
                                             AS 'meta_balance_60',
    'R' + convert(VARCHAR,convert(MONEY, arb.Val30daysInv), 1)
                                             AS 'meta_balance_30',
    'R' + convert(VARCHAR,convert(MONEY, arb.ValCurrentInv), 1)
                                             AS 'meta_balance_current',
    'R' + convert(VARCHAR,convert(MONEY, ar.CreditLimit - arb.CurrentBalance1), 1)
                                             AS 'meta_credit_available',
	ar.PriceCategoryTable                    AS 'meta_price_category_table',

    ar.PriceCode                             AS 'price_tier',
    ar.SalesWarehouse                        AS 'qty_availability',

    -- CONTRACT PRICING:
    -- A contract consists of 6 parts, these are:-
    --    Order:    The hierarchy (order) in which to load the contracts. The first successful contract will be used
    --    Entity:   The entity the contract will apply to. For example, a customer would have a contract with entity "product" to apply to the product.
    --    Key:      The corresponding entities key to match on. In the above example, this would be "category"
    --    Type:     Discount or fixed. Is this contract a fixed price or discount from some other price.
    --    Value:    The value of the contract, in the case of type discount, this would be a percentage expressed as an integer. In the case of a fixed price, it would be the price,
    -- e.g. order_0|entity_product|key_product_code|value_123|type_discount~10
    -- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    -- Available ContractTypes:
    --   B = Buying Group
    --   C = Customer
    -- Available PriceMethods:
    --   C = Coded Selling Price
    --   D = List less chain discount
    --   F = Fixed price
    --   K = Markup list price by pct
    --   L = List less percentage
    --   M = Mark-up cost by percentage
    --   P = Price code less chain
    --   Q = Quantity discount breaks
    --   U = Coded less unit discount

    -- Contract: Customer contract percentage
    -- Note: discount can apply to either the default price tier or channel price tier
    -- Requirements:
    --   Product.meta_stock_code
    STUFF((
      SELECT
      ',order_0|entity_product|key_stock_code|value_' + CONVERT(VARCHAR, LTRIM(RTRIM(sor.StockCode))) + '|type_discount~' +
      CASE WHEN (DATEADD(day, DATEDIFF(day,0,GETDATE()),0) <= ExpiryDate or ExpiryDate is NULL) AND DATEADD(day, DATEDIFF(day,0,GETDATE()),0) >= StartDate
        THEN CONVERT(VARCHAR, LTRIM(RTRIM(sor.FixedPriceCode))) + '-' + CONVERT(VARCHAR, COALESCE(sor.Discount1, 0))
        ELSE '0'
      END
      FROM SorContractPrice sor WITH (NOLOCK)
      INNER JOIN InvMaster inv WITH (NOLOCK) ON sor.StockCode = inv.StockCode
      WHERE sor.CustomerBuyGrp = ar.Customer
      AND sor.ContractType='C'
      AND sor.PriceMethod in ('P', 'C')
      -- AND inv.web_active IN ('active', 'inactive')
      ORDER BY LTRIM(RTRIM(sor.StockCode))
    for xml path('')),1,1,'')
                                             AS "csv_contract_product_code",

    -- Contract: Customer contract fixed
    -- Requirements:
    --   Product.meta_stock_code
    STUFF((
      SELECT
      ',order_0|entity_product|key_stock_code|value_' + CONVERT(VARCHAR, LTRIM(RTRIM(sor.StockCode))) + '|type_fixed~' +
      CASE WHEN (DATEADD(day, DATEDIFF(day,0,GETDATE()),0) <= ExpiryDate or ExpiryDate is NULL) AND DATEADD(day, DATEDIFF(day,0,GETDATE()),0) >= StartDate
        THEN CONVERT(VARCHAR, COALESCE(sor.FixedPrice, 0))
        ELSE '0'
      END
      FROM SorContractPrice sor WITH (NOLOCK)
      INNER JOIN InvMaster inv WITH (NOLOCK) ON sor.StockCode = inv.StockCode
      WHERE sor.CustomerBuyGrp = ar.Customer
      AND sor.ContractType='C'
      AND sor.PriceMethod = 'F'
      -- AND inv.web_active IN ('active', 'inactive')
      ORDER BY LTRIM(RTRIM(sor.StockCode))
    for xml path('')),1,1,'')
                                             AS "csv_contract_product_code_fixed"

    -- LEGACY CONTRACT PRICING (kept here for referencing purposes only
--    STUFF((
--      SELECT
--      ',' + CONVERT(VARCHAR, LTRIM(RTRIM(sor.StockCode))) + '|' +
--      CASE WHEN (DATEADD(day, DATEDIFF(day,0,GETDATE()),0) <= ExpiryDate or ExpiryDate is NULL) AND DATEADD(day, DATEDIFF(day,0,GETDATE()),0) >= StartDate
--        THEN CONVERT(VARCHAR, LTRIM(RTRIM(sor.FixedPriceCode))) + '-' + CONVERT(VARCHAR, COALESCE(sor.Discount1, 0))
--        ELSE '0-0'
--      END
--      FROM SorContractPrice sor WITH (NOLOCK)
--      WHERE sor.CustomerBuyGrp = ar.Customer
--      AND sor.ContractType='C'
--      AND sor.PriceMethod in ('P', 'C')
--      ORDER BY LTRIM(RTRIM(sor.StockCode))
--    for xml path('')),1,1,'')
--                                             as "csv_product_code",

    -- ContractType:
    --   B = Buying Group
    --   C = Customer
    -- PriceMethod:
    --   C = Coded Selling Price
    --   D = List less chain discount
    --   F = Fixed price
    --   K = Markup list price by pct
    --   L = List less percentage
    --   M = Mark-up cost by percentage
    --   P = Price code less chain
    --   Q = Quantity discount breaks
    --   U = Coded less unit discount
--    STUFF((
--      SELECT
--      ',' + CONVERT(VARCHAR, LTRIM(RTRIM(sor.StockCode))) + '|' +
--
--      CASE WHEN (DATEADD(day, DATEDIFF(day,0,GETDATE()),0) <= ExpiryDate or ExpiryDate is NULL) AND DATEADD(day, DATEDIFF(day,0,GETDATE()),0) >= StartDate
--        THEN CONVERT(VARCHAR, COALESCE(sor.FixedPrice, 0))
--        ELSE '0'
--      END
--      FROM SorContractPrice sor WITH (NOLOCK)
--      WHERE sor.CustomerBuyGrp = ar.Customer
--      AND sor.ContractType='C'
--      AND sor.PriceMethod = 'F'
--      ORDER BY LTRIM(RTRIM(sor.StockCode))
--    for xml path('')),1,1,'')
--                                             as "csv_product_code_fixed"

FROM ArCustomer ar WITH (nolock)
 INNER JOIN ArCustomerBal arb WITH (nolock)
 ON arb.Customer = ar.Customer
-- LEFT JOIN [ArCustomer+] ar2 WITH (nolock)
-- ON ar2.Customer = ar.Customer
 LEFT JOIN SalSalesperson sp
 ON sp.Salesperson = ar.Salesperson and ar.Branch = sp.Branch
 LEFT JOIN [TblSoDiscount] di WITH (nolock)
 ON di.DiscountCode = ar.LineDiscCode

WHERE ISNULL(ar.Email, '') <> ''
AND convert(varchar, ar.Customer) = '%(source_customer_code)s'
;