import json
import imp
from datetime import datetime
import requests
from .controllers import base_push as base
import traceback

# urlparse was renamed between Python 2.7 and the expected Python 3.3.5
try:  # Python 3
    from urllib.request import urlopen
except ImportError:  # Python 2
    from urllib import urlopen


class Api(base.Api):
    _configDir = "config_testdustin"

    def reload(self):
        imp.reload(base)
        base.Api.reload(self)
        response = {
            "status": True,
            "data": {
                "reload": True
            }
        }
        return json.dumps(response, indent=self._indent)

    def getProductsBatch(self):
        response = super(Api, self).getProductsBatch()
        try:
            params = self._getParams()
            if "current_iteration" not in params:
                params["current_iteration"] = "1"

            # Only reset orders on the first iteration:
            if params["current_iteration"] == "1":

                # Check and get meta for resetFailedOrders
                resetFailedOrders = self._getMeta("resetFailedOrders")
                if resetFailedOrders["status"]:
                    resetFailedOrders = datetime.strptime(resetFailedOrders["data"]["value"], "%Y-%m-%d %H:%M:%S")

                    # If the resetFailedOrders is less than an hour ago then return
                    if (datetime.now() - resetFailedOrders).seconds < 3600:
                        return response

                else:
                    self._setMeta("resetFailedOrders", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))

                # Try to reset orders
                self.resetFailedOrders()
        except Exception as e:
            full_error = traceback.format_exc()
            body = {
                "message": "Error resetting orders",
                "level": "error",
                "method": "GET",
                "log_to_es": True,
                "response": str(e),
                "traceback": full_error,
            }
            self.writeToLog(body)

        return response

    def resetFailedOrders(self):

        # Add count variables
        expired_failed_count = 0
        failed_orders_count = 0

        # Set a meta to show the last time this was run
        self._setMeta("resetFailedOrders", datetime.now().strftime("%Y-%m-%d %H:%M:%S"), True)

        # Get list of failed queue items https://app.stock2shop.com/v1/queue/items?mode=non-blocking&instructions
        # =add_order&status=failed&token={{token}}

        # https://app.stock2shop.com/v1/queue/items?source_id=1037&id=xxxxx&mode=non-blocking&search_mode=orders
        # &instructions=add_order&status=failed&token=xxxxx

        token = self._config["push"]["token"]
        url = "https://app.stock2shop.com/v1/queue/items?mode=non-blocking&instructions=add_order&status=failed&token" \
              "={}".format(token)

        # Make the request
        response = requests.get(url)
        if response.status_code != 200:
            # Add logging to connector logs when error
            body = {
                "message": "Error getting queue items",
                "level": "error",
                "log_to_es": True,
                "response": json.loads(response.text).get("errors", None),
            }
            self.writeToLog(body)
            return

        queueItems = response.json()

        # Check if there are any queue items
        if len(queueItems.get("system_items", [])) > 0:

            # Loop through the queue items
            for queueItem in queueItems["system_items"]:

                # Increase the failed_orders_count
                failed_orders_count += 1
                # Get the queueItemId and created
                queueItemId = queueItem["id"]
                created = queueItem["created"]

                # Calculate the age of the queue item
                age = datetime.utcnow() - datetime.strptime(created, "%Y-%m-%d %H:%M:%S")

                # Get order payload by fetching queue item with ID
                # Without queue ID specified payload is not included
                url = "https://app.stock2shop.com/v1/queue/items?token={}&id={}".format(token, queueItemId)

                # Make the request
                response = requests.get(url)
                if response.status_code != 200:
                    # Add logging to connector logs when error
                    body = {
                        "message": "Error getting queue item",
                        "level": "error",
                        "log_to_es": True,
                        "response": json.loads(response.text).get("errors", None),
                    }
                    self.writeToLog(body)
                    return
                details = response.json()

                # Store payload variables
                order_id = details["system_items"][0].get("payload", {}).get("order_id", None)
                channel_order_code = details["system_items"][0].get("payload", {}).get("order", {}).get(
                    "system_order", {}).get("channel_order_code", None)
                history_lines = details["system_items"][0].get("payload", {}).get("order", {}).get(
                    "system_order", {}).get("history", None)
                storage_code = history_lines[-1].get("storage_code", None)

                # If the created is older than 48 hours ignore it If the created is newer than 48 hours,
                # call https://app.stock2shop.com/v1/queue/reset/{{queueItemId}}?token={{token}}
                if age.days < 2:
                    url = "https://app.stock2shop.com/v1/queue/reset/{}?token={}".format(queueItemId, token)
                    response = requests.put(url, headers={"Accept": "application/json"})
                    if response.status_code != 200:

                        # Write the error to connector logs using the following self.writeToLog
                        body = {
                            "message": "Error resetting order",
                            "level": "error",
                            "order_id": order_id,
                            "queue_item_id": queueItemId,
                            "queue_item_error": queueItem.get("log_message", None),
                            "storage_code": storage_code,
                            "log_to_es": True,
                            "channel_order_code": channel_order_code,
                            "reset_error": json.loads(response.text).get("errors", None),
                        }
                        self.writeToLog(body)
                    else:
                        body = {
                            "message": "Order reset successfully",
                            "level": "info",
                            "order_id": order_id,
                            "queue_item_id": queueItemId,
                            "storage_code": storage_code,
                            "queue_item_error": queueItem.get("log_message", None),
                            "log_to_es": True,
                            "channel_order_code": channel_order_code,
                        }
                        self.writeToLog(body)
                else:
                    expired_failed_count += 1

        # Write count variables to log
        body = {
            "message": "Completed check for failed orders",
            "level": "info",
            "log_to_es": True,
            "expired_failed_count": expired_failed_count,
            "failed_orders_count": failed_orders_count
        }
        self.writeToLog(body)

    def writeToLog(self, body):
        urlopen(self._config["push"]["writeLog"], json.dumps(body).encode('utf-8'))

    # Add _transformProduct here to override default
