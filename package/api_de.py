from .controllers import base_push as base
from .shared import sap_one
from .shared import utils
import json
from builtins import open, float, int, str, len, round, Exception, getattr
import time
import imp
import math
import collections
from datetime import datetime

# urlparse was renamed between Python 2.7 and the expected Python 3.3.5
try:  # Python 3
    from urllib.request import urlopen
except ImportError:  # Python 2
    from urllib import urlopen



class Api(base.Api):
    _configDir = "config_de"

    def reload(self):
        imp.reload(base)
        base.Api.reload(self)
        response = {
            "status": True,
            "data": {
                "reload": True
            }
        }
        return json.dumps(response, indent=self._indent)

    @staticmethod
    def _parseCustomerName(row):
        company = row["card_name"]
        first_name = row["first_name"]
        last_name = row["last_name"]
        return company, first_name, last_name

    def _getEMPID(self, email):
        sql = "Select empID from OHEM where email = '%(email)s'"
        sql = utils.bindSqlParams(sql, {
            "email": str(email)
        })
        params = {
            "sql": sql,
            "limit": 100
        }
        with sap_one.openConn(self._config["dsn"]) as cursor:
            return utils.runODBC(params, cursor)

    def _beforeCreateOrder(self, payload, params):

        # Since we use card code plus contact id {card_code}-{contact_id} as source customer code,
        # We need to strip out the contact id and only use card code
        source = payload["sources"][0]
        payload["sources"][0]["original_source_customer_code"] = source["source_customer_code"]
        codes = source["source_customer_code"].split("-")
        if codes[1]:
            source["source_customer_code"] = codes[0]
        else:
            raise Exception("Invalid source_customer_code {}".format(source["source_customer_code"]))

        # Set contact person
        params["contact_person_code"] = codes[1]

        # Set owner
        if "console_user_email" in params and params["console_user_email"] != "":
            response = self._getEMPID(params["console_user_email"])
            if len(response["data"]["rows"]) > 0:
                if int(response["data"]["rows"][0]["empid"]) > 0:
                    params["documents_owner"] = response["data"]["rows"][0]["empid"]

        # Set Route code
        if "route_code" in params:
            params["user_field_order_U_RouteC"] = params["route_code"]
            if "delivery_method" in params:
                if params["delivery_method"] == "0":
                    params["user_field_order_U_PrintType"] = 2
                else:
                    params["user_field_order_U_PrintType"] = 1

        # Set transaction reference if there is one (Adumo payments only)
        transaction = self._getLatestTransaction(payload["system_order"])
        if transaction is not None:
            transaction_id = transaction.get("payment_code", None)
            payment_gateway = transaction.get("gateway", None)
            if transaction_id and payment_gateway == "AdumoVirtual":
                params["user_field_order_U_AdumoRef"] = transaction_id

        return payload, params

    def writeToLog(self, body):
        urlopen(self._config["push"]["writeLog"], json.dumps(body).encode('utf-8'))

    def createOrder(self):
        t1 = time.time()
        self._setConfig()

        params = self._getParams()
        response = utils.checkRequiredParams(
            ["warehouse_code"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        try:
            payload = json.loads(self._payload())
        except ValueError:
            response = {
                "status": False,
                "description": "Request body is not valid json",
                "line": utils.lineNo()
            }
            return json.dumps(response, indent=self._indent)

        # hook for custom transforms
        payload, params = self._beforeCreateOrder(payload, params)

        order = payload["system_order"]
        source = payload["sources"][0]

        if "source_customer_code" in source and source["source_customer_code"]:
            source_customer_code = source["source_customer_code"]
        else:
            if "default_customer_code" in params:
                source_customer_code = params["default_customer_code"]

        if source_customer_code is None:
            raise Exception("source_customer_code missing and no default_customer_code set")

        # If contacts are included in the source customer code,
        # we need to strip out the contact id and only use card code.
        # The format of source_customer_code is:
        # {card_code}-{contact_id}
        if "contact_source_customer" in params:
            if params["contact_source_customer"] == "true":
                source["original_source_customer_code"] = source["source_customer_code"]
                codes = source["source_customer_code"].rsplit('-', 1)
                if codes[1]:
                    source_customer_code = codes[0]
                else:
                    raise Exception("Invalid source_customer_code {}".format(source["source_customer_code"]))

        # Parse document type if it is given in the params, otherwise create invoices by default
        document_type = "oInvoices"
        if "order_document_type" in params:
            document_type = params["order_document_type"]
            if document_type not in sap_one.BO_OBJECT_TYPES:
                response = {
                    "status": False,
                    "description": "Invalid document type '{}' given".format(document_type),
                    "line": utils.lineNo()
                }
                return json.dumps(response, indent=self._indent)

        # Get company instance
        oCompany = sap_one.getCompany(self._config)

        # Try to connect
        response = sap_one.connect(oCompany)
        if not response["status"]:
            return json.dumps(response, indent=self._indent)

        object_type = sap_one.BO_OBJECT_TYPES[document_type]["code"]
        db_server_type = sap_one.DB_SERVER_TYPES[self._config["dbServerType"]]["code"]

        # Create order object
        oOrder = oCompany.GetBusinessObject(object_type)
        oOrder.CardCode = source_customer_code
        oOrder.DocDueDate = sap_one.getVariantDate(order["created"])
        notes = ""
        if "notes" in order:
            notes = order["notes"]
        oOrder.Comments = notes

        if "doc_due_date" in params:
            oOrder.DocDueDate = sap_one.getVariantDate(params["doc_due_date"])

        if "card_name" in params:
            oOrder.CardName = params["card_name"]

        customer_reference = ""
        if "customer_reference" in params:
            customer_reference = params["customer_reference"]
        oOrder.NumAtCard = customer_reference

        if "contact_person_code" in params:
            oOrder.ContactPersonCode = params["contact_person_code"]

        if "documents_owner" in params:
            oOrder.DocumentsOwner = params["documents_owner"]

        if "project" in params:
            oOrder.Project = params["project"]

        if "reserve_invoice" in params:
            if params["reserve_invoice"] == "true" and document_type == "oInvoices":
                oOrder.ReserveInvoice = sap_one.BO_YES_NO_ENUM["tYES"]["code"]

        if "transportation_code" in params:
            oOrder.TransportationCode = params["transportation_code"]

        if "address" in params:
            oOrder.Address = params["address"]

        if "address2" in params:
            oOrder.Address2 = params["address2"]

        if "set_branch_code" in params:
            if params["set_branch_code"] == "true":
                branch_code = self._getBranchCode(
                    params["warehouse_code"])
                oOrder.BPL_IDAssignedToInvoice = branch_code

        if "currency" in params:
            oOrder.DocCurrency = params["currency"]

        # Set user defined fields which exists in the ORDR table (Header)
        for field in params:
            if "user_field_order_" in field:
                user_field = field.replace("user_field_order_", "")
                # TODO should this be?
                # oOrder.UserFields.Item(user_field).Value = params[field]
                oOrder.UserFields.Fields.Item(user_field).Value = params[field]

        # Try to set numbering series
        Series, NextNumber  = self._getNumberingSeriesByName(
            params["warehouse_code"],
            object_type,
            db_server_type)
        if Series is not None and NextNumber is not None:
            oOrder.Series = Series
            # To use the HandWritten property to manually set the next number:
            # oOrder.HandWritten = sap_one.BO_YES_NO_ENUM["tYES"]["code"]
            # oOrder.DocNum = NextNumber
        else:
            raise Exception(
                "Numbering series not found for warehouse {}".format(
                    params["warehouse_code"]))

        # Add line items to order
        exclusiveTotalCents = 0
        for line_item in order["line_items"]:
            # Check if line item exists,
            # it maybe have been deleted from SAP
            # and not synced to stock2shop
            ItemCode = self._getItemStatus(line_item["sku"])
            if ItemCode is None:
                response = {
                    "status": False,
                    "description": "ItemCode {} does not exist".format(
                        line_item["sku"]),
                    "line": utils.lineNo()
                }
                return json.dumps(response, indent=self._indent)

            oOrder.Lines.ItemCode = line_item["sku"]
            oOrder.Lines.Quantity = line_item["qty"]

            # warehouse code set on line items
            if "use_product_warehouse_code" in params and params["use_product_warehouse_code"] == "true":
                oOrder.Lines.WarehouseCode = self._getProductDefaultWarehouseCode(line_item["sku"])
            else:
                oOrder.Lines.WarehouseCode = params["warehouse_code"]

            # unit price or price
            if "use_unit_price" in params and params["use_unit_price"] == "true":
                oOrder.Lines.UnitPrice = line_item["price"]
            else:
                oOrder.Lines.Price = line_item["price"]

            if "line_item_project_code" in params:
                oOrder.Lines.ProjectCode = params["line_item_project_code"]

            if "line_item_costing_code" in params:
                oOrder.Lines.CostingCode = params["line_item_costing_code"]

            # Set user defined fields which exists in the RDR1 table (Order rows / line items)
            for field in params:
                if "user_field_line_" in field:
                    user_field = field.replace("user_field_line_", "")
                    oOrder.Lines.UserFields.Fields.Item(user_field).Value = params[field]

            # Set account codes if we have them for the client
            sales_account = self._getSalesGLAccount(line_item["sku"], source_customer_code)
            cogs_account = self._getCOGSGLAccount(line_item["sku"], source_customer_code)
            if sales_account is not None:
                oOrder.Lines.AccountCode = sales_account

            if cogs_account is not None:
                oOrder.Lines.COGSAccountCode = cogs_account

            exclusiveTotalCents += round(int(line_item["qty"]) * (line_item["price"] * 100))

            tax_line = line_item["tax_lines"][0]
            tax_code = self._getTaxCode(
                params, tax_line["code"]
            )
            if tax_code:
                oOrder.Lines.TaxCode = tax_code

            try:
                oOrder.Lines.Add()
            except Exception as e:
                response = {
                    "status": False,
                    "description": str(e),
                    "line": utils.lineNo()
                }
                return json.dumps(response, indent=self._indent)

        add_shipping = True
        if "ignore_shipping" in params:
            if params["ignore_shipping"] == "true":
                add_shipping = False
        if "shipping_lines" in order and "shipping_code" in params and add_shipping:
            for shipping_line in order["shipping_lines"]:
                oOrder.Lines.ItemCode = params["shipping_code"]
                oOrder.Lines.Quantity = 1
                oOrder.Lines.UnitPrice = shipping_line["price"]
                if "shipping_project_code" in params:
                    oOrder.Lines.ProjectCode = params["shipping_project_code"]

                if "shipping_costing_code" in params:
                    oOrder.Lines.CostingCode = params["shipping_costing_code"]

                exclusiveTotalCents += round(shipping_line["price"] * 100)

                try:
                    oOrder.Lines.Add()
                except Exception as e:
                    response = {
                        "status": False,
                        "description": str(e),
                        "line": utils.lineNo()
                    }
                    return json.dumps(response, indent=self._indent)

        # By default the discount is applied as a percentage on the order
        if "total_discount" in order:
            # Prevent division by zero
            if exclusiveTotalCents > 0:
                totalDiscountCents = round(float(order["total_discount"]) * 100)
                oOrder.DiscountPercent = (totalDiscountCents / exclusiveTotalCents) * 100

        # Hook for transforming order just before order is added
        self._duringCreateOrder(payload, oOrder, params)

        try:
            retCode = oOrder.Add()
        except Exception as e:
            response = {
                "status": False,
                "description": str(e),
                "line": utils.lineNo()
            }
            return json.dumps(response, indent=self._indent)

        if retCode != 0:
            code, message = oCompany.GetLastError(retCode)
            code = int(math.fabs(int(code)))
            if code == 5009:
                # Default message for this code is too cryptic
                message = "SKU not found"
            response = {
                "status": False,
                "description": "{} - {}".format(code, message),
                "line": utils.lineNo()
            }
            return json.dumps(response, indent=self._indent)

        else:
            # GetNewObjectCode returns DocEntry,
            # to get DocNum query the ordr table
            DocEntry = oCompany.GetNewObjectCode()
            with sap_one.openConn(self._config["dsn"]) as cursor:
                # Sales order
                if object_type == sap_one.BO_OBJECT_TYPES["oOrders"]["code"]:
                    sql = '''select ordr."DocNum" as docnum from ordr
                    where ordr."DocEntry" = '%(DocEntry)n' '''

                # Invoice
                elif object_type == sap_one.BO_OBJECT_TYPES["oInvoices"]["code"]:
                    sql = '''select oinv."DocNum" as docnum from oinv
                    where oinv."DocEntry" = '%(DocEntry)n' '''

                # Quotation
                elif object_type == sap_one.BO_OBJECT_TYPES["oQuotations"]["code"]:
                    sql = '''select oqut."DocNum" as docnum from oqut
                    where oqut."DocEntry" = '%(DocEntry)n' '''

                else:
                    response = {
                        "status": False,
                        "description": "object_type {} not implemented".format(
                            object_type),
                        "line": utils.lineNo()
                    }
                    return json.dumps(response, indent=self._indent)

                sql = utils.bindSqlParams(sql, {"DocEntry": int(DocEntry)})
                cursor.execute(sql)
                rows = utils.getRowsODBC(cursor, 1)

                # Logging for issue #244
                body = {
                    "message": "Checking Order",
                    "level": "info",
                    "log_to_es": True,
                    "rows": rows
                }
                self.writeToLog(body)
                if len(rows) == 0:
                    response = {
                        "status": False,
                        "description":
                            "DocNum for DocEntry {} not found".format(DocEntry),
                        "line": utils.lineNo()
                    }
                    return json.dumps(response, indent=self._indent)

                source_order_code = rows[0]["docnum"] # Must be lowercase

        # Remember to disconnect when done
        if oCompany.Connected:
            oCompany.Disconnect()

        # hook for after order
        source_order_code, source_customer_code = self._afterCreateOrder(payload, source_order_code, source_customer_code)

        # if contact used in source customer code, return the original source customer code, i.e.
        # {card_code}-{contact_id}
        if "contact_source_customer" in params:
            if params["contact_source_customer"] == "true":
                source_customer_code = source["original_source_customer_code"]

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {
                "source_order_code": source_order_code,
                "source_customer_code": source_customer_code
            }
        }

        # Logging for issue #244
        t3 = time.time()
        body = {
            "message": "Checking Order data",
            "level": "info",
            "log_to_es": True,
            "response": response,
            "time to process": str(t3 - t1)
        }
        self.writeToLog(body)
        return json.dumps(response, indent=self._indent)

    def _afterCreateOrder(self, payload, source_order_code, source_customer_code):
        # TODO this should use the "contact_source_customer" feature
        return source_order_code, payload["sources"][0]["original_source_customer_code"]

    @staticmethod
    def getProductMetaByKey(source_product: dict, key: str):
        """
        returns the specified meta that has the 
        specificed `key` otherwise returns an empty string
        """

        for value in source_product["product"]["meta"]:
            if value["key"] == key:
                return value["value"]
        return ""

    @staticmethod
    def createHTMLProductList(metaValue: str):
        """
        returns an array of recommended products, enclosed in anchor tags, that were comma seperated
        otherwise returns an empty array if the meta value is an empty string (`""`)
        """

        products = ""
        for product in metaValue.split(","):
            if product != "" and product != "-":
                products += "<li><a href='/#/products?q=" + product + "'>" + product + "</a></li>"
        return products
    
    @staticmethod
    def addBodyHtml(source_product: dict):
        """
        Adds the body html to a source product by using the 
        `meta_recommended` and `meta_bom` products
        """

        rec_products_meta = Api.getProductMetaByKey(source_product, "recommended")
        rec_products = Api.createHTMLProductList(rec_products_meta)

        bom_products_meta = Api.getProductMetaByKey(source_product, "bom")
        bom_products = Api.createHTMLProductList(bom_products_meta)

        bodyHTML = ""
        if rec_products != "":
            bodyHTML += "<p>Recommended Products:</p><ul>" + rec_products + "</ul>"

        if bom_products != "":
            bodyHTML += "<p>BOM Products:</p><ul>" + bom_products + "</ul>"

        return bodyHTML

    @staticmethod
    def _afterProductTransform(source_product):

        # Adjusts the body html using the recommended 
        # and bom products if set
        source_product["product"]["body_html"] = Api.addBodyHtml(source_product)

        # add in visual vailability (for display)
        # W_01 + W_02 = "VER: x",W_04 = "BFN: x", W_05 = "SPR: x", W_03 = "JHB: x"

        meta = collections.OrderedDict()
        meta["key"] = "availability"

        warehouses = collections.OrderedDict()
        warehouses["VER"] = 0
        warehouses["BFN"] = 0
        warehouses["SPR"] = 0
        warehouses["JHB"] = 0
        for availability in source_product["product"]["variants"]["qty_availability"]:

            qty = int(availability["qty"])

            if "W_01" in availability["description"] or "W_02" in availability["description"]:
                warehouses["VER"] += qty

            if "W_03" in availability["description"]:
                warehouses["JHB"] += qty

            if "W_04" in availability["description"]:
                warehouses["BFN"] += qty

            if "W_05" in availability["description"]:
                warehouses["SPR"] += qty

        # Build formatted string from warehouse dictionary
        values = []
        for key in warehouses:
            values.append("{}: {}".format(key, warehouses[key]))

        meta["value"] = ", ".join(values)
        source_product["product"]["meta"].append(meta)

        # Create tags from specific meta values and append to current tags
        for item in source_product["product"]["meta"]:
            key = item["key"]
            value = item["value"]
            if key.startswith("oenr_tag") and value != "":
                source_product["product"]["tags"] += "," + value


        # Create meta to show if there is stock for the product in any warehouse
        instockMeta = collections.OrderedDict()

        instock = "In Stock" if any(availability["qty"] > 0 for availability in source_product["product"]["variants"]["qty_availability"]) else "No Stock"
        
        instockMeta["key"] = "instock"
        instockMeta["value"] = instock
        source_product["product"]["meta"].append(instockMeta)

    @staticmethod
    def _getSeriesNameByWarehouseCode(warehouse_code):
        if warehouse_code == "W_02": return "Ver1"
        if warehouse_code == "W_03": return "JHB"
        if warehouse_code == "W_04": return "BLM"
        if warehouse_code == "W_05": return "SPR"
        raise Exception(
            "No series mapping for warehouse {}".format(warehouse_code))

    # Testing image implementation
    def testGetImage(self):
        import os
        import base64

        self._setConfig()

        with sap_one.openConn(self._config["dsn"]) as cursor:
            sql = '''SELECT TOP 1 BitmapPath FROM oadp'''
            cursor.execute(sql)
            rows = utils.getRowsODBC(cursor, 1)
            if len(rows) == 0:
                response = {
                    "status": False,
                    "description": "No path set for picture folder",
                    "line": utils.lineNo()
                }
                return json.dumps(response, indent=self._indent)

            imagePath = rows[0]["bitmappath"]  # Must be lowercase
            path = os.path.join(imagePath, 'PW016GRE.jpg')

            if os.path.isfile(path):
                with open(path, "rb") as f:
                    encoded = base64.b64encode(f.read())
                    encoded = encoded.decode("utf-8")
            else:
                response = {
                    "status": False,
                    "description": "Image {} not found".format(path),
                    "line": utils.lineNo()
                }
                return json.dumps(response, indent=self._indent)

        response = {
            "status": True,
            "timer": 0,
            "data": {
                "path": imagePath,
                "imagePath": path,
                "encoded": encoded
            }
        }
        return json.dumps(response, indent=self._indent)

    def _getSalesGLAccount(self, item_code, customer_code):
        self._setConfig()

        with sap_one.openConn(self._config["dsn"]) as cursor:
            sql = '''
              SELECT TOP 1 AcctCode FROM oact WITH (NOLOCK)
              WHERE formatcode =
                (
                SELECT TOP 1 inv.U_SalesGLAcc FROM oitm inv WITH (NOLOCK)
                WHERE inv.ItemCode = '%(ItemCode)s'
                )
                +
                (
                SELECT TOP 1 t1.Segment_1 FROM OCRD t0 WITH (NOLOCK) inner join OACT t1 WITH (NOLOCK) on t0.DebPayAcct = t1.AcctCode
                WHERE t0.CardCode = '%(CardCode)s'
                )
            '''
            sql = utils.bindSqlParams(sql, {"ItemCode": str(item_code), "CardCode": str(customer_code)})
            cursor.execute(sql)
            rows = utils.getRowsODBC(cursor, 1)

            if len(rows) > 0:
                return rows[0]['acctcode']

        return None

    def _getCOGSGLAccount(self, item_code, customer_code):
        self._setConfig()

        with sap_one.openConn(self._config["dsn"]) as cursor:
            sql = '''
              SELECT TOP 1 AcctCode FROM oact WITH (NOLOCK)
              WHERE formatcode =
                (
                SELECT TOP 1 inv.U_COSAcc FROM oitm inv WITH (NOLOCK)
                WHERE inv.ItemCode = '%(ItemCode)s'
                )
                +
                (
                SELECT TOP 1 t1.Segment_1 FROM OCRD t0 WITH (NOLOCK) inner join OACT t1 WITH (NOLOCK) on t0.DebPayAcct = t1.AcctCode
                WHERE t0.CardCode = '%(CardCode)s'
                )
            '''
            sql = utils.bindSqlParams(sql, {"ItemCode": str(item_code), "CardCode": str(customer_code)})
            cursor.execute(sql)
            rows = utils.getRowsODBC(cursor, 1)

            if len(rows) > 0:
                return rows[0]['acctcode']

        return None

    def _getLatestTransaction(self, order):
        if "transactions" in order and len(order["transactions"]) > 0:
            # sort transactions by "completed_date" date desc
            sorted_transactions = sorted(
                order["transactions"],
                key=lambda k: datetime.strptime(k["completed_date"], "%Y-%m-%d %H:%M:%S.%f"),
                reverse=True
            )
            return sorted_transactions[0]

        return None
