SELECT
    row_number() over (order by inv.StockCode) AS n,

    RTRIM(LTRIM(inv.StockCode))                AS "source_product_code",
    RTRIM(LTRIM(inv.StockCode))                AS "source_variant_code",
    inv.Description                            AS "title",
    ''                                         AS "collection",
    ''                                         AS "product_type",
    ''                                         AS "vendor",
    ''                                         AS "variants.option1",
    ''                                         AS "variants.option2",
    ''                                         AS "variants.option3",
    RTRIM(LTRIM(inv.StockCode))                AS "variants.sku",
    ''                                         AS "body_html",
    0                                          AS "variants.weight",
    ''                                         AS "variants.barcode",

    'B1'                                       AS "default_qty_availability",
    STUFF((
      SELECT
        ',' + CONVERT(VARCHAR, LTRIM(RTRIM(whse.Warehouse))) + '|' +
              CONVERT(VARCHAR, CONVERT(INTEGER, COALESCE(whse.QtyOnHand, 0) - COALESCE(whse.QtyAllocated, 0)))
      FROM InvWarehouse whse WITH (NOLOCK)
      WHERE whse.StockCode = RTRIM(LTRIM(inv.StockCode))
      ORDER BY LTRIM(RTRIM(whse.Warehouse))
    for xml path('')),1,1,'')
                                               AS "csv_qty_availability",

    'A'                                        AS "default_price_tier",
    STUFF((
      SELECT
        ',' + CONVERT(VARCHAR, LTRIM(RTRIM(price_tiers.PriceCode))) + '|' +
              CONVERT(VARCHAR, CAST(ISNULL(price_tiers.SellingPrice, 0) AS FLOAT))
      FROM InvPrice price_tiers WITH (NOLOCK)
      WHERE price_tiers.StockCode = RTRIM(LTRIM(inv.StockCode))
      ORDER BY LTRIM(RTRIM(price_tiers.PriceCode))
    for xml path('')),1,1,'')
                                               AS "csv_price_tiers",

    'true'                                     AS "variants.inventory_management",
    LOWER(inv2.OnlineSync)                     AS "product_active",
    ''                                         AS "tags"

FROM InvMaster inv WITH (NOLOCK)
LEFT JOIN [InvMaster+] inv2 WITH (NOLOCK)
ON inv2.StockCode = inv.StockCode

WHERE LOWER(inv2.OnlineSync) IS NOT null;