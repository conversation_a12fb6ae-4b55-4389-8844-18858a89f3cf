{"dbName": "local.db", "dsn": "Driver={SQL Server};Server=************;Database=SysproCompanyGHP;Uid=GHP;Pwd=*********;", "audit_limit": 1000, "push": {"source_id": 1597, "limit": 500, "token": "O2H0O6TQ0WY96WOGBD97SDDBRG3AN6VRC7H8TDTH", "writeLog": "http://localhost:13337/writeLog", "getProducts": "http://localhost:13337/odbc/getProductsBatch", "getCustomers": "http://localhost:13337/odbc/getCustomersBatch", "countProducts": "http://localhost:13337/odbc/countProducts", "countCustomers": "http://localhost:13337/odbc/countCustomers", "getMeta": "http://localhost:13337/odbc/getMeta", "setMeta": "http://localhost:13337/odbc/setMeta", "productsQueue": "https://app.stock2shop.com/v1/products/queue", "customersQueue": "https://app.stock2shop.com/v1/customers/queue"}}