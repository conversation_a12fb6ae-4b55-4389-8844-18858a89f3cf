SELECT
    CONVERT(<PERSON><PERSON>HAR, cl.Account)                                        AS 'source_customer_code',

    CASE
        WHEN CHARINDEX(' ', cl.Contact_Person) > 0 THEN RTRIM(ISNULL(LEFT(cl.Contact_Person, CHARINDEX(' ', cl.Contact_Person) - 1), ''))
        ELSE RTRIM(ISNULL(cl.Contact_Person, ''))
    END                                                                 AS 'first_name',

    CASE
        WHEN CHARINDEX(' ', cl.Contact_Person) > 0 THEN LTRIM(RTRIM(ISNULL(RIGHT(cl.Contact_Person, LEN(cl.Contact_Person) - CHARINDEX(' ', cl.Contact_Person) + 1), '')))
        ELSE ''
    END                                                                 AS 'last_name',

    cl.EMail                                                            AS 'email',

    CASE
        WHEN cl.On_Hold = 1 THEN 'false'
        ELSE 'true'
    END                                                                 AS 'customer_active',

    0                                                                   AS 'accepts_marketing',
    cl.Physical1                                                        AS 'address.address1', -- OR Address?
    cl.Physical2                                                        AS 'address.address2',
    cl.Physical3                                                        AS 'address.city',
    ''                                                                  AS 'address.country',
    ''                                                                  AS 'address.country_code',
    ''                                                                  AS 'address.province',
    ''                                                                  AS 'address.province_code',
    cl.PhysicalPC                                                       AS 'address.zip',
    cl.Name                                                             AS 'address.company',
    cl.Telephone                                                        AS 'address.phone',
    ''                                                                  AS 'meta_show_online',
    ''                                                                  AS 'meta_display_name',
    cl.Credit_Limit                                                     AS 'meta_credit_limit',
    cl.DCBalance                                                        AS 'meta_balance',
    ''                                                                  AS 'meta_branch',
    ''                                                                  AS 'price_tier',
    ''                                                                  AS 'qty_availability',

    -- segments
    -- Eg: VALUE  AS  "segment|owner|type|key|operator"
    'PriceList1'                                                        AS 'segment|source|products|variants.price_tiers.tier|equal',
    '0'                                                                 AS 'segment|source|products|variants.price_tiers.price|greater than',

    -- change this depending ON tax rules
    CASE cl.CT
        WHEN 4 THEN '0'
        ELSE '15'
    END                                                                 AS 'meta_tax_rate',
    cl.AutoDisc                                                         AS 'meta_discount',
    -- Discount BY customer GROUP
    STUFF(
        (SELECT CONVERT(VARCHAR, vdln.iStockID) + '|' +
                CONVERT(VARCHAR,
                CASE WHEN getdate() >= vdln.dEffDate AND getdate() <= vdln.dExpDate
                  THEN vdlvln.fPriceDisc
                  ELSE 0
                END
                ) + ','
         FROM _etblVDAR vd WITH (NOLOCK)
             INNER JOIN _etblVDLnAR vdln WITH (NOLOCK)
                 ON vd.IDVD = vdln.iVDID
             INNER JOIN _etblVDLnLvlAR vdlvln WITH (NOLOCK)
                 ON vdln.IDVDLn = vdlvln.iVDLnID
         WHERE vd.bARAPAll = 0                -- discount for all
          AND vd.iARAPID = 0                  -- per accounts receivable
          AND vdlvln.fQuantity = 1            -- volume discount
          AND vdln.iStockID > 0               -- stock item
          AND vd.iGroupID = cl.iClassID       -- customer GROUP
          AND vdln.iStGroupID = 0             -- stock GROUP
          AND vdln.bUseStockPrc = 0           -- fixed OR percentage
         ORDER BY vdln.iStockID
         FOR XML PATH ('')), 1, 0, '')
                                                                       AS 'csv_product_customer_group',

    -- Discount BY customer GROUP fixed
    STUFF(
        (SELECT CONVERT(VARCHAR, vdln.iStockID) + '|' +
                CONVERT(VARCHAR,
                CASE WHEN getdate() >= vdln.dEffDate AND getdate() <= vdln.dExpDate
                  THEN vdlvln.fPriceDisc
                  ELSE 0
                END
                ) + ','
         FROM _etblVDAR vd WITH (NOLOCK)
             INNER JOIN _etblVDLnAR vdln WITH (NOLOCK)
                 ON vd.IDVD = vdln.iVDID
             INNER JOIN _etblVDLnLvlAR vdlvln WITH (NOLOCK)
                 ON vdln.IDVDLn = vdlvln.iVDLnID
         WHERE vd.bARAPAll = 0                -- discount for all
          AND vd.iARAPID = 0                  -- per accounts receivable
          AND vdlvln.fQuantity = 1            -- volume discount
          AND vdln.iStockID > 0               -- stock item
          AND vd.iGroupID = cl.iClassID       -- customer GROUP
          AND vdln.iStGroupID = 0             -- stock GROUP
          AND vdln.bUseStockPrc = 1           -- fixed OR percentage
         ORDER BY vdln.iStockID
         FOR XML PATH ('')), 1, 0, '')
                                                                       AS 'csv_product_customer_group_fixed'

FROM Client cl WITH (NOLOCK)
WHERE RTRIM(ISNULL(cl.EMail, '')) <> ''
    AND cl.Account = '%(source_customer_code)s'
;