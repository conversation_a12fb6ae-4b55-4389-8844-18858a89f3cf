{"dbName": "local.db", "dsn": "Driver={SQL Server Native Client 11.0};Server=serverName\\dbInstance;Database=dbName;Uid=user;Pwd=****;", "dsnNoDb": "Driver={SQL Server Native Client 11.0};Server=serverName\\dbInstance;Uid=user;Pwd=****;", "server": "serverName\\dbInstance", "companyDb": "companyDb", "userName": "manager", "****word": "manager", "dbServerType": "MSSQL2008", "dbUserName": "user", "dbPassword": "****word", "licenseServer": "serverName", "audit_limit": 500, "audit_image_limit": 100, "push": {"source_id": 1390, "limit": 100, "image_limit": 2, "token": "3YM05AE8CK9MNNSER9U28ZA84R6IREH8NLAMOFRP", "writeLog": "http://localhost:13337/writeLog", "getProducts": "http://localhost:13337/sapone/getProductsBatch", "getCustomers": "http://localhost:13337/sapone/getCustomersBatch", "countProducts": "http://localhost:13337/sapone/countProducts", "countCustomers": "http://localhost:13337/sapone/countCustomers", "getMeta": "http://localhost:13337/sapone/getMeta", "setMeta": "http://localhost:13337/sapone/setMeta", "productsQueue": "https://app.stock2shop.com/v1/products/queue", "customersQueue": "https://app.stock2shop.com/v1/customers/queue"}}