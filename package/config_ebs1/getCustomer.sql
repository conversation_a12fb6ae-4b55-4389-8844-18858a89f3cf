select
    CardCode                            as 'source_customer_code',
    CardName                            as 'card_name',
    E_Mail                              as 'email',

    case ocrd.U_S2sActive
        when 1 then 'true'
        when 0 then 'false'
        else 'false'
    end                                 as "customer_active",

    1                                   as 'accepts_marketing',
    MailAddres                          as 'address.address1', -- or Address?
    ''                                  as 'address.address2',
    City                                as 'address.city',
    Country                             as 'address.country',
    'ZA'                                as 'address.country_code',
    ''                                  as 'address.province',
    ''                                  as 'address.province_code',
    ZipCode                             as 'address.zip',
    ''                                  as 'address.company',
    CntctPrsn                           as 'address.contact_person',
    Phone1                              as 'address.phone',
    SlpCode                             as 'meta_sales_rep_code',

    -- Discount groups
    stuff(
    (select
    convert(varchar, ospg.ObjKey) + '|' + -- oitb.ItmsGrpCod
    convert(varchar, ospg.Discount) + ','
    from ospg
    where ospg.CardCode = ocrd.CardCode
    and ospg.ObjType = 52 -- Discount Groups
    and ospg.Discount > 0
    order by ospg.ObjKey
    for xml path('')),1,0,'')
                                        as 'csv_discount_groups',
    -- For customers

    ListNum                             as 'price_tier',
    ''                                  as 'qty_availability',

    -- segments
    -- Eg: value                        as  "segment|owner|type|key|operator"

    ListNum                             as 'segment|source|products|variants.price_tiers.tier|equal',
    '0'                                 as 'segment|source|products|variants.price_tiers.price|greater than',
    'sales_rep_code'                    as 'segment|source|products|meta.key|equal',
     SlpCode                            as 'segment|source|products|meta.value|equal'

from ocrd
where CardCode = '%(source_customer_code)s';
