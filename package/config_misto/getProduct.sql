select
    inv.ItemCode                as "source_product_code",
    inv.ItemCode                as "source_variant_code",
    inv.ItemName                as "title",
    oitb.ItmsGrpNam             as "collection",
    ''                          as "product_type",
    ''                          as "vendor",
    ''                          as "variants.option1_name",
    ''                          as "variants.option1_value",
    ''                          as "variants.option2",
    ''                          as "variants.option3",
    ''                          as "body_html",
    0                           as "variants.weight",
    inv.CodeBars                as "variants.barcode",

     -- All price tiers
     '1'                         as "default_price_tier",
     stuff(
     (select
     convert(varchar, itm1.PriceList) + '|' +
     convert(varchar, itm1.Price) + ','
     from itm1
     where itm1.ItemCode = inv.ItemCode
     order by itm1.PriceList
     for xml path('')),1,0,'')
                                 as "csv_price_tiers",

     -- All qty availability
     '01'                        as "default_qty_availability",
     stuff(
     (select
     convert(varchar, oitw.WhsCode) + '|' +
     convert(varchar, oitw.OnHand-oitw.IsCommited)+','
     from oitw
     where oitw.ItemCode = inv.ItemCode
     order by oitw.WhsCode
     for xml path('')),1,0,'')
                                 as "csv_qty_availability",

    'true'                      as "variants.inventory_management",

    CASE
        WHEN inv.validFor = 'Y' THEN 'true'
        ELSE 'false'
    END                         as "product_active",

    ''                          as "tags",

    oitb.ItmsGrpCod             as "meta_item_group_code",
    oitb.ItmsGrpNam             as "meta_item_group_name",
    OVTG.Rate					as "meta_tax_rate",

    -- Special prices for all periods and volumes
    stuff(
    (select
    convert(varchar, ospp.CardCode) + '|' +
    convert(varchar, ospp.Discount)+','
    from ospp
    where ospp.ItemCode = inv.ItemCode
    and ospp.Discount > 0
    order by ospp.ItemCode
    for xml path('')),1,0,'')
                                as 'csv_special_prices'

    -- Special prices for period join ospp with spp1
    -- Special prices for volume join ospp with spp2

FROM OITM inv WITH (NOLOCK)
left join oitb WITH (NOLOCK) on inv.ItmsGrpCod = oitb.ItmsGrpCod
left join OVTG WITH (NOLOCK) on OVTG.Code = inv.VatGourpSa

where NOT inv.ItmsGrpCod in ('143', '154')
and inv.ItemCode = '%(source_variant_code)s';


