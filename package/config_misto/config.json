{"dbName": "local.db", "dsn": "Driver={SQL Server};Server=BETSRV2;Database=SBOBethaliaM01;Uid=sa;Pwd=********;", "server": "BETSRV", "companyDb": "SBOMistroFoodsM01", "userName": "Manager", "password": "ebs123", "dbServerType": "MSSQL2008", "dbUserName": "sa", "dbPassword": "********", "licenseServer": "**********", "audit_limit": 2000, "push": {"source_id": 729, "limit": 500, "token": "1PV079CSMDRXMV7AHTTK8GHX0YMGXJVDY58CDQZM", "writeLog": "http://localhost:13337/writeLog", "getProducts": "http://localhost:13337/sapone/getProductsBatch", "getCustomers": "http://localhost:13337/sapone/getCustomersBatch", "countProducts": "http://localhost:13337/sapone/countProducts", "countCustomers": "http://localhost:13337/sapone/countCustomers", "getMeta": "http://localhost:13337/sapone/getMeta", "setMeta": "http://localhost:13337/sapone/setMeta", "productsQueue": "https://app.stock2shop.com/v1/products/queue", "customersQueue": "https://app.stock2shop.com/v1/customers/queue"}}