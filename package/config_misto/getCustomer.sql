select
    -- bug with push.exe causes source_customer_code to be converted to int even though it
    -- is a string. Concatenate it to avoid this.
    ocpr.CardCode + '-' + convert(varchar, ocpr.CntctCode) as 'source_customer_code',
    CardName                                               as 'card_name',
    E_Mail                                                 as 'email',
    case ocrd.validFor
        when 'Y' then 'true'
        else 'false'
        end                                                as 'customer_active',
    1                                                      as 'accepts_marketing',
    ocrd.MailAddres                                        as 'address.address1', -- or Address?
    ''                                                     as 'address.address2',
    crd1.City                                              as 'address.city',
    crd1.Country                                           as 'address.country',
    'ZA'                                                   as 'address.country_code',
    ''                                                     as 'address.province',
    ''                                                     as 'address.province_code',
    crd1.ZipCode                                           as 'address.zip',
    ''                                                     as 'address.company',
    ocrd.CntctPrsn                                         as 'address.contact_person',
    ocrd.Phone1                                            as 'address.phone',

    -- XML addresses
    (
        SELECT crd1.Address                 as 'address_code',
               CASE crd1.AdresType
                   WHEN 'B' THEN 'billing'
                   WHEN 'S' THEN 'shipping'
                   END
                                            as 'type',
               coalesce(crd1.Block, '')     as 'address1',
               coalesce(crd1.Address2, '')  as 'address2',
               coalesce(crd1.City, '')      as 'city',
               coalesce(crd1.Country, '')   as 'country',
               coalesce(crd1.Country, '')   as 'country_code',
               coalesce(crd1.County, '')    as 'province',
               coalesce(crd1.State, '')     as 'province_code',
               coalesce(crd1.ZipCode, '')   as 'zip',
               coalesce(ocrd.Phone1, '')    as 'phone',
               coalesce(ocrd.CardName, '')  as 'company',
               coalesce(ocpr.FirstName, '') as 'first_name',
               coalesce(ocpr.LastName, '')  as 'last_name'
        FROM crd1
        WHERE crd1.CardCode = ocpr.CardCode
          and crd1.AdresType in ('S', 'B')
        ORDER BY crd1.Address
        FOR xml PATH ('address'), root ('addresses')
    )                                                      as 'xml_addresses',

    -- Discount groups
    stuff(
            (select convert(varchar, ospg.ObjKey) + '|' + -- oitb.ItmsGrpCod
                    convert(varchar, ospg.Discount) + ','
             from ospg
             where ospg.CardCode = ocrd.CardCode
               and ospg.ObjType = 52 -- Discount Groups
               and ospg.Discount > 0
             order by ospg.ObjKey
             for xml path('')), 1, 0, '')
                                                           as 'csv_discount_groups',
    -- For customers

    ListNum                                                as 'price_tier',
    ''                                                     as 'qty_availability',
    '1471'                                                 as 'meta_channel_id'

from ocrd WITH (NOLOCK)
         INNER JOIN ocpr WITH (NOLOCK) on ocrd.CardCode = ocpr.CardCode
         LEFT JOIN crd1 WITH (NOLOCK)
                   on crd1.CardCode = ocpr.CardCode and crd1.Address = ocrd.ShipToDef and crd1.AdresType = 'S'
where CardType = 'C' -- Customer
  and datalength(e_mail) <> 0
  and E_Mail is not null
  and ocpr.CardCode + '-' + convert(varchar, ocpr.CntctCode) = '%(source_customer_code)s'
;
