select

    (select count(inv.ItemCode) as product_count
FROM OITM inv WITH (NOLOCK)
left join oitb WITH (NOLOCK) on inv.ItmsGrpCod = oitb.ItmsGrpCod
left join OVTG WITH (NOLOCK) on OVTG.Code = inv.VatGourpSa

where NOT inv.ItmsGrpCod in ('143', '154')
    )
                                                as count,

    (select count(inv.ItemCode) as product_count
FROM OITM inv WITH (NOLOCK)
left join oitb WITH (NOLOCK) on inv.ItmsGrpCod = oitb.ItmsGrpCod
left join OVTG WITH (NOLOCK) on OVTG.Code = inv.VatGourpSa

where NOT inv.ItmsGrpCod in ('143', '154')  and inv.validFor = 'Y'
    )
                                                as active,

    (select count(inv.ItemCode) as product_count
FROM OITM inv WITH (NOLOCK)
left join oitb WITH (NOLOCK) on inv.ItmsGrpCod = oitb.ItmsGrpCod
left join OVTG WITH (NOLOCK) on OVTG.Code = inv.VatGourpSa

where NOT inv.ItmsGrpCod in ('143', '154') and inv.validFor = 'N'
    )
                                                as inactive,

    (select count(inv.ItemCode) as product_count
FROM OITM inv WITH (NOLOCK)
left join oitb WITH (NOLOCK) on inv.ItmsGrpCod = oitb.ItmsGrpCod
left join OVTG WITH (NOLOCK) on OVTG.Code = inv.VatGourpSa

)
                                                as total;


