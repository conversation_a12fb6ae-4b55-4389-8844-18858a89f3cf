SELECT
  st.Code                                                              AS "source_product_code",
  st.Code                                                              AS "source_variant_code",
  st.Description_1                                                     AS "title",
  grp.Description                                                      AS "collection",
  'n/a'                                                                AS "product_type",
  'n/a'                                                                AS "vendor",
  st.Code                                                              AS "variants.sku",
  ''                                                                   AS "variants.option1",
  ''                                                                   AS "variants.option2",
  ''                                                                   AS "variants.option3",
  ''                                                                   AS "body_html",
  0                                                                    AS "variants.weight",
  st.Bar_code                                                          AS "variants.barcode",
  'true'                                                               AS "variants.inventory_management",
  CASE st.ulIIWebActive
    WHEN 'ACTIVE' THEN 'true'
    ELSE 'false'
  END                                                                  AS "product_active",
  ''                                                                   AS "tags",

  'EUR (DF4)'                                                                  AS "default_price_tier",
  stuff(
    (select
      ',' + convert(varchar, price_names.cName) + '|' +
            convert(varchar, price.fExclPrice)
    from dbo._etblPriceListPrices price WITH (NOLOCK)

      inner join _etblPriceListName price_names WITH (NOLOCK)
        ON price.iPriceListNameID = price_names.IDPriceListName AND price.iStockID = st.StockLink and price.iWarehouseID = 11

    order by price_names.cName
    for xml path('')),1,1,'')
                                                                       AS "csv_price_tiers",


  CAST(ISNULL(st.Qty_On_Hand - st.QtyOnSO, 0) AS INTEGER)              AS "variants.qty",
  'CD'                                                               AS "default_qty_availability",
  STUFF((
    SELECT
      CONVERT(VARCHAR, whse_mst.Code) + '|' +
      CONVERT(VARCHAR, ISNULL(whse.WHQtyOnHand - whse.WHQtyOnSO, 0)) + ','
    FROM WhseStk whse WITH (NOLOCK)

      LEFT JOIN WhseMst whse_mst WITH (NOLOCK)
        ON whse_mst.WhseLink = whse.WHWhseID

    WHERE whse.WHStockLink = st.StockLink
    ORDER BY whse_mst.Code
    for xml path('')),1,0,'')
                                                                      AS "csv_qty_availability",

  -- List of images for this product
  -- Enables automatic pulling of new images when image is added
  ISNULL(
  STUFF(
    (SELECT ',' + convert(varchar, i.idInvImage)
    FROM _etblInvImages i WITH (NOLOCK)
    WHERE st.StockLink = i.iStockLink
    ORDER BY i.idInvImage
    FOR XML PATH ('')), 1, 1, ''), '')
                                                                       AS "meta_image_id_csv"

FROM StkItem st WITH (NOLOCK)

  -- General info
  LEFT JOIN GrpTbl grp WITH (NOLOCK) ON grp.StGroup = st.ItemGroup

--    -- Extra meta-data
--   LEFT JOIN _etblInvSegValue seg1 WITH (NOLOCK)
--     ON seg1.idInvSegValue = st.iInvSegValue1ID
--   LEFT JOIN _etblInvSegValue seg2 WITH (NOLOCK)
--     ON seg2.idInvSegValue = st.iInvSegValue2ID
--   LEFT JOIN _etblInvSegValue seg3 WITH (NOLOCK)
--     ON seg3.idInvSegValue = st.iInvSegValue3ID
--   LEFT JOIN _etblInvSegValue seg4 WITH (NOLOCK)
--     ON seg4.idInvSegValue = st.iInvSegValue4ID
--   LEFT JOIN _etblInvSegValue seg5 WITH (NOLOCK)
--     ON seg5.idInvSegValue = st.iInvSegValue5ID
--   LEFT JOIN _etblInvSegValue seg6 WITH (NOLOCK)
--     ON seg6.idInvSegValue = st.iInvSegValue6ID
--   LEFT JOIN _etblInvSegValue seg7 WITH (NOLOCK)
--     ON seg7.idInvSegValue = st.iInvSegValue7ID

WHERE st.ulIIWebActive in ('ACTIVE', 'DELETE')
;