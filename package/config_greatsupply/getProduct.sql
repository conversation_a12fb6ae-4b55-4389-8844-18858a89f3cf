SELECT
  st.Code                                                              AS "source_product_code",
  st.StockLink                                                         AS "source_variant_code",
  st.Description_1                                                     AS "title",
  ''                                                                   AS "collection",
  ''                                                                   AS "product_type",
  ''                                                                   AS "vendor",
  st.Code                                                              AS "variants.sku",
  ''                                                                   AS "variants.option1",
  ''                                                                   AS "variants.option2",
  ''                                                                   AS "variants.option3",
  ''                                                                   AS "body_html",
  0                                                                    AS "variants.weight",
  ''                                                                   AS "variants.barcode",
  'true'                                                               AS "variants.inventory_management",
  CASE st.bSyncToSOT
    WHEN 1 THEN 'true'
    ELSE 'false'
  END                                                                  AS "product_active",
  ''                                                                   AS "tags",

  '1 Small Packs'                                                      AS "default_price_tier",
  stuff(
    (select
      ',' + convert(varchar, price_names.cName) + '|' +
            convert(varchar, price.fInclPrice)
    from dbo._etblPriceListPrices price WITH (NOLOCK)

      inner join _etblPriceListName price_names WITH (NOLOCK)
        ON price.iPriceListNameID = price_names.IDPriceListName AND price.iStockID = st.StockLink and price.iWarehouseID = 0

    order by price_names.cName
    for xml path('')),1,1,'')
                                                                       AS "csv_price_tiers",

  'Mstr'                                                               AS "default_qty_availability",
  STUFF((
    SELECT
      CONVERT(VARCHAR, whse_mst.Code) + '|' +
      CONVERT(VARCHAR, ISNULL(sq.QtyOnHand, 0)) + ','
    FROM _etblStockQtys sq WITH (NOLOCK)

      LEFT JOIN WhseMst whse_mst WITH (NOLOCK)
        ON whse_mst.WhseLink = sq.WhseID
    WHERE sq.StockID = st.StockLink
    ORDER BY whse_mst.Code
    for xml path('')),1,0,'')
                                                                      AS "csv_qty_availability",

  -- List of images for this product
  -- Enables automatic pulling of new images when image is added
  ISNULL(
  STUFF(
    (SELECT ',' + convert(varchar, i.idInvImage)
    FROM _etblInvImages i WITH (NOLOCK)
    WHERE st.StockLink = i.iStockLink
    ORDER BY i.idInvImage
    FOR XML PATH ('')), 1, 1, ''), '')
                                                                       AS "meta_image_id_csv"

FROM StkItem st WITH (NOLOCK)
WHERE (LOWER(st.bSyncToSOT)) in ('1','0')
AND st.StockLink = '%(source_variant_code)s'
;