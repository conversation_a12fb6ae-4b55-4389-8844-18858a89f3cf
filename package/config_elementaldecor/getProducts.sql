SELECT
    rtrim(i.ItemCode)         AS "source_product_code",
    rtrim(i.ItemCode)         AS "source_variant_code",
    rtrim(i.Description)      AS "title",
    rtrim(i.ItemCode)         AS "variants.sku",
    rtrim(ig.Description)     AS "collection",
    ''                        AS "product_type",
    ''                        AS "vendor",
    ''                        AS "variants.option1",
    ''                        AS "variants.option2",
    ''                        AS "variants.option3",
    ''                        AS "body_html",
    rtrim(i.NettMass)         AS "variants.weight",
    rtrim(i.Barcode)          AS "variants.barcode",

    -- Warehouse 1 qty
    (
      (
        (wh1.OpeningQty + wh1.QtyBuyLast + wh1.QtyAdjustLast - wh1.QtySellLast) +
        (wh1.QtyBuyThis01 + wh1.QtyBuyThis02 + wh1.QtyBuyThis03 + wh1.QtyBuyThis04 +
            wh1.QtyBuyThis05 + wh1.QtyBuyThis06 + wh1.QtyBuyThis07 + wh1.QtyBuyThis08 +
            wh1.QtyBuyThis09 + wh1.QtyBuyThis10 + wh1.QtyBuyThis11 + wh1.QtyBuyThis12 +
            wh1.QtyBuyThis13) +
        (wh1.QtyAdjustThis01 + wh1.QtyAdjustThis02 + wh1.QtyAdjustThis03 +
            wh1.QtyAdjustThis04 + wh1.QtyAdjustThis05 + wh1.QtyAdjustThis06 +
            wh1.QtyAdjustThis07 + wh1.QtyAdjustThis08 + wh1.QtyAdjustThis09 +
            wh1.QtyAdjustThis10 + wh1.QtyAdjustThis11 + wh1.QtyAdjustThis12 +
            wh1.QtyAdjustThis13) -
        (wh1.QtySellThis01 + wh1.QtySellThis02 + wh1.QtySellThis03 + wh1.QtySellThis04 +
            wh1.QtySellThis05 + wh1.QtySellThis06 + wh1.QtySellThis07 + wh1.QtySellThis08 +
            wh1.QtySellThis09 + wh1.QtySellThis10 + wh1.QtySellThis11 + wh1.QtySellThis12 +
            wh1.QtySellThis13)
      )
    )                         as "qty_on_hand",

    -(ISNULL(U.SalesOrder, 0) + ISNULL(U.BatchQty, 0))
                              as "qty_on_sales_order",

    -- Note that price can be set by warehouse (store)
    '03'                      as "default_price_tier",
    (
    '01|' + REPLACE(CAST(wh1.SellExcl01 AS VARCHAR(30)), ',', '.') + ',' +
    '02|' + REPLACE(CAST(wh1.SellExcl02 AS VARCHAR(30)), ',', '.') + ',' +
    '03|' + REPLACE(CAST(wh1.SellExcl03 AS VARCHAR(30)), ',', '.') + ',' +
    '04|' + REPLACE(CAST(wh1.SellExcl04 AS VARCHAR(30)), ',', '.') + ',' +
    '05|' + REPLACE(CAST(wh1.SellExcl05 AS VARCHAR(30)), ',', '.') + ',' +
    '06|' + REPLACE(CAST(wh1.SellExcl06 AS VARCHAR(30)), ',', '.') + ',' +
    '07|' + REPLACE(CAST(wh1.SellExcl07 AS VARCHAR(30)), ',', '.') + ',' +
    '08|' + REPLACE(CAST(wh1.SellExcl08 AS VARCHAR(30)), ',', '.') + ',' +
    '09|' + REPLACE(CAST(wh1.SellExcl09 AS VARCHAR(30)), ',', '.') + ',' +
    '10|' + REPLACE(CAST(wh1.SellExcl10 AS VARCHAR(30)), ',', '.') + ',' +
    'special|' + REPLACE(CAST(wh1.SpecialPriceExcl AS VARCHAR(30)), ',', '.'))
                              as "csv_price_tiers",
    case
        when rtrim(i.UserDefNum01) = 1 then 'true'
        else 'false'
    end                       as "product_active",
    ''                        as "tags",
    'true'                    as "variants.inventory_management",
    i.Picture                 as "picture",
    rtrim(i.UserDefNum01)     as "meta_user_def_num_1",
    rtrim(i.UserDefNum02)     as "meta_user_def_num_2",
    rtrim(i.UserDefNum03)     as "meta_user_def_num_3",
    rtrim(i.UserDefText01)    as "meta_user_def_text_1",
    rtrim(i.UserDefText02)    as "meta_user_def_text_2",
    rtrim(i.UserDefText03)    as "meta_user_def_text_3"

FROM Inventory i

    -- Warehouse 1
    left join MultiStoreTrn wh1 on
      wh1.ItemCode = i.ItemCode and
      wh1.StoreCode = '%(multi_store)s'
    left join InventoryGroups ig on
      ig.InvGroup = wh1.InvGroup

    -- Attribute info
    left join InventoryCategory ic on
      i.Category = ic.ICCode

    LEFT JOIN Unposted U on
      i.ItemCode = U.ItemCode AND '%(multi_store)s' = U.StoreCode

WHERE i.Physical = 1 AND rtrim(i.UserDefNum01) in (1, 2) AND wh1.StoreCode = '%(multi_store)s'
;