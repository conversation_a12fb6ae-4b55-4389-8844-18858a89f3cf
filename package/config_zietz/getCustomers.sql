select *
from (
select
    row_number() over (order by ocrd.CardCode) as n,

    -- There must only be one result row per CardCode!
    CardCode   as 'source_customer_code',
    CardName   as 'card_name',
    --E_Mail     as 'email',
    '<EMAIL>'     as 'email',

    case ocrd.U_S2sActive
        when 1 then 'true'
        when 0 then 'false'
        else 'false'
    end        as "customer_active",

    1          as 'accepts_marketing',
    MailAddres as 'address.address1', -- or Address?
    ''         as 'address.address2',
    City       as 'address.city',
    Country    as 'address.country',
    'ZA'       as 'address.country_code',
    ''         as 'address.province',
    ''         as 'address.province_code',
    ZipCode    as 'address.zip',
    ''         as 'address.company',
    CntctPrsn  as 'address.contact_person',
    Phone1     as 'address.phone',

    -- Discount groups
    stuff(
    (select
    convert(varchar, ospg.ObjKey) + '|' + -- oitb.ItmsGrpCod
    convert(varchar, ospg.Discount) + ','
    from ospg
    where ospg.CardCode = ocrd.CardCode
    and ospg.ObjType = 52 -- Discount Groups
    and ospg.Discount > 0
    order by ospg.ObjKey
    for xml path('')),1,0,'')
               as 'csv_discount_groups',
               -- For customers

    ListNum    as 'price_tier',
    ''         as 'qty_availability'

from ocrd
where 1 = 1
-- Only track customers that have a value for U_S2sActive
and (
    U_S2sActive = 1 or
    U_S2sActive = 0
)
and CardType = 'C' -- Customer
and CardName not like '%(closed)'
and datalength(e_mail) <> 0
and E_Mail is not null

) as rows
where
n > '%(audit_lower_limit)n'
and n <= '%(audit_upper_limit)n'