select

    RTRIM(inv2.WebProductCode)                  AS "source_product_code",
    RTRIM(inv.StockCode)                        AS "source_variant_code",
    RTRIM(inv2.WebDescription)					AS "title",
    RTRIM(pc.Description)                       AS "collection",
    CASE WHEN RTRIM(LTRIM(LOWER(inv.UserField1))) = 'dead stock'
        THEN 'Clearance'
        ELSE ''
    END					                        AS "product_type",
    RTRIM(inv2.Brand)					        AS "vendor",
    'Options'                                   AS "variants.option1_name",
    RTRIM(inv2.WebOption)                       AS "variants.option1_value",
    RTRIM(inv.StockCode)                        AS "variants.sku",
    inv.LongDesc                                AS "body_html",
    0                                           AS "variants.weight",
    CONCAT(inv2.PreludeSku,'|',inv.StockUom)    AS "variants.barcode",
    'true'                                      AS "variants.inventory_management",
    CASE WHEN LTRIM(RTRIM(lower(inv2.WebStatusProduct))) = 'active'
        THEN 'true'
        ELSE 'false'
    END                                         AS "product_active",
    STUFF((
      SELECT
      CONVERT(VARCHAR, LTRIM(RTRIM(ps.SupCatalogueNum))) + ','
      from PorSupStkInfo ps WITH (nolock)
      INNER JOIN InvMaster inm WITH (NOLOCK)
           ON inm.StockCode = ps.StockCode
      INNER JOIN [InvMaster+] inm2 WITH (NOLOCK)
           ON inm.StockCode = inm2.StockCode
      WHERE inm2.WebProductCode = inv2.WebProductCode
            AND LTRIM(RTRIM(lower(inm2.WebStatusProduct))) = 'active'
            AND ISNULL(inm2.WebDescription, '') <> ''
      ORDER BY LTRIM(RTRIM(inv.StockCode))
    for xml path('')),1,1,'')										    AS "tags",

    -- Prices
    'A'                                         AS "default_price_tier",
    STUFF((
      SELECT
        ',' + CONVERT(VARCHAR, LTRIM(RTRIM(price_tiers.PriceCode))) + '|' +
              CONVERT(VARCHAR, CAST(ISNULL(price_tiers.SellingPrice, 0) AS FLOAT))
      FROM InvPrice price_tiers WITH (NOLOCK)
      WHERE price_tiers.StockCode = inv.StockCode
      ORDER BY LTRIM(RTRIM(price_tiers.PriceCode))
    for xml path('')),1,1,'')
                                                      AS "csv_price_tiers",

    -- Quantities
    '51'											  AS "default_qty_availability",
    STUFF((
      SELECT
        ',' + CONVERT(VARCHAR, LTRIM(RTRIM(whse.Warehouse))) + '|' +
              CONVERT(VARCHAR, CONVERT(INTEGER, COALESCE(whse.QtyOnHand, 0) - COALESCE(whse.QtyAllocated, 0)))
      FROM InvWarehouse whse WITH (NOLOCK)
      WHERE whse.StockCode = inv.StockCode
      ORDER BY LTRIM(RTRIM(whse.Warehouse))
    for xml path('')),1,1,'')
                                                      AS "csv_qty_availability",
	inv.ProductClass								  AS "meta_product_class",
	(select top 1 ps.SupCatalogueNum
	    from PorSupStkInfo ps WITH (nolock)
    Where ps.StockCode = inv.StockCode)               AS "meta_supplier_code",

	REPLACE(REPLACE(inv.StorageHazard, 'DG ',''),'', 'N/A')
	                                                  AS "meta_storage_hazard",
    inv.StockUom                                      AS "meta_uom",
    inv2.PreludeSku                                   AS "meta_prelude_code",
     STUFF((
      SELECT
        ',' + CONVERT(VARCHAR, LTRIM(RTRIM(inv3.StockCode))) + '|' + CONVERT(VARCHAR, (inv.StockUom))
      FROM InvMaster inv3 WITH (NOLOCK)
	  WHERE inv3.StockCode = inv.StockCode
      ORDER BY LTRIM(RTRIM(inv.StockCode))
    for xml path('')),1,1,'')                         AS "csv_uom",

    (select top 1 sor.FixedUom
        from SorContractPrice sor WITH (NOLOCK)
            WHERE sor.StockCode = inv.StockCode)      AS "meta_fixed_uom",

    CASE WHEN
       ISNULL(inv.StorageHazard,'') = ''
        THEN 'false'
        ELSE 'true'
        END
	                                                  AS "meta_dangerous_goods",

    -- Segments
	CASE
	    WHEN RTRIM(LOWER(inv2.Brand)) = 'poolwerx' OR
	         RTRIM(LOWER(inv2.Brand)) = 'clark' OR
	         RTRIM(LOWER(inv2.Brand)) = 'filtrite' OR
	         RTRIM(LOWER(inv2.Brand)) = 'swimart'
	        THEN 'false'
	    ELSE 'true'
	    END
	                                                  AS "meta_segA",
	CASE
	    WHEN RTRIM(LOWER(inv2.Brand)) = 'clark' OR
	         RTRIM(LOWER(inv2.Brand)) = 'filtrite' OR
	         RTRIM(LOWER(inv2.Brand)) = 'swimart'
	        THEN 'false'
	    ELSE 'true'
	    END
	                                                  AS "meta_segB",
	CASE
	    WHEN RTRIM(LOWER(inv2.Brand)) = 'poolwerx' OR
	         RTRIM(LOWER(inv2.Brand)) = 'swimart'
	        THEN 'false'
	    ELSE 'true'
	    END
	                                                  AS "meta_segC",
	CASE
	    WHEN RTRIM(LOWER(inv2.Brand)) = 'poolwerx' OR
	         RTRIM(LOWER(inv2.Brand)) = 'clark' OR
	         RTRIM(LOWER(inv2.Brand)) = 'filtrite'
	        THEN 'false'
	    ELSE 'true'
	    END
	                                                  AS "meta_segD",

       -- Contracts
    -- Fixed Pricing
    STUFF((
      SELECT
        ',' + CONVERT(VARCHAR, LTRIM(RTRIM(sor.CustomerBuyGrp))) + '_' + CONVERT(VARCHAR, LTRIM(RTRIM(sor.StockCode))) + '|' + CONVERT(VARCHAR, COALESCE(sor.FixedPrice, 0))
      FROM SorContractPrice sor WITH (NOLOCK)
      INNER JOIN InvMaster inm WITH (nolock)
           ON inm.StockCode = sor.StockCode
      INNER JOIN [InvMaster+] inm2 WITH (nolock)
           ON inm.StockCode = inm2.StockCode
      WHERE inm2.WebProductCode = inv2.WebProductCode
      AND LTRIM(RTRIM(lower(inm2.WebStatusProduct))) = 'active'
      AND ISNULL(inm2.WebDescription, '') <> ''
      AND sor.ContractType='B'
      AND sor.PriceMethod='F'
      AND (DATEADD(day, DATEDIFF(day,0,GETDATE()),0) <= ExpiryDate or ExpiryDate is NULL )
      AND DATEADD(day, DATEDIFF(day,0,GETDATE()),0) >= StartDate
      ORDER BY LTRIM(RTRIM(sor.CustomerBuyGrp))
    for xml path('')),1,1,'')
                                                      AS "csv_discount_groups_fixed",
    -- Create two new contracts as per the below
    -- Of all three contracts system must use the lowest price , however must use contract and not list (Only revert to list when no contracts)
    -- List less percent
    STUFF((
      SELECT
      ',' + CONVERT(VARCHAR, LTRIM(RTRIM(sor.CustomerBuyGrp))) + '_' + CONVERT(VARCHAR, LTRIM(RTRIM(sor.StockCode))) + '|' +
      CONVERT(VARCHAR, COALESCE(sor.Discount1, 0))
      FROM SorContractPrice sor WITH (NOLOCK)
      INNER JOIN InvMaster inm WITH (nolock)
           ON inm.StockCode = sor.StockCode
      INNER JOIN [InvMaster+] inm2 WITH (nolock)
           ON inm.StockCode = inm2.StockCode
      WHERE inm2.WebProductCode = inv2.WebProductCode
      AND LTRIM(RTRIM(lower(inm2.WebStatusProduct))) = 'active'
      AND ISNULL(inm2.WebDescription, '') <> ''
      AND sor.ContractType='C'
      AND sor.PriceMethod ='L'
      AND DATEADD(day, DATEDIFF(day,0,GETDATE()),0) <= ExpiryDate or ExpiryDate is NULL
      AND DATEADD(day, DATEDIFF(day,0,GETDATE()),0) >= StartDate
      ORDER BY LTRIM(RTRIM(sor.StockCode))
    for xml path('')),1,1,'')
                                             AS "csv_customer_percent",

       -- Customer per product fixed
    STUFF((
      SELECT
      ',' + CONVERT(VARCHAR, LTRIM(RTRIM(sor.CustomerBuyGrp))) + '_' + CONVERT(VARCHAR, LTRIM(RTRIM(sor.StockCode))) + '|' +
      CONVERT(VARCHAR, COALESCE(sor.FixedPrice, 0))
      FROM SorContractPrice sor WITH (NOLOCK)
      INNER JOIN InvMaster inm WITH (nolock)
           ON inm.StockCode = sor.StockCode
      INNER JOIN [InvMaster+] inm2 WITH (nolock)
           ON inm.StockCode = inm2.StockCode
      WHERE inm2.WebProductCode = inv2.WebProductCode
      AND LTRIM(RTRIM(lower(inm2.WebStatusProduct))) = 'active'
      AND ISNULL(inm2.WebDescription, '') <> ''
      AND sor.ContractType='C'
      AND sor.PriceMethod = 'F'
      AND DATEADD(day, DATEDIFF(day,0,GETDATE()),0) <= ExpiryDate or ExpiryDate is NULL
      AND DATEADD(day, DATEDIFF(day,0,GETDATE()),0) >= StartDate
      ORDER BY LTRIM(RTRIM(sor.StockCode))
    for xml path('')),1,1,'')
                                             AS "csv_customer_fixed",
    -- Customer Mark-up price discount (K)
    STUFF((
      SELECT
      ',' + CONVERT(VARCHAR, LTRIM(RTRIM(sor.CustomerBuyGrp))) + '_' + CONVERT(VARCHAR, LTRIM(RTRIM(sor.StockCode))) + '|' +
      CONVERT(VARCHAR, COALESCE(sor.Discount1, 0))
      FROM SorContractPrice sor WITH (NOLOCK)
      INNER JOIN InvMaster inm WITH (NOLOCK)
           ON inm.StockCode = sor.StockCode
      INNER JOIN [InvMaster+] inm2 WITH (NOLOCK)
           ON inm.StockCode = inm2.StockCode
      WHERE inm2.WebProductCode = inv2.WebProductCode
      AND LTRIM(RTRIM(lower(inm2.WebStatusProduct))) = 'active'
      AND ISNULL(inm2.WebDescription, '') <> ''
      AND sor.ContractType = 'B'
      AND sor.PriceMethod = 'K'
      AND DATEADD(day, DATEDIFF(day,0,GETDATE()),0) <= ExpiryDate or ExpiryDate is NULL
      AND DATEADD(day, DATEDIFF(day,0,GETDATE()),0) >= StartDate
      ORDER BY LTRIM(RTRIM(sor.StockCode))
    for xml path('')),1,1,'')
                                              AS "csv_customer_markup_percentage",
	STUFF((
      SELECT
      CONVERT(VARCHAR, LTRIM(RTRIM(ps.SupCatalogueNum))) + ' | '
      from PorSupStkInfo ps WITH (nolock)
      INNER JOIN InvMaster inm WITH (NOLOCK)
           ON inm.StockCode = ps.StockCode
      INNER JOIN [InvMaster+] inm2 WITH (NOLOCK)
           ON inm.StockCode = inm2.StockCode
      WHERE inm2.WebProductCode = inv2.WebProductCode
            AND LTRIM(RTRIM(lower(inm2.WebStatusProduct))) = 'active'
            AND ISNULL(inm2.WebDescription, '') <> ''
      ORDER BY LTRIM(RTRIM(inv.StockCode))
    for xml path('')),1,1,'')                       as "meta_supplier_codes"


FROM InvMaster inv WITH (NOLOCK)

    -- Additonal Data
    LEFT JOIN SalProductClassDes pc WITH (nolock)
    ON inv.ProductClass = pc.ProductClass

    -- Base Data
    LEFT JOIN [InvMaster+] inv2 WITH (nolock)
    ON inv.StockCode = inv2.StockCode

    -- Supplier codes
    LEFT JOIN PorSupStkInfo pss WITH (nolock)
    ON inv.StockCode = pss.StockCode

WHERE LTRIM(RTRIM(lower(inv2.WebStatusProduct))) in ('active', 'delete')
    AND ISNULL(RTRIM(inv2.WebProductCode), '') <> ''
    AND ISNULL(inv2.WebDescription, '') <> ''
    AND RTRIM(inv.StockCode) = '%(source_variant_code)s'
    ;