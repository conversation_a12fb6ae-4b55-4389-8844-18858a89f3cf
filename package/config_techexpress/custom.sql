SELECT *
FROM (
SELECT
    row_number() OVER (ORDER BY inv.ITEMNO)           AS n,

    RTRIM(LTRIM(inv.ITEMNO))                          AS "source_product_code",
    RTRIM(LTRIM(inv.ITEMNO))                          AS "source_variant_code",
    RTRIM(LTRIM(inv.[DESC]))                          AS "title",
    (select LTRIM(RTRIM(ICCATG.[DESC])) from ICCATG where ICCATG.CATEGORY = inv.CATEGORY)
                                                      AS "collection",
	  ''                                                AS "product_type",
    (select LTRIM(RTRIM(CONVERT(VARCHAR, ICITEMO.[VALUE]))) from ICITEMO where ICITEMO.ITEMNO = inv.ITEMNO and LTRIM(RTRIM(ICITEMO.OPTFIELD)) = 'Brand')
                                                      AS "vendor",
    RTRIM(LTRIM(inv.ITEMNO))                          AS "variants.sku",
    ''                                                AS "body_html",
    0                                                 AS "variants.weight",
    ''                                                AS "variants.barcode",
    'true'                                            AS "variants.inventory_management",

    CASE WHEN (inv.ALLOWONWEB = 1)
    THEN 'true'
    ELSE 'false'
    END
                                                      AS "product_active",
    ''                                                AS "tags",

    -- Prices
	  -- https://smist08.wordpress.com/2011/08/20/tables-and-data-flow-of-the-accpac-inventory-control-module-part-1/
	  -- ICPRIC = price lists
	  -- ICPRICP = price per unit
    'DEFAUL'                                          AS "default_price_tier",
    STUFF((
      SELECT
        ',' + CONVERT(VARCHAR, LTRIM(RTRIM(pr.PRICELIST))) + '|' +
              CONVERT(VARCHAR, CAST(ISNULL(pr.UNITPRICE, 0)*1.15 AS FLOAT))
      FROM ICPRICP pr WITH (NOLOCK)
      WHERE pr.ITEMNO = inv.ITEMNO and pr.DPRICETYPE=1
      ORDER BY LTRIM(RTRIM(pr.PRICELIST))
    for xml path('')),1,1,'')
                                                      AS "csv_price_tiers",

	  -- QTY
	  -- https://smist08.wordpress.com/2011/08/20/tables-and-data-flow-of-the-accpac-inventory-control-module-part-1/
	  -- ICLOC = locations
	  -- ICILOC = item qty per loc
    'TechExpress (Pty) Ltd'                          AS "default_qty_availability",
    STUFF((
      SELECT
        ',' + CONVERT(VARCHAR, LTRIM(RTRIM(ic.[DESC]))) + '|' +
              CONVERT(VARCHAR,
              CASE WHEN CONVERT(INTEGER, COALESCE(icl.QTYONHAND , 0) - COALESCE(icl.QTYSALORDR, 0)) < 0
                THEN 0
                ELSE CONVERT(INTEGER, COALESCE(icl.QTYONHAND , 0) - COALESCE(icl.QTYSALORDR, 0))
              END
              )
     FROM ICILOC icl WITH (NOLOCK)
	   INNER JOIN ICLOC ic on ic.LOCATION = icl.LOCATION
      WHERE icl.ITEMNO = inv.ITEMNO
      ORDER BY LTRIM(RTRIM(ic.[DESC]))
    for xml path('')),1,1,'')
                                                      AS "csv_qty_availability"

FROM ICITEM inv WITH (NOLOCK)

) as rows
where
n > '0'
and n <= '1000'