select
  cast(i.InvoiceId as nvarchar)                  as 'source_order_code',
  cast(c.<PERSON>erId as nvarchar)                 as 'source_client_code',
  ''                                             as 'notes1',
  ''                                             as 'notes2',
  ''                                             as 'notes3',
  c.<PERSON><PERSON><PERSON>                                    as 'customer.first_name',
  c.<PERSON><PERSON>                                     as 'customer.last_name',
  c.<PERSON><PERSON>                                        as 'customer.email',

  i.<PERSON><PERSON><PERSON>dd<PERSON>                               as 'billing_address.address1',
  ''                                             as 'billing_address.address2',
  i.BillingCity                                  as 'billing_address.city',
  ''                                             as 'billing_address.company',
  i.BillingCountry                               as 'billing_address.country',
  'ZA'                                           as 'billing_address.country_code',
  c.<PERSON><PERSON>ame      as 'billing_address.first_name',
  c.LastName   as 'billing_address.last_name',
  c.Phone                                        as 'billing_address.phone',
  i.BillingState                                 as 'billing_address.province',
  i.BillingPostalCode                            as 'billing_address.zip',

  i.BillingAddress                               as 'shipping_address.address1',
  ''                                             as 'shipping_address.address2',
  i.Billing<PERSON>ity                                  as 'shipping_address.city',
  ''                                             as 'shipping_address.company',
  i.<PERSON><PERSON>ountry                               as 'shipping_address.country',
  'ZA'                                           as 'shipping_address.country_code',
  c.<PERSON>Name                                    as 'shipping_address.first_name',
  c.LastName                                     as 'shipping_address.last_name',
  c.Phone                                        as 'shipping_address.phone',
  i.BillingState                                 as 'shipping_address.province',
  i.BillingPostalCode                            as 'shipping_address.zip',

  cast(il.TrackId as nvarchar)                   as 'line_item.sku',
  t.Name                                         as 'line_item.title',
  cast(il.UnitPrice as float)                    as 'line_item.price',
  il.Quantity                                    as 'line_item.qty',
  'item'                                         as 'line_item.code',
  cast(round((il.UnitPrice * il.Quantity) * 14/100, 2) as float)
                                                 as 'tax_line.price',
  '14'                                           as 'tax_line.rate',
  'VAT'                                          as 'tax_line.title',
  'taxed'                                        as 'tax_line.code'
from Invoice i
join InvoiceLine il on i.InvoiceId = il.InvoiceId
join Customer c on c.CustomerId = i.CustomerId
join Track t on il.TrackId = t.TrackId
where i.InvoiceId = '%(source_order_code)s';