SELECT
  st.Code                                                              AS "source_product_code",
  st.Code                                                              AS "source_variant_code",
  st.Description_1                                                     AS "title",
  (
    SELECT top 1 grp.Description
    FROM GrpTbl grp WITH (NOLOCK)
    INNER JOIN _etblStockDetails sd WITH (NOLOCK)
        ON sd.StockID = st.StockLink
    AND grp.idGrpTbl = sd.GroupID
    AND sd.WhseID = '-1'
    )                                                                  AS "collection",
  ''                                                                   AS "product_type",
  ''                                                                   AS "vendor",
  st.Code                                                              AS "variants.sku",
  ''                                                                   AS "variants.option1",
  ''                                                                   AS "variants.option2",
  ''                                                                   AS "variants.option3",
  ''                                                                   AS "body_html",
  0                                                                    AS "variants.weight",
  ''                                                                   AS "variants.barcode",
  'true'                                                               AS "variants.inventory_management",
    case
        when rtrim(ltrim(st.ucIIUser_defined_1)) = '1' then 'true'
        else 'false'
    end                                                                AS "product_active",
  ''                                                                   AS "tags",

  'Price List 1'                                                                  AS "default_price_tier",
  stuff(
    (select
      ',' + convert(varchar, price_names.cName) + '|' +
            convert(varchar, price.fExclPrice)
    from dbo._etblPriceListPrices price WITH (NOLOCK)

      inner join _etblPriceListName price_names WITH (NOLOCK)
        ON price.iPriceListNameID = price_names.IDPriceListName AND price.iStockID = st.StockLink and price.iWarehouseID = 0

    order by price_names.cName
    for xml path('')),1,1,'')
                                                                       AS "csv_price_tiers",


  'Mstr'                                                               AS "default_qty_availability",
  STUFF((
    SELECT
      CONVERT(VARCHAR, whse_mst.Code) + '|' +
      CONVERT(VARCHAR, ISNULL(sq.QtyOnHand - sq.QtyOnSO, 0)) + ','
    FROM _etblStockQtys sq WITH (NOLOCK)

      LEFT JOIN WhseMst whse_mst WITH (NOLOCK)
        ON whse_mst.WhseLink = sq.WhseID
    WHERE sq.StockID = st.StockLink
    ORDER BY whse_mst.Code
    for xml path('')),1,0,'')
                                                                      AS "csv_qty_availability",

  -- List of images for this product
  -- Enables automatic pulling of new images when image is added
  ISNULL(
  STUFF(
    (SELECT ',' + convert(varchar, i.idInvImage)
    FROM _etblInvImages i WITH (NOLOCK)
    WHERE st.StockLink = i.iStockLink
    ORDER BY i.idInvImage
    FOR XML PATH ('')), 1, 1, ''), '')
                                                                       AS "meta_image_id_csv",
   tr.TaxRate                                                          AS "meta_tax_rate",
   st.ufIIPriority                                                     AS "meta_line_item_order",
   ISNULL(RTRIM(LTRIM(st.ucIIUser_defined_3)), 1)                      AS "meta_qty_multiples_of"

FROM StkItem st WITH (NOLOCK)

  -- TAX
  LEFT JOIN _etblStockDetails sd WITH (NOLOCK)
	                             ON sd.StockID = st.StockLink
	                             AND sd.WhseID = '-1'

  LEFT JOIN TaxRate tr WITH (NOLOCK)
                       ON tr.idTaxRate = sd.TTInvID

WHERE rtrim(ltrim(st.ucIIUser_defined_1)) in ('1', '2')
    AND st.Code = '%(source_variant_code)s'
;