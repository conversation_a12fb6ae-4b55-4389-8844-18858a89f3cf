REM Apifact Connector
c:\apifact\bin\nssm\win32\nssm.exe install "Apifact Connector" "c:\apifact\connector\connector.exe"

c:\apifact\bin\nssm\win32\nssm.exe set  "Apifact Connector" AppDirectory c:\apifact\connector\
c:\apifact\bin\nssm\win32\nssm.exe set  "Apifact Connector" AppParameters "-config=c:\apifact\connector\config.json"
c:\apifact\bin\nssm\win32\nssm.exe set  "Apifact Connector" AppStderr "c:\apifact\connector.log"
c:\apifact\bin\nssm\win32\nssm.exe set  "Apifact Connector" AppStdout "c:\apifact\connector.log"

REM Apifact Tunnel
c:\apifact\bin\nssm\win32\nssm.exe install "Apifact Tunnel" "c:\apifact\bin\tunnel\windows_386\ngrok.exe"

c:\apifact\bin\nssm\win32\nssm.exe set  "Apifact Tunnel" AppDirectory c:\apifact\
c:\apifact\bin\nssm\win32\nssm.exe set  "Apifact Tunnel" AppParameters "-config=c:\apifact\connector\ngrok-config -log=stdout start testserver"
c:\apifact\bin\nssm\win32\nssm.exe set  "Apifact Tunnel" AppStderr "c:\apifact\tunnel.log"
c:\apifact\bin\nssm\win32\nssm.exe set  "Apifact Tunnel" AppStdout "c:\apifact\tunnel.log"

REM Rotate tunnel log file bigger than 1MB. Rotate files while the service is running
c:\apifact\bin\nssm\win32\nssm.exe set  "Apifact Connector" AppRotateBytes 1048576
c:\apifact\bin\nssm\win32\nssm.exe set  "Apifact Connector" AppRotateFiles 1
c:\apifact\bin\nssm\win32\nssm.exe set  "Apifact Connector" AppRotateOnline 1
c:\apifact\bin\nssm\win32\nssm.exe set  "Apifact Tunnel" AppRotateBytes 1048576
c:\apifact\bin\nssm\win32\nssm.exe set  "Apifact Tunnel" AppRotateFiles 1
c:\apifact\bin\nssm\win32\nssm.exe set  "Apifact Tunnel" AppRotateOnline 1

REM Create tunnel config
c:\apifact\connector\cli.exe ngrok-config -c c:\apifact\connector\config.json