select

    p.MainCode                                 as "source_product_code",
    p.VariantCode                              as "source_variant_code",
    p.Title                                    as "title",
    p.Collection                               as "collection",
    p.ProductType                              as "product_type",
    p<PERSON><PERSON><PERSON>                                   as "vendor",
    ISNULL(p.Option1Name, '')                  as "variants.option1_name",
    ISNULL(p.Option1Value, '')                 as "variants.option1_value",
    ISNULL(p.Option2Name, '')                  as "variants.option2_name",
    ISNULL(p.Option2Value, '')                 as "variants.option2_value",
    ISNULL(p.Option3Name, '')                  as "variants.option3_name",
    ISNULL(p.Option3Value, '')                 as "variants.option3_value",
    p.SKU                                      as "variants.sku",
    ''                                         as "body_html",
    p.Weight                                   as "variants.weight",
    p.Barcode                                  as "variants.barcode",
    p.Qty                                      as "variants.qty",
    p.Price                                    as "variants.retail_price",
    'true'                                     as "variants.inventory_management",
    case when Active=1
             then 'true'
         else 'false'
        end                                        as "product_active",
    'Apifact - test.devan'                     as "meta_source",
    ''                                         as "tags"

from testdevan_products p with (nolock)
where p.Active in (1, 2)
  and p.MainCode <> ''
  and p.variantcode = '%(source_variant_code)s'
;