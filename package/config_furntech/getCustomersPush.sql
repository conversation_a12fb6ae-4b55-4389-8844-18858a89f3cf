select *
from (
SELECT
    row_number() over (order by da.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) AS n,

    convert(varchar, cl.Account)   AS 'source_customer_code',

    CASE
        WHEN CHARINDEX(' ', cl.Contact_Person) > 0 THEN RTRIM(ISNULL(LEFT(cl.Contact_Person, CHARINDEX(' ', cl.Contact_Person) - 1), ''))
        ELSE RTRIM(ISNULL(cl.Contact_Person, ''))
    END                            AS 'first_name',

    CASE
        WHEN CHARINDEX(' ', cl.Contact_Person) > 0 THEN LTRIM(RTRIM(ISNULL(RIGHT(cl.Contact_Person, LEN(cl.Contact_Person) - CHARINDEX(' ', cl.Contact_Person) + 1), '')))
        ELSE ''
    END                            AS 'last_name',

    RTRIM(ISNULL(da.cEmail, ''))
                                   AS 'email',

    CASE
        WHEN LOWER(RTRIM(LTRIM(cl.Ularwebstatus))) = 'active' THEN 'true'
        ELSE 'false'
    END                            AS 'customer_active',

    0                              AS 'accepts_marketing',
    cl.Physical1                   AS 'address.address1', -- or Address?
    cl.Physical2                   AS 'address.address2',
    cl.Physical3                   AS 'address.city',
    ''                             AS 'address.country',
    'ZA'                           AS 'address.country_code',
    ''                             AS 'address.province',
    ''                             AS 'address.province_code',
    cl.PhysicalPC                  AS 'address.zip',
    cl.Name                        AS 'address.company',
    cl.Telephone                   AS 'address.phone',
    cl.Credit_Limit                AS 'meta_credit_limit',
    cl.DCBalance                   AS 'meta_balance',
    ''                             AS 'meta_branch',
    (
      SELECT ar.Code
      FROM Areas ar WITH (NOLOCK)
      WHERE ar.idAreas = cl.iAreasID
    )
                                   AS 'meta_warehouse',
    (
      SELECT sr.Code
      FROM SalesRep sr WITH (NOLOCK)
      WHERE sr.idSalesRep = cl.RepID
    )
                                   AS 'meta_sales_rep',
    (
      SELECT pn.cName
      FROM _etblPriceListName pn WITH (NOLOCK)
      WHERE pn.IDPriceListName = cl.iARPriceListNameID
    )
                                   AS 'price_tier',
    'Main'                         AS 'qty_availability',
   LOWER(RTRIM(LTRIM(cl.Ularwebstatus)))
                                   AS 'meta_b2b_active'

FROM Client cl WITH (NOLOCK)
INNER JOIN _etblDelAddress da WITH (NOLOCK) ON da.iAccountID = cl.DCLink
WHERE RTRIM(ISNULL(da.cEmail, '')) <> '' AND LOWER(RTRIM(LTRIM(cl.Ularwebstatus))) IN ('active', 'delete')


) as rows
where
n > '%(audit_lower_limit)n'
and n <= '%(audit_upper_limit)n'