select *
from (
         select
             row_number()
             over (
                 order by idInvImage) as n,
             count(*)
             over ()                  as 'total_rows',
             i.idInvImage             as 'id_inv_image',
             i.nInvImage              as 'n_inv_image',
             st.StockLink             as 'source_variant_code',
             i.cInvImageType          as 'c_inv_image_type',
             i.cInvImageDesc          as 'c_inv_image_desc'
         from StkItem st
             join _etblInvImages i on st.StockLink = i.iStockLink
         where st.StockLink = '%(source_variant_code)n'
               and i.cInvImageType != 'TBitmap'
               and datalength(i.nInvImage) <= 1024 * 1024
     ) as rows
where
    n > '%(lower_limit)n'
    and n <= '%(upper_limit)n'