SELECT
    rtrim(i.ItemCode)         AS "source_product_code",
    rtrim(i.ItemCode)         AS "source_variant_code",
    rtrim(i.Description)      AS "title",
    rtrim(i.ItemCode)         AS "variants.sku",
    rtrim(ic.ICDesc)          AS "collection",
    ''                        AS "product_type",
    rtrim(ig.Description)     AS "vendor",
    ''                        AS "variants.option1",
    ''                        AS "variants.option2",
    ''                        AS "variants.option3",
    ''                        AS "body_html",
    0                         AS "variants.weight",
    ''                        AS "variants.barcode",

    -- Warehouse 1 qty
    (
      (
        (wh1.OpeningQty + wh1.QtyBuyLast + wh1.QtyAdjustLast - wh1.QtySellLast) +
        (wh1.QtyBuyThis01 + wh1.QtyBuyThis02 + wh1.QtyBuyThis03 + wh1.QtyBuyThis04 +
            wh1.QtyBuyThis05 + wh1.QtyBuyThis06 + wh1.QtyBuyThis07 + wh1.QtyBuyThis08 +
            wh1.QtyBuyThis09 + wh1.QtyBuyThis10 + wh1.QtyBuyThis11 + wh1.QtyBuyThis12 +
            wh1.QtyBuyThis13) +
        (wh1.QtyAdjustThis01 + wh1.QtyAdjustThis02 + wh1.QtyAdjustThis03 +
            wh1.QtyAdjustThis04 + wh1.QtyAdjustThis05 + wh1.QtyAdjustThis06 +
            wh1.QtyAdjustThis07 + wh1.QtyAdjustThis08 + wh1.QtyAdjustThis09 +
            wh1.QtyAdjustThis10 + wh1.QtyAdjustThis11 + wh1.QtyAdjustThis12 +
            wh1.QtyAdjustThis13) -
        (wh1.QtySellThis01 + wh1.QtySellThis02 + wh1.QtySellThis03 + wh1.QtySellThis04 +
            wh1.QtySellThis05 + wh1.QtySellThis06 + wh1.QtySellThis07 + wh1.QtySellThis08 +
            wh1.QtySellThis09 + wh1.QtySellThis10 + wh1.QtySellThis11 + wh1.QtySellThis12 +
            wh1.QtySellThis13)
      )
    )                         as "qty_on_hand",
    (
      select
        sum(QtyLeft)

      from HistoryLines hl where
        hl.Documenttype = 102 and
        hl.ItemCode = i.ItemCode and
        hl.MultiStore = wh1.StoreCode
    )                         as "qty_on_sales_order",
    -- Warehouse 2 qty
    (
      (
        (wh2.OpeningQty + wh2.QtyBuyLast + wh2.QtyAdjustLast - wh2.QtySellLast) +
        (wh2.QtyBuyThis01 + wh2.QtyBuyThis02 + wh2.QtyBuyThis03 + wh2.QtyBuyThis04 +
            wh2.QtyBuyThis05 + wh2.QtyBuyThis06 + wh2.QtyBuyThis07 + wh2.QtyBuyThis08 +
            wh2.QtyBuyThis09 + wh2.QtyBuyThis10 + wh2.QtyBuyThis11 + wh2.QtyBuyThis12 +
            wh2.QtyBuyThis13) +
        (wh2.QtyAdjustThis01 + wh2.QtyAdjustThis02 + wh2.QtyAdjustThis03 +
            wh2.QtyAdjustThis04 + wh2.QtyAdjustThis05 + wh2.QtyAdjustThis06 +
            wh2.QtyAdjustThis07 + wh2.QtyAdjustThis08 + wh2.QtyAdjustThis09 +
            wh2.QtyAdjustThis10 + wh2.QtyAdjustThis11 + wh2.QtyAdjustThis12 +
            wh2.QtyAdjustThis13) -
        (wh2.QtySellThis01 + wh2.QtySellThis02 + wh2.QtySellThis03 + wh2.QtySellThis04 +
            wh2.QtySellThis05 + wh2.QtySellThis06 + wh2.QtySellThis07 + wh2.QtySellThis08 +
            wh2.QtySellThis09 + wh2.QtySellThis10 + wh2.QtySellThis11 + wh2.QtySellThis12 +
            wh2.QtySellThis13)
      )
    )                         as "qty_on_hand_2",
    (
      select
        sum(QtyLeft)

      from HistoryLines hl where
        hl.Documenttype = 102 and
        hl.ItemCode = i.ItemCode and
        hl.MultiStore = wh2.StoreCode
    )                         as "qty_on_sales_order_2",

    -- Batches
--    -(ISNULL(U.SalesOrder, 0) + ISNULL(U.BatchQty, 0))
--                              as "qty_on_sales_order",

    -- Note that price can be set by warehouse (store)
    '01'                      as "default_price_tier",
    (
    '01|' + REPLACE(CAST(wh1.SellExcl01 AS VARCHAR(30)), ',', '.') + ',' +
    '02|' + REPLACE(CAST(wh1.SellExcl02 AS VARCHAR(30)), ',', '.') + ',' +
    '03|' + REPLACE(CAST(wh1.SellExcl03 AS VARCHAR(30)), ',', '.') + ',' +
    '04|' + REPLACE(CAST(wh1.SellExcl04 AS VARCHAR(30)), ',', '.') + ',' +
    '05|' + REPLACE(CAST(wh1.SellExcl05 AS VARCHAR(30)), ',', '.') + ',' +
    '06|' + REPLACE(CAST(wh1.SellExcl06 AS VARCHAR(30)), ',', '.') + ',' +
    '07|' + REPLACE(CAST(wh1.SellExcl07 AS VARCHAR(30)), ',', '.') + ',' +
    '08|' + REPLACE(CAST(wh1.SellExcl08 AS VARCHAR(30)), ',', '.') + ',' +
    '09|' + REPLACE(CAST(wh1.SellExcl09 AS VARCHAR(30)), ',', '.') + ',' +
    '10|' + REPLACE(CAST(wh1.SellExcl10 AS VARCHAR(30)), ',', '.') + ',' +
    'special|' + REPLACE(CAST(wh1.SpecialPriceExcl AS VARCHAR(30)), ',', '.'))
                              as "csv_price_tiers",
    case
        when i.UserDefNum03 = 1 then 'true'
        else 'false'
    end                       as "product_active",
    ''                        as "tags",
    'true'                    as "variants.inventory_management",
    i.Picture                 as "picture",
    rtrim(i.UserDefNum01)     as "meta_user_def_num_1",
    rtrim(i.UserDefNum02)     as "meta_user_def_num_2",
    rtrim(i.UserDefNum03)     as "meta_user_def_num_3",
    rtrim(i.UserDefText01)    as "meta_user_def_text_1",
    rtrim(i.UserDefText02)    as "meta_user_def_text_2",
    rtrim(i.UserDefText03)    as "meta_user_def_text_3"

FROM Inventory i

    -- Warehouse 1
    left join MultiStoreTrn wh1 on
      wh1.ItemCode = i.ItemCode and
      wh1.StoreCode = '%(multi_store)s'

    -- Warehouse 2
    left join MultiStoreTrn wh2 on
      wh2.ItemCode = i.ItemCode and
      wh2.StoreCode = '002'

    left join InventoryGroups ig on
      ig.InvGroup = wh1.InvGroup

    -- Attribute info
    left join InventoryCategory ic on
      i.Category = ic.ICCode

    -- Batches
--    LEFT JOIN Unposted U on
--      i.ItemCode = U.ItemCode AND '%(multi_store)s' = U.StoreCode

WHERE i.Physical = 1 AND wh1.StoreCode = '%(multi_store)s'
    AND rtrim(i.ItemCode) = '%(source_variant_code)s'
    AND rtrim(i.UserDefNum03) in (1, 2)
;