SELECT
    rtrim(i.UserDefText01)    AS "source_product_code",
    rtrim(i.GUID)             AS "source_variant_code",
    rtrim(i.UserDefText01)    AS "title",
    rtrim(i.ItemCode)         AS "variants.sku",
    rtrim(ig.Description)     AS "collection",
    ''                        AS "product_type",
    rtrim(ic.ICDesc)          AS "vendor",
    'Size'                    AS "variants.option1_name",
    RTRIM(i.UserDefText02)    AS "variants.option1_value",
    'Colour'                  AS "variants.option2_name",
    RTRIM(i.UserDefText03)    AS "variants.option2_value",
    ''                        AS "variants.option3",
    ''                        AS "body_html",
    rtrim(i.NettMass)         AS "variants.weight",
    rtrim(i.Barcode)          AS "variants.barcode",

    -- Warehouse 1 qty
    (
      (
        (wh1.OpeningQty + wh1.QtyBuyLast + wh1.QtyAdjustLast - wh1.QtySellLast) +
        (wh1.QtyBuyThis01 + wh1.QtyBuyThis02 + wh1.QtyBuyThis03 + wh1.QtyBuyThis04 +
            wh1.QtyBuyThis05 + wh1.QtyBuyThis06 + wh1.QtyBuyThis07 + wh1.QtyBuyThis08 +
            wh1.QtyBuyThis09 + wh1.QtyBuyThis10 + wh1.QtyBuyThis11 + wh1.QtyBuyThis12 +
            wh1.QtyBuyThis13) +
        (wh1.QtyAdjustThis01 + wh1.QtyAdjustThis02 + wh1.QtyAdjustThis03 +
            wh1.QtyAdjustThis04 + wh1.QtyAdjustThis05 + wh1.QtyAdjustThis06 +
            wh1.QtyAdjustThis07 + wh1.QtyAdjustThis08 + wh1.QtyAdjustThis09 +
            wh1.QtyAdjustThis10 + wh1.QtyAdjustThis11 + wh1.QtyAdjustThis12 +
            wh1.QtyAdjustThis13) -
        (wh1.QtySellThis01 + wh1.QtySellThis02 + wh1.QtySellThis03 + wh1.QtySellThis04 +
            wh1.QtySellThis05 + wh1.QtySellThis06 + wh1.QtySellThis07 + wh1.QtySellThis08 +
            wh1.QtySellThis09 + wh1.QtySellThis10 + wh1.QtySellThis11 + wh1.QtySellThis12 +
            wh1.QtySellThis13)
      )
    )                         as "qty_on_hand",
    (
      select sum(QtyLeft)
      from HistoryLines where
        Documenttype = 102 and
        ItemCode = i.ItemCode and
        MultiStore = wh1.StoreCode
    )                         as "qty_on_sales_order",

    -- Warehouse 1 prices
    -- Note that price can be set by warehouse (store)
    wh1.SellExcl03            as "variants.retail_price",
    0                         as "variants.dealer_price",
    0                         as "variants.distribution_price",
    wh1.SellExcl02            as "variants.wholesale_price",
    wh1.SellExcl01            as "variants.unknown1_price",
    wh1.SellExcl04            as "variants.unknown2_price",

    case
        when i.Blocked = 1 then 'false'
        else 'true'
    end                       as "product_active",
    ''                        as "tags",
    rtrim(ig.Description)     as "inventory_group",
    'true'                    as "variants.inventory_management",
    i.Picture                 as "picture"

FROM Inventory i

    -- Warehouse 1
    left join MultiStoreTrn wh1 on
      wh1.ItemCode = i.ItemCode and
      wh1.StoreCode = '%(multi_store)s'
    left join InventoryGroups ig on
      ig.InvGroup = wh1.InvGroup

    -- Attribute info
    left join InventoryCategory ic on
      i.Category = ic.ICCode

WHERE wh1.StoreCode = '%(multi_store)s' AND RTRIM(i.UserDefText01) <> '' AND RTRIM(i.GUID) <> '' AND i.UserDefNum01 = 1;