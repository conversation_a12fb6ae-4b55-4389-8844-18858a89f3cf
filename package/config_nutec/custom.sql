select
    convert(varchar, ar.Customer)            AS 'source_customer_code',
    ar.Name                                  AS 'first_name',
    ''                                       AS 'last_name',
    ar.Email                                 AS 'email',
    'true'                                   AS 'customer_active',
    1                                        AS 'accepts_marketing',
    ar.ShipToAddr1                           AS 'address.address2',
    ar.ShipToAddr2 +
    CASE LTRIM(RTRIM(ar.ShipToAddr3Loc))
    WHEN '' THEN ''
    ELSE ', ' + ar.ShipToAddr3Loc
    END                                      AS 'address.address1',
    ar.ShipToAddr3                           AS 'address.city',
    ar.ShipToAddr5                           AS 'address.country',
    ''                                       AS 'address.country_code',
    ar.ShipToAddr4                           AS 'address.province',
    ''                                       AS 'address.province_code',
    ar.ShipPostalCode                        AS 'address.zip',
    ar.Name                                  AS 'address.company',
    ar.Telephone                             AS 'address.phone',
    ar.BuyingGroup1                          AS 'meta_buying_group1',
    ar.ShippingInstrs                        AS 'meta_shipping_instructions',
    ar.Salesperson                           AS 'meta_sales_person',
    sp.Name                                  AS 'meta_sales_person_name',
    ar.CreditLimit                           AS 'meta_credit_limit',
    ar.Branch                                AS 'meta_branch',
    ar.TermsCode                             AS 'meta_terms_code',
    rtrim(art.Description)                   AS 'meta_payment_terms',
    ar.Area                                  AS 'meta_area',
	RTRIM(ar.Currency)                       AS 'meta_currency',
    ar.PriceCode                             AS 'price_tier',
    ar.SalesWarehouse                        AS 'qty_availability',
    ar.Customer                              AS 'segment|source|products|meta.key|contains',
    CASE LOWER(ar.Branch)
    WHEN 'ld'
        THEN '15'
    ELSE '0'
    END                                      AS "meta_tax_rate"


FROM ArCustomer ar WITH (nolock)
 INNER JOIN ArCustomerBal arb WITH (nolock)
 ON arb.Customer = ar.Customer
 LEFT JOIN SalSalesperson sp
 ON sp.Salesperson = ar.Salesperson and ar.Branch = sp.Branch
 LEFT JOIN [TblSoDiscount] di WITH (nolock)
 ON di.DiscountCode = ar.LineDiscCode
 LEFT JOIN[TblArTerms] art WITH (nolock)
 ON art.TermsCode =  ar.TermsCode

WHERE ISNULL(ar.Email, '') <> ''
AND convert(varchar, ar.Customer) = 'CRA001'
;