{"dbName": "local.db", "dsn": "Driver={SQL Server};Server=ROFF-SAP10;Database=ROFF_IND;Uid=shopsync;Pwd=*******;", "dsnNoDb": "Driver={SQL Server Native Client 11.0};Server=serverName\\dbInstance;Uid=user;Pwd=****;", "server": "ROFF-SAP10", "companyDb": "ROFF_IND", "userName": "stock2shop", "****word": "&s4ggh", "dbServerType": "MSSQL2019", "dbUserName": "shopsync", "dbPassword": "*******", "licenseServer": "localhost", "audit_limit": 1000, "push": {"source_id": 977, "limit": 500, "token": "OHJGLCYCO26FJG33W87RF8X7YF0TPYX9WZCUSGLL", "writeLog": "http://localhost:13338/writeLog", "getProducts": "http://localhost:13338/sapone/getProductsBatch", "getCustomers": "http://localhost:13338/sapone/getCustomersBatch", "countProducts": "http://localhost:13338/sapone/countProducts", "countCustomers": "http://localhost:13338/sapone/countCustomers", "getMeta": "http://localhost:13338/sapone/getMeta", "setMeta": "http://localhost:13338/sapone/setMeta", "productsQueue": "https://app.stock2shop.com/v1/products/queue", "customersQueue": "https://app.stock2shop.com/v1/customers/queue"}}