select
    -- Column must be named "count", it is used by push.exe
    (
    select count(rwi.source_variant_code) as product_count
    from dbo.ROFF_WEB_ITEMS rwi with (nolock)
    where rtrim(isnull(rwi.source_product_code, '')) <> ''      
        and rtrim(isnull(rwi.source_variant_code, '')) <> ''           
        and rtrim(isnull(rwi.title, '')) <> ''
        and rtrim(lower(rwi.product_active)) in ('true','false')
		)
                                                as count,

    (
    select count(rwi.source_variant_code) as product_count
    from dbo.ROFF_WEB_ITEMS rwi with (nolock)
    where rtrim(isnull(rwi.source_product_code, '')) <> ''      
        and rtrim(isnull(rwi.source_variant_code, '')) <> ''           
        and rtrim(isnull(rwi.title, '')) <> ''
        and rtrim(lower(rwi.product_active)) = 'true'
    )
                                                as active,

    (
    select count(rwi.source_variant_code) as product_count
    from dbo.ROFF_WEB_ITEMS rwi with (nolock)
    where rtrim(isnull(rwi.source_product_code, '')) <> ''      
        and rtrim(isnull(rwi.source_variant_code, '')) <> ''           
        and rtrim(isnull(rwi.title, '')) <> ''
        and rtrim(lower(rwi.product_active)) = 'false'
    )
                                                as inactive,

    (
    select count(rwi.source_variant_code) as product_count
    from dbo.ROFF_WEB_ITEMS rwi with (nolock)
    where rtrim(isnull(rwi.source_product_code, '')) <> ''      
        and rtrim(isnull(rwi.source_variant_code, '')) <> ''           
        and rtrim(isnull(rwi.title, '')) <> ''
    )
                                                as total;
