select
    row_number() over (order by rwi.source_variant_code) as n,

    -- There must only be one result row per ItemCode!
    rtrim(rwi.source_product_code)      as "source_product_code",
    rtrim(rwi.source_variant_code)      as "source_variant_code",
    rtrim(rwi.title)                    as "title",
    rwi.collection                      as "collection",
    rwi.product_type                    as "product_type",
    rwi.vendor                          as "vendor",
    ''                                  as "variants.option1",
    ''                                  as "variants.option2",
    ''                                  as "variants.option3",
    rwi.body_html                       as "body_html",
    rwi.[variants.weight]               as "variants.weight",
    rwi.[variants.barcode]              as "variants.barcode",

    -- All price tiers
    rwi.default_price_tier              as "default_price_tier",
    rwi.csv_price_tiers                 as "csv_price_tiers",

    -- All qty availability
    rwi.default_qty_availability        as "default_qty_availability",
    rwi.csv_qty_availability            as "csv_qty_availability",

    case 
        when rtrim(lower(rwi.[variants.inventory_management])) = 'true'
          or rtrim(lower(rwi.[variants.inventory_management])) = 'false'
        then rwi.[variants.inventory_management] 
        else 'true' 
    end                                 as "variants.inventory_management",

    rtrim(lower(rwi.product_active))    as "product_active",
    rwi.tags                            as "tags",

    rwi.meta_item_group_code            as "meta_item_group_code",
    rwi.meta_item_group_name            as "meta_item_group_name",

    -- Special prices for all periods and volumes
    
    rwi.csv_special_prices              as 'csv_special_prices'

    

from dbo.ROFF_WEB_ITEMS rwi with (nolock)
-- View Created on ROFF  Sapone 


where rtrim(isnull(rwi.source_product_code, '')) <> ''      
    and rtrim(isnull(rwi.source_variant_code, '')) <> ''           
    and rtrim(isnull(rwi.title, '')) <> ''
    and rtrim(lower(rwi.product_active)) in ('true','false')
    and rtrim(rwi.source_variant_code) = '%(sku)s'
    ;

