select *
from (
SELECT
    row_number() OVER (ORDER BY inv.StockCode)        AS n,

    RTRIM(LTRIM(inv.StockCode))                       AS "source_product_code",
    RTRIM(LTRIM(inv.StockCode))                       AS "source_variant_code",
    RTRIM(LTRIM(inv.Description))                     AS "title",
    ''                                                AS "collection",
    ''                                                AS "product_type",
    RTRIM(LTRIM(inv.DrawOfficeNum))                   AS "vendor",
    RTRIM(LTRIM(inv.StockCode))                       AS "variants.sku",
    RTRIM(LTRIM(inv.LongDesc))                        AS "body_html",
    CAST(ISNULL(inv.Mass, 0) AS INTEGER)              AS "variants.weight",
    inv.AlternateKey1                                 AS "variants.barcode",
    'true'                                            AS "variants.inventory_management",
    CASE
        WHEN LOWER(RTRIM(ISNULL(im.ECommerce, ''))) = 'y' THEN 'true'
        ELSE 'false'
    END                                               AS "product_active",
    ''                                                AS "tags",

    -- Prices
    'A'                                               AS "default_price_tier",
    STUFF((
      SELECT
        ',' + CONVERT(VARCHAR, LTRIM(RTRIM(price_tiers.PriceCode))) + '|' +
              CONVERT(VARCHAR, CAST(ISNULL(price_tiers.SellingPrice, 0) AS FLOAT))
      FROM InvPrice price_tiers WITH (NOLOCK)
      WHERE price_tiers.StockCode = inv.StockCode
      ORDER BY LTRIM(RTRIM(price_tiers.PriceCode))
    for xml path('')),1,1,'')
                                                      AS "csv_price_tiers",

    -- Warehouse
    '02'                                              AS "default_qty_availability",
    STUFF((
      SELECT
        ',' + CONVERT(VARCHAR, LTRIM(RTRIM(whse.Warehouse))) + '|' +
              CONVERT(VARCHAR, CONVERT(INTEGER, COALESCE(whse.QtyOnHand, 0)))
      FROM InvWarehouse whse WITH (NOLOCK)
      WHERE whse.StockCode = inv.StockCode
      ORDER BY LTRIM(RTRIM(whse.Warehouse))
    for xml path('')),1,1,'')
                                                      AS "csv_qty_availability",

    atx.CurTaxRate                                    AS "meta_tax_rate",
    inv.StockUom                                      AS "meta_uom"


FROM InvMaster inv WITH (NOLOCK)

    -- Active form field
   INNER JOIN [InvMaster+] im WITH (nolock)
    ON im.StockCode=inv.StockCode AND im.ECommerce in ('Y','N')

    LEFT JOIN AdmTax atx WITH (nolock)
    ON atx.TaxCode = inv.TaxCode

) as rows
where
n > '%(audit_lower_limit)n'
and n <= '%(audit_upper_limit)n'