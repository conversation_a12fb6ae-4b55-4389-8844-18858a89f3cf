{"dbName": "local.db", "dsn": "Driver={SQL Server};Server=SQL2\\SYSPRO8;Database=SysproCompanyU;Uid=Stock2Shop1;Pwd=*******$;", "audit_limit": 1000, "push": {"source_id": 784, "limit": 500, "token": "U7BSQ9TSHM15H6RBCYBG2WMRX8NRKGBKBC57SZDO", "writeLog": "http://localhost:13337/writeLog", "getProducts": "http://localhost:13337/odbc/getProductsBatch", "getCustomers": "http://localhost:13337/odbc/getCustomersBatch", "countProducts": "http://localhost:13337/odbc/countProducts", "countCustomers": "http://localhost:13337/odbc/countCustomers", "getMeta": "http://localhost:13337/odbc/getMeta", "setMeta": "http://localhost:13337/odbc/setMeta", "productsQueue": "https://app.stock2shop.com/v1/products/queue", "customersQueue": "https://app.stock2shop.com/v1/customers/queue"}}