select *
from (
select
    row_number() over (order by ocrd.CardCode) as n,


    case
        when ocrd.QryGroup4  = 'Y' then 'MSales001-' + ocrd.CardCode
        else ocrd.CardCode + '-E'
    end                                 as 'source_customer_code',
    LTRIM(RTRIM((REPLACE(REPLACE(REPLACE(ocrd.CardName, '**', ''), 'COD', ''), '-', ''))))
                                        as 'card_name',
    ocrd.E_Mail                         as 'email',

    case         
        when ocrd.validFor ='Y'  then 'true'            
        else 'false'            
    end                                 as "customer_active",

    1                                   as 'accepts_marketing',
    ocrd.MailAddres                     as 'address.address1', -- or Address?
    ''                                  as 'address.address2',
    crd1.City                           as 'address.city',
    crd1.Country                        as 'address.country',
    'ZA'                                as 'address.country_code',
    ''                                  as 'address.province',
    ''                                  as 'address.province_code',
    crd1.ZipCode                        as 'address.zip',
    ''                                  as 'address.company',
    ocrd.CntctPrsn                      as 'address.contact_person',
    ocrd.Phone1                         as 'address.phone',

    -- XML addresses
    (
      SELECT
          crd1.Address                 as 'address_code',
          CASE crd1.AdresType
          WHEN 'B' THEN 'billing'
          WHEN 'S' THEN 'shipping'
          END
                                       as 'type',
          coalesce(crd1.Block, '')     as 'address1',
          coalesce(crd1.Address2, '')  as 'address2',
          coalesce(crd1.City, '')      as 'city',
          coalesce(crd1.Country, '')   as 'country',
          coalesce(crd1.Country, '')   as 'country_code',
          coalesce(crd1.County, '')    as 'province',
          coalesce(crd1.State, '')     as 'province_code',
          coalesce(crd1.ZipCode, '')   as 'zip',
          coalesce(ocrd.Phone1, '')    as 'phone',
          coalesce(ocrd.CardName, '')  as 'company',
          ''                           as 'first_name',
          ''                           as 'last_name'

      FROM crd1
      WHERE crd1.CardCode=ocrd.CardCode 
         and crd1.AdresType in ('S','B')
       ORDER BY crd1.City
       FOR xml PATH ('address'), root ('addresses')
    )                                   as 'xml_addresses',

    -- Discount groups
    stuff(
    (select
    convert(varchar, ospg.ObjKey) + '|' + -- oitb.ItmsGrpCod
    convert(varchar, ospg.Discount) + ','
    from ospg with (nolock)
    where ospg.CardCode = ocrd.CardCode
    and ospg.ObjType = 52 -- Discount Groups
    and ospg.Discount > 0
    order by ospg.ObjKey
    for xml path('')),1,0,'')
                                        as 'csv_discount_groups',
               -- For customers

    ocrd.ListNum                        as 'price_tier',
    ''                                  as 'qty_availability',

    '680'                               as 'meta_channel_id',
    ocrd.CardType                       as 'meta_card_type',
    case 
        when ocrd.QryGroup4 = 'Y' then ocrd.CardCode
        else ''
    end                                 as "meta_ext_cust"

from ocrd with(nolock)
left join crd1 with (nolock)
    on crd1.cardcode = ocrd.cardcode
        and crd1.address = ocrd.shiptodef
        and crd1.adrestype = 'S'
where ocrd.cardtype in ('C', 'L')
    and datalength(e_mail) <> 0
    and ocrd.e_mail is not null

) as rows
where
n > '%(audit_lower_limit)n'
and n <= '%(audit_upper_limit)n'