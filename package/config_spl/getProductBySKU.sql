select
    oitm.ItemCode                               as "source_product_code",
    oitm.ItemCode                               as "source_variant_code",
    oitm.ItemName                               as "title",
    oitb.ItmsGrpNam                             as "collection",
    ''                                          as "product_type",
    ''                                          as "vendor",
    ''                                          as "variants.option1",
    ''                                          as "variants.option2",
    ''                                          as "variants.option3",
    ''                                          as "body_html",
    0                                           as "variants.weight",
    oitm.CodeBars                               as "variants.barcode",

    -- All price tiers
    '2'                                         as "default_price_tier",
    stuff(
    (select
    convert(varchar, itm1.PriceList) + '|' +
    convert(varchar, itm1.Price) + ','
    from itm1
    where itm1.ItemCode = oitm.ItemCode
    order by itm1.PriceList
    for xml path('')),1,0,'')
                                                as "csv_price_tiers",

    -- All qty availability
    'Bio'                                       as "default_qty_availability",
    stuff(
    (select
    convert(varchar, oitw.WhsCode) + '|' +
    convert(varchar, oitw.OnHand-oitw.IsCommited)+','
    from oitw
    where oitw.ItemCode = oitm.ItemCode
    order by oitw.WhsCode
    for xml path('')),1,0,'')
                                                as "csv_qty_availability",

    'true'                                      as "variants.inventory_management",

    CASE
       WHEN oitm.U_Online_AVA in ('B2B','B2B_EUO','EUO')
       THEN 'true'
       ELSE 'false'
    END                                         as "product_active",

    ''                                          as "tags",

    oitb.ItmsGrpCod                             as "meta_item_group_code",
    oitb.ItmsGrpNam                             as "meta_item_group_name",

    -- Special prices for all periods and volumes
    stuff(
    (select
    convert(varchar, ospp.CardCode) + '|' +
    convert(varchar, ospp.Discount)+','
    from ospp
    where ospp.ItemCode = oitm.ItemCode
    and ospp.Discount > 0
    order by ospp.ItemCode
    for xml path('')),1,0,'')
                                                as 'csv_special_prices',

    oitm.U_PACK_QTY                             as "meta_pack_qty",
    oitm.U_Online_AVA                           as "meta_store",
    oitm.U_Add_Ons                              as "meta_add_ons",
    '2021-11-24'                                as "meta_full_sync"

    -- Special prices for period join ospp with spp1
    -- Special prices for volume join ospp with spp2

from oitm  WITH (NOLOCK)
INNER JOIN OVTG WITH (NOLOCK) on OVTG.Code = OITM.VatGourpSa
INNER JOIN oitb WITH (NOLOCK) on oitm.ItmsGrpCod = oitb.ItmsGrpCod
where
    oitm.ItemCode = '%(sku)s';


