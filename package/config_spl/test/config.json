{"dbName": "local.db", "dsn": "Driver={SQL Server Native Client 11.0};Server=serverName\\dbInstance;Database=dbName;Uid=user;Pwd=****;", "dsnNoDb": "Driver={SQL Server Native Client 11.0};Server=serverName\\dbInstance;Uid=user;Pwd=****;", "server": "serverName\\dbInstance", "companyDb": "companyDb", "userName": "manager", "****word": "manager", "dbServerType": "MSSQL2008", "dbUserName": "user", "dbPassword": "****word", "licenseServer": "serverName", "audit_limit": 500, "push": {"source_id": 99999, "limit": 100, "token": "token", "writeLog": "http://localhost:1337/writeLog", "getProducts": "http://localhost:1337/sapone/getProductsBatch", "getCustomers": "http://localhost:1337/sapone/getCustomersBatch", "countProducts": "http://localhost:1337/sapone/countProducts", "countCustomers": "http://localhost:1337/sapone/countCustomers", "getMeta": "http://localhost:1337/sapone/getMeta", "setMeta": "http://localhost:1337/sapone/setMeta", "productsQueue": "https://app.stock2shop.com/v1/products/queue", "customersQueue": "https://app.stock2shop.com/v1/customers/queue"}}