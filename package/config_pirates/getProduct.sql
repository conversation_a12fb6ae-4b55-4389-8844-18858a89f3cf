SELECT
    inv2.barCode                                    AS "source_product_code",
    inv2.barCode                                    AS "source_variant_code",
    inv.description + ' - ' + inv2.childDescription AS "title",
    ''                                              AS "collection",
    ''                                              AS "product_type",
    ''                                              AS "vendor",
    inv2.childDescription                           AS "variants.option1",
    ''                                              AS "variants.option2",
    ''                                              AS "variants.option3",
    inv2.barCode                                    AS "variants.sku",
    ''                                              AS "body_html",
    0                                               AS "variants.weight",
    inv2.barCode                                    AS "variants.barcode",
    inv2.qty                                        AS "variants.qty",
    CAST(inv.sellingIncluding AS FLOAT)             AS "variants.retail_price",
    CAST(inv.costIncluding AS FLOAT)                AS "variants.cost_price",
    'true'                                          AS "variants.inventory_management",
    'true'                                          AS "product_active",
    ''                                              AS "tags",

    inv2.childCode                                  AS "meta_materialCode"

FROM tblProducts inv with (nolock)

    -- Base Data
    INNER JOIN tblStockChildItems inv2 WITH (nolock)
    ON inv.code = inv2.mainCode

WHERE LEN(LTRIM(RTRIM(inv2.barCode))) = 13 AND inv2.barCode = '%(source_variant_code)s';
