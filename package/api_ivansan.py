import json
import imp
import time
import requests
from .shared import utils
from .controllers import base_push as base

class Api(base.Api):
    _configDir = "config_ivansan"

    def reload(self):
        imp.reload(base)
        base.Api.reload(self)
        response = {
            "status": True,
            "data": {
                "reload": True
            }
        }
        return json.dumps(response, indent=self._indent)

    # Add _transformProduct here to override default

    def _afterProductTransform(self, params, result, source_product):
        from collections import OrderedDict

        # add compare at pricelist
        price_tier = OrderedDict()
        price_tier["tier"] = "compare_at"
        price_tier["price"] = result["variants.compare_at"]
        source_product["product"]["variants"]["price_tiers"].append(price_tier)

        return source_product

    def _getDictOrEmptyString(self, row, key):
        if key in row:
            return row[key]
        return ""

    def _isInteger(self, n):
        try:
            float(n)
        except ValueError:
            return False
        else:
            return float(n).is_integer()

    def createOrder(self):
        t1 = time.time()

        self._setConfig()

        params = self._getParams()

        payload = self._payload()
        try:
            # Payload might be bytes, see connector.py
            payload = payload.decode("utf-8")
        except AttributeError:
            # Assume payload is already a decoded unicode string
            pass
        try:
            payload = json.loads(payload)
        except ValueError:
            response = {
                "status": False,
                "description": "Request body is not valid json",
                "line": utils.lineNo()
            }
            return json.dumps(response, indent=self._indent)

        s2sOrder = payload["system_order"]
        source = payload["sources"][0]

        # source customer code
        if "source_customer_code" in source and source["source_customer_code"]:
            source_customer_code = source["source_customer_code"]
        else:
            if "default_customer_code" in params:
                source_customer_code = params["default_customer_code"]
        if source_customer_code is None:
            raise Exception("source_customer_code missing and no default_customer_code set")

        # request payload
        url = "http://localhost/Sage300webapi/V1.0/-/CALDAT/OE/OEOrders"
        sage_order = {
            "CustomerNumber": source_customer_code,
            # "CustomerGroupCode": "ONLINE",
            # "PostInvoice": True,
            # "InvoiceWillBeProduced": True,
            # "PerformShipAll": True,
            "BillToName": self._getDictOrEmptyString(s2sOrder["billing_address"], "company"),
            "BillToAddressLine1": self._getDictOrEmptyString(s2sOrder["billing_address"], "address1"),
            "BillToAddressLine2": self._getDictOrEmptyString(s2sOrder["billing_address"], "address2"),
            # "BillToAddressLine3": "",
            # "BillToAddressLine4": "",
            "BillToCity": self._getDictOrEmptyString(s2sOrder["billing_address"], "city"),
            "BillToStateProvince": self._getDictOrEmptyString(s2sOrder["billing_address"], "province"),
            "BillToZipPostalCode": self._getDictOrEmptyString(s2sOrder["billing_address"], "zip"),
            "BillToCountry": self._getDictOrEmptyString(s2sOrder["billing_address"], "country"),
            "BillToPhoneNumber": self._getDictOrEmptyString(s2sOrder["billing_address"], "phone"),
            # "BillToFaxNumber": "",
            "BillToContact": self._getDictOrEmptyString(s2sOrder["billing_address"], "first_name"),
            "BillToEmail": self._getDictOrEmptyString(s2sOrder["customer"], "email"),
            # "BillToContactPhone": "",
            # "BillToContactFax": "",
            # "BillToContactEmail": "",
            # "ShipToLocationCode": "",
            "ShipToName": self._getDictOrEmptyString(s2sOrder["shipping_address"], "company"),
            "ShipToAddressLine1": self._getDictOrEmptyString(s2sOrder["shipping_address"], "address1"),
            "ShipToAddressLine2": self._getDictOrEmptyString(s2sOrder["shipping_address"], "address2"),
            # "ShipToAddressLine3": "",
            # "ShipToAddressLine4": "",
            "ShipToCity": self._getDictOrEmptyString(s2sOrder["shipping_address"], "city"),
            "ShipToStateProvince": self._getDictOrEmptyString(s2sOrder["shipping_address"], "province"),
            "ShipToZipPostalCode": self._getDictOrEmptyString(s2sOrder["shipping_address"], "zip"),
            "ShipToCountry": self._getDictOrEmptyString(s2sOrder["shipping_address"], "country"),
            "ShipToPhoneNumber": self._getDictOrEmptyString(s2sOrder["shipping_address"], "phone"),
            # "ShipToFaxNumber": "",
            "ShipToContact": self._getDictOrEmptyString(s2sOrder["shipping_address"], "first_name"),
            "ShipToEmail": self._getDictOrEmptyString(s2sOrder["customer"], "email"),
            # "ShipToContactPhone": "",
            # "ShipToContactFax": "",
            # "ShipToContactEmail": "",
            # "PurchaseOrderNumber": self._getDictOrEmptyString(s2sOrder, "channel_order_code"),
            "OrderReference": self._getDictOrEmptyString(s2sOrder, "channel_order_code"),
            # "OrderType": "Active",
            # "DefaultLocationCode": params["location_code"],
            "OrderDescription": self._getDictOrEmptyString(s2sOrder, "channel_order_code"),
            "OrderComment": self._getDictOrEmptyString(s2sOrder, "notes"),
            # "ShipViaCode": "COLLEC",
            "OrderNumber": str(self._getDictOrEmptyString(s2sOrder, "channel_order_code")),
            # "OrderHomeCurrency": "ZAR",
            # "OrderSourceCurrency": "ZAR",
            # "TaxGroup": "RSA",
            "OrderDetails": [],
            "OrderOptionalFields": []
        }

        # line items
        for item in s2sOrder["line_items"]:
            line_item = {
                # "LineType": "Item",
                "Item": item["sku"],
                # "Location": params["location_code"],
                "QuantityOrdered": item["qty"],
                # "QuantityShipped": item["qty"],
                # "OrderUnitOfMeasure": "EA",
                # "OrderUnitPrice": float(item["price"]) * 1.15,
                # "PricingUnitPrice": float(item["price"]) * 1.15,
                "PricingUnitPrice": float(item["price"]),
                "PriceOverride": True
            }
            sage_order["OrderDetails"].append(line_item)

        # shipping added as line item
        # if "shipping_code" in params:
        #     for item in s2sOrder["shipping_lines"]:
        #         line_item = {
        #             "LineType": "Miscellaneous",
        #             "Item": params["shipping_code"],
        #             # "Location": params["location_code"],
        #             "QuantityOrdered": item["qty"],
        #             # "QuantityShipped": item["qty"],
        #             # "OrderUnitOfMeasure": "EA",
        #             # "OrderUnitPrice": float(item["price"]) * 1.15,
        #             # "PricingUnitPrice": float(item["price"]) * 1.15,
        #             "PricingUnitPrice": float(item["price"]),
        #             "PriceOverride": True
        #         }
        #         sage_order["OrderDetails"].append(line_item)

        # # ship to code
        # if "ship_to_code" in params:
        #     sage_order["ShipViaCode"] = params["ship_to_code"]
        #
        # # purchase order
        # if "purchase_order" in params:
        #     sage_order["PurchaseOrderNumber"] = params["purchase_order"]
        #
        # # Optional rep field
        # if "rep" in params:
        #     sage_order["OrderOptionalFields"].append({
        #         "OptionalField": "REP",
        #         "Value": params["rep"]
        #     })
        # else:
        #     sage_order["OrderOptionalFields"].append({
        #         "OptionalField": "REP",
        #         "Value": "WAREHOUSE"
        #     })

        json_data = json.dumps(sage_order)
        # write out payload to file - debug
        text_file = open("output2.txt", "w")
        text_file.write(json.dumps(sage_order))
        text_file.close()
        # -- End Debug
        headers = {'Content-Type': 'application/json'}
        auth = (params["username"], params["password"])
        r = requests.post(url, params=params, data=json_data, auth=auth, headers=headers)
        # write out response data to file - debug
        text_file = open("output.txt", "w")
        text_file.write(json.dumps(r.json()))
        text_file.close()
        # -- End Debug
        if r.status_code > 204:
            response = {
                "status": False,
                "code": r.status_code,
                "description": r.text,
                "line": utils.lineNo()
            }
            return response
        sage_response = r.json()
        t2 = time.time()
        if "OrderNumber" not in sage_response or "CustomerNumber" not in sage_response:
            response = {
                "status": False,
                "code": r.status_code,
                "description": r.text,
                "line": utils.lineNo()
            }
            return response
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {
                "source_order_code": sage_response["OrderNumber"],
                "source_customer_code": sage_response["CustomerNumber"]
            },
        }
        return json.dumps(response, indent=self._indent)