SELECT
    row_number() OVER (ORDER BY inv.StockCode)        AS n,

    RTRIM(LTRIM(inv.StockCode))                       AS "source_product_code",
    RTRIM(LTRIM(inv.StockCode))                       AS "source_variant_code",
    RTRIM(LTRIM(inv.Description))                     AS "title",
    ''							                      AS "collection",
    ''                                                AS "product_type",
    ''								                  AS "vendor",
    RTRIM(LTRIM(inv.StockCode))                       AS "variants.sku",
	''												  AS "body_html",
    0									              AS "variants.weight",
    ''                                                AS "variants.barcode",
    CASE LOWER(inv2.ProductActive)
	WHEN 'active' THEN 'true'
	ELSE 'false'
	END                                               AS "product_active",
    'true'                                            AS "variants.inventory_management",
    ''                                                AS "tags",

    -- Prices
    'D'                                               AS "default_price_tier",
    STUFF((
      SELECT
        ',' + CONVERT(VARCHAR, LTRIM(RTRIM(price_tiers.PriceCode))) + '|' +
              CONVERT(VARCHAR, CAST(ISNULL(price_tiers.SellingPrice, 0) AS FLOAT))
      FROM InvPrice price_tiers WITH (NOLOCK)
      WHERE price_tiers.StockCode = inv.StockCode
      ORDER BY LTRIM(RTRIM(price_tiers.PriceCode))
    for xml path('')),1,1,'')
                                                      AS "csv_price_tiers",

    '10B'                                             AS "default_qty_availability",
    -- We only sync percentage of certain warehouse stock
    -- Warehouse 10B = 5%
    -- Warehouse 10 = 3%
    -- Warehouse 15 = 100%
    -- Warehouse 10C = 5%
    STUFF((
      SELECT
        ',' + CONVERT(VARCHAR, LTRIM(RTRIM(whse.Warehouse))) + '|' +
        CASE RTRIM(LTRIM(inv2.ShopifyActiveWareh))
        WHEN '10B' THEN CONVERT(VARCHAR, CONVERT(INTEGER, COALESCE(whse.QtyOnHand, 0) - COALESCE(whse.QtyAllocated, 0)) * 0.05)
        WHEN '10C' THEN CONVERT(VARCHAR, CONVERT(INTEGER, COALESCE(whse.QtyOnHand, 0) - COALESCE(whse.QtyAllocated, 0)) * 0.05)
        WHEN '10' THEN CONVERT(VARCHAR, CONVERT(INTEGER, COALESCE(whse.QtyOnHand, 0) - COALESCE(whse.QtyAllocated, 0)) * 0.03)
        ELSE CONVERT(VARCHAR, CONVERT(INTEGER, COALESCE(whse.QtyOnHand, 0) - COALESCE(whse.QtyAllocated, 0)))
        END
      FROM InvWarehouse whse WITH (NOLOCK)
      WHERE whse.StockCode = inv.StockCode
      ORDER BY LTRIM(RTRIM(whse.Warehouse))
    for xml path('')),1,1,'')
                                                      AS "csv_qty_availability",

    -- If FixedPriceCode is set, use discount 1
    STUFF((
      SELECT
      ',' + CONVERT(VARCHAR, LTRIM(RTRIM(sor.CustomerBuyGrp))) + '|' + CONVERT(VARCHAR, LTRIM(RTRIM(sor.FixedPriceCode))) + '-' + CONVERT(VARCHAR, COALESCE(sor.Discount1, 0))
      FROM SorContractPrice sor WITH (NOLOCK)
      WHERE sor.StockCode = inv.StockCode
      AND sor.ContractType='B'
      AND sor.PriceMethod in ('P', 'C')
      AND (DATEADD(day, DATEDIFF(day,0,GETDATE()),0) <= ExpiryDate or ExpiryDate is NULL )
      AND DATEADD(day, DATEDIFF(day,0,GETDATE()),0) >= StartDate
      ORDER BY LTRIM(RTRIM(sor.CustomerBuyGrp))
    for xml path('')),1,1,'')
                                                      AS "csv_discount_groups",

    -- If FixedPriceCode is empty, uses fixedPrice
    STUFF((
      SELECT
        ',' + CONVERT(VARCHAR, LTRIM(RTRIM(sor.CustomerBuyGrp))) + '|' + CONVERT(VARCHAR, COALESCE(sor.FixedPrice, 0))
      FROM SorContractPrice sor WITH (NOLOCK)
      WHERE sor.StockCode = inv.StockCode
      AND sor.ContractType='B'
      AND sor.PriceMethod='F'
      AND (DATEADD(day, DATEDIFF(day,0,GETDATE()),0) <= ExpiryDate or ExpiryDate is NULL )
      AND DATEADD(day, DATEDIFF(day,0,GETDATE()),0) >= StartDate
      ORDER BY LTRIM(RTRIM(sor.CustomerBuyGrp))
    for xml path('')),1,1,'')
                                                      AS "csv_discount_groups_fixed",
    RTRIM(LTRIM(inv2.ShopifyActiveWareh))             AS "meta_shopify_channel",
    inv.StockUom                                      AS "meta_uom"


FROM InvMaster inv WITH (NOLOCK)

    -- Base Data
    LEFT JOIN [InvMaster+] inv2 WITH (nolock)
    ON inv.StockCode = inv2.StockCode

    LEFT JOIN AdmTax atx WITH (nolock)
    ON atx.TaxCode = inv.TaxCode

where RTRIM(LTRIM(inv.StockCode)) = '%(sku)s';