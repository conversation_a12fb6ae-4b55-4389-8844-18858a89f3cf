SELECT *
FROM (
SELECT
  row_number() over (order by st.StockLink) as n,

  st.ucIIProductCode                                                   AS "source_product_code",
  st.StockLink                                                         AS "source_variant_code",
  st.ucIIWebTitle                                                      AS "title",
  st.ucIIMainCategory                                                  AS "collection",
  st.ucIISubCategory                                                   AS "product_type",
  ''                                                                   AS "vendor",
  st.Code                                                              AS "variants.sku",
  st.ucIIProdOptionName1                                               AS "variants.option1_name",
  st.ucIIProdOptionValue1                                              AS "variants.option1_value",
  st.ucIIProdOptionName2                                               AS "variants.option2_name",
  st.ucIIProdOptionValue2                                              AS "variants.option2_value",
  st.ucIIWebTitleDescription                                           AS "body_html",
  0                                                                    AS "variants.weight",
  CASE WHEN st.ucIIOldCode IS NULL THEN '' 
  ELSE st.ucIIOldCode
  END                                                                  AS "variants.barcode",
  'true'                                                               AS "variants.inventory_management",
  CASE st.bSyncToSOT
    WHEN 1 THEN 'true'
    ELSE 'false'
  END                                                                  AS "product_active",
  ''                                                                   AS "tags",

  'BASE'                                                               AS "default_price_tier",
  stuff(
    (select
      ',' + convert(varchar, price_names.cName) + '|' +
            convert(varchar, price.fExclPrice)
    from dbo._etblPriceListPrices price WITH (NOLOCK)

      inner join _etblPriceListName price_names WITH (NOLOCK)
        ON price.iPriceListNameID = price_names.IDPriceListName AND price.iStockID = st.StockLink and price.iWarehouseID = 102000007

    order by price_names.cName
    for xml path('')),1,1,'')
                                                                       AS "csv_price_tiers",

  ''                                                                   AS "default_qty_availability",
  CONCAT(
      'MAIN_BOK|',
      CONVERT(VARCHAR, ISNULL(qty_wh1.WHQtyOnHand - qty_wh1.WHQtyOnSO, 0)),
      ',BRAZ_BOK|',
      CONVERT(VARCHAR, ISNULL(qty_wh2.WHQtyOnHand - qty_wh2.WHQtyOnSO, 0))
  )
                                                                      AS "csv_qty_availability",

  -- Discount by customer per product
    stuff(
        (SELECT convert(VARCHAR, st.StockLink) + '-' + convert(VARCHAR, vd.iARAPID) + '|' +
                convert(VARCHAR, vdlvln.fPriceDisc) + ','
         FROM _etblVDAR vd
             INNER JOIN _etblVDLnAR vdln
                 ON vd.IDVD = vdln.iVDID
             INNER JOIN _etblVDLnLvlAR vdlvln
                 ON vdln.IDVDLn = vdlvln.iVDLnID
         WHERE vd.bARAPAll = 0
           AND getdate() >= vdln.dEffDate AND getdate() <= vdln.dExpDate
           AND vd.iARAPID > 0
           AND vdlvln.fQuantity = 1
           AND vdln.iStockID = st.StockLink
           AND vd.iGroupID = 0
           AND vdln.iSTGroupID = 0
           AND vdln.bUseStockPrc = 0
         ORDER BY vd.iARAPID
         FOR XML PATH ('')), 1, 0, '')
                                                                       AS 'csv_customer',
  -- Discount by customer per product fixed
    stuff(
        (SELECT convert(VARCHAR, st.StockLink) + '-' + convert(VARCHAR, vd.iARAPID) + '|' +
                convert(VARCHAR, vdlvln.fPriceDisc) + ','
         FROM _etblVDAR vd
             INNER JOIN _etblVDLnAR vdln
                 ON vd.IDVD = vdln.iVDID
             INNER JOIN _etblVDLnLvlAR vdlvln
                 ON vdln.IDVDLn = vdlvln.iVDLnID
         WHERE vd.bARAPAll = 0
           AND getdate() >= vdln.dEffDate AND getdate() <= vdln.dExpDate
           AND vd.iARAPID > 0
           AND vdlvln.fQuantity = 1
           AND vdln.iStockID = st.StockLink
           AND vd.iGroupID = 0
           AND vdln.iSTGroupID = 0
           AND vdln.bUseStockPrc = 1
         ORDER BY vd.iARAPID
         FOR XML PATH ('')), 1, 0, '')
                                                                       AS 'csv_customer_fixed',

  -- Discount by customer linked to product group
    stuff(
        (SELECT convert(VARCHAR, st.StockLink) + '-' + convert(VARCHAR, vd.iARAPID) + '|' +
                convert(VARCHAR, vdlvln.fPriceDisc) + ','
         FROM _etblVDAR vd
             INNER JOIN _etblVDLnAR vdln
                 ON vd.IDVD = vdln.iVDID
             INNER JOIN _etblVDLnLvlAR vdlvln
                 ON vdln.IDVDLn = vdlvln.iVDLnID
          WHERE vd.bARAPAll = 0
          AND getdate() >= vdln.dEffDate AND getdate() <= vdln.dExpDate
          AND vd.iARAPID > 0
          AND vdlvln.fQuantity = 1
          AND vdln.iStockID = 0
          AND vd.iGroupID = 0
          AND vdln.iStGroupID = grp.idGrpTbl
          AND vdln.bUseStockPrc = 0
         ORDER BY vd.iARAPID
         FOR XML PATH ('')), 1, 0, '')
                                                                       AS 'csv_customer_product_group',

    -- Discount by customer linked to product group fixed
    stuff(
        (SELECT convert(VARCHAR, st.StockLink) + '-' + convert(VARCHAR, vd.iARAPID) + '|' +
                convert(VARCHAR, vdlvln.fPriceDisc) + ','
         FROM _etblVDAR vd
             INNER JOIN _etblVDLnAR vdln
                 ON vd.IDVD = vdln.iVDID
             INNER JOIN _etblVDLnLvlAR vdlvln
                 ON vdln.IDVDLn = vdlvln.iVDLnID
         WHERE vd.bARAPAll = 0
          AND getdate() >= vdln.dEffDate AND getdate() <= vdln.dExpDate
          AND vd.iARAPID > 0
          AND vdlvln.fQuantity = 1
          AND vdln.iStockID = 0
          AND vd.iGroupID = 0
          AND vdln.iStGroupID = grp.idGrpTbl
          AND vdln.bUseStockPrc = 1
         ORDER BY vd.iARAPID
         FOR XML PATH ('')), 1, 0, '')
                                                                       AS 'csv_customer_product_group_fixed',

    -- Discount by customer group linked to product group
    stuff(
        (SELECT convert(VARCHAR, st.StockLink) + '-' + convert(VARCHAR, vd.iGroupID) + '|' +
                convert(VARCHAR, vdlvln.fPriceDisc) + ','
         FROM _etblVDAR vd
             INNER JOIN _etblVDLnAR vdln
                 ON vd.IDVD = vdln.iVDID
             INNER JOIN _etblVDLnLvlAR vdlvln
                 ON vdln.IDVDLn = vdlvln.iVDLnID
         WHERE vd.bARAPAll = 0                -- discount for all
          AND getdate() >= vdln.dEffDate AND getdate() <= vdln.dExpDate -- Valid Date
          AND vd.iARAPID = 0                  -- per accounts receivable
          AND vdlvln.fQuantity = 1            -- volume discount
          AND vdln.iStockID = 0               -- stock item
          AND vd.iGroupID > 0                 -- customer group
          AND vdln.iStGroupID = grp.idGrpTbl  -- stock group
          AND vdln.bUseStockPrc = 0           -- fixed or percentage
         ORDER BY vd.iGroupID
         FOR XML PATH ('')), 1, 0, '')
                                                                       AS 'csv_customer_group_product_group',

       -- Discount by customer group linked to product group fixed
    stuff(
        (SELECT convert(VARCHAR, st.StockLink) + '-' + convert(VARCHAR, vd.iGroupID) + '|' +
                convert(VARCHAR, vdlvln.fPriceDisc) + ','
         FROM _etblVDAR vd
             INNER JOIN _etblVDLnAR vdln
                 ON vd.IDVD = vdln.iVDID
             INNER JOIN _etblVDLnLvlAR vdlvln
                 ON vdln.IDVDLn = vdlvln.iVDLnID
         WHERE vd.bARAPAll = 0                -- discount for all
          AND getdate() >= vdln.dEffDate AND getdate() <= vdln.dExpDate -- Valid Date
          AND vd.iARAPID = 0                  -- per accounts receivable
          AND vdlvln.fQuantity = 1            -- volume discount
          AND vdln.iStockID = 0               -- stock item
          AND vd.iGroupID > 0                 -- customer group
          AND vdln.iStGroupID = grp.idGrpTbl  -- stock group
          AND vdln.bUseStockPrc = 1           -- fixed or percentage
         ORDER BY vd.iGroupID
         FOR XML PATH ('')), 1, 0, '')
                                                                       AS 'csv_customer_group_product_group_fixed',

  -- product group
  qty_wh2.WHStockGroup                                                as "meta_item_group_name",
 (Select idGrpTbl from GrpTbl where StGroup = qty_wh2.WHStockGroup)   as "meta_item_group_code",
 qty_wh1.WHStockGroup                                                 as "meta_item_group_name2",
 (Select idGrpTbl from GrpTbl where StGroup = qty_wh1.WHStockGroup)   as "meta_item_group_code2",
 st.ucIIOldCode                                                       as "meta_old_code",

  -- List of images for this product
  -- Enables automatic pulling of new images when image is added
  ISNULL(
  STUFF(
    (SELECT ',' + convert(varchar, i.idInvImage)
    FROM _etblInvImages i WITH (NOLOCK)
    WHERE st.StockLink = i.iStockLink
    ORDER BY i.idInvImage
    FOR XML PATH ('')), 1, 1, ''), '')
                                                                       AS "meta_image_id_csv"

FROM StkItem st WITH (NOLOCK)

  -- qty
  LEFT JOIN dbo.WhseStk qty_wh1 WITH (NOLOCK)
    ON qty_wh1.WHStockLink = st.StockLink AND
      qty_wh1.WHWhseID = 102000007 -- MAIN_BOK

  LEFT JOIN dbo.WhseStk qty_wh2 WITH (NOLOCK)
    ON qty_wh2.WHStockLink = st.StockLink AND
      qty_wh2.WHWhseID = 6 -- BRAZ_BOK

  -- Group (based on warehouse not stock item)
  LEFT JOIN GrpTbl grp WITH (NOLOCK) ON grp.StGroup = qty_wh1.WHStockGroup
  -- LEFT JOIN GrpTbl grp WITH (NOLOCK) ON grp.StGroup = st.ItemGroup

WHERE st.ucIIProductCode <> '' AND st.ucIIWebTitle <> ''

) AS rows
WHERE n > '%(audit_lower_limit)n' AND n <= '%(audit_upper_limit)n';