SELECT
    convert(varchar, ar.Customer)
                          as 'source_customer_code',

    ar.Name               as 'first_name',
    ''                    as 'last_name',
    ar.Email              as 'email',
    CASE rtrim(lower(ar.CustomerClass))
        WHEN 'z' THEN 'false'
        WHEN 'w' THEN 'false'
    ELSE 'true' END       as 'customer_active',
    1                     as 'accepts_marketing',
    ar.ShipToAddr1        as 'address.address1', -- or Address?
    ar.ShipToAddr2        as 'address.address2',
    ar.ShipToAddr3        as 'address.city',
    ''                    as 'address.country',
    ''                    as 'address.country_code',
    ''                    as 'address.province',
    ''                    as 'address.province_code',
    ar.ShipPostalCode     as 'address.zip',
    ar.ShortName          as 'address.company',
    ar.Telephone          as 'address.phone',

    ar2.ShowOnline        as 'meta_show_online',
    ar2.DisplayName       as 'meta_display_name',
    ar.CreditLimit        as 'meta_credit_limit',
    ar.<PERSON>             as 'meta_branch',
    arb.CurrentBalance1   as 'meta_balance',

    CASE
        WHEN LOWER(LTRIM(ar.PriceCode)) = 'f' THEN '$'
        WHEN LOWER(LTRIM(ar.PriceCode)) = 'e' THEN '€'
        WHEN LOWER(LTRIM(ar.PriceCode)) = '' THEN 'R'
        ELSE 'R'
    END                   as 'meta_currency',

    CASE
        WHEN LOWER(LTRIM(ar.PriceCode)) IN ('f', 'e') THEN '0'
        WHEN LOWER(LTRIM(ar.PriceCode)) = '' THEN '15'
        ELSE '15'
    END                   as 'meta_tax_rate',

    CASE
        WHEN LOWER(LTRIM(ar.PriceCode)) IN ('f', 'e') THEN '0'
        WHEN LOWER(LTRIM(ar.PriceCode)) = '' THEN '15'
        ELSE '15'
    END                   as 'meta_tax_rate_shipping',

    CASE LOWER(LTRIM(ar.PriceCode))
        WHEN 'e' THEN 'DE' -- customer price list E is linked to the product price tier DE in SYSPRO
        ELSE LTRIM(ar.PriceCode)
    END                   as 'price_tier',
    ''                    as 'qty_availability',
    '237'                 as 'meta_channel_id'

FROM ArCustomer ar WITH (nolock)
 INNER JOIN ArCustomerBal arb WITH (nolock)
 ON arb.Customer = ar.Customer
 LEFT JOIN [ArCustomer+] ar2
 ON ar2.Customer = ar.Customer
where ar.Customer = '%(source_customer_code)s';