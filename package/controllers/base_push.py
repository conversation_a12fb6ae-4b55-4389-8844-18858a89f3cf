import json
from builtins import open, float, int, str, len, round, Exception, getattr
import os
import time
import imp
import subprocess
import locale
import datetime
import clr
import base64
import hashlib
import xml.etree.ElementTree as etree
from ..shared import pastel_evo as pastel
from ..shared import utils
from collections import OrderedDict
from copy import deepcopy
import collections

packageDir = os.path.join(os.path.realpath(os.path.join(os.getcwd(),
    os.path.dirname(__file__))), os.pardir)

class Api:
    _indent = False

    _configDir = "config"
    _config = None

    def _setConfig(self):
        params = self._getParams()
        test = False
        if "test" in params:
            test = params["test"] == "true"
        if test:
            configFile = open(
                os.path.join(packageDir, self._configDir, "test", "config.json"), "r")
            self._config = json.loads(configFile.read())
        else:
            configFile = open(os.path.join(packageDir, self._configDir, "config.json"), "r")
            self._config = json.loads(configFile.read())
        configFile.close()

    # App will override this
    def _getParams(self):
        return {}

    # App will override this
    def _payload(self):
        return {}

    def _getDbPath(self):
        return os.path.join(packageDir, self._configDir, self._config["dbName"])

    def _checkInstall(self):
        dbPath = self._getDbPath()
        table_results = []
        index_results = []

        with utils.openSqliteConn(self._getDbPath()) as cursor:
            if os.path.isfile(dbPath):
                sql = "select * from sqlite_master where type='table';"
                cursor.execute(sql)
                table_results = cursor.fetchall()

                sql = "select * from sqlite_master where type='index';"
                cursor.execute(sql)
                index_results = cursor.fetchall()

        # Create tables
        if len(table_results) == 0:
            self.createTables()

        # Create indexes
        if len(index_results) == 0:
            self.createIndexes()

    _imageIndex = {
        "source_image_code": 0,
        "source_product_code": 1,
        "source_variant_code": 2,
        "hash": 3,
        "sync_token": 4,
        "modified": 5,
        "filename": 6
    }

    _productIndex = {
        "source_product_code": 0,
        "source_variant_code": 1,
        "hash": 2,
        "sync_token": 3,
        "modified": 4
    }

    def createTables(self):
        self._setConfig()
        with utils.openSqliteConn(self._getDbPath(), commit=True) as cursor:
            # If not installed then create a new sqlite database
            cursor.execute(
                "create table product (source_product_code, source_variant_code, hash, sync_token, modified)")
            cursor.execute(
                "create table customer (source_customer_code, hash, sync_token, modified)")
            cursor.execute(
                "create table image (source_image_code, source_product_code, source_variant_code, hash, sync_token, modified, filename)")
            cursor.execute(
                "create table meta (key, value)")

        response = {
            "status": True,
            "data": {
                "Create Tables": True
            }
        }
        return json.dumps(response, indent=self._indent)

    def createIndexes(self):
        self._setConfig()
        with utils.openSqliteConn(self._getDbPath(), commit=True) as cursor:
            # product table indexes
            cursor.execute(
                "CREATE INDEX idx_product_source_product_code_source_variant_code ON product(`source_product_code`,`source_variant_code`)")
            cursor.execute(
                "CREATE INDEX idx_product_sync_token ON product(`sync_token`)")

            # customer table indexes
            cursor.execute(
                "CREATE INDEX idx_customer_source_customer_code ON customer(`source_customer_code`)")
            cursor.execute(
                "CREATE INDEX idx_customer_sync_token ON customer(`sync_token`)")

            # image table indexes
            cursor.execute(
                "CREATE INDEX idx_image_source_variant_code ON image(`source_variant_code`)")
            cursor.execute(
                "CREATE INDEX idx_image_sync_token ON image(`sync_token`)")

        response = {
            "status": True,
            "data": {
                "Indexed": True
            }
        }
        return json.dumps(response, indent=self._indent)

    def reindex(self):
        self._setConfig()
        self._dropIndexes()
        self.createIndexes()
        response = {
            "status": True,
            "data": {
                "Reindex": True
            }
        }
        return json.dumps(response, indent=self._indent)

    def _dropIndexes(self):
        with utils.openSqliteConn(self._getDbPath(), commit=True) as cursor:
            # product table indexes
            cursor.execute(
                "DROP INDEX IF EXISTS idx_product_source_product_code_source_variant_code")
            cursor.execute(
                "DROP INDEX IF EXISTS idx_product_sync_token")

            # customer table indexes
            cursor.execute(
                "DROP INDEX IF EXISTS idx_customer_source_customer_code")
            cursor.execute(
                "DROP INDEX IF EXISTS idx_customer_sync_token")

            # image table indexes
            cursor.execute(
                "DROP INDEX IF EXISTS idx_image_source_variant_code")
            cursor.execute(
                "DROP INDEX IF EXISTS idx_image_sync_token")



    def _getProductsPastel(self):
        path = os.path.join(packageDir, self._configDir, "getProductsPush.sql")
        f = self._open(path, "r")
        sql = f.read()
        f.close()
        return sql

    def _getImagesPastel(self):
        path = os.path.join(packageDir, self._configDir, "getImages.sql")
        if os.path.isfile(path):
            f = self._open(path, "r")
            sql = f.read()
            f.close()
            return sql
        else:
            return None

    def _getImagePastel(self):
        path = os.path.join(packageDir, self._configDir, "getImage.sql")
        if os.path.isfile(path):
            f = self._open(path, "r")
            sql = f.read()
            f.close()
            return sql
        else:
            return None

    _customerIndex = {
        "source_customer_code": 0,
        "hash": 1,
        "sync_token": 2,
        "modified": 3
    }

    def _transformCustomer(self, params, row, sync_token):
        """
        Given the result of getCustomer we return a dictionary
        with required structure ready to be converted to JSON.
        """

        source_customer = OrderedDict()
        source_customer["source"] = OrderedDict()
        source_customer["source"]["source_id"] = params["source_id"]
        source_customer["source"]["customer_active"] = row["customer_active"]
        source_customer["source"]["source_customer_code"] = row["source_customer_code"]
        source_customer["source"]["sync_token"] = sync_token

        customer = OrderedDict()
        company, first_name, last_name = self._parseCustomerName(row)
        customer["first_name"] = first_name
        customer["last_name"] = last_name
        customer["email"] = row["email"]
        if row["accepts_marketing"] == 1:
            customer["accepts_marketing"] = True
        else:
            customer["accepts_marketing"] = False
        customer["addresses"] = self._getCustomerAddresses(row, company, first_name, last_name)

        customer["price_tier"] = str(row["price_tier"])
        customer["qty_availability"] = str(row["qty_availability"])

        customer["meta"] = self._parseMetaFields(row)
        customer["segments"] = self._parseSegments(row)

        source_customer["customer"] = customer

        return source_customer

    @staticmethod
    def _getCustomerAddresses(row, company, first_name, last_name):
        customer_addresses = []

        # Parse address xml
        if "xml_addresses" in row and row["xml_addresses"] is not None:
            root = etree.fromstring(row["xml_addresses"])
            for child in root:
                address = collections.OrderedDict()
                address["type"] = child.find("type").text
                address["address_code"] = child.find("address_code").text
                address["address1"] = child.find("address1").text
                address["address2"] = child.find("address2").text
                address["city"] = child.find("city").text
                address["country"] = child.find("country").text
                address["country_code"] = child.find("country_code").text
                address["province"] = child.find("province").text
                address["province_code"] = child.find("province_code").text
                address["zip"] = child.find("zip").text
                address["company"] = child.find("company").text
                address["first_name"] = child.find("first_name").text
                address["last_name"] = child.find("last_name").text
                address["phone"] = child.find("phone").text
                customer_addresses.append(address)
        else:
            address = collections.OrderedDict()
            address["address1"] = row["address.address1"]
            address["address2"] = row["address.address2"]
            address["city"] = row["address.city"]
            address["country"] = row["address.country"]
            address["country_code"] = row["address.country_code"]
            address["province"] = row["address.province"]
            address["province_code"] = row["address.province_code"]
            address["zip"] = row["address.zip"]
            address["company"] = company
            address["first_name"] = first_name
            address["last_name"] = last_name
            address["phone"] = row["address.phone"]
            customer_addresses.append(address)

        return customer_addresses

    @staticmethod
    def _getImagesPastelLegacy():
        # For backward compatibility
        # Find all columns with data_type = 'image' in SQL Server:
        # http://stackoverflow.com/a/9678320/639133
        # Pastel evolution supports multiple images per product.
        # To avoid duplicating the white list of image types just filter out bitmaps.
        # Ignore images greater than 1024 * 1024 bytes (1MB)
        return '''select * from (
            select
            row_number() over ( order by idInvImage ) as n,
            count(*) over()  as 'total_rows',
            i.idInvImage     as 'id_inv_image',
            i.nInvImage      as 'n_inv_image',
            st.Code          as 'source_variant_code',
            i.cInvImageType  as 'c_inv_image_type',
            i.cInvImageDesc  as 'c_inv_image_desc'
            from StkItem st
            join _etblInvImages i on st.StockLink = i.iStockLink
            where st.Code = ?
            and i.cInvImageType != 'TBitmap'
            and datalength(i.nInvImage) <= 1024 * 1024
            ) as rows
            where
            n > ?
            and n <= ?
            '''


    def _countProductsPastel(self):
        path = os.path.join(packageDir, self._configDir, "countProducts.sql")
        f = self._open(path, "r")
        sql = f.read()
        f.close()
        return sql

    def _getProductPastel(self):
        path = os.path.join(packageDir, self._configDir, "getProduct.sql")
        f = self._open(path, "r")
        sql = f.read()
        f.close()
        return sql

    def _getProductBySKUPastel(self):
        path = os.path.join(packageDir, self._configDir, "getProductBySKU.sql")
        f = self._open(path, "r")
        sql = f.read()
        f.close()
        return sql

    def _customOdbc(self, fileName=None):
        if not fileName:
            fileName = "custom.sql"
        path = os.path.join(packageDir, self._configDir, fileName)
        f = self._open(path, "r")
        sql = f.read()
        f.close()
        return sql


    def _getOrderPastel(self):
        path = os.path.join(packageDir, self._configDir, "getOrder.sql")
        f = self._open(path, "r")
        sql = f.read()
        f.close()
        return sql

    def _getDefaultLineItemWarehousePastel(self):
        path = os.path.join(packageDir, self._configDir, "getLineItemWarehouse.sql")
        if os.path.exists(path) == False:
            return False
        f = self._open(path, "r")
        sql = f.read()
        f.close()
        return sql

    def _getAvailableLotsPastel(self):
        path = os.path.join(packageDir, self._configDir, "getAvailableLots.sql")
        f = self._open(path, "r")
        sql = f.read()
        f.close()
        return sql

    @staticmethod
    def _getImagesLocal():
        return '''
              select * from image where sync_token > ?
              order by sync_token asc limit ?
            '''


    def _getProductsLocal(self):
        return '''
          select * from product where sync_token > ?
          order by sync_token asc limit ?
        '''

    def _getProductLocal(self):
        return '''
          select * from product
          where sync_token > ?
          and source_product_code = ?
          order by sync_token asc
        '''

    """
    Log message to file
    """
    def _logMessageToFile(self, filePath, msg):
        self._setConfig()

        if "debug" in self._config and self._config["debug"] == 1:
            msg = utils.getTimestamp() + " " + msg + "\n"
            text_file = open(filePath, "a")
            text_file.write(str(msg))
            text_file.close()

    def _updateHash(self, row, sqliteCursor):
        rowHash = utils.getHash(json.dumps(row))

        # Compare hash to existing row
        hashIndex = 0
        sql = "select hash from product where source_product_code = ?" + \
              " and source_variant_code = ?"
        sqliteCursor.execute(
            sql, ((row["source_product_code"], row["source_variant_code"]))
        )
        oldProduct = sqliteCursor.fetchone()  # Assuming there is only one match

        # Fetch the last sync_token
        syncTokenIndex = 0
        sql = '''
          select sync_token from product
          order by sync_token desc limit 1
        '''
        sqliteCursor.execute(sql)
        lastModified = sqliteCursor.fetchone()
        if lastModified is None:
            sync_token = 1
        else:
            sync_token = int(lastModified[syncTokenIndex]) + 1

        if oldProduct is None:
            sql = "insert into product values (?, ?, ?, ?, ?)"
            sqliteCursor.execute(
                sql, ((
                          row["source_product_code"],
                          row["source_variant_code"],
                          rowHash,
                          sync_token,
                          utils.getTimestamp()
                      ))
            )

        elif oldProduct[hashIndex] != rowHash:
            # Update if hash has changed
            sql = "update product set" \
                  " hash = ?," + \
                  " modified = ?," \
                  " sync_token = ?" \
                  " where" + \
                  " source_product_code = ?" + \
                  " and source_variant_code = ?"
            sqliteCursor.execute(
                sql,
                ((
                     rowHash,
                     utils.getTimestamp(),
                     sync_token,
                     row["source_product_code"],
                     row["source_variant_code"]
                 ))
            )


    def _imageHashChanged(self, row, encoded, sqliteCursor):
        """
        Update image hash in local database.
        Return true if a row was inserted or updated, otherwise return false.
        """
        # Include source_variant_code in hash,
        # in case the same picture is used for multiple products.
        imageHash = utils.getHash("{} {}".format(encoded, row["source_variant_code"]))

        # Compare hash to existing row
        hashIndex = 0
        sql = "select hash from image where source_variant_code = ?"
        sqliteCursor.execute(
            sql, (row["source_variant_code"],)
        )
        oldImage = sqliteCursor.fetchone()  # Assuming there is only one match

        if oldImage is None:
            sql = "insert into image values (?, ?, ?)"
            sqliteCursor.execute(
                sql, ((
                          row["source_variant_code"],
                          imageHash,
                          utils.getTimestamp()
                      ))
            )
            return True

        elif oldImage[hashIndex] != imageHash:
            # Update if hash has changed
            sql = "update image set" \
                  " hash = ?," + \
                  " modified = ?" \
                  " where" + \
                  " source_variant_code = ?"
            sqliteCursor.execute(
                sql,
                ((
                     imageHash,
                     utils.getTimestamp(),
                     row["source_variant_code"]
                 ))
            )
            return True

        else:
            return False


    def _transformImage(self, params, result, sync_token):
        image_hash = hashlib.md5(result["image"]).hexdigest()
        encoded = base64.b64encode(result["image"])
        encoded = encoded.decode("utf-8")

        source_image = OrderedDict()
        source_image["source"] = OrderedDict()
        source_image["image"] = OrderedDict()

        source_image["source"]["source_id"] = params["source_id"]
        source_image["source"]["source_product_code"] = str(result["source_product_code"])
        source_image["source"]["source_variant_code"] = str(result["source_variant_code"])
        source_image["source"]["sync_token"] = sync_token

        source_image["image"]["image_id"] = result["image_id"]
        source_image["image"]["action"] = 'I'
        source_image["image"]["hash"] = image_hash
        source_image["image"]["image"] = encoded

        return source_image


    def _transformProduct(self, params, result, sync_token):
        """
        Given the result of getProduct we return a dictionary
        with required structure ready to be converted to JSON.
        """
        source_product = OrderedDict()
        source_product["source"] = OrderedDict()
        source_product["product"] = OrderedDict()
        source_product["product"]["variants"] = OrderedDict()

        source_product = self._beforeProductTransform(params, result, source_product)

        source_product["source"]["source_id"] = params["source_id"]
        source_product["source"]["product_active"] = result["product_active"]
        source_product["source"]["source_product_code"] = str(result["source_product_code"])
        source_product["source"]["sync_token"] = sync_token

        source_product["product"]["options"] = []
        source_product["product"]["body_html"] = result["body_html"]
        source_product["product"]["collection"] = result["collection"]
        source_product["product"]["product_type"] = result["product_type"]
        source_product["product"]["tags"] = str(result["tags"]).lower()
        source_product["product"]["title"] = result["title"]
        source_product["product"]["vendor"] = result["vendor"]
        source_product["product"]["meta"] = self._parseMetaFields(result)

        source_product["product"]["variants"]["source_variant_code"] = str(result["source_variant_code"])
        # ensure backward compatible
        if "variants.sku" in result:
            source_product["product"]["variants"]["sku"] = str(result["variants.sku"])
        else:
            source_product["product"]["variants"]["sku"] = str(result["source_variant_code"])
        source_product["product"]["variants"]["barcode"] = result["variants.barcode"]
        source_product["product"]["variants"]["inventory_management"] = result["variants.inventory_management"]

        # For the weight, check for "variants.weight" key first, then check for "variants.grams"
        # "variants.weight" seems to be standard
        weight = 0
        if "variants.weight" in result:
            weight = result["variants.weight"]
        elif "variants.grams" in result:
            weight = result["variants.grams"]
        source_product["product"]["variants"]["grams"] = int(weight)

        source_product = self._parsePriceTiers(result, source_product)
        source_product = self._afterPriceTiers(result, source_product)
        source_product = self._parseQtyAvailability(result, source_product)
        source_product = self._parseOptions(result, source_product)

        return self._afterProductTransform(params, result, source_product)

    def _beforeProductTransform(self, params, result, source_product):
        """
        Hook method that gets called before a product has been transformed.
        For default behavior just return the passed "source_product"
        """
        return source_product

    def _afterProductTransform(self, params, result, source_product):
        """
        Hook method that gets called after a product has been transformed.
        For default behavior just return the passed "source_product"
        """
        return source_product

    def _afterPriceTiers(self, result, source_product):
        """
        Hook method that gets called after price tiers have been parsed..
        For default behavior just return the passed "source_product"
        """
        return source_product

    @staticmethod
    def _parseOptions(row, source_product):
        """
        Parses the optionX fields in the query result.
        This uses either the old format of "variants.optionX" or the newer
        version of key/value pairs in the form "variants.optionX_name" and "variants.optionX_value"
        """
        source_product_result = source_product

        # Test and add the 3 key/value pairs for options
        optionIndex = 1
        options = []
        for i in range(1, 4):
            nameKey = 'variants.option{}_name'.format(i)
            valueKey = 'variants.option{}_value'.format(i)
            # Add this option to product if both key/value exists and is not empty
            if nameKey in row and row[nameKey] is not None and str(row[nameKey]).strip() != '' and \
                    valueKey in row and row[valueKey] is not None and str(row[valueKey]).strip() != '':
                option = OrderedDict()
                option["name"] = row[nameKey]
                option["position"] = '{}'.format(optionIndex)
                option["value"] = "undefined"
                options.append(option)

                optionKey = 'option{}'.format(optionIndex)
                source_product_result["product"]["variants"][optionKey] = row[valueKey]
                optionIndex += 1
            else:
                # Fall back to older "optionX" version
                key = 'variants.option{}'.format(i)
                if key in row and row[key] is not None and str(row[key]).strip() != '':
                    source_product['product']['variants']['option{}'.format(i)] = row[key]
        source_product_result["product"]["options"] = options
        return source_product_result

    @staticmethod
    def _parsePriceTiers(row, source_product):
        import xml.sax.saxutils as saxutils

        """
        Parses the "csv_price_tiers" field.
        If we have a CSV list of prices use that, otherwise use the default.
        """
        source_product_result = source_product

        source_product_result["product"]["variants"]["price_tiers"] = []

        # Add retail price tier
        if "variants.retail_price" in row:
            price_tier = OrderedDict()
            price_tier["tier"] = "retail"
            price_tier["price"] = row["variants.retail_price"]
            source_product_result["product"]["variants"]["price_tiers"].append(price_tier)

        # Add wholesale price tier if it exists
        if "variants.wholesale_price" in row:
            price_tier = OrderedDict()
            price_tier["tier"] = "wholesale"
            price_tier["price"] = row["variants.wholesale_price"]
            source_product_result["product"]["variants"]["price_tiers"].append(price_tier)

        if "csv_price_tiers" in row and row["csv_price_tiers"] is not None:
            default_price = 0
            # The stuff function used in the sql queries
            # will encode special characters as html entities,
            # for example, "&" becomes "&amp;".
            # Call html.unescape to convert back to special characters
            prices = saxutils.unescape(row["csv_price_tiers"])
            prices = prices.split(",")
            for priceStr in prices:
                priceItem = priceStr.split("|")
                if len(priceItem) == 2 and priceItem[0].strip() != "":
                    tier = priceItem[0]
                    # Throw away everything after the second decimal
                    price = float("{0:.2f}".format(float(priceItem[1])))
                    if tier == row["default_price_tier"]:
                        default_price = price
                    price_tier = OrderedDict()
                    price_tier["tier"] = tier
                    price_tier["price"] = price
                    source_product_result["product"]["variants"]["price_tiers"].append(price_tier)
            source_product_result["product"]["variants"]["price"] = default_price
        else:
            if "variants.retail_price" in row:
                source_product_result["product"]["variants"]["price"] = row["variants.retail_price"]
            else:
                source_product_result["product"]["variants"]["price"] = 0

        return source_product_result

    @staticmethod
    def _parseQtyAvailability(row, source_product):
        import xml.sax.saxutils as saxutils
        import math

        """
        Parses the "csv_qty_availability" field.
        If we have a CSV list of warehouse quantities use that, otherwise use the default.
        """
        source_product_result = source_product

        if "csv_qty_availability" in row and row["csv_qty_availability"] is not None:
            source_product_result["product"]["variants"]["qty_availability"] = []
            default_qty = 0
            quantities = saxutils.unescape(row["csv_qty_availability"])
            quantities = quantities.split(",")
            for qtyStr in quantities:
                qtyItem = qtyStr.split("|")
                if len(qtyItem) == 2 and qtyItem[0].strip() != "":
                    description = qtyItem[0]
                    # Qty must be an integer
                    qty = math.trunc(float(qtyItem[1]))
                    if description == row["default_qty_availability"]:
                        default_qty = qty
                    availability = OrderedDict()
                    availability["qty"] = qty
                    availability["description"] = description
                    source_product_result["product"]["variants"]["qty_availability"].append(availability)
            source_product_result["product"]["variants"]["qty"] = default_qty
        else:
            if "variants.qty" in row:
                source_product_result["product"]["variants"]["qty"] = row["variants.qty"]
            else:
                source_product_result["product"]["variants"]["qty"] = 0

        return source_product_result

    @staticmethod
    def _parseCustomerName(row):
        first_name = row["first_name"]
        last_name = row["last_name"]
        company = row["address.company"]
        if first_name is None and last_name is None:
            # Note that company could also be None
            first_name = company
        return company, first_name, last_name

    @staticmethod
    def _parseSegments(row):
        segments = []

        # Anything that starts with segment| gets converted into a segment
        # Expecting the following fields for Segments:
        # owner|type|key|operator|value
        # The sql on query must follow segment structure
        # 'value'  as "segment|source|products|meta_rep_id|equal"
        for key in row:
            if key.startswith("segment|"):
                parts = key.split("|")
                segment = OrderedDict()
                segment["owner"] = parts[1].strip()
                segment["type"] = parts[2].strip()
                segment["key"] = parts[3].strip()
                segment["operator"] = parts[4].strip()
                segment["value"] = row[key]
                segments.append(segment)

        return segments

    @staticmethod
    def _parseMetaFields(row):
        import xml.sax.saxutils as saxutils
        metaFields = []

        # Anything that starts with csv gets converted into meta
        # e.g. csv_special_prices will convert to:
        # meta_special_prices_{key} = {value}
        # ignore special keywords
        for key in row:
            if key.startswith("csv_") and \
                    not key.startswith("csv_price_tiers") and \
                    not key.startswith("csv_contract_") and \
                    not key.startswith("csv_qty_availability"):
                # Value from database might be null
                if row[key] is not None:
                    title = key.replace("csv_", "")
                    values = saxutils.unescape(row[key]).split(",")
                    for value in values:
                        valueItem = value.split("|")
                        if len(valueItem) == 2 and valueItem[0].strip() != "":
                            left = valueItem[0].strip()
                            right = float(valueItem[1])
                            meta = OrderedDict()
                            meta["key"] = str(title + "_{}".format(left))
                            meta["value"] = str(right)
                            metaFields.append(meta)

        # Parse special prices
        # TODO make sure no one uses this and remove
        if "csv_special_prices" in row and row["csv_special_prices"] is not None:
            prices = saxutils.unescape(row["csv_special_prices"])
            prices = prices.split(",")
            for price in prices:
                priceItem = price.split("|")
                if len(priceItem) == 2 and priceItem[0].strip() != "":
                    card_code = priceItem[0].strip()
                    discount = float(priceItem[1])
                    if discount > 0:
                        meta = OrderedDict()
                        meta["key"] = str("special_price_{}".format(card_code))
                        meta["value"] = str(discount)
                        metaFields.append(meta)
        # Meta
        for key in row:
            if key.startswith("meta_"):
                # Value from database might be null
                if row[key] is not None:
                    meta = OrderedDict()
                    meta["key"] = str(key[5:])  # Remove "meta_" from key
                    meta["value"] = saxutils.unescape(str(row[key]))
                    metaFields.append(meta)

            # Contract pricing
            if key.startswith("csv_contract_"):
                # Value from database might be null
                if row[key] is not None:
                    # Example row value:
                    # order_0|entity_customer|key_item_group_code|value_{}|type_discount~10,
                    # order_0|entity_product|key_item_group_code|value_{}|type_discount~20
                    # Output:
                    # [
                    #   {
                    #       key: order_0|entity_customer|key_item_group_code|value_abc|type_discount
                    #       value: 10
                    #   },
                    #   {
                    #       key: order_0|entity_customer|key_item_group_code|value_xyz|type_discount
                    #       value: 20
                    #   }
                    # ]
                    contracts = saxutils.unescape(row[key]).split(",")
                    for contract in contracts:
                        contractItem = contract.split("~")
                        if len(contractItem) == 2 and contractItem[0].strip() != "":
                            meta_key = contractItem[0].strip()
                            meta_value = contractItem[1]  # discount / fixed price
                            meta = collections.OrderedDict()
                            meta["key"] = str(meta_key)
                            meta["value"] = str(meta_value)
                            metaFields.append(meta)

        return metaFields

    def _transformOrder(self, params, rows):
        i = 0
        source_order = OrderedDict();

        for row in rows:
            # Read order details from the first line item
            if i == 0:
                source_order["sources"] = [OrderedDict()]
                source_order["sources"][0]["source_id"] = params["source_id"]
                source_order["sources"][0]["source_order_code"] = row.source_order_code.strip()
                source_order["sources"][0]["source_customer_code"] = row.source_client_code.strip()

                source_order["source_order"] = OrderedDict()
                source_order["source_order"]["id"] = row.id.strip()
                source_order["source_order"]["notes"] = row.notes1.strip() + "\\n" + \
                    row.notes2.strip() + "\\n" + \
                    row.notes3.strip()
                source_order["source_order"]["customer"] = OrderedDict()
                source_order["source_order"]["customer"]["first_name"] = getattr(row, "customer.first_name").strip()
                source_order["source_order"]["customer"]["last_name"] = getattr(row, "customer.last_name").strip()
                source_order["source_order"]["customer"]["email"] = getattr(row, "customer.email").strip()

                billing_address = OrderedDict()
                billing_address["address1"] = getattr(row, "billing_address.address1").strip()
                billing_address["address2"] = getattr(row, "billing_address.address2").strip()
                billing_address["city"] = getattr(row, "billing_address.city").strip()
                billing_address["company"] = getattr(row, "billing_address.company").strip()
                billing_address["country"] = getattr(row, "billing_address.country").strip()
                billing_address["first_name"] = getattr(row, "billing_address.first_name").strip()
                billing_address["last_name"] = getattr(row, "billing_address.last_name").strip()
                billing_address["phone"] = getattr(row, "billing_address.phone").strip()
                billing_address["province"] = getattr(row, "billing_address.province").strip()
                billing_address["zip"] = getattr(row, "billing_address.zip").strip()
                billing_address["country_code"] = getattr(row, "billing_address.country_code").strip()
                source_order["source_order"]["customer"]["addresses"] = [billing_address]

                source_order["source_order"]["billing_address"] = billing_address

                shipping_address = OrderedDict()
                shipping_address["address1"] = getattr(row, "shipping_address.address1").strip()
                shipping_address["address2"] = getattr(row, "shipping_address.address2").strip()
                shipping_address["city"] = getattr(row, "shipping_address.city").strip()
                shipping_address["company"] = getattr(row, "shipping_address.company").strip()
                shipping_address["country"] = getattr(row, "shipping_address.country").strip()
                shipping_address["first_name"] = getattr(row, "shipping_address.first_name").strip()
                shipping_address["last_name"] = getattr(row, "shipping_address.last_name").strip()
                shipping_address["phone"] = getattr(row, "shipping_address.phone").strip()
                shipping_address["province"] = getattr(row, "shipping_address.province").strip()
                shipping_address["zip"] = getattr(row, "shipping_address.zip").strip()
                shipping_address["country_code"] = getattr(row, "shipping_address.country_code").strip()
                source_order["source_order"]["shipping_address"] = shipping_address

                source_order["source_order"]["line_items"] = []
                source_order["source_order"]["shipping_lines"] = []

            # Each additional row contains a line item
            line_item = OrderedDict()
            line_item["source_id"] = params["source_id"]
            line_item["sku"] = getattr(row, "line_item.sku").strip()
            line_item["title"] = getattr(row, "line_item.title").strip()
            line_item["price"] = getattr(row, "line_item.price")
            line_item["qty"] = getattr(row, "line_item.qty")
            line_item["code"] = "item"
            tax_line = OrderedDict()
            tax_line["price"] = getattr(row, "line_item.price")
            tax_line["rate"] = 14
            tax_line["title"] = "VAT"
            tax_line["code"] = "taxed"
            line_item["tax_lines"] = [tax_line]

            if line_item["sku"] == "SHIP001":
                source_order["source_order"]["shipping_lines"].append(line_item)
            else:
                source_order["source_order"]["line_items"].append(line_item)

            i += 1

        return source_order

    def reload(self):
        imp.reload(pastel)
        imp.reload(utils)
        response = {
            "status": True,
            "data": {
                "reload": True
            }
        }
        return json.dumps(response, indent=self._indent)

    # Do not allow writing to sqlite database using this function,
    # i.e. do not pass commit=True to utils.openSqliteConn.
    def runLocal(self):
        self._setConfig()
        params = self._getParams()
        with utils.openSqliteConn(self._getDbPath()) as cursor:
            response = utils.runSQLite(params, cursor)
            return json.dumps(response, indent=self._indent)

    def runCustom(self):
        self._setConfig()
        params = self._getParams()
        fileName = None
        if "fileName" in params:
            fileName = params["fileName"]
        if "limit" in params:
            limit = int(params["limit"])
        else:
            limit = 500

        # use sql in params otherwise use filename
        if "sql" in  params:
            sql = params["sql"]
        else:
            sql = self._customOdbc(fileName)

        if sql == "":
            return json.dumps({
                "status": False,
                "description": "Sql not given. Use query param 'fileName=xyz.sql' or 'sql=select * from...'",
                "line": utils.lineNo()
            }, indent=self._indent)

        params = {
            "sql": sql,
            "limit": limit
        }
        with pastel.openConn(self._config["dsn"]) as cursor:
            response = utils.runODBC(params, cursor)
            response["sql"] = sql
            return json.dumps(response)


    # Do not allow writing to database using this function,
    # i.e. do not pass commit=True to pastel.openConn.
    # def _run(self):
    #     self._setConfig()
    #     params = self._getParams()
    #     with pastel.openConn(self._config["dsn"]) as cursor:
    #         response = utils.runODBC(params, cursor)
    #         return json.dumps(response, indent=self._indent)

    def log(self):
        params = self._getParams()
        response = utils.log(params, packageDir)
        return json.dumps(response, indent=self._indent)

    def auditReset(self):
        t1 = time.time()
        self._setConfig()
        dbPath = self._getDbPath()
        if os.path.isfile(dbPath):
            os.remove(dbPath)
        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {}
        }
        return json.dumps(response, indent=self._indent)

    def auditResetCustomers(self):
        """
        Remove all customers from the local audit database
        """
        t1 = time.time()
        self._setConfig()
        with utils.openSqliteConn(self._getDbPath(), commit=True) as sqliteCursor:
            sql = "delete from customer"
            sqliteCursor.execute(sql)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {}
        }
        return json.dumps(response, indent=self._indent)

    def auditResetImages(self):
        """
        Remove all images from the local audit database
        """
        t1 = time.time()
        self._setConfig()
        with utils.openSqliteConn(self._getDbPath(), commit=True) as sqliteCursor:
            sql = "DROP TABLE image"
            sqliteCursor.execute(sql)

        # TODO delete from meta where `key` in ('audit_offset_image', 'sync_token_image')?

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {}
        }
        return json.dumps(response, indent=self._indent)

    def getImages(self):
        t1 = time.time()
        self._setConfig()

        self._checkInstall()

        params = self._getParams()
        response = utils.checkRequiredParams(
            ["source_product_code", "sync_token", "source_id", "current_iteration", "limit"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        current_iteration = int(params["current_iteration"])
        limit = int(params["limit"])

        # Only fetch images if the product has changed since sync_token
        with utils.openSqliteConn(self._getDbPath(), commit=True) as sqliteCursor:
            sql = self._getProductLocal()
            if "sourceProductCodeType" not in self._config:
                self._config["sourceProductCodeType"] = "s"
            sqliteCursor.execute(sql, (
                int(params["sync_token"]),
                utils.castSqlParamByType(
                    params["source_product_code"],
                    self._config["sourceProductCodeType"]
                )
            ))

            rows = []
            total_rows = 0
            # We only need to know if there was a change on product
            sourceVariantCodeIndex = 1
            sqliteRow = sqliteCursor.fetchone()
            while sqliteRow is not None:
                with pastel.openConn(self._config["dsn"]) as cursor:
                    source_variant_code = sqliteRow[sourceVariantCodeIndex]
                    sql = self._getImagesPastel()
                    if sql is None:
                        sql = self._getImagesPastelLegacy()
                        cursor.execute(sql, (
                            source_variant_code,
                            (current_iteration - 1) * limit,
                            current_iteration * limit
                        ))
                    else:
                        sql = utils.bindSqlParams(sql, {
                            # source_variant_code could be a string or a number
                            "source_variant_code": utils.castSqlParam(
                                sql, source_variant_code, "source_variant_code"
                            ),
                            "lower_limit": (current_iteration - 1) * limit,
                            "upper_limit": current_iteration * limit,
                        })
                        cursor.execute(sql)
                    imageRows = utils.getRowsODBC(cursor, 1)

                    while len(imageRows) > 0:
                        row = imageRows[0]
                        total_rows = row["total_rows"]

                        # We only return the image if the hash has changed,
                        # i.e. sync_token only applies to products.
                        # To test or re-fetch the same image we need a flag.
                        skipImageHash = False
                        if "skip_image_hash" in params:
                            skipImageHash = params["skip_image_hash"] == "true"

                        # Check hash against local db
                        # If skipImageHash is True we still have to call _imageHashChanged,
                        # otherwise we will fetch the image twice, on add and for the first update.
                        if self._imageHashChanged(row, row["n_inv_image"], sqliteCursor) \
                            or skipImageHash:
                            # The hash has changed or image not found
                            image = OrderedDict()
                            image["image"] = OrderedDict()
                            encoded = base64.b64encode(row["n_inv_image"])
                            encoded = encoded.decode("utf-8")
                            image["image"]["image"] = encoded
                            # Should actually be called source_image_code,
                            # lets try to be backward compatible...
                            image["image"]["image_id"] = row["c_inv_image_desc"]
                            image["image"]["action"] = "I" # Always insert
                            image["source"] = OrderedDict()

                            image["source"]["sync_token"] = True
                            image["source"]["source_id"] = params["source_id"]
                            image["source"]["source_product_code"] = params["source_product_code"]
                            image["source"]["source_variant_code"] = source_variant_code
                            rows.append(image)

                        imageRows = utils.getRowsODBC(cursor, 1)

                sqliteRow = sqliteCursor.fetchone()


        # Tell stock2shop to stop fetching when we've fetched all the images.
        # If rows is empty then stock2shop will also stop fetching,
        # so setting sync_token = False below isn't really necessary.
        # It is left in for completeness and to document how the code behaves.
        if current_iteration * limit > total_rows and len(rows) > 0:
            rows[len(rows) - 1]["source"]["sync_token"] = False

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {
                "source_images": rows,
            }
        }
        return json.dumps(response, indent=self._indent)

    def auditImagesBatch(self):
        """
        This function kicks off a process that reads all the images
        filenames in the Evo database and updates the hashes in sqlite.
        If the sqlite hash differs the time stamp the image row is updated.
        getImagesBatch can then check the sync token against sqlite.
        """
        t1 = time.clock()
        images = 0
        imagePath = ""
        notFound = 0
        invalidType = 0
        uploads = 0
        self._setConfig()

        self._checkInstall()
        params = self._getParams()

        response = utils.checkRequiredParams(
            ["audit_lower_limit", "audit_upper_limit"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        error_log_path = os.path.join(packageDir, self._configDir, "error.log")

        # Read images from Evo
        with pastel.openConn(self._config["dsn"]) as cursor:
            sql = self._getImagesPastel()
            sql = utils.bindSqlParams(sql, {
                "lower_limit": int(params["audit_lower_limit"]),
                "upper_limit": int(params["audit_upper_limit"])
            })
            cursor.execute(sql)
            rows = utils.getRowsODBC(cursor, 1)

            # Get local connection
            if len(rows) > 0:
                with utils.openSqliteConn(self._getDbPath(), commit=True) as sqliteCursor:
                    while len(rows) > 0:
                        source_product_code = rows[0]['source_product_code']
                        source_variant_code = rows[0]['source_variant_code']
                        source_image_code = rows[0]['source_image_code']

                        # Get new sync token to use
                        sql = "select sync_token from image order by sync_token desc limit 1"
                        sqliteCursor.execute(sql)
                        lastModified = sqliteCursor.fetchone()
                        sync_token = 1
                        if lastModified is not None:
                            sync_token = int(lastModified[0]) + 1

                        sqliteCursor.execute(
                            "select count(*) from image where source_image_code = ?", (source_image_code,)
                        )
                        countImages = sqliteCursor.fetchone()[0]

                        if countImages <= 0:
                            # This image will be pushed
                            # Write the entry even if file > 1MB, so we don't handle it again next time
                            sql = "insert into image (source_image_code, source_product_code, source_variant_code, modified, sync_token, filename) values (?, ?, ?, ?, ?, ?)"
                            sqliteCursor.execute(
                                sql, ((
                                    source_image_code,
                                    source_product_code,
                                    source_variant_code,
                                    utils.getTimestamp(),
                                    sync_token,
                                    ''
                                ))
                            )
                            uploads += 1
                        elif countImages == 1:
                            # TODO: When it was already found in audit file, update the hash
                            pass

                        images += 1

                        # else:
                        #     # TODO: Find a way to report here that image doesn't exist on machine
                        #     notFound += 1
                        #     pass

                        rows = utils.getRowsODBC(cursor, 1)

        t2 = time.clock()
        response = {
            "status": True,
            "timer": '{} ms'.format((t2 - t1) * 1000.0),
            "data": {
                "images_audited": images,
                "images_uploaded": uploads,
                "image_path": imagePath,
                "image_not_found": notFound,
                "image_invalid": invalidType
            }
        }
        return json.dumps(response, indent=self._indent)

    def getImagesBatch(self):
        t1 = time.time()
        self._setConfig()

        params = self._getParams()

        response = utils.checkRequiredParams(
            ["sync_token", "limit"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        # Test source in the s2s console does not pass in current_iteration
        audit_images = False
        if "current_iteration" not in params:
            params["current_iteration"] = "1"

        # Only audit images on the first iteration
        if params["current_iteration"] == "1":
            response = self.auditImagesBatch()
            response = json.loads(response)
            if not response["status"]:
                return json.dumps(response, indent=self._indent)
            audit_images = True

        error_log_path = os.path.join(packageDir, self._configDir, "error.log")

        with pastel.openConn(self._config["dsn"]) as cursor:
            with utils.openSqliteConn(self._getDbPath()) as sqliteCursor:
                sql = self._getImagesLocal()
                sqliteCursor.execute(sql, (
                    int(params["sync_token"]), int(params["limit"])
                ))

                rows = []
                auditRow = sqliteCursor.fetchone()
                if auditRow is not None and len(auditRow) < 1:
                    txt = "Img not found in audit [Token]: " + params["sync_token"]
                    self._logMessageToFile(error_log_path, txt)
                while auditRow is not None:

                    # Fetch image from Evo
                    sql = self._getImagePastel()
                    sql = utils.bindSqlParams(sql, {
                        "source_image_code": utils.castSqlParam(
                            sql, auditRow[self._imageIndex["source_image_code"]], "source_image_code"
                        )
                    })
                    cursor.execute(sql)
                    odbcRows = utils.getRowsODBC(cursor, 1)
                    if len(odbcRows) > 0:
                        rows.append(
                            self._transformImage(
                                params,
                                odbcRows[0],
                                auditRow[self._imageIndex["sync_token"]]
                            )
                        )
                    else:
                        txt = "Img not found in Evo: " + auditRow[self._imageIndex["source_variant_code"]]
                        self._logMessageToFile(error_log_path, txt)
                    auditRow = sqliteCursor.fetchone()

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {"source_images": rows},
            "audit_images": audit_images
        }
        return json.dumps(response, indent=self._indent)

    def getImage(self):
        self._setConfig()
        raise Exception("Not implemented")
        # t1 = time.clock()
        # self._setConfig()
        #
        # params = self._getParams()
        #
        # response = utils.checkRequiredParams(
        #     ["source_variant_code"],
        #     params
        # )
        # if response is not None:
        #     return json.dumps(response, indent=self._indent)
        #
        # with pastel.openConn(self._config["dsn"]) as cursor:
        #     sql = self._getImagePastel()
        #     sql = utils.bindSqlParams(sql, {
        #         "source_variant_code": str(params["source_variant_code"])
        #     })
        #     cursor.execute(sql)
        #     rows = utils.getRowsODBC(cursor, 1)
        #
        #     if len(rows) == 1:
        #         imageBase64 = base64.b64encode(rows[0]['n_inv_image'])
        #         imageFileName = rows[0]['c_inv_image_desc']
        #
        #         # path = os.path.join(imagePath, imageFileName)
        #
        #         size = 0
        #         if size > (1024 * 1024):
        #             response = {
        #                 "status": False,
        #                 "description": "Image '{}' is larger than limit of 1MB".format(imageFileName),
        #                 "line": utils.lineNo()
        #             }
        #             return json.dumps(response, indent=self._indent)
        #         else:
        #             imageHash = hashlib.md5(imageBase64).hexdigest()
        #             encoded = imageBase64.decode("utf-8")
        #
        #             source_image = OrderedDict()
        #             source_image["image"] = OrderedDict()
        #             source_image["image"]["hash"] = imageHash
        #             source_image["image"]["image"] = encoded
        #
        #             t2 = time.clock()
        #             response = {
        #                 "status": True,
        #                 "timer": '{} ms'.format((t2 - t1) * 1000.0),
        #                 "data": {"source_image": source_image}
        #             }
        #             return json.dumps(response, indent=self._indent)
        #     else:
        #         response = {
        #             "status": False,
        #             "description": "No image found for source_variant_code '{}'".format(str(params["source_variant_code"])),
        #             "line": utils.lineNo()
        #         }
        #         return json.dumps(response, indent=self._indent)
        #
        # response = {
        #     "status": False,
        #     "description": "No images selected",
        #     "line": utils.lineNo()
        # }
        # return json.dumps(response, indent=self._indent)

    def getWarehouseCodes(self):
        self._setConfig()
        params = {
            "sql": "select WhseLink, Code, Name, KnownAs from WhseMst",
            "limit": 5000
        }
        with pastel.openConn(self._config["dsn"]) as cursor:
            response = utils.runODBC(params, cursor)
            return json.dumps(response, indent=self._indent)

    def getBranchCodes(self):
        self._setConfig()
        params = {
            "sql": "select idBranch, cBranchCode, cBranchDescription from _etblBranch",
            "limit": 5000
        }
        with pastel.openConn(self._config["dsn"]) as cursor:
            response = utils.runODBC(params, cursor)
            return json.dumps(response, indent=self._indent)

    def getPriceLists(self):
        self._setConfig()
        params = {
            "sql": "select cName, cDescription, IDPriceListName from _etblPriceListName"
        }
        with pastel.openConn(self._config["dsn"]) as cursor:
            response = utils.runODBC(params, cursor)
            return json.dumps(response, indent=self._indent)

    def getStkItemColumns(self):
        self._setConfig()
        params = {
            "sql": "select column_name from [{}].information_schema.columns where table_name = N'StkItem'".format(
                self._config["sdkDbName"]
            ),
            "limit": 5000
        }
        with pastel.openConn(self._config["dsn"]) as cursor:
            response = utils.runODBC(params, cursor)
            return json.dumps(response, indent=self._indent)

    def getGroups(self):
        self._setConfig()
        params = {
            "sql": "select idGrpTbl, StGroup,Description from GrpTbl".format(
                self._config["sdkDbName"]
            ),
            "limit": 5000
        }
        with pastel.openConn(self._config["dsn"]) as cursor:
            response = utils.runODBC(params, cursor)
            return json.dumps(response, indent=self._indent)

    def getClientColumns(self):
        self._setConfig()
        params = {
            "sql": "select column_name from [{}].information_schema.columns where table_name = N'Client'".format(
                self._config["sdkDbName"]
            ),
            "limit": 5000
        }
        with pastel.openConn(self._config["dsn"]) as cursor:
            response = utils.runODBC(params, cursor)
            return json.dumps(response, indent=self._indent)

    def getOrderColumns(self):
        self._setConfig()
        params = {
            "sql": "select column_name from [{}].information_schema.columns where table_name = N'InvNum'".format(
                self._config["sdkDbName"]
            ),
            "limit": 5000
        }
        with pastel.openConn(self._config["dsn"]) as cursor:
            response = utils.runODBC(params, cursor)
            return json.dumps(response, indent=self._indent)

    def getCustomerGroups(self):
        self._setConfig()
        params = {
            "sql": "select idCliClass, Code,Description from CliClass".format(
                self._config["sdkDbName"]
            ),
            "limit": 5000
        }
        with pastel.openConn(self._config["dsn"]) as cursor:
            response = utils.runODBC(params, cursor)
            return json.dumps(response, indent=self._indent)

    def getSegments(self):
        self._setConfig()
        params = {
            "sql": "select top 100 * from _etblInvSegValue"
        }
        with pastel.openConn(self._config["dsn"]) as cursor:
            response = utils.runODBC(params, cursor)
            return json.dumps(response, indent=self._indent)

    def getTaxCodes(self):
        self._setConfig()
        params = {
            "sql": "select Code, Description, TaxRate from TaxRate"
        }
        with pastel.openConn(self._config["dsn"]) as cursor:
            response = utils.runODBC(params, cursor)
            return json.dumps(response, indent=self._indent)

    def getProjectCodes(self):
        self._setConfig()
        params = {
            "sql": "select ProjectCode, ProjectName, ActiveProject, ProjectDescription, MasterSubProject from Project"
        }
        with pastel.openConn(self._config["dsn"]) as cursor:
            response = utils.runODBC(params, cursor)
            return json.dumps(response, indent=self._indent)

    def getSalesRepresentatives(self):
        self._setConfig()
        params = {
            "sql": "select Code, Name from SalesRep"
        }
        with pastel.openConn(self._config["dsn"]) as cursor:
            response = utils.runODBC(params, cursor)
            return json.dumps(response, indent=self._indent)

    @staticmethod
    def _getCustomersLocal():
        return '''
        select * from customer where sync_token > ?
        order by sync_token asc limit ?
        '''

    def getCustomersBatch(self):
        t1 = time.time()
        self._setConfig()

        params = self._getParams()

        response = utils.checkRequiredParams(
            ["sync_token", "limit"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        audit_customers = False
        if "current_iteration" not in params:
            params["current_iteration"] = "1"

        # Only audit customers on the first iteration
        if params["current_iteration"] == "1":
            response = self.auditCustomersBatch()
            response = json.loads(response)
            if not response["status"]:
                return json.dumps(response, indent=self._indent)
            audit_customers = True

        with pastel.openConn(self._config["dsn"]) as cursor:
            with utils.openSqliteConn(self._getDbPath(), commit=True) as sqliteCursor:
                sql = self._getCustomersLocal()
                sqliteCursor.execute(sql, (
                    int(params["sync_token"]), int(params["limit"])
                ))

                rows = []
                auditRow = sqliteCursor.fetchone()
                while auditRow is not None:
                    sql = self._getCustomerPastel()
                    sql = utils.bindSqlParams(sql, {
                        "source_customer_code":
                            auditRow[self._customerIndex["source_customer_code"]]
                    })
                    cursor.execute(sql)
                    odbcRow = utils.getRowsODBC(cursor, 1)
                    if len(odbcRow) > 0:
                        rows.append(
                            self._transformCustomer(
                                params,
                                odbcRow[0],
                                auditRow[self._customerIndex["sync_token"]]
                            )
                        )
                    auditRow = sqliteCursor.fetchone()

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {"source_customers": rows},
            "audit_customers": audit_customers
        }
        return json.dumps(response, indent=self._indent)

    def getCustomer(self):
        """
        This functions uses getCustomer.sql to return the customer.
        It includes the raw data returned by the sql query
        as well as the transformed S2S customer
        """
        t1 = time.time()
        self._setConfig()

        params = self._getParams()

        response = utils.checkRequiredParams(
            ["source_customer_code"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        rows = []
        with pastel.openConn(self._config["dsn"]) as cursor:
            sql = self._getCustomerPastel()
            sql = utils.bindSqlParams(sql, {
                "source_customer_code": params["source_customer_code"],
            })
            cursor.execute(sql)
            result = utils.getRowsODBC(cursor, 1)
            if len(result) > 0:
                transformed_customer = self._transformCustomer(
                    params,
                    result[0],
                    0
                )
                transformed_customer["raw_query"] = result
                rows.append(transformed_customer)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {"source_customers": rows}
        }
        return json.dumps(response, indent=self._indent)

    def getProductBySKU(self):
        t1 = time.time()
        self._setConfig()
        params = self._getParams()

        response = utils.checkRequiredParams(["sku"], params)
        if response is not None:
            return json.dumps(response, indent=self._indent)

        rows = []
        with pastel.openConn(self._config["dsn"]) as cursor:
            sql = self._getProductBySKUPastel()
            sql = utils.bindSqlParams(sql, {
                "sku": str(params["sku"])
            })

            cursor.execute(sql)
            rows = utils.getRowsODBC(cursor, 1)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {"source_products": rows}
        }
        return json.dumps(response, indent=self._indent)

    def countProducts(self):
        t1 = time.time()
        self._setConfig()

        params = self._getParams()

        if "warehouse_code" not in params:
            params["warehouse_code"] = "n/a"

        with pastel.openConn(self._config["dsn"]) as cursor:
            sql = self._countProductsPastel()
            sql = utils.bindSqlParams(sql, {
                "warehouse_code": str(params["warehouse_code"])
            })

            cursor.execute(sql)
            rows = utils.getRowsODBC(cursor, 1)
            product_count = rows[0]["count"]

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {
                "count": product_count
            }
        }
        return json.dumps(response, indent=self._indent)

    def _countCustomersPastel(self):
        path = os.path.join(packageDir, self._configDir, "countCustomers.sql")
        f = self._open(path, "r")
        sql = f.read()
        f.close()
        return sql

    def _countImagesPastel(self):
        path = os.path.join(packageDir, self._configDir, "countImages.sql")
        f = self._open(path, "r")
        sql = f.read()
        f.close()
        return sql

    def countCustomers(self):
        t1 = time.time()
        self._setConfig()

        params = self._getParams()

        with pastel.openConn(self._config["dsn"]) as cursor:
            sql = self._countCustomersPastel()
            sql = utils.bindSqlParams(sql, params)

            cursor.execute(sql)
            rows = utils.getRowsODBC(cursor, 1)

        # "count" must be present and be an int,
        # it is used by push.exe
        data = OrderedDict()
        if "count" in rows[0]:
            data["count"] = int(rows[0]["count"])
        if "active" in rows[0]:
            data["active"] = int(rows[0]["active"])
        if "inactive" in rows[0]:
            data["inactive"] = int(rows[0]["inactive"])
        if "total" in rows[0]:
            data["total"] = int(rows[0]["total"])

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": data
        }
        return json.dumps(response, indent=self._indent)

    def countImages(self):
        t1 = time.time()
        self._setConfig()

        params = self._getParams()

        with pastel.openConn(self._config["dsn"]) as cursor:
            sql = self._countImagesPastel()
            sql = utils.bindSqlParams(sql, params)

            cursor.execute(sql)
            rows = utils.getRowsODBC(cursor, 1)

        # "count" must be present and be an int,
        # it is used by push.exe
        data = OrderedDict()
        data["count"] = int(rows[0]["count"])

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": data
        }
        return json.dumps(response, indent=self._indent)

    def getProduct(self):
        """
        This functions uses the grtProduct.sql to return the product.
        It also includes the raw data returned by the sql query as well as the transformed S2S product
        """
        t1 = time.time()
        self._setConfig()

        params = self._getParams()

        response = utils.checkRequiredParams(
            ["source_variant_code", "warehouse_code", "source_id"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        rows = []
        with pastel.openConn(self._config["dsn"]) as cursor:
            sql = self._getProductPastel()
            sql = utils.bindSqlParams(sql, {
                "source_variant_code": utils.castSqlParam(
                    sql, params["source_variant_code"], "source_variant_code"
                ),
                "warehouse_code": str(params["warehouse_code"])
            })
            cursor.execute(sql)
            result = utils.getRowsODBC(cursor, 1)
            if len(result) > 0:
                transformed_product = self._transformProduct(
                    params,
                    result[0],
                    "0"
                )
                transformed_product["raw_query"] = result
                rows.append(transformed_product)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {"source_products": rows}
        }

        # Validate product if validation was requested
        if 'validate' in params and params['validate'] == 'true':
            # TODO: Remove import from here, this is just so client doesn't require package
            from jsonschema import validate
            from ..shared import schema
            validate(response, schema.product)

        return json.dumps(response, indent=self._indent)

    def auditProductsBatch(self):
        """
        This function kicks off a process that reads all the products
        in the pastel database and updates the hashes in sqlite.
        If the sqlite hash differs the time stamp the product row is updated.
        getProducts can then check the sync token against sqlite.
        """
        t1 = time.time()
        products = 0
        self._setConfig()

        self._checkInstall()
        params = self._getParams()

        response = utils.checkRequiredParams(
            ["audit_lower_limit", "audit_upper_limit"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        error_log_path = os.path.join(packageDir, self._configDir, "error.log")

        # Read products from pastel
        with pastel.openConn(self._config["dsn"]) as cursor:
            sql = self._getProductsPastel()
            sql = utils.bindSqlParams(sql, {
                "audit_lower_limit": int(params["audit_lower_limit"]),
                "audit_upper_limit": int(params["audit_upper_limit"])
            })

            response = pastel.execute(cursor, sql)
            if response is not None:
                return json.dumps(response, indent=self._indent)

            rows = utils.getRowsODBC(cursor, 1)

            # Get local connection
            if len(rows) > 0:
                with utils.openSqliteConn(self._getDbPath(), commit=True) as sqliteCursor:
                    while len(rows) > 0:
                        products += 1

                        if rows[0]["product_active"] == "false":
                            # Delete inactive product images from audit table
                            self._deleteVariantImage(rows[0]["source_variant_code"], sqliteCursor)

                            # Update meta.sync_token_image
                            sql = "update meta set value = cast(min(value, (select max(sync_token) from image)) as text) where `key` = 'sync_token_image'"
                            sqliteCursor.execute(sql)
                            txt = 'Inactive record found - source_variant_code: ' + str(rows[0]["source_variant_code"])
                            self._logMessageToFile(error_log_path, txt)

                        # Update hash for this product
                        self._updateHash(rows[0], sqliteCursor)
                        rows = utils.getRowsODBC(cursor, 1)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {
                "products": products
            }
        }
        return json.dumps(response, indent=self._indent)

    def _deleteVariantImage(self, source_variant_code, sqliteCursor):
        sql = "delete from image where source_variant_code = ?"

        if "sourceVariantCodeType" not in self._config:
            self._config["sourceVariantCodeType"] = "s"

        source_variant_code = utils.castSqlParamByType(
            source_variant_code,
            self._config["sourceVariantCodeType"]
        )

        sqliteCursor.execute(sql, (source_variant_code,))

    def _getCustomerPastel(self):
        path = os.path.join(packageDir, self._configDir, "getCustomer.sql")
        f = self._open(path, "r")
        sql = f.read()
        f.close()
        return sql

    def _getCustomersPastel(self):
        path = os.path.join(packageDir, self._configDir, "getCustomersPush.sql")
        f = self._open(path, "r")
        sql = f.read()
        f.close()
        return sql

    @staticmethod
    def _getLastModifiedCustomer(sqliteCursor):
        sql = '''
          select * from customer
          order by sync_token desc limit 1
        '''
        sqliteCursor.execute(sql)
        lastModified = sqliteCursor.fetchone()
        return lastModified

    def _getCustomerSyncToken(self, lastModified):
        if lastModified is None:
            sync_token = 1
        else:
            sync_token = int(lastModified[self._customerIndex["sync_token"]]) + 1
        return sync_token

    def _updateCustomerHash(self, row, sqliteCursor):
        params = {"source_id": None}
        auditRow = {
            self._customerIndex["sync_token"]: None
        }
        rowHash = utils.getHash(json.dumps(self._transformCustomer(
            params, row, auditRow)))

        # Compare hash to existing row
        sql = "select * from customer where source_customer_code = ?"
        sqliteCursor.execute(
            sql, ((
                row["source_customer_code"],
            ))
        )
        oldCustomer = sqliteCursor.fetchone()  # Assuming there is only one match

        if oldCustomer is None:
            lastModified = self._getLastModifiedCustomer(sqliteCursor)
            sql = "insert into customer values (?, ?, ?, ?)"
            sqliteCursor.execute(
                sql, ((
                          row["source_customer_code"],
                          rowHash,
                          self._getCustomerSyncToken(lastModified),
                          utils.getTimestamp(),
                      ))
            )

        elif oldCustomer[self._customerIndex["hash"]] != rowHash:
            lastModified = self._getLastModifiedCustomer(sqliteCursor)
            # Update if hash has changed
            sql = "update customer set" \
                  " hash = ?," + \
                  " modified = ?," \
                  " sync_token = ?" \
                  " where" + \
                  " source_customer_code = ?"
            sqliteCursor.execute(
                sql,
                ((
                     rowHash,
                     utils.getTimestamp(),
                     self._getCustomerSyncToken(lastModified),
                     row["source_customer_code"],
                 ))
            )

    # This probably doesn't work correctly as customer fetching for the Evolution package doesn't seem that fleshed
    # out
    def auditCustomersBatch(self):
        """
        This function kicks off a process that reads all the customers
        in the SQL server database and updates the hashes in sqlite.
        If the sqlite hash differs from the time stamp the customer row is updated.
        getCustomers can then check the sync token against sqlite.
        """
        t1 = time.time()
        customers = 0
        self._setConfig()

        self._checkInstall()
        params = self._getParams()

        response = utils.checkRequiredParams(
            ["audit_lower_limit", "audit_upper_limit"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        # Read products from SQL server
        with pastel.openConn(self._config["dsn"]) as cursor:
            sql = self._getCustomersPastel()
            sql = utils.bindSqlParams(sql, {
                "audit_lower_limit": int(params["audit_lower_limit"]),
                "audit_upper_limit": int(params["audit_upper_limit"])
            })
            cursor.execute(sql)
            rows = utils.getRowsODBC(cursor, 1)

            # Get local connection
            if len(rows) > 0:
                with utils.openSqliteConn(self._getDbPath(), commit=True) as sqliteCursor:
                    while len(rows) > 0:
                        customers += 1
                        # Update hash for this customer
                        self._updateCustomerHash(rows[0], sqliteCursor)
                        rows = utils.getRowsODBC(cursor, 1)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {
                "customers": customers
            }
        }
        return json.dumps(response, indent=self._indent)

    def getProductsBatch(self):
        t1 = time.time()
        self._setConfig()

        params = self._getParams()

        response = utils.checkRequiredParams(
            ["sync_token", "limit"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        # Test source in the s2s console does not pass in current_iteration
        audit_products = False
        if "current_iteration" not in params:
            params["current_iteration"] = "1"

        # Only audit products on the first iteration
        if params["current_iteration"] == "1":
            response = self.auditProductsBatch()
            response = json.loads(response)
            if not response["status"]:
                return json.dumps(response, indent=self._indent)
            audit_products = True

        with pastel.openConn(self._config["dsn"]) as cursor:
            with utils.openSqliteConn(self._getDbPath(), commit=True) as sqliteCursor:
                sql = self._getProductsLocal()
                sqliteCursor.execute(sql, (
                    int(params["sync_token"]), int(params["limit"])
                ))

                rows = []
                auditRow = sqliteCursor.fetchone()
                while auditRow is not None:
                    sql = self._getProductPastel()
                    sql = utils.bindSqlParams(sql, {
                        "source_variant_code": utils.castSqlParam(
                            sql, auditRow[self._productIndex["source_variant_code"]], "source_variant_code"
                        )
                    })
                    cursor.execute(sql)
                    odbcRows = utils.getRowsODBC(cursor, 1)
                    if len(odbcRows) > 0:
                        rows.append(
                            self._transformProduct(
                                params,
                                odbcRows[0],
                                auditRow[self._productIndex["sync_token"]]
                            )
                        )
                    auditRow = sqliteCursor.fetchone()

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {"source_products": rows},
            "audit_products": audit_products
        }
        return json.dumps(response, indent=self._indent)

    def getOrder(self):
        t1 = time.time()
        self._setConfig()

        params = self._getParams()
        response = utils.checkRequiredParams(
            ["source_order_code", "source_id"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        with pastel.openConn(self._config["dsn"]) as cursor:
            sql = self._getOrderPastel()
            sql = utils.bindSqlParams(sql, {
                "source_order_code": str(params["source_order_code"])
            })
            cursor.execute(sql)
            rows = cursor.fetchall()
            source_order = self._transformOrder(params, rows)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": source_order
        }
        return json.dumps(response, indent=self._indent)

    def testSdk(self):
        """
        Tests if the SDK is working by using it to fetch a customer.
        """
        t1 = time.time()
        self._setConfig()

        try:
            sdk, helper = pastel.getSdk(self._config)
            Customer = sdk.Customer.Get("Name like 'A%'")
            message = ""
            if Customer is not None:
                message = "Customer description: %s" % Customer.Description

        except Exception as exc:
            response = {
                "status": False,
                "code": None,
                "description": str(exc),
                "line": utils.lineNo()
            }
            return json.dumps(response, indent=self._indent)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": "It worked. %s" % message
        }
        return json.dumps(response, indent=self._indent)

    """
    events that can be overridden
     saves having to override the entire create order or create customer method
    """
    def _beforeOrderSave(self, so, payload):
        return

    def _beforeCustomerSave(self, cu, payload=None):
        return

    """
    returns modified notes field
    """
    def _beforeNotesSave(self, notes, order):
        return notes

    def _beforeNotes2Save(self, notes, order):
        return notes

    def _beforeNotes3Save(self, notes, order):
        return notes

    """
    address is an evolution SDK address
    returns modified address
    """
    def _beforeAddressSave(self, address):
        return address

    def _afterOrderSave(self, so, payload):
        return

    def _afterCustomerSave(self, cu, payload=None):
        return

    def _afterInvoiceProcessed(self, so):
        return

    def _afterInvoiceCompleted(self, so):
        return

    def _getCustomerAddress(self, source_customer_code = None, Customer = None):
        response = pastel.getCustomerAddress(self._config, source_customer_code, Customer)
        return response

    def _getCustomerDeliveryAddress(self, source_customer_code = None, Customer = None):
        response = pastel.getCustomerAddress(self._config, source_customer_code, Customer,
                                             property = "DefaultDeliveryAddress")
        return response

    def _getTaxCode(self, params, code, shipping=False):
        paramRequired = False
        newCode = False
        code = code.strip() # Remove spaces

        # Valid param keys are:
        # default_tax_code, default_tax_code_exempt (required)
        # default_tax_code_shipping, default_tax_code_exempt_shipping (optional)

        def lookupCode(append=""):
            key = "default_tax_code" + append

            # Shipping may have a separate code
            if shipping:
                shippingKey = key + "_shipping"
                if shippingKey in params:
                    return params[shippingKey]

            # Otherwise just use the "taxed" or "exempt" code
            if key in params:
                return params[key]

            return False

        if code == "taxed":
            paramRequired = True
            newCode = lookupCode()

        elif code == "exempt":
            paramRequired = True
            newCode = lookupCode(append="_exempt")

        if paramRequired and not newCode:
            raise Exception("Could not lookup tax code for code: {}".format(code))

        # Assume the code given is a valid tax code
        return newCode


    #TODO we should allow the address fields to be set by params
    def _getPastelAddress(self, sdk, address):
        utils.noneToString(address)
        a = sdk.Address()
        if "company" in address and address["company"] != "":
            name = address["company"] + " (" + address["first_name"] + " " + address["last_name"] + " " + address["phone"] + ")"
        else:
            name = address["first_name"] + " " + address["last_name"] + " " + address["phone"]
        a.Line1 = utils.truncateString(name, 40)
        a.Line2 = utils.truncateString(address["address1"], 40)
        a.Line3 = utils.truncateString(address["address2"], 40)
        a.Line4 = utils.truncateString(address["city"], 40)
        a.Line5 = utils.truncateString(address["country"], 40)
        a.PostalCode = utils.truncateString(address["zip"], 40)
        return a

    def _getPastelCustomer(self, sdk, params, source_customer_code):
        pastelCustomer = None
        if source_customer_code is not None:
            pastelCustomer = sdk.Customer.GetByCode(source_customer_code)

        # source_customer_code not found?
        if pastelCustomer is None and "default_customer_code" in params:
            pastelCustomer = sdk.Customer.GetByCode(params["default_customer_code"])

        # default_customer_code not found?
        if pastelCustomer is None:
            create_customer = False
            if "create_customer_enabled" in params:
                create_customer = (params["create_customer_enabled"] == "true")
            if create_customer:
                pastelCustomer = self._createPastelCustomer(sdk, params)
            else:
                raise Exception("Customer not allowed to be created, no source customer code given")

        return pastelCustomer

    def _createPastelCustomer(self, sdk, params):
        # ensure payload decoded
        payload = self._payload()
        try:
            # Payload might be bytes, see connector.py
            payload = payload.decode("utf-8")
        except AttributeError:
            # Assume payload is already a decoded unicode string
            pass
        payload = json.loads(payload)
        order = payload["system_order"]
        pastelCustomer = sdk.Customer()
        pastelCustomer.Description = utils.truncateString(order["customer"]["first_name"] + " " +
                             order["customer"]["last_name"], 40)
        pastelCustomer.EmailAddress =order["customer"]["email"]

        # representative
        if "new_customer_representative_code" in params:
            code = params["new_customer_representative_code"]
            sales_rep_id = sdk.SalesRepresentative.FindByCode(code)
            # Requires pythonnet 2.1.0
            # https://github.com/pythonnet/pythonnet/issues/211
            pastelCustomer.SalesRepID = sales_rep_id

        # customer default price list
        if "new_customer_price_list_code" in params:
            pastelCustomer.DefaultPriceList = sdk.PriceList(params["new_customer_price_list_code"])

        # customer area
        if "new_customer_area_code" in params:
            pastelCustomer.Area = sdk.Area(params["new_customer_area_code"])

        # customer area
        if "new_customer_group_code" in params:
            pastelCustomer.Group = sdk.CustomerGroup(params["new_customer_group_code"])

        # phone
        if "phone" in order["shipping_address"] and order["shipping_address"]["phone"] not in [None, ""]:
            pastelCustomer.Telephone = order["shipping_address"]["phone"]
        else:
            pastelCustomer.Telephone = order["billing_address"]["phone"]

        # address
        pastelCustomer.PhysicalAddress = self._getPastelAddress(sdk, order["shipping_address"])
        pastelCustomer.PostalAddress = self._getPastelAddress(sdk, order["billing_address"])

        # user_field_customer_[x]
        for field in params:
            if "user_field_customer_" in field:
                user_field = field.replace("user_field_customer_", "")
                pastelCustomer.UserFields[user_field] = params[field]

        self._beforeCustomerSave(pastelCustomer, payload)
        pastelCustomer.Save()
        self._afterCustomerSave(pastelCustomer, payload)

        # delivery address, must happen after customer already saved since we may need to create one
        code = "S2SDelivery1"
        if sdk.DeliveryAddressCode.FindByCode(code) == -1:
            da = sdk.DeliveryAddressCode()
            da.Code = code
            da.Description = ("S2S delivery address")
            da.Save()
        pastelCustomer.DeliveryAddresses.Add(
            code,
            self._getPastelAddress(sdk, order["shipping_address"])
        )
        pastelCustomer.Save()
        return pastelCustomer

    def _setUserField(self, object, params, prefix):
        for field in params:
            if prefix in field:
                user_field = field.replace(prefix, "")
                object.UserFields[user_field] = params[field]

    # TODO Currently only USN is using this function,
    # free_shipping probably isn't a generic feature?
    def _createCreditNote(
            self, sdk, helper, order, source_order_code, params,
            pastelCustomer):

        notes = ""
        if "notes" in order:
            notes = order["notes"]

        total_discount = 0
        if "total_discount" in order:
            total_discount = order["total_discount"]

        shipping_discount = 0
        if "free_shipping" in params:
            if params["free_shipping"] == "true":
                for shipping_line in order["shipping_lines"]:
                    # Always add cents to avoid floating point error
                    shipping_discount += shipping_line["price"] * 100
                shipping_discount = shipping_discount / 100
            # Subtract shipping_discount from total_discount
            if shipping_discount > 0:
                total_discount = (total_discount * 100 - shipping_discount * 100) / 100

        cn = sdk.CreditNote()
        cn.Account = pastelCustomer
        cn.OrderNo = str(source_order_code)

        # Line item for total_discount
        od = sdk.OrderDetail()
        cn.Detail.Add(od)
        if "gl_discount_code" in params:
            od.GLAccount = helper.GetGLAccount(params["gl_discount_code"])
        od.Quantity = 1
        od.UnitSellingPrice = total_discount
        # TODO Which tax code to use for total_discount?
        taxCode = ""
        if "default_tax_code" in params:
            taxCode = params["default_tax_code"]
        od.TaxType = sdk.TaxRate(taxCode)

        # Only add shipping discount if there are shipping lines
        if len(order["shipping_lines"]) > 0:
            # Line item for shipping_discount
            od = sdk.OrderDetail()
            cn.Detail.Add(od)
            if "gl_shipping_discount_code" in params:
                od.GLAccount = helper.GetGLAccount(params["gl_shipping_discount_code"])
            od.Quantity = 1
            od.UnitSellingPrice = shipping_discount
            # TODO We only support one shipping line w.r.t. tax
            taxCodeShipping = self._getTaxCode(
                params, order["shipping_lines"][0]["tax_lines"][0]["code"], shipping=True
            )
            od.TaxType = sdk.TaxRate(taxCodeShipping)

        # Use same notes as on per the sales order
        cn.MessageLine1 = notes

        # Only proceed if there is a non-zero discount line
        if total_discount > 0 or shipping_discount > 0:
            cn.Save()

            if "process_credit_note" in params:
                if params["process_credit_note"] == "true":
                    cn.Process()

            if "complete_credit_note" in params:
                if params["complete_credit_note"] == "true":
                    cn.Complete()


    def createOrder(self):
        t1 = time.time()

        self._setConfig()

        params = self._getParams()
        required_params = ["warehouse_code", "shipping_code"]
        if "lot_enabled" in params and params["lot_enabled"] == "true":
            required_params.append("lot_warehouse_id")
            required_params.append("lot_status_ids")
        response = utils.checkRequiredParams(
            required_params,
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        payload = self._payload()
        try:
            # Payload might be bytes, see connector.py
            payload = payload.decode("utf-8")
        except AttributeError:
            # Assume payload is already a decoded unicode string
            pass
        try:
            payload = json.loads(payload)
        except ValueError:
            response = {
                "status": False,
                "description": "Request body is not valid json",
                "line": utils.lineNo()
            }
            return json.dumps(response, indent=self._indent)

        order = payload["system_order"]
        source = payload["sources"][0]

        source_customer_code = None

        # Check order line items are in stock if negative stock disabled
        negative_stock_disabled = False
        if "negative_stock_disabled" in params:
            negative_stock_disabled = params["negative_stock_disabled"] == "true"
        if negative_stock_disabled:
            description = ""
            descriptionTemplate = "%s x %s ordered %s available, "
            with pastel.openConn(self._config["dsn"]) as cursor:
                for line_item in order["line_items"]:
                    sql = self._getProductPastel()

                    source_variant_code = line_item["source_variant_code"]
                    source_variant_code = source_variant_code.replace("'", "")

                    sql = utils.bindSqlParams(sql, {
                        "source_variant_code": str(source_variant_code),
                        "warehouse_code": str(params["warehouse_code"])
                    })
                    cursor.execute(sql)
                    result = utils.getRowsODBC(cursor, 1)
                    if len(result) > 0:
                        params["source_id"] = self._config["push"]["source_id"]
                        transformed_product = self._transformProduct(
                            params,
                            result[0],
                            "0"
                        )
                        default_qty = transformed_product["product"]["variants"]["qty"]
                        if int(line_item["qty"]) > 0 and int(line_item["qty"]) > int(default_qty):
                            description += descriptionTemplate % (line_item["qty"], line_item["sku"], default_qty)

                    else:
                        description += "sku %s not found, " % (line_item["sku"])

            if len(description) > 0:
                response = {
                    "status": False,
                    "code": "409",
                    "description": description,
                    "line": utils.lineNo()
                }
                return json.dumps(response, indent=self._indent)

        use_credit_note_discount = False
        if "use_credit_note_discount" in params:
            use_credit_note_discount = params["use_credit_note_discount"] == "true"

        sdk, helper = pastel.getSdk(self._config)
        version_number = self._getVersionNumber()

        # WARNING Doing a try catch around everything is bad form,
        # instead of making debugging easier it hides where the exception comes from!
        # try:

        # Try to find the order customer account
        if "source_customer_code" in source:
            source_customer_code = source["source_customer_code"]


        # If contacts (or persons) are included in the source customer code,
        # we need to strip out the contact id and only use customer code.
        # The convention of source_customer_code is:
        # {customer_code}-{contact_id}
        if "contact_source_customer" in params:
            if params["contact_source_customer"] == "true":
                source["original_source_customer_code"] = source["source_customer_code"]
                codes = source["source_customer_code"].rsplit('-', 1)
                if codes[1]:
                    source_customer_code = codes[0]
                else:
                    raise Exception("Invalid source_customer_code {}".format(source["source_customer_code"]))

        pastelCustomer = self._getPastelCustomer(sdk, params, source_customer_code)
        source_customer_code = pastelCustomer.Code

        # ==== CREDIT LIMIT =====
        # check if the customer is not over terms
        # the SDK documentation explicitly states that it does not do this check
        if "check_credit_limit" in params and params["check_credit_limit"] == "true":
            credit_limit = pastelCustomer.CreditLimit
            if credit_limit > 0:
                # subtract order total + balance from credit limit
                account_balance = pastelCustomer.AccountBalance
                order_total = 0
                for line_item in order["line_items"]:
                    order_total += (line_item["qty"] * line_item["price"])
                if (credit_limit - (account_balance + order_total)) < 0:
                    raise Exception("Credit limit reached - Credit Limit: %s, Account Balance: %s, Order Total: %s" %
                                       (credit_limit, account_balance, order_total))

        # set branch if required
        if "branch_code" in params:
            branch_id = sdk.Branch.FindByCode(params["branch_code"])
            if branch_id == -1:
                raise Exception(
                    "Branch code {} not found".format(params["branch_code"]))
            # Requires pythonnet 2.1.0
            # https://github.com/pythonnet/pythonnet/issues/211
            sdk.DatabaseContext.SetBranchContext(branch_id)

        if "order_document_type" in params and params["order_document_type"] == "Quotation":
            so = sdk.SalesOrderQuotation()
        else:
            so = sdk.SalesOrder()
        so.Customer = pastelCustomer

        # ==== Duplicate PO Number Check ====
        if "enable_unique_po_number" in params and params["enable_unique_po_number"] == "true":
            if self.hasPONumber(so):
                raise Exception("Duplicate purchase order number not allowed.")

        # Set order date
        date_created = order["created"]
        if "time_zone" in params:
            date_created = self._getDateByTimezone(date_created, params["time_zone"])


        so.OrderDate = pastel.DateTime(pastel.iso8601ToTicks(date_created))

        # due date
        if "due_date" in params:
            so.DueDate = pastel.DateTime(pastel.iso8601ToTicks(params["due_date"]))

        # delivery date
        if "delivery_date" in params:
            so.DeliveryDate = pastel.DateTime(pastel.iso8601ToTicks(params["delivery_date"]))

        # TODO Allow an array of notes?
        notes = ""
        if "notes" in order:
            # Message field only allows 255 chars
            notes = order["notes"][:255]

        # Transform notes if neccessary
        notes1 = notes
        notes2 = self._beforeNotes2Save('', order)
        notes3 = self._beforeNotes3Save('', order)

        so.MessageLine1 = notes1
        so.MessageLine2 = notes2
        so.MessageLine3 = notes3

        use_customer_address = False
        if "use_customer_address" in params:
            use_customer_address = (params["use_customer_address"] == "true")
        if use_customer_address:
            response = self._getCustomerDeliveryAddress(Customer = pastelCustomer)
            delivery_address = response["address"]
            a = sdk.Address()
            a.Line1 = delivery_address["Line1"]
            a.Line2 = delivery_address["Line2"]
            a.Line3 = delivery_address["Line3"]
            a.Line4 = delivery_address["Line4"]
            a.Line5 = delivery_address["Line5"]
            a.PostalCode = delivery_address["PostalCode"]
            so.DeliverTo = self._beforeAddressSave(a)

            response = self._getCustomerAddress(Customer = pastelCustomer)
            postal_address = response["address"]
            a = sdk.Address()
            a.Line1 = postal_address["Line1"]
            a.Line2 = postal_address["Line2"]
            a.Line3 = postal_address["Line3"]
            a.Line4 = postal_address["Line4"]
            a.Line5 = postal_address["Line5"]
            a.PostalCode = postal_address["PostalCode"]
            so.InvoiceTo = self._beforeAddressSave(a)

        else:
            so.DeliverTo = self._beforeAddressSave(self._getPastelAddress(sdk, order["shipping_address"]))
            so.InvoiceTo = self._beforeAddressSave(self._getPastelAddress(sdk, order["billing_address"]))

        if "external_order_no" in params:
            so.ExternalOrderNo = params["external_order_no"]

        if "order_project_code" in params:
            so.Project = sdk.Project(params["order_project_code"])

        if "delivery_method" in params:
            so.DeliveryMethod = sdk.DeliveryMethod(params["delivery_method"])

        if "order_representative_code" in params:
            code = params["order_representative_code"]
            sales_rep_id = sdk.SalesRepresentative.FindByCode(code)
            # Requires pythonnet 2.1.0
            # https://github.com/pythonnet/pythonnet/issues/211
            so.RepresentativeID = int(sales_rep_id)
            # so.Representative = sdk.SalesRepresentative(params["order_representative_code"])

        process_invoice = False
        if "process_invoice" in params:
            if params["process_invoice"] == "true":
                process_invoice = True

        confirm_qty = False
        if "confirm_qty" in params:
            if params["confirm_qty"] == "true":
                confirm_qty = True

        confirm_qty_shipping = False
        if "confirm_qty_shipping" in params:
            if params["confirm_qty_shipping"] == "true":
                confirm_qty_shipping = True

        # Note. The order has to be saved and not processed to reserve stock.
        # and can only be applied to Sales Orders as per SDK documentation
        # Reference:  http://developmentzone.pastel.co.za/index.php?title=C_Sales_Orders
        process_reserved = False
        if "process_reserved" in params and params["process_reserved"] == "true":
            if "order_document_type" in params and params["order_document_type"] != "Quotation":
                if process_invoice is not True:
                    process_reserved = True

        useForeignCurrency = False
        if "currency_code" in params:
            # Bug: Pastel Evolution 6 does not automatically calculate the exchange rate based on the currency so we have to do it manually
            # Below steps are required to set the exchange rate for Evolution versions below 7 (tested with 6.80.45)
            # 1. Check evo version first - if < 7.x.x then just set UnitSellingPrice
            # 2. Check if customer has currency set - run code if it's set
            # 3. What happens when a customer has no currency set?? It simply uses the default
            # 4. Should the discount cents not be converted into the same currency?? No. the discount percent is the same doesn't matter the currency

            # We only compare the major version (left most number)
            if version_number != False and int(version_number.split('.')[0]) < 7:
                if so.Customer.IsForeignCurrencyAccount:
                    # For some reason Evo v6 has an issue setting so.Currency using so.Currency = sdk.Currency.GetByCode(params["currency_code"])
                    # despite the value of params["currency_code"] being the same as so.Customer.Currency.Code
                    so.Currency = sdk.Currency.GetByCode(so.Customer.Currency.Code)
                    useForeignCurrency = True
                    currencyID = so.Customer.CurrencyID
                    ticksDate = pastel.DateTime(pastel.iso8601ToTicks(datetime.datetime.now().isoformat()))
                    idCurrencyHist = sdk.ExchangeRate.FindLatest(currencyID, ticksDate)
                    drCurrencyHist = sdk.DatabaseContext.ExecuteCommandSingleRow(
                        "SELECT * FROM CurrencyHist WHERE idCurrencyHist = " + str(idCurrencyHist))
                    so.ExchangeRate = float(drCurrencyHist["fBuyRate"])
            else:
                so.Currency = sdk.Currency.GetByCode(params["currency_code"])


        if "lot_enabled" in params and params["lot_enabled"] == "true":
            order["line_items"] = self._splitOrderLineItemsByLots(order["line_items"])

        # When param use_line_item_discounts is true, we set total_discount to zero and set line item discounts
        use_line_item_discount = False
        if "use_line_item_discounts" in params and params["use_line_item_discounts"] == "true":
            use_line_item_discount = True

        exclusiveTotalCents = 0
        for line_item in order["line_items"]:
            od = sdk.OrderDetail()
            od.InventoryItem = helper.GetStockItem(line_item["sku"])

            add_warehouse_code = True
            if "ignore_warehouse_code" in params:
                if params["ignore_warehouse_code"] == "true":
                    add_warehouse_code = False
            if add_warehouse_code:
                if od.InventoryItem.IsWarehouseTracked:
                    warehouse_code = self.getDefaultWarehouseCode(line_item["sku"])
                    od.Warehouse = helper.GetWarehouseByCode(warehouse_code)

            od.Quantity = line_item["qty"]

            if process_invoice or confirm_qty:
                od.ToProcess = line_item["qty"]

            if process_reserved:
                od.Reserved = line_item["qty"]

            if useForeignCurrency:
                od.UnitSellingPriceForeign = line_item["price"]
                od.UnitSellingPrice = line_item["price"] * so.ExchangeRate
            else:
                od.UnitSellingPrice = line_item["price"]

            # Assign lot if available
            if "lot_id" in line_item:
                lot = sdk.Lot(line_item["lot_id"])
                lot.InventoryItemID = line_item["source_variant_code"]
                od.Lot = lot

            # Gets or sets the total line discount value (applies to tax excl. value)
            if "total_discount" in line_item and use_line_item_discount:
                if line_item["total_discount"] > 0:
                    od.Discount = line_item["total_discount"] * line_item["qty"]

            exclusiveTotalCents += round(int(line_item["qty"]) * (line_item["price"] * 100))

            tax_line = line_item["tax_lines"][0]
            taxCode = self._getTaxCode(
                params, tax_line["code"]
            )
            od.TaxType = sdk.TaxRate(taxCode)


            so.Detail.Add(od)

        add_shipping = True
        if "ignore_shipping" in params:
            if params["ignore_shipping"] == "true":
                add_shipping = False
        if "shipping_lines" in order and add_shipping:
            for shipping_line in order["shipping_lines"]:

                # the check for shipping which is has a zero price is removed.
                # we may need to create a parameter for this to set it per client
                # # only add shipping as line item if it is greater than 0
                # if int(shipping_line["price"]) > 0:
                od = sdk.OrderDetail()
                so.Detail.Add(od)

                if "gl_shipping_code" in params:
                    od.GLAccount = helper.GetGLAccount(params["gl_shipping_code"])
                else:
                    od.InventoryItem = helper.GetStockItem(params["shipping_code"])
                    if od.InventoryItem.IsWarehouseTracked:
                        od.Warehouse = helper.GetWarehouseByCode(
                            params["warehouse_code"])

                shippingQty = 1
                od.Quantity = shippingQty
                if process_invoice or confirm_qty_shipping:
                    od.ToProcess = shippingQty

                if useForeignCurrency:
                    od.UnitSellingPriceForeign = shipping_line["price"]
                    od.UnitSellingPrice = shipping_line["price"] * so.ExchangeRate
                else:
                    od.UnitSellingPrice = shipping_line["price"]
                exclusiveTotalCents += round(shippingQty * shipping_line["price"] * 100)

                tax_line = shipping_line["tax_lines"][0]
                taxCode = self._getTaxCode(
                    params, tax_line["code"], shipping=True
                )
                od.TaxType = sdk.TaxRate(taxCode)

        # By default we let the system assign an order code
        use_channel_order_code = False
        if "use_channel_order_code" in params:
            use_channel_order_code = (params["use_channel_order_code"] == "true")
        if use_channel_order_code:
            orderFound = sdk.SalesOrder.Find(
                "OrderNum = '%s'" % order["channel_order_code"])
            if orderFound > -1:
                # Saving an existing order will duplicate line items
                raise Exception(
                    "Order already exists: %s" % order["channel_order_code"])

            so.OrderNo = order["channel_order_code"]

        # By default the discount is applied as a percentage on the order
        if "total_discount" in order and not use_line_item_discount:
            # Prevent division by zero
            if exclusiveTotalCents > 0 and not use_credit_note_discount:
                totalDiscountCents = round(float(order["total_discount"]) * 100)
                so.DiscountPercent = totalDiscountCents / exclusiveTotalCents * 100

        # Order user defined fields
        Api.handleUserDefinedFields(so, params, version_number, "user_field_order_")

        # Line item user defined fields
        Api.handleUserDefinedFields(so.Detail, params, version_number, "user_field_line_")

        self._beforeOrderSave(so, payload)
        so.Save()
        self._afterOrderSave(so, payload)

        # set source order code
        if "order_document_type" in params and params["order_document_type"] == "Quotation":
            source_order_code = so.QuoteNo
        else:
            source_order_code = so.OrderNo

        if "process_invoice" in params:
            if params["process_invoice"] == "true":
                so.InvoiceDate = pastel.DateTime(pastel.iso8601ToTicks(date_created))
                so.Process()

        if "complete_invoice" in params:
            if params["complete_invoice"] == "true":
                so.InvoiceDate = pastel.DateTime(pastel.iso8601ToTicks(date_created))
                so.Complete()



        # TODO we should use SQL to fetch invoice number,
        # if "use_invoice_number" in params:
        #    if params["use_invoice_number"] == "true":
        #        source_order_code = so.InvoiceNumber


        # Create credit note for discount
        if "total_discount" in order and use_credit_note_discount:
            self._createCreditNote(
                order=order,
                source_order_code=source_order_code,
                params=params,
                pastelCustomer=pastelCustomer,
                sdk=sdk,
                helper=helper
            )

        # except Exception as exc:
        #     response = {
        #         "status": False,
        #         "code": None,
        #         "description": str(exc),
        #         "data": {
        #             "source_order_code": source_order_code,
        #             "source_customer_code": source_customer_code
        #         },
        #         "line": utils.lineNo()
        #     }
        #     return json.dumps(response, indent=self._indent)

        # if contact used in source customer code, return the original source customer code, i.e.
        # {customer_code}-{contact_id}
        if "contact_source_customer" in params:
            if params["contact_source_customer"] == "true":
                source_customer_code = source["original_source_customer_code"]

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {
                "source_order_code": source_order_code,
                "source_customer_code": source_customer_code
            },
        }
        return json.dumps(response, indent=self._indent)
    
    @staticmethod
    def handleUserDefinedFields(ob: any, params: dict, versionNumber: any, identifier:str=""):
        """
        Sets Evolution User Defined Fields on the respective sdk object depending on:
            - The Evolution database version as per this [comment](https://github.com/stock2shop/gomedia_evolution/issues/681#issuecomment-2164720559)
            - The identifier present in the function call, which can relate to line items, order or customers.
        """
        if identifier == "user_field_line_":
            # check if the param line_item_udf_map exists in the params, and needs
            # to be in this format for the properties to be assigned correctly:
            #
            # {
            #   "sku" {
            #       "user_field_line_{{property}}": {{valueToMap}},
            #       ...
            #   }
            # }
            if "line_item_udf_map" in params and "line_item_udf_map" != "":
                line_item_map = json.loads(params["line_item_udf_map"])
                for order_detail in ob:
                    if order_detail.InventoryItem.Code in line_item_map:
                        Api.setUserDefinedFields(
                            order_detail,
                            line_item_map[order_detail.InventoryItem.Code],
                            versionNumber,
                            identifier,
                        )
        else:
            Api.setUserDefinedFields(ob, params, versionNumber, identifier)

    @staticmethod
    def setUserDefinedFields(obj: any, params: dict, versionNumber: any, identifier:str=""):
        if identifier != "":
            for field in params:
                if identifier in field:
                    user_field = field.replace(identifier, "")
                    if versionNumber != False and int(versionNumber.split('.')[0]) >= 11:
                        obj.SetUserField(user_field, params[field])
                    else:
                        obj.UserFields[user_field] = params[field]

    def getVersion(self):
        """
        Check the current Evolution Database version.
        """
        t1 = time.time()
        self._setConfig()

        version = self._getVersionNumber()
        if version != False:
            t2 = time.time()
            response = {
                "status": True,
                "timer": str(t2 - t1),
                "data": "Evolution Database Version: %s" % version,
                "version": int(version.split('.')[0])
            }
        else:
            t2 = time.time()
            response = {
                "status": False,
                "timer": str(t2 - t1),
                "data": "Error retrieving Evolution Database Version",
            }
        return json.dumps(response, indent=self._indent)

    def _getVersionNumber(self):
        self._setConfig()

        try:
            sdk, helper = pastel.getSdk(self._config)
            version = sdk.ComHelper().CurrentEvolutionDatabaseVersion
            if version is not None:
                return version
            else:
                return False

        except Exception as exc:
            return False

    def hasPONumber(self, so: any):
        """
        Determines if a purchase order number already exists inside their evolution.
        Returns `True` if the PO number already exists.
        """
        params = self._getParams()
        account_specific_po_check = "false"
        sql = '''
            SELECT DISTINCT
                inv.ExtOrderNum
            FROM Client c
            JOIN InvNum inv on inv.AccountID = c.DCLink
            WHERE
        '''

        if "enable_account_specific_po_check" in params and params["enable_account_specific_po_check"] != "":
            account_specific_po_check = params["enable_account_specific_po_check"]

        if "external_order_no" in params and params["external_order_no"] != "":
            if account_specific_po_check == "true":
                sql += "AccountID = '%s' AND ExtOrderNum = '%s'" % (so.Customer.ID, params["external_order_no"])
            else:
                sql += "ExtOrderNum = '%s'" % (params["external_order_no"])

            with pastel.openConn(self._config["dsn"]) as cursor:
                sql = utils.bindSqlParams(sql, params)
                cursor.execute(sql)
                rows = utils.getRowsODBC(cursor, 1)
                if len(rows) > 0:
                    return True
        return False

    def getDefaultWarehouseCode(self, sku):
        self._setConfig()

        params = self._getParams()

        code = False
        if "warehouse_code" in params and params["warehouse_code"] != "":
            code = params["warehouse_code"]

        # query db for default warehouse code
        with pastel.openConn(self._config["dsn"]) as cursor:
            sql = self._getDefaultLineItemWarehousePastel()
            if sql != False:
                sql = utils.bindSqlParams(sql, {
                    "sku": utils.castSqlParam(
                        sql, sku, "sku"
                    )
                })
                cursor.execute(sql)
                result = utils.getRowsODBC(cursor, 1)
                if len(result) > 0 and len(result[0]["warehouse_code"]) > 0:
                    code = result[0]["warehouse_code"]

        return code

    '''
    Gets available lots and creates additional line items
    depending on stock availability within lots.
    
    For example - if 100 qty was ordered for sku A, but lot 1 only has 50 available, and lot 2 and another 50, then
    line_items will have two items for sku A, with 50 from lot 1, and the remaining 50 from lot 2.  
    '''
    def _splitOrderLineItemsByLots(self, line_items):
        self._setConfig()

        params = self._getParams()
        lot = collections.OrderedDict()

        # 1. Get available lots and assign lot id and qty to line items
        '''
        Example result:
        line_items: [
          {
              "qty": 100,
              "sku": "105",
              "lots": [
                  {
                      "lot_id": 1,
                      "lot_desc": "P1",
                      "qty": 10
                  },
                  {
                      "lot_id": 2,
                      "lot_desc": "P2",
                      "qty": 30
                  },
                  {
                      "lot_id": 3,
                      "lot_desc": "P3",
                      "qty": 60
                  },
              ]
          }
        ]
        '''
        for item in line_items:
            source_variant_code = str(item["sku"])
            lot[source_variant_code] = collections.OrderedDict()

            with pastel.openConn(self._config["dsn"]) as cursor:
                sql = self._getAvailableLotsPastel()
                sql = utils.bindSqlParams(sql, {
                    "source_variant_code": int(item["source_variant_code"]),
                    "warehouse_id": int(params["lot_warehouse_id"]),
                    "lot_status_ids": str(params["lot_status_ids"])
                })
                cursor.execute(sql)
                result = utils.getRowsODBC(cursor, 100) # get first 100
                if len(result) > 0:
                    # loop through sql results
                    # check lot availability in lot against item['qty']
                    item["lots"] = []
                    qty_balance = item["qty"]

                    # Goes from oldest to newest lot
                    for lot in result:
                        if lot["qty_available"] > 0:
                            # TODO what happens if the ordered qty > summed lot qty?
                            if int(qty_balance) <= int(lot["qty_available"]):
                                # there is enough stock,
                                reserve_qty = qty_balance

                                assigned_lot = collections.OrderedDict()
                                assigned_lot["lot_id"] = lot["lot_id"]
                                assigned_lot["lot_desc"] = lot["lot_desc"]
                                assigned_lot["qty"] = reserve_qty
                                item["lots"].append(assigned_lot)

                                break
                            else:
                                # not enough stock
                                reserve_qty = lot["qty_available"]
                                qty_balance = qty_balance - reserve_qty

                                assigned_lot = collections.OrderedDict()
                                assigned_lot["lot_id"] = lot["lot_id"]
                                assigned_lot["lot_desc"] = lot["lot_desc"]
                                assigned_lot["qty"] = reserve_qty
                                item["lots"].append(assigned_lot)

        # 2. Add additional line items for lots
        '''
        Example result:
        line_items: [
          {
            "qty": 10,
            "sku": "105",
            "lot_id": 1,
            "lot_desc": "p1"
          },
          {
            "qty": 30,
            "sku": "105",
            "lot_id": 2,
            "lot_desc": "p2"
          },
          {
            "qty": 60,
            "sku": "105",
            "lot_id": 3,
            "lot_desc": "p3"
          }
        ]
        '''
        for item in line_items:
            lot_items = []
            if "lots" in item:
                cnt = 1
                lots = item["lots"]
                del item["lots"]
                for lot in lots:
                    if cnt < len(lots):
                        new_item = deepcopy(item)
                        new_item["qty"] = lot["qty"]
                        new_item["lot_id"] = lot["lot_id"]
                        new_item["lot_desc"] = lot["lot_desc"]
                        line_items.append(new_item)
                    else:
                        item["qty"] = lot["qty"]
                        item["lot_id"] = lot["lot_id"]
                        item["lot_desc"] = lot["lot_desc"]
                    cnt += 1

            if len(lot_items) > 0:
                line_items.append(lot_items)


        return line_items

    def getHostname(self):
        """
        This function is useful when configuring the ODBC connection string
        """
        self._setConfig()
        args = ["hostname"]

        cmd = subprocess.Popen(args, stdout=subprocess.PIPE)
        response = cmd.communicate()[0]

        os_encoding = locale.getpreferredencoding()
        response = response.decode(os_encoding)

        hostname = response.replace('\n', ' ').replace('\r', '')
        response = {
            "status": True,
            "data": {
                "hostname": hostname.strip()
            }
        }
        return json.dumps(response, indent=self._indent)

    def getIpConfig(self):
        """
        This function is useful when configuring the ODBC connection string
        """
        self._setConfig()
        args = ["ipconfig"]

        cmd = subprocess.Popen(args, stdout=subprocess.PIPE)
        response = cmd.communicate()[0]

        os_encoding = locale.getpreferredencoding()
        response = response.decode(os_encoding)

        ipconfig = response.split("\r\n")
        response = {
            "status": True,
            "data": {
                "ipconfig": ipconfig
            }
        }
        return json.dumps(response, indent=self._indent)

    metaIndex = {
        "key": 0,
        "value": 1,
    }

    # TODO: Change this for Pastel evolution
    @staticmethod
    def _getMetaLocal():
        return '''
        select `key`, value from meta where `key` = ?
        '''

    # TODO: Change this for Pastel evolution
    def _setMeta(self, key, value, update=False):
        t1 = time.time()
        with utils.openSqliteConn(
            self._getDbPath(), commit=True) as sqliteCursor:
            if update:
                sql = "update meta set value = ? where `key` = ?"
            else:
                sql = "insert into meta (value, `key`) values (?, ?)"
            # Note that param order must work for both queries
            sqliteCursor.execute(sql, (value, key))

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {
                "key": key,
                "value": value
            }
        }
        return response

    # TODO: Change this for Pastel evolution
    def setMeta(self):
        self._setConfig()
        self._checkInstall()

        params = self._getParams()
        response = utils.checkRequiredParams(
            ["key", "value"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        response = self._getMeta(params["key"])
        if response["status"]:
            response = self._setMeta(
                params["key"], params["value"], update=True)
        else:
            response = self._setMeta(params["key"], params["value"])

        return json.dumps(response, indent=self._indent)

    # TODO: Change this for Pastel evolution
    def _getMeta(self, key):
        t1 = time.time()
        with utils.openSqliteConn(self._getDbPath()) as sqliteCursor:
            sql = self._getMetaLocal()
            sqliteCursor.execute(sql, (key,))
            row = sqliteCursor.fetchone()
            if row is None:
                response = {
                    "status": False,
                    "description": "Key {} not found".format(key),
                    "line": utils.lineNo()
                }
                return response

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {
                "key": row[self.metaIndex["key"]],
                "value": row[self.metaIndex["value"]]
            }
        }
        return response

    # TODO: Change this for Pastel evolution
    def getMeta(self):
        self._setConfig()
        self._checkInstall()

        params = self._getParams()
        response = utils.checkRequiredParams(
            ["key"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        response = self._getMeta(params["key"])
        return json.dumps(response, indent=self._indent)

    def deleteMeta(self):
        t1 = time.time()
        self._setConfig()
        self._checkInstall()

        params = self._getParams()
        response = utils.checkRequiredParams(
            ["key"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        response = self._getMeta(params["key"])
        if response["status"]:
            with utils.openSqliteConn(
                self._getDbPath(), commit=True) as sqliteCursor:
                sql = "delete from meta where `key` = ?"
                sqliteCursor.execute(sql, (params["key"],))

        else:
            response = {
                "status": False,
                "description": "Key {} not found".format(params["key"]),
                "line": utils.lineNo()
            }
            return json.dumps(response, indent=self._indent)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {
                "key": params["key"]
            }
        }
        return json.dumps(response, indent=self._indent)

    def isPush(self):
        response = {
            "status": True,
            "data": {
                "push": True
            }
        }
        return json.dumps(response, indent=self._indent)

    def _open(self, path, mode):
        if "defaultEncoding" in self._config:
            return open(path, mode=mode, encoding=self._config["defaultEncoding"])
        else:
            return open(path, mode)

    """
    This takes a date and applies the given timezone to that date, then retruns the given 
    
    Example:
    date_time = '2020-01-16 09:00:00'
    source_timezone = 'America/Chicago'
    """
    def _getDateByTimezone(self, date_time, source_timezone):
        from pytz import timezone

        server_offset = 'UTC+0000'  # Should this be a source param? In case s2s server moves to a different region...
        normal_format = "%Y-%m-%d %H:%M:%S"
        timezone_format = "%Y-%m-%d %H:%M:%S %Z%z"

        # We need to tell the system what the given dates timezone is
        orderdate = datetime.datetime.strptime(date_time + ' ' + server_offset, timezone_format)

        # Convert order date to given timezone datetime
        # Ref: https://en.wikipedia.org/wiki/List_of_tz_database_time_zones
        converted_date = orderdate.astimezone(timezone(source_timezone))

        # Format the converted date into date reqiured by source
        formatted_date = converted_date.strftime(normal_format)

        return formatted_date

    def getDefaultEncoding(self):
        os_encoding = locale.getpreferredencoding()

        response = {
            "status": True,
            "data": {
                "encoding": str(os_encoding)
            }
        }
        return json.dumps(response, indent=self._indent)
