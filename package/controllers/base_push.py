from builtins import open, float, int, str, len, round, Exception
import json
import os
import time
import imp
import subprocess
import datetime
from dateutil.relativedelta import relativedelta
import locale
from ..shared import pastel_partner2 as pastel
from ..shared import utils
from collections import OrderedDict
import base64
import math

packageDir = os.path.join(os.path.realpath(os.path.join(os.getcwd(),
    os.path.dirname(__file__))), os.pardir)

class Api:
    _indent = False

    _configDir = "config"
    _config = None

    def _setConfig(self):
        params = self._getParams()
        test = False
        if "test" in params:
            test = params["test"] == "true"
        if test:
            configPath = os.path.join(
                packageDir, self._configDir, "test", "config.json"
            )
            configFile = open(configPath, "r")
            self._config = json.loads(configFile.read())
        else:
            configPath = os.path.join(
                packageDir, self._configDir, "config.json"
            )
            configFile = open(configPath, "r")
            self._config = json.loads(configFile.read())
        configFile.close()

    # App will override this
    def _getParams(self):
        return {}

    # App will override this
    def _payload(self):
        return {}

    def _getDbPath(self):
        return os.path.join(packageDir, self._configDir, self._config["dbName"])

    def _checkInstall(self):
        dbPath = self._getDbPath()
        table_results = []
        index_results = []

        with utils.openSqliteConn(self._getDbPath()) as cursor:
            if os.path.isfile(dbPath):
                sql = "select * from sqlite_master where type='table';"
                cursor.execute(sql)
                table_results = cursor.fetchall()

                sql = "select * from sqlite_master where type='index';"
                cursor.execute(sql)
                index_results = cursor.fetchall()

        # Create tables
        if len(table_results) == 0:
            self.createTables()

        # Create indexes
        if len(index_results) == 0:
            self.createIndexes()

    def createTables(self):
        self._setConfig()
        with utils.openSqliteConn(self._getDbPath(), commit=True) as cursor:
            # If not installed then create a new sqlite database
            cursor.execute(
                "create table product (source_product_code, source_variant_code, hash, sync_token, modified)")
            cursor.execute(
                "create table customer (source_customer_code, hash, sync_token, modified)")
            cursor.execute(
                "create table image (source_variant_code, hash, modified)")
            cursor.execute("""create table meta (key, value)""")

        response = {
            "status": True,
            "data": {
                "Create Tables": True
            }
        }
        return json.dumps(response, indent=self._indent)

    def createIndexes(self):
        self._setConfig()
        with utils.openSqliteConn(self._getDbPath(), commit=True) as cursor:
            # product table indexes
            cursor.execute(
                "CREATE INDEX idx_product_source_product_code_source_variant_code ON product(`source_product_code`,`source_variant_code`)")
            cursor.execute(
                "CREATE INDEX idx_product_sync_token ON product(`sync_token`)")

            # customer table indexes
            cursor.execute(
                "CREATE INDEX idx_customer_source_customer_code ON customer(`source_customer_code`)")
            cursor.execute(
                "CREATE INDEX idx_customer_sync_token ON customer(`sync_token`)")

            # image table indexes
            cursor.execute(
                "CREATE INDEX idx_image_source_variant_code ON image(`source_variant_code`)")

        response = {
            "status": True,
            "data": {
                "Indexed": True
            }
        }
        return json.dumps(response, indent=self._indent)

    def reindex(self):
        self._setConfig()
        self._dropIndexes()
        self.createIndexes()
        response = {
            "status": True,
            "data": {
                "Reindex": True
            }
        }
        return json.dumps(response, indent=self._indent)

    def _dropIndexes(self):
        with utils.openSqliteConn(self._getDbPath(), commit=True) as cursor:
            # product table indexes
            cursor.execute(
                "DROP INDEX IF EXISTS idx_product_source_product_code_source_variant_code")
            cursor.execute(
                "DROP INDEX IF EXISTS idx_product_sync_token")

            # customer table indexes
            cursor.execute(
                "DROP INDEX IF EXISTS idx_customer_source_customer_code")
            cursor.execute(
                "DROP INDEX IF EXISTS idx_customer_sync_token")

            # image table indexes
            cursor.execute(
                "DROP INDEX IF EXISTS idx_image_source_variant_code")

    def _getProductsPastel(self):
        path = os.path.join(packageDir, self._configDir, "getProducts.sql")
        f = open(path, "r")
        sql = f.read()
        f.close()
        return sql

    def _getCustomersPastel(self):
        path = os.path.join(packageDir, self._configDir, "getCustomers.sql")
        f = open(path, "r")
        sql = f.read()
        f.close()
        return sql

    def _getProductPastel(self):
        path = os.path.join(packageDir, self._configDir, "getProduct.sql")
        f = open(path, "r")
        sql = f.read()
        f.close()
        return sql

    def _countCustomersPastel(self):
        path = os.path.join(packageDir, self._configDir, "countCustomers.sql")
        f = open(path, "r")
        sql = f.read()
        f.close()
        return sql

    def _getProductBySKUPastel(self):
        path = os.path.join(packageDir, self._configDir, "getProductBySKU.sql")
        f = open(path, "r")
        sql = f.read()
        f.close()
        return sql

    def _getCustomerPastel(self):
        path = os.path.join(packageDir, self._configDir, "getCustomer.sql")
        f = open(path, "r")
        sql = f.read()
        f.close()
        return sql

    def _countProductsPastel(self):
        path = os.path.join(packageDir, self._configDir, "countProducts.sql")
        f = open(path, "r")
        sql = f.read()
        f.close()
        return sql

    def _customOdbc(self, fileName=None):
        if not fileName:
            fileName = "custom.sql"
        path = os.path.join(packageDir, self._configDir, fileName)
        f = open(path, "r")
        sql = f.read()
        f.close()
        return sql


    def _getOrderPastel(self):
        path = os.path.join(packageDir, self._configDir, "getOrder.sql")
        f = open(path, "r")
        sql = f.read()
        f.close()
        return sql

    _productIndex = {
        "source_product_code": 0,
        "source_variant_code": 1,
        "hash": 2,
        "sync_token": 3,
        "modified": 4
    }

    @staticmethod
    def _getProductsLocal():
        return '''
          select * from product where sync_token > ?
          order by sync_token asc limit ?
        '''

    @staticmethod
    def _getProductLocal():
        return '''
          select * from product where sync_token > ? and source_product_code = ?
          order by sync_token asc limit ?
        '''

    _customerIndex = {
        "source_customer_code": 0,
        "hash": 1,
        "sync_token": 2,
        "modified": 3
    }

    @staticmethod
    def _getCustomersLocal():
        return '''
          select * from customer where sync_token > ?
          order by sync_token asc limit ?
        '''

    @staticmethod
    def _getCustomerLocal():
        return '''
          select * from customer
          where source_customer_code = ?
        '''

    metaIndex = {
        "key": 0,
        "value": 1,
    }

    @staticmethod
    def _getMetaLocal():
        return '''
        select `key`, value from meta where `key` = ?
        '''

    @staticmethod
    def _getLastModifiedProduct(sqliteCursor):
        sql = '''
          select * from product
          order by sync_token desc limit 1
        '''
        sqliteCursor.execute(sql)
        lastModified = sqliteCursor.fetchone()
        return lastModified

    def _getProductSyncToken(self, lastModified):
        if lastModified is None:
            sync_token = 1
        else:
            sync_token = int(lastModified[self._productIndex["sync_token"]]) + 1
        return sync_token

    def _updateHash(self, row, sqliteCursor):
        params = {"source_id": None}
        auditRow = {
            self._productIndex["sync_token"]: None,
        }
        rowHash = utils.getHash(json.dumps(self._transformProduct(
            params, row, auditRow)))

        # Compare hash to existing row
        sql = "select * from product where source_product_code = ?" + \
              " and source_variant_code = ?"
        sqliteCursor.execute(
            sql, ((
                row["source_product_code"], row["source_variant_code"]
            ))
        )
        oldProduct = sqliteCursor.fetchone()  # Assuming there is only one match

        if oldProduct is None:
            lastModified = self._getLastModifiedProduct(sqliteCursor)
            sql = "insert into product values (?, ?, ?, ?, ?)"
            sqliteCursor.execute(
                sql, ((
                          row["source_product_code"],
                          row["source_variant_code"],
                          rowHash,
                          self._getProductSyncToken(lastModified),
                          utils.getTimestamp()
                      ))
            )

        elif oldProduct[self._productIndex["hash"]] == rowHash:
            pass

        else:
            # Update if hash has changed
            lastModified = self._getLastModifiedProduct(sqliteCursor)
            sql = "update product set" \
                  " hash = ?," + \
                  " modified = ?," \
                  " sync_token = ?" \
                  " where" + \
                  " source_product_code = ?" + \
                  " and source_variant_code = ?"
            sqliteCursor.execute(
                sql,
                ((
                     rowHash,
                     utils.getTimestamp(),
                     self._getProductSyncToken(lastModified),
                     row["source_product_code"],
                     row["source_variant_code"],
                 ))
            )

    @staticmethod
    def _getLastModifiedCustomer(sqliteCursor):
        sql = '''
          select * from customer
          order by sync_token desc limit 1
        '''
        sqliteCursor.execute(sql)
        lastModified = sqliteCursor.fetchone()
        return lastModified

    def _getCustomerSyncToken(self, lastModified):
        if lastModified is None:
            sync_token = 1
        else:
            sync_token = int(lastModified[self._customerIndex["sync_token"]]) + 1
        return sync_token

    def _updateCustomerHash(self, row, sqliteCursor):
        params = {"source_id": None}
        auditRow = {
            self._customerIndex["sync_token"]: None
        }
        rowHash = utils.getHash(json.dumps(self._transformCustomer(
            params, row, auditRow)))

        # Compare hash to existing row
        sql = "select * from customer where source_customer_code = ?"
        sqliteCursor.execute(
            sql, ((
                row["source_customer_code"],
            ))
        )
        oldCustomer = sqliteCursor.fetchone()  # Assuming there is only one match

        if oldCustomer is None:
            lastModified = self._getLastModifiedCustomer(sqliteCursor)
            sql = "insert into customer values (?, ?, ?, ?)"
            sqliteCursor.execute(
                sql, ((
                          row["source_customer_code"],
                          rowHash,
                          self._getCustomerSyncToken(lastModified),
                          utils.getTimestamp(),
                      ))
            )

        elif oldCustomer[self._customerIndex["hash"]] != rowHash:
            lastModified = self._getLastModifiedCustomer(sqliteCursor)
            # Update if hash has changed
            sql = "update customer set" \
                  " hash = ?," + \
                  " modified = ?," \
                  " sync_token = ?" \
                  " where" + \
                  " source_customer_code = ?"
            sqliteCursor.execute(
                sql,
                ((
                     rowHash,
                     utils.getTimestamp(),
                     self._getCustomerSyncToken(lastModified),
                     row["source_customer_code"],
                 ))
            )

    def _imageHashChanged(self, row, encoded, sqliteCursor):
        """
        Update image hash in local database.
        Return true if a row was inserted or updated, otherwise return false.
        """
        # Include filename and ItemCode in hash.
        # In case the same picture deleted and added again,
        # or the same picture is used for multiple products.
        imageHash = utils.getHash("{} {} {}".format(encoded, row["filename"], row["source_variant_code"]))

        # Compare hash to existing row
        hashIndex = 0
        sql = "select hash from image where source_variant_code = ?"
        sqliteCursor.execute(
            sql, (row["source_variant_code"],)
        )
        oldImage = sqliteCursor.fetchone()  # Assuming there is only one match

        if oldImage is None:
            sql = "insert into image values (?, ?, ?, ?)"
            sqliteCursor.execute(
                sql, ((
                          row["source_variant_code"],
                          row["filename"],
                          imageHash,
                          utils.getTimestamp()
                      ))
            )
            return True

        elif oldImage[hashIndex] != imageHash:
            # Update if hash has changed
            sql = "update image set" \
                  " hash = ?," + \
                  " modified = ?" \
                  " where" + \
                  " source_variant_code = ?"
            sqliteCursor.execute(
                sql,
                ((
                     imageHash,
                     utils.getTimestamp(),
                     row["source_variant_code"]
                 ))
            )
            return True

        else:
            return False

    @staticmethod
    def _getQty(product):
        qty_on_hand = product["qty_on_hand"]
        if qty_on_hand is None:
            qty_on_hand = 0
        qty_on_sales_order = product["qty_on_sales_order"]
        if qty_on_sales_order is None:
            return qty_on_hand
        else:
            return qty_on_hand - qty_on_sales_order

    def _transformProduct(self, params, result, sync_token):
        """
        Given the result of getProduct we return a dictionary
        with required structure ready to be converted to JSON.
        """
        source_product = OrderedDict()
        source_product["source"] = OrderedDict()
        source_product["product"] = OrderedDict()
        source_product["product"]["variants"] = OrderedDict()

        source_product = self._beforeProductTransform(params, result, source_product)

        source_product["source"]["source_id"] = params["source_id"]
        source_product["source"]["product_active"] = result["product_active"]
        source_product["source"]["source_product_code"] = str(result["source_product_code"])
        source_product["source"]["sync_token"] = sync_token

        source_product["product"]["options"] = []
        source_product["product"]["body_html"] = result["body_html"]
        source_product["product"]["collection"] = result["collection"]
        source_product["product"]["product_type"] = result["product_type"]
        source_product["product"]["tags"] = str(result["tags"]).lower()
        source_product["product"]["title"] = result["title"]
        source_product["product"]["vendor"] = result["vendor"]
        source_product["product"]["meta"] = self._parseMetaFields(result)

        source_product["product"]["variants"]["source_variant_code"] = str(result["source_variant_code"])
        # ensure backward compatible
        if "variants.sku" in result:
            source_product["product"]["variants"]["sku"] = str(result["variants.sku"])
        else:
            source_product["product"]["variants"]["sku"] = str(result["source_variant_code"])
        source_product["product"]["variants"]["barcode"] = result["variants.barcode"]
        source_product["product"]["variants"]["inventory_management"] = result["variants.inventory_management"]

        source_product["product"]["variants"]["qty_availability"] = []

        # For the weight, check for "variants.weight" key first, then check for "variants.grams"
        # "variants.weight" seems to be standard
        weight = 0
        if "variants.weight" in result:
            weight = result["variants.weight"]
        elif "variants.grams" in result:
            weight = result["variants.grams"]
        source_product["product"]["variants"]["grams"] = int(weight)

        source_product = self._parsePriceTiers(result, source_product)
        source_product = self._parseQtyAvailability(result, source_product)
        source_product = self._parseOptions(result, source_product)

        return self._afterProductTransform(params, result, source_product)

    def _beforeProductTransform(self, params, result, source_product):
        """
        Hook method that gets called before a product has been transformed.
        For default behavior just return the passed "source_product"
        """
        return source_product

    def _afterProductTransform(self, params, result, source_product):
        """
        Hook method that gets called after a product has been transformed.
        For default behavior just return the passed "source_product"
        """
        return source_product

    @staticmethod
    def _parseOptions(result, source_product):
        """
        Parses the optionX fields in the query result.
        This uses either the old format of "variants.optionX" or the newer
        version of key/value pairs in the form "variants.optionX_name" and "variants.optionX_value"
        """
        source_product_result = source_product

        # Test and add the 3 key/value pairs for options
        optionIndex = 1
        options = []
        for i in range(1, 4):
            nameKey = 'variants.option{}_name'.format(i)
            valueKey = 'variants.option{}_value'.format(i)
            # Add this option to product if both key/value exists and is not empty
            if nameKey in result and result[nameKey] is not None and str(result[nameKey]).strip() != '' and \
                    valueKey in result and result[valueKey] is not None and str(result[valueKey]).strip() != '':
                option = OrderedDict()
                option["name"] = result[nameKey]
                option["position"] = '{}'.format(optionIndex)
                option["value"] = "undefined"
                options.append(option)

                optionKey = 'option{}'.format(optionIndex)
                source_product_result["product"]["variants"][optionKey] = result[valueKey]
                optionIndex += 1
            else:
                # Fall back to older "optionX" version
                key = 'variants.option{}'.format(i)
                if key in result and result[key] is not None and str(result[key]).strip() != '':
                    source_product['product']['variants']['option{}'.format(i)] = result[key]
        source_product_result["product"]["options"] = options
        return source_product_result

    @staticmethod
    def _parsePriceTiers(row, source_product):
        """
        Parses the "csv_price_tiers" field.
        If we have a CSV list of prices use that, otherwise use the default.
        """
        import xml.sax.saxutils as saxutils

        source_product_result = source_product

        source_product_result["product"]["variants"]["price_tiers"] = []

        # Add retail price tier
        if "variants.retail_price" in row:
            price_tier = OrderedDict()
            price_tier["tier"] = "retail"
            price_tier["price"] = row["variants.retail_price"]
            source_product_result["product"]["variants"]["price_tiers"].append(price_tier)

        # Add wholesale price tier if it exists
        if "variants.wholesale_price" in row:
            price_tier = OrderedDict()
            price_tier["tier"] = "wholesale"
            price_tier["price"] = row["variants.wholesale_price"]
            source_product_result["product"]["variants"]["price_tiers"].append(price_tier)

        # Add dealer price tier if it exists
        if "variants.dealer_price" in row:
            price_tier = OrderedDict()
            price_tier["tier"] = "dealer"
            price_tier["price"] = row["variants.dealer_price"]
            source_product_result["product"]["variants"]["price_tiers"].append(price_tier)

        # Add distribution price tier if it exists
        if "variants.distribution_price" in row:
            price_tier = OrderedDict()
            price_tier["tier"] = "distribution"
            price_tier["price"] = row["variants.distribution_price"]
            source_product_result["product"]["variants"]["price_tiers"].append(price_tier)

        if "csv_price_tiers" in row and row["csv_price_tiers"] is not None:
            default_price = 0
            # The stuff function used in the sql queries
            # will encode special characters as html entities,
            # for example, "&" becomes "&amp;".
            # Call html.unescape to convert back to special characters
            prices = saxutils.unescape(row["csv_price_tiers"])
            prices = prices.split(",")
            for priceStr in prices:
                priceItem = priceStr.split("|")
                if len(priceItem) == 2 and priceItem[0].strip() != "":
                    tier = priceItem[0]
                    # Throw away everything after the second decimal
                    price = float("{0:.2f}".format(float(priceItem[1])))
                    if tier == row["default_price_tier"]:
                        default_price = price
                    price_tier = OrderedDict()
                    price_tier["tier"] = tier
                    price_tier["price"] = price
                    source_product_result["product"]["variants"]["price_tiers"].append(price_tier)
            source_product_result["product"]["variants"]["price"] = default_price
        else:
            if "variants.retail_price" in row:
                source_product_result["product"]["variants"]["price"] = row["variants.retail_price"]
            else:
                source_product_result["product"]["variants"]["price"] = 0

        return source_product_result

    @staticmethod
    def _parseQtyAvailability(row, source_product):
        """
        Parses the "csv_qty_availability" field.
        If we have a CSV list of warehouse quantities use that, otherwise use the default.
        """
        import xml.sax.saxutils as saxutils

        source_product_result = source_product

        if "csv_qty_availability" in row and row["csv_qty_availability"] is not None:
            source_product_result["product"]["variants"]["qty_availability"] = []
            default_qty = 0
            quantities = saxutils.unescape(row["csv_qty_availability"])
            quantities = quantities.split(",")
            for qtyStr in quantities:
                qtyItem = qtyStr.split("|")
                if len(qtyItem) == 2 and qtyItem[0].strip() != "":
                    description = qtyItem[0]
                    # Qty must be an integer
                    qty = math.trunc(float(qtyItem[1]))
                    if description == row["default_qty_availability"]:
                        default_qty = qty
                    availability = OrderedDict()
                    availability["qty"] = qty
                    availability["description"] = description
                    source_product_result["product"]["variants"]["qty_availability"].append(availability)

        # Pastel Partner calculates quantities in a different way
        source_product_result["product"]["variants"]["qty"] = int(Api._getQty(row))

        return source_product_result

    @staticmethod
    def _parseSegments(row):
        segments = []

        # Anything that starts with segment| gets converted into a segment
        # Expecting the following fields for Segments:
        # owner|type|key|operator|value
        # The sql on query must follow segment structure
        # 'value'  as "segment|source|products|meta_rep_id|equal"
        for key in row:
            if key.startswith("segment|"):
                parts = key.split("|")
                segment = OrderedDict()
                segment["owner"] = parts[1].strip()
                segment["type"] = parts[2].strip()
                segment["key"] = parts[3].strip()
                segment["operator"] = parts[4].strip()
                segment["value"] = row[key]
                segments.append(segment)

        return segments

    @staticmethod
    def _parseMetaFields(row):
        import xml.sax.saxutils as saxutils
        metaFields = []
        for key in row:
            if key.startswith("meta_"):
                # Value from database might be null
                if row[key] is not None:
                    meta = OrderedDict()
                    meta["key"] = str(key[5:])  # Remove "meta_" from key
                    meta["value"] = saxutils.unescape(str(row[key]))
                    metaFields.append(meta)

        return metaFields

    @staticmethod
    def _parseCustomerName(row):
        first_name = row["first_name"]
        last_name = row["last_name"]
        company = row["address.company"]
        if first_name is None and last_name is None:
            # Note that company could also be None
            first_name = company
        return company, first_name, last_name

    def _transformCustomer(self, params, row, sync_token):
        """
        Given the result of getCustomer we return a dictionary
        with required structure ready to be converted to JSON.
        """

        source_customer = OrderedDict()
        source_customer["source"] = OrderedDict()
        source_customer["source"]["source_id"] = params["source_id"]
        source_customer["source"]["customer_active"] = row["customer_active"]
        source_customer["source"]["source_customer_code"] = \
            row["source_customer_code"]
        source_customer["source"]["sync_token"] = sync_token

        company, first_name, last_name = self._parseCustomerName(row)
        customer = OrderedDict()
        customer["first_name"] = first_name
        customer["last_name"] = last_name
        customer["email"] = row["email"]
        if row["accepts_marketing"] == 1:
            customer["accepts_marketing"] = True
        else:
            customer["accepts_marketing"] = False
        address = OrderedDict()
        address["address1"] = row["address.address1"]
        address["address2"] = row["address.address2"]
        address["city"] = row["address.city"]
        address["country"] = row["address.country"]
        address["country_code"] = row["address.country_code"]
        address["province"] = row["address.province"]
        address["province_code"] = row["address.province_code"]
        address["zip"] = row["address.zip"]
        address["company"] = company
        address["first_name"] = row["first_name"]
        address["last_name"] = row["last_name"]
        address["phone"] = row["address.phone"]
        customer["addresses"] = [address]

        customer["price_tier"] = str(row["price_tier"])
        customer["qty_availability"] = str(row["qty_availability"])

        customer["meta"] = self._parseMetaFields(row)
        customer["segments"] = self._parseSegments(row)

        source_customer["customer"] = customer

        return source_customer

    def _transformOrder(self, params, rows):
        i = 0
        source_order = OrderedDict();

        for row in rows:
            # Read order details from the first line item
            if i == 0:
                contact = row.Contact.split(' ')
                if len(contact) >= 2:
                    firstName = contact[0]
                    lastName = contact[1]
                else:
                    firstName = contact[0]
                    lastName = ''

                source_order["sources"] = [OrderedDict()]
                source_order["sources"][0]["source_id"] = params["source_id"]
                source_order["sources"][0]["source_order_code"] = row.DocumentNumber.strip()
                source_order["sources"][0]["source_customer_code"] = row.CustomerCode.strip()

                source_order["source_order"] = OrderedDict()
                source_order["source_order"]["id"] = row.DocumentNumber.strip()

                source_order["source_order"]["notes"] = []#row.OrderNumber.strip()
                notes = []
                if 'notes1' in row and row['notes1'] is not None and row['notes1'].strip() != '':
                    notes.append(row['notes1'])
                if 'notes2' in row and row['notes2'] is not None and row['notes2'].strip() != '':
                    notes.append(row['notes2'])
                if 'notes3' in row and row['notes3'] is not None and row['notes3'].strip() != '':
                    notes.append(row['notes3'])
                source_order["source_order"]["notes"] = notes

                source_order["source_order"]["customer"] = OrderedDict()
                source_order["source_order"]["customer"]["first_name"] = firstName
                source_order["source_order"]["customer"]["last_name"] = lastName
                source_order["source_order"]["customer"]["email"] = row.Email.strip()

                address0 = OrderedDict()
                address0["address1"] = row.PostAddress01.strip()
                address0["address2"] = row.PostAddress02.strip()
                address0["city"] = row.PostAddress03.strip()
                address0["company"] = row.CustomerDesc.strip()
                address0["country"] = "South Africa"
                address0["first_name"] = ""
                address0["last_name"] = ""
                address0["phone"] = ""
                address0["province"] = row.PostAddress04.strip()
                address0["zip"] = row.PostAddress05.strip()
                address0["country_code"] = "ZA"
                source_order["source_order"]["customer"]["addresses"] = [address0]

                billing_address = OrderedDict()
                billing_address["address1"] = row.PostAddress01.strip()
                billing_address["address2"] = row.PostAddress02.strip()
                billing_address["city"] = row.PostAddress03.strip()
                billing_address["company"] = row.CustomerDesc.strip()
                billing_address["country"] = "South Africa"
                billing_address["first_name"] = ""
                billing_address["last_name"] = ""
                billing_address["phone"] = ""
                billing_address["province"] = row.PostAddress05.strip()
                billing_address["zip"] = row.PostAddress04.strip()
                billing_address["country_code"] = "ZA"
                source_order["source_order"]["billing_address"] = billing_address

                shipping_address = OrderedDict()
                shipping_address["address1"] = row.DelAddress01.strip()
                shipping_address["address2"] = row.DelAddress02.strip()
                shipping_address["city"] = row.DelAddress03.strip()
                shipping_address["company"] = row.CustomerDesc.strip()
                shipping_address["country"] = "South Africa"
                shipping_address["first_name"] = firstName
                shipping_address["last_name"] = lastName
                shipping_address["phone"] = row.Telephone.strip()
                shipping_address["province"] = row.DelAddress05.strip()
                shipping_address["zip"] = row.DelAddress04.strip()
                shipping_address["country_code"] = "ZA"
                source_order["source_order"]["shipping_address"] = shipping_address

                source_order["source_order"]["line_items"] = []
                source_order["source_order"]["shipping_lines"] = []

            # Each additional row contains a line item
            # Why do some orders have items with zero qty?
            if int(row.Qty) > 0:
                line_item = OrderedDict()
                line_item["source_id"] = params["source_id"]
                line_item["sku"] = row.ItemCode.strip()
                line_item["title"] = row.Description.strip()
                line_item["price"] = float(row.UnitPrice)
                line_item["qty"] = int(row.Qty)
                line_item["code"] = "item"
                tax_line = OrderedDict()
                tax_line["price"] = (float(row.InclusivePrice) * int(row.Qty)) - (
                    float(row.UnitPrice) * int(row.Qty)
                )
                tax_line["rate"] = 14
                tax_line["title"] = "VAT"
                tax_line["code"] = "taxed"
                line_item["tax_lines"] = [tax_line]
                source_order["source_order"]["line_items"].append(line_item)

            i += 1

        return source_order


    def _getDocumentHeaderOrder(self, order, source_customer_code, address, deliveryAddress):

        params = self._getParams()

        dh = pastel.getDocumentHeader(optional=True)
        dh["Customer Code"]["val"] = source_customer_code
        dh["Date"]["val"] = pastel.getPastelDate(
            utils.dateFromISO8601(order["modified"])
        )

        if "notes" in order:
            dh["Invoice Message 1"]["val"] = order["notes"]
        else:
            dh["Invoice Message 1"]["val"] = "Web sale"

        # display/set order as including or excluding
        has_taxes_incl = False
        if "has_taxes_incl" in order and order["has_taxes_incl"] == 1:
            has_taxes_incl = True
        if has_taxes_incl:
            dh["Inc/Exc"]["val"] = "Y"
        else:
            dh["Inc/Exc"]["val"] = "N"

        # Insert placeholder.
        # We only know the discount percentage after loop line and shipping items.
        dh["Discount"]["val"] = "---DISCOUNT---"

        dh["Delivery Address 1"]["val"] = utils.truncateString(deliveryAddress["address_1"], 30)
        dh["Delivery Address 2"]["val"] = utils.truncateString(deliveryAddress["address_2"], 30)
        dh["Delivery Address 3"]["val"] = utils.truncateString(deliveryAddress["address_3"], 30)
        dh["Delivery Address 4"]["val"] = utils.truncateString(deliveryAddress["address_4"], 30)
        dh["Delivery Address 5"]["val"] = utils.truncateString(deliveryAddress["address_5"], 30)
        dh["Job Code"]["val"] = "00"

        ignore_due_date = 'false'
        if "ignore_due_date" in params:
            ignore_due_date = params["ignore_due_date"]

        if ignore_due_date == 'false':
            closing_date = utils.dateFromISO8601(order["modified"])
            if "use_customer_payment_terms" in params:
                if params["use_customer_payment_terms"] == 'true':
                    terms = self._getCustomerPaymentTerms(source_customer_code)
                    if terms > 0:
                        closing_date = self._getOrderDueDate(utils.dateFromISO8601(order["modified"]), terms)

            dh["Closing Date"]["val"] = pastel.getPastelDate(closing_date)

        dh["Ship / Deliver"]["val"] = utils.truncateString(deliveryAddress["phone"], 16)

        # dh["Description"]["val"] = "Description"
        # dh["ExemptRef"]["val"] = "ExemptRef"
        if "source_order_code" in params:
            dh["Order Number"]["val"] = params["source_order_code"]
        else:
            dh["Order Number"]["val"] = order["channel_order_code"]

        # dh["Sales Analysis Code"] = "00003"

        # Split the notes into several fields with 30 chars max
        dh["Invoice Message 1"]["val"] = order["notes"][0:30]
        dh["Invoice Message 2"]["val"] = order["notes"][30:60]
        dh["Invoice Message 3"]["val"] = order["notes"][60:90]

        dh["Address 1"]["val"] = utils.truncateString(address["address_1"], 30)
        dh["Address 2"]["val"] = utils.truncateString(address["address_2"], 30)
        dh["Address 3"]["val"] = utils.truncateString(address["address_3"], 30)
        dh["Address 4"]["val"] = utils.truncateString(address["address_4"], 30)
        dh["Address 5"]["val"] = utils.truncateString(address["address_5"], 30)

        if "exchange_rate" in params:
            dh["Exchange Rate"]["val"] = params["exchange_rate"]

        return dh

    def _getOrderDueDate(self, order_date, terms):
        terms_month = {
            30: 1,
            60: 2,
            90: 3,
            120: 4
        }
        if terms in terms_month:
            order_month_due = order_date + relativedelta(months=terms_month[terms])
            closing_date = utils.getLastDayOfMonth(order_month_due)

            return closing_date

        return order_date

    def _getCustomerPaymentTerms(self, source_customer_code):
        """
        Get the payment terms for a customer by source customer code
        PaymentTerms are always in days
        - 0 days [Current]
        - 30 days
        - 60 days
        - 90 days
        - 120 Days
        """

        # TODO Partner supports "Monthly Terms" as well as "Daily Terms". Currently the 'use_customer_payment_terms' feature only supports monthly terms. Will need to tweak this function to support daily terms
        self._setConfig()
        params = {
            "sql": "select PaymentTerms from CustomerMaster where CustomerCode='" + source_customer_code + "'"
        }
        conn, cursor = pastel.getConn(self._config["dsn"])
        response = utils.runODBC(params, cursor)
        terms = 0
        if len(response["data"]["rows"]) > 0:
            terms = response["data"]["rows"][0]["paymentterms"]
        return terms


    def _getDocumentLineOrder(self, line_item, multi_store):
        params = self._getParams()
        dl = pastel.getDocumentLine()

        # if tax rate is zero and we have tax_type_exempt use it else
        tax_type = "01"
        tax_line = line_item["tax_lines"][0]
        if int(tax_line['rate']) == 0:
            if "tax_type_exempt" in params:
                tax_type = params["tax_type_exempt"]
        else:
            if "tax_type" in params:
                tax_type = params["tax_type"]

        dl["Cost Price"]["val"] = line_item["price"]
        dl["Quantity"]["val"] = line_item["qty"]
        dl["Unit Selling Price"]["val"] = line_item["price"]

        # Set inclusive price if set
        if "price_incl" in line_item and line_item["price_incl"] is not None:
            dl["Inclusive Price"]["val"] = line_item["price_incl"]

        dl["Unit"]["val"] = self._getUomBySku(line_item["sku"])
        dl["Tax Type"]["val"] = tax_type
        dl["Code"]["val"] = line_item["sku"]
        dl["Description"]["val"] = line_item["title"]
        dl["Line Type"]["val"] = "4"
        dl["Multi-store"]["val"] = multi_store

        return dl


    def _getImportCustomer(self, customer, source_customer_code):
        default_address = customer["default_address"]

        ic = pastel.getImportCustomer()

        ic["Number"]["val"] = source_customer_code
        ic["Description"]["val"] = default_address["first_name"] + " " + \
                                   default_address["last_name"]
        ic["Address 1"]["val"] = default_address["first_name"] + " " + \
                                 default_address["last_name"]
        ic["Address 2"]["val"] = default_address["address1"]
        ic["Address 3"]["val"] = default_address["address2"]
        ic["Address 4"]["val"] = default_address["city"] + ", " + default_address[
            "country"]
        ic["Address 5"]["val"] = default_address["zip"]
        ic["Tax Exempt"]["val"] = "N"
        ic["Settlement Terms"]["val"] = "01"
        ic["Price List"]["val"] = "1"
        ic["Blocked"]["val"] = "N"
        ic["Discount"]["val"] = "0"
        ic["Exclusive"]["val"] = "N"
        ic["Statements"]["val"] = "1"
        ic["Open Item"]["val"] = "Y"

        ic["Currency Code"]["val"] = self._getCustomerCurrencyCode(source_customer_code)
        ic["Payment Terms"]["val"] = "00"
        ic["MonthOrDay"]["val"] = "M"
        ic["StatPrintOrEmail"]["val"] = "1"
        ic["DocPrintOrEmail"]["val"] = "1"
        ic["Email"]["val"] = customer["email"]
        ic["Cash Customer"]["val"] = "N"

        return ic

    def _getCustomerCurrencyCode(self, source_customer_code):
        params = self._getParams()

        if "use_customer_currency" in params and params["use_customer_currency"] == "true":
            self._setConfig()
            params = {
                "sql": "select CurrencyCode FROM CustomerMaster where CustomerCode='" + source_customer_code + "'"
            }
            conn, cursor = pastel.getConn(self._config["dsn"])
            response = utils.runODBC(params, cursor)
            if len(response["data"]["rows"]) > 0:
                return response["data"]["rows"][0]["currencycode"]

        return "00" # default

    def _getUomBySku(self, sku):
        params = self._getParams()

        if "use_line_item_uom" in params and params["use_line_item_uom"]:
            self._setConfig()
            params = {
                "sql": "select UnitSize from Inventory where ItemCode='" + sku + "'"
            }
            conn, cursor = pastel.getConn(self._config["dsn"])
            response = utils.runODBC(params, cursor)
            if len(response["data"]["rows"]) > 0:
                return response["data"]["rows"][0]["unitsize"]

        return "EACH" #default

    def reload(self):
        imp.reload(pastel)
        imp.reload(utils)
        response = {
            "status": True,
            "data": {
                "reload": True
            }
        }
        return json.dumps(response, indent=self._indent)

    def echo(self):
        params = self._getParams()
        response = utils.echo(params)
        return json.dumps(response, indent=self._indent)

    def time(self):
        response = utils.getTime()
        return json.dumps(response, indent=self._indent)

    # Do not allow writing to sqlite database using this function,
    # i.e. do not pass commit=True to utils.openSqliteConn.
    def runLocal(self):
        self._setConfig()
        params = self._getParams()
        with utils.openSqliteConn(self._getDbPath()) as cursor:
            response = utils.runSQLite(params, cursor)
            return json.dumps(response, indent=self._indent)

    def runCustom(self):
        self._setConfig()
        params = self._getParams()
        fileName = None
        if "fileName" in params:
            fileName = params["fileName"]
        if "limit" in params:
            limit = int(params["limit"])
        else:
            limit = 500

        # use sql in params otherwise use filename
        if "sql" in params:
            sql = params["sql"]
        else:
            sql = self._customOdbc(fileName)

        if sql == "":
            return json.dumps({
                "status": False,
                "description": "Sql not given. Use query param 'fileName=xyz.sql' or 'sql=select * from...'",
                "line": utils.lineNo()
            }, indent=self._indent)

        params = {
            "sql": sql,
            "limit": limit
        }
        with pastel.openConn(self._config["dsn"]) as cursor:
            response = utils.runODBC(params, cursor)
            return json.dumps(response)

    def getCategoryCodes(self):
        self._setConfig()
        params = {
            "sql": "Select * from  InventoryCategory"
        }
        conn, cursor = pastel.getConn(self._config["dsn"])
        response = utils.runODBC(params, cursor)
        return json.dumps(response, indent=self._indent)

    def getMultiStoreCodes(self):
        self._setConfig()
        params = {
            "sql": "select distinct StoreCode FROM MultiStoreTrn order by StoreCode"
        }
        conn, cursor = pastel.getConn(self._config["dsn"])
        response = utils.runODBC(params, cursor)
        return json.dumps(response, indent=self._indent)

    def getCustomerCodes(self):
        self._setConfig()
        params = {
            "sql": "select CustomerCode FROM CustomerMaster order by CustomerCode"
        }
        conn, cursor = pastel.getConn(self._config["dsn"])
        response = utils.runODBC(params, cursor)
        return json.dumps(response, indent=self._indent)

    def log(self):
        params = self._getParams()
        response = utils.log(params, packageDir)
        return json.dumps(response, indent=self._indent)

    def auditReset(self):
        t1 = time.time()
        self._setConfig()
        dbPath = self._getDbPath()
        if os.path.isfile(dbPath):
            os.remove(dbPath)
        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {}
        }
        return json.dumps(response, indent=self._indent)

    def auditResetCustomers(self):
        """
        Remove all customers from the local audit database
        """
        t1 = time.time()
        self._setConfig()
        with utils.openSqliteConn(self._getDbPath(), commit=True) as sqliteCursor:
            sql = "delete from customer"
            sqliteCursor.execute(sql)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {}
        }
        return json.dumps(response, indent=self._indent)

    def auditResetProducts(self):
        """
        Remove all products from the local audit database
        """
        t1 = time.time()
        self._setConfig()
        with utils.openSqliteConn(self._getDbPath(), commit=True) as sqliteCursor:
            sql = "delete from product"
            sqliteCursor.execute(sql)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {}
        }
        return json.dumps(response, indent=self._indent)

    # We assume getImages will always run after auditProducts
    def getImages(self):
        t1 = time.time()
        self._setConfig()

        self._checkInstall()

        params = self._getParams()
        response = utils.checkRequiredParams(
            ["source_product_code", "sync_token", "source_id"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        # Only fetch images if the product has changed since sync_token
        with utils.openSqliteConn(self._getDbPath(), commit=True) as sqliteCursor:
            sql = self._getProductLocal()
            limit = 1;
            sqliteCursor.execute(sql, (
                int(params["sync_token"]),
                params["source_product_code"],
                limit
            ))

            rows = []
            # We only need to know if there was a change on product
            sourceVariantCodeIndex = 1
            sqliteRow = sqliteCursor.fetchone()
            while sqliteRow is not None:
                # Pastel partner only supports one image per product
                with pastel.openConn(self._config["dsn"]) as cursor:
                    sql = '''
                        select
                          ltrim(rtrim(isnull(i.Picture, '')))  as filename,
                          i.ItemCode                           as source_variant_code
                        from Inventory i
                        where ltrim(rtrim(isnull(i.Picture, ''))) <> ''
                        and i.ItemCode = ?
                    '''

                    source_variant_code = sqliteRow[sourceVariantCodeIndex]
                    cursor.execute(sql, source_variant_code)
                    imageRows = utils.getRowsODBC(cursor, 1);

                    if len(imageRows) > 0:
                        row = imageRows[0]
                        path = os.path.join(self._config["pastelDataPath"], "images", row["filename"])
                        # We do not support bitmaps
                        if ".bmp" in path:
                            t2 = time.time()
                            response = {
                                "status": False,
                                "code": None,
                                "timer": str(t2 - t1),
                                "description": "Bitmaps not supported, source_variant_code: {} path: {}".format(source_variant_code, path)
                            }
                            return json.dumps(response, indent=self._indent)

                        if os.path.isfile(path):
                            with open(path, "rb") as f:
                                encoded = base64.b64encode(f.read())
                                encoded = encoded.decode("utf-8")

                            # We only return the image if the hash has changed,
                            # i.e. sync_token only applies to products.
                            # To test or re-fetch the same image we need a flag.
                            skipImageHash = False
                            if "skip_image_hash" in params:
                                skipImageHash = params["skip_image_hash"] == "true"

                            # Check hash against local db
                            # If skipImageHash is True we still have to call _imageHashChanged,
                            # otherwise we will fetch the image twice, on add and for the first update.
                            if (self._imageHashChanged(row, encoded, sqliteCursor) or skipImageHash):
                                # The hash has changed or image not found
                                image = OrderedDict()
                                image["image"] = OrderedDict()
                                image["image"]["image"] = encoded
                                # Should actually be called source_image_code,
                                # lets try to be backward compatible...
                                image["image"]["image_id"] = row["filename"]
                                image["image"]["action"] = "I" # Always insert
                                image["source"] = OrderedDict()
                                # This is how we tell stock2shop to stop fetching.
                                # Not ideal...
                                image["source"]["sync_token"] = False
                                image["source"]["source_id"] = params["source_id"]
                                image["source"]["source_product_code"] = params["source_product_code"]
                                image["source"]["source_variant_code"] = source_variant_code
                                rows.append(image)

                sqliteRow = sqliteCursor.fetchone()

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {
                "source_images": rows,
            }
        }
        return json.dumps(response, indent=self._indent)

    def getProductBySKU(self):
        t1 = time.time()
        self._setConfig()
        params = self._getParams()

        response = utils.checkRequiredParams(["sku", "multi_store"], params)
        if response is not None:
            return json.dumps(response, indent=self._indent)

        rows = []
        with pastel.openConn(self._config["dsn"]) as cursor:
            sql = self._getProductBySKUPastel()
            sql = utils.bindSqlParams(sql, {
                "multi_store": str(params["multi_store"]),
                "sku": str(params["sku"])
            })

            cursor.execute(sql)
            rows = utils.getRowsODBC(cursor, 1)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {"source_products": rows}
        }
        return json.dumps(response, indent=self._indent)

    def countProducts(self):
        t1 = time.time()
        self._setConfig()

        with pastel.openConn(self._config["dsn"]) as cursor:
            sql = self._countProductsPastel()

            # To push products from different stores,
            # multiple apifact packages must be used
            sql = utils.bindSqlParams(sql, {
                "multi_store": self._config["multi_store"]
            })

            cursor.execute(sql)
            rows = utils.getRowsODBC(cursor, 1)
            product_count = int(rows[0]["product_count"])

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {
                "count": product_count
            }
        }
        return json.dumps(response, indent=self._indent)

    def countCustomers(self):
        t1 = time.time()
        self._setConfig()

        params = self._getParams()

        with pastel.openConn(self._config["dsn"]) as cursor:
            sql = self._countCustomersPastel()
            sql = utils.bindSqlParams(sql, params)

            cursor.execute(sql)
            rows = utils.getRowsODBC(cursor, 1)

        # "count" must be present and be an int,
        # it is used by push.exe
        data = OrderedDict()
        if "count" in rows[0]:
            data["count"] = int(rows[0]["count"])

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": data
        }
        return json.dumps(response, indent=self._indent)

    def auditProductsBatch(self):
        """
        This function kicks off a process that reads all the products
        in the SQL server database and updates the hashes in sqlite.
        If the sqlite hash differs from the time stamp the product row is updated.
        getProducts can then check the sync token against sqlite.
        """
        t1 = time.time()
        products = 0
        self._setConfig()

        self._checkInstall()
        params = self._getParams()

        response = utils.checkRequiredParams(
            ["audit_lower_limit", "audit_upper_limit"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        # Read products from SQL server
        with pastel.openConn(self._config["dsn"]) as cursor:
            sql = self._getProductsPastel()
            sql = utils.bindSqlParams(sql, {
                "audit_lower_limit": int(params["audit_lower_limit"]),
                "audit_upper_limit": int(params["audit_upper_limit"]),
                # To push products from different stores,
                # multiple apifact packages must be used
                "multi_store": self._config["multi_store"],
            })

            cursor.execute(sql)
            rows = utils.getRowsODBC(cursor, 1)

            # Get local connection
            if len(rows) > 0:
                with utils.openSqliteConn(self._getDbPath(), commit=True) as sqliteCursor:
                    while len(rows) > 0 and products <= int(params["audit_upper_limit"]):
                        products += 1
                        # Update hash for this product
                        self._updateHash(rows[0], sqliteCursor)
                        rows = utils.getRowsODBC(cursor, 1)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {
                "products": products
            }
        }
        return json.dumps(response, indent=self._indent)

    # TODO This has not been test with actual data yet,
    # no other partner setups are syncing customers
    def auditCustomersBatch(self):
        """
        This function kicks off a process that reads all the customers
        in the SQL server database and updates the hashes in sqlite.
        If the sqlite hash differs from the time stamp the customer row is updated.
        getCustomers can then check the sync token against sqlite.
        """
        t1 = time.time()
        customers = 0
        self._setConfig()

        self._checkInstall()
        params = self._getParams()

        response = utils.checkRequiredParams(
            ["audit_lower_limit", "audit_upper_limit"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        # Read products from SQL server
        with pastel.openConn(self._config["dsn"]) as cursor:
            sql = self._getCustomersPastel()
            sql = utils.bindSqlParams(sql, {
                "audit_lower_limit": int(params["audit_lower_limit"]),
                "audit_upper_limit": int(params["audit_upper_limit"]),
            })

            cursor.execute(sql)
            rows = utils.getRowsODBC(cursor, 1)

            # Get local connection
            if len(rows) > 0:
                with utils.openSqliteConn(self._getDbPath(), commit=True) as sqliteCursor:
                    while len(rows) > 0 and customers <= int(params["audit_upper_limit"]):
                        customers += 1
                        # Update hash for this customer
                        self._updateCustomerHash(rows[0], sqliteCursor)
                        rows = utils.getRowsODBC(cursor, 1)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {
                "customers": customers
            }
        }
        return json.dumps(response, indent=self._indent)

    def getProduct(self):
        """
        This functions uses the grtProduct.sql to return the product.
        It also includes the raw data returned by the sql query as well as the transformed S2S product
        """
        t1 = time.time()
        self._setConfig()

        params = self._getParams()

        response = utils.checkRequiredParams(
            ["source_variant_code", "multi_store", "source_id"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        rows = []
        with pastel.openConn(self._config["dsn"]) as cursor:
            sql = self._getProductPastel()
            sql = utils.bindSqlParams(sql, {
                "multi_store": params["multi_store"],
                "source_variant_code": params["source_variant_code"]
            })

            response = pastel.executeSQL(cursor, sql)
            if response is not None:
                return json.dumps(response, indent=self._indent)

            result = utils.getRowsODBC(cursor, 1)
            if len(result) > 0:
                transformed_product = self._transformProduct(
                    params,
                    result[0],
                    "0"
                )
                transformed_product["raw_query"] = result
                rows.append(transformed_product)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {"source_products": rows}
        }
        return json.dumps(response, indent=self._indent)

    def getProductsBatch(self):
        t1 = time.time()
        self._setConfig()

        params = self._getParams()

        response = utils.checkRequiredParams(
            ["sync_token", "limit", "source_id"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        # Test source in the s2s console does not pass in current_iteration
        audit_products = False
        if "current_iteration" not in params:
            params["current_iteration"] = "1"

        # Only audit products on the first iteration
        if params["current_iteration"] == "1":
            response = self.auditProductsBatch()
            response = json.loads(response)
            if not response["status"]:
                return json.dumps(response, indent=self._indent)
            audit_products = True

        with pastel.openConn(self._config["dsn"]) as cursor:
            with utils.openSqliteConn(self._getDbPath(), commit=True) as sqliteCursor:
                sql = self._getProductsLocal()
                sqliteCursor.execute(sql, (
                    int(params["sync_token"]), int(params["limit"])
                ))

                rows = []
                auditRow = sqliteCursor.fetchone()
                while auditRow is not None:
                    sql = self._getProductPastel()
                    sql = utils.bindSqlParams(sql, {
                        "source_variant_code":
                            auditRow[self._productIndex["source_variant_code"]],
                        # To push products from different stores,
                        # multiple apifact packages must be used
                        "multi_store": self._config["multi_store"],
                    })
                    cursor.execute(sql)
                    odbcRows = utils.getRowsODBC(cursor, 1)
                    if len(odbcRows) > 0:
                        rows.append(
                            self._transformProduct(
                                params,
                                odbcRows[0],
                                auditRow[self._productIndex["sync_token"]]
                            )
                        )
                    auditRow = sqliteCursor.fetchone()

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {"source_products": rows},
            "audit_products": audit_products
        }
        return json.dumps(response, indent=self._indent)

    def getCustomersBatch(self):
        t1 = time.time()
        self._setConfig()

        params = self._getParams()

        response = utils.checkRequiredParams(
            ["sync_token", "limit"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        audit_customers = False
        if "current_iteration" not in params:
            params["current_iteration"] = "1"

        # Only audit customers on the first iteration
        if params["current_iteration"] == "1":
            response = self.auditCustomersBatch()
            response = json.loads(response)
            if not response["status"]:
                return json.dumps(response, indent=self._indent)
            audit_customers = True

        with pastel.openConn(self._config["dsn"]) as cursor:
            with utils.openSqliteConn(self._getDbPath(), commit=True) as sqliteCursor:
                sql = self._getCustomersLocal()
                sqliteCursor.execute(sql, (
                    int(params["sync_token"]), int(params["limit"])
                ))

                rows = []
                auditRow = sqliteCursor.fetchone()
                while auditRow is not None:
                    sql = self._getCustomerPastel()
                    sql = utils.bindSqlParams(sql, {
                        "source_customer_code":
                            auditRow[self._customerIndex["source_customer_code"]]
                    })
                    cursor.execute(sql)
                    odbcRow = utils.getRowsODBC(cursor, 1)
                    if len(odbcRow) > 0:
                        rows.append(
                            self._transformCustomer(
                                params,
                                odbcRow[0],
                                auditRow[self._customerIndex["sync_token"]]
                            )
                        )
                    auditRow = sqliteCursor.fetchone()

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {"source_customers": rows},
            "audit_customers": audit_customers
        }
        return json.dumps(response, indent=self._indent)

    def getOrder(self):
        t1 = time.time()
        self._setConfig()

        params = self._getParams()
        response = utils.checkRequiredParams(
            ["source_order_code", "source_id"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        with pastel.openConn(self._config["dsn"]) as cursor:
            sql = self._getOrderPastel()
            sql = utils.bindSqlParams(sql, {
                "source_order_code": params["source_order_code"]
            })
            cursor.execute(sql)
            rows = cursor.fetchall()
            if (len(rows) == 0):
                response = {
                    "status": False,
                    "code": None,
                    "description": "Source order %s not found" % params["source_order_code"],
                    "line": utils.lineNo()
                }
                return json.dumps(response, indent=self._indent)

            else:
                source_order = self._transformOrder(params, rows)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": source_order
        }
        return json.dumps(response, indent=self._indent)

    def checkSdk(self):
        """
        Tests if the SDK is working by using it to fetch a customer.
        """
        t1 = time.time()
        self._setConfig()

        params = self._getParams()
        response = utils.checkRequiredParams(
            ["source_customer_code"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        try:
            command = "GetNearest"
            sdkParams = [command]
            sdkParams.append("ACCMASD")
            sdkParams.append("0")
            sdkParams.append(params["source_customer_code"])
            sdkResponse = pastel.execute(self._config, sdkParams)

        except Exception as exc:
            response = {
                "status": False,
                "code": None,
                "description": str(exc),
                "line": utils.lineNo()
            }
            return json.dumps(response, indent=self._indent)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {
                "sdkResponse": sdkResponse
            }
        }
        return json.dumps(response, indent=self._indent)

    def _getCustomerAddress(self, source_customer_code):
        response = pastel.getCustomerAddress(self._config,
                                             source_customer_code)
        return response

    def _getCustomerDeliveryAddress(self, source_customer_code):
        response = pastel.getCustomerAddress(self._config,
                                             source_customer_code,
                                             file="ACCDELIV")
        return response

    def createOrder(self):
        t1 = time.time()
        self._setConfig()

        params = self._getParams()

        response = utils.checkRequiredParams(
            ["multi_store", "shipping_code"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        payload = self._payload()
        if isinstance(payload, bytes):
            payload = json.loads(payload.decode("utf-8"))
        else:
            payload = json.loads(payload)
        order = payload["system_order"]
        source = payload["sources"][0]
        utils.noneToString(source)

        # Check order line items are in stock if negative stock disabled
        negative_stock_disabled = False
        if "negative_stock_disabled" in params:
            negative_stock_disabled = params["negative_stock_disabled"] == "true"
        if negative_stock_disabled:
            description = ""
            descriptionTemplate = "%s x %s ordered %s available, "
            with pastel.openConn(self._config["dsn"]) as cursor:
                for line_item in order["line_items"]:
                    sql = self._getProductPastel()
                    source_variant_code = line_item["source_variant_code"]
                    source_variant_code = source_variant_code.replace("'", "")
                    sql = utils.bindSqlParams(sql, {
                        "multi_store": params["multi_store"],
                        "source_variant_code": source_variant_code
                    })
                    cursor.execute(sql)
                    result = utils.getRowsODBC(cursor, 1)
                    if len(result) > 0:
                        qty = self._getQty(result[0])
                        if int(line_item["qty"]) > 0 and int(line_item["qty"]) > qty:
                            description += descriptionTemplate % (line_item["qty"], line_item["sku"], qty)
                    else:
                        description += "sku %s not found, " % (line_item["sku"])

            if len(description) > 0:
                response = {
                    "status": False,
                    "code": "409",
                    "description": description,
                    "line": utils.lineNo()
                }
                return json.dumps(response, indent=self._indent)

        # Lookup source_customer_code
        customer = None
        source_customer_code = ""
        description = ""

        if "source_customer_code" in source:
            customer = pastel.getCustomer(self._config["dsn"],
                                          source["source_customer_code"])
            source_customer_code = source["source_customer_code"]
            description = "Customer does not exist: %s" % source_customer_code

        if customer is None and "default_customer_code" in params:
            # Try the default customer
            default_customer_code = params["default_customer_code"]
            customer = pastel.getCustomer(self._config["dsn"],
                                          default_customer_code)
            source_customer_code = default_customer_code
            source["source_customer_code"] = source_customer_code
            description = "Customer does not exist: %s" % source_customer_code

        if customer is None:
            create_customer = False
            if "create_customer_enabled" in params:
                create_customer = (params["create_customer_enabled"] == "true")

            if create_customer:
                # Create order customer if not exist
                customer = pastel.getCustomer(self._config["dsn"], source_customer_code)
                if customer is None:
                    pipeString = pastel.getPipeString(
                        self._getImportCustomer(customer, source_customer_code)
                    )
                    pastel.createCustomer(self._config, source_customer_code, pipeString)

            else:
                response = {
                    "status": False,
                    "code": None,
                    "description": description,
                    "line": utils.lineNo()
                }
                return json.dumps(response, indent=self._indent)

        # Set the order addresses
        use_customer_address = False
        if "use_customer_address" in params:
            use_customer_address = (params["use_customer_address"] == "true")
        if use_customer_address:
            response = self._getCustomerAddress(source_customer_code)
            if not response["status"]:
                response["data"] = {"source_customer_code": source_customer_code}
                return json.dumps(response, indent=self._indent)
            address = response["address"]

            response = self._getCustomerDeliveryAddress(source_customer_code)
            if not response["status"]:
                response["data"] = {"source_customer_code": source_customer_code}
                return json.dumps(response, indent=self._indent)
            deliveryAddress = response["address"]

        else:
            billing_address = order["billing_address"]
            utils.noneToString(billing_address)
            address = {
                "address_1": "%s %s" % (billing_address["first_name"], billing_address["last_name"]),
                "address_2": billing_address["address1"],
                "address_3": billing_address["address1"],
                "address_4": billing_address["city"],
                "address_5": billing_address["zip"]
            }

            shipping_address = order["shipping_address"]
            utils.noneToString(shipping_address)
            deliveryAddress = {
                "address_1": "%s %s" % (shipping_address["first_name"], shipping_address["last_name"]),
                "address_2": shipping_address["address1"],
                "address_3": shipping_address["address2"],
                "address_4": shipping_address["city"],
                "address_5": shipping_address["zip"],
                "phone": shipping_address["phone"]
            }

        command = "ImportDocument"
        sdkParams = [command]

        order_document_type = "Sales Order"
        if "order_document_type" in params:
            order_document_type = params["order_document_type"]
        documentType = pastel.sdkDocumentTypesBatch[order_document_type]
        sdkParams.append(documentType)

        userId = "-1"
        if "order_user_id" in params:
            userId = params["order_user_id"]
        sdkParams.append(userId)

        additionalCostInvoice = "true"
        sdkParams.append(additionalCostInvoice)

        order, address, deliveryAddress = self._beforeCreateOrder(order, source_customer_code, address, deliveryAddress, params)

        documentHeader = pastel.getPipeString(
            self._getDocumentHeaderOrder(
                order,
                source_customer_code,
                address,
                deliveryAddress
            )
        )
        sdkParams.append(documentHeader)
        documentHeaderIndex = 4

        # Line items
        exclusiveTotalCents = 0
        for line_item in order["line_items"]:
            pipeString = pastel.getPipeString(
                self._getDocumentLineOrder(line_item, params["multi_store"])
            )
            sdkParams.append(pipeString)
            exclusiveTotalCents += round(int(line_item["qty"]) * (line_item["price"] * 100))

        # Shipping lines (transformed to line item in pastel)
        if "shipping_lines" in order:
            for shipping_line in order["shipping_lines"]:
                shipping_line['sku'] = params['shipping_code']
                shippingQty = 1
                shipping_line['qty'] = shippingQty
                pipeString = pastel.getPipeString(
                    self._getDocumentLineOrder(shipping_line, params["multi_store"])
                )
                sdkParams.append(pipeString)
                exclusiveTotalCents += round(shippingQty * shipping_line["price"] * 100)

        if "total_discount" in order:
            # Prevent division by zero
            if exclusiveTotalCents > 0:
                # not sure if this is specifically for version 12 but tested on version 14 you
                # do not need to create a four digit string with leading zeros as per the comment below
                # Discount in the document header is a percentage represented as a string of four digits
                # totalDiscountCents = round(float(order["total_discount"]) * 100)
                # discountPercent = str(totalDiscountCents / exclusiveTotalCents * 100).replace(".", "")
                # discountPercent = "00{}".format(discountPercent[:2])
                # sdkParams[documentHeaderIndex] = sdkParams[documentHeaderIndex].replace(
                #     "---DISCOUNT---", discountPercent
                # )

                # new way of handeling discount
                totalDiscountCents = round(float(order["total_discount"]) * 100)
                discountPercent = round(((totalDiscountCents / exclusiveTotalCents) * 100), 4)
                discountPercent = str(discountPercent)
                sdkParams[documentHeaderIndex] = sdkParams[documentHeaderIndex].replace(
                    "---DISCOUNT---", discountPercent
                )
        # Ensure we've removed the discount placeholder if there was no total_discount
        sdkParams[documentHeaderIndex] = sdkParams[documentHeaderIndex].replace(
            "---DISCOUNT---", ""
        )

        if "pipe_string" in params:
            response = {
                "status": True,
                "code": None,
                "sdkParams": sdkParams
            }
            return json.dumps(response, indent=self._indent)

        sdkResponse = ""
        try:
            sdkResponse = pastel.execute(self._config, sdkParams)

        except Exception as exc:
            response = {
                "status": False,
                "code": None,
                "description": str(exc),
                "sdkResponse": sdkResponse,
                "line": utils.lineNo()
            }
            return json.dumps(response, indent=self._indent)

        sdkCodes = pastel.getSdkCode(sdkResponse)
        t2 = time.time()

        if pastel.sdkSuccess(sdkCodes):
            response = {
                "status": True,
                "timer": str(t2 - t1),
                "data": {
                    "source_order_code": sdkCodes[1],
                    "source_customer_code": source_customer_code,
                }
            }
        else:
            if sdkCodes[0] == "1000":
                description = sdkCodes[1]
            else:
                description = pastel.sdkReturnCodes[sdkCodes[0]]
            response = {
                "status": False,
                "code": sdkCodes[0],
                "sdkResponse": sdkResponse,
                "description": description,
                "timer": str(t2 - t1),
                "sdkParams": sdkParams,
                "line": utils.lineNo()
            }

        return json.dumps(response, indent=self._indent)

    def _beforeCreateOrder(self, order, source_customer_code, address, deliveryAddress, params):
        """
        Hook method that gets called before billing and shipping address details are added to order header.
        For default behavior just return the passed "address, deliveryAddress"
        """
        return order, address, deliveryAddress

    def getHostname(self):
        """
        This function is useful when configuring the ODBC connection string
        """
        self._setConfig()
        args = ["hostname"]

        cmd = subprocess.Popen(args, stdout=subprocess.PIPE)
        response = cmd.communicate()[0]

        os_encoding = locale.getpreferredencoding()
        response = response.decode(os_encoding)

        hostname = response.replace('\n', ' ').replace('\r', '')
        response = {
            "status": True,
            "data": {
                "hostname": hostname.strip()
            }
        }
        return json.dumps(response, indent=self._indent)

    def getIpConfig(self):
        """
        This function is useful when configuring the ODBC connection string
        """
        self._setConfig()
        args = ["ipconfig"]

        cmd = subprocess.Popen(args, stdout=subprocess.PIPE)
        response = cmd.communicate()[0]

        os_encoding = locale.getpreferredencoding()
        response = response.decode(os_encoding)

        ipconfig = response.split("\r\n")
        response = {
            "status": True,
            "data": {
                "ipconfig": ipconfig
            }
        }
        return json.dumps(response, indent=self._indent)

    def _getItemCodeByGUID(self):
        self._setConfig()

        params = self._getParams()

        response = utils.checkRequiredParams(["guid"], params)
        if response is not None:
            return json.dumps(response, indent=self._indent)

        with pastel.openConn(self._config["dsn"]) as cursor:
            sql = '''select ItemCode from Inventory where GUID = '%(guid)s';'''
            sql = utils.bindSqlParams(sql, {
                "guid": params['guid']
            })
            cursor.execute(sql)
            result = utils.getRowsODBC(cursor, 1)
            if len(result) > 0:
                response = {
                    "status": True,
                    "itemcode": result[0]['itemcode']
                }
                return json.dumps(response, indent=self._indent)

    def getUserDefinedText01Grouping(self):
        self._setConfig()
        sql = '''select top 100 i.UserDefText01 AS "value", COUNT(i.UserDefText01) AS "count" from Inventory i group by i.UserDefText01;'''
        params = {
            "sql": sql,
            "limit": 100
        }
        with pastel.openConn(self._config["dsn"]) as cursor:
            response = utils.runODBC(params, cursor)
            return json.dumps(response)

    def getUserDefinedText02Grouping(self):
        self._setConfig()
        sql = '''select top 100 i.UserDefText02 AS "value", COUNT(i.UserDefText02) AS "count" from Inventory i group by i.UserDefText02;'''
        params = {
            "sql": sql,
            "limit": 100
        }
        with pastel.openConn(self._config["dsn"]) as cursor:
            response = utils.runODBC(params, cursor)
            return json.dumps(response)

    def getUserDefinedText03Grouping(self):
        self._setConfig()
        sql = '''select top 100 i.UserDefText03 AS "value", COUNT(i.UserDefText03) AS "count" from Inventory i group by i.UserDefText03;'''
        params = {
            "sql": sql,
            "limit": 100
        }
        with pastel.openConn(self._config["dsn"]) as cursor:
            response = utils.runODBC(params, cursor)
            return json.dumps(response)

    def _setMeta(self, key, value, update=False):
        t1 = time.time()
        with utils.openSqliteConn(
            self._getDbPath(), commit=True) as sqliteCursor:
            if update:
                sql = "update meta set value = ? where `key` = ?"
            else:
                sql = "insert into meta (value, `key`) values (?, ?)"
            # Note that param order must work for both queries
            sqliteCursor.execute(sql, (value, key))

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {
                "key": key,
                "value": value
            }
        }
        return response


    def setMeta(self):
        self._setConfig()
        self._checkInstall()

        params = self._getParams()
        response = utils.checkRequiredParams(
            ["key", "value"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        response = self._getMeta(params["key"])
        if response["status"]:
            response = self._setMeta(
                params["key"], params["value"], update=True)
        else:
            response = self._setMeta(params["key"], params["value"])

        return json.dumps(response, indent=self._indent)


    def _getMeta(self, key):
        t1 = time.time()
        with utils.openSqliteConn(self._getDbPath()) as sqliteCursor:
            sql = self._getMetaLocal()
            sqliteCursor.execute(sql, (key,))
            row = sqliteCursor.fetchone()
            if row is None:
                response = {
                    "status": False,
                    "description": "Key {} not found".format(key),
                    "line": utils.lineNo()
                }
                return response

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {
                "key": row[self.metaIndex["key"]],
                "value": row[self.metaIndex["value"]]
            }
        }
        return response


    def getMeta(self):
        self._setConfig()
        self._checkInstall()

        params = self._getParams()
        response = utils.checkRequiredParams(
            ["key"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        response = self._getMeta(params["key"])
        return json.dumps(response, indent=self._indent)


    def deleteMeta(self):
        t1 = time.time()
        self._setConfig()
        self._checkInstall()

        params = self._getParams()
        response = utils.checkRequiredParams(
            ["key"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        response = self._getMeta(params["key"])
        if response["status"]:
            with utils.openSqliteConn(
                self._getDbPath(), commit=True) as sqliteCursor:
                sql = "delete from meta where `key` = ?"
                sqliteCursor.execute(sql, (params["key"],))

        else:
            response = {
                "status": False,
                "description": "Key {} not found".format(params["key"]),
                "line": utils.lineNo()
            }
            return json.dumps(response, indent=self._indent)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {
                "key": params["key"]
            }
        }
        return json.dumps(response, indent=self._indent)

    def isPush(self):
        response = {
            "status": True,
            "data": {
                "push": True
            }
        }
        return json.dumps(response, indent=self._indent)