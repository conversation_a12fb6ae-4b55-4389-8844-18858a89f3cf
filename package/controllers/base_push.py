import json
import locale
from builtins import open, float, int, str, len, round, Exception, getattr
import os
import time
import imp
import xml.sax.saxutils as saxutils
import xml.etree.ElementTree as etree

import math

from ..shared import utils
from ..shared import sap_one
import collections
import subprocess
import base64
import hashlib

packageDir = os.path.join(os.path.realpath(os.path.join(os.getcwd(),
    os.path.dirname(__file__))), os.pardir)

class Api:
    _indent = False

    _configDir = "config"
    _config = None

    def _setConfig(self):
        params = self._getParams()
        test = False
        if "test" in params:
            test = params["test"] == "true"
        if test:
            configFile = open(
                os.path.join(
                    packageDir, self._configDir, "test", "config.json"), "r")
            self._config = json.loads(configFile.read())
        else:
            configFile = open(
                os.path.join(packageDir, self._configDir, "config.json"), "r")
            self._config = json.loads(configFile.read())
        configFile.close()

    # App will override this
    @staticmethod
    def _getParams():
        return {}

    # App will override this
    @staticmethod
    def _payload():
        return {}

    def _getDbPath(self):
        return os.path.join(packageDir, self._configDir, self._config["dbName"])

    def _checkInstall(self):
        dbPath = self._getDbPath()
        table_results = []
        index_results = []

        with utils.openSqliteConn(self._getDbPath()) as cursor:
            if os.path.isfile(dbPath):
                sql = "select * from sqlite_master where type='table';"
                cursor.execute(sql)
                table_results = cursor.fetchall()

                sql = "select * from sqlite_master where type='index';"
                cursor.execute(sql)
                index_results = cursor.fetchall()

                # For backward compatibility and to prevent having to reinstall package,
                # we check if missing columns exist and add them.
                if len(table_results) > 0:
                    if self.hasColumnLocal('image', 'error') == False:
                        self.createColumnLocal('image', 'error')



        # Create tables
        if len(table_results) == 0:
            self.createTables()

        # Create indexes
        if len(index_results) == 0:
            self.createIndexes()

    def hasColumnLocal(self, tableName, columnName):
        self._setConfig()
        with utils.openSqliteConn(self._getDbPath()) as cursor:
            sql = "select count(*) as cnt from pragma_table_info('" + tableName + "') WHERE name='" + columnName + "'"
            cursor.execute(sql)
            row = cursor.fetchone()
            if row[0] >= 1:
                return True

        return False

    def createColumnLocal(self, tableName, columnName):
        self._setConfig()
        with utils.openSqliteConn(self._getDbPath(), commit=True) as cursor:
            sql = "alter table " + tableName + " add column " + columnName
            cursor.execute(sql)

    def deleteColumnLocal(self, tableName, columnName):
        self._setConfig()
        with utils.openSqliteConn(self._getDbPath(), commit=True) as cursor:
            sql = "alter table " + tableName + " drop column " + columnName
            cursor.execute(sql)

    def createTables(self):
        self._setConfig()
        with utils.openSqliteConn(self._getDbPath(), commit=True) as cursor:
            # If not installed then create a new sqlite database
            cursor.execute("""
            create table product
             (source_product_code, source_variant_code,
             hash, sync_token, modified, json)
            """)
            cursor.execute("""
            create table customer
             (source_customer_code, hash, sync_token, modified, json)
            """)
            cursor.execute("""
                create table image (source_image_code, source_product_code, source_variant_code, hash, sync_token, modified, filename, error)""")
            cursor.execute("""create table meta (key, value)""")

        response = {
            "status": True,
            "data": {
                "Create Tables": True
            }
        }
        return json.dumps(response, indent=self._indent)

    def createIndexes(self):
        self._setConfig()
        with utils.openSqliteConn(self._getDbPath(), commit=True) as cursor:
            # product table indexes
            cursor.execute(
                "CREATE INDEX idx_product_source_product_code_source_variant_code ON product(`source_product_code`,`source_variant_code`)")
            cursor.execute(
                "CREATE INDEX idx_product_sync_token ON product(`sync_token`)")

            # customer table indexes
            cursor.execute(
                "CREATE INDEX idx_customer_source_customer_code ON customer(`source_customer_code`)")
            cursor.execute(
                "CREATE INDEX idx_customer_sync_token ON customer(`sync_token`)")

            # image table indexes
            cursor.execute(
                "CREATE INDEX idx_image_source_variant_code ON image(`source_variant_code`)")
            cursor.execute(
                "CREATE INDEX idx_image_sync_token ON image(`sync_token`)")

        response = {
            "status": True,
            "data": {
                "Indexed": True
            }
        }
        return json.dumps(response, indent=self._indent)

    def reindex(self):
        self._setConfig()
        self._dropIndexes()
        self.createIndexes()
        response = {
            "status": True,
            "data": {
                "Reindex": True
            }
        }
        return json.dumps(response, indent=self._indent)

    def _dropIndexes(self):
        with utils.openSqliteConn(self._getDbPath(), commit=True) as cursor:
            # product table indexes
            cursor.execute(
                "DROP INDEX IF EXISTS idx_product_source_product_code_source_variant_code")
            cursor.execute(
                "DROP INDEX IF EXISTS idx_product_sync_token")

            # customer table indexes
            cursor.execute(
                "DROP INDEX IF EXISTS idx_customer_source_customer_code")
            cursor.execute(
                "DROP INDEX IF EXISTS idx_customer_sync_token")

            # image table indexes
            cursor.execute(
                "DROP INDEX IF EXISTS idx_image_source_variant_code")
            cursor.execute(
                "DROP INDEX IF EXISTS idx_image_sync_token")



    def _getProductsOdbc(self):
        path = os.path.join(packageDir, self._configDir, "getProducts.sql")
        f = open(path, "r")
        sql = f.read()
        f.close()
        return sql

    def _getProductBySKUOdbc(self):
        path = os.path.join(packageDir, self._configDir, "getProductBySKU.sql")
        f = open(path, "r")
        sql = f.read()
        f.close()
        return sql




    def _getImagesOdbc(self):
        path = os.path.join(packageDir, self._configDir, "getImages.sql")
        if os.path.isfile(path):
            f = open(path, "r")
            sql = f.read()
            f.close()
            return sql
        else:
            return None


    def _getImageOdbc(self):
        path = os.path.join(packageDir, self._configDir, "getImage.sql")
        if os.path.isfile(path):
            f = open(path, "r")
            sql = f.read()
            f.close()
            return sql
        else:
            return None


    def _getCustomersOdbc(self):
        path = os.path.join(packageDir, self._configDir, "getCustomers.sql")
        f = open(path, "r")
        sql = f.read()
        f.close()
        return sql


    def _countProductsOdbc(self):
        path = os.path.join(packageDir, self._configDir, "countProducts.sql")
        f = open(path, "r")
        sql = f.read()
        f.close()
        return sql


    def _countCustomersOdbc(self):
        path = os.path.join(packageDir, self._configDir, "countCustomers.sql")
        f = open(path, "r")
        sql = f.read()
        f.close()
        return sql


    def _countImagesOdbc(self):
        path = os.path.join(packageDir, self._configDir, "countImages.sql")
        f = open(path, "r")
        sql = f.read()
        f.close()
        return sql


    def _getProductOdbc(self):
        path = os.path.join(packageDir, self._configDir, "getProduct.sql")
        f = open(path, "r")
        sql = f.read()
        f.close()
        return sql

    def _getCustomerOdbc(self):
        path = os.path.join(packageDir, self._configDir, "getCustomer.sql")
        f = open(path, "r")
        sql = f.read()
        f.close()
        return sql

    def _customOdbc(self, fileName=None):
        if not fileName:
            fileName = "custom.sql"
        path = os.path.join(packageDir, self._configDir, fileName)
        f = open(path, "r")
        sql = f.read()
        f.close()
        return sql

    def _getOrderOdbc(self):
        path = os.path.join(packageDir, self._configDir, "getOrder.sql")
        f = open(path, "r")
        sql = f.read()
        f.close()
        return sql

    _imageIndex = {
        "source_image_code": 0,
        "source_product_code": 1,
        "source_variant_code": 2,
        "hash": 3,
        "sync_token": 4,
        "modified": 5,
        "filename": 6,
        "error": 7
    }

    _productIndex = {
        "source_product_code": 0,
        "source_variant_code": 1,
        "hash": 2,
        "sync_token": 3,
        "modified": 4,
        "json": 5
    }

    @staticmethod
    def _getProductsLocal():
        return '''
          select * from product where sync_token > ?
          order by sync_token asc limit ?
        '''

    @staticmethod
    def _getImagesLocal():
        return '''
          select * from image where (error is null or error = '') and sync_token > ?
          order by sync_token asc limit ?
        '''

    @staticmethod
    def _getProductLocal():
        return '''
          select * from product
          where source_variant_code = ?
        '''

    _customerIndex = {
        "source_customer_code": 0,
        "hash": 1,
        "sync_token": 2,
        "modified": 3,
        "json": 4
    }

    @staticmethod
    def _getCustomersLocal():
        return '''
          select * from customer where sync_token > ?
          order by sync_token asc limit ?
        '''

    @staticmethod
    def _getCustomerLocal():
        return '''
          select * from customer
          where source_customer_code = ?
        '''

    metaIndex = {
        "key": 0,
        "value": 1,
    }

    @staticmethod
    def _getMetaLocal():
        return '''
        select `key`, value from meta where `key` = ?
        '''

    @staticmethod
    def _getNewJson(newMetas, oldJson):
        """
        We have to keep track of deleted meta keys,
        s2s will delete keys passed in the meta_delete array
        """
        newJson = collections.OrderedDict()
        newJson["meta"] = []
        newJson["meta_delete"] = []

        if oldJson is None:
            # Don't store values for current meta keys
            for meta in newMetas:
                newJson["meta"].append({"key": meta["key"]})
            # First time this audit row is processed
            return newJson

        # oldMetas is a dict, it has this structure:
        # {"key1": True, "key2": True, ...}
        oldMetas = {meta["key"]: True for (meta) in oldJson["meta"]}

        for newMeta in newMetas:
            # newMetas is a list, it has this structure:
            # [{"key": "key1", "value": "a"}, ...]
            if newMeta["key"] in oldMetas:
                # This key was not deleted
                del oldMetas[newMeta["key"]]

        # Keys left over in oldMetas were deleted
        for key in oldMetas:
            newJson["meta_delete"].append({"key": key})

        # Don't store values for current meta keys
        for meta in newMetas:
            newJson["meta"].append({"key": meta["key"]})

        return newJson

    @staticmethod
    def _getLastModifiedProduct(sqliteCursor):
        sql = '''
          select * from product
          order by sync_token desc limit 1
        '''
        sqliteCursor.execute(sql)
        lastModified = sqliteCursor.fetchone()
        return lastModified

    def _getProductSyncToken(self, lastModified):
        if lastModified is None:
            sync_token = 1
        else:
            sync_token = int(lastModified[self._productIndex["sync_token"]]) + 1
        return sync_token

    def _updateHash(self, row, sqliteCursor):
        params = {"source_id": None}
        auditRow = {
            self._productIndex["sync_token"]: None,
            self._productIndex["json"]: "{}"
        }
        rowHash = utils.getHash(json.dumps(self._transformProduct(
            params, row, auditRow)))

        # Compare hash to existing row
        sql = "select * from product where source_product_code = ?" + \
              " and source_variant_code = ?"
        sqliteCursor.execute(
            sql, ((
                row["source_product_code"], row["source_variant_code"]
            ))
        )
        oldProduct = sqliteCursor.fetchone()  # Assuming there is only one match

        if oldProduct is None:
            lastModified = self._getLastModifiedProduct(sqliteCursor)
            productJson = self._getNewJson(
                self._parseMetaFields(row), None)
            sql = "insert into product values (?, ?, ?, ?, ?, ?)"
            sqliteCursor.execute(
                sql, ((
                          row["source_product_code"],
                          row["source_variant_code"],
                          rowHash,
                          self._getProductSyncToken(lastModified),
                          utils.getTimestamp(),
                          json.dumps(productJson),
                      ))
            )

        elif oldProduct[self._productIndex["hash"]] == rowHash:
            pass

        else:
            # Update if hash has changed
            lastModified = self._getLastModifiedProduct(sqliteCursor)
            productJson = self._getNewJson(
                self._parseMetaFields(row),
                json.loads(lastModified[self._productIndex["json"]]))
            sql = "update product set" \
                  " hash = ?," + \
                  " modified = ?," \
                  " sync_token = ?," \
                  " json = ?" \
                  " where" + \
                  " source_product_code = ?" + \
                  " and source_variant_code = ?"
            sqliteCursor.execute(
                sql,
                ((
                     rowHash,
                     utils.getTimestamp(),
                     self._getProductSyncToken(lastModified),
                     json.dumps(productJson),
                     row["source_product_code"],
                     row["source_variant_code"],
                 ))
            )

    @staticmethod
    def _getLastModifiedCustomer(sqliteCursor):
        sql = '''
          select * from customer
          order by sync_token desc limit 1
        '''
        sqliteCursor.execute(sql)
        lastModified = sqliteCursor.fetchone()
        return lastModified

    def _getCustomerSyncToken(self, lastModified):
        if lastModified is None:
            sync_token = 1
        else:
            sync_token = int(lastModified[self._customerIndex["sync_token"]]) + 1
        return sync_token

    def _updateCustomerHash(self, row, sqliteCursor):
        params = {"source_id": None}
        auditRow = {
            self._customerIndex["sync_token"]: None,
            self._customerIndex["json"]: "{}"
        }
        rowHash = utils.getHash(json.dumps(self._transformCustomer(
            params, row, auditRow)))

        # Compare hash to existing row
        sql = "select * from customer where source_customer_code = ?"
        sqliteCursor.execute(
            sql, ((
                row["source_customer_code"],
            ))
        )
        oldCustomer = sqliteCursor.fetchone()  # Assuming there is only one match

        if oldCustomer is None:
            lastModified = self._getLastModifiedCustomer(sqliteCursor)
            customerJson = self._getNewJson(
                self._parseMetaFields(row), None)
            sql = "insert into customer values (?, ?, ?, ?, ?)"
            sqliteCursor.execute(
                sql, ((
                          row["source_customer_code"],
                          rowHash,
                          self._getCustomerSyncToken(lastModified),
                          utils.getTimestamp(),
                          json.dumps(customerJson),
                      ))
            )

        elif oldCustomer[self._customerIndex["hash"]] != rowHash:
            lastModified = self._getLastModifiedCustomer(sqliteCursor)
            # Update if hash has changed
            customerJson = self._getNewJson(
                self._parseMetaFields(row),
                json.loads(lastModified[self._customerIndex["json"]]))
            sql = "update customer set" \
                  " hash = ?," + \
                  " modified = ?," \
                  " sync_token = ?," \
                  " json = ?" \
                  " where" + \
                  " source_customer_code = ?"
            sqliteCursor.execute(
                sql,
                ((
                     rowHash,
                     utils.getTimestamp(),
                     self._getCustomerSyncToken(lastModified),
                     json.dumps(customerJson),
                     row["source_customer_code"],
                 ))
            )

    @staticmethod
    def _parseSegments(row):
        segments = []

        # Anything that starts with segment| gets converted into a segment
        # Expecting the following fields for Segments:
        # owner|type|key|operator|value
        # The sql on query must follow segment structure
        # 'rep_id'        as "segment|source|products|meta.key|equal"
        # RTRIM(SlpCode)  as "segment|source|products|meta.value|equal"
        for key in row:
            if key.startswith("segment|"):
                parts = key.split("|")
                segment = collections.OrderedDict()
                segment["owner"] = parts[1].strip()
                segment["type"] = parts[2].strip()
                segment["key"] = parts[3].strip()
                segment["operator"] = parts[4].strip()
                segment["value"] = row[key]
                segments.append(segment)

        return segments

    @staticmethod
    def _parseMetaFields(row):
        metaFields = []

        # Anything that starts with csv gets converted into meta
        # e.g. csv_special_prices will convert to:
        # meta_special_prices_{key} = {value}
        # ignore special keywords
        for key in row:
            if key.startswith("csv_") and \
                    not key.startswith("csv_qty_availability") and \
                    not key.startswith("csv_price_tiers") and \
                    not key.startswith("csv_promotional_discounts") and \
                    not key.startswith("csv_special_prices") and \
                    not key.startswith("csv_special_prices_fixed") and \
                    not key.startswith("csv_discount_customer") and \
                    not key.startswith("csv_discount_customer_groups") and \
                    not key.startswith("csv_discount_groups") and \
                    not key.startswith("csv_contract_"):
                # Value from database might be null
                if row[key] is not None:
                    title = key.replace("csv_", "")
                    values = saxutils.unescape(row[key]).split(",")
                    for value in values:
                        valueItem = value.split("|")
                        if len(valueItem) == 2 and valueItem[0].strip() != "":
                            meta_key = valueItem[0].strip()
                            meta_value = float(valueItem[1])
                            meta = collections.OrderedDict()
                            meta["key"] = str(title + "_{}".format(meta_key))
                            meta["value"] = str(meta_value)
                            metaFields.append(meta)

        # TODO make this work the same way as gomedia_evolution, all csv's are converted to meta
        # Parse promotional discounts
        if "csv_promotional_discounts" in row and row["csv_promotional_discounts"] is not None:
            prices = saxutils.unescape(row["csv_promotional_discounts"])
            prices = prices.split(",")
            for price in prices:
                priceItem = price.split("|")
                if len(priceItem) == 2 and priceItem[0].strip() != "":
                    card_code = priceItem[0].strip()
                    discount = float(priceItem[1])
                    meta = collections.OrderedDict()
                    meta["key"] = str("promotional_discount_{}".format(card_code))
                    meta["value"] = str(discount)
                    metaFields.append(meta)

        # Parse special prices
        if "csv_special_prices" in row and row["csv_special_prices"] is not None:
            prices = saxutils.unescape(row["csv_special_prices"])
            prices = prices.split(",")
            for price in prices:
                priceItem = price.split("|")
                if len(priceItem) == 2 and priceItem[0].strip() != "":
                    card_code = priceItem[0].strip()
                    discount = float(priceItem[1])
                    meta = collections.OrderedDict()
                    meta["key"] = str("special_price_{}".format(card_code))
                    meta["value"] = str(discount)
                    metaFields.append(meta)

        # Parse special prices
        if "csv_special_prices_fixed" in row and row["csv_special_prices_fixed"] is not None:
            prices = saxutils.unescape(row["csv_special_prices_fixed"])
            prices = prices.split(",")
            for price in prices:
                priceItem = price.split("|")
                if len(priceItem) == 2 and priceItem[0].strip() != "":
                    card_code = priceItem[0].strip()
                    discount = float(priceItem[1])
                    meta = collections.OrderedDict()
                    meta["key"] = str("special_price_fixed{}".format(card_code))
                    meta["value"] = str(discount)
                    metaFields.append(meta)

        # Parse discount groups
        if "csv_discount_customer" in row and row["csv_discount_customer"] is not None:
            groups = saxutils.unescape(row["csv_discount_customer"])
            groups = groups.split(",")
            for group in groups:
                groupItem = group.split("|")
                if len(groupItem) == 2 and groupItem[0].strip() != "":
                    group_code = groupItem[0].strip()
                    try:
                        discount = float(groupItem[1])
                        meta = collections.OrderedDict()
                        meta["key"] = str(
                            "discount_customer_{}".format(group_code))
                        meta["value"] = str(discount)
                        metaFields.append(meta)
                    except ValueError:
                        pass

        # Parse discount groups
        if "csv_discount_customer_groups" in row and row["csv_discount_customer_groups"] is not None:
            groups = saxutils.unescape(row["csv_discount_customer_groups"])
            groups = groups.split(",")
            for group in groups:
                groupItem = group.split("|")
                if len(groupItem) == 2 and groupItem[0].strip() != "":
                    group_code = groupItem[0].strip()
                    try:
                        discount = float(groupItem[1])
                        meta = collections.OrderedDict()
                        meta["key"] = str(
                            "discount_customer_group_code_{}".format(group_code))
                        meta["value"] = str(discount)
                        metaFields.append(meta)
                    except ValueError:
                        pass

        # Parse discount groups
        if "csv_discount_groups" in row and row["csv_discount_groups"] is not None:
            groups = saxutils.unescape(row["csv_discount_groups"])
            groups = groups.split(",")
            for group in groups:
                groupItem = group.split("|")
                if len(groupItem) == 2 and groupItem[0].strip() != "":
                    group_code = groupItem[0].strip()
                    try:
                        discount = float(groupItem[1])
                        meta = collections.OrderedDict()
                        meta["key"] = str(
                            "discount_group_code_{}".format(group_code))
                        meta["value"] = str(discount)
                        metaFields.append(meta)
                    except ValueError:
                        pass

        # Meta
        for key in row:
            if key.startswith("meta_"):
                # Value from database might be null
                if row[key] is not None:
                    meta = collections.OrderedDict()
                    meta["key"] = str(key[5:])  # Remove "meta_" from key
                    meta["value"] = saxutils.unescape(str(row[key]))
                    metaFields.append(meta)

            # Contract pricing
            if key.startswith("csv_contract_"):
                # Value from database might be null
                if row[key] is not None:
                    # Example row value: order_0|entity_customer|key_item_group_code|value_{}|type_discount~10,order_0|entity_product|key_item_group_code|value_{}|type_discount~20
                    # Output:
                    # [
                    #   {
                    #       key: order_0|entity_customer|key_item_group_code|value_abc|type_discount
                    #       value: 10
                    #   },
                    #   {
                    #       key: order_0|entity_customer|key_item_group_code|value_xyz|type_discount
                    #       value: 20
                    #   }
                    # ]
                    contracts = saxutils.unescape(row[key]).split(",")
                    for contract in contracts:
                        contractItem = contract.split("~")
                        if len(contractItem) == 2 and contractItem[0].strip() != "":
                            meta_key = contractItem[0].strip()
                            meta_value = contractItem[1]  # discount / fixed price
                            meta = collections.OrderedDict()
                            meta["key"] = str(meta_key)
                            meta["value"] = str(meta_value)
                            metaFields.append(meta)

        return metaFields

    @staticmethod
    def _afterProductTransform(source_product):
        pass

    def _transformProduct(self, params, row, auditRow):
        """
        Given the result of getProduct we return a dictionary
        with required structure ready to be converted to JSON.
        """
        source_product = collections.OrderedDict()
        source_product["source"] = collections.OrderedDict()
        source_product["source"]["source_id"] = params["source_id"]
        source_product["source"]["product_active"] = row["product_active"]
        source_product["source"]["source_product_code"] = row["source_product_code"]
        source_product["source"]["sync_token"] = auditRow[self._productIndex["sync_token"]]

        source_product["product"] = collections.OrderedDict()
        source_product["product"]["options"] = []
        source_product["product"]["body_html"] = row["body_html"]
        source_product["product"]["collection"] = row["collection"]
        source_product["product"]["product_type"] = row["product_type"]
        source_product["product"]["tags"] = str(row["tags"]).lower()
        source_product["product"]["title"] = row["title"]
        source_product["product"]["vendor"] = row["vendor"]

        source_product["product"]["variants"] = collections.OrderedDict()
        source_product["product"]["variants"]["source_variant_code"] = row["source_variant_code"]
        source_product["product"]["variants"]["sku"] = row["source_variant_code"]
        source_product["product"]["variants"]["barcode"] = row["variants.barcode"]

        source_product["product"]["variants"]["inventory_management"] = row["variants.inventory_management"]
        weight = 0
        if "variants.weight" in row:
            weight = row["variants.weight"]
        elif "variants.grams" in row:
            weight = row["variants.grams"]
        if weight is not None:
            source_product["product"]["variants"]["grams"] = int(weight)

        # Parse price tiers
        default_price = 0
        source_product["product"]["variants"]["price_tiers"] = []
        if "csv_price_tiers" in row and row["csv_price_tiers"] is not None:
            # The stuff function used in the sql queries
            # will encode special characters as html entities,
            # for example, "&" becomes "&amp;".
            # Call html.unescape to convert back to special characters
            prices = saxutils.unescape(row["csv_price_tiers"])
            prices = prices.split(",")
            for priceStr in prices:
                priceItem = priceStr.split("|")
                if len(priceItem) == 2 and priceItem[0].strip() != "":
                    tier = priceItem[0]
                    # Throw away everything after the second decimal
                    price = float("{0:.2f}".format(float(priceItem[1])))
                    if tier == row["default_price_tier"]:
                        default_price = price
                    price_tier = collections.OrderedDict()
                    price_tier["tier"] = tier
                    price_tier["price"] = price
                    source_product["product"]["variants"]["price_tiers"].append(price_tier)
        else:
            if "variants.retail_price" in row:
                default_price = row["variants.retail_price"]
        source_product["product"]["variants"]["price"] = default_price

        # Parse qty availability
        default_qty = 0
        source_product["product"]["variants"]["qty_availability"] = []
        if "csv_qty_availability" in row and row["csv_qty_availability"] is not None:
            quantities = saxutils.unescape(row["csv_qty_availability"])
            quantities = quantities.split(",")
            for qtyStr in quantities:
                qtyItem = qtyStr.split("|")
                if len(qtyItem) == 2 and qtyItem[0].strip() != "":
                    description = qtyItem[0]
                    # Qty must be an integer
                    qty = math.trunc(float(qtyItem[1]))
                    if description == row["default_qty_availability"]:
                        default_qty = qty
                    availability = collections.OrderedDict()
                    availability["qty"] = qty
                    availability["description"] = description
                    source_product["product"]["variants"]["qty_availability"].append(availability)
        if "variants.qty" in row:
            default_qty = row["variants.qty"]
        source_product["product"]["variants"]["qty"] = default_qty

        source_product["product"]["meta"] = self._parseMetaFields(row)
        productJson = json.loads(auditRow[self._productIndex["json"]])
        if "meta_delete" in productJson:
            source_product["product"]["meta_delete"] = productJson["meta_delete"]

        source_product = self._parseOptions(row, source_product)

        self._afterProductTransform(source_product)
        return source_product

    @staticmethod
    def _parseOptions(row, source_product):
        """
        Parses the optionX fields in the query result.
        This uses either the old format of "variants.optionX" or the newer
        version of key/value pairs in the form "variants.optionX_name" and "variants.optionX_value"
        """
        source_product_result = source_product

        # Test and add the 3 key/value pairs for options
        optionIndex = 1
        options = []
        for i in range(1, 4):
            nameKey = 'variants.option{}_name'.format(i)
            valueKey = 'variants.option{}_value'.format(i)
            # Add this option to product if both key/value exists and is not empty
            if nameKey in row and row[nameKey] is not None and str(row[nameKey]).strip() != '' and \
                    valueKey in row and row[valueKey] is not None and str(row[valueKey]).strip() != '':
                option = collections.OrderedDict()
                option["name"] = row[nameKey]
                option["position"] = '{}'.format(optionIndex)
                option["value"] = "undefined"
                options.append(option)

                optionKey = 'option{}'.format(optionIndex)
                source_product_result["product"]["variants"][optionKey] = row[valueKey]
                optionIndex += 1
            else:
                # Fall back to older "optionX" version
                key = 'variants.option{}'.format(i)
                if key in row and row[key] is not None and str(row[key]).strip() != '':
                    source_product['product']['variants']['option{}'.format(i)] = row[key]
        source_product_result["product"]["options"] = options
        return source_product_result

    @staticmethod
    def _getCustomerAddresses(row, company, first_name, last_name):
        customer_addresses = []

        # Parse address xml
        if "xml_addresses" in row and row["xml_addresses"] is not None:
            root = etree.fromstring(row["xml_addresses"])
            for child in root:
                address = collections.OrderedDict()
                address["type"] = child.find("type").text
                address["address_code"] = child.find("address_code").text
                address["address1"] = child.find("address1").text
                address["address2"] = child.find("address2").text
                address["city"] = child.find("city").text
                address["country"] = child.find("country").text
                address["country_code"] = child.find("country_code").text
                address["province"] = child.find("province").text
                address["province_code"] = child.find("province_code").text
                address["zip"] = child.find("zip").text
                address["company"] = child.find("company").text
                address["first_name"] = child.find("first_name").text
                address["last_name"] = child.find("last_name").text
                address["phone"] = child.find("phone").text
                customer_addresses.append(address)
        else:
            address = collections.OrderedDict()
            address["address1"] = row["address.address1"]
            address["address2"] = row["address.address2"]
            address["city"] = row["address.city"]
            address["country"] = row["address.country"]
            address["country_code"] = row["address.country_code"]
            address["province"] = row["address.province"]
            address["province_code"] = row["address.province_code"]
            address["zip"] = row["address.zip"]
            address["company"] = company
            address["first_name"] = first_name
            address["last_name"] = last_name
            address["phone"] = row["address.phone"]
            customer_addresses.append(address)

        return customer_addresses

    @staticmethod
    def _parseCustomerName(row):
        company = row["card_name"]
        last_name = ""
        if row["address.contact_person"] is None:
            first_name = row["card_name"]
        else:
            first_name = row["address.contact_person"]
        return company, first_name, last_name

    @staticmethod
    def _afterCustomerTransform(customer):
        pass

    def _transformCustomer(self, params, row, auditRow):
        """
        Given the result of getCustomer we return a dictionary
        with required structure ready to be converted to JSON.
        """

        source_customer = collections.OrderedDict()
        source_customer["source"] = collections.OrderedDict()
        source_customer["source"]["source_id"] = params["source_id"]
        source_customer["source"]["customer_active"] = row["customer_active"]
        source_customer["source"]["source_customer_code"] = \
            row["source_customer_code"]
        source_customer["source"]["sync_token"] = auditRow[self._customerIndex["sync_token"]]

        customer = collections.OrderedDict()
        company, first_name, last_name = self._parseCustomerName(row)
        customer["first_name"] = first_name
        customer["last_name"] = last_name
        customer["email"] = row["email"]
        if row["accepts_marketing"] == 1:
            customer["accepts_marketing"] = True
        else:
            customer["accepts_marketing"] = False
        customer["addresses"] = self._getCustomerAddresses(row, company, first_name, last_name)
        customer["meta"] = self._parseMetaFields(row)
        customer["segments"] = self._parseSegments(row)
        customerJson = json.loads(auditRow[self._customerIndex["json"]])
        if "meta_delete" in customerJson:
            customer["meta_delete"] = customerJson["meta_delete"]

        customer["price_tier"] = str(row["price_tier"])
        customer["qty_availability"] = str(row["qty_availability"])

        source_customer["customer"] = customer

        self._afterCustomerTransform(source_customer)
        return source_customer

    @staticmethod
    def _getattrOrEmptyString(row, key):
        attr = getattr(row, key)
        if attr is None:
            return ""
        return attr

    def _transformOrder(self, params, rows):
        i = 0
        source_order = collections.OrderedDict()

        for row in rows:
            # Read order details from the first line item
            if i == 0:
                source_order["sources"] = [collections.OrderedDict()]
                source_order["sources"][0]["source_id"] = params["source_id"]
                source_order["sources"][0]["source_order_code"] = row.source_order_code.strip()
                source_order["sources"][0]["source_customer_code"] = row.source_client_code.strip()

                source_order["source_order"] = collections.OrderedDict()
                source_order["source_order"]["id"] = row.source_order_code.strip()
                source_order["source_order"]["notes"] = row.notes1.strip() + "\\n" + \
                    row.notes2.strip() + "\\n" + \
                    row.notes3.strip()
                source_order["source_order"]["customer"] = collections.OrderedDict()
                source_order["source_order"]["customer"]["first_name"] = self._getattrOrEmptyString(row, "customer.first_name").strip()
                source_order["source_order"]["customer"]["last_name"] = self._getattrOrEmptyString(row, "customer.last_name").strip()
                source_order["source_order"]["customer"]["email"] = self._getattrOrEmptyString(row, "customer.email").strip()

                billing_address = collections.OrderedDict()
                billing_address["address1"] = self._getattrOrEmptyString(row, "billing_address.address1").strip()
                billing_address["address2"] = self._getattrOrEmptyString(row, "billing_address.address2").strip()
                billing_address["city"] = self._getattrOrEmptyString(row, "billing_address.city").strip()
                billing_address["company"] = self._getattrOrEmptyString(row, "billing_address.company").strip()
                billing_address["country"] = self._getattrOrEmptyString(row, "billing_address.country").strip()
                billing_address["first_name"] = self._getattrOrEmptyString(row, "billing_address.first_name").strip()
                billing_address["last_name"] = self._getattrOrEmptyString(row, "billing_address.last_name").strip()
                billing_address["phone"] = self._getattrOrEmptyString(row, "billing_address.phone").strip()
                billing_address["province"] = self._getattrOrEmptyString(row, "billing_address.province").strip()
                billing_address["zip"] = self._getattrOrEmptyString(row, "billing_address.zip").strip()
                billing_address["country_code"] = self._getattrOrEmptyString(row, "billing_address.country_code").strip()
                source_order["source_order"]["customer"]["addresses"] = [billing_address]

                source_order["source_order"]["billing_address"] = billing_address

                shipping_address = collections.OrderedDict()
                shipping_address["address1"] = self._getattrOrEmptyString(row, "shipping_address.address1").strip()
                shipping_address["address2"] = self._getattrOrEmptyString(row, "shipping_address.address2").strip()
                shipping_address["city"] = self._getattrOrEmptyString(row, "shipping_address.city").strip()
                shipping_address["company"] = self._getattrOrEmptyString(row, "shipping_address.company").strip()
                shipping_address["country"] = self._getattrOrEmptyString(row, "shipping_address.country").strip()
                shipping_address["first_name"] = self._getattrOrEmptyString(row, "shipping_address.first_name").strip()
                shipping_address["last_name"] = self._getattrOrEmptyString(row, "shipping_address.last_name").strip()
                shipping_address["phone"] = self._getattrOrEmptyString(row, "shipping_address.phone").strip()
                shipping_address["province"] = self._getattrOrEmptyString(row, "shipping_address.province").strip()
                shipping_address["zip"] = self._getattrOrEmptyString(row, "shipping_address.zip").strip()
                shipping_address["country_code"] = self._getattrOrEmptyString(row, "shipping_address.country_code").strip()
                source_order["source_order"]["shipping_address"] = shipping_address

                source_order["source_order"]["line_items"] = []
                source_order["source_order"]["shipping_lines"] = []

            # Each additional row contains a line item
            line_item = collections.OrderedDict()
            line_item["source_id"] = params["source_id"]
            line_item["sku"] = self._getattrOrEmptyString(row, "line_item.sku").strip()
            line_item["title"] = self._getattrOrEmptyString(row, "line_item.title").strip()
            line_item["price"] = getattr(row, "line_item.price")
            line_item["qty"] = getattr(row, "line_item.qty")
            line_item["code"] = "item"
            tax_line = collections.OrderedDict()
            tax_line["price"] = getattr(row, "line_item.price")
            tax_line["rate"] = 14
            tax_line["title"] = "VAT"
            tax_line["code"] = "taxed"
            line_item["tax_lines"] = [tax_line]

            if line_item["sku"] == "SHIP001":
                source_order["source_order"]["shipping_lines"].append(line_item)
            else:
                source_order["source_order"]["line_items"].append(line_item)

            i += 1

        return source_order

    def _getTaxCode(self, params, code, shipping=False):
        newCode = False
        code = code.strip() # Remove spaces

        # Valid param keys are:
        # default_tax_code, default_tax_code_exempt (required)
        # default_tax_code_shipping, default_tax_code_exempt_shipping (optional)

        def lookupCode(append=""):
            key = "default_tax_code" + append

            # Shipping may have a separate code
            if shipping:
                shippingKey = key + "_shipping"
                if shippingKey in params:
                    return params[shippingKey]

            # Otherwise just use the "taxed" or "exempt" code
            if key in params:
                return params[key]

            return False

        if code == "taxed":
            newCode = lookupCode()

        elif code == "exempt":
            newCode = lookupCode(append="_exempt")

        return newCode

    def reload(self):
        imp.reload(sap_one)
        imp.reload(utils)
        response = {
            "status": True,
            "data": {
                "reload": True
            }
        }
        return json.dumps(response, indent=self._indent)

    # Do not allow writing to sqlite database using this function,
    # i.e. do not pass commit=True to utils.openSqliteConn.
    def runLocal(self):
        self._setConfig()
        params = self._getParams()
        with utils.openSqliteConn(self._getDbPath()) as cursor:
            response = utils.runSQLite(params, cursor)
            return json.dumps(response, indent=self._indent)

    def runCustom(self):
        self._setConfig()
        params = self._getParams()
        fileName = None
        if "fileName" in params:
            fileName = params["fileName"]
        if "limit" in params:
            limit = int(params["limit"])
        else:
            limit = 500

        # use sql in params otherwise use filename
        if "sql" in params:
            sql = params["sql"]
        else:
            sql = self._customOdbc(fileName)

        if sql == "":
            return json.dumps({
                "status": False,
                "description": "Sql not given. Use query param 'fileName=xyz.sql' or 'sql=select * from...'",
                "line": utils.lineNo()
            }, indent=self._indent)

        params = {
            "sql": sql,
            "limit": limit
        }
        with sap_one.openConn(self._config["dsn"]) as cursor:
            response = utils.runODBC(params, cursor)
            response["sql"] = sql
            return json.dumps(response, indent=self._indent)

    # getAllProducts can be used to page through all products
    # regardless of the value of U_S2sActive.
    # This can be useful for fixing the stock2shop database
    # if the client deleted previously active products
    def getAllProducts(self):
        self._setConfig()
        params = self._getParams()
        response = utils.checkRequiredParams(
            ["audit_lower_limit", "audit_upper_limit"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        sql = self._customOdbc("getAllProducts.sql")
        sql = utils.bindSqlParams(sql, {
            "audit_lower_limit": int(params["audit_lower_limit"]),
            "audit_upper_limit": int(params["audit_upper_limit"]),
        })

        params = {
            "sql": sql,
            "limit": int(params["audit_upper_limit"]) -
                     int(params["audit_lower_limit"])
        }
        with sap_one.openConn(self._config["dsn"]) as cursor:
            response = utils.runODBC(params, cursor)
            return json.dumps(response, indent=self._indent)

    def getPriceLists(self):
        self._setConfig()
        params = {
            "sql": "select ListNum, ListName from opln order by ListNum;"
        }
        with sap_one.openConn(self._config["dsn"]) as cursor:
            response = utils.runODBC(params, cursor)
            return json.dumps(response, indent=self._indent)

    def getWarehouseCodes(self):
        self._setConfig()
        params = {
            "sql": "select WhsCode, WhsName, bplid from owhs order by WhsCode;"
        }
        with sap_one.openConn(self._config["dsn"]) as cursor:
            response = utils.runODBC(params, cursor)
            return json.dumps(response, indent=self._indent)

    def getUserDefinedFieldValues(self):
        self._setConfig()
        params = self._getParams()

        response = utils.checkRequiredParams(
            ["table_id", "alias_id"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        sql =  """
        select cufd.TableID, cufd.AliasID, cufd.Descr, ufd1.IndexID, ufd1.Descr, ufd1.FldValue from cufd
        join ufd1 on cufd.FieldID = ufd1.FieldID and cufd.TableID = ufd1.TableID
        where cufd.TableID = '%(table_id)s'
        and cufd.AliasID = '%(alias_id)s'
        order by cufd.TableID, cufd.AliasID, cufd.Descr, ufd1.IndexID, ufd1.Descr, ufd1.FldValue
        """
        params["sql"] = utils.bindSqlParams(sql, params)
        params["limit"] = 2000

        with sap_one.openConn(self._config["dsn"]) as cursor:
            response = utils.runODBC(params, cursor)
            return json.dumps(response, indent=self._indent)

    def getUserDefinedFieldIds(self):
        self._setConfig()
        params = {
            "sql": """
            select TableId, FieldID, AliasID, Descr from cufd
            where TableId in ('ocrd', 'oitm')
            order by TableId, FieldID, AliasID, Descr
            """,
            "limit": 2000
        }
        with sap_one.openConn(self._config["dsn"]) as cursor:
            response = utils.runODBC(params, cursor)
            return json.dumps(response, indent=self._indent)

    def getTableColumns(self):
        self._setConfig()
        params = self._getParams()

        response = utils.checkRequiredParams(
            ["table_name"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        sql = """
        select column_name, data_type, character_maximum_length
        from information_schema.columns
        where table_name = '%(table_name)s'
        order by ordinal_position
        """
        params["sql"] = utils.bindSqlParams(sql, params)
        params["limit"] = 2000

        with sap_one.openConn(self._config["dsn"]) as cursor:
            response = utils.runODBC(params, cursor)
            return json.dumps(response, indent=self._indent)

    def getNumberingSeries(self):
        self._setConfig()
        params = self._getParams()

        sql = """
        select
        *
        from nnm1
        where 1 = 1
        """

        if "object_code" in params:
            sql = "{} and ObjectCode = '%(object_code)n'".format(sql)
            sql = utils.bindSqlParams(sql, {
                "object_code": int(params["object_code"]),
            })

        if "warehouse_code" in params:
            sql = "{} and SeriesName = '%(warehouse_code)s'".format(sql)
            sql = utils.bindSqlParams(sql, {
                "warehouse_code": str(params["warehouse_code"]),
            })

        with sap_one.openConn(self._config["dsn"]) as cursor:
            response = utils.runODBC({
                "sql": sql
            }, cursor)
            return json.dumps(response, indent=self._indent)

    def getNumberingSeriesByNameTest(self):
        self._setConfig()
        params = self._getParams()

        object_code = params["object_code"]
        warehouse_code = params["warehouse_code"]

        sql = """
            select Series as series, NextNumber as nextnumber from nnm1
            where ObjectCode = '%(object_code)n' 
            and SeriesName = '%(warehouse_code)s'
            """
        sql = utils.bindSqlParams(sql, {
            "object_code": int(object_code),
            "warehouse_code": str(warehouse_code),
        })
        series = None
        nextnumber = None
        with sap_one.openConn(self._config["dsn"]) as cursor:
            cursor.execute(sql)
            rows = utils.getRowsODBC(cursor, 1)
            if len(rows) > 0:
                series = int(rows[0]["series"])
                nextnumber = int(rows[0]["nextnumber"])
        return json.dumps({
            "series": series,
            "nextnumber": nextnumber
        }, indent=self._indent)

    def _getNumberingSeriesByName(self, warehouse_code, object_code, db_server_type=False):
        try:
            # See if the client has a custom mapping
            series_name = self._getSeriesNameByWarehouseCode(warehouse_code)
        except AttributeError:
            # Assume series_name is the same as warehouse_code by default
            series_name = warehouse_code

        if sap_one.DB_SERVER_TYPES["HANADB"]["code"] == db_server_type:
            sql = """
                select nnm1."Series" as series, nnm1."NextNumber" as nextnumber from nnm1
                where nnm1."ObjectCode" = '%(object_code)s'
                and nnm1."SeriesName" = '%(series_name)s'
                """
            sql = utils.bindSqlParams(sql, {
                "object_code": str(object_code),
                "series_name": str(series_name),
            })
        else:
            sql = """
                select nnm1."Series" as series, nnm1."NextNumber" as nextnumber from nnm1
                where nnm1."ObjectCode" = '%(object_code)n'
                and nnm1."SeriesName" = '%(series_name)s'
                """
            sql = utils.bindSqlParams(sql, {
                "object_code": int(object_code),
                "series_name": str(series_name),
            })
        with sap_one.openConn(self._config["dsn"]) as cursor:
            cursor.execute(sql)
            rows = utils.getRowsODBC(cursor, 1)
            if len(rows) > 0:
                return int(rows[0]["series"]), int(rows[0]["nextnumber"])
        return None, None

    def _getProductDefaultWarehouseCode(self, item_code):
        sql = """
                select DfltWH as warehouse_code from oitm where ItemCode='%(item_code)s';
                """
        sql = utils.bindSqlParams(sql, {"item_code": item_code})
        with sap_one.openConn(self._config["dsn"]) as cursor:
            cursor.execute(sql)
            rows = utils.getRowsODBC(cursor, 1)
            if len(rows) > 0:
                return rows[0]["warehouse_code"]
        return ""

    def _getBranchCode(self, warehouse_code):
        sql = """
            select bplid as branch_code from owhs
            where whscode = '%(warehouse_code)s'
            """
        sql = utils.bindSqlParams(sql, {
            "warehouse_code": str(warehouse_code),
        })
        with sap_one.openConn(self._config["dsn"]) as cursor:
            cursor.execute(sql)
            rows = utils.getRowsODBC(cursor, 1)
            if len(rows) > 0:
                return int(rows[0]["branch_code"])
        return None

    def _getItemStatus(self, ItemCode):
        sql = """
            select oitm."ItemCode" as itemcode from oitm
            where oitm."ItemCode" = '%(ItemCode)s'
            """
        sql = utils.bindSqlParams(sql, {
            "ItemCode": str(ItemCode),
        })
        with sap_one.openConn(self._config["dsn"]) as cursor:
            cursor.execute(sql)
            rows = utils.getRowsODBC(cursor, 1)
            if len(rows) > 0:
                return str(rows[0]["itemcode"])
        return None, None

    # File permission error when using this with v3,
    # rather use readLog?n=LINES endpoints defined in connector.go
    # def log(self):
    #     params = self._getParams()
    #     response = utils.log(params, packageDir)
    #     return json.dumps(response, indent=self._indent)

    # WARNING Be careful when doing audit reset,
    # json history is lost and meta might not be deleted
    def auditReset(self):
        t1 = time.time()
        self._setConfig()
        dbPath = self._getDbPath()
        if os.path.isfile(dbPath):
            os.remove(dbPath)
        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {}
        }
        return json.dumps(response, indent=self._indent)

    def auditResetProducts(self):
        """
        Remove all products from the local audit database
        """
        t1 = time.time()
        self._setConfig()
        with utils.openSqliteConn(self._getDbPath(), commit=True) as sqliteCursor:
            sql = "delete from product"
            sqliteCursor.execute(sql)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {}
        }
        return json.dumps(response, indent=self._indent)

    def auditResetImages(self):
        """
        Remove all images from the local audit database
        """
        t1 = time.time()
        self._setConfig()
        with utils.openSqliteConn(self._getDbPath(), commit=True) as sqliteCursor:
            sql = "delete from image"
            sqliteCursor.execute(sql)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {}
        }
        return json.dumps(response, indent=self._indent)

    def auditResetCustomers(self):
        """
        Remove all customers from the local audit database
        """
        t1 = time.time()
        self._setConfig()
        with utils.openSqliteConn(self._getDbPath(), commit=True) as sqliteCursor:
            sql = "delete from customer"
            sqliteCursor.execute(sql)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {}
        }
        return json.dumps(response, indent=self._indent)

    def auditImagesBatch(self):
        """
        This function kicks off a process that reads all the images
        filenames in the SAP database and updates the hashes in sqlite.
        If the sqlite hash differs the time stamp the image row is updated.
        getImagesBatch can then check the sync token against sqlite.
        """
        t1 = time.clock()
        images = 0
        changed = 0
        imagePath = ""
        notFound = 0
        invalidType = 0
        tooLarge = 0
        uploads = 0
        self._setConfig()

        self._checkInstall()
        params = self._getParams()

        response = utils.checkRequiredParams(
            ["audit_lower_limit", "audit_upper_limit"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        error_log_path = os.path.join(packageDir, self._configDir, "error.log")

        # Read images from SAP
        with sap_one.openConn(self._config["dsn"]) as cursor:
            # Read path to bitmap directory if no path specified
            if "image_path" in self._config:
                imagePath = self._config["image_path"]
            else:
                sql = '''SELECT TOP 1 BitmapPath FROM oadp'''
                cursor.execute(sql)
                rows = utils.getRowsODBC(cursor, 1)
                if len(rows) == 0:
                    response = {
                        "status": False,
                        "description": "No path set for picture folder",
                        "line": utils.lineNo()
                    }
                    return json.dumps(response, indent=self._indent)
                imagePath = rows[0]["bitmappath"]  # Must be lowercase

            sql = self._getImagesOdbc()
            sql = utils.bindSqlParams(sql, {
                "audit_lower_limit": int(params["audit_lower_limit"]),
                "audit_upper_limit": int(params["audit_upper_limit"])
            })
            cursor.execute(sql)
            rows = utils.getRowsODBC(cursor, 1)

            images_to_delete = []
            images_to_insert = []
            sync_token = 1

            # Get local connection
            if len(rows) > 0:
                with utils.openSqliteConn(self._getDbPath(), commit=True) as sqliteCursor:
                    while len(rows) > 0:
                        # Load the image from filesystem
                        imageFileName = rows[0]['filename']

                        path = os.path.join(imagePath, imageFileName)

                        # Exclude image types we don't want here
                        isValidType = True
                        if ".tif" in path:
                            isValidType = False

                        source_product_code = rows[0]['source_product_code']
                        source_variant_code = rows[0]['source_variant_code']
                        source_image_code = rows[0]['source_image_code']

                        if isValidType:
                            if os.path.isfile(path):
                                size = os.path.getsize(path)
                                if size <= (1024 * 1024):
                                    # Get new sync token to use
                                    sql = "select sync_token from image where error is null or error = '' order by sync_token desc limit 1"
                                    sqliteCursor.execute(sql)
                                    lastModified = sqliteCursor.fetchone()
                                    if lastModified is not None:
                                        if sync_token < (int(lastModified[0]) + 1):
                                            sync_token = int(lastModified[0]) + 1

                                    '''
                                    NB! File will not resync if it already exists in audit table and then name stays the same.
                                    In order to resync a file, one would need to either:
                                      1. Rename the file
                                      2. Remove the audit record from "image" table
                                    '''
                                    sqliteCursor.execute("select * from image where source_image_code = ?", (source_image_code,))
                                    auditRow = sqliteCursor.fetchone()
                                    # Insert image record if:
                                    # 1. it doesn't exist
                                    # 2. it exists and has error log
                                    if auditRow is None or (auditRow is not None and auditRow[self._imageIndex['error']] != "" and auditRow[self._imageIndex['error']] is not None):
                                        # This image will be pushed
                                        # Write the entry even if file > 1MB, so we don't handle it again next time
                                        images_to_insert.append((
                                            source_image_code,
                                            source_product_code,
                                            source_variant_code,
                                            utils.getTimestamp(),
                                            sync_token,
                                            imageFileName,
                                            ''
                                        ))
                                        sync_token += 1
                                        if auditRow is not None and auditRow[self._imageIndex['error']] != "" and auditRow[self._imageIndex['error']] is not None:
                                            images_to_delete.append(source_image_code)
                                        uploads += 1
                                    elif auditRow is not None:
                                        # TODO: When it was already found in audit file, update the hash
                                        pass
                                    images += 1
                                else:
                                    images_to_insert.append((
                                        source_image_code,
                                        source_product_code,
                                        source_variant_code,
                                        utils.getTimestamp(),
                                        '',
                                        imageFileName,
                                        'File size too large'
                                    ))
                                    error_msg = 'File size too large - ' + source_variant_code + ' - ' + imageFileName
                                    self._logMessageToFile(error_log_path, error_msg)

                                    # Set image to be deleted if there is an existing record in audit table
                                    images_to_delete.append(source_image_code)

                                    invalidType += 1
                                    pass
                            else:
                                images_to_insert.append((
                                    source_image_code,
                                    source_product_code,
                                    source_variant_code,
                                    utils.getTimestamp(),
                                    '',
                                    imageFileName,
                                    'File not found'
                                ))

                                error_msg = 'File not found - ' + source_variant_code + ' - ' + imageFileName
                                self._logMessageToFile(error_log_path, error_msg)

                                # Set image to be deleted if there is an existing record in audit table
                                images_to_delete.append(source_image_code)

                                notFound += 1
                                pass

                        else:
                            images_to_insert.append((
                                source_image_code,
                                source_product_code,
                                source_variant_code,
                                utils.getTimestamp(),
                                '',
                                imageFileName,
                                'Invalid image type'
                            ))

                            error_msg = 'Invalid image type - ' + source_variant_code + ' - ' + imageFileName
                            self._logMessageToFile(error_log_path, error_msg)

                            # Set image to be deleted if there is an existing record in audit table
                            images_to_delete.append(source_image_code)

                            # Invalid image type
                            invalidType += 1
                            pass

                        rows = utils.getRowsODBC(cursor, 1)
            if images_to_delete:
                self._deleteImages(images_to_delete)

            if images_to_insert:
                self._insertImages(images_to_insert)
        t2 = time.clock()
        response = {
            "status": True,
            "timer": '{} ms'.format((t2 - t1) * 1000.0),
            "data": {
                "images_audited": images,
                "images_uploaded": uploads,
                "image_path": imagePath,
                "image_not_found": notFound,
                "image_invalid": invalidType
            }
        }
        return json.dumps(response, indent=self._indent)

    def getImagesBatch(self):
        t1 = time.time()
        self._setConfig()

        params = self._getParams()

        response = utils.checkRequiredParams(
            ["sync_token", "limit"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        # Test source in the s2s console does not pass in current_iteration
        audit_images = False
        if "current_iteration" not in params:
            params["current_iteration"] = "1"

        # Only audit images on the first iteration
        if params["current_iteration"] == "1":
            response = self.auditImagesBatch()
            response = json.loads(response)
            if not response["status"]:
                return json.dumps(response, indent=self._indent)
            audit_images = True

        error_log_path = os.path.join(packageDir, self._configDir, "error.log")
        images_to_delete = []

        with sap_one.openConn(self._config["dsn"]) as cursor:
            # Read path to bitmap directory
            if "image_path" in self._config:
                imagePath = self._config["image_path"]
            else:
                sql = '''SELECT TOP 1 BitmapPath FROM oadp'''
                cursor.execute(sql)
                rows = utils.getRowsODBC(cursor, 1)
                if len(rows) == 0:
                    response = {
                        "status": False,
                        "description": "No path set for picture folder",
                        "line": utils.lineNo()
                    }
                    return json.dumps(response, indent=self._indent)
                imagePath = rows[0]["bitmappath"]  # Must be lowercase

            with utils.openSqliteConn(self._getDbPath()) as sqliteCursor:

                # Get images without errors from audit db
                # When a file is added to the audit db, then at that point we know the file:
                # - Is a valid image type (jpg, png, etc)
                # - Physically exist
                # - Is < 1MB
                # We perform the above check here again when trying to sync the images,
                # and if any of the issues are found we remove that image from the audit db
                #
                sql = self._getImagesLocal()
                sqliteCursor.execute(sql, (
                    int(params["sync_token"]), int(params["limit"])
                ))

                rows = []
                auditRow = sqliteCursor.fetchone()
                while auditRow is not None:
                    filename = auditRow[self._imageIndex['filename']]
                    source_product_code = auditRow[self._imageIndex['source_product_code']]
                    source_variant_code = auditRow[self._imageIndex['source_variant_code']]
                    sync_token = auditRow[self._imageIndex['sync_token']]
                    source_image_code = auditRow[self._imageIndex['source_image_code']]

                    path = os.path.join(imagePath, filename)
                    if os.path.isfile(path):
                        size = os.path.getsize(path)
                        if size <= (1024 * 1024):
                            with open(path, "rb") as f:
                                imageData = f.read()
                                imageHash = hashlib.md5(imageData).hexdigest()
                                encoded = base64.b64encode(imageData)
                                encoded = encoded.decode("utf-8")

                                source_image = collections.OrderedDict()
                                source_image["source"] = collections.OrderedDict()
                                source_image["source"]["source_id"] = params["source_id"]
                                source_image["source"]["source_product_code"] = source_product_code
                                source_image["source"]["source_variant_code"] = source_variant_code
                                source_image["source"]["sync_token"] = sync_token

                                source_image["image"] = collections.OrderedDict()
                                source_image["image"]["image_id"] = source_image_code
                                source_image["image"]["action"] = 'I'
                                source_image["image"]["hash"] = imageHash
                                source_image["image"]["image"] = encoded

                                rows.append(source_image)
                        else:
                            # Delete image from audit db as it is no longer valid
                            images_to_delete.append(source_image_code)
                    else:
                        # Delete image from audit db as it is no longer valid
                        images_to_delete.append(source_image_code)
                        # TODO Image no longer exists so remove audit image entry from local db to prevent blocking sync
                    auditRow = sqliteCursor.fetchone()

        if images_to_delete:
            self._deleteImages(images_to_delete)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {"source_images": rows},
            "audit_images": audit_images
        }
        return json.dumps(response, indent=self._indent)

    """
    This method is not used during the syncing process, but is here
    for the purpose of finding out whether a product's image has any issues (i.e. used for debugging)
    """
    def getImage(self):
        t1 = time.clock()
        self._setConfig()

        params = self._getParams()

        response = utils.checkRequiredParams(
            ["source_variant_code"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        with sap_one.openConn(self._config["dsn"]) as cursor:
            # Read path to bitmap directory
            if "image_path" in self._config:
                imagePath = self._config["image_path"]
            else:
                sql = '''SELECT TOP 1 BitmapPath FROM oadp'''
                cursor.execute(sql)
                rows = utils.getRowsODBC(cursor, 1)
                if len(rows) == 0:
                    response = {
                        "status": False,
                        "description": "No path set for picture folder",
                        "line": utils.lineNo()
                    }
                    return json.dumps(response, indent=self._indent)
                imagePath = rows[0]["bitmappath"]  # Must be lowercase

            sql = self._getImageOdbc()
            sql = utils.bindSqlParams(sql, {
                "source_variant_code": params["source_variant_code"],
            })
            cursor.execute(sql)
            rows = utils.getRowsODBC(cursor, 1)

            if len(rows) == 1:
                imageFileName = rows[0]['filename']

                path = os.path.join(imagePath, imageFileName)

                # NB NOTE: Files stored at some network location will not be found.
                # Creating a symlink - e.g. mklink /d "C:\Apifact_SAP_Images" "z:\" - also does not resolve this issue
                # https://github.com/stock2shop/gomedia_sapone/issues/184#issuecomment-1322203579
                if os.path.isfile(path):
                    size = os.path.getsize(path)
                    # If image is over size limit, return error, otherwise return image data
                    if size > (1024 * 1024):
                        response = {
                            "status": False,
                            "description": "Image '{}' is larger than limit of 1MB".format(path),
                            "line": utils.lineNo()
                        }
                        return json.dumps(response, indent=self._indent)
                    else:
                        with open(path, "rb") as f:
                            imageData = f.read()
                            imageHash = hashlib.md5(imageData).hexdigest()
                            encoded = base64.b64encode(imageData)
                            encoded = encoded.decode("utf-8")

                            source_image = collections.OrderedDict()
                            source_image["image"] = collections.OrderedDict()
                            source_image["image"]["hash"] = imageHash
                            source_image["image"]["image"] = encoded

                            t2 = time.clock()
                            response = {
                                "status": True,
                                "timer": '{} ms'.format((t2 - t1) * 1000.0),
                                "data": {"source_image": source_image}
                            }
                            return json.dumps(response, indent=self._indent)
                else:
                    response = {
                        "status": False,
                        "description": "No image found",
                        "line": utils.lineNo(),
                        "data": {
                            "source_image": rows,
                            "image_path": imagePath,
                            "image_full_path": path
                        }
                    }
                    return json.dumps(response, indent=self._indent)
        response = {
            "status": False,
            "description": "No image found for source_variant_code '{}'".format(str(params["source_variant_code"])),
            "line": utils.lineNo()
        }
        return json.dumps(response, indent=self._indent)

    def countProducts(self):
        t1 = time.time()
        self._setConfig()

        params = self._getParams()

        with sap_one.openConn(self._config["dsn"]) as cursor:
            sql = self._countProductsOdbc()
            sql = utils.bindSqlParams(sql, params)

            cursor.execute(sql)
            rows = utils.getRowsODBC(cursor, 1)

        # "count" must be present and be an int,
        # it is used by push.exe
        data = collections.OrderedDict()
        if "count" in rows[0]:
            data["count"] = int(rows[0]["count"])
        if "active" in rows[0]:
            data["active"] = int(rows[0]["active"])
        if "inactive" in rows[0]:
            data["inactive"] = int(rows[0]["inactive"])
        if "total" in rows[0]:
            data["total"] = int(rows[0]["total"])

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": data
        }
        return json.dumps(response, indent=self._indent)

    def countCustomers(self):
        t1 = time.time()
        self._setConfig()

        params = self._getParams()

        with sap_one.openConn(self._config["dsn"]) as cursor:
            sql = self._countCustomersOdbc()
            sql = utils.bindSqlParams(sql, params)

            cursor.execute(sql)
            rows = utils.getRowsODBC(cursor, 1)

        # "count" must be present and be an int,
        # it is used by push.exe
        data = collections.OrderedDict()
        if "count" in rows[0]:
            data["count"] = int(rows[0]["count"])
        if "active" in rows[0]:
            data["active"] = int(rows[0]["active"])
        if "inactive" in rows[0]:
            data["inactive"] = int(rows[0]["inactive"])
        if "total" in rows[0]:
            data["total"] = int(rows[0]["total"])

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": data
        }
        return json.dumps(response, indent=self._indent)

    def countImages(self):
        t1 = time.time()
        self._setConfig()

        params = self._getParams()

        with sap_one.openConn(self._config["dsn"]) as cursor:
            sql = self._countImagesOdbc()
            sql = utils.bindSqlParams(sql, params)

            cursor.execute(sql)
            rows = utils.getRowsODBC(cursor, 1)

        # "count" must be present and be an int,
        # it is used by push.exe
        data = collections.OrderedDict()
        data["count"] = int(rows[0]["count"])

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": data
        }
        return json.dumps(response, indent=self._indent)

    def getProduct(self):
        """
        This functions uses the getProduct.sql to return the product.
        It also includes the raw data returned by the sql query
        as well as the transformed S2S product
        """
        t1 = time.time()
        self._setConfig()

        params = self._getParams()

        response = utils.checkRequiredParams(
            ["source_variant_code", "source_id"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        rows = []
        with sap_one.openConn(self._config["dsn"]) as cursor:
            sql = self._getProductOdbc()
            sql = utils.bindSqlParams(sql, {
                "source_variant_code": params["source_variant_code"],
            })

            response = sap_one.execute(cursor, sql)
            if response is not None:
                return json.dumps(response, indent=self._indent)

            result = utils.getRowsODBC(cursor, 1)

            with utils.openSqliteConn(self._getDbPath()) as sqliteCursor:
                sql = self._getProductLocal()
                sqliteCursor.execute(sql, (params["source_variant_code"],))
                auditRow = sqliteCursor.fetchone()
                if auditRow is None:
                    auditRow = {
                        self._productIndex["sync_token"]: "0",
                        self._productIndex["json"]: "{}"
                    }

                if len(result) > 0:
                    transformed_product = self._transformProduct(
                        params,
                        result[0],
                        auditRow
                    )
                    transformed_product["raw_query"] = result
                    transformed_product["hash"] = utils.getHash(json.dumps(result[0]))
                    rows.append(transformed_product)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {"source_products": rows}
        }
        return json.dumps(response, indent=self._indent)

    def getCustomer(self):
        """
        This functions uses getCustomer.sql to return the customer.
        It includes the raw data returned by the sql query
        as well as the transformed S2S customer
        """
        t1 = time.time()
        self._setConfig()

        params = self._getParams()

        response = utils.checkRequiredParams(
            ["source_customer_code", "source_id"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        rows = []
        with sap_one.openConn(self._config["dsn"]) as cursor:
            sql = self._getCustomerOdbc()
            sql = utils.bindSqlParams(sql, {
                "source_customer_code": params["source_customer_code"],
            })
            cursor.execute(sql)
            result = utils.getRowsODBC(cursor, 1)

            with utils.openSqliteConn(self._getDbPath(), commit=True) as sqliteCursor:
                sql = self._getCustomerLocal()
                sqliteCursor.execute(sql, (params["source_customer_code"],))
                auditRow = sqliteCursor.fetchone()
                if auditRow is None:
                    auditRow = {
                        self._customerIndex["sync_token"]: "0",
                        self._customerIndex["json"]: "{}"
                    }

                if len(result) > 0:
                    transformed_customer = self._transformCustomer(
                        params,
                        result[0],
                        auditRow
                    )
                    transformed_customer["raw_query"] = result
                    rows.append(transformed_customer)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {"source_customers": rows}
        }
        return json.dumps(response, indent=self._indent)

    def getProductBySKU(self):
        t1 = time.time()
        self._setConfig()
        params = self._getParams()

        response = utils.checkRequiredParams(["sku"], params)
        if response is not None:
            return json.dumps(response, indent=self._indent)

        rows = []
        with sap_one.openConn(self._config["dsn"]) as cursor:
            sql = self._getProductBySKUOdbc()
            sql = utils.bindSqlParams(sql, {
                "sku": str(params["sku"])
            })

            cursor.execute(sql)
            rows = utils.getRowsODBC(cursor, 1)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {"source_products": rows}
        }
        return json.dumps(response, indent=self._indent)

    def auditProductsBatch(self):
        """
        This function kicks off a process that reads all the products
        in the SQL server database and updates the hashes in sqlite.
        If the sqlite hash differs from the time stamp the product row is updated.
        getProducts can then check the sync token against sqlite.
        """
        t1 = time.time()
        products = 0
        self._setConfig()

        self._checkInstall()
        params = self._getParams()

        response = utils.checkRequiredParams(
            ["audit_lower_limit", "audit_upper_limit"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        # Read products from SQL server
        with sap_one.openConn(self._config["dsn"]) as cursor:
            sql = self._getProductsOdbc()
            sql = utils.bindSqlParams(sql, {
                "audit_lower_limit": int(params["audit_lower_limit"]),
                "audit_upper_limit": int(params["audit_upper_limit"]),
            })

            response = sap_one.execute(cursor, sql)
            if response is not None:
                return json.dumps(response, indent=self._indent)

            rows = utils.getRowsODBC(cursor, 1)

            # Get local connection
            if len(rows) > 0:
                with utils.openSqliteConn(self._getDbPath(), commit=True) as sqliteCursor:
                    while len(rows) > 0:
                        products += 1

                        if rows[0]["product_active"] == "false":
                            # Delete inactive product images from audit table
                            self._deleteVariantImage(rows[0]["source_variant_code"], sqliteCursor)

                            # Update meta.sync_token_image
                            sql = "update meta set value = cast(min(value, (select max(sync_token) from image where error is null or error = '')) as varchar) where `key` = 'sync_token_image'"
                            sqliteCursor.execute(sql)

                        # Update hash for this product
                        self._updateHash(rows[0], sqliteCursor)
                        rows = utils.getRowsODBC(cursor, 1)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {
                "products": products
            }
        }
        return json.dumps(response, indent=self._indent)

    def auditCustomersBatch(self):
        """
        This function kicks off a process that reads all the customers
        in the SQL server database and updates the hashes in sqlite.
        If the sqlite hash differs from the time stamp the customer row is updated.
        getCustomers can then check the sync token against sqlite.
        """
        t1 = time.time()
        customers = 0
        self._setConfig()

        self._checkInstall()
        params = self._getParams()

        response = utils.checkRequiredParams(
            ["audit_lower_limit", "audit_upper_limit"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        # Read products from SQL server
        with sap_one.openConn(self._config["dsn"]) as cursor:
            sql = self._getCustomersOdbc()
            sql = utils.bindSqlParams(sql, {
                "audit_lower_limit": int(params["audit_lower_limit"]),
                "audit_upper_limit": int(params["audit_upper_limit"]),
            })
            cursor.execute(sql)
            rows = utils.getRowsODBC(cursor, 1)

            # Get local connection
            if len(rows) > 0:
                with utils.openSqliteConn(self._getDbPath(), commit=True) as sqliteCursor:
                    while len(rows) > 0:
                        customers += 1
                        # Update hash for this customer
                        self._updateCustomerHash(rows[0], sqliteCursor)
                        rows = utils.getRowsODBC(cursor, 1)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {
                "customers": customers
            }
        }
        return json.dumps(response, indent=self._indent)

    def getProductsBatch(self):
        t1 = time.time()
        self._setConfig()

        params = self._getParams()

        response = utils.checkRequiredParams(
            ["sync_token", "limit"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        # Test source in the s2s console does not pass in current_iteration
        audit_products = False
        if "current_iteration" not in params:
            params["current_iteration"] = "1"

        # Only audit products on the first iteration
        if params["current_iteration"] == "1":
            response = self.auditProductsBatch()
            response = json.loads(response)
            if not response["status"]:
                return json.dumps(response, indent=self._indent)
            audit_products = True

        with sap_one.openConn(self._config["dsn"]) as cursor:
            with utils.openSqliteConn(self._getDbPath(), commit=True) as sqliteCursor:
                sql = self._getProductsLocal()
                sqliteCursor.execute(sql, (
                    int(params["sync_token"]), int(params["limit"])
                ))

                rows = []
                auditRow = sqliteCursor.fetchone()
                while auditRow is not None:
                    sql = self._getProductOdbc()
                    sql = utils.bindSqlParams(sql, {
                        "source_variant_code":
                            auditRow[self._productIndex["source_variant_code"]]
                    })
                    cursor.execute(sql)
                    odbcRows = utils.getRowsODBC(cursor, 1)
                    if len(odbcRows) > 0:
                        rows.append(
                            self._transformProduct(
                                params,
                                odbcRows[0],
                                auditRow
                            )
                        )
                    auditRow = sqliteCursor.fetchone()

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {"source_products": rows},
            "audit_products": audit_products
        }
        return json.dumps(response, indent=self._indent)

    def getCustomersBatch(self):
        t1 = time.time()
        self._setConfig()

        params = self._getParams()

        response = utils.checkRequiredParams(
            ["sync_token", "limit"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        audit_customers = False
        if "current_iteration" not in params:
            params["current_iteration"] = "1"

        # Only audit customers on the first iteration
        if params["current_iteration"] == "1":
            response = self.auditCustomersBatch()
            response = json.loads(response)
            if not response["status"]:
                return json.dumps(response, indent=self._indent)
            audit_customers = True

        with sap_one.openConn(self._config["dsn"]) as cursor:
            with utils.openSqliteConn(self._getDbPath(), commit=True) as sqliteCursor:
                sql = self._getCustomersLocal()
                sqliteCursor.execute(sql, (
                    int(params["sync_token"]), int(params["limit"])
                ))

                rows = []
                auditRow = sqliteCursor.fetchone()
                while auditRow is not None:
                    sql = self._getCustomerOdbc()
                    sql = utils.bindSqlParams(sql, {
                        "source_customer_code":
                            auditRow[self._customerIndex["source_customer_code"]]
                    })
                    cursor.execute(sql)
                    odbcRow = utils.getRowsODBC(cursor, 1)
                    if len(odbcRow) > 0:
                        rows.append(
                            self._transformCustomer(
                                params,
                                odbcRow[0],
                                auditRow
                            )
                        )
                    auditRow = sqliteCursor.fetchone()

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {"source_customers": rows},
            "audit_customers": audit_customers
        }
        return json.dumps(response, indent=self._indent)

    def getOrder(self):
        t1 = time.time()
        self._setConfig()

        params = self._getParams()
        response = utils.checkRequiredParams(
            ["source_order_code", "source_id"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        with sap_one.openConn(self._config["dsn"]) as cursor:
            sql = self._getOrderOdbc()
            sql = utils.bindSqlParams(sql, {
                "source_order_code": params["source_order_code"]
            })
            cursor.execute(sql)
            rows = cursor.fetchall()
            source_order = self._transformOrder(params, rows)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": source_order
        }
        return json.dumps(response, indent=self._indent)

    def _beforeCreateOrder(self, payload, params):
        return payload, params

    def _duringCreateOrder(self, payload, oOrder, params):
        return

    def _afterCreateOrder(self, payload, source_order_code, source_customer_code):
        return source_order_code, source_customer_code

    # TODO Set console_user_email to keep track of sales reps for commission?
    def createOrder(self):
        t1 = time.time()
        self._setConfig()

        params = self._getParams()
        response = utils.checkRequiredParams(
            ["warehouse_code"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        try:
            payload = json.loads(self._payload())
        except ValueError:
            response = {
                "status": False,
                "description": "Request body is not valid json",
                "line": utils.lineNo()
            }
            return json.dumps(response, indent=self._indent)

        # hook for custom transforms
        payload, params = self._beforeCreateOrder(payload, params)

        order = payload["system_order"]
        source = payload["sources"][0]

        if "source_customer_code" in source and source["source_customer_code"]:
            source_customer_code = source["source_customer_code"]
        else:
            if "default_customer_code" in params:
                source_customer_code = params["default_customer_code"]

        if source_customer_code is None:
            raise Exception("source_customer_code missing and no default_customer_code set")

        # If contacts are included in the source customer code,
        # we need to strip out the contact id and only use card code.
        # The format of source_customer_code is:
        # {card_code}-{contact_id}
        if "contact_source_customer" in params:
            if params["contact_source_customer"] == "true":
                source["original_source_customer_code"] = source["source_customer_code"]
                codes = source["source_customer_code"].rsplit('-', 1)
                if codes[1]:
                    source_customer_code = codes[0]
                else:
                    raise Exception("Invalid source_customer_code {}".format(source["source_customer_code"]))

        # Parse document type if it is given in the params, otherwise create invoices by default
        document_type = "oInvoices"
        if "order_document_type" in params:
            document_type = params["order_document_type"]
            if document_type not in sap_one.BO_OBJECT_TYPES:
                response = {
                    "status": False,
                    "description": "Invalid document type '{}' given".format(document_type),
                    "line": utils.lineNo()
                }
                return json.dumps(response, indent=self._indent)

        # Get company instance
        oCompany = sap_one.getCompany(self._config)

        # Try to connect
        response = sap_one.connect(oCompany)
        if not response["status"]:
            return json.dumps(response, indent=self._indent)

        object_type = sap_one.BO_OBJECT_TYPES[document_type]["code"]
        db_server_type = sap_one.DB_SERVER_TYPES[self._config["dbServerType"]]["code"]

        # Create order object
        oOrder = oCompany.GetBusinessObject(object_type)
        oOrder.CardCode = source_customer_code
        oOrder.DocDueDate = sap_one.getVariantDate(order["created"])
        notes = ""
        if "notes" in order:
            notes = order["notes"]
        oOrder.Comments = notes

        if "doc_due_date" in params:
            oOrder.DocDueDate = sap_one.getVariantDate(params["doc_due_date"])

        if "card_name" in params:
            oOrder.CardName = params["card_name"]

        customer_reference = ""
        if "customer_reference" in params:
            customer_reference = params["customer_reference"]
        oOrder.NumAtCard = customer_reference

        if "contact_person_code" in params:
            oOrder.ContactPersonCode = params["contact_person_code"]

        if "documents_owner" in params:
            oOrder.DocumentsOwner = params["documents_owner"]

        if "project" in params:
            oOrder.Project = params["project"]

        if "reserve_invoice" in params:
            if params["reserve_invoice"] == "true" and document_type == "oInvoices":
                oOrder.ReserveInvoice = sap_one.BO_YES_NO_ENUM["tYES"]["code"]

        if "transportation_code" in params:
            oOrder.TransportationCode = params["transportation_code"]

        if "address" in params:
            oOrder.Address = params["address"]

        if "address2" in params:
            oOrder.Address2 = params["address2"]

        if "set_branch_code" in params:
            if params["set_branch_code"] == "true":
                branch_code = self._getBranchCode(
                    params["warehouse_code"])
                oOrder.BPL_IDAssignedToInvoice = branch_code

        if "currency" in params:
            oOrder.DocCurrency = params["currency"]

        # Set user defined fields which exists in the ORDR table (Header)
        for field in params:
            if "user_field_order_" in field:
                user_field = field.replace("user_field_order_", "")
                # TODO should this be?
                # oOrder.UserFields.Item(user_field).Value = params[field]
                oOrder.UserFields.Fields.Item(user_field).Value = params[field]

        # Try to set numbering series
        Series, NextNumber  = self._getNumberingSeriesByName(
            params["warehouse_code"],
            object_type,
            db_server_type)
        if Series is not None and NextNumber is not None:
            oOrder.Series = Series
            # To use the HandWritten property to manually set the next number:
            # oOrder.HandWritten = sap_one.BO_YES_NO_ENUM["tYES"]["code"]
            # oOrder.DocNum = NextNumber
        else:
            raise Exception(
                "Numbering series not found for warehouse {}".format(
                    params["warehouse_code"]))

        # Add line items to order
        exclusiveTotalCents = 0
        line_item_discount_applied = False
        base_line_count = 0
        for line_item in order["line_items"]:
            # Check if line item exists,
            # it maybe have been deleted from SAP
            # and not synced to stock2shop
            line_item_price_cents = 0
            line_discount_cents = 0
            ItemCode = self._getItemStatus(line_item["sku"])
            if ItemCode is None:
                response = {
                    "status": False,
                    "description": "ItemCode {} does not exist".format(
                        line_item["sku"]),
                    "line": utils.lineNo()
                }
                return json.dumps(response, indent=self._indent)

            oOrder.Lines.ItemCode = line_item["sku"]
            oOrder.Lines.Quantity = line_item["qty"]

            # warehouse code set on line items
            if "use_product_warehouse_code" in params and params["use_product_warehouse_code"] == "true":
                oOrder.Lines.WarehouseCode = self._getProductDefaultWarehouseCode(line_item["sku"])
            else:
                oOrder.Lines.WarehouseCode = params["warehouse_code"]

            # unit price or price
            if "use_unit_price" in params and params["use_unit_price"] == "true":
                oOrder.Lines.UnitPrice = line_item["price"]
            else:
                oOrder.Lines.Price = line_item["price"]

            if "line_item_project_code" in params:
                oOrder.Lines.ProjectCode = params["line_item_project_code"]

            if "line_item_costing_code" in params:
                oOrder.Lines.CostingCode = params["line_item_costing_code"]

            # Set user defined fields which exists in the RDR1 table (Order rows / line items)
            for field in params:
                if "user_field_line_" in field:
                    user_field = field.replace("user_field_line_", "")
                    oOrder.Lines.UserFields.Fields.Item(user_field).Value = params[field]

            # Set account codes if we have them for the client
            sales_account = self._getSalesGLAccount(line_item["sku"], source_customer_code)
            cogs_account = self._getCOGSGLAccount(line_item["sku"], source_customer_code)
            if sales_account is not None:
                oOrder.Lines.AccountCode = sales_account

            if cogs_account is not None:
                oOrder.Lines.COGSAccountCode = cogs_account

            if "use_line_item_discounts" in params and params["use_line_item_discounts"] == "true":
                line_item_price_cents = round(int(line_item["qty"]) * (line_item["price"] * 100))
                line_discount_cents = round(int(line_item["qty"]) * (line_item["total_discount"] * 100))
                oOrder.Lines.DiscountPercent = ((line_discount_cents/line_item_price_cents)*100)
                line_item_discount_applied = True
                
            exclusiveTotalCents += round(int(line_item["qty"]) * (line_item["price"] * 100))

            tax_line = line_item["tax_lines"][0]
            tax_code = self._getTaxCode(
                params, tax_line["code"]
            )
            if tax_code:
                oOrder.Lines.TaxCode = tax_code

            self._beforeAddOrderLineItem(oOrder, line_item, params, base_line_count)
            base_line_count += 1

            try:
                oOrder.Lines.Add()
            except Exception as e:
                response = {
                    "status": False,
                    "description": str(e),
                    "line": utils.lineNo()
                }
                return json.dumps(response, indent=self._indent)

        add_shipping = True
        if "ignore_shipping" in params:
            if params["ignore_shipping"] == "true":
                add_shipping = False
        if "shipping_lines" in order and "shipping_code" in params and add_shipping:
            for shipping_line in order["shipping_lines"]:
                oOrder.Lines.ItemCode = params["shipping_code"]
                oOrder.Lines.Quantity = 1
                oOrder.Lines.UnitPrice = shipping_line["price"]
                if "shipping_project_code" in params:
                    oOrder.Lines.ProjectCode = params["shipping_project_code"]

                if "shipping_costing_code" in params:
                    oOrder.Lines.CostingCode = params["shipping_costing_code"]

                exclusiveTotalCents += round(shipping_line["price"] * 100)

                try:
                    oOrder.Lines.Add()
                except Exception as e:
                    response = {
                        "status": False,
                        "description": str(e),
                        "line": utils.lineNo()
                    }
                    return json.dumps(response, indent=self._indent)

        # By default the discount is applied as a percentage on the order
        if "total_discount" in order:

            # We only want to apply total order discount if no line item discount was applied
            if not line_item_discount_applied:
                # Prevent division by zero
                if exclusiveTotalCents > 0:
                    totalDiscountCents = round(float(order["total_discount"]) * 100)
                    oOrder.DiscountPercent = (totalDiscountCents / exclusiveTotalCents) * 100

        # Hook for transforming order just before order is added
        self._duringCreateOrder(payload, oOrder, params)

        try:
            retCode = oOrder.Add()
        except Exception as e:
            response = {
                "status": False,
                "description": str(e),
                "line": utils.lineNo()
            }
            return json.dumps(response, indent=self._indent)

        if retCode != 0:
            code, message = oCompany.GetLastError(retCode)
            code = int(math.fabs(int(code)))
            if code == 5009:
                # Default message for this code is too cryptic
                message = "SKU not found"
            response = {
                "status": False,
                "description": "{} - {}".format(code, message),
                "line": utils.lineNo()
            }
            return json.dumps(response, indent=self._indent)

        else:
            # GetNewObjectCode returns DocEntry,
            # to get DocNum query the ordr table
            DocEntry = oCompany.GetNewObjectCode()
            with sap_one.openConn(self._config["dsn"]) as cursor:
                # Sales order
                if object_type == sap_one.BO_OBJECT_TYPES["oOrders"]["code"]:
                    sql = '''select ordr."DocNum" as docnum from ordr
                    where ordr."DocEntry" = '%(DocEntry)n' '''

                # Invoice
                elif object_type == sap_one.BO_OBJECT_TYPES["oInvoices"]["code"]:
                    sql = '''select oinv."DocNum" as docnum from oinv
                    where oinv."DocEntry" = '%(DocEntry)n' '''

                # Quotation
                elif object_type == sap_one.BO_OBJECT_TYPES["oQuotations"]["code"]:
                    sql = '''select oqut."DocNum" as docnum from oqut
                    where oqut."DocEntry" = '%(DocEntry)n' '''

                else:
                    response = {
                        "status": False,
                        "description": "object_type {} not implemented".format(
                            object_type),
                        "line": utils.lineNo()
                    }
                    return json.dumps(response, indent=self._indent)

                sql = utils.bindSqlParams(sql, {"DocEntry": int(DocEntry)})
                cursor.execute(sql)
                rows = utils.getRowsODBC(cursor, 1)
                if len(rows) == 0:
                    response = {
                        "status": False,
                        "description":
                            "DocNum for DocEntry {} not found".format(DocEntry),
                        "line": utils.lineNo()
                    }
                    return json.dumps(response, indent=self._indent)

                source_order_code = rows[0]["docnum"] # Must be lowercase

        # Remember to disconnect when done
        if oCompany.Connected:
            oCompany.Disconnect()

        # hook for after order
        source_order_code, source_customer_code = self._afterCreateOrder(payload, source_order_code, source_customer_code)

        # if contact used in source customer code, return the original source customer code, i.e.
        # {card_code}-{contact_id}
        if "contact_source_customer" in params:
            if params["contact_source_customer"] == "true":
                source_customer_code = source["original_source_customer_code"]

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {
                "source_order_code": source_order_code,
                "source_customer_code": source_customer_code
            }
        }
        return json.dumps(response, indent=self._indent)

    def checkSdk(self):
        self._setConfig()
        response = sap_one.testSdk(self._config)
        return json.dumps(response, indent=self._indent)

    def getHostname(self):
        """
        This function is useful when configuring the ODBC connection string
        """
        self._setConfig()
        args = ["hostname"]

        cmd = subprocess.Popen(args, stdout=subprocess.PIPE)
        response = cmd.communicate()[0]

        os_encoding = locale.getpreferredencoding()
        response = response.decode(os_encoding)

        hostname = response.replace('\n', ' ').replace('\r', '')
        response = {
            "status": True,
            "data": {
                "hostname": hostname.strip()
            }
        }
        return json.dumps(response, indent=self._indent)

    def _setMeta(self, key, value, update=False):
        t1 = time.time()
        with utils.openSqliteConn(
            self._getDbPath(), commit=True) as sqliteCursor:
            if update:
                sql = "update meta set value = ? where `key` = ?"
            else:
                sql = "insert into meta (value, `key`) values (?, ?)"
            # Note that param order must work for both queries
            sqliteCursor.execute(sql, (value, key))

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {
                "key": key,
                "value": value
            }
        }
        return response

    def setMeta(self):
        self._setConfig()
        self._checkInstall()

        params = self._getParams()
        response = utils.checkRequiredParams(
            ["key", "value"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        response = self._getMeta(params["key"])
        if response["status"]:
            response = self._setMeta(
                params["key"], params["value"], update=True)
        else:
            response = self._setMeta(params["key"], params["value"])

        return json.dumps(response, indent=self._indent)

    def _getMeta(self, key):
        t1 = time.time()
        with utils.openSqliteConn(self._getDbPath()) as sqliteCursor:
            sql = self._getMetaLocal()
            sqliteCursor.execute(sql, (key,))
            row = sqliteCursor.fetchone()
            if row is None:
                response = {
                    "status": False,
                    "description": "Key {} not found".format(key),
                    "line": utils.lineNo()
                }
                return response

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {
                "key": row[self.metaIndex["key"]],
                "value": row[self.metaIndex["value"]]
            }
        }
        return response

    def getMeta(self):
        self._setConfig()
        self._checkInstall()

        params = self._getParams()
        response = utils.checkRequiredParams(
            ["key"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        response = self._getMeta(params["key"])
        return json.dumps(response, indent=self._indent)

    def deleteMeta(self):
        t1 = time.time()
        self._setConfig()
        self._checkInstall()

        params = self._getParams()
        response = utils.checkRequiredParams(
            ["key"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        response = self._getMeta(params["key"])
        if response["status"]:
            with utils.openSqliteConn(
                self._getDbPath(), commit=True) as sqliteCursor:
                sql = "delete from meta where `key` = ?"
                sqliteCursor.execute(sql, (params["key"],))

        else:
            response = {
                "status": False,
                "description": "Key {} not found".format(params["key"]),
                "line": utils.lineNo()
            }
            return json.dumps(response, indent=self._indent)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {
                "key": params["key"]
            }
        }
        return json.dumps(response, indent=self._indent)

    def _beforeAddOrderLineItem(self, oOrder, line_item, params, base_line_count):
        """
        This function is a hook for adding custom logic before adding a line item
        """

    """
    Log message to file
    """
    def _logMessageToFile(self, filePath, msg):
        self._setConfig()

        if "debug" in self._config and self._config["debug"] == 1:
            msg = utils.getTimestamp() + " " + msg + "\n"
            text_file = open(filePath, "a")
            text_file.write(str(msg))
            text_file.close()

    """
    Delete image records from local audit db
    """
    def _deleteImages(self, images_to_delete):
        self._setConfig()

        with utils.openSqliteConn(self._getDbPath(), commit=True) as sqliteCursor:
            sql = "delete from image where `source_image_code` in (?)"
            for chunk in images_to_delete:
                sqliteCursor.execute(sql, (chunk,))

    """
    Insert image records to local audit db
    """
    def _insertImages(self, images_to_insert):
        self._setConfig()

        with utils.openSqliteConn(self._getDbPath(), commit=True) as sqliteCursor:
            sqliteCursor.execute('BEGIN TRANSACTION')
            sql = "insert into image (source_image_code, source_product_code, source_variant_code, modified, sync_token, filename, error) values (?, ?, ?, ?, ?, ?, ?)"
            for chunk in images_to_insert:
                sqliteCursor.execute(sql, chunk)
            sqliteCursor.execute('COMMIT')

    def _deleteVariantImage(self, source_variant_code, sqliteCursor):
        sql = "delete from image where source_variant_code = ?"
        sqliteCursor.execute(
            sql, ((
                source_variant_code,
            ))
        )

    def getDatabases(self):
        """
        This functions lists the database available on the SQL Server instance
        """
        t1 = time.time()
        self._setConfig()

        with sap_one.openConn(self._config["dsnNoDb"]) as cursor:
            sql = "select name from master.dbo.sysdatabases"
            cursor.execute(sql)
            result = utils.getRowsODBC(cursor, 100)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {"databases": result}
        }
        return json.dumps(response, indent=self._indent)

    def _getSalesGLAccount(self, item_code, customer_code):
        return None

    def _getCOGSGLAccount(self, item_code, customer_code):
        return None
