select
    rtrim(i.GUID)             as "source_product_code",
    rtrim(i.GUID)             as "source_variant_code",
    rtrim(i.Description)      as "title",
    rtrim(i.ItemCode)         as "variants.sku",
    rtrim(ig.Description)     as "collection",
    ''                        as "product_type",
    rtrim(ic.ICDesc)          as "vendor",
    ''                        as "variants.option1",
    ''                        as "variants.option2",
    ''                        as "variants.option3",
    ''                        as "body_html",
    rtrim(i.NettMass)         as "variants.weight",
    rtrim(i.Barcode)          as "variants.barcode",

    -- Warehouse 1 qty
    (
      (
        (wh1.OpeningQty + wh1.QtyBuyLast + wh1.QtyAdjustLast - wh1.QtySellLast) +
        (wh1.QtyBuyThis01 + wh1.QtyBuyThis02 + wh1.QtyBuyThis03 + wh1.QtyBuyThis04 +
            wh1.QtyBuyThis05 + wh1.QtyBuyThis06 + wh1.QtyBuyThis07 + wh1.QtyBuyThis08 +
            wh1.QtyBuyThis09 + wh1.QtyBuyThis10 + wh1.QtyBuyThis11 + wh1.QtyBuyThis12 +
            wh1.QtyBuyThis13) +
        (wh1.QtyAdjustThis01 + wh1.QtyAdjustThis02 + wh1.QtyAdjustThis03 +
            wh1.QtyAdjustThis04 + wh1.QtyAdjustThis05 + wh1.QtyAdjustThis06 +
            wh1.QtyAdjustThis07 + wh1.QtyAdjustThis08 + wh1.QtyAdjustThis09 +
            wh1.QtyAdjustThis10 + wh1.QtyAdjustThis11 + wh1.QtyAdjustThis12 +
            wh1.QtyAdjustThis13) -
        (wh1.QtySellThis01 + wh1.QtySellThis02 + wh1.QtySellThis03 + wh1.QtySellThis04 +
            wh1.QtySellThis05 + wh1.QtySellThis06 + wh1.QtySellThis07 + wh1.QtySellThis08 +
            wh1.QtySellThis09 + wh1.QtySellThis10 + wh1.QtySellThis11 + wh1.QtySellThis12 +
            wh1.QtySellThis13)
      )
    )                         as "qty_on_hand",
    (
      select sum(QtyLeft)
      from HistoryLines where
        Documenttype = 102 and
        ItemCode = i.ItemCode and
        MultiStore = wh1.StoreCode
    )                         as "qty_on_sales_order",

    -- Warehouse 1 prices
    -- Note that price can be set by warehouse (store)
    wh1.SellExcl01            as "variants.retail_price",
    wh1.SellExcl02            as "variants.dealer_price",
    wh1.SellExcl03            as "variants.distribution_price",
    wh1.SellExcl04            as "variants.special_price",
    wh1.SellExcl05            as "variants.wholesale_price",

    '01'                      as "default_price_tier",
    (
    '01|' + CAST(cast(wh1.SellExcl01 AS DECIMAL (10,2)) AS VARCHAR(30)) + ',' +
    '02|' + CAST(cast(wh1.SellExcl02 AS DECIMAL (10,2)) AS VARCHAR(30)) + ',' +
    '03|' + CAST(cast(wh1.SellExcl03 AS DECIMAL (10,2)) AS VARCHAR(30)) + ',' +
    '04|' + CAST(cast(wh1.SellExcl04 AS DECIMAL (10,2)) AS VARCHAR(30)) + ',' +
    '05|' + CAST(cast(wh1.SellExcl05 AS DECIMAL (10,2)) AS VARCHAR(30)) + ',' +
    '06|' + CAST(cast(wh1.SellExcl06 AS DECIMAL (10,2)) AS VARCHAR(30)) + ',' +
    '07|' + CAST(cast(wh1.SellExcl07 AS DECIMAL (10,2)) AS VARCHAR(30)) + ',' +
    '08|' + CAST(cast(wh1.SellExcl08 AS DECIMAL (10,2)) AS VARCHAR(30)) + ',' +
    '09|' + CAST(cast(wh1.SellExcl09 AS DECIMAL (10,2)) AS VARCHAR(30)) + ',' +
    '10|' + CAST(cast(wh1.SellExcl10 AS DECIMAL (10,2)) AS VARCHAR(30)) + ',' +
    'special|' + CAST(cast(wh1.SpecialPriceExcl AS DECIMAL (10,2)) AS VARCHAR(30)))
                              as "csv_price_tiers",

    case
        when i.Blocked = 1 then 'false'
        else 'true'
    end                       as "product_active",
    ''                        as "tags",
    rtrim(ig.Description)     as "inventory_group",

    'true'                    as "variants.inventory_management",

    i.Picture                 as "picture",
    wh1.SellExcl04            as "meta_special_price",
    rtrim(i.UserDefText03)    as "meta_momsen_online",
    rtrim(i.UserDefNum01)     as "meta_user_def_num_1",
    rtrim(i.UserDefNum02)     as "meta_user_def_num_2",
    rtrim(i.UserDefNum03)     as "meta_user_def_num_3",
    rtrim(i.UserDefText01)    as "meta_user_def_text_1",
    rtrim(i.UserDefText02)    as "meta_user_def_text_2",
    rtrim(i.UserDefText03)    as "meta_user_def_text_3",
    
    case rtrim(i.UserDefNum02)
        when '1' then 'true'
        else 'false'
    end                       as "meta_over_order_enabled"

from Inventory i

    -- Warehouse 1
    left join MultiStoreTrn wh1 on
      wh1.ItemCode = i.ItemCode and
      wh1.StoreCode = '%(multi_store)s'
    left join InventoryGroups ig on
      ig.InvGroup = wh1.InvGroup

    -- Attribute info
    left join InventoryCategory ic on
      i.Category = ic.ICCode

WHERE i.GUID <> '' AND i.UserDefText01 = 'b2b_website' AND wh1.StoreCode = '%(multi_store)s' AND i.GUID = '%(source_variant_code)s';
