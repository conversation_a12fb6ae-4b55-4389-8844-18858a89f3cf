-- SELECT TOP 25
--     wh1.StoreCode
--
-- FROM MultiStoreTrn wh1
-- WHERE wh1.ItemCode = 'WL5984';


select top 2
    -- Warehouse 1 qty
    (
      (
        (wh1.OpeningQty + wh1.QtyBuyLast + wh1.QtyAdjustLast - wh1.QtySellLast) +
        (wh1.QtyBuyThis01 + wh1.QtyBuyThis02 + wh1.QtyBuyThis03 + wh1.QtyBuyThis04 +
            wh1.QtyBuyThis05 + wh1.QtyBuyThis06 + wh1.QtyBuyThis07 + wh1.QtyBuyThis08 +
            wh1.QtyBuyThis09 + wh1.QtyBuyThis10 + wh1.QtyBuyThis11 + wh1.QtyBuyThis12 +
            wh1.QtyBuyThis13) +
        (wh1.QtyAdjustThis01 + wh1.QtyAdjustThis02 + wh1.QtyAdjustThis03 +
            wh1.QtyAdjustThis04 + wh1.QtyAdjustThis05 + wh1.QtyAdjustThis06 +
            wh1.QtyAdjustThis07 + wh1.QtyAdjustThis08 + wh1.QtyAdjustThis09 +
            wh1.QtyAdjustThis10 + wh1.QtyAdjustThis11 + wh1.QtyAdjustThis12 +
            wh1.QtyAdjustThis13) -
        (wh1.QtySellThis01 + wh1.QtySellThis02 + wh1.QtySellThis03 + wh1.QtySellThis04 +
            wh1.QtySellThis05 + wh1.QtySellThis06 + wh1.QtySellThis07 + wh1.QtySellThis08 +
            wh1.QtySellThis09 + wh1.QtySellThis10 + wh1.QtySellThis11 + wh1.QtySellThis12 +
            wh1.QtySellThis13)
      )
    )                         as "qty_on_hand_1",
    (
      select sum(QtyLeft)
      from HistoryLines where
        Documenttype = 102 and
        ItemCode = i.ItemCode and
        MultiStore = wh1.StoreCode
    )                         as "qty_on_sales_order_1",

    -- Warehouse 2 qty
    (
      (
        (wh2.OpeningQty + wh2.QtyBuyLast + wh2.QtyAdjustLast - wh2.QtySellLast) +
        (wh2.QtyBuyThis01 + wh2.QtyBuyThis02 + wh2.QtyBuyThis03 + wh2.QtyBuyThis04 +
            wh2.QtyBuyThis05 + wh2.QtyBuyThis06 + wh2.QtyBuyThis07 + wh2.QtyBuyThis08 +
            wh2.QtyBuyThis09 + wh2.QtyBuyThis10 + wh2.QtyBuyThis11 + wh2.QtyBuyThis12 +
            wh2.QtyBuyThis13) +
        (wh2.QtyAdjustThis01 + wh2.QtyAdjustThis02 + wh2.QtyAdjustThis03 +
            wh2.QtyAdjustThis04 + wh2.QtyAdjustThis05 + wh2.QtyAdjustThis06 +
            wh2.QtyAdjustThis07 + wh2.QtyAdjustThis08 + wh2.QtyAdjustThis09 +
            wh2.QtyAdjustThis10 + wh2.QtyAdjustThis11 + wh2.QtyAdjustThis12 +
            wh2.QtyAdjustThis13) -
        (wh2.QtySellThis01 + wh2.QtySellThis02 + wh2.QtySellThis03 + wh2.QtySellThis04 +
            wh2.QtySellThis05 + wh2.QtySellThis06 + wh2.QtySellThis07 + wh2.QtySellThis08 +
            wh2.QtySellThis09 + wh2.QtySellThis10 + wh2.QtySellThis11 + wh2.QtySellThis12 +
            wh2.QtySellThis13)
      )
    )                         as "qty_on_hand_2",
    (
      select sum(QtyLeft)
      from HistoryLines where
        Documenttype = 102 and
        ItemCode = i.ItemCode and
        MultiStore = wh2.StoreCode
    )                         as "qty_on_sales_order_2",

    -- Warehouse 3 qty
    (
      (
        (wh3.OpeningQty + wh3.QtyBuyLast + wh3.QtyAdjustLast - wh3.QtySellLast) +
        (wh3.QtyBuyThis01 + wh3.QtyBuyThis02 + wh3.QtyBuyThis03 + wh3.QtyBuyThis04 +
            wh3.QtyBuyThis05 + wh3.QtyBuyThis06 + wh3.QtyBuyThis07 + wh3.QtyBuyThis08 +
            wh3.QtyBuyThis09 + wh3.QtyBuyThis10 + wh3.QtyBuyThis11 + wh3.QtyBuyThis12 +
            wh3.QtyBuyThis13) +
        (wh3.QtyAdjustThis01 + wh3.QtyAdjustThis02 + wh3.QtyAdjustThis03 +
            wh3.QtyAdjustThis04 + wh3.QtyAdjustThis05 + wh3.QtyAdjustThis06 +
            wh3.QtyAdjustThis07 + wh3.QtyAdjustThis08 + wh3.QtyAdjustThis09 +
            wh3.QtyAdjustThis10 + wh3.QtyAdjustThis11 + wh3.QtyAdjustThis12 +
            wh3.QtyAdjustThis13) -
        (wh3.QtySellThis01 + wh3.QtySellThis02 + wh3.QtySellThis03 + wh3.QtySellThis04 +
            wh3.QtySellThis05 + wh3.QtySellThis06 + wh3.QtySellThis07 + wh3.QtySellThis08 +
            wh3.QtySellThis09 + wh3.QtySellThis10 + wh3.QtySellThis11 + wh3.QtySellThis12 +
            wh3.QtySellThis13)
      )
    )                         as "qty_on_hand_3",
    (
      select sum(QtyLeft)
      from HistoryLines where
        Documenttype = 102 and
        ItemCode = i.ItemCode and
        MultiStore = wh3.StoreCode
    )                         as "qty_on_sales_order_3",

    -- Warehouse 4 qty
    (
      (
        (wh4.OpeningQty + wh4.QtyBuyLast + wh4.QtyAdjustLast - wh4.QtySellLast) +
        (wh4.QtyBuyThis01 + wh4.QtyBuyThis02 + wh4.QtyBuyThis03 + wh4.QtyBuyThis04 +
            wh4.QtyBuyThis05 + wh4.QtyBuyThis06 + wh4.QtyBuyThis07 + wh4.QtyBuyThis08 +
            wh4.QtyBuyThis09 + wh4.QtyBuyThis10 + wh4.QtyBuyThis11 + wh4.QtyBuyThis12 +
            wh4.QtyBuyThis13) +
        (wh4.QtyAdjustThis01 + wh4.QtyAdjustThis02 + wh4.QtyAdjustThis03 +
            wh4.QtyAdjustThis04 + wh4.QtyAdjustThis05 + wh4.QtyAdjustThis06 +
            wh4.QtyAdjustThis07 + wh4.QtyAdjustThis08 + wh4.QtyAdjustThis09 +
            wh4.QtyAdjustThis10 + wh4.QtyAdjustThis11 + wh4.QtyAdjustThis12 +
            wh4.QtyAdjustThis13) -
        (wh4.QtySellThis01 + wh4.QtySellThis02 + wh4.QtySellThis03 + wh4.QtySellThis04 +
            wh4.QtySellThis05 + wh4.QtySellThis06 + wh4.QtySellThis07 + wh4.QtySellThis08 +
            wh4.QtySellThis09 + wh4.QtySellThis10 + wh4.QtySellThis11 + wh4.QtySellThis12 +
            wh4.QtySellThis13)
      )
    )                         as "qty_on_hand_4",
    (
      select sum(QtyLeft)
      from HistoryLines where
        Documenttype = 102 and
        ItemCode = i.ItemCode and
        MultiStore = wh4.StoreCode
    )                         as "qty_on_sales_order_4",

    -- Warehouse 5 qty
    (
      (
        (wh5.OpeningQty + wh5.QtyBuyLast + wh5.QtyAdjustLast - wh5.QtySellLast) +
        (wh5.QtyBuyThis01 + wh5.QtyBuyThis02 + wh5.QtyBuyThis03 + wh5.QtyBuyThis04 +
            wh5.QtyBuyThis05 + wh5.QtyBuyThis06 + wh5.QtyBuyThis07 + wh5.QtyBuyThis08 +
            wh5.QtyBuyThis09 + wh5.QtyBuyThis10 + wh5.QtyBuyThis11 + wh5.QtyBuyThis12 +
            wh5.QtyBuyThis13) +
        (wh5.QtyAdjustThis01 + wh5.QtyAdjustThis02 + wh5.QtyAdjustThis03 +
            wh5.QtyAdjustThis04 + wh5.QtyAdjustThis05 + wh5.QtyAdjustThis06 +
            wh5.QtyAdjustThis07 + wh5.QtyAdjustThis08 + wh5.QtyAdjustThis09 +
            wh5.QtyAdjustThis10 + wh5.QtyAdjustThis11 + wh5.QtyAdjustThis12 +
            wh5.QtyAdjustThis13) -
        (wh5.QtySellThis01 + wh5.QtySellThis02 + wh5.QtySellThis03 + wh5.QtySellThis04 +
            wh5.QtySellThis05 + wh5.QtySellThis06 + wh5.QtySellThis07 + wh5.QtySellThis08 +
            wh5.QtySellThis09 + wh5.QtySellThis10 + wh5.QtySellThis11 + wh5.QtySellThis12 +
            wh5.QtySellThis13)
      )
    )                         as "qty_on_hand_5",
    (
      select sum(QtyLeft)
      from HistoryLines where
        Documenttype = 102 and
        ItemCode = i.ItemCode and
        MultiStore = wh5.StoreCode
    )                         as "qty_on_sales_order_5",

    -- Warehouse 6 qty
    (
      (
        (wh6.OpeningQty + wh6.QtyBuyLast + wh6.QtyAdjustLast - wh6.QtySellLast) +
        (wh6.QtyBuyThis01 + wh6.QtyBuyThis02 + wh6.QtyBuyThis03 + wh6.QtyBuyThis04 +
            wh6.QtyBuyThis05 + wh6.QtyBuyThis06 + wh6.QtyBuyThis07 + wh6.QtyBuyThis08 +
            wh6.QtyBuyThis09 + wh6.QtyBuyThis10 + wh6.QtyBuyThis11 + wh6.QtyBuyThis12 +
            wh6.QtyBuyThis13) +
        (wh6.QtyAdjustThis01 + wh6.QtyAdjustThis02 + wh6.QtyAdjustThis03 +
            wh6.QtyAdjustThis04 + wh6.QtyAdjustThis05 + wh6.QtyAdjustThis06 +
            wh6.QtyAdjustThis07 + wh6.QtyAdjustThis08 + wh6.QtyAdjustThis09 +
            wh6.QtyAdjustThis10 + wh6.QtyAdjustThis11 + wh6.QtyAdjustThis12 +
            wh6.QtyAdjustThis13) -
        (wh6.QtySellThis01 + wh6.QtySellThis02 + wh6.QtySellThis03 + wh6.QtySellThis04 +
            wh6.QtySellThis05 + wh6.QtySellThis06 + wh6.QtySellThis07 + wh6.QtySellThis08 +
            wh6.QtySellThis09 + wh6.QtySellThis10 + wh6.QtySellThis11 + wh6.QtySellThis12 +
            wh6.QtySellThis13)
      )
    )                         as "qty_on_hand_6",
    (
      select sum(QtyLeft)
      from HistoryLines where
        Documenttype = 102 and
        ItemCode = i.ItemCode and
        MultiStore = wh6.StoreCode
    )                         as "qty_on_sales_order_6"

from Inventory i

-- rows: [
-- {
-- storecode: ""
-- },
-- {
-- storecode: "001"
-- },
-- {
-- storecode: "002"
-- },
-- {
-- storecode: "003"
-- },
-- {
-- storecode: "004"
-- },
-- {
-- storecode: "005"
-- }
-- ]
-- },

    -- Warehouse 1
    left join MultiStoreTrn wh1 on
      wh1.ItemCode = i.ItemCode and
      wh1.StoreCode = '001'

    -- Warehouse 2
    left join MultiStoreTrn wh2 on
      wh2.ItemCode = i.ItemCode and
      wh2.StoreCode = '002'

    -- Warehouse 3
    left join MultiStoreTrn wh3 on
      wh3.ItemCode = i.ItemCode and
      wh3.StoreCode = '003'

    -- Warehouse 4
    left join MultiStoreTrn wh4 on
      wh4.ItemCode = i.ItemCode and
      wh4.StoreCode = '004'

    -- Warehouse 5
    left join MultiStoreTrn wh5 on
      wh5.ItemCode = i.ItemCode and
      wh5.StoreCode = '005'

    -- Warehouse wtf
    left join MultiStoreTrn wh6 on
      wh6.ItemCode = i.ItemCode and
      wh6.StoreCode = ''

WHERE i.ItemCode = 'WL5984';


-- select top 2
--     rtrim(i.GUID)             as "source_product_code",
--     rtrim(i.GUID)             as "source_variant_code",
--     rtrim(i.Description)      as "title",
--     rtrim(i.ItemCode)         as "variants.sku",
--     rtrim(ig.Description)     as "collection",
--     ''                        as "product_type",
--     rtrim(ic.ICDesc)          as "vendor",
--     ''                        as "variants.option1",
--     ''                        as "variants.option2",
--     ''                        as "variants.option3",
--     ''                        as "body_html",
--     rtrim(i.NettMass)         as "variants.weight",
--     rtrim(i.Barcode)          as "variants.barcode",
--
--     -- Warehouse 1 qty
--     (
--       (
--         (wh1.OpeningQty + wh1.QtyBuyLast + wh1.QtyAdjustLast - wh1.QtySellLast) +
--         (wh1.QtyBuyThis01 + wh1.QtyBuyThis02 + wh1.QtyBuyThis03 + wh1.QtyBuyThis04 +
--             wh1.QtyBuyThis05 + wh1.QtyBuyThis06 + wh1.QtyBuyThis07 + wh1.QtyBuyThis08 +
--             wh1.QtyBuyThis09 + wh1.QtyBuyThis10 + wh1.QtyBuyThis11 + wh1.QtyBuyThis12 +
--             wh1.QtyBuyThis13) +
--         (wh1.QtyAdjustThis01 + wh1.QtyAdjustThis02 + wh1.QtyAdjustThis03 +
--             wh1.QtyAdjustThis04 + wh1.QtyAdjustThis05 + wh1.QtyAdjustThis06 +
--             wh1.QtyAdjustThis07 + wh1.QtyAdjustThis08 + wh1.QtyAdjustThis09 +
--             wh1.QtyAdjustThis10 + wh1.QtyAdjustThis11 + wh1.QtyAdjustThis12 +
--             wh1.QtyAdjustThis13) -
--         (wh1.QtySellThis01 + wh1.QtySellThis02 + wh1.QtySellThis03 + wh1.QtySellThis04 +
--             wh1.QtySellThis05 + wh1.QtySellThis06 + wh1.QtySellThis07 + wh1.QtySellThis08 +
--             wh1.QtySellThis09 + wh1.QtySellThis10 + wh1.QtySellThis11 + wh1.QtySellThis12 +
--             wh1.QtySellThis13)
--       )
--     )                         as "qty_on_hand",
--     (
--       select sum(QtyLeft)
--       from HistoryLines where
--         Documenttype = 102 and
--         ItemCode = i.ItemCode and
--         MultiStore = wh1.StoreCode
--     )                         as "qty_on_sales_order",
--
--     -- Warehouse 1 prices
--     -- Note that price can be set by warehouse (store)
--     wh1.SellExcl01            as "variants.retail_price",
--     wh1.SellExcl02            as "variants.dealer_price",
--     wh1.SellExcl03            as "variants.distribution_price",
--     wh1.SellExcl05            as "variants.wholesale_price",
--     case
--         when i.Blocked = 1 then 'false'
--         else 'true'
--     end                       as "product_active",
--     ''                        as "tags",
--     rtrim(ig.Description)     as "inventory_group",
--     'true'                    as "variants.inventory_management",
--     i.Picture                 as "picture"
-- from Inventory i
--
--     -- Warehouse 1
--     left join MultiStoreTrn wh1 on
--       wh1.ItemCode = i.ItemCode and
--       wh1.StoreCode = '001'
--     left join InventoryGroups ig on
--       ig.InvGroup = wh1.InvGroup
--
--     -- Attribute info
--     left join InventoryCategory ic on
--       i.Category = ic.ICCode
--
-- WHERE i.GUID <> '' AND i.UserDefText01 = 'b2b_website' AND wh1.StoreCode = '001' AND i.GUID = 'F5FFA95D4989E76A174BECA966362D98';
