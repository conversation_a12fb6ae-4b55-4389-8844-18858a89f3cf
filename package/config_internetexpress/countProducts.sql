-- Example uses Chinook database v1.4
select count(*) as count
from vX_IX_S2S_Item_Master m with (nolock) 
    inner JOIN  vX_IX_S2S_Item_SOH s with (nolock) on m.source_variant_code = s.source_variant_code
    inner  Join vX_IX_S2S_Item_Price p  with (nolock) on p.source_variant_code = m.source_variant_code
    

where isnull(s.source_variant_code,'') <> ''
  And isnull(m.source_product_code,'') <>''
