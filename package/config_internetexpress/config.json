{"dbName": "local.db", "dsn": "Driver={SQL Server Native Client 11.0};Server=monster;Database=FORWARD_INTERNET_EXPRESS;Uid=S2S;Pwd=********;", "audit_limit": 1000, "push": {"source_id": 1213, "limit": 500, "token": "97LJ9VUSDELDKHBGR5TWYZK2SMDNNIOZ6ZT9I8Y9", "writeLog": "http://localhost:13337/writeLog", "getProducts": "http://localhost:13337/odbc/getProductsBatch", "getCustomers": "http://localhost:13337/odbc/getCustomersBatch", "countProducts": "http://localhost:13337/odbc/countProducts", "countCustomers": "http://localhost:13337/odbc/countCustomers", "getMeta": "http://localhost:13337/odbc/getMeta", "setMeta": "http://localhost:13337/odbc/setMeta", "productsQueue": "https://app.stock2shop.com/v1/products/queue", "customersQueue": "https://app.stock2shop.com/v1/customers/queue"}}