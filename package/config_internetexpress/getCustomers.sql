select *
from (
select
    row_number() over (order by Customer.CustomerId) as n,

    convert(varchar, CustomerId)
               as 'source_customer_code',

    FirstName  as 'first_name',
    LastName   as 'last_name',
    Email      as 'email',
    'true'     as 'customer_active',
    1          as 'accepts_marketing',
    Address    as 'address.address1', -- or Address?
    ''         as 'address.address2',
    City       as 'address.city',
    Country    as 'address.country',
    ''         as 'address.country_code',
    ''         as 'address.province',
    ''         as 'address.province_code',
    PostalCode as 'address.zip',
    Company    as 'address.company',
    Phone      as 'address.phone',

    ''         as 'price_tier',
    ''         as 'qty_availability'

from Customer with (nolock)

) as rows
where
n > '%(audit_lower_limit)n'
and n <= '%(audit_upper_limit)n'