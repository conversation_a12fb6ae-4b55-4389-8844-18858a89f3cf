select
    

    m.source_product_code                      as "source_product_code",
    m.source_variant_code                      as "source_variant_code",
    (m.tags + ' - '+ m.title)                  as "title",
    m.collection                               as "collection",
    m.product_type                             as "product_type",
    m.vendor                                   as "vendor",
    <PERSON>Null(m.Option1_Name,'')                  as "variants.option1_name",
    Is<PERSON>ull(m.Option1_value,'')                 as "variants.option1_value",
    IsNull(m.Option2_Name,'')                  as "variants.option2_name",
    IsNull(m.Option2_value,'')                 as "variants.option2_value",
    IsNull(m.Option3_Name,'')                  as "variants.option3_name",
    IsNull(m.Option3_value,'')                 as "variants.option3_value",
    m.source_variant_code                      as "variants.sku",
    m.body_html                                as "body_html",
    (select cast(m.grams as INT) )             as "variants.weight",
    m.barcode                                  as "variants.barcode",
    isnull(s.qty,0)                            as "variants.qty",
    -- isnull(p.selling_price,0)                  as "variants.retail_price",
    'true'                                     as "variants.inventory_management",
    m.product_active                           as "product_active",
    (m.collection +','+m.product_type +',' +m.tags) 
                                               as "tags",


    'Selling'                            as "default_price_tier",
    'Selling|' +
            CONVERT(VARCHAR, CAST(isnull(p.selling_price,0)   AS FLOAT)) +
        ',' +'PPD|' +
            CONVERT(VARCHAR, CAST(isnull(p.ppd,0)   AS FLOAT))
                                                as "csv_price_tiers"

from vX_IX_S2S_Item_Master m with (nolock) 
    inner join  vX_IX_S2S_Item_SOH s with (nolock) on m.source_variant_code = s.source_variant_code
    inner join vX_IX_S2S_Item_Price p  with (nolock) on p.source_variant_code = m.source_variant_code
    

where isnull(m.source_variant_code,'') <> ''
  And isnull(m.source_product_code,'') <>''
  and m.source_variant_code  = '%(source_variant_code)s';