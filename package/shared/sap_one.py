import pyodbc
import win32com.client
import win32timezone # used by getVariantDate
import pythoncom
from contextlib import contextmanager
from datetime import datetime
import time

from datetime import tzinfo, timedelta
from . import utils
import re

# ..............................................................................
# win32com.client.VARIANT requires a timezone aware datetime
# http://stackoverflow.com/a/25421145/639133

ZERO = timedelta(0)


class UTC(tzinfo):
    def utcoffset(self, dt):
        return ZERO

    def tzname(self, dt):
        return "UTC"

    def dst(self, dt):
        return ZERO


utc = UTC()

# ..............................................................................

# Values cribbed using Visual Studio project shared/SapOne
DB_SERVER_TYPES = {
    "MSSQL": {"code": 1},
    "MSSQL2005": {"code": 4},
    "MSSQL2008": {"code": 6},
    "MSSQL2012": {"code": 7},
    "MSSQL2014": {"code": 8},
    "HANADB": {"code": 9},
    "MSSQL2016": {"code": 10},
    "MSSQL2017": {"code": 11},
    "MSSQL2019": {"code": 15},
}

BO_OBJECT_TYPES = {
    "oInvoices": {"code": 13},
    "oDeliveryNotes": {"code": 15},
    "oOrders": {"code": 17},
    "oPurchaseInvoices": {"code": 18},
    "oPurchaseOrders": {"code": 22},
    "oQuotations": {"code": 23},
    "oIncomingPayments": {"code": 24},
}

BO_YES_NO_ENUM = {
    "tNO": {"code": 0},
    "tYES": {"code": 1},
}

# Rather user openConn instead of getConn
# because it automatically closes the connection:
# http://stackoverflow.com/a/3783305/639133
@contextmanager
def openConn(dsn, commit=False):
    connection = pyodbc.connect(dsn)
    cursor = connection.cursor()
    try:
        yield cursor
    except pyodbc.DatabaseError as err:
        error, = err.args
        cursor.execute("ROLLBACK")
        raise err
    else:
        if commit:
            cursor.execute("COMMIT")
    finally:
        connection.close()


def getCompany(config):
    company = win32com.client.Dispatch("SAPBobsCOM.Company")
    company.Server = config["server"]
    company.CompanyDB = config["companyDb"]
    company.UserName = config["userName"]
    company.Password = config["password"]
    company.DbServerType = DB_SERVER_TYPES[config["dbServerType"]]["code"]
    company.DbUserName = config["dbUserName"]
    company.DbPassword = config["dbPassword"]
    company.LicenseServer = config["licenseServer"]
    return company


def connect(company):
    retVal = company.Connect()

    if retVal == 0:
        response = {
            "status": True,
            "data": {
                "company_name": company.CompanyName
            }
        }

    else:
        error = company.GetLastError(retVal)
        response = {
            "status": False,
            "code": error[0],
            "description": error[1]
        }

    return response


def testSdk(config):
    # Get company instance
    company = getCompany(config)

    # Try to connect
    response = connect(company)

    # Remember to disconnect when done
    if company.Connected:
        company.Disconnect()

    return response


def getVariantDate(isoDate):
    """
    Return win32com compatible date given iso date string.

    Most of the python win32com interface takes python objects as params,
    in some cases params must be declared as win32com.client.VARIANT
    http://docs.activestate.com/activepython/3.4/pywin32/html/com/win32com/HTML/variant.html

    :param isoDate:
    :return:
    """

    # We assume that isoDate is UTC,
    # date strings returned from the stock2shop API must always be UTC!
    # Conversion to local timezones for display must be done client side.
    pythonDate = datetime.strptime(isoDate[:19], "%Y-%m-%dT%H:%M:%S")

    tzDate = datetime(
        pythonDate.year, pythonDate.month, pythonDate.day, tzinfo=utc)

    variantDate = win32com.client.VARIANT(pythoncom.VT_DATE, tzDate)

    return variantDate

def execute(cursor, sql):
    try:
        cursor.execute(sql)
    except pyodbc.ProgrammingError as err:
        response = {
            "status": False,
            "code": None,
            "description": "SQL Error: {}".format(re.sub('\[.*?\]', '', err.args[1]).strip()),
            "line": utils.lineNo()
        }
        return response
    return None
