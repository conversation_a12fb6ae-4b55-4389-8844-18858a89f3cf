import pyodbc
import clr
from dateutil import tz
import collections
from contextlib import contextmanager
from datetime import datetime
import time
import math
from . import utils
import re

# Importing from System will only work after import clr (i.e. only Windows).
# To explore how it works use ipython in the Windows VM.
from System import DateTime


def getConn(dsn):
    conn = pyodbc.connect(dsn)
    cursor = conn.cursor()
    return conn, cursor

# Rather user openConn instead of getConn
# because it automatically closes the connection:
# http://stackoverflow.com/a/3783305/639133
@contextmanager
def openConn(dsn, commit=False):
    connection = pyodbc.connect(dsn)
    cursor = connection.cursor()
    try:
        yield cursor
    except pyodbc.DatabaseError as err:
        error, = err.args
        cursor.execute("ROLLBACK")
        raise err
    else:
        if commit:
            cursor.execute("COMMIT")
    finally:
        connection.close()


def getSdk(config):
    clr.AddReference(config["sdkObjectName"])
    import Pastel.Evolution as sdk

    helper = sdk.ComHelper()

    # The command database might not use the default name
    if "sdkDbCommonName" in config:
        sdk.DatabaseContext.CreateCommonDBConnection(
            config["sdkDbCommonInstance"],
            config["sdkDbCommonName"],
            config["sdkDbCommonUsername"],
            config["sdkDbCommonPassword"],
            False
        )

    else:
        # Use the default common database.
        sdk.DatabaseContext.CreateCommonDBConnection(
            config["sdkDbInstance"],
            "EvolutionCommon",
            config["sdkDbUsername"],
            config["sdkDbPassword"],
            False
        )

    sdk.DatabaseContext.SetLicense(config["licenseSerialNo"], config["licenseAuthCode"])
    sdk.DatabaseContext.CreateConnection(
        config["sdkDbInstance"],
        config["sdkDbName"],
        config["sdkDbUsername"],
        config["sdkDbPassword"],
        False
    )

    return (sdk, helper)


def getPastelDate(dateObject):
    localDate = dateObject.astimezone(tz.tzlocal())
    return localDate.strftime("%d/%m/%Y")


def getCustomerAddress(config, source_customer_code=None, Customer=None,
                       property="PostalAddress"):
    """
    Get the customer address
    :param config:
    :param source_customer_code: Must be given if Customer is None
    :param Customer: Code not required if Customer is specified
    :param property: Must be either PostalAddress or DefaultDeliveryAddress
    :return:
    """

    # Try to lookup customer
    if Customer is None:
        try:
            sdk, helper = getSdk(config)
            Customer = sdk.Customer.GetByCode(source_customer_code)
            if Customer is None:
                raise Exception(
                    "Customer %s not found" % source_customer_code)

        except Exception as exc:
            response = {
                "status": False,
                "code": None,
                "description": str(exc)
            }
            return response

    if property == "PostalAddress":
        CustomerAddress = Customer.PostalAddress
    elif property == "DefaultDeliveryAddress":
        CustomerAddress = Customer.DefaultDeliveryAddress

    address = collections.OrderedDict()
    address["Line1"] = CustomerAddress.Line1
    address["Line2"] = CustomerAddress.Line2
    address["Line3"] = CustomerAddress.Line3
    address["Line4"] = CustomerAddress.Line4
    address["Line5"] = CustomerAddress.Line5
    address["PostalCode"] = CustomerAddress.PostalCode
    response = {
        "status": True,
        "address": address
    }

    return response


def iso8601ToTicks(isoDate):
    '''
    See discussion here: http://stackoverflow.com/a/14427911/639133
    :param isoDate:
    :return:
    '''
    order_date = datetime.strptime(isoDate[:19],"%Y-%m-%dT%H:%M:%S")
    unix_timestamp = time.mktime(order_date.timetuple())

    # Seconds between 0001-00-01 00:00:00 and 1970-01-01 01:00:00
    # unix_epoch = datetime.strptime("1970-01-01 01:00:00"[:19],"%Y-%m-%d %H:%M:%S")
    # c_epoch = datetime.strptime("0001-01-01 00:00:00"[:19],"%Y-%m-%d %H:%M:%S")
    # (unix_epoch - c_epoch).total_seconds() = 62135600400.0
    seconds_offset = 62135600400

    ticks = math.floor((unix_timestamp + seconds_offset) * 10**7)

    return ticks

def execute(cursor, sql):
    try:
        cursor.execute(sql)
    except pyodbc.ProgrammingError as err:
        response = {
            "status": False,
            "code": None,
            "description": "SQL Error: {}".format(re.sub('\[.*?\]', '', err.args[1]).strip()),
            "line": utils.lineNo()
        }
        return response
    return None
