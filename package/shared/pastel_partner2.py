# Use this version in combination with the PastelPartner.exe wrapper.

import pyodbc
import collections
import dateutil.parser
from dateutil import tz
import collections
import os
import subprocess
from contextlib import contextmanager
import sys
from ..shared import utils
import locale
import re


location = os.path.realpath(
    os.path.join(os.getcwd(), os.path.dirname(__file__)))

sdkReturnCodes = {
    "0": "General Ledger successfully updated",
    "1": "File not found",
    "2": "Invalid number of fields",
    "3": "Record update not successful",
    "4": "Record insert not successful",
    "5": "Record does not exist in file",
    "6": "Data path does not exist",
    "7": "Access denied",
    "9": "End of file",
    "10": "Field number specified not valid",
    "11": "Invalid period number (1 to 13)",
    "12": "Invalid date",
    "13": "Invalid account type (GDC)",
    "14": "Invalid general ledger account number",
    "15": "General ledger account contains sub accounts",
    "16": "General ledger account number must be numeric",
    "17": "Invalid customer account code",
    "18": "Invalid supplier account code",
    "19": "Invalid inventory item code",
    "20": "Invalid salesman code",
    "21": "Invalid job code",
    "22": "Invalid Tax Type (0 to 30)",
    "23": "Transaction amount cannot be less that the tax amount",
    "24": "Invalid open item transaction type - must be O (Original) or A (Allocation)",
    "25": "There cannot be more than 500 lines in a batch",
    "26": "Invalid account description",
    "27": "Default group needs to set up in Pastel",
    "28": "Invalid document line type – must be 2, 5, or 7",
    "29": "Invalid exclusive / inclusive – must be 0 or 1",
    "30": "Invalid Entry Type (1 to 90)",
    "31": "Duplicate inventory item",
    "32": "Invalid multi-store code",
    "33": "Invalid Currency Code",
    "99": "General Error (Normally due to long path names)",
    # For some API endpoints we return HTTP error codes,
    # luckily the SDK doesn't use these numbers internally.
    "409": "HTTP Conflict",
    "1000": "SDK wrapper error"
}

sdkDocumentTypesBatch = {
    "Quotation": "101",
    "Sales Order": "102",
    "Invoice": "103"
}

# We can add more column indices as we go along
sdkFiles = {
    "ACCMASD": {
        "Address1": 56,
        "Address2": 57,
        "Address3": 58,
        "Address4": 59,
        "Address5": 60
    },
    "ACCDELIV": {
        "Address1": 8,
        "Address2": 9,
        "Address3": 10,
        "Address4": 11,
        "Address5": 12
    }
}


def getSdkDescription(code, description):
    """
    Lookup description for given code,
    for wrapper error we echo back the description given.
    """
    code = code.strip()
    if code == "1000":
        return "%s: %s" % (sdkReturnCodes[code], description)
    elif code in sdkReturnCodes:
        return sdkReturnCodes[code]
    return ""


def getDocumentHeader(optional=False):
    dh = collections.OrderedDict()

    dh["Document Number"] = {"format": "8 characters maximum"}
    dh["Deleted"] = {"format": "Y=Deleted, <space>=Not deleted"}
    dh["Print Status"] = {"format": "Y=Printed, <space>=Not printed"}
    dh["Customer Code"] = {"format": "7 characters maximum"}
    dh["Date"] = {
        "format": "DD/MM/YYYY (Date defaults the period, unless it is not within the range of the year)"}
    dh["Order Number"] = {"format": "15 characters maximum"}
    dh["Inc/Exc"] = {"format": "Y=Inclusive, N=Exclusive"}
    dh["Discount"] = {"format": "Numeric: 4 characters"}
    dh["Invoice Message 1"] = {"format": "30 characters maximum"}
    dh["Invoice Message 2"] = {"format": "30 characters maximum"}
    dh["Invoice Message 3"] = {"format": "30 characters maximum"}
    dh["Delivery Address 1"] = {"format": "30 characters maximum"}
    dh["Delivery Address 2"] = {"format": "30 characters maximum"}
    dh["Delivery Address 3"] = {"format": "30 characters maximum"}
    dh["Delivery Address 4"] = {"format": "30 characters maximum"}
    dh["Delivery Address 5"] = {"format": "30 characters maximum"}
    dh["Sales Analysis Code"] = {"format": "5 characters maximum"}
    dh["Settlement Terms Code"] = {"format": "00-32"}
    dh["Job Code"] = {"format": "5 characters maximum"}
    dh["Closing Date"] = {
        "format": "DD/MM/YYYY, expiry date for quotes, and expected delivery date for orders"}
    dh["Telephone"] = {"format": "16 characters maximum"}
    dh["Contact"] = {"format": "16 characters maximum"}
    dh["Fax"] = {"format": "16 characters maximum"}
    dh["Exchange Rate"] = {"format": "Numeric: Exchange rate"}
    if optional:
        dh["Description"] = {"format": "40 characters"}
        dh["ExemptRef"] = {"format": "16 characters"}
        dh["Address 1"] = {"format": "30 characters"}
        dh["Address 2"] = {"format": "30 characters"}
        dh["Address 3"] = {"format": "30 characters"}
        dh["Address 4"] = {"format": "30 characters"}
        dh["Address 5"] = {"format": "30 characters"}
        dh["Ship / Deliver"] = {"format": "16 characters"}
        dh["Freight"] = {"format": "10 characters"}
        dh["On Hold"] = {"format": "Y=On hold, N=Not on hold"}

    return dh


def getDocumentLine():
    dl = collections.OrderedDict()

    dl["Cost Price"] = {"format": "Numeric: Unit cost price"}
    dl["Quantity"] = {"format": "Numeric: Line Quantity"}
    dl["Unit Selling Price"] = {
        "format": "Numeric: Exclusive selling price per unit"}
    dl["Inclusive Price"] = {
        "format": "Numeric: Inclusive selling price per unit"}
    dl["Unit"] = {"format": "4 characters maximum"}
    dl["Tax Type"] = {"format": "00-30"}
    dl["Discount Type"] = {
        "format": "0=None, 1=Settlement, 2=Overall, 3=Both"}
    dl["Discount Percentage"] = {
        "format": "4 characters, omit decimals, e.g. 12.5%=1250"}
    dl["Code"] = {
        "format": "15 characters maximum, inventory or GL"}
    dl["Description"] = {"format": "40 characters maximum"}
    dl["Line Type"] = {"format": "4=Inventory, 6=GL, 7=Remarks"}
    dl["Multi-store"] = {"format": "3 characters maximum"}
    dl["Cost Code"] = {"format": "5 characters maximum"}

    return dl


def getImportCustomer():
    ic = collections.OrderedDict()

    ic["Number"] = {"format": "6 characters maximum"}
    ic["Description"] = {"format": "40 characters maximum"}
    ic["Address 1"] = {"format": "30 characters maximum"}
    ic["Address 2"] = {"format": "30 characters maximum"}
    ic["Address 3"] = {"format": "30 characters maximum"}
    ic["Address 4"] = {"format": "30 characters maximum"}
    ic["Address 5"] = {"format": "30 characters maximum"}
    ic["Telephone"] = {"format": "16 characters maximum"}
    ic["Fax"] = {"format": "16 characters maximum"}
    ic["Contact"] = {"format": "16 characters maximum"}
    ic["Tax Exempt"] = {"format": "Y or N"}
    ic["Settlement Terms"] = {"format": "2 characters, '01' to '32'"}
    ic["Price List"] = {"format": "1 character maximum"}
    ic["Sales Analysis Code"] = {"format": "5 characters maximum"}
    ic["Delivery Address 1"] = {"format": "30 characters maximum"}
    ic["Delivery Address 2"] = {"format": "30 characters maximum"}
    ic["Delivery Address 3"] = {"format": "30 characters maximum"}
    ic["Delivery Address 4"] = {"format": "30 characters maximum"}
    ic["Delivery Address 5"] = {"format": "30 characters maximum"}
    ic["Blocked"] = {"format": "Y or N"}
    ic["Discount"] = {"format": "5 characters maximum e.g. '10.11'"}
    ic["Exclusive"] = {"format": "1 character maximum (Y or N)"}
    ic["Statements"] = {"format": "1 character maximum (0-2)"}
    ic["Open Item"] = {"format": "1 character maximum (Y or N)"}
    ic["Customer Category"] = {"format": "2 characters maximum"}
    ic["Currency Code"] = {"format": "00-16"}
    ic["Payment Terms"] = {"format": "00, 30, 60, 90, 12"}
    ic["Credit Limit"] = {"format": "7 characters maximum, no decimals"}
    ic["User Field 1"] = {"format": "16 characters maximum"}
    ic["User Field 2"] = {"format": "16 characters maximum"}
    ic["User Field 3"] = {"format": "16 characters maximum"}
    ic["User Field 4"] = {"format": "16 characters maximum"}
    ic["User Field 5"] = {"format": "16 characters maximum"}
    ic["MonthOrDay"] = {"format": "M=Month, D=Day"}
    ic["StatPrintOrEmail"] = {"format": "Integer: 1-4"}
    ic["DocPrintOrEmail"] = {"format": "Integer: 1-3"}
    ic["Cellphone"] = {"format": "Cellphone number, 16 characters maximum"}
    ic["Email"] = {"format": "Email Address, 50 characters maximum"}
    ic["Freight"] = {"format": "10 characters maximum"}
    ic["Ship"] = {"format": "16 characters maximum"}
    ic["Tax Reference"] = {"format": "16 characters maximum"}
    ic["Cash Customer"] = {"format": "1 character (Y or N)"}

    ic["ContactDocs"] = {"format": ""}
    ic["EmailDocs"] = {"format": ""}
    ic["ContactStatement"] = {"format": ""}
    ic["EmailStatement"] = {"format": ""}
    ic["Sole Prop"] = {"format": ""}
    ic["Company / Individual Name"] = {"format": ""}
    ic["Surname"] = {"format": ""}
    ic["ID Number"] = {"format": ""}
    ic["Account Holder"] = {"format": ""}
    ic["Bank Type"] = {"format": ""}
    ic["BankAccRelation"] = {"format": ""}
    ic["ThirdPartyID"] = {"format": ""}
    ic["Passport Number"] = {"format": ""}

    return ic


def getPipeString(items):
    values = []
    for key in items:
        val = ""
        if "val" in items[key]:
            val = items[key]["val"]
        values.append(utils.escapeSdkParam(str(val)))
    s = "|".join(values)
    return s


def getSdkCode(sdkResponse):
    values = sdkResponse[0].split("|")
    if len(values) == 1:
        return values[0], None
    return values[0], values[1]


def sdkSuccess(sdkCodes):
    # The sdkResponse might have one or more return codes,
    # the first code indicates whether the preceding call was a success.
    return sdkCodes[0].strip() == "0"


def getConn(dsn):
    conn = pyodbc.connect(dsn)
    cursor = conn.cursor()
    return conn, cursor


# Rather user openConn instead of getConn
# because it automatically closes the connection:
# http://stackoverflow.com/a/3783305/639133
@contextmanager
def openConn(dsn, commit=False):
    connection = pyodbc.connect(dsn)
    cursor = connection.cursor()
    try:
        yield cursor
    except pyodbc.DatabaseError as err:
        error, = err.args
        cursor.execute("ROLLBACK")
        raise err
    else:
        if commit:
            cursor.execute("COMMIT")
    finally:
        connection.close()


def getPastelDate(dateObject, leading_zero=True):
    localDate = dateObject.astimezone(tz.tzlocal())
    if leading_zero:
        return localDate.strftime("%d/%m/%Y")
    else:
        return localDate.strftime("%-d/%-m/%Y")


def getCustomer(dsn, source_customer_code):
    conn, cursor = getConn(dsn)
    sql = "select * from CustomerMaster where CustomerCode = '%s'" % source_customer_code
    cursor.execute(sql)
    row = cursor.fetchone()
    conn.close()
    return row


def createCustomer(config, source_customer_code, pipeString):
    sdkResponse = ""
    try:
        # Import customer
        params = ["ImportCustomer", pipeString]
        sdkResponse = execute(config, params)

    except Exception as exc:
        response = {
            "status": False,
            "code": None,
            "sdkResponse": sdkResponse,
            "description": str(exc),
            "line": utils.lineNo()
        }
        return response

    sdkCodes = getSdkCode(sdkResponse)
    if sdkSuccess(sdkCodes):
        response = {
            "status": True,
            "source_customer_code": source_customer_code,
            "line": utils.lineNo()
        }
    else:
        response = {
            "status": False,
            "code": sdkCodes[0],
            "sdkResponse": sdkResponse,
            "description": getSdkDescription(sdkCodes[0], sdkCodes[1]),
            "line": utils.lineNo()
        }

    return response


def getCustomerAddress(config, source_customer_code, file="ACCMASD"):
    """
    By default we read the postal address from the account master table.
    To get the delivery address pass in file="ACCDELIV"
    """
    sdkResponse = ""
    try:
        params = ["GetRecord", file, "0", source_customer_code]
        sdkResponse = execute(config, params)
        # Leaving the code below as an example of printing to cmd.exe
        # import locale
        # os_encoding = locale.getpreferredencoding()
        # print(str(sdkResponse).encode(os_encoding))

    except Exception as exc:
        response = {
            "status": False,
            "code": None,
            "sdkResponse": sdkResponse,
            "description": str(exc),
            "line": utils.lineNo()
        }
        return response

    sdkCodes = getSdkCode(sdkResponse)
    if sdkSuccess(sdkCodes):
        pipeString = str(sdkResponse)
        pipeArray = pipeString.split("|")
        index = sdkFiles[file]
        address = collections.OrderedDict()
        address["address_1"] = pipeArray[index["Address1"]]
        address["address_2"] = pipeArray[index["Address2"]]
        address["address_3"] = pipeArray[index["Address3"]]
        address["address_4"] = pipeArray[index["Address4"]]
        address["address_5"] = pipeArray[index["Address5"]]
        address["phone"] = ""

        response = {
            "status": True,
            "address": address,
            "line": utils.lineNo()
        }
    else:
        response = {
            "status": False,
            "code": sdkCodes[0],
            "sdkResponse": sdkResponse,
            "description": getSdkDescription(sdkCodes[0], sdkCodes[1]),
            "line": utils.lineNo()
        }

    return response

def execute(config, params = []):
    """
    Instead of using win32com we can make system calls to the SDK wrapper
    """
    prefix = []
    prefix.append(os.path.join(location, "PastelPartner.exe"))
    prefix.append(config["licenseDK"])
    prefix.append(config["licenseAuthCode"])
    prefix.append(config["pastelDataPath"])
    prefix.append(config["pastelGLPath"])
    args = prefix + params
    # print(" ".join(args))
    # utils.writeToLog(lines=['pastel.execute(args = "{}")'.format(args)])
    cmd  = subprocess.Popen(args, stdout=subprocess.PIPE)
    response = cmd.communicate()[0]

    os_encoding = locale.getpreferredencoding()
    # response = response.decode("utf-8")
    response = response.decode(os_encoding)

    response = response.replace('\n', ' ').replace('\r', '')
    # Return response inside a list to be consistent with existing code
    return [response]

def executeSQL(cursor, sql):
    try:
        cursor.execute(sql)
    except pyodbc.ProgrammingError as err:
        response = {
            "status": False,
            "code": None,
            "description": "SQL Error: {}".format(re.sub('\[.*?\]', '', err.args[1]).strip()),
            "line": utils.lineNo()
        }
        return response
    return None
