import collections
import decimal
import hashlib
import datetime
import decimal
import json
import time
import dateutil.parser
import os
from contextlib import contextmanager
import sqlite3
import re
import inspect
import base64

# urlparse was renamed between Python 2.7 and the expected Python 3.3.5
try:    # Python 3
    from urllib.request import urlopen
except ImportError:     # Python 2
    from urllib import urlopen

def getHash(s):
    md5 = hashlib.md5()
    md5.update(str.encode(s))
    return md5.hexdigest()


def dateFromISO8601(s):
    return dateutil.parser.parse(s)


def getTimestamp(ts=None, tsFormat="%Y-%m-%d %H:%M:%S"):
    if ts is None:
        ts = time.time()
    return datetime.datetime.fromtimestamp(ts).strftime(tsFormat)


@contextmanager
def openSqliteConn(dbPath, commit=False):
    connection = sqlite3.connect(dbPath)
    cursor = connection.cursor()
    try:
        yield cursor
    except sqlite3.DatabaseError as err:
        try:
            cursor.execute("ROLLBACK")
        except sqlite3.OperationalError:
            # Error occurred with commit=True but no transactions active,
            # nothing to roll back. Raise previous error
            pass
        raise err
    else:
        if commit:
            try:
                cursor.execute("COMMIT")
            except sqlite3.OperationalError as err:
                error, = err.args
                # Sometimes we specify commit=True but there might not be
                # any transactions that have to be committed, better way?
                if error != "cannot commit - no transaction is active":
                    raise err
    finally:
        connection.close()


@contextmanager
def openFile(filePath, mode="r"):
    f = open(filePath, mode)
    try:
        yield f
    except (OSError, IOError) as err:
        # Anything to do here?
        # http://stackoverflow.com/a/15032444/639133
        raise err
    else:
        pass
    finally:
        f.close()


def getRowsODBC(cursor, limit):
    """
    Useful when working with ODBC. Given a cursor this function will return a
    list with limit number of rows. Each row item is a dictionary keyed on
    columns as selected by the SQL query.
    """
    rows = []
    i = 0
    while i < limit:
        row = cursor.fetchone()
        if row is not None:
            # If we don't use on OrderedDict the keys are dumped to json
            # in arbitrary order, and that messes up hashing for changes.
            # The order of the keys in the cursor will correspond to the
            # order that was specified in the SQL query.
            rowData = collections.OrderedDict()
            index = 0
            for key in row.cursor_description:
                value = row[index]
                # http://stackoverflow.com/questions/16957275/python-to-json-serialization-fails-on-decimal
                if type(value) is str:
                    value = value.replace("\u0000", "")

                elif type(value) is decimal.Decimal:
                    value = float(value)

                elif type(value) is datetime.date:
                    value = value.isoformat()

                elif type(value) is datetime.datetime:
                    value = value.isoformat()

                elif isinstance(value, bytearray):
                    value = base64.b64encode(value)
                    value = value.decode("utf-8")

                if type(key[0]) is str:
                    key = key[0].lower()
                else:
                    key = key[0]

                rowData[key] = value
                index += 1
            rows.append(rowData)
        i += 1
    return rows


def runODBC(params, cursor):
    t1 = time.time()

    if "sql" not in params:
        response = {
            "status": False,
            "code": None,
            "description": "Required parameter missing"
        }
        return response

    if "limit" in params:
        limit = params["limit"]
    else:
        limit = 100

    try:
        cursor.execute(params["sql"])
        rows = getRowsODBC(cursor, limit)
    except Exception as inst:
        response = {
            "status": False,
            "code": None,
            "description": str(inst)
        }
        return response

    t2 = time.time()
    response = {
        "status": True,
        "code": None,
        "timer": str(t2 - t1),
        "data": {
            "rows": rows
        }
    }
    return response


def runSQLite(params, cursor):
    t1 = time.time()

    if "sql" not in params:
        response = {
            "status": False,
            "code": None,
            "description": "Required parameter missing"
        }
        return response

    try:
        cursor.execute(params["sql"])
    except Exception as inst:
        response = {
            "status": False,
            "code": None,
            "description": str(inst)
        }
        return response

    t2 = time.time()
    response = {
        "status": True,
        "code": None,
        "timer": str(t2 - t1),
        "data": {
            "rows": cursor.fetchall()
        }
    }
    return response


def echo(params):
    return {
        "status": True,
        "code": None,
        "data": params
    }


def getTime():
    return {
        "status": True,
        "code": None,
        "data": {
            "time": getTimestamp()
        }
    }


def log(params, location, relPath=None):
    t1 = time.time()

    n = 10
    if "n" in params:
        n = int(params["n"])

    if relPath is None:
        logFilePath = os.path.join(location, "..", "..", "..", "connector.log")
    else:
        logFilePath = os.path.join(location, relPath)

    try:
        f = open(os.path.join(location, logFilePath), "r")
    except FileNotFoundError:
        response = {
            "status": False,
            "description":
                "Log file not found at path: {}".format(logFilePath)
        }
        return response

    lines = tail(f, n)

    fileSize = str(
        round(os.path.getsize(logFilePath) / (1024 * 1024), 2)) + "MB"

    t2 = time.time()
    response = {
        "status": True,
        "code": None,
        "timer": str(t2 - t1),
        "data": {
            "size": fileSize,
            "lines": lines
        }
    }
    return response


# http://stackoverflow.com/a/692616/639133
def tail(f, n, offset=None):
    """Reads n lines from f with an offset of offset lines. The return
    value is a tuple in the form ``(lines, has_more)`` where `has_more` is
    an indicator that is `True` if there are more lines in the file.
    """
    avg_line_length = 74
    to_read = n + (offset or 0)

    while 1:
        try:
            f.seek(-(avg_line_length * to_read), 2)
        except IOError:
            # Oops. Apparently file is smaller than what we want
            # to step back, go to the beginning instead
            f.seek(0)
        pos = f.tell()
        lines = f.read().splitlines()
        if len(lines) >= to_read or pos == 0:
            return lines[-to_read:offset and -offset or None], \
                   len(lines) > to_read or pos > 0
        avg_line_length *= 1.3


def checkRequiredParams(keys, params):
    response = {
        "status": False,
        "code": 400
    }
    for key in keys:
        if key not in params:
            response["description"] = "Required parameter missing: %s" % key
            return response
    return None


def noneToString(d):
    """
    Shallow check given dictionary and convert None values to empty string
    """
    for key in d:
        if d[key] is None:
            d[key] = ""


# It would be ideal to use named parameters with pyodbc:
# https://www.python.org/dev/peps/pep-0249/#paramstyle
# Unfortunately, pyodbc currently only supports qmark.
def escapeSqlParam(param):
    """
    Strip unwanted characters from param: single quote
    """

    pattern = re.compile('[\']+', re.UNICODE)
    return pattern.sub('', param)


def bindSqlParams(sql, params):
    """
    The qmark interpolation used by pyodbc will convert the string param '001'
    to the integer 1 when used with some databases.

    For the convenience of using named and to get around issues like the one
    this sql parameter interpolation function can be used.

    Two types of params are supported: string and number,
    always wrap everything in single quotes so the template can be valid SQL,
    for example:

    sql = "select '%(my_string)s' as 'string', '%(my_number)n' as 'number'"
    params = {"my_string": "001", "my_number": 1}

    Use this function until pyodbc supports the pyformat param style:
    https://www.python.org/dev/peps/pep-0249/#paramstyle

    :param sql:
    :type sql: string
    :param params:
    :type params: dict
    :return:
    """
    singles = re.compile('[\']+', re.UNICODE)

    regex = "('\\%\\((\w+)\\)(\w)')"
    pattern = re.compile(regex, re.UNICODE)
    matches = pattern.finditer(sql)

    for match in matches:
        groups = match.groups()
        placeholder = groups[0]
        key = groups[1]
        varType = groups[2]

        if key not in params:
            raise Exception("Key {} not found in params".format(key))
        param = params[key]

        if varType == "n":
            if not (isinstance(param, int) or isinstance(param, float)):
                raise Exception("Number required for key {}, param is {}"
                                .format(key, type(param).__name__))

            sql = sql.replace(placeholder, str(param))

        elif varType == "s":
            if not isinstance(param, str):
                raise Exception("String required for key {}, param is {}"
                                .format(key, type(param).__name__))
            # String single quotes from param
            param = singles.sub("", param)
            sql = sql.replace(placeholder, "'{}'".format(param))

        else:
            raise Exception("Invalid placeholder {}".format(placeholder))

    return sql


def castSqlParam(sql, param, key):
    """
    Helper function for use with bindSqlParams,
    sometimes we want to query different column types depending on the client.
    Use castSqlParams(sql, param, "key") to search for "key" in the sql and
    return param cast to the type expected by the sql query
    """

    # Supported variable types: string => %(key)s, number => %(key)n,
    # update pattern when adding more types to bindSqlParams
    regex = '\%\(' + key + '\)[sn]{1}'
    pattern = re.compile(regex, re.UNICODE)
    matches = pattern.findall(sql)
    if len(matches) == 0:
        raise Exception("Key {} not used in sql".format(key))
    else:
        match = matches[0]

    uniqueMatch = [match]
    for match in matches[1:]:
        # If the variable name is used more than once in the query,
        # then it must always be specified as the same type
        if match not in uniqueMatch:
            raise Exception("Key {} has multiple types in sql".format(key))
        uniqueMatch.append(match)

    varType = match[-1:]
    return castSqlParamByType(param, varType)


def castSqlParamByType(param, varType):
    if varType == "n":
        try:
            return int(param)
        except ValueError:
            return float(param)

    elif varType == "s":
        return str(param)

    else:
        raise Exception("Unknown varType {}".format(varType))


def truncateString(s, length):
    return (s[:length]) if len(s) > length else s


def lineNo():
    """Returns the current line number in our program."""
    return inspect.currentframe().f_back.f_lineno


def escapeSdkParam(param):
    return param.replace("|", " ")


# Writes to the log file using the Go connector's "writeLog" endpoint
# This is just meant for debugging, so it just takes an array of lines to print to the
# log file
def writeToLog(lines):
    body = {
        "lines": lines
    }
    urlopen("http://127.0.0.1:1337/writeLog", json.dumps(body).encode('utf-8'))
    # TODO: Return response from writeLog here
    response = {
        "status": True
    }
    return json.dumps(response, indent=False)
