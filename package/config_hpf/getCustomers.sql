select *
from (
select
    row_number() over (order by ocrd.CardCode) as n,

    -- There must only be one result row per CardCode!
    ocrd.CardCode   as 'source_customer_code',
    ocrd.CardName   as 'card_name',
    ocrd.E_Mail     as 'email',

    case ocrd.U_U_S2sActive
        when 1 then 'true'
        else 'false'
    end             as "customer_active",

    1               as 'accepts_marketing',
    crd1.Street     as 'address.address1', -- or Address?
    ''              as 'address.address2',
    crd1.City       as 'address.city',
    crd1.Country    as 'address.country',
    'ZA'            as 'address.country_code',
    ''              as 'address.province',
    ''              as 'address.province_code',
    crd1.ZipCode    as 'address.zip',
    ''              as 'address.company',
    ocrd.CntctPrsn  as 'address.contact_person',
    ocrd.Phone1     as 'address.phone',

    -- Discount groups
    stuff(
    (select
    convert(varchar, ospg.ObjKey) + '|' + -- oitb.ItmsGrpCod
    convert(varchar, ospg.Discount) + ','
    from ospg
    where ospg.CardCode = ocrd.CardCode
    and ospg.ObjType = 52 -- Discount Groups
    and ospg.Discount > 0
    order by ospg.ObjKey
    for xml path('')),1,0,'')
                    as 'csv_discount_groups',
                    -- For customers

    ocrd.ListNum    as 'price_tier',
    ''              as 'qty_availability'

from ocpr WITH (NOLOCK)
INNER JOIN ocrd WITH (NOLOCK) on ocrd.CardCode=ocpr.CardCode
LEFT JOIN crd1 WITH (NOLOCK) on crd1.CardCode=ocpr.CardCode and crd1.Address = ocrd.ShipToDef and crd1.AdresType='S'
-- Only track customers that have a value for U_S2sActive
WHERE ocrd.U_U_S2sActive in (0, 1)
and ocrd.CardType = 'C' -- Customer
and ocrd.CardName not like '%(closed)'
and datalength(ocrd.E_mail) <> 0
and ocrd.E_Mail is not null

) as rows
where
n > '%(audit_lower_limit)n'
and n <= '%(audit_upper_limit)n'