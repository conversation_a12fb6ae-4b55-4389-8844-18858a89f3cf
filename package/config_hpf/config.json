{"dbName": "local.db", "dsn": "Driver={SQL Server Native Client 11.0};Server=localhost;Database=HPF;Uid=sa;Pwd=*********;", "dsnNoDb": "Driver={SQL Server Native Client 11.0};Server=localhost;Uid=sa;Pwd=*********;", "server": "**************", "companyDb": "HPF", "userName": "S2Shop", "password": "t2Y~zH", "dbServerType": "MSSQL2012", "dbUserName": "sa", "dbPassword": "*********", "licenseServer": "**************:30000", "audit_limit": 500, "push": {"source_id": 880, "limit": 100, "token": "68JSCJFDX1IJW8BJJ5TL2MVBORWVZP4L43LHNR9N", "writeLog": "http://localhost:1337/writeLog", "getProducts": "http://localhost:1337/sapone/getProductsBatch", "getCustomers": "http://localhost:1337/sapone/getCustomersBatch", "countProducts": "http://localhost:1337/sapone/countProducts", "countCustomers": "http://localhost:1337/sapone/countCustomers", "getMeta": "http://localhost:1337/sapone/getMeta", "setMeta": "http://localhost:1337/sapone/setMeta", "productsQueue": "https://app.stock2shop.com/v1/products/queue", "customersQueue": "https://app.stock2shop.com/v1/customers/queue"}}