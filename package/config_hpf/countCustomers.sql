-- Column must be named "count", it is used by push.exe
select count(*) as count
from ocpr WITH (NOLOCK)
INNER JOIN ocrd WITH (NOLOCK) on ocrd.CardCode=ocpr.CardCode
LEFT JOIN crd1 WITH (NOLOCK) on crd1.CardCode=ocpr.CardCode and crd1.Address = ocrd.ShipToDef and crd1.AdresType='S'
-- Only track customers that have a value for U_S2sActive
WHERE ocrd.U_U_S2sActive in (0, 1)
and ocrd.CardType = 'C' -- Customer
and ocrd.CardName not like '%(closed)'
and datalength(ocrd.E_mail) <> 0
and ocrd.E_Mail is not null
;