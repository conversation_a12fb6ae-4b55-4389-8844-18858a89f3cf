select *
from (
SELECT
    row_number() OVER (ORDER BY inv.StockCode)        AS n,

    inv.StockCode                                     AS "source_product_code",
    inv.StockCode                                     AS "source_variant_code",
    CASE ISNULL(inv2.WsProductdescripti, '')
      WHEN '' THEN inv.Description
      ELSE inv2.WsProductdescripti
    END                                               AS "title",
    CONCAT(
      CASE ISNULL(inv2.WsMaincategory, 'UnCategorized')
        WHEN '' THEN 'UnCategorized'
        ELSE ISNULL(inv2.WsMaincategory, 'UnCategorized')
      END,
      '>',
      CASE ISNULL(inv2.WsSubcategory, 'UnCategorized')
        WHEN '' THEN 'UnCategorized'
        ELSE ISNULL(inv2.WsSubcategory, 'UnCategorized')
      END
    )                                                 AS "collection",
    ''                                                AS "product_type",
    ISNULL(inv2.Brand, '')                            AS "vendor",
    ''                                                AS "variants.option1",
    ''                                                AS "variants.option2",
    ''                                                AS "variants.option3",
    inv.StockCode                                     AS "variants.sku",
    inv2.LongDetails                                  AS "body_html",
    CAST(ISNULL(inv2.WeightInPackaging, 0) * 1000 AS INTEGER)
                                                      AS "variants.weight",
    inv.LongDesc                                      AS "variants.barcode",
    CAST((wh1.QtyOnHand - wh1.QtyAllocated)AS INTEGER)
                                                      AS "variants.qty",
    Round(CAST(ISNULL(inv2.LeroyMerlinPrice, 0) AS FLOAT),2)
                                                      AS "variants.retail_price",
    'true'                                            AS "variants.inventory_management",

    CASE
        WHEN ISNULL(inv2.Stock2ShopStatus, '') = '1' THEN 'true'
        ELSE 'false'
    END                                               AS "product_active",

    ''                                                AS "tags",

    -- Prices
    'Contract Fixed Price'                            AS "default_price_tier",
    STUFF((
      SELECT
        ',' + CONVERT(VARCHAR, LTRIM(RTRIM(price_tiers.PriceCode))) + '|' +
              CONVERT(VARCHAR, CAST(ISNULL(price_tiers.SellingPrice, 0) AS FLOAT))
      FROM InvPrice price_tiers WITH (NOLOCK)
      WHERE price_tiers.StockCode = inv.StockCode
      ORDER BY LTRIM(RTRIM(price_tiers.PriceCode))
    for xml path('')),1,1,'')
                                                      AS "csv_price_tiers",

    CASE
      WHEN ISNULL(sor.FixedPrice, 0) = 0 THEN '0'
      ELSE CONVERT(VARCHAR, CAST(ISNULL(sor.FixedPrice, 0) AS FLOAT), 0)
    END                                               AS "variants.wholesale_price",

    '01'                                              AS "default_qty_availability",
    -- STUFF((
    --   SELECT
    --     ',' + CONVERT(VARCHAR, whse.Warehouse) + '|' +
    --           CONVERT(VARCHAR, CONVERT(INTEGER, COALESCE(whse.QtyOnHand, 0) - COALESCE(whse.QtyAllocated, 0)))
    --   FROM InvWarehouse whse WITH (NOLOCK)
    --   WHERE whse.StockCode = inv.StockCode
    --   ORDER BY whse.Warehouse
    -- for xml path('')),1,1,'')
    --                                                   AS "csv_qty_availability",

    
    ISNULL(inv2.WidthInPackaging, '0')                  AS "meta_width_cm",
    ISNULL(inv2.HeightInPackaging, '0')                 AS "meta_height_cm",
    ISNULL(inv2.LenghtInPackaging, '0')                 AS "meta_length_cm",
    inv2.LeroyMerlin                                  AS "meta_LeroyMerlin",
    ISNULL(inv2.WsSubcategory, '')                    AS "meta_SubCategory",
    ISNULL(inv2.WsMaincategory, '')                   AS "meta_MainCategory",
    ISNULL(inv2.OuterBoxQty, '0')                       AS "meta_outerboxqty",
    ISNULL(inv2.OuterHeight, '0')                       AS "meta_outerheight",
    ISNULL(inv2.OuterWidth, '0')                        AS "meta_outerwidth",
    ISNULL(inv2.OuterLength, '0')                       AS "meta_outerlength",
    ISNULL(inv2.OuterWeight, '0')                       AS "meta_outerweight",
    ISNULL(inv2.InnerBoxQty, '0')                       AS "meta_innerboxqty",
    ISNULL(inv2.InnerHeight, '0')                       AS "meta_innerheight",
    ISNULL(inv2.InnerWidth, '0')                        AS "meta_innerwidth",
    ISNULL(inv2.InnerLength, '0')                       AS "meta_innerlength",
    ISNULL(inv2.InnerWeight, '0')                       AS "meta_innerweight",
    ISNULL(inv2.WeightInPackaging, '0')                 AS "meta_weightinpackaging",
    ISNULL(inv2.HeightItemOnly, '0')                    AS "meta_heightitemonly",
    ISNULL(inv2.WidthItemOnly, '0')                     AS "meta_WidthItemOnly",
    ISNULL(inv2.LenghtItemOnly, '0')                    AS "meta_lenghtitemonly",
    ISNULL(inv2.WeightItemOnly, '0')                    AS "meta_weightitemonly",
    ISNULL(inv2.Brand,'')                             AS "meta_brand",
    ISNULL(inv2.Colour,'')                            AS "meta_colour",
    ISNULL(inv2.Made,'')                              AS "meta_made",
    ISNULL(inv2.Warranty,'')                          AS "meta_warranty"

FROM InvMaster inv WITH (NOLOCK)
    LEFT JOIN SorContractPrice sor WITH (NOLOCK)
      ON inv.StockCode = sor.StockCode
      AND sor.CustomerBuyGrp = '01'
      AND sor.ContractType = 'B'
      AND (DATEADD(day, DATEDIFF(day,0,GETDATE()),0) <= sor.ExpiryDate or sor.ExpiryDate is NULL)
      AND DATEADD(day, DATEDIFF(day,0,GETDATE()),0) >= sor.StartDate
      AND sor.PriceMethod = 'F'

    -- Meta data
    INNER JOIN [InvMaster+] inv2 WITH (NOLOCK)
      ON inv.StockCode = inv2.StockCode

    -- Main Warehouse
    INNER  JOIN InvWarehouse wh1 WITH (nolock)
      ON inv.StockCode = wh1.StockCode AND wh1.Warehouse = '01'

WHERE ISNULL(inv2.Stock2ShopStatus, '') IN ('1','2')
) as rows
where
n > '%(audit_lower_limit)n'
and n <= '%(audit_upper_limit)n'