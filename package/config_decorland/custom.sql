SELECT
    CASE SUBSTRING(RTRIM(LTRIM(LOWER(inv.StockCode))),1,2)
       WHEN 'dt'
           THEN CONCAT('DT ',inv.[Product Code])
           ELSE inv.[Product Code]
    END                                               AS "source_product_code",
    inv.StockCode                                     AS "source_variant_code",
    inv.[Product Code]                                AS "title",
    inv.Category                                      AS "collection",
    inv.Type                                          AS "product_type",
    inv.Brand                                         AS "vendor",
    'Size'                                            AS "variants.option1_name",
    inv.Size                                          AS "variants.option1_value",
    ''                                                AS "variants.option2",
    ''                                                AS "variants.option3",
    inv.StockCode                                     AS "variants.sku",
    ''                                                AS "body_html",
    inv.barcode                                       AS "variants.barcode",
    'normal_quantity'                                 AS "default_qty_availability",

-- CDD
    (
        SELECT cast(ISNULL(SUM(inv2.QtyAvailable), 0) AS INTEGER)
        FROM vw_LedaineEcomm inv2
        WHERE inv2.StockCode LIKE 'CDD%' 
        AND REPLACE(inv2.StockCode, 'CDD', '') = REPLACE(inv.StockCode, 'CDD', '')
        AND inv2.StockCode = inv.StockCode

    )                                                 AS "cdd_quantity", 

-- Normal
    (
        SELECT cast(ISNULL(SUM(inv2.QtyAvailable), 0) AS INTEGER)
        FROM vw_LedaineEcomm inv2
        WHERE inv2.StockCode = inv.StockCode
        AND inv2.StockCode NOT LIKE 'CDD%'
    )                                                 AS "normal_quantity",


    'retail_price'                                    AS "default_price_tier",
      CONCAT(
        'tal_price|',
        CONVERT(VARCHAR, ISNULL(TAL_price, 0)),
        ',tal_rrp|',
        CONVERT(VARCHAR, ISNULL(TAL_rrp, 0)),
        ',retail_price|',
        CONVERT(VARCHAR, ISNULL(SellingPrice, 0)),
        ',projectx_price|',
        CONVERT(VARCHAR, ISNULL(BXO_price, 0))
    )
                                                      AS "csv_price_tiers",
    'true'                                            AS "variants.inventory_management",

    CASE inv.ForEcommerceYN
    WHEN 'Y'
        THEN 'true'
        ELSE 'false'
    END                                               AS "product_active",

    CASE RTRIM(LTRIM(LOWER(inv.TAL_status)))
         WHEN 'active'
         THEN 1
    ELSE 0
    END                                               AS "variants.grams",
    ''                                                AS "tags",

    CASE inv.Company
    WHEN 'J'
        THEN 'true'
        ELSE 'false'
    END                                               AS "meta_sync_shopify",

    CASE inv.Company
    WHEN 'D'
        THEN 'true'
        ELSE 'false'
    END                                               AS "meta_sync_magento",

    inv.TAL_status                                    AS "meta_tal_status",
    inv.TAL_leadtime_days                             AS "meta_leadtime_days",
	ISNULL(inv.DecorlandOnline, 'FALSE')			  AS "meta_DecorlandOnline",
	ISNULL(inv.DecorDepotOnline, 'FALSE')			  AS "meta_DecorDepotOnline",
	ISNULL(inv.ImagineriumOnline, 'FALSE')			  AS "meta_ImagineriumOnline"

FROM [vw_LedaineEcomm] inv WITH (nolock)
WHERE inv.ForEcommerceYN in ('Y', 'N')
 and ISNULL(inv.[Product Code], '') <> ''
 and inv.StockCode in ('CDD16000047','16000047', 'CDD15000022','15000022','CDD15000017','15000017');
