import json
import imp
import time
import pyodbc
import re
from collections import OrderedDict
from .shared import utils
from .shared import odbc
from .controllers import base_push as base

class Api(base.Api):
    _configDir = "config_fashionfusion"

    def reload(self):
        imp.reload(base)
        base.Api.reload(self)
        response = {
            "status": True,
            "data": {
                "reload": True
            }
        }
        return json.dumps(response, indent=self._indent)

    def createOrder(self):
        t1 = time.time()

        self._setConfig()

        params = self._getParams()

        payload = self._payload()
        try:
            # Payload might be bytes, see connector.py
            payload = payload.decode("utf-8")
        except AttributeError:
            # Assume payload is already a decoded unicode string
            pass
        try:
            payload = json.loads(payload)
        except ValueError:
            response = {
                "status": False,
                "description": "Request body is not valid json",
                "line": utils.lineNo()
            }
            return json.dumps(response, indent=self._indent)

        # Add params to order
        # We don't want to add all params to the order as there may be some sensitive information
        orderParams = OrderedDict()
        paramsToKeep = ['storeNum']
        for key, value in params.items():
            if key in paramsToKeep:
                orderParams[key] = value

        if orderParams:
            payload['params'] = orderParams

        s2sOrder = json.dumps(payload)

        with odbc.openConn(self._config["dsn2"], True) as cursor:
            # Sanitising user input
            # Note: simply using 'insert into ... ('%s');" % (s2sOrder, )' is supposed to sanitise the input,
            # but for some reason results in an error when single quote is included in the order.
            # So we just replace the single quotes
            s2sOrder = s2sOrder.replace("'", "''")
            sql = "INSERT INTO vex_s2s_sale_json (json_sale) VALUES ('%s');" % (s2sOrder, )
            response = odbc.execute(cursor, sql)
            if response is not None:
                return json.dumps(response, indent=self._indent)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {
                "source_order_code": "N/A",
                "source_customer_code": "N/A"
            },
        }
        return json.dumps(response, indent=self._indent)