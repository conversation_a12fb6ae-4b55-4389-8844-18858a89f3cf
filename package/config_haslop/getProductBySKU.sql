SELECT
  row_number() over (order by st.StockLink) as n,

  LTRIM(RTRIM(st.ucIIMasterSKU))                                       AS "source_product_code",
  st.<PERSON>                                                              AS "source_variant_code",
  LTRIM(RTRIM(st.ucIIProdTitle))                                       AS "title",
  LTRIM(RTRIM(st.ulIICategory))                                        AS "collection",
  LTRIM(RTRIM(st.uliitype))                                            AS "product_type",
  LTRIM(RTRIM(st.uliisupplier))                                        AS "vendor",
  st.Code                                                              AS "variants.sku",
  LTRIM(RTRIM(st.uciisel1desc))                                        AS "variants.option1_name",
  LTRIM(RTRIM(st.ucIISelection1))                                      AS "variants.option1_value",
  LTRIM(RTRIM(st.uciisel2desc))                                        AS "variants.option2_name",
  LTRIM(RTRIM(st.ucIISelection2))                                      AS "variants.option2_value",
  LTRIM(RTRIM(st.uciisel3desc))                                        AS "variants.option3_name",
  LTRIM(RTRIM(st.ucIISelection3))                                      AS "variants.option3_value",
  ''                                                                   AS "body_html",
  0                                                                    AS "variants.weight",
  LTRIM(RTRIM(st.ucIIVarDesc))                                         AS "variants.barcode",
  'true'                                                               AS "variants.inventory_management",
  CASE LTRIM(RTRIM(LOWER(st.ulIIS2S)))
    WHEN 'active' THEN 'true'
    ELSE 'false'
  END                                                                  AS "product_active",
  ''                                                                   AS "tags",

  'Price List 1_incl'                                                  AS "default_price_tier",
  stuff(
    (select
      ',' + convert(varchar, price_names.cName) + '_excl|' +
            convert(varchar, price.fExclPrice)
      + ',' + convert(varchar, price_names.cName) + '_incl|' +
            convert(varchar, price.fInclPrice)
    from dbo._etblPriceListPrices price WITH (NOLOCK)

      inner join _etblPriceListName price_names WITH (NOLOCK)
        ON price.iPriceListNameID = price_names.IDPriceListName AND price.iStockID = st.StockLink and price.iWarehouseID = 0

    order by price_names.cName
    for xml path('')),1,1,'')
                                                                       AS "csv_price_tiers",


  --CAST(ISNULL(st.Qty_On_Hand - st.QtyOnSO, 0) AS INTEGER)              AS "variants.qty",
  'Mstr'                                                               AS "default_qty_availability",
  STUFF((
    SELECT
      CONVERT(VARCHAR, whse_mst.Code) + '|' +
      CONVERT(VARCHAR, ISNULL(whse.WHQtyOnHand - whse.WHQtyOnSO, 0)) + ','
    FROM WhseStk whse WITH (NOLOCK)

      LEFT JOIN WhseMst whse_mst WITH (NOLOCK)
        ON whse_mst.WhseLink = whse.WHWhseID

    WHERE whse.WHStockLink = st.StockLink
    ORDER BY whse_mst.Code
    for xml path('')),1,0,'')
                                                                      AS "csv_qty_availability",

  -- List of images for this product
  -- Enables automatic pulling of new images when image is added
  ISNULL(
  STUFF(
    (SELECT ',' + convert(varchar, i.idInvImage)
    FROM _etblInvImages i WITH (NOLOCK)
    WHERE st.StockLink = i.iStockLink
    ORDER BY i.idInvImage
    FOR XML PATH ('')), 1, 1, ''), '')
                                                                       AS "meta_image_id_csv",
  LTRIM(RTRIM(st.ucIIShortDesc))                                       AS "meta_shortDesc"


FROM StkItem st WITH (NOLOCK)

  -- General info

WHERE st.Code = '%(sku)s'

;