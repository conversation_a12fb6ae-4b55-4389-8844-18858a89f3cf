select
  inv.OrderNum                                                        as 'source_order_code',
  c.Account                                                           as 'source_client_code',
  inv.OrderNum                                                        as 'id',
  inv.Message1                                                        as 'notes1',
  inv.Message2                                                        as 'notes2',
  inv.Message3                                                        as 'notes3',
  substring(c.Name, 1, charindex(' ', c.Name) - 1)                    as 'customer.first_name',
  substring(c.Name, charindex(' ', c.Name) + 1, 1000)                 as 'customer.last_name',
  c.EMail                                                             as 'customer.email',

  inv.PAddress1                                                       as 'billing_address.address1',
  inv.PAddress3                                                       as 'billing_address.address2',
  inv.PAddress4                                                       as 'billing_address.city',
  inv.PAddress2                                                       as 'billing_address.company',
  inv.PAddress5                                                       as 'billing_address.country',
  'ZA'                                                                as 'billing_address.country_code',
  substring(inv.PAddress1, 1, charindex(' ', inv.PAddress1) - 1)      as 'billing_address.first_name',
  substring(inv.PAddress1, charindex(' ', inv.PAddress1) + 1, 1000)   as 'billing_address.last_name',
  c.Telephone                                                         as 'billing_address.phone',
  ''                                                                  as 'billing_address.province',
  inv.PAddress6                                                       as 'billing_address.zip',

  inv.Address1                                                       as 'shipping_address.address1',
  inv.Address3                                                       as 'shipping_address.address2',
  inv.Address4                                                       as 'shipping_address.city',
  inv.Address2                                                       as 'shipping_address.company',
  inv.Address5                                                       as 'shipping_address.country',
  'ZA'                                                               as 'shipping_address.country_code',
  substring(inv.Address1, 1, charindex(' ', inv.Address1) - 1)       as 'shipping_address.first_name',
  substring(inv.Address1, charindex(' ', inv.Address1) + 1, 1000)    as 'shipping_address.last_name',
  c.Telephone                                                        as 'shipping_address.phone',
  ''                                                                 as 'shipping_address.province',
  inv.Address6                                                       as 'shipping_address.zip',

  s.Code                                                             as 'line_item.sku',
  il.cDescription                                                    as 'line_item.title',
  il.fUnitPriceExcl                                                  as 'line_item.price',
  il.fQuantity                                                       as 'line_item.qty',
  'item'                                                             as 'line_item.code',
  (il.fQuantity * il.fUnitPriceIncl)
  - (il.fQuantity * il.fUnitPriceExcl)                               as 'tax_line.price',
  '14'                                                               as 'tax_line.rate',
  'VAT'                                                              as 'tax_line.title',
  'taxed'                                                            as 'tax_line.code'
from InvNum inv
join Client c on inv.AccountID = c.DCLink
join _btblInvoiceLines il on il.iInvoiceID = inv.AutoIndex
join StkItem s on s.StockLink = il.iStockCodeID
where OrderNum = '%(source_order_code)s';