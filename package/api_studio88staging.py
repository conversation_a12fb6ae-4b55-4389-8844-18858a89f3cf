import json
import imp
import time
import requests
from .shared import utils
from .controllers import base_push as base
from collections import OrderedDict

class Api(base.Api):
    _configDir = "config_studio88staging"

    def reload(self):
        imp.reload(base)
        base.Api.reload(self)
        response = {
            "status": True,
            "data": {
                "reload": True
            }
        }
        return json.dumps(response, indent=self._indent)

    def _getDictOrEmptyString(self, row, key):
        if key in row:
            return row[key]
        return ""

    def _isInteger(self, n):
        try:
            float(n)
        except ValueError:
            return False
        else:
            return float(n).is_integer()

    def _afterProductTransform(self, params, result, source_product):
        import datetime
        if 'meta_special_price' in result:
            special_price = float(result['meta_special_price'])
            if special_price == 0:
                special_price = float(source_product["product"]["variants"]["price"])
        else:
            special_price = float(source_product["product"]["variants"]["price"])
        for meta in source_product["product"]['meta']:
            if meta['key'] == 'special_price_start' or meta['key'] == 'special_price_end':
                if len(str(meta['value'])) == 8:
                    temp_date = datetime.datetime.strptime(str(meta['value']), "%Y%m%d")
                    temp_date = temp_date.strftime('%m/%d/%Y')
                    meta["value"] = temp_date
            if meta['key'] == 'special_price':
                meta["value"] = str(special_price)
        return source_product

    def createOrder(self):
        t1 = time.time()

        self._setConfig()

        params = self._getParams()

        payload = self._payload()
        try:
            # Payload might be bytes, see connector.py
            payload = payload.decode("utf-8")
        except AttributeError:
            # Assume payload is already a decoded unicode string
            pass
        try:
            payload = json.loads(payload)
        except ValueError:
            response = {
                "status": False,
                "description": "Request body is not valid json",
                "line": utils.lineNo()
            }
            return json.dumps(response, indent=self._indent)

        s2sOrder = payload["system_order"]
        source = payload["sources"][0]

        # source customer code
        if "source_customer_code" in source and source["source_customer_code"]:
            source_customer_code = source["source_customer_code"]
        else:
            if "default_customer_code" in params:
                source_customer_code = params["default_customer_code"]
        if source_customer_code is None:
            raise Exception("source_customer_code missing and no default_customer_code set")

        # request payload
        
        url = "http://s88-api-01/Sage300WebApi/V1.0/-/88GER/OE/OEOrders"

        sage_order = {
            "CustomerNumber": source_customer_code,
            # "CustomerGroupCode": "ONLINE",
            "PostInvoice": True,
            "InvoiceWillBeProduced": True,
            "PerformShipAll": True,
            "BillToName": self._getDictOrEmptyString(s2sOrder["billing_address"], "company"),
            "BillToAddressLine1": self._getDictOrEmptyString(s2sOrder["billing_address"], "address1"),
            "BillToAddressLine2": self._getDictOrEmptyString(s2sOrder["billing_address"], "address2"),
            # "BillToAddressLine3": "",
            # "BillToAddressLine4": "",
            "BillToCity": self._getDictOrEmptyString(s2sOrder["billing_address"], "city"),
            "BillToStateProvince": self._getDictOrEmptyString(s2sOrder["billing_address"], "province"),
            "BillToZipPostalCode": self._getDictOrEmptyString(s2sOrder["billing_address"], "zip"),
            "BillToCountry": self._getDictOrEmptyString(s2sOrder["billing_address"], "country"),
            "BillToPhoneNumber": self._getDictOrEmptyString(s2sOrder["billing_address"], "phone"),
            # "BillToFaxNumber": "",
            "BillToContact": self._getDictOrEmptyString(s2sOrder["billing_address"], "first_name") + " " + self._getDictOrEmptyString(s2sOrder["billing_address"], "last_name"),
            "BillToEmail": self._getDictOrEmptyString(s2sOrder["customer"], "email"),
            # "BillToContactPhone": "",
            # "BillToContactFax": "",
            # "BillToContactEmail": "",
            # "ShipToLocationCode": "",
            "ShipToName": self._getDictOrEmptyString(s2sOrder["shipping_address"], "company"),
            "ShipToAddressLine1": self._getDictOrEmptyString(s2sOrder["shipping_address"], "address1"),
            "ShipToAddressLine2": self._getDictOrEmptyString(s2sOrder["shipping_address"], "address2"),
            # "ShipToAddressLine3": "",
            # "ShipToAddressLine4": "",
            "ShipToCity": self._getDictOrEmptyString(s2sOrder["shipping_address"], "city"),
            "ShipToStateProvince": self._getDictOrEmptyString(s2sOrder["shipping_address"], "province"),
            "ShipToZipPostalCode": self._getDictOrEmptyString(s2sOrder["shipping_address"], "zip"),
            "ShipToCountry": self._getDictOrEmptyString(s2sOrder["shipping_address"], "country"),
            "ShipToPhoneNumber": self._getDictOrEmptyString(s2sOrder["shipping_address"], "phone"),
            # "ShipToFaxNumber": "",
            "ShipToContact": self._getDictOrEmptyString(s2sOrder["shipping_address"], "first_name") + " " + self._getDictOrEmptyString(s2sOrder["shipping_address"], "last_name"),
            "ShipToEmail": self._getDictOrEmptyString(s2sOrder["customer"], "email"),
            # "ShipToContactPhone": "",
            # "ShipToContactFax": "",
            # "ShipToContactEmail": "",
            "PurchaseOrderNumber": self._getDictOrEmptyString(s2sOrder, "channel_order_code"),
            "OrderReference": self._getDictOrEmptyString(s2sOrder, "channel_order_code"),
            "OrderType": "Active",
            "DefaultLocationCode": params["location_code"],
            "OrderDescription": self._getDictOrEmptyString(s2sOrder, "channel_order_code"),
            "OrderComment": "ONLINE SALE " + self._getDictOrEmptyString(s2sOrder, "notes"),
            "OrderHomeCurrency": "ZAR",
            "OrderSourceCurrency": "ZAR",
            "TaxGroup": "RSA",
            "OrderDetails": []
        }

        # line items
        for item in s2sOrder["line_items"]:
            line_item = {
                "LineType": "Item",
                "Item": item["source_variant_code"],
                "Location": params["location_code"],
                "QuantityOrdered": item["qty"],
                "QuantityShipped": item["qty"],
                "OrderUnitOfMeasure": "EA",
                "OrderUnitPrice": float(item["price"]) * 1.15,
                "PricingUnitPrice": float(item["price"]) * 1.15,
                "PriceOverride": True
            }
            sage_order["OrderDetails"].append(line_item)

        # shipping added as line item
        if "shipping_code" in params:
            for item in s2sOrder["shipping_lines"]:
                line_item = {
                    "LineType": "Item",
                    "Item": params["shipping_code"],
                    "Location": params["location_code"],
                    "QuantityOrdered": item["qty"],
                    "QuantityShipped": item["qty"],
                    "OrderUnitOfMeasure": "EA",
                    "OrderUnitPrice": float(item["price"]) * 1.15,
                    "PricingUnitPrice": float(item["price"]) * 1.15,
                    "PriceOverride": True
                }
                sage_order["OrderDetails"].append(line_item)

        json_data = json.dumps(sage_order)
        # write out response data to file
        text_file = open("output2.txt", "w")
        text_file.write(json.dumps(sage_order))
        text_file.close() 
        headers = {'Content-Type': 'application/json'}
        auth = (params["username"], params["password"])
        r = requests.post(url, params=params, data=json_data, auth=auth, headers=headers)
        # write out response data to file
        text_file = open("output.txt", "w")
        text_file.write(json.dumps(r.json()))
        text_file.close()
        if r.status_code > 204:
            response = {
                "status": False,
                "code": r.status_code,
                "description": r.text,
                "line": utils.lineNo()
            }
            return response
        sage_response = r.json()
        t2 = time.time()
        if "OrderNumber" not in sage_response or "CustomerNumber" not in sage_response:
            response = {
                "status": False,
                "code": r.status_code,
                "description": r.text,
                "line": utils.lineNo()
            }
            return response
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {
                "source_order_code": sage_response["OrderNumber"],
                "source_customer_code": sage_response["CustomerNumber"]
            },
        }
        return json.dumps(response, indent=self._indent)
