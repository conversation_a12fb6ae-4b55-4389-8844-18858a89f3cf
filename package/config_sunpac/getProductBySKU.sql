SELECT
    RTRIM(LTRIM(inv.StockCode))                       AS "source_product_code",
    RTRIM(LTRIM(inv.StockCode))                       AS "source_variant_code",
    RTRIM(LTRIM(inv.LongDesc))                        AS "title",
    CONCAT(
        RTRIM(LTRIM(ISNULL(inv2.Dept, ''))),
        '>',
        RTRIM(LTRIM(ISNULL(inv2.SubCategory, ''))),
        '>',
        RTRIM(LTRIM(ISNULL(inv2.ProductClassificat, ''))),
        '>',
        RTRIM(LTRIM(ISNULL(inv2.Franchise, '')))
    )                                                 AS "collection",
    ''                                                AS "product_type",
    ''                                                AS "vendor",
    RTRIM(LTRIM(inv.StockCode))                       AS "variants.sku",
    ''                                                AS "body_html",
    CAST(ISNULL(inv.Mass, 0) AS INTEGER)              AS "variants.weight",
    ''                                                AS "variants.barcode",
    CASE
       WHEN LOWER(RTRIM(ISNULL(inv2.Onlineactive, ''))) = 'active' THEN 'true'
        ELSE 'false'
    END                                               AS "product_active",
    'true'                                            AS "variants.inventory_management",
    ''                                                AS "tags",

    -- Prices
    'A'                                               AS "default_price_tier",
    STUFF((
      SELECT
        ',' + CONVERT(VARCHAR, LTRIM(RTRIM(price_tiers.PriceCode))) + '|' +
              CONVERT(VARCHAR, CAST(ISNULL(price_tiers.SellingPrice, 0) AS FLOAT))
      FROM InvPrice price_tiers WITH (NOLOCK)
      WHERE price_tiers.StockCode = inv.StockCode
      ORDER BY LTRIM(RTRIM(price_tiers.PriceCode))
    for xml path('')),1,1,'')
                                                      AS "csv_price_tiers",

    'CT'                                              AS "default_qty_availability",
    STUFF((
      SELECT
        ',' + CONVERT(VARCHAR, LTRIM(RTRIM(whse.Warehouse))) + '|' +
              CONVERT(VARCHAR, CONVERT(INTEGER, COALESCE(whse.QtyOnHand, 0) - COALESCE(whse.QtyAllocated, 0)))
      FROM InvWarehouse whse WITH (NOLOCK)
      WHERE whse.StockCode = inv.StockCode
      ORDER BY LTRIM(RTRIM(whse.Warehouse))
    for xml path('')),1,1,'')
                                                      AS "csv_qty_availability",

    -- If FixedPriceCode is set, use discount 1
    STUFF((
      SELECT
      ',' + CONVERT(VARCHAR, LTRIM(RTRIM(sor.CustomerBuyGrp))) + '|' +
      CASE WHEN (DATEADD(day, DATEDIFF(day,0,GETDATE()),0) <= ExpiryDate or ExpiryDate is NULL) AND DATEADD(day, DATEDIFF(day,0,GETDATE()),0) >= StartDate
        THEN CONVERT(VARCHAR, LTRIM(RTRIM(sor.FixedPriceCode))) + '-' + CONVERT(VARCHAR, COALESCE(sor.Discount1, 0))
        ELSE '0-0'
      END
      FROM SorContractPrice sor WITH (NOLOCK)
      WHERE sor.StockCode = inv.StockCode
      AND sor.ContractType='B'
      AND sor.PriceMethod in ('P', 'C')
      ORDER BY LTRIM(RTRIM(sor.CustomerBuyGrp))
    for xml path('')),1,1,'')
                                                      AS "csv_group_code",

    -- If FixedPriceCode is empty, uses fixedPrice
    STUFF((
      SELECT
      ',' + CONVERT(VARCHAR, LTRIM(RTRIM(sor.CustomerBuyGrp))) + '|' +
      CASE WHEN (DATEADD(day, DATEDIFF(day,0,GETDATE()),0) <= ExpiryDate or ExpiryDate is NULL) AND DATEADD(day, DATEDIFF(day,0,GETDATE()),0) >= StartDate
        THEN CONVERT(VARCHAR, COALESCE(sor.FixedPrice, 0))
        ELSE '0'
      END
      FROM SorContractPrice sor WITH (NOLOCK)
      WHERE sor.StockCode = inv.StockCode
      AND sor.ContractType='B'
      AND sor.PriceMethod='F'
      ORDER BY LTRIM(RTRIM(sor.CustomerBuyGrp))
    for xml path('')),1,1,'')
                                                      AS "csv_group_code_fixed"

FROM InvMaster inv WITH (NOLOCK)

    -- Base Data
    LEFT JOIN [InvMaster+] inv2 WITH (nolock)
    ON inv.StockCode = inv2.StockCode

where RTRIM(LTRIM(inv.StockCode)) = '%(sku)s';