SELECT *
FROM (
SELECT
  row_number() over (order by st.StockLink) as n,

  st.ucIIProductCode                                                   AS "source_product_code",
  st.StockLink                                                         AS "source_variant_code",
  st.ucIIWebTitle                                                      AS "title",
  'n/a'                                                                AS "collection",
  'n/a'                                                                AS "product_type",
  'n/a'                                                                AS "vendor",
  st.Code                                                              AS "variants.sku",
  'Selection'                                                          AS "variants.option1_name",
  st.ucIIProductOption                                                 AS "variants.option1_value",
  ''                                                                   AS "variants.option2",
  ''                                                                   AS "variants.option3",
  ''                                                                   AS "body_html",
  0                                                                    AS "variants.weight",
  (
    SELECT top 1 bc.Barcode
    FROM _etblBarcodes bc WITH (NOLOCK)
    WHERE bc.StockID = st.StockLink and bc.WhseID = 0
  )                                                                    AS "variants.barcode",
  'RRP'                                                       AS "default_price_tier",
  stuff(
    (select
      ',' + convert(varchar, price_names.cName) + '|' +
            convert(varchar, CAST(price.fExclPrice AS DECIMAL(10,2))) +
      ',' + convert(varchar, price_names.cName) + '_incl|' +
            convert(varchar, CAST(price.fInclPrice AS DECIMAL(10,2)))
    from dbo._etblPriceListPrices price WITH (NOLOCK)

      inner join _etblPriceListName price_names WITH (NOLOCK)
        ON price.iPriceListNameID = price_names.IDPriceListName AND price.iStockID = st.StockLink and price.iWarehouseID = 0

    order by price_names.cName
    for xml path('')),1,1,'')
                                                                       AS "csv_price_tiers",

  'Mstr'                                                               AS "default_qty_availability",
  STUFF((
            SELECT
                    CONVERT(VARCHAR, whse_mst.Code) + '|' +
                    CONVERT(VARCHAR, ISNULL(sq.QtyOnHand - sq.QtyOnSO - sq.QtyReserved, 0)) + ','
            FROM (
                     SELECT ssq.StockID, ssq.WhseID,
                            SUM(QtyOnHand) AS QtyOnHand,
                            SUM(QtyOnSO) AS QtyOnSO,
                            Sum(QtyReserved) AS QtyReserved
                     FROM _etblStockQtys ssq
                     GROUP BY ssq.StockID, ssq.WhseID
                 ) sq

                     LEFT JOIN WhseMst whse_mst WITH (NOLOCK)
                               ON whse_mst.WhseLink = sq.WhseID
            WHERE sq.StockID = st.StockLink
            ORDER BY whse_mst.Code
            for xml path('')),1,0,'')
                                                                       AS "csv_qty_availability",
  'true'                                                               AS "variants.inventory_management",

  CASE
    WHEN LOWER(LTRIM(RTRIM(ISNULL(st.ulIIONLINE, '')))) = 'delete' THEN 'false'
    ELSE 'true'
  END                                                                  AS "product_active",

  ''                                                                   AS "tags",

  -- List of images for this product
  -- Enables automatic pulling of new images when image is added
  ISNULL(
  STUFF(
    (SELECT ',' + convert(varchar, i.idInvImage)
    FROM _etblInvImages i WITH (NOLOCK)
    WHERE st.StockLink = i.iStockLink
    ORDER BY i.idInvImage
    FOR XML PATH ('')), 1, 1, ''), '')
                                                                       AS "meta_image_id_csv"

FROM StkItem st WITH (NOLOCK)

WHERE LOWER(LTRIM(RTRIM(ISNULL(st.ulIIONLINE, '')))) IN ('active', 'delete') AND RTRIM(ISNULL(st.ucIIProductCode, '')) <> ''

) AS rows
WHERE n > '%(audit_lower_limit)n' AND n <= '%(audit_upper_limit)n';