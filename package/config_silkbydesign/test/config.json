{"dbName": "local.db", "dsn": "DRIVER=Firebird/InterBase(r) driver;UID=SYSDBA;PWD=masterkey;DBNAME=localhost:C:\\Omni\\SA Demo\\Data\\SA_DEMO_COMPANY.FDB;", "audit_limit": 1000, "omni_api": {"company_name": "SA Example Company Demo", "user_name": "Admin", "password": "", "url": "http://127.0.0.1:8080/"}, "push": {"source_id": 99999, "limit": 500, "token": "--- Insert S2S client cron token here --", "writeLog": "http://localhost:1337/writeLog", "getProducts": "http://localhost:1337/omniaccounts/getProductsBatch", "getCustomers": "http://localhost:1337/omniaccounts/getCustomersBatch", "countProducts": "http://localhost:1337/omniaccounts/countProducts", "countCustomers": "http://localhost:1337/omniaccounts/countCustomers", "getMeta": "http://localhost:1337/omniaccounts/getMeta", "setMeta": "http://localhost:1337/omniaccounts/setMeta", "productsQueue": "https://app.stock2shop.com/v1/products/queue", "customersQueue": "https://app.stock2shop.com/v1/customers/queue"}}