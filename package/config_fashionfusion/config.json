{"dbName": "local.db", "dsn": "Driver={SQL Server Native Client 11.0};Server=**********;Database=RMS;Uid=s2s;Pwd=*************$RK;", "dsn2": "Driver={SQL Server Native Client 11.0};Server=**********;Database=INT;Uid=s2s;Pwd=*************$RK;", "audit_limit": 1000, "push": {"source_id": 1280, "limit": 500, "token": "NUCFJTETTDCXYJEUI1JJDHEMMPYIP8HVQC7UUOPG", "writeLog": "http://localhost:13337/writeLog", "getProducts": "http://localhost:13337/odbc/getProductsBatch", "getCustomers": "http://localhost:13337/odbc/getCustomersBatch", "countProducts": "http://localhost:13337/odbc/countProducts", "countCustomers": "http://localhost:13337/odbc/countCustomers", "getMeta": "http://localhost:13337/odbc/getMeta", "setMeta": "http://localhost:13337/odbc/setMeta", "productsQueue": "https://app.stock2shop.com/v1/products/queue", "customersQueue": "https://app.stock2shop.com/v1/customers/queue"}}