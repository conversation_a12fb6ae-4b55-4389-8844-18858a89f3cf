-- Column must be named "count", it is used by push.exe
select count(*) as count
from ocpr WITH (NOLOCK)
INNER JOIN ocrd WITH (NOLOCK) on ocrd.CardCode=ocpr.CardCode
LEFT JOIN crd1 WITH (NOLOCK) on crd1.CardCode=ocpr.CardCode and crd1.Address = ocrd.ShipToDef and crd1.AdresType='S'
where 1 = 1
and (
    ocrd.U_S2sActive = 1 or
    ocrd.U_S2sActive = 0
)
and ocrd.CardType = 'C'
and ocrd.CardName not like '%(closed)'
and datalength(ocpr.E_MailL) <> 0
and ocpr.E_MailL is not null
and ocpr.FirstName is not null
and ocpr.LastName is not null
;