select *
from (
select
    row_number() over (order by oitm.ItemCode) as n,

    -- There must only be one result row per ItemCode!
    oitm.ItemCode               as "source_product_code",
    oitm.ItemCode               as "source_variant_code",
    oitm.ItemName               as "title",
    oitb.ItmsGrpNam             as "collection",
    (select top 1 ufd1.descr from ufd1 WITH (NOLOCK)
    where ufd1.tableid = 'oitm'
    and ufd1.fldvalue = oitm.U_Category
    and ufd1.fieldid = 0)
                                as "product_type",
    (select top 1 ufd1.descr from ufd1 WITH (NOLOCK)
    where ufd1.tableid = 'oitm'
    and ufd1.fldvalue = oitm.U_Brand
    and ufd1.fieldid = 9)
                                as "vendor",
    ''                          as "variants.option1",
    ''                          as "variants.option2",
    ''                          as "variants.option3",
    ' '                         as "body_html",
    0                           as "variants.weight",
    oitm.CodeBars               as "variants.barcode",

    -- All price tiers
    '1'                         as "default_price_tier",
    stuff(
    (select
    convert(varchar, itm1.PriceList) + '|' +
    convert(varchar, itm1.Price) + ','
    from itm1 WITH (NOLOCK)
    where itm1.ItemCode = oitm.ItemCode
    order by itm1.PriceList
    for xml path('')),1,0,'')
                                as "csv_price_tiers",

    -- All qty availability
    'SBY'                      as "default_qty_availability",
    stuff(
    (select
    convert(varchar, oitw.WhsCode) + '|' +
    convert(varchar, oitw.OnHand)+','
    from oitw WITH (NOLOCK)
    where oitw.ItemCode = oitm.ItemCode
    and oitw.WhsCode in (
        'R-MAIN','R-PTA','R-BFN','R-CPT','R-DBN','R-NLP','R-PLZ', 'R-ERD', 'R-EXP', 'R-VDB', 'R-WSR',
        'A-SBY','A-CPT','A-DBN','A-NLP','A-PLZ', 'R-BULK'
    )
    order by oitw.WhsCode
    for xml path('')),1,0,'')
                                as "csv_qty_availability",

    'true'                      as "variants.inventory_management",

    case
        when (COALESCE(oitm.U_S2sActive, 0) + COALESCE(oitm.U_S2sActive2, 0)) > 0 then 'true'
        when (COALESCE(oitm.U_S2sActive, 0) + COALESCE(oitm.U_S2sActive2, 0)) = 0 then 'false'
        else 'false'
    end                         as "product_active",

    stuff(
    (select convert(varchar, oali.AltItem) + ','
    from oali WITH (NOLOCK)
    where oitm.ItemCode = oali.OrigItem
    order by oali.AltItem
    for xml path('')),1,0,'')
                                as 'tags',

    oitb.ItmsGrpCod             as "meta_item_group_code",
    oitb.ItmsGrpNam             as "meta_item_group_name",

    oitm.U_1                    as "meta_description_1",
    oitm.U_2                    as "meta_description_2",
    oitm.U_3                    as "meta_description_3",
    oitm.U_4                    as "meta_description_4",
    oitm.U_5                    as "meta_description_5",

    ISNULL(oitm.U_OENR1,'')     as "meta_oenr_tag1",
    ISNULL(oitm.U_OENR2,'')     as "meta_oenr_tag2",
    ISNULL(oitm.U_OENR3,'')     as "meta_oenr_tag3",
    ISNULL(oitm.U_OENR4,'')     as "meta_oenr_tag4",
    ISNULL(oitm.U_OENR5,'')     as "meta_oenr_tag5",
    ISNULL(oitm.U_OENR6,'')     as "meta_oenr_tag6",
    ISNULL(oitm.U_OENR7,'')     as "meta_oenr_tag7",
    ISNULL(oitm.U_OENR8,'')     as "meta_oenr_tag8",
    ISNULL(oitm.U_OENR9,'')     as "meta_oenr_tag9",
    ISNULL(oitm.U_OENR10,'')     as "meta_oenr_tag10",

    COALESCE(oitm.U_S2sActive, 0)
                                as "meta_auto",
    COALESCE(oitm.U_S2sActive2, 0)
                                as "meta_refrigeration",

    (select top 1 ufd1.descr from ufd1 WITH (NOLOCK)
    where ufd1.tableid = 'oitm'
    and ufd1.fldvalue = oitm.U_Comp1stGroove
    and ufd1.fieldid = 35)
                                as "meta_Compressor 1st Groove",
    (select top 1 ufd1.descr from ufd1 WITH (NOLOCK)
    where ufd1.tableid = 'oitm'
    and ufd1.fldvalue = oitm.U_CompPortEntry
    and ufd1.fieldid = 33)
                                as "meta_Compressor Port Entry",
    (select top 1 ufd1.descr from ufd1 WITH (NOLOCK)
    where ufd1.tableid = 'oitm'
    and ufd1.fldvalue = oitm.U_CompModel
    and ufd1.fieldid = 28)
                                as "meta_Compressor Model",
    (select top 1 ufd1.descr from ufd1 WITH (NOLOCK)
    where ufd1.tableid = 'oitm'
    and ufd1.fldvalue = oitm.U_COMPMount
    and ufd1.fieldid = 29)
                                as "meta_Compressor Mount",
    (select top 1 ufd1.descr from ufd1 WITH (NOLOCK)
    where ufd1.tableid = 'oitm'
    and ufd1.fldvalue = oitm.U_CompPort
    and ufd1.fieldid = 34)
                                as "meta_Compressor Port",
    (select top 1 ufd1.descr from ufd1 WITH (NOLOCK)
    where ufd1.tableid = 'oitm'
    and ufd1.fldvalue = oitm.U_PulleyDiameter
    and ufd1.fieldid = 32)
                                as "meta_Pulley Diameter",
    (select top 1 ufd1.descr from ufd1 WITH (NOLOCK)
    where ufd1.tableid = 'oitm'
    and ufd1.fldvalue = oitm.U_POLYVEE
    and ufd1.fieldid = 31)
                                as "meta_Poly Vee",
    (select top 1 ufd1.descr from ufd1 WITH (NOLOCK)
    where ufd1.tableid = 'oitm'
    and ufd1.fldvalue = oitm.U_VOLTAGE
    and ufd1.fieldid = 30)
                                as "meta_Voltage",

    COALESCE(
        (
        select top 1 'New Stock due shortly!'
        from POR1 WITH (NOLOCK)
        where POR1.ItemCode = OITM.ItemCode and POR1.OpenQty > 0
        ),
        'None. Please contact sales staff.'
    )                           as "meta_po_qty",

    'n/a'                       as "meta_po_date",
    (
            (select
                     'Nationwide: ' + convert(varchar, cast(sum(oitw.OnHand) as int))+','
             from oitw WITH (NOLOCK)
             where oitw.ItemCode = oitm.ItemCode
               and oitw.WhsCode in (
                                    'R-MAIN','R-PTA','R-BFN','R-CPT','R-DBN','R-NLP','R-PLZ', 'R-ERD', 'R-EXP', 'R-VDB', 'R-WSR',
                                    'A-SBY' ,'A-CPT','A-DBN','A-NLP','A-PLZ', 'R-BULK'
                 )

             ))
                                as "meta_qty_nationwide",

    -- Special prices for all periods and volumes
    stuff(
    (select
    convert(varchar, ospp.CardCode) + '|' +
   CASE lower(ospp.AutoUpdt) WHEN 'y'
    THEN convert(varchar(50),
        (convert(float,(select itm1.Price
            from itm1
                     inner join ocrd
                         on itm1.ItemCode = oitm.ItemCode
            where
                itm1.PriceList = ocrd.ListNum
            and ospp.CardCode = ocrd.CardCode)) -
         (convert(float,(select itm1.Price
            from itm1
                     inner join ocrd
                         on itm1.ItemCode = oitm.ItemCode
            where
                itm1.PriceList = ocrd.ListNum
            and ospp.CardCode = ocrd.CardCode)) * ospp.Discount) / 100))
             ELSE convert(varchar(50), ospp.Price)
    END
        + ','
    from ospp WITH (NOLOCK)
    where ospp.ItemCode = oitm.ItemCode
    and ospp.Valid = 'Y'
    and ospp.Price > 0
    and DATEADD(day, DATEDIFF(day,0,GETDATE()),0) <= ISNULL(ospp.ValidTo, DATEADD(day, DATEDIFF(day,0,GETDATE()),0))
    and DATEADD(day, DATEDIFF(day,0,GETDATE()),0) >= ISNULL(ospp.ValidFrom, DATEADD(day, DATEDIFF(day,0,GETDATE()),0))
    order by ospp.ItemCode
    for xml path('')),1,0,'')
                                as 'csv_special_prices',

    STUFF(
    (SELECT
    ',<a href="/#/industry/automotive/products?q=' + convert(varchar, oali.AltItem) + '">' + convert(varchar, oali.AltItem) + '</a>'
    FROM oali WITH (NOLOCK)
    WHERE oitm.ItemCode = oali.OrigItem
    ORDER BY oali.AltItem
    for xml path('')),1,1,'')
                                AS 'meta_Alternatives',

    -- Recommended products
    concat(
        oitm.U_S2SRec1,',',
        oitm.U_S2SRec2,',',
        oitm.U_S2SRec3,',',
        oitm.U_S2SRec4,',',
        oitm.U_S2SRec5,',',
        oitm.U_S2SRec6,',',
        oitm.U_S2SRec7,',',
        oitm.U_S2SRec8,',',
        oitm.U_S2SRec9,',',
        oitm.U_S2SRec10
    )                          AS "meta_recommended",
    
-- BOM products
(
    CASE
        WHEN oitm.U_S2sActive = 1 THEN
        concat(
            oitm.U_BOM1,'-',oitm.U_BOM1Desc,',', 
            oitm.U_BOM2,'-',oitm.U_BOM2Desc,',', 
            oitm.U_BOM3,'-',oitm.U_BOM3Desc,',', 
            oitm.U_BOM4,'-',oitm.U_BOM4Desc,',', 
            oitm.U_BOM5,'-',oitm.U_BOM5Desc,',', 
            oitm.U_BOM6,'-',oitm.U_BOM6Des,',', 
            oitm.U_BOM7,'-',oitm.U_BOM7Desc,',', 
            oitm.U_BOM8,'-',oitm.U_BOM8Desc,',', 
            oitm.U_BOM9,'-',oitm.U_BOM9Desc,','
        )  
    END
)                             AS "meta_bom",

    -- Special prices for period join ospp with spp1
    ISNULL(
    (SELECT spp1.Price
    FROM SPP1 WITH (NOLOCK)
    where SPP1.ItemCode = oitm.ItemCode and DATEADD(day, DATEDIFF(day,0,GETDATE()),0) <= SPP1.ToDate
    ),0)                        as 'meta_period_price'

    -- Special prices for volume join ospp with spp2

from oitm WITH (NOLOCK)
join oitb on oitm.ItmsGrpCod = oitb.ItmsGrpCod


) as rows
where
n > '%(audit_lower_limit)n'
and n <= '%(audit_upper_limit)n'


