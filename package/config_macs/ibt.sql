SELECT
    [APCode] = BV.DflVendor,
    [FromBranchID] = BV.BPLId,
    [ARCode] = BC.DflCust,
    [ToBranchID] = BC.BPLId,
    WV.WhsCode AS [FromWhs],
    WC.WhsCode AS [ToWhs],
    <PERSON><PERSON>,
    [AvgPrice] = CASE
                 WHEN isnull(S.U_PriceSource, 'I') = 'I'
                   THEN I.AvgPrice
                 ELSE WV1.AvgPrice
                 END,
    [VendorStock] = (WV1.OnHand + WV1.OnOrder) - WV1.IsCommited,
    [CustStock] = (WC1.OnHand + WC1.OnOrder) - WC1.IsCommited,
    [ReqStock] = CASE
                 WHEN (WC1.OnHand + WC1.OnOrder) - WC1.IsCommited >= '%(qty)n'
                   THEN 0
                 WHEN (WC1.OnHand + WC1.OnOrder) - WC1.IsCommited < '%(qty)n' AND
                      (WV1.OnHand + WV1.OnOrder) - WV1.IsCommited > 0 AND
                      ((WV1.OnHand + WV1.OnOrder) - WV1.IsCommited) + ((WC1.OnHand + WC1.OnOrder) - WC1.IsCommited) >=
                      '%(qty)n'
                   THEN '%(qty)n' - ((WC1.OnHand + WC1.OnOrder) - WC1.IsCommited)
                 WHEN ((WV1.OnHand + WV1.OnOrder) - WV1.IsCommited) + ((WC1.OnHand + WC1.OnOrder) - WC1.IsCommited) <
                      '%(qty)n'
                   THEN -1
                 END
FROM OITM I, OITW WV1, OWHS WC, [@DBS_IBS_SETUP] S, OBPL BV, OWHS WV, OITW WC1, OBPL BC
WHERE S.U_MainBranch = BV.BPLId
      AND WV.BPLid = BV.BPLId
      AND WC.BPLid = BC.BPLId
      AND I.ItemCode = '%(sku)s'
      AND WV1.ItemCode = I.ItemCode
      AND WV1.WhsCode = WV.WhsCode
      AND WC1.WhsCode = WC.WhsCode
      AND WC1.ItemCode = I.ItemCode
      AND WC.WhsCode = '%(warehouse_code)s'
      AND WC.Location = WV.Location
      AND I.InvntItem = 'Y'
      AND I.QryGroup3 = 'Y'