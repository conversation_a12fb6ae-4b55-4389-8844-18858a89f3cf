select
    ocpr.CardCode + '-' + convert(varchar, ocpr.CntctCode)   as 'source_customer_code',
    ocpr.FirstName   as 'first_name',
	  ocpr.LastName   as 'last_name',
	  ocrd.CardName   as 'card_name',
    ocpr.E_MailL     as 'email',

    case convert(varchar, ocrd.U_S2sActive) + ocpr.Pager
        when '1Y' then 'true'
        else 'false'
    end        as "customer_active",

    1               as 'accepts_marketing',
    crd1.Address2   as 'address.address1',
    crd1.Address3   as 'address.address2',
    crd1.City       as 'address.city',
    crd1.Country    as 'address.country',
    crd1.Country    as 'address.country_code',
    crd1.County     as 'address.province',
    crd1.State      as 'address.province_code',
    crd1.ZipCode    as 'address.zip',
    ocrd.CardName   as 'address.company',
    ocrd.CntctPrsn  as 'address.contact_person',
    ocrd.Phone1     as 'address.phone',

    -- XML addresses
    CASE WHEN ocrd.U_S2sActiveRef <> 1 THEN
        (
          SELECT
              crd1.Address    as 'address_code',
              CASE crd1.AdresType
              WHEN 'B' THEN 'billing'
              WHEN 'S' THEN 'shipping'
              END
                              as 'type',
              coalesce(crd1.Block, '')      as 'address1',
              coalesce(crd1.Address2, '')   as 'address2',
              coalesce(crd1.City, '')       as 'city',
              coalesce(crd1.Country, '')    as 'country',
              coalesce(crd1.Country, '')    as 'country_code',
              coalesce(crd1.County, '')     as 'province',
              coalesce(crd1.State, '')      as 'province_code',
              coalesce(crd1.ZipCode, '')    as 'zip',
              coalesce(ocrd.Phone1, '')     as 'phone',
              coalesce(ocrd.CardName, '')   as 'company',
              coalesce(ocpr.FirstName, '')  as 'first_name',
              coalesce(ocpr.LastName, '')   as 'last_name'
           FROM crd1
           WHERE crd1.CardCode=ocpr.CardCode and crd1.AdresType in ('S','B')
           ORDER BY crd1.Address
           FOR xml PATH ('address'), root ('addresses')
        )
    ELSE
        NULL
    END             as 'xml_addresses',

    ocrd.GroupCode  as 'meta_customer_group_code',
	  case when ocrd.VatStatus = 'N' then '0'
        else '15'
    end
	             as 'meta_tax_rate',

    CASE convert(VARCHAR,RTRIM(LTRIM(ocrd.U_Del_Coll)))
        WHEN 'C'
        THEN '{
	"Collect": {
		"method": "flat_rate",
		"price": 0,
		"tax_lines": [{
			"rate": 0,
			"price": 0,
			"title": "VAT",
			"code": "taxed"
		}]
	}
    }'
        WHEN 'D'
	    THEN '{
	"Free Delivery": {
		"method": "flat_rate",
		"price": 0,
		"tax_lines": [{
			"rate": 0,
			"price": 0,
			"title": "VAT",
			"code": "taxed"
		}]
	},
	"Pick Up": {
		"method": "flat_rate",
		"price": 0,
		"tax_lines": [{
			"rate": 0,
			"price": 0,
			"title": "VAT",
			"code": "taxed"
		}]
	}
    }'
    END
                                      as 'meta_shipping_methods',
    RTRIM(LTRIM(ocrd.U_Del_Coll))     as 'meta_customer_del_options',


    -- Discount groups (Old version)
--     stuff(
--     (select
--     convert(varchar, ospg.ObjKey) + '|' + -- oitb.ItmsGrpCod
--     convert(varchar, ospg.Discount) + ','
--     from ospg
--     where ospg.CardCode = ocrd.CardCode
--     and ospg.ObjType = 52 -- Discount Groups
--     and ospg.Discount > 0
--     order by ospg.ObjKey
--     for xml path('')),1,0,'')
--                as 'csv_discount_groups',
--                -- For customers

    -- Discount groups (New version)
    stuff(
    (select
    convert(varchar, edg1.ObjKey) + '|' + -- oitb.ItmsGrpCod
    case convert(varchar, edg1.Discount) when '0.010000' then '0.000000,'
    else convert(varchar, edg1.Discount) + ','
    end
    from oedg  WITH (NOLOCK) inner join edg1
    on oedg.AbsEntry = edg1.AbsEntry
    where oedg.ObjCode = ocrd.CardCode
    order by edg1.ObjKey
    for xml path('')),1,0,'')
               as 'csv_discount_groups',
               -- For customers

    ListNum    as 'price_tier',

    case
    when QryGroup16 = 'Y' then 'R-MAIN'
    when QryGroup2 = 'Y' then 'R-PTA'
    when QryGroup3 = 'Y' then 'R-BFN'
    when QryGroup4 = 'Y' then 'R-CPT'
    when QryGroup5 = 'Y' then 'R-DBN'
    when QryGroup6 = 'Y' then 'R-NLP'
    when QryGroup8 = 'Y' then 'R-PLZ'
    when QryGroup15 = 'Y' then 'R-EXP'
    when QryGroup42 = 'Y' then 'R-VDB'
    when QryGroup47 = 'Y' then 'R-WSR'

    when QryGroup20 = 'Y' then 'A-SBY'
    when QryGroup21 = 'Y' then 'A-PTA'
--     when QryGroup = 'Y' then 'A-BFN'
    when QryGroup22 = 'Y' then 'A-CPT'
    when QryGroup23 = 'Y' then 'A-DBN'
    when QryGroup24 = 'Y' then 'A-NLP'
    when QryGroup25 = 'Y' then 'A-PLZ'
    when QryGroup31 = 'Y' then 'R-ERD'
    end
                as 'qty_availability',
    'Y'         as 'meta_updated',
    case ocrd.U_S2sActiveRef
        when 1 then '1783' -- macs cool
        else        '230' -- macs auto
    end         as 'meta_channel_id',
    ocrd.U_S2sActive  as 'meta_active',
    case ocrd.QryGroup7
        when 'Y' then 'COD'
        else          'On Account'
    end         as 'meta_account_terms',
    case
    -- Account customers = OnAccount
    -- COD customers = MyGate
    when ocrd.U_S2sActiveRef = 1 and ocrd.QryGroup7 = 'Y' then
        '[{
            "method": "AdumoVirtual",
            "description": "Credit Card (Adumo)",
            "iss": "MACS COOL (PTY) LTD",
            "auid": "C0101B6D-93A5-4A6F-96EF-05D4850EDA82",
            "cuid": "98DA3FC4-1ECF-43F2-99D4-F6502B8A7DCD",
            "jwt_secret": "3d43bbc3-5302-4078-a4f1-c03f703390ae",
            "url_staging": "https://staging-apiv2.adumoonline.com/product/payment/v1/initialisevirtual",
            "url_production": "https://apiv2.adumoonline.com/product/payment/v1/initialisevirtual",
            "in_production": true,
            "currency": "zar"
        }]'
    when ocrd.U_S2sActiveRef = 1 and ocrd.QryGroup7 <> 'Y' then
        '[        {
            "method": "OnAccount",
            "description": "On Account"
                   },
            {
            "method": "AdumoVirtual",
            "description": "Credit Card – receive a '+ left(convert(varchar, ocrd.U_S2SDisc),3) +'% Discount when paying by Credit Card instead of on Account",
            "iss": "MACS COOL (PTY) LTD",
            "auid": "C0101B6D-93A5-4A6F-96EF-05D4850EDA82",
            "cuid": "98DA3FC4-1ECF-43F2-99D4-F6502B8A7DCD",
            "jwt_secret": "3d43bbc3-5302-4078-a4f1-c03f703390ae",
            "url_staging": "https://staging-apiv2.adumoonline.com/product/payment/v1/initialisevirtual",
            "url_production": "https://apiv2.adumoonline.com/product/payment/v1/initialisevirtual",
            "in_production": true,
            "currency": "zar"
        }]'
    else
        '[{
            "method": "OnAccount",
            "description": "On Account"
        }]'
    end
                as 'meta_payment_methods',
    case
        when ocrd.QryGroup36 = 'Y' then 'Y' -- Delivery
        else                            'N' -- Collection
    end         as 'meta_delivery',
    ocrd.Balance                        as 'meta_balance_display',
    (case
    when (ocrd.CreditLine - ABS(ocrd.Balance)) < 0 then 0
    else (ocrd.CreditLine - ABS(ocrd.Balance))
    end)
                                        as 'meta_credit_limit',
    ocrd.U_S2SDisc                      as 'meta_discount_percentage'

from ocpr  WITH (NOLOCK)
INNER JOIN ocrd WITH (NOLOCK) on ocrd.CardCode=ocpr.CardCode
LEFT JOIN crd1 WITH (NOLOCK) on crd1.CardCode=ocpr.CardCode and crd1.Address = ocrd.ShipToDef and crd1.AdresType='S'
where ocpr.CardCode + '-' + convert(varchar, ocpr.CntctCode) = '20067-2180';
