  select
      -- Column must be named "count", it is used by push.exe
      (
      select count(oitm.ItemCode) as product_count
      from oitm WITH (NOLOCK)
      )
                                                  as count,

      (select count(oitm.ItemCode) as product_count
      from oitm WITH (NOLOCK)
      WHERE
      COALESCE(oitm.U_S2sActive, 0) + COALESCE(oitm.U_S2sActive2, 0) > 0
      )
                                                  as active,

      (select count(oitm.ItemCode) as product_count
      from oitm WITH (NOLOCK)
      WHERE
      COALESCE(oitm.U_S2sActive, 0) + COALESCE(oitm.U_S2sActive2, 0) = 0
      )
                                                  as inactive,

      (select count(oitm.ItemCode) as product_count
      from oitm WITH (NOLOCK))
                                                  as total;
