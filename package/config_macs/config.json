{"dbName": "local.db", "dsn": "Driver={SQL Server};Server=MACS-SQL;Database=MacsAuto_LIVE;Uid=sa;Pwd=**********;", "server": "MACS-SQL", "companyDb": "MacsAuto_LIVE", "xcompanyDb": "MacsBranch_Testing", "userName": "SBYIAUTO", "password": "stock2s", "XuserName": "cons70", "Xpassword": "macs@1", "dbServerType": "MSSQL2019", "dbUserName": "sa", "dbPassword": "**********", "licenseServer": "MACS-SQL", "audit_limit": 1000, "audit_image_limit": 100, "debug": 0, "image_path": "D:\\SAP_IMG", "push": {"source_id": 246, "limit": 500, "image_limit": 7, "token": "995395a69a472f55196809fa0486187f0df106a0", "writeLog": "http://localhost:13337/writeLog", "getProducts": "http://localhost:13337/sapone/getProductsBatch", "getCustomers": "http://localhost:13337/sapone/getCustomersBatch", "getImages": "http://localhost:13337/sapone/getImagesBatch", "countProducts": "http://localhost:13337/sapone/countProducts", "countCustomers": "http://localhost:13337/sapone/countCustomers", "countImages": "http://localhost:13337/sapone/countImages", "getMeta": "http://localhost:13337/sapone/getMeta", "setMeta": "http://localhost:13337/sapone/setMeta", "productsQueue": "https://app.stock2shop.com/v1/products/queue", "customersQueue": "https://app.stock2shop.com/v1/customers/queue", "imagesQueue": "https://app.stock2shop.com/v1/images/queue"}}