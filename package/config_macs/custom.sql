select oitm.ItemCode as "source_product_code",
       -- Special prices for all periods and volumes
       stuff(
               (select convert(varchar, ospp.CardCode) + '|' +
                       CASE lower(ospp.AutoUpdt)
                           WHEN 'y'
                               THEN convert(varchar(50),
                                   (convert(float, (select itm1.Price
                                                    from itm1
                                                             inner join ocrd
                                                                        on itm1.ItemCode = oitm.ItemCode
                                                    where itm1.PriceList = ocrd.ListNum
                                                      and ospp.CardCode = ocrd.CardCode)) -
                                    (convert(float, (select itm1.Price
                                                     from itm1
                                                              inner join ocrd
                                                                         on itm1.ItemCode = oitm.ItemCode
                                                     where itm1.PriceList = ospp.ListNum
                                                       and ospp.CardCode = ocrd.CardCode)) * ospp.Discount) / 100))
                           ELSE convert(varchar(50), ospp.Price)
                           END
                           + ','
                from ospp WITH (NOLOCK)
                where ospp.ItemCode = oitm.ItemCode
                  and ospp.Price > 0
                order by ospp.ItemCode
                for xml path('')), 1, 0, '')
                     as 'csv_special_prices'
from oitm WITH (NOLOCK)
where oitm.ItemCode = 'TTCP-SE-321';


