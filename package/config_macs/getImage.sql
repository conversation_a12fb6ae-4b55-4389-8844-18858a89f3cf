SELECT
    ROW_NUMBER() OVER (ORDER BY inv.ItemCode) AS n,

    -- "source_image_code" must uniquely identify an image
    -- Since we don't have multiple images here we can just use product ID and name of picture
    inv.ItemCode + '-' + inv.PicturName  AS "source_image_code",
    inv.ItemCode                         AS "source_product_code",
    inv.ItemCode                         AS "source_variant_code",
    inv.PicturName                       AS "filename"

FROM oitm inv WITH (NOLOCK)
WHERE RTRIM(ISNULL(inv.PicturName, '')) <> '' AND inv.U_S2sActive = 1
AND inv.ItemCode = '%(source_variant_code)s'
