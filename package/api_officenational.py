import json
import imp
import collections
import xml.sax.saxutils as saxutils
from .controllers import base_push as base

class Api(base.Api):
    _configDir = "config_officenational"

    def reload(self):
        imp.reload(base)
        base.Api.reload(self)
        response = {
            "status": True,
            "data": {
                "reload": True
            }
        }
        return json.dumps(response, indent=self._indent)

    # Add _transformProduct here to override default

    def _afterProductTransform(self, params, result, source_product):
        from collections import OrderedDict

        metas = []
        if result["csv_discount_groups"] is not None:
            groups = saxutils.unescape(result["csv_discount_groups"])
            groups = groups.split(",")
            for group in groups:
                groupItem = group.split("|")
                if len(groupItem) == 2 and groupItem[0].strip() != "":
                    group_code = groupItem[0].strip()
                    try:
                        meta = collections.OrderedDict()
                        meta["key"] = str("customer_code_{}".format(group_code))
                        meta["value"] = str(float(groupItem[1]))
                        metas.append(meta)
                    except ValueError:
                        pass
        for key in result:
            if key.startswith("meta_"):
                meta = collections.OrderedDict()
                meta["key"] = str(key[5:])  # Remove "meta_" from key
                meta["value"] = saxutils.unescape(str(row[key]))
                metas.append(meta)

        source_product["product"]["meta"] = metas

        return source_product