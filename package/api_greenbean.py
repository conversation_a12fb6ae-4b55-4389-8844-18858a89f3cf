import json
import imp
import os
import collections
from datetime import datetime, timedelta
from .shared import utils
from .shared import pastel_evo as pastel
from .controllers import base_push as base

CONST_LANDED_PRICE_TIER = "Landed"
CONST_JHB_WHSE_CODE     = "JHB Warehouse"
CONST_CPT_WHSE_CODE     = "CPT Warehouse"
CONST_JHB_DMG_WHSE_CODE = "JHB Damaged Stock"
CONST_CPT_DMG_WHSE_CODE = "CPT Damaged Stock"

CONST_WHSE_SPLIT        = "warehouse_split"
CONST_QTY_CUSTOM_QUERY  = "getWhseQtyBySKU.sql"
CONST_PROVINCE_MAP_KEY  = "province_map"
CONST_WHSE_INVERTED_MAP = {
    CONST_JHB_WHSE_CODE: CONST_CPT_WHSE_CODE,
    CONST_CPT_WHSE_CODE: CONST_JHB_WHSE_CODE
}

CONST_META_PROCESS_INVOICE     = "process_invoice"
CONST_META_CONFIRM_QTY         = "confirm_qty"
CONST_META_PROCESS_RESERVE     = "process_reserved"
CONST_META_ORDER_DOCUMENT_TYPE = "order_document_type"

packageDir = os.path.join(
    os.path.realpath(os.path.join(os.getcwd(), os.path.dirname(__file__)))
)

class Api(base.Api):
    _configDir = "config_greenbean"

    def reload(self):
        imp.reload(base)
        base.Api.reload(self)
        response = {
            "status": True,
            "data": {
                "reload": True
            }
        }
        return json.dumps(response, indent=self._indent)

    def _afterProductTransform(self, params, result, source_product):
        """
        - Set negative qty values to 0
        - Add additional warehouse with a safety margin
        """

        # Set negative values to 0 (Takealot does not accept negative values for qty)
        if "qty_availability" in source_product["product"]["variants"]:
            for warehouse in source_product["product"]["variants"]["qty_availability"]:
                if warehouse['qty'] < 0:
                    warehouse['qty'] = 0

        # New subsidy's are added by adding a new dictionary in the same format
        # dont forget the comma
        subsidys = [{"PL": 3}]
        result = []


        # possibility that qty_availability might not be set
        if "qty_availability" not in source_product["product"]["variants"]:
            source_product["product"]["variants"]["qty_availability"] = []

        warehouses = source_product["product"]["variants"]["qty_availability"]

        for subsidy in subsidys:
            # iterates through the disctionary as a key and value pair
            for key, value in subsidy.items():
                for warehouse in warehouses:
                    # create new dictionary for new price tier
                    new_warehouse = dict()
                    new_warehouse["description"] = warehouse["description"] + "_" + key
                    new_warehouse["qty"] = warehouse["qty"] - value
                    result.append(new_warehouse)

        # if qty in warehouse jhb and cpt is greater than zero set meta=3 else meta=4
        # Check if qty in warehouse JHB and CPT is greater than zero
        qty_jhb = sum(warehouse["qty"] for warehouse in warehouses if "JHB Warehouse" in warehouse["description"].lower())
        qty_cpt = sum(warehouse["qty"] for warehouse in warehouses if "CPT Warehouse" in warehouse["description"].lower())

        if qty_jhb > 0 and qty_cpt > 0:
            leadtime_days = 3
        else:
            leadtime_days = 4

        # add the leadtime_days as meta
        meta = collections.OrderedDict()
        meta["key"] = "leadtime_days"
        meta["value"] = str(leadtime_days)
        source_product["product"]["meta"].append(meta)

        Api.setDamagedStockAndPriceMeta(source_product)
        Api.setDimensionsBarcodeMeta(source_product)
        Api.setAvailIncomingStockMeta(source_product)

        # combine arrays
        source_product["product"]["variants"]["qty_availability"] += result
        return source_product

    @staticmethod
    def setDamagedStockAndPriceMeta(source_product: dict) -> dict:
        """
        Create `damaged_stock_price` and assign it to `source_product` dict
        """
        damaged_qty = Api.sumCsvQty(
            Api.getProductMetaByKey(source_product, "damaged_qty"),
            [CONST_CPT_DMG_WHSE_CODE, CONST_JHB_DMG_WHSE_CODE],
        )
        damaged_stock_price = "Qty: " + damaged_qty

        # We can only display the landed price if:
        # - The landed price is not an empty value
        # - The damaged qty is not an empty value
        landed_price = Api.getProductPrice(source_product, CONST_LANDED_PRICE_TIER)
        if landed_price not in ["0", ""] and damaged_qty not in ["0", ""]:
            damaged_stock_price += " | Reduced Price: R" + landed_price

        # if there is no landed price, but dmg qty, then we must display `SQ` next to the damaged qty
        # and ignore displaying the landed price
        if landed_price in ["0", ""]:
            damaged_stock_price += " | SQ"

        source_product = Api.createProductMeta(
            source_product, "damaged_stock_price", damaged_stock_price
        )
        return source_product

    @staticmethod
    def setAvailIncomingStockMeta(source_product: dict) -> dict:
        """
        Create `available_incoming_stock` and assign it to `source_product` dict
        """
        csv_po_qty = Api.getProductMetaByKey(source_product, "po_qty")
        available_incoming_stock = (
            "JHB: " + Api.getProductWhse(source_product, CONST_JHB_WHSE_CODE) + " | " +
            "CPT: " + Api.getProductWhse(source_product, CONST_CPT_WHSE_CODE) + " | " +
            "Stock en Route: " + str(Api.sumCsvQty(csv_po_qty, []))
        )

        source_product = Api.createProductMeta(
            source_product, "available_incoming_stock", available_incoming_stock
        )
        return source_product

    @staticmethod
    def setDimensionsBarcodeMeta(source_product: dict) -> dict:
        """
        Create `meta_ship_dims_and_barcode` and assign it to `source_product` dict
        """
        dimensions = Api.getProductMetaByKey(source_product, "dimensions")
        barcode = Api.getProductMetaByKey(source_product, "barcode")
        ship_dims_and_barcode = dimensions + " | " + barcode

        source_product = Api.createProductMeta(
            source_product, "ship_dims_and_barcode", ship_dims_and_barcode
        )
        return source_product

    @staticmethod
    def sumCsvQty(csv_qty_csv: str, warehouse_names: list) -> str:
        """
        Sums all the qty inside a csv string and returns the number. A haystack can be passed as a second
        param. Only warehouses included in the list's qty's will be summed.
        """
        total = 0
        if csv_qty_csv is not None and csv_qty_csv != "":
            warehouses = csv_qty_csv.split(",")
            for warehouse in warehouses:
                qty = warehouse.split("|")
                if len(qty) > 1:
                    # this means that we have a haystack to compare with
                    if len(warehouse_names) > 0:
                        if qty[0] in warehouse_names:
                            if qty[1] == "":
                                qty[1] = 0
                            total += int(qty[1])
                    # no haystack present, so we simply add all of them
                    else:
                        if qty[1] == "":
                            qty[1] = 0
                        total += int(qty[1])
            return str(total)
        return str(total)

    @staticmethod
    def getProductMetaByKey(source_product: dict, key: str) -> str:
        """
        Returns the `value` of the respective product meta if found in the `source_product`
        """
        if key is None or key == "":
            return ""

        for meta in source_product["product"]["meta"]:
            if meta["key"] == key:
                return meta["value"]
        return ""

    @staticmethod
    def getProductPrice(source_product: dict, price_tier: str) -> str:
        """
        Returns the `price` of the respective product price tier if found in the `variant`
        """
        if price_tier == "":
            return ""
        for tier in source_product["product"]["variants"]["price_tiers"]:
            if tier["tier"] == price_tier:
                return str(round(tier["price"]))
        return ""

    @staticmethod
    def getProductWhse(source_product: dict, whse_name: str) -> str:
        """
        Returns the `qty` of the respective product meta if found in the `variant`
        """
        for whse in source_product["product"]["variants"]["qty_availability"]:
            if whse["description"] == whse_name:
                return str(whse["qty"])
        return "0"

    @staticmethod
    def createProductMeta(source_product: dict, key: str, value: str) -> dict:
        """
        Helper method to keep the code dry from all the meta creation
        """
        meta = collections.OrderedDict()
        meta["key"] = key
        meta["value"] = value
        source_product["product"]["meta"].append(meta)
        return source_product

    def setOrderDetailWhse(self, so: any, params: dict):
        default_whse_qty_dict = {}
        province_map = json.loads(params[CONST_PROVINCE_MAP_KEY])
        province = params["province"]
        shipping_code = str(params["shipping_code"])

        # Only reserve qty if process_reserve="true" and order_document_type!="Quotation"
        # only applies to the `warehouse_split` feature
        apply_reserve = (
            Api.checkParamKeyEqualsValue(params, CONST_META_PROCESS_RESERVE, "true") and
            not Api.checkParamKeyEqualsValue(params, CONST_META_ORDER_DOCUMENT_TYPE, "Quotation")
        )
        apply_to_process = (
            Api.checkParamKeyEqualsValue(params, CONST_META_PROCESS_INVOICE, "true") or
            Api.checkParamKeyEqualsValue(params, CONST_META_CONFIRM_QTY, "true")
        )

        if province in province_map:
            # check if the line items on the `so` has qty at the default warehouse (which is decided by the province).
            # if it requires a splitting of warehouses (one line does not have enough qty)
            # then we need to do another query to retrieve the qty in the other warehouse
            # if the default was `CPT Warehouse` then we use `JHB Warehouse` as the 'other' warehouse.
            try:
                so, need_split, default_whse_qty_dict = self.handleOrderDetailsWhse(
                    so,
                    province_map[province],
                    shipping_code,
                    default_whse_qty_dict,
                    False,
                    apply_reserve,
                    apply_to_process,
                )
            except Exception as e:
                raise Exception("Error occurred: " + str(e))

            if need_split:
                # only if anyone of the line items do not have enough stock at the default warehouse
                # then we must split them (duplicate order detail)
                # NOTE: the inversion here as per `CONST_WHSE_INVERTED_MAP`
                try:
                    so, need_split, default_whse_qty_dict = self.handleOrderDetailsWhse(
                        so,
                        CONST_WHSE_INVERTED_MAP[province_map[province]],
                        shipping_code,
                        default_whse_qty_dict,
                        need_split,
                        apply_reserve,
                        apply_to_process,
                    )
                except Exception as e:
                    raise Exception("Error occurred when trying to split stock: " + str(e))

    def handleOrderDetailsWhse(
        self,
        so: any,
        whse_code: str,
        shipping_code: str,
        qty_dict: dict,
        split_enabled: bool = False,
        apply_reserve: bool = False,
        apply_to_process: bool = False,
    ) -> tuple:
        """
        Description:
        ---

        Sets the warehouse on each line item (Order Detail) depending on the province the customer selects.
        The default warehouse (which is used by default for all line item, prior to any checks)
        is decided by the channel map value respective to the province. E.g:
        ```
        {
            "province": "whse_code"
        }
        ```
        """
        new_order_details = []
        need_split = False
        if split_enabled:
            qty_dict_new = {}
        for od in so.Detail:
            # skip splitting all shipping_codes
            if str(od.InventoryItem.Code) == shipping_code:
                continue

            if od.InventoryItem.IsWarehouseTracked:
                code = str(od.InventoryItem.Code)

                # skip over any order details that has enough stock at it's present warehouse
                if code in qty_dict:
                    if qty_dict[code]["has_stock"]:
                        continue

                # if split is enabled, then we assign qty results to new dict
                # without overwriting the previous one
                # which should contain the qtys from the default_whse (previous iteration)
                if split_enabled:
                    has_stock, qty_dict_new = self.isStockAtWhse(
                        code, whse_code, od.Quantity, qty_dict_new
                    )
                else:
                    has_stock, qty_dict = self.isStockAtWhse(
                        code, whse_code, od.Quantity, qty_dict
                    )

                if has_stock and not split_enabled:
                    # check if the whse currently assign to the od is the same
                    # as the current one that we are checking stock for
                    if whse_code != str(od.Warehouse.Code):
                        sdk, helper = pastel.getSdk(self._config)
                        od.Warehouse = helper.GetWarehouseByCode(whse_code)
                    continue

                elif (not has_stock and split_enabled) or (has_stock and split_enabled):
                    new_od_dict = self.handleWarehouseSplit(
                        od,
                        qty_dict,
                        qty_dict_new,
                        code,
                        whse_code,
                        apply_reserve,
                        apply_to_process,
                    )
                    od = new_od_dict["od"]

                    if new_od_dict["new_detail"]:
                        new_order_details.append(new_od_dict["new_od"])

                else:
                    need_split = True

        # if there are any new_order_details, then add them to the list of `Detail` in the `so`
        if len(new_order_details) > 0:
            for n_od in new_order_details:
                so.Detail.Add(n_od)

        return so, (need_split and not split_enabled), qty_dict

    def handleWarehouseSplit(
        self,
        od: any,
        qty_dict: dict,
        qty_dict_new: dict,
        code: str,
        whse_code: str,
        apply_reserve: bool,
        apply_to_process: bool,
    ) -> dict:
        """
        Description:
        ---

        This function handles the warehouse splitting. The results are returned in a dict.
        """
        result = {"new_detail": False, "od": od}
        sdk, helper = pastel.getSdk(self._config)
        evo_whse = helper.GetWarehouseByCode(whse_code)

        # if the default whse partially has qty for that particular line
        # then we partially assign qty to that default whse, and update it's qty
        # NOTE: this should never be negative or zero, since the check above `if has_stock:` covers that
        # also, we ignore all zero values inside the `qty_dict`
        original_qty = int(od.Quantity)
        if qty_dict[code]["qty"] > 0 and qty_dict[code]["qty"] <= original_qty:
            od = Api.updateOrderQtyDetails(
                od,
                qty_dict[code]["qty"],
                helper.GetWarehouseByCode(qty_dict[code]["whse"]),
                apply_reserve,
                apply_to_process,
            )

        # if the product has some qty in the 'new' warehouse (`qty > 0`)
        # we should first subtract from that quantity then split it if there is still original qty left
        elif qty_dict_new[code]["qty"] > 0 and qty_dict[code]["qty"] <= 0:
            # if the old warehouse has a qty <= 0
            # we should not assign the same whse again in the with the remaining qty
            # but, instead create a single line with all qty pointing to the 'new' warehouse
            if qty_dict[code]["qty"] <= 0:
                qty_dict_new[code]["qty"] = od.Quantity

            od = Api.updateOrderQtyDetails(
                od,
                qty_dict_new[code]["qty"],
                helper.GetWarehouseByCode(qty_dict_new[code]["whse"]),
                apply_reserve,
                apply_to_process,
            )
            original_qty -= qty_dict_new[code]["qty"]

        # if there is still available qty left
        # then we assign the remaining qty to the 'new' warehouse
        # and set the respective whse on the `new_od`
        avail_qty = original_qty - qty_dict[code]["qty"]
        if avail_qty > 0:
            new_od = Api.createOrderDetailFromCurrent(
                sdk, od, evo_whse, avail_qty, apply_reserve, apply_to_process
            )
            result["new_detail"] = True
            result["new_od"] = new_od

        result["od"] = od
        return result

    def isStockAtWhse(
        self, code: str, whse_code: str, qty: int, qty_dict: dict
    ) -> tuple:
        """
        Description:
        ---

        Determines if there exists any stock at a respective warehouse
        Returns a tuple that has two elements:
            - bool: `True` if there is enough stock `>=` or `False` if there is not enough.
            - dict: results of the query (like qty avail. etc... per sku e.g:
            ```
                {
                    "sku": "qty",
                    ...
                }
            ```
        """

        query_results = self.getWhseQtyBySKUQuery(code)
        whse_qty = int(Api.sumCsvQty(query_results["qty"], [whse_code]))

        # avoid negative quantities
        if whse_qty < 0:
            whse_qty = 0

        if code not in qty_dict:
            qty_dict[code] = {"qty": whse_qty, "has_stock": True, "whse": whse_code}

        # we also want to persist the details on whether the previous whse has enough stock or not
        # inside the `qty_dict`, just to skip over any products that does not need checking.

        # if the quantity in the database is less then the qty being ordered:
        if whse_qty < qty:
            qty_dict[code]["has_stock"] = False
            return False, qty_dict

        # if it's enough we do nothing in particular
        return True, qty_dict

    def getWhseQtyBySKUQuery(self, code: str) -> dict:
        """
        Description:
        ---

        Returns the custom SQL's file results.
        """
        with pastel.openConn(self._config["dsn"]) as cursor:
            # gets the custom sql file data to query the database with
            path = os.path.join(packageDir, self._configDir, CONST_QTY_CUSTOM_QUERY)
            f = self._open(path, "r")
            sql = f.read()
            f.close()

            sql = utils.bindSqlParams(sql, {"sku": code})

            cursor.execute(sql)
            results = utils.getRowsODBC(cursor, 1)
            if len(results) > 0:
                return results[0]
            return {}

    @staticmethod
    def createOrderDetailFromCurrent(
        sdk: any,
        current_od: any,
        evo_whse: any,
        qty: int,
        apply_reserve: bool,
        apply_to_process: bool,
    ) -> any:
        """
        Returns a new `OrderDetail` sdk object that is a duplicate of the `current_od: OrderDetail`.
        Difference is that the `Quantity` & `Warehouse` properties are updated.
        """
        new_od = sdk.OrderDetail()

        # need to assign the InventoryItem proeprty/object first (there is an error if you don't)
        new_od.InventoryItem = current_od.InventoryItem

        new_od = Api.updateOrderQtyDetails(
            new_od,
            qty,
            evo_whse,
            apply_reserve,
            apply_to_process,
        )
        new_od.UnitSellingPriceForeign = current_od.UnitSellingPriceForeign
        new_od.UnitSellingPrice = current_od.UnitSellingPrice
        new_od.Discount = current_od.Discount
        new_od.TaxType = current_od.TaxType

        return new_od

    @staticmethod
    def updateOrderQtyDetails(
        od: any, qty: int, whse: any, apply_reserve: bool, apply_to_process: bool
    ) -> any:
        """
        Updates an order detail's `Quantity`, `Reserved`, `ToProcess`, and `Warehouse` properties
        Note that only if the `apply_reserve`, `apply_to_process` parameters are `True` will the respective properties
        be updated.
        """
        od.Warehouse = whse
        od.Quantity = qty

        if apply_reserve:
            od.Reserved = qty
        if apply_to_process:
            od.ToProcess = qty
        return od

    @staticmethod
    def checkParamKeyEqualsValue(params: dict, key: str, value: any) -> bool:
        """Return `True` if the respective param `key`'s value equates to the `value`
        in the params dict, otherwise `False`.

        E.g `"create_order_enabled": "true"`, will equate to `True` when the key `create_order_enabled` is passed with
            a value of `True`

        Args:
            params (dict): dictionary containing order parameters
            key (str): needle
            value (any): expected `value` to be found at the respective `key` inside the `params`

        Returns:
            bool: result
        """
        if key in params:
            if params[key] == value:
                return True
        return False
    
    def setLotsOnOrderDetails(self, so: any) -> any:
        """
        Assigns lots to order details based on available lots in the database.

        Args:
            so (object): The sales order object containing order details.

        Returns:
            object: The updated sales order object with lots assigned to order details.

        Raises:
            ValueError: If required parameters are missing or invalid.
            Exception: If an error occurs during database interaction.
        """
        params = self._getParams()

        # check if the lot_status_ids is in the params
        # and if it is not empty
        # if it is not, then we raise an error
        if "lot_status_ids" not in params or not params["lot_status_ids"]:
            raise ValueError("Missing or invalid 'lot_status_ids' parameter")

        sdk, helper = pastel.getSdk(self._config)
        for od in so.Detail:

            # check if the order detail is a lot tracked item
            # this skips the shipping line item as well
            if not od.InventoryItem.IsLotTracked:
                continue

            # retrieve the available lots for this order detail
            try:
                lots = self.getLotsForOrderDetails(
                    int(od.InventoryItem.ID),
                    int(od.Warehouse.ID),
                    str(params["lot_status_ids"])
                )
            except Exception as e:

                # Log the error and skip this order detail
                raise Exception(f"Error retrieving lots for order detail {od.InventoryItem.ID}: {e}")

            # if there are no lots available, then we skip this order detail
            if len(lots) == 0:
                continue

            try:
                self.assignLotToOrderDetail(od, lots, sdk)
            except Exception as e:

                # Log the error and skip assigning the lot
                raise Exception(f"Error assigning lot to order detail {od.InventoryItem.ID}: {e}")
        return so

    def assignLotToOrderDetail(self, od, lots, sdk):
        """
        Assigns a lot to an order detail if the lot has sufficient available quantity.

        Args:
            od (object): The order detail object containing information about the order item.
            lots (list): A list of dictionaries, where each dictionary represents a lot with
                         details such as "qty_available" and "lot_id".
            sdk (module): The SDK module used to create and manage lot details.
        """
        for lot in lots:
            if lot["qty_available"] >= od.Quantity:
                lot_detail = sdk.Lot(lot["lot_id"])
                lot_detail.InventoryItemID = od.InventoryItem.ID
                od.Lot = lot_detail

    def getLotsForOrderDetails(self, stock_id: int, whse_id: int, lot_status_ids: str) -> list:
        """
        Retrieves a list of lots for order details based on the provided stock ID, warehouse ID, 
        and lot status IDs.

        Args:
            stock_id (int): The ID of the stock item.
            whse_id (int): The ID of the warehouse.
            lot_status_ids (str): A comma-separated string of lot status IDs.

        Returns:
            list: A list of lots matching the specified criteria, retrieved from the database.
        """
        with pastel.openConn(self._config["dsn"]) as cursor:
            sql = self._getAvailableLotsPastel()
            sql = utils.bindSqlParams(sql, {
                "source_variant_code": stock_id,
                "warehouse_id": whse_id,
                "lot_status_ids": lot_status_ids
            })
            cursor.execute(sql)
            return utils.getRowsODBC(cursor, 100)

    def setOrderedDate(self, so, payload: dict, params: dict):
        """
        Sets the order date for the given sales order object (so) based on the provided payload.

        Args:
            so (`object`): The sales order object that will have its order date set.
            payload (`dict`): A dictionary containing order details, including the "system_order" key.

        Returns:
            None
        """
        order = payload["system_order"]

        # Set order date to the ordered_date and not the date the order was created in Stock2Shop.
        # check if the ordered_date is in the payload
        if "ordered_date" in order and order["ordered_date"] != "":
            ordered_date = order["ordered_date"]
            if "time_zone" in params:
                ordered_date = self._getDateByTimezone(ordered_date, params["time_zone"])

            # Slicing and concatenating
            if ordered_date[10] == ' ':
                ordered_date = ordered_date[:10] + "T" + ordered_date[11:]

            # update the order document to use the ordered_date field
            so.OrderDate = pastel.DateTime(pastel.iso8601ToTicks(ordered_date))
        return

    def _beforeOrderSave(self, so, payload):
        self._setConfig()
        params = self._getParams()

        if CONST_WHSE_SPLIT in params and "province" in params:
            self.setOrderDetailWhse(so, params)

        so = self.setLotsOnOrderDetails(so)

        order = payload["system_order"]
        address = order["shipping_address"]
        name = address["first_name"] + " " + address["last_name"] + " " + address["phone"]
        if so.DeliverTo:
            so.DeliverTo.Line1 = utils.truncateString(name, 40)
            so.DeliverTo.Line5 = utils.truncateString(address["country"] + ", " + address["zip"], 40)
            so.DeliverTo.PostalCode = ""
        
        if "use_ordered_date" in params and params["use_ordered_date"] == "true":
            self.setOrderedDate(so, payload, params)
        return

    def _getDateByTimezone(self, date_time, source_timezone):
        TIMEZONE_OFFSETS = {
            'UTC': 0,
            'Africa/Johannesburg': 2
        }

        normal_format = "%Y-%m-%dT%H:%M:%S"

        if source_timezone not in TIMEZONE_OFFSETS:
            raise ValueError(f"Unknown timezone: {source_timezone}")

        # Parse the input date assuming it's in UTC
        orderdate = datetime.strptime(date_time, normal_format)

        # Apply the timezone offset
        offset_hours = TIMEZONE_OFFSETS[source_timezone]
        converted_date = orderdate + timedelta(hours=offset_hours)

        # Format the converted date
        formatted_date = converted_date.strftime(normal_format)

        return formatted_date
