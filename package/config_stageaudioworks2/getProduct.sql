select
    oitm.ItemCode               as "source_product_code",
    oitm.ItemCode               as "source_variant_code",
    oitm.ItemName               as "title",
    oitb.ItmsGrpNam             as "collection",
    ''                          as "product_type",
    omrc.FirmName               as "vendor",
    ''                          as "variants.option1",
    ''                          as "variants.option2",
    ''                          as "variants.option3",
    ''                          as "body_html",
    oitm.IWeight1               as "variants.weight",
    oitm.CodeBars               as "variants.barcode",

    -- All price tiers
    '2'                         as "default_price_tier",
    stuff(
    (select
    convert(varchar, itm1.PriceList) + '|' +
    convert(varchar, itm1.Price) + ','
    from itm1 with (nolock)
    where itm1.ItemCode = oitm.ItemCode
    order by itm1.PriceList
    for xml path('')),1,0,'')
                                as "csv_price_tiers",

    -- All qty availability
    'WDH'                       as "default_qty_availability",
	CONCAT(

    stuff(
    (select
    convert(varchar, oitw.WhsCode) + '|' +
    convert(varchar, oitw.OnHand-oitw.IsCommited)+','
    from oitw with (nolock)
    where oitw.ItemCode = oitm.ItemCode
    order by oitw.WhsCode
    for xml path('')),1,0,'')

	,

	stuff(
    (select
    'JNB-SEN|' +
    convert(varchar, oitw.OnHand-oitw.IsCommited)+','
    from SBO_Sennheiser_M01.dbo.oitw with (nolock)
    where oitw.ItemCode = oitm.ItemCode and oitw.WhsCode='JNB'
    order by oitw.WhsCode
    for xml path('')),1,0,'')

	,

	stuff(
    (select
    'CPT-ROC|' +
    convert(varchar, oitw.OnHand-oitw.IsCommited)+','
    from SBO_RockitDistribution_M01.dbo.oitw with (nolock)
    where oitw.ItemCode = oitm.ItemCode and oitw.WhsCode='CPT'
    order by oitw.WhsCode
    for xml path('')),1,0,'')

	,

	stuff(
    (select
    'JNB-ACT|' +
    convert(varchar, oitw.OnHand-oitw.IsCommited)+','
    from SBO_ActiveMusic_M01.dbo.oitw with (nolock)
    where oitw.ItemCode = oitm.ItemCode and oitw.WhsCode='JNB'
    order by oitw.WhsCode
    for xml path('')),1,0,'')

    ,

	stuff(
    (select
    'JNB-SAW|' +
    convert(varchar, oitw.OnHand-oitw.IsCommited)+','
    from SBO_SAW_M01.dbo.oitw with (nolock)
    where oitw.ItemCode = oitm.ItemCode and oitw.WhsCode='JNB'
    order by oitw.WhsCode
    for xml path('')),1,0,'')

	,

	stuff(
    (select
    'JNB-AV|' +
    convert(varchar, oitw.OnHand-oitw.IsCommited)+','
    from SBO_AV_Dist_M02.dbo.oitw with (nolock)
    where oitw.ItemCode = oitm.ItemCode and oitw.WhsCode='JNB'
    order by oitw.WhsCode
    for xml path('')),1,0,'')

	,

	stuff(
    (select
    'CONS-AV|' +
    convert(varchar, oitw.OnHand-oitw.IsCommited)+','
    from SBO_AV_Dist_M02.dbo.oitw with (nolock)
    where oitw.ItemCode = oitm.ItemCode and oitw.WhsCode='CONS'
    order by oitw.WhsCode
    for xml path('')),1,0,'')

    ,

	stuff(
    (select
    'SERVICE|' +
    convert(varchar, oitw.OnHand-oitw.IsCommited)+','
    from SBO_Sennheiser_M01.dbo.oitw with (nolock)
    where oitw.ItemCode = oitm.ItemCode and oitw.WhsCode='SERVICE'
    order by oitw.WhsCode
    for xml path('')),1,0,'')

	)

                                as "csv_qty_availability",

    'true'                      as "variants.inventory_management",

    case when oitm.QryGroup51 = 'Y' or oitm.frozenFOR = 'Y'
        then 'false'
        else 'true'
    end                         as "product_active",

    ''                          as "tags",

    oitm.FirmCode               as "meta_item_group_code",
	oitm.QryGroup53             as "meta_channel_tal",
	oitm.QryGroup52             as "meta_channel_woo",
    oitb.ItmsGrpNam             as "meta_item_group_name",
    convert(varchar, oitm.LeadTime) + ' days'
                                as "meta_lead_time",
    oitm.SalUnitMsr             as "meta_uom",
    (
	  select 'R' + convert(varchar ,FORMAT(itm1.Price, 'N', 'en-us'))
    from itm1 with (nolock)
    where itm1.ItemCode = oitm.ItemCode and itm1.PriceList = '2'
    )                           as "meta_msrp",
	oitm.QryGroup53             as "meta_saw_na_essentials",
	oitm.QryGroup54             as "meta_mitech_shopify",
	'2021-11-24'                as "meta_full_sync",

    -- Special prices for all periods and volumes
    stuff(
    (select
    convert(varchar, ospp.CardCode) + '|' +
    convert(varchar, ospp.Discount)+','
    from ospp with (nolock)
    where ospp.ItemCode = oitm.ItemCode
    and ospp.Discount > 0
    order by ospp.ItemCode
    for xml path('')),1,0,'')
                                as 'csv_special_prices'

    -- Special prices for period join ospp with spp1
    -- Special prices for volume join ospp with spp2

from oitm with (nolock)
join oitb with (nolock) on oitm.ItmsGrpCod = oitb.ItmsGrpCod
join omrc with (nolock) on oitm.FirmCode = omrc.FirmCode
where oitm.QryGroup50 = 'Y'

and oitm.ItemCode = '%(source_variant_code)s';


