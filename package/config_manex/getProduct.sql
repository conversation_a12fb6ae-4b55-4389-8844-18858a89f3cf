SELECT
  COALESCE(NULLIF(st.ucIIWebProductCode,''), CONVERT(VARCHAR, st.StockLink)) AS "source_product_code",
  st.StockLink                                                               AS "source_variant_code",
  st.Description_1                                                           AS "title",

  -- Collection/Group: Field 1 from segment value table
  CASE
    WHEN COALESCE(st.iInvSegValue1ID,0) > 0 THEN (SELECT seg.cDescription FROM _etblInvSegValue seg WITH (NOLOCK) WHERE seg.idInvSegValue = st.iInvSegValue1ID)
    ELSE 'n/a'
  END                                                                        AS "collection",

  (
    SELECT grp.StGroup
    FROM GrpTbl grp WITH (NOLOCK)
    INNER JOIN _etblStockDetails sd WITH (NOLOCK)
        ON sd.StockID = st.StockLink
    AND grp.idGrpTbl = sd.GroupID
    AND sd.WhseID = 2
  )                                                                          AS "product_type",

  -- Vendor: Field 2 from segment value table
  CASE
    WHEN COALESCE(st.iInvSegValue2ID,0) > 0 THEN (SELECT seg.cDescription FROM _etblInvSegValue seg WITH (NOLOCK) WHERE seg.idInvSegValue = st.iInvSegValue2ID)
    ELSE 'n/a'
  END                                                                        AS "vendor",

  st.Code                                                                    AS "variants.sku",
  'size'                                                                     AS "variants.option1_name",
  st.ucIIWebSize                                                             AS "variants.option1_value",
  ''                                                                         AS "variants.option2",
  ''                                                                         AS "variants.option3",
  ''                                                                         AS "body_html",
  0                                                                          AS "variants.weight",
  (
    SELECT top 1 bc.Barcode
    FROM _etblBarcodes bc WITH (NOLOCK)
    WHERE bc.StockID = st.StockLink and bc.WhseID = 0
  )                                                                          AS "variants.barcode",
  -- CAST(ISNULL(sqm.QtyOnHand - sqm.QtyOnSO, 0) AS INTEGER)                    AS "variants.qty",
  ISNULL(retail.fExclPrice, 0)                                               AS "variants.retail_price",
  ISNULL(wholesale.fExclPrice, 0)                                            AS "variants.wholesale_price",
  ISNULL(takealot.fInclPrice, 0)                                             AS "variants.takealot_price",
  ISNULL(authorised.fExclPrice, 0)                                           AS "variants.diving_auth_price",
  ISNULL(independant.fExclPrice, 0)                                          AS "variants.diving_independant_price",
  ISNULL(premier.fExclPrice, 0)                                              AS "variants.diving_premier_price",
  'true'                                                                     AS "variants.inventory_management",

  -- Warehouse quantities
  -- Warehouses
  --   59: "DIVMAIT"
  --    8: "JHBS"
  --    2: "CPTR"
  'CPTR'                                                                     AS "default_qty_availability",
  STUFF((
    SELECT
      CONVERT(VARCHAR, whse_mst.Code) + '|' +
      CONVERT(VARCHAR, ISNULL(sq.QtyOnHand - sq.QtyOnSO, 0)) + ','
    FROM _etblStockQtys sq WITH (NOLOCK)

      LEFT JOIN WhseMst whse_mst WITH (NOLOCK)
        ON whse_mst.WhseLink = sq.WhseID
    WHERE sq.StockID = st.StockLink
    ORDER BY whse_mst.Code
    for xml path('')),1,0,'')
                                                                             AS "csv_qty_availability",

  CASE st.ulIIWebStatus
    WHEN 'active' THEN 'true'
    ELSE 'false'
  END                                                                        AS "product_active",
  ''                                                                         AS "tags"

FROM StkItem st WITH (NOLOCK)


  -- price lists
  LEFT JOIN dbo._etblPriceListPrices retail WITH (NOLOCK)
    ON retail.iStockID = st.StockLink AND
       retail.iPriceListNameID = 1 AND
       retail.iWarehouseID = 0
  LEFT JOIN dbo._etblPriceListPrices wholesale WITH (NOLOCK)
    ON wholesale.iStockID = st.StockLink AND
       wholesale.iPriceListNameID = 2 AND
       wholesale.iWarehouseID = 0
  LEFT JOIN dbo._etblPriceListPrices takealot WITH (NOLOCK)
    ON takealot.iStockID = st.StockLink AND
       takealot.iPriceListNameID = 1 AND
       takealot.iWarehouseID = 0
  LEFT JOIN dbo._etblPriceListPrices authorised WITH (NOLOCK)
    ON authorised.iStockID = st.StockLink AND
       authorised.iPriceListNameID = 9 AND
       authorised.iWarehouseID = 0
  LEFT JOIN dbo._etblPriceListPrices independant WITH (NOLOCK)
    ON independant.iStockID = st.StockLink AND
       independant.iPriceListNameID = 12 AND
       independant.iWarehouseID = 0
  LEFT JOIN dbo._etblPriceListPrices premier WITH (NOLOCK)
    ON premier.iStockID = st.StockLink AND
       premier.iPriceListNameID = 10 AND
       premier.iWarehouseID = 0

  -- qty
  -- LEFT JOIN _etblStockQtys sqm WITH (NOLOCK)
	--   ON sqm.StockID = st.StockLink
	--   AND sqm.WhseID = 2 -- Master Warehouse

WHERE st.ulIIWebStatus IN ('active', 'delete') AND st.StockLink = '%(source_variant_code)n';
