{"dbName": "local.db", "dsn": "Driver={SQL Server};Server=DESKTOP-CD3L66A\\TESTSQLSERVER;Database=Apifact;Uid=sa;Pwd=*************;", "audit_limit": 1000, "push": {"source_id": 999, "limit": 500, "token": "CRON TOKEN", "writeLog": "http://localhost:13337/writeLog", "getProducts": "http://localhost:13337/sitha/getProductsBatch", "getCustomers": "http://localhost:13337/sitha/getCustomersBatch", "countProducts": "http://localhost:13337/sitha/countProducts", "countCustomers": "http://localhost:13337/sitha/countCustomers", "getMeta": "http://localhost:13337/sitha/getMeta", "setMeta": "http://localhost:13337/sitha/setMeta", "productsQueue": "https://app.stock2shop.com/v1/products/queue", "customersQueue": "https://app.stock2shop.com/v1/customers/queue"}}