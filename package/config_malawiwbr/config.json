{"dbName": "local.db", "dsn": "Driver={SQL Server};Server=ENTERPRISESERVE\\SQLEXPRESS;Database=Buffalo Bicycle;Uid=sa;Pwd=*********;", "sdkObjectName": "Pastel.Evolution", "sdkDbInstance": "ENTERPRISESERVE\\SQLEXPRESS", "sdkDbName": "Buffalo Bicycle", "sdkDbUsername": "sa", "sdkDbPassword": "*********", "sdkCommonDbInstance": "ENTERPRISESERVE\\SQLEXPRESS", "sdkCommonDb": "<PERSON><PERSON><PERSON><PERSON>", "sdkDbCommonName": "<PERSON><PERSON><PERSON><PERSON>", "sdkDbCommonInstance": "ENTERPRISESERVE\\SQLEXPRESS", "sdkDbCommonUsername": "sa", "sdkDbCommonPassword": "*********", "licenseSerialNo": "**********", "licenseAuthCode": "1627115", "sourceProductCodeType": "n", "XdefaultEncoding": "utf-8", "audit_limit": 1000, "audit_image_limit": 100, "push": {"source_id": 1471, "limit": 500, "image_limit": 2, "token": "34a4d28944eef564edb0c259d259e280acd799c2", "writeLog": "http://localhost:13337/writeLog", "getProducts": "http://localhost:13337/evolution/getProductsBatch", "getCustomers": "http://localhost:13337/evolution/getCustomersBatch", "getImages": "http://localhost:13337/evolution/getImagesBatch", "countProducts": "http://localhost:13337/evolution/countProducts", "countCustomers": "http://localhost:13337/evolution/countCustomers", "countImages": "http://localhost:13337/evolution/countImages", "getMeta": "http://localhost:13337/evolution/getMeta", "setMeta": "http://localhost:13337/evolution/setMeta", "productsQueue": "https://app.stock2shop.com/v1/products/queue", "customersQueue": "https://app.stock2shop.com/v1/customers/queue", "imagesQueue": "https://app.stock2shop.com/v1/images/queue"}}