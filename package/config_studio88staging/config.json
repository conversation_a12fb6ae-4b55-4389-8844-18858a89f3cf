{"dbName": "local.db", "dsn": "Driver={SQL Server};Server=S88-SQL16;Database=88GER;Uid=s2s;Pwd=*$e266rboFBz5GNBWmwSSyvP15qV@S;", "audit_limit": 1000, "push": {"source_id": 610, "limit": 500, "token": "OOX*HTYGV4WWZ5QD0JG6QQS8EIUDLIVNGXDRM5RP", "writeLog": "http://localhost:13337/writeLog", "getProducts": "http://localhost:13337/odbc/getProductsBatch", "getCustomers": "http://localhost:13337/odbc/getCustomersBatch", "countProducts": "http://localhost:13337/odbc/countProducts", "countCustomers": "http://localhost:13337/odbc/countCustomers", "getMeta": "http://localhost:13337/odbc/getMeta", "setMeta": "http://localhost:13337/odbc/setMeta", "productsQueue": "https://app.stock2shop.com/v1/products/queue", "customersQueue": "https://app.stock2shop.com/v1/customers/queue"}}