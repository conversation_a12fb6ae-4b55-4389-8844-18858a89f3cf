select count(*) as count

FROM ICITEM inv WITH (NOLOCK)
-- Prices
LEFT JOIN ICPRICP pr WITH (NOLOCK)
  ON  pr.ITEMNO = inv.ITEMNO and pr.DPRICETYPE=1
LEFT JOIN ICPRICP ps WITH (NOLOCK)
  ON  ps.ITEMNO = inv.ITEMNO and ps.DPRICETYPE=2
-- Item Options
INNER JOIN ICITEMO opt WITH (NOLOCK)
  ON opt.ITEMNO = inv.ITEMNO AND lower(LTRIM(RTRIM(opt.[VALUE]))) in ('aero','paul') AND RTRIM(LOWER(opt.OPTFIELD))='web'
WHERE INV.ALLOWONWEB IN (0,1)
;