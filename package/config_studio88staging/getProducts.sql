SELECT *
FROM (
SELECT
    row_number() OVER (ORDER BY inv.ITEMNO)           AS n,

  CASE
    WHEN (RTRIM(LTRIM(inv.SEGMENT1)) <> '') THEN RTRIM(LTRIM(inv.SEGMENT1))
	  WHEN (RTRIM(LTRIM(inv.SEGMENT3)) <> '') THEN RTRIM(LTRIM(inv.SEGMENT3))
	  ELSE RTRIM(LTRIM(inv.ITEMNO))
	END
	                                                    AS "source_product_code",
    RTRIM(LTRIM(inv.ITEMNO))                          AS "source_variant_code",
    RTRIM(LTRIM(inv.[DESC]))                          AS "title",
	(select LTRIM(RTRIM(ICCATG.[DESC])) from ICCATG where ICCATG.CATEGORY = inv.CATEGORY)
                                                      AS "collection",
	(select LTRIM(RTRIM(CONVERT(VARCHAR, ICITEMO.[VALUE]))) from ICITEMO where ICITEMO.ITEMNO = inv.ITEMNO AND RTRIM(LOWER(ICITEMO.OPTFIELD))='web')
                                                      AS "product_type",
  (select LTRIM(RTRIM(CONVERT(VARCHAR, ICITEMO.[VALUE]))) from ICITEMO where ICITEMO.ITEMNO = inv.ITEMNO AND RTRIM(LOWER(ICITEMO.OPTFIELD))='web')
                                                      AS "vendor",

    CASE WHEN LTRIM(RTRIM(inv.SEGMENT2)) <> '' or LTRIM(RTRIM(inv.SEGMENT4)) <> '' or LTRIM(RTRIM(inv.SEGMENT5)) <> ''
      THEN 'size'
      ELSE ''
    END
                                                      AS "variants.option1_name",
    CASE
      WHEN (LTRIM(RTRIM(inv.SEGMENT2)) <> '') THEN (select LTRIM(RTRIM(ICSEGV.[DESC])) from ICSEGV where ICSEGV.SEGMENT=2 and ICSEGV.SEGVAL = inv.SEGMENT2)
      WHEN (LTRIM(RTRIM(inv.SEGMENT4)) <> '') THEN (select LTRIM(RTRIM(ICSEGV.[DESC])) from ICSEGV where ICSEGV.SEGMENT=4 and ICSEGV.SEGVAL = inv.SEGMENT4)
      WHEN (LTRIM(RTRIM(inv.SEGMENT5)) <> '') THEN (select LTRIM(RTRIM(ICSEGV.[DESC])) from ICSEGV where ICSEGV.SEGMENT=5 and ICSEGV.SEGVAL = inv.SEGMENT5)
      ELSE ''
    END
                                                      AS "variants.option1_value",
     ''                                                AS "variants.option2",
     ''                                                AS "variants.option3",
    replace(replace(replace(RTRIM(LTRIM(inv.ITEMNO)),' ','<>'),'><',''),'<>','_')                      
                                                      AS "variants.sku",
    ''                                                AS "body_html",
    0                                                 AS "variants.weight",
    ''                                                AS "variants.barcode",
    COALESCE((
        SELECT REPLACE(CONVERT(VARCHAR, CAST(ISNULL(pr.UNITPRICE, 0) AS FLOAT)), ',', '.')
        FROM ICPRICP pr WITH (NOLOCK)
        WHERE pr.ITEMNO = inv.ITEMNO and pr.DPRICETYPE=2
    ), '0')
                                                      AS "variants.special_price",
    'true'                                            AS "variants.inventory_management",

    CASE 
    WHEN (INV.ALLOWONWEB=1 ) then 'true'
    ELSE 'false'
    END
                                                      AS "product_active",
    ''                                                AS "tags",
	(select LTRIM(RTRIM(CONVERT(VARCHAR, ICITEMO.[VALUE]))) from ICITEMO where ICITEMO.ITEMNO = inv.ITEMNO AND RTRIM(LOWER(ICITEMO.OPTFIELD))='web' )
	                                                    AS "meta_department",
	(select LTRIM(RTRIM(CONVERT(VARCHAR, ICITEMO.[VALUE]))) from ICITEMO where ICITEMO.ITEMNO = inv.ITEMNO AND RTRIM(LOWER(ICITEMO.OPTFIELD))='web')
	                                                    AS "meta_sub_brand",

    -- Prices
	  -- https://smist08.wordpress.com/2011/08/20/tables-and-data-flow-of-the-accpac-inventory-control-module-part-1/
	  -- ICPRIC = price lists
	  -- ICPRICP = price per unit
    'RTL'                                             AS "default_price_tier",
    CONCAT('RTL|', (ISNULL( pr.UNITPRICE,0)),',special|' , CASE  WHEN (ISNULL(ps.UNITPRICE,0))=0 THEN pr.UNITPRICE ELSE ps.UNITPRICE END)
                                                      AS  "csv_price_tiers" ,

	  -- QTY
	  -- https://smist08.wordpress.com/2011/08/20/tables-and-data-flow-of-the-accpac-inventory-control-module-part-1/
	  -- ICLOC = locations
	  -- ICILOC = item qty per loc
    '020'                                             AS "default_qty_availability",
    STUFF((
      SELECT
        ',' + CONVERT(VARCHAR, LTRIM(RTRIM(ic.LOCATION))) + '|' +
              CONVERT(VARCHAR, CONVERT(INTEGER, COALESCE(icl.QTYONHAND , 0) - COALESCE(icl.QTYSALORDR, 0)))
     FROM ICILOC icl WITH (NOLOCK)
	   INNER JOIN ICLOC ic on ic.LOCATION = icl.LOCATION
      WHERE icl.ITEMNO = inv.ITEMNO
      ORDER BY LTRIM(RTRIM(ic.LOCATION))
    for xml path('')),1,1,'')
                                                      AS "csv_qty_availability",

    lower(LTRIM(RTRIM(opt.[VALUE])))                  AS "meta_channel",
        ISNULL((select ICITEMO.[VALUE] from ICITEMO where RTRIM(LOWER(OPTFIELD))='colour' AND  ICITEMO.ITEMNO = inv.ITEMNO ),'')
                                                      AS "meta_product_colour",
    
    COALESCE((
        SELECT REPLACE(CONVERT(VARCHAR, CAST(ISNULL(pr.UNITPRICE, 0) AS FLOAT)), ',', '.')
        FROM ICPRICP pr WITH (NOLOCK)
        WHERE pr.ITEMNO = inv.ITEMNO and pr.DPRICETYPE=2
    ), '0')
                                                      AS "meta_special_price",

	COALESCE((
	    SELECT
        CONVERT(VARCHAR(10), pr.SALESTART, 110)
        FROM ICPRICP pr WITH (NOLOCK)
        WHERE pr.ITEMNO = inv.ITEMNO and pr.DPRICETYPE=2
    ), '')                                            AS "meta_special_price_start",

    COALESCE((
	    SELECT
        CONVERT(VARCHAR(10), pr.SALEEND, 110)
        FROM ICPRICP pr WITH (NOLOCK)
        WHERE pr.ITEMNO = inv.ITEMNO and pr.DPRICETYPE=2
    ), '')                                            AS "meta_special_price_end"

FROM ICITEM inv WITH (NOLOCK)
-- Prices
LEFT JOIN ICPRICP pr WITH (NOLOCK)
  ON  pr.ITEMNO = inv.ITEMNO and pr.DPRICETYPE=1
LEFT JOIN ICPRICP ps WITH (NOLOCK)
  ON  ps.ITEMNO = inv.ITEMNO and ps.DPRICETYPE=2
-- Item Options
INNER JOIN ICITEMO opt WITH (NOLOCK)
  ON opt.ITEMNO = inv.ITEMNO AND lower(LTRIM(RTRIM(opt.[VALUE]))) in ('aero','paul') AND RTRIM(LOWER(opt.OPTFIELD))='web'
WHERE INV.ALLOWONWEB IN (0,1)
) as rows
where
n > '%(audit_lower_limit)n'
and n <= '%(audit_upper_limit)n'