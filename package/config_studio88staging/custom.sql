Select

CONCAT('RTL|', (ISNULL( pr.UNITPRICE,0)),',special|' , CASE  WHEN (ISNULL(ps.UNITPRICE,0))=0 THEN pr.UNITPRICE ELSE ps.UNITPRICE END)
                                                      AS  "csv_price_tiers"

FROM ICITEM inv WITH (NOLOCK)
-- Prices
LEFT JOIN ICPRICP pr WITH (NOLOCK)
  ON  pr.ITEMNO = inv.ITEMNO and pr.DPRICETYPE=1
LEFT JOIN ICPRICP ps WITH (NOLOCK)
  ON  ps.ITEMNO = inv.ITEMNO and ps.DPRICETYPE=2
WHERE replace(replace(replace(RTRIM(LTRIM(inv.ITEMNO)),' ','<>'),'><',''),'<>','_') = '16730_XXL';