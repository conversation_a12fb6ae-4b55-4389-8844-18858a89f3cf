select *
from (
SELECT
    row_number() over (order by cl.DCLink)                              AS n,

    convert(varchar, cl.Account)                                        AS 'source_customer_code',

    CASE
        WHEN CHARINDEX(' ', cl.name) > 0 THEN RTRIM(ISNULL(LEFT(cl.name, CHARINDEX(' ', cl.name) - 1), ''))
        ELSE RTRIM(ISNULL(cl.name, ''))
    END                                                                 AS 'first_name',

    CASE
        WHEN CHARINDEX(' ', cl.name) > 0 THEN LTRIM(RTRIM(ISNULL(RIGHT(cl.name, LEN(cl.name) - CHARINDEX(' ', cl.name) + 1), '')))
        ELSE ''
    END                                                                 AS 'last_name',

    cl.ucARUser_defined_2                                               AS 'email',

    CASE
        WHEN RTRIM(LTRIM(cl.ucARUser_defined_1)) = 1 THEN 'true'
        ELSE 'false'
    END                                                                 AS 'customer_active',

    0                                                                   AS 'accepts_marketing',
    cl.Physical1                                                        AS 'address.address1', -- or Address?
    cl.Physical2                                                        AS 'address.address2',
    cl.Physical3                                                        AS 'address.city',
    ''                                                                  AS 'address.country',
    ''                                                                  AS 'address.country_code',
    ''                                                                  AS 'address.province',
    ''                                                                  AS 'address.province_code',
    cl.PhysicalPC                                                       AS 'address.zip',
    cl.Name                                                             AS 'address.company',
    cl.Telephone                                                        AS 'address.phone',
    ''                                                                  AS 'meta_show_online',
    ''                                                                  AS 'meta_display_name',
    cl.Credit_Limit                                                     AS 'meta_credit_limit',
    cl.DCBalance                                                        AS 'meta_balance',
    ''                                                                  AS 'meta_branch',
    (
        select
               p.cName
        from client c
        left join _etblPriceListName p
        on c.iARPriceListNameID = p.IDPriceListName
        where c.Account = cl.Account
    )                                                                  AS 'price_tier',
    ''                                                                 AS 'qty_availability',

    -- change this depending on tax rules
    CASE cl.CT
        WHEN 4 THEN '0'
        ELSE '15'
    END                                                                 AS 'meta_tax_rate',
    cl.AutoDisc                                                         AS 'meta_discount',
   '2022-08-30'                                                         AS 'meta_activated',
    (
        select
               p.cName
        from client c
        left join _etblPriceListName p
        on c.iARPriceListNameID = p.IDPriceListName
        where c.Account = cl.Account
    )
                                                                       AS 'segment|source|products|variants.price_tiers.tier|equal',

   '0'                                                                 AS 'segment|source|products|variants.price_tiers.price|greater than'

FROM Client cl WITH (NOLOCK)
WHERE RTRIM(ISNULL(cl.ucARUser_defined_2, '')) <> ''
AND RTRIM(LTRIM(cl.ucARUser_defined_1)) in ('1', '2')
) as rows
where
n > '%(audit_lower_limit)n'
and n <= '%(audit_upper_limit)n'