import json
import imp
from .controllers import base_push as base

class Api(base.Api):
    _configDir = "config_scottishgroup"

    def reload(self):
        imp.reload(base)
        base.Api.reload(self)
        response = {
            "status": True,
            "data": {
                "reload": True
            }
        }
        return json.dumps(response, indent=self._indent)

    # Add _transformProduct here to override default
    def _afterProductTransform(self, params, result, source_product):
        if "on_lay_bye" in result and result["on_lay_bye"] is not None:
            source_product["product"]["variants"]["qty"] -= int(result["on_lay_bye"])
        return source_product
