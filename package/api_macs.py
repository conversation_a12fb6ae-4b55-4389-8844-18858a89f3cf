import win32com.client
import json
import imp
from .controllers import base_push as base
from .shared import utils
from .shared import sap_one
import collections
import os
import math

packageDir = os.path.join(os.path.realpath(os.path.join(os.getcwd(),
    os.path.dirname(__file__))))

class Api(base.Api):
    _configDir = "config_macs"

    def reload(self):
        imp.reload(base)
        base.Api.reload(self)
        response = {
            "status": True,
            "data": {
                "reload": True
            }
        }
        return json.dumps(response, indent=self._indent)

    # Add _transformProduct here to override default

    @staticmethod
    def _parseCustomerName(row):
        company = row["card_name"]
        first_name = row["first_name"]
        last_name = row["last_name"]
        return company, first_name, last_name
    
    @staticmethod
    def getProductMetaByKey(source_product: dict, key: str):
        """
        returns the specified meta that has the
        specified `key` otherwise returns an empty string
        """
        for value in source_product["product"]["meta"]:
            if(value["key"] == key):
                return value["value"]
        return ""
    
    @staticmethod
    def adjustReccProducts(metaValue: str):
        """
        returns an array of recommended products, enclosed in anchor tags, that were comma seperated
        otherwise an empty array if the meta value is an empty string (`""`)
        """
        products = ""
        for product in metaValue.split(","):
            if(product != ""):
                products += "<li><a href='/#/products?q=" + product + "'>" + product + "</a></li>"
        return products
    
    @staticmethod
    def adjustAltProducts(metaValue: str):
        """
        returns an array of alternative products, enclosed in anchor tags, that were comma seperated
        otherwise an empty array if the meta value is an empty string (`""`)
        """
        products = ""
        for product in metaValue.split(","):
            if(product != ""):
                products += "<li><a href='/#/industry/automotive/products?q=" + product + "'>" + product + "</a></li>"
        return products
    
    @staticmethod
    def adjustBOMProducts(metaValue: str):
        """
        returns an array of BOM products, enclosed in anchor tags, that were comma seperated
        otherwise returns an empty array if the meta value is an empty string (`""`)
        """

        products = ""
        for product in metaValue.split(","):
            if product != "" and product != "-":
                products += "<li><a href='/#/products?q=" + product + "'>" + product + "</a></li>"
        return products
    
    @staticmethod
    def addBodyHtml(source_product: dict):
        """
        Adds the body html to a source product by using the 
        `meta_recommended` and `meta_alternatives` products
        """
        recc_products_meta = Api.getProductMetaByKey(source_product, "recommended")
        recc_products = Api.adjustReccProducts(recc_products_meta)

        alt_products_meta = Api.getProductMetaByKey(source_product, "alternatives")
        alt_products = Api.adjustAltProducts(alt_products_meta)
        
        bom_products_meta = Api.getProductMetaByKey(source_product, "bom")
        bom_products = Api.adjustBOMProducts(bom_products_meta)

        bodyHTML = ""
        if(recc_products != ""):
            bodyHTML += "<p>Recommended Products:</p><ul>" + recc_products + "</ul>"
        if(alt_products != ""):
            bodyHTML += "<p>Alternative Products:</p><ul>" + alt_products + "</ul>"
        if (bom_products != ""):
            bodyHTML += "<p>BOM Products:</p><ul>" + bom_products + "</ul>"
        return bodyHTML

    @staticmethod
    def _afterProductTransform(source_product):

        # Adjusts the body html using the recommended 
        # and alternative products if set
        source_product["product"]["body_html"] = Api.addBodyHtml(source_product)
        
        meta = collections.OrderedDict()
        meta["key"] = "availability"

        names = collections.OrderedDict()
        names["SBY"] = "Jhb"
        names["PTA"] = "Pta"
        names["BFN"] = "Blm"
        names["CPT"] = "CPT"
        names["DBN"] = "Dbn"
        names["NLP"] = "Nlp"
        names["PLZ"] = "Plz"
        names["ERD"] = "Erd"
        names["EXP"] = "Exp"
        names["VDB"] = "Vdb"
        names["WSR"] = "Wsr"

        wh_quantities = collections.OrderedDict()
        for availability in source_product["product"]["variants"]["qty_availability"]:
            # Last 3 characters is warehouse codes
            warehouse = availability["description"][-3:]

            # If warehouse does not exist yet, initialize it with zero quantity
            if warehouse not in wh_quantities:
                wh_quantities[warehouse] = 0

            # Add this quantity to the warehouse
            qty = int(availability["qty"])
            if qty > 0:
                wh_quantities[warehouse] += qty

        # add the summed qty to warehouse "A"
        for availability in source_product["product"]["variants"]["qty_availability"]:
            prefix = availability["description"][:1]
            warehouse = availability["description"][-3:]
            if prefix.upper() == 'A':
                # Last 3 characters is warehouse codes
                availability["qty"] = wh_quantities[warehouse]

        # Build formatted string from warehouse dictionary
        values = []
        for key in wh_quantities:
            if wh_quantities[key] > 0:
                wh_name = 'Unknown'
                if key in names:
                    wh_name = names[key]
                values.append("{}: {}".format(wh_name, wh_quantities[key]))

        values.sort()

        meta["value"] = ", ".join(values)
        source_product["product"]["meta"].append(meta)

        # Create tags from specific meta values and append to current tags
        for item in source_product["product"]["meta"]:
            key = item["key"]
            value = item["value"]
            if key.startswith("oenr_tag") and value != "":
                source_product["product"]["tags"] += "," + value
        source_product["product"]["tags"] += "," + source_product["source"]["source_product_code"]


    @staticmethod
    def _afterCustomerTransform(source_customer):
        if type(source_customer["customer"]["email"]) == "str" \
            and source_customer["customer"]["email"].strip() == "":
            source_customer["customer"]["email"] = "<EMAIL>"
        if source_customer["customer"]["email"] is None:
            source_customer["customer"]["email"] = "<EMAIL>"

    def _getIBTOdbc(self):
        path = os.path.join(packageDir, self._configDir, "ibt.sql")
        f = open(path, "r")
        sql = f.read()
        f.close()
        return sql

    def _getIBT(self, sku, warehouse_code, qty):
        sql = self._getIBTOdbc()
        sql = utils.bindSqlParams(sql, {
            "sku": str(sku),
            "warehouse_code": str(warehouse_code),
            "qty": int(qty),
        })
        params = {
            "sql": sql,
            "limit": 100
        }
        with sap_one.openConn(self._config["dsn"]) as cursor:
            return utils.runODBC(params, cursor)

    def _getEMPID(self, email):
        sql = "Select empID from OHEM where email = '%(email)s'"
        sql = utils.bindSqlParams(sql, {
            "email": str(email)
        })
        params = {
            "sql": sql,
            "limit": 100
        }
        with sap_one.openConn(self._config["dsn"]) as cursor:
            return utils.runODBC(params, cursor)

    def getIBT(self):
        self._setConfig()
        params = self._getParams()
        response = utils.checkRequiredParams(
            ["sku", "warehouse_code", "qty"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)
        response = self._getIBT(params["sku"], params["warehouse_code"], params["qty"])
        return json.dumps(response, indent=self._indent)

    def _beforeCreateOrder(self, payload, params):
        """
        Process inter branch transfers where required
        :param payload:
        :return:
        """

        # Since we use card code plus contact id {card_code}-{contact_id} as source customer code,
        # We need to strip out the contact id and only use card code
        source = payload["sources"][0]
        payload["sources"][0]["original_source_customer_code"] = source["source_customer_code"]
        codes = source["source_customer_code"].split("-")
        if codes[1]:
            source["source_customer_code"] = codes[0]
        else:
            raise Exception("Invalid source_customer_code {}".format(source["source_customer_code"]))
        payload["sources"][0]["contact_person_code"] = codes[1]
        order = payload["system_order"]

        # Set contact person
        params["contact_person_code"] = codes[1]

        # Set owner
        if "console_user_email" in params and params["console_user_email"] != "":
            response = self._getEMPID(params["console_user_email"])
            if len(response["data"]["rows"]) > 0:
                if int(response["data"]["rows"][0]["empid"]) > 0:
                    params["documents_owner"] = response["data"]["rows"][0]["empid"]

        # loop through SKU's to see what needs to be transfered
        rows = []
        for line_item in order["line_items"]:
            response = self._getIBT(line_item["sku"], params["warehouse_code"], line_item["qty"])
            if len(response["data"]["rows"]) > 0:
                if int(response["data"]["rows"][0]["reqstock"]) > 0:
                    rows.append(response["data"]["rows"][0])

        # We have rows to process IBT's for
        if len(rows)>0:

            # Get company instance
            oCompany = sap_one.getCompany(self._config)

            # Try to connect
            response = sap_one.connect(oCompany)
            if not response["status"]:
                raise Exception('Failed to connect to company')

            # if oCompany.InTransaction:
            #     # 0 is commit 1 is rollback
            #     oCompany.EndTransaction('0')

            # # start transaction so we can roll back
            # oCompany.StartTransaction()

            # Create ap and ar objects
            oArInv = oCompany.GetBusinessObject(sap_one.BO_OBJECT_TYPES["oInvoices"]["code"])
            oArInv.CardCode = rows[0]["arcode"]
            oArInv.BPL_IDAssignedToInvoice = rows[0]["frombranchid"]
            oArInv.NumAtCard = ""
            oArInv.Comments = "Auto generated as part of inter branch sale."

            oApInv = oCompany.GetBusinessObject(sap_one.BO_OBJECT_TYPES["oPurchaseInvoices"]["code"])
            oApInv.CardCode = rows[0]["apcode"]
            oApInv.BPL_IDAssignedToInvoice = rows[0]["tobranchid"]
            oApInv.NumAtCard = ""
            oApInv.Comments = "Auto generated as part of inter branch sale."

            # post transfer to AR and AP accounts
            for row in rows:
                oArInv.Lines.ItemCode = row["itemcode"]
                oArInv.Lines.WarehouseCode = row["fromwhs"]
                oArInv.Lines.Quantity = row["reqstock"]
                oArInv.Lines.UnitPrice = row["avgprice"]
                oArInv.Lines.Price = row["avgprice"]
                try:
                    oArInv.Lines.Add()
                except Exception:
                    # # roll back
                    # oCompany.EndTransaction(1)
                    raise

                oApInv.Lines.ItemCode = row["itemcode"]
                oApInv.Lines.WarehouseCode = row["towhs"]
                oApInv.Lines.Quantity = row["reqstock"]
                oApInv.Lines.UnitPrice = row["avgprice"]
                oApInv.Lines.Price = row["avgprice"]
                try:
                    oApInv.Lines.Add()
                except Exception:
                    # # roll back
                    # oCompany.EndTransaction('1')
                    raise

            # add ar invoice
            try:
                retCode = oArInv.Add()
            except Exception:
                # # roll back
                # oCompany.EndTransaction('1')
                raise
            if retCode != 0:
                code, message = oCompany.GetLastError(retCode)
                code = int(math.fabs(int(code)))
                # # roll back
                # oCompany.EndTransaction('1')
                raise Exception("{} - {}".format(code, message))

            # add ap invoice
            try:
                retCode = oApInv.Add()
            except Exception:
                # # roll back
                # oCompany.EndTransaction('1')
                raise
            if retCode != 0:
                code, message = oCompany.GetLastError(retCode)
                code = int(math.fabs(int(code)))
                # # roll back
                # oCompany.EndTransaction('1')
                raise Exception("{} - {}".format(code, message))

            # # valid order
            # oCompany.EndTransaction('0')
        return payload, params

    # @staticmethod
    # def _duringOrderTransform(payload, oOrder, params):
    #     # Set contact
    #     if "contact_person_code" in payload["sources"][0]:
    #         oOrder.ContactPersonCode = payload["sources"][0]["contact_person_code"]
    #
    #     oOrder.DocumentsOwner = 13
    #     # Set owner
    #     # for meta in payload["params"]:
    #     #     if "console_user_email" in meta and meta["console_user_email"] != "":
    #     #         # fetch owner id
    #     #         sql = "Select empID from OHEM where email = '%(email)s'"
    #     #         sql = utils.bindSqlParams(sql, {
    #     #             "sku": str(meta["console_user_email"])
    #     #         })
    #     #         params = {
    #     #             "sql": sql,
    #     #             "limit": 1
    #     #         }
    #     #         with sap_one.openConn(self._config["dsn"]) as cursor:
    #     #             response = utils.runODBC(params, cursor)
    #     #         if len(response["data"]["rows"]) > 0:
    #     #             oOrder.DocumentsOwner = response["data"]["rows"][0]["empID"]

    def _afterCreateOrder(self, payload, source_order_code, source_customer_code):
        # TODO this should use the "contact_source_customer" feature
        return source_order_code, payload["sources"][0]["original_source_customer_code"]
    
    @staticmethod
    def _getSeriesNameByWarehouseCode(warehouse_code):
        if warehouse_code == "R-MAIN":
            return "R-MNS"
        return warehouse_code
