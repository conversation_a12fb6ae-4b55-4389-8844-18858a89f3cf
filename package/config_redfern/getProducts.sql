SELECT
  st.Code                                                              AS "source_product_code",
  st.StockLink                                                         AS "source_variant_code",
  st.Description_1                                                     AS "title",
  grp.Description                                                      AS "collection",
  ''                                                                   AS "product_type",
  ''                                                                   AS "vendor",
  st.Code                                                              AS "variants.sku",
  ''                                                                   AS "variants.option1",
  ''                                                                   AS "variants.option2",
  ''                                                                   AS "variants.option3",
  ''                                                                   AS "body_html",
  convert(int, st.fNetMass)                                            AS "variants.weight",
  st.Bar_code                                                          AS "variants.barcode",
  'true'                                                               AS "variants.inventory_management",
  CASE LTRIM(RTRIM(LOWER(st.uliiactivefield)))
    WHEN 'active' THEN 'true'
    ELSE 'false'
  END                                                                  AS "product_active",
  ''                                                                   AS "tags",

  '1'                                                                  AS "default_price_tier",
  stuff(
    (select
      ',' + convert(varchar, price_names.cName) + '|' +
            convert(varchar, price.fInclPrice)
    from dbo._etblPriceListPrices price WITH (NOLOCK)

      inner join _etblPriceListName price_names WITH (NOLOCK)
        ON price.iPriceListNameID = price_names.IDPriceListName AND price.iStockID = st.StockLink and price.iWarehouseID = 0

    order by price_names.cName
    for xml path('')),1,1,'')
                                                                       AS "csv_price_tiers",

  'JHB'                                                                AS "default_qty_availability",
  STUFF((
    SELECT
      CONVERT(VARCHAR, whse_mst.Code) + '|' +
      CONVERT(VARCHAR, ISNULL(whse.WHQtyOnHand, 0))
      + ','
    FROM WhseStk whse WITH (NOLOCK)

      LEFT JOIN WhseMst whse_mst WITH (NOLOCK)
        ON whse_mst.WhseLink = whse.WHWhseID
    WHERE whse.WHStockLink = st.StockLink
    ORDER BY whse_mst.Code
    for xml path('')),1,0,'')
                                                                      AS "csv_qty_availability",

  -- List of images for this product
  -- Enables automatic pulling of new images when image is added
  ISNULL(
  STUFF(
    (SELECT ',' + convert(varchar, i.idInvImage)
    FROM _etblInvImages i WITH (NOLOCK)
    WHERE st.StockLink = i.iStockLink
    ORDER BY i.idInvImage
    FOR XML PATH ('')), 1, 1, ''), '')
                                                                       AS "meta_image_id_csv"

FROM StkItem st WITH (NOLOCK)

  -- General info
  LEFT JOIN GrpTbl grp WITH (NOLOCK) ON grp.StGroup = st.ItemGroup

WHERE LTRIM(RTRIM(LOWER(st.uliiactivefield))) IN ('active','delete')

;