SELECT
  RTRIM(LTRIM(st.ucIIPatternName))                                     AS "source_product_code",
  st.Code                                                              AS "source_variant_code",
  st.ucIIPatternName                                                   AS "title",
  CONCAT(
    RTRIM(LTRIM(st.ucIIWC1)),
    CASE WHEN RTRIM(LTRIM(ISNULL(st.ucIIWC2, ''))) <> ''
        THEN '>' + RTRIM(LTRIM(st.ucIIWC2))
    END,
    CASE WHEN RTRIM(LTRIM(ISNULL(st.ucIIVendor, ''))) <> ''
        THEN '>' + RTRIM(LTRIM(st.ucIIVendor))
    END
  )                                                                    AS "collection",
  st.ucIIPatternName                                                   AS "product_type",
  st.ucIIVendor                                                        AS "vendor",
  'Size'                                                               AS "variants.option1_name",
  RTRIM(LTRIM(st.ucIITyreSize))                                        AS "variants.option1_value",
  st.Code                                                              AS "variants.sku",
  (SELECT
    (SELECT 'SIZE' AS th, 'JHB' AS th, 'CPT' AS th, 'DBN' AS th, 'PE' AS th
            FOR XML raw('tr'),ELEMENTS, TYPE) AS 'thead',
    (SELECT st2.ucIITyreSize AS td,
            -- JHB1 = 8
            (SELECT CONVERT(VARCHAR, ISNULL(WHQtyOnHand, 0)) FROM WhseStk WHERE st2.StockLink = WHStockLink AND WHWhseID = 8) AS td,
            -- CPT4 = 12
            (SELECT CONVERT(VARCHAR, ISNULL(WHQtyOnHand, 0)) FROM WhseStk WHERE st2.StockLink = WHStockLink AND WHWhseID = 12) AS td,
            -- DBN1  = 5
            (SELECT CONVERT(VARCHAR, ISNULL(WHQtyOnHand, 0)) FROM WhseStk WHERE st2.StockLink = WHStockLink AND WHWhseID = 5) AS td,
            -- PE1 = 14
            (SELECT CONVERT(VARCHAR, ISNULL(WHQtyOnHand, 0)) FROM WhseStk WHERE st2.StockLink = WHStockLink AND WHWhseID = 14) AS td
       FROM StkItem st2 WHERE RTRIM(LTRIM(st.ucIIPatternName)) = RTRIM(LTRIM(st2.ucIIPatternName))
    FOR XML RAW('tr'), ELEMENTS, TYPE
    ) AS 'tbody'
  FOR XML PATH(''), ROOT('table')
  )                                                                    AS "body_html",

  st.cSellWeight                                                       AS "variants.weight",
  convert(varchar, st.StockLink)                                       AS "variants.barcode",
  'true'                                                               AS "variants.inventory_management",
  CASE st.ucIIWebActive
    WHEN 1 THEN 'true'
    ELSE 'false'
  END                                                                  AS "product_active",
  ''                                                                   AS "tags",

  'Inland'                                                             AS "default_price_tier",
  stuff(
    (select
      ',' + convert(varchar, price_names.cName) + '|' +
            convert(varchar, price.fExclPrice)
    from dbo._etblPriceListPrices price WITH (NOLOCK)

      inner join _etblPriceListName price_names WITH (NOLOCK)
        ON price.iPriceListNameID = price_names.IDPriceListName AND price.iStockID = st.StockLink and price.iWarehouseID = 2

    order by price_names.cName
    for xml path('')),1,1,'')
                                                                       AS "csv_price_tiers",


  -- CAST(ISNULL(st.Qty_On_Hand - st.QtyOnSO, 0) AS INTEGER)              AS "variants.qty",
  'JHB1'                                                               AS "default_qty_availability",
  STUFF((
    SELECT
      CONVERT(VARCHAR, whse_mst.Code) + '|' +
      CONVERT(VARCHAR, ISNULL(whse.WHQtyOnHand, 0)) + ','
    FROM WhseStk whse WITH (NOLOCK)

      LEFT JOIN WhseMst whse_mst WITH (NOLOCK)
        ON whse_mst.WhseLink = whse.WHWhseID

    WHERE whse.WHStockLink = st.StockLink
    ORDER BY whse_mst.Code
    for xml path('')),1,0,'')
                                                                      AS "csv_qty_availability",

  -- List of images for this product
  -- Enables automatic pulling of new images when image is added
  ISNULL(
  STUFF(
    (SELECT ',' + convert(varchar, i.idInvImage)
    FROM _etblInvImages i WITH (NOLOCK)
    WHERE st.StockLink = i.iStockLink
    ORDER BY i.idInvImage
    FOR XML PATH ('')), 1, 1, ''), '')
                                                                       AS "meta_image_id_csv",

   st.ucIISDBCO                                                        AS "meta_sdb_code",
   -- st.ucIISDBCO                                                        AS "meta_qty_on_shipment", -- warehouse dbn2
   st.ucIIOTD                                                          AS "meta_original_tread_depth",
   st.ucIISectionWidth                                                 AS "meta_section_width",
   st.ucIIOverallDiameter                                              AS "meta_overall_diameter",
   st.ucIIPLR                                                          AS "meta_ply_rating",
   st.ucIITyreSize                                                     AS "meta_size",
   st.ucIILIN                                                          AS "meta_load_index",
   st.ucIIWC1                                                          AS "meta_web_class_1",
   st.ucIIWC2                                                          AS "meta_web_class_2",
   st.ucIIWC3                                                          AS "meta_web_class_3",
   st.ucIISPR                                                          AS "meta_speed_index",
  (SELECT
    (SELECT 'SIZE' AS th, 'PLY RATING' AS th, 'LOAD INDEX' AS th, 'SPEED RATING' AS th, 'TREAD DEPTH' AS th, 'SECTION WIDTH' AS th, 'OVERALL DIAMETER' AS th
            FOR XML raw('tr'),ELEMENTS, TYPE) AS 'thead',
    (SELECT st2.ucIITyreSize AS td, st2.ucIIPLR AS td, st2.ucIILIN AS td, st2.ucIISPR AS td, st2.ucIIOTD AS td, st2.ucIISectionWidth AS td, st2.ucIIOverallDiameter AS td
       FROM StkItem st2 WHERE RTRIM(LTRIM(st.ucIIPatternName)) = RTRIM(LTRIM(st2.ucIIPatternName))
    FOR XML RAW('tr'), ELEMENTS, TYPE
    ) AS 'tbody'
  FOR XML PATH(''), ROOT('table')
  )                                                                    AS "meta_long_description",
  LTRIM(RTRIM(ucIIWebActiveSRSA))                                      AS "meta_channel"


FROM StkItem st WITH (NOLOCK)
  LEFT JOIN GrpTbl grp WITH (NOLOCK) ON grp.StGroup = st.ItemGroup

WHERE st.ucIIWebActive in (1,2) and not st.ucIISDBCO is null and st.ucIIPatternName is not null and RTRIM(LTRIM(st.ucIIPatternName)) <> ''
and st.Code = '%(source_variant_code)s';