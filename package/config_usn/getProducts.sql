select
  st.Code                                           as "source_product_code",
  st.Code                                           as "source_variant_code",
  st.Description_1                                  as "title",
  st.ucIIProductCategory                            as "collection",
  'n/a'                                             as "product_type",
  'n/a'                                             as "vendor",
  ''                                                as "variants.option1",
  ''                                                as "variants.option2",
  ''                                                as "variants.option3",
  ''                                                as "body_html",
  0                                                 as "variants.weight",
  st.Bar_code                                       as "variants.barcode",
  (
     SELECT qws.WHQtyOnHand - qws.WHQtyOnSO
     FROM WhseStk qws
       INNER JOIN StkItem qs on qs.StockLink = qws.WHStockLink
     WHERE qws.WHWhseID = ws.WHWhseID and qs.code = st.Code
  )                                                 as "variants.qty",
  (
    SELECT p.fInclPrice
    FROM dbo._etblPriceListPrices p
    WHERE
      p.iStockID = st.StockLink and
      p.iPriceListNameID = 49 and
      (
        (
          ws.WHUsePriceDefs = 0 and
          p.iWarehouseID = ws.WHWhseID
        ) or
        (
          ws.WHUsePriceDefs = 1 and
          p.iWarehouseID = 0
        )
      )
  )
                                                    as "variants.retail_price",
  (
    select p.fInclPrice
    from dbo._etblPriceListPrices p
    where
      p.iStockID = st.StockLink and
      p.iPriceListNameID = 57 and
      (
        (
          ws.WHUsePriceDefs = 0 and
          p.iWarehouseID = ws.WHWhseID
        ) or
        (
          ws.WHUsePriceDefs = 1 and
          p.iWarehouseID = 0
        )
      )
  )
                                                    as "variants.wholesale_price",
  'true'                                            as "variants.inventory_management",
  CASE st.ItemActive
    WHEN 1 THEN 'true'
    ELSE 'false'
  END                                               AS "product_active",
  ''                                                as "tags"
from StkItem st
  inner join WhseStk ws
    on ws.WHStockLink = st.StockLink
  inner join WhseMst wm
    on wm.WhseLink = ws.WHWhseID
where
  wm.Code = 'ONLINE';