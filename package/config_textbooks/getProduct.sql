SELECT
    rtrim(i.ItemCode)         AS "source_product_code",
    rtrim(i.ItemCode)         AS "source_variant_code",
    rtrim(i.Description)      AS "title",
    rtrim(i.ItemCode)         AS "variants.sku",
    ''                        AS "collection",
    ''                        AS "product_type",
    ''                        AS "vendor",
    ''                        AS "variants.option1",
    ''                        AS "variants.option2",
    ''                        AS "variants.option3",
    ''                        AS "body_html",
    0                         AS "variants.weight",
    rtrim(i.ItemCode)         AS "variants.barcode",

    -- Warehouse 1 qty
    (
      (
        (wh1.OpeningQty + wh1.QtyBuyLast + wh1.QtyAdjustLast - wh1.QtySellLast) +
        (wh1.QtyBuyThis01 + wh1.QtyBuyThis02 + wh1.QtyBuyThis03 + wh1.QtyBuyThis04 +
            wh1.QtyBuyThis05 + wh1.QtyBuyThis06 + wh1.QtyBuyThis07 + wh1.QtyBuyThis08 +
            wh1.QtyBuyThis09 + wh1.QtyBuyThis10 + wh1.QtyBuyThis11 + wh1.QtyBuyThis12 +
            wh1.QtyBuyThis13) +
        (wh1.QtyAdjustThis01 + wh1.QtyAdjustThis02 + wh1.QtyAdjustThis03 +
            wh1.QtyAdjustThis04 + wh1.QtyAdjustThis05 + wh1.QtyAdjustThis06 +
            wh1.QtyAdjustThis07 + wh1.QtyAdjustThis08 + wh1.QtyAdjustThis09 +
            wh1.QtyAdjustThis10 + wh1.QtyAdjustThis11 + wh1.QtyAdjustThis12 +
            wh1.QtyAdjustThis13) -
        (wh1.QtySellThis01 + wh1.QtySellThis02 + wh1.QtySellThis03 + wh1.QtySellThis04 +
            wh1.QtySellThis05 + wh1.QtySellThis06 + wh1.QtySellThis07 + wh1.QtySellThis08 +
            wh1.QtySellThis09 + wh1.QtySellThis10 + wh1.QtySellThis11 + wh1.QtySellThis12 +
            wh1.QtySellThis13)
      )
    )                         as "qty_on_hand",
    -- (
    --   select
    --     sum(QtyLeft)

    --   from HistoryLines hl where
    --     hl.Documenttype = 102 and
    --     hl.ItemCode = i.ItemCode and
    --     hl.MultiStore = wh1.StoreCode
    -- )                         as "qty_on_sales_order",

    -- Batches
   -(ISNULL(U.SalesOrder, 0) + ISNULL(U.BatchQty, 0))
                             as "qty_on_sales_order",

    -- Note that price can be set by warehouse (store)
    '01'                      as "default_price_tier",
    (
    '01|' + REPLACE(CAST(wh1.SellIncl01 AS VARCHAR(30)), ',', '.') + ',' +
    '02|' + REPLACE(CAST(wh1.SellIncl02 AS VARCHAR(30)), ',', '.') + ',' +
    '03|' + REPLACE(CAST(wh1.SellIncl03 AS VARCHAR(30)), ',', '.') + ',' +
    '04|' + REPLACE(CAST(wh1.SellIncl04 AS VARCHAR(30)), ',', '.') + ',' +
    '05|' + REPLACE(CAST(wh1.SellIncl05 AS VARCHAR(30)), ',', '.') + ',' +
    '06|' + REPLACE(CAST(wh1.SellIncl06 AS VARCHAR(30)), ',', '.') + ',' +
    '07|' + REPLACE(CAST(wh1.SellIncl07 AS VARCHAR(30)), ',', '.') + ',' +
    '08|' + REPLACE(CAST(wh1.SellIncl08 AS VARCHAR(30)), ',', '.') + ',' +
    '09|' + REPLACE(CAST(wh1.SellIncl09 AS VARCHAR(30)), ',', '.') + ',' +
    '10|' + REPLACE(CAST(wh1.SellIncl10 AS VARCHAR(30)), ',', '.') + ',' +
    'special|' + REPLACE(CAST(wh1.SpecialPriceIncl AS VARCHAR(30)), ',', '.'))
                              as "csv_price_tiers",
    case rtrim(i.UserDefNum01)
        when 1 then 'true'
        else 'false'
    end                       as "product_active",
    ''                        as "tags",
    case rtrim(i.UserDefNum03)
        when 0 then 'true'
        else 'false'
    end                       as "variants.inventory_management",
    i.Picture                 as "picture",
    rtrim(i.UserDefNum01)     as "meta_active",
    rtrim(i.UserDefNum02)     as "meta_channel",
    rtrim(i.UserDefNum03)     as "meta_inventory_not_managed",
    rtrim(i.UserDefText01)    as "meta_user_def_text_1",
    rtrim(i.UserDefText02)    as "meta_user_def_text_2",
    rtrim(i.UserDefText03)    as "meta_user_def_text_3"

FROM Inventory i

    -- Warehouse 1
    left join MultiStoreTrn wh1 on
      wh1.ItemCode = i.ItemCode and
      wh1.StoreCode = '%(multi_store)s'

    -- Batches
   LEFT JOIN Unposted U on
     i.ItemCode = U.ItemCode AND '%(multi_store)s' = U.StoreCode

WHERE i.Physical = 1
  AND wh1.StoreCode = '%(multi_store)s'
  AND rtrim(i.UserDefNum01) in (1,2)
  AND rtrim(i.UserDefNum03) in (0,1)
  AND rtrim(i.ItemCode) = '%(source_variant_code)s'
;