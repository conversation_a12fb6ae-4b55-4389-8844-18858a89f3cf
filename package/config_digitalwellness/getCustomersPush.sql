SELECT *
FROM (
SELECT
    row_number() OVER (ORDER BY cl.DCLink)          AS n,

    convert(varchar, cl.Account)                    AS 'source_customer_code',

    CASE
        WHEN CHARINDEX(' ', cl.Contact_Person) > 0 THEN RTRIM(ISNULL(LEFT(cl.Contact_Person, CHARINDEX(' ', cl.Contact_Person) - 1), ''))
        ELSE RTRIM(ISNULL(cl.Contact_Person, ''))
    END                                             AS 'first_name',

    CASE
        WHEN CHARINDEX(' ', cl.Contact_Person) > 0 THEN LTRIM(RTRIM(ISNULL(RIGHT(cl.Contact_Person, LEN(cl.Contact_Person) - CHARINDEX(' ', cl.Contact_Person) + 1), '')))
        ELSE ''
    END                                             AS 'last_name',

    cl.EMail                                        AS 'email',

    CASE
        WHEN cl.On_Hold = 1 THEN 'false'
        ELSE 'true'
    END                                             AS 'customer_active',

    0                                               AS 'accepts_marketing',
    cl.Physical1                                    AS 'address.address1', -- or Address?
    cl.Physical2                                    AS 'address.address2',
    cl.Physical3                                    AS 'address.city',
    ''                                              AS 'address.country',
    ''                                              AS 'address.country_code',
    ''                                              AS 'address.province',
    ''                                              AS 'address.province_code',
    cl.PhysicalPC                                   AS 'address.zip',
    cl.Name                                         AS 'address.company',
    cl.Telephone                                    AS 'address.phone',
    ''                                              AS 'meta_show_online',
    ''                                              AS 'meta_display_name',
    cl.Credit_Limit                                 AS 'meta_credit_limit',
    cl.DCBalance                                    AS 'meta_balance',
    ''                                              AS 'meta_branch',
    ''                                              AS 'price_tier',
    ''                                              AS 'qty_availability',
    RTRIM(LTRIM(ISNULL(sr.Code, '')))               AS 'meta_salesrep_code',
    RTRIM(LTRIM(ISNULL(sr.Name, '')))               AS 'meta_salesrep_name',

    -- change this depending on tax rules
    CASE cl.CT
        WHEN 4 THEN '0'
        ELSE '15'
    END                                             AS 'meta_tax_rate',

    -- discount group
    cl.DCLink                                       AS 'meta_customer_id',
    cl.iClassID                                     AS 'meta_customer_group_id',
    cl.AutoDisc                                     AS 'meta_discount',

    -- CONTRACT PRICING:
    -- A contract consists of 6 parts, these are:-
    --    Order:    The hierarchy (order) in which to load the contracts. The first successful contract will be used
    --    Entity:   The entity the contract will apply to. For example, a customer would have a contract with entity “product” to apply to the product.
    --    Key:      The corresponding entities key to match on. In the above example, this would be “category”
    --    Type:     Discount or fixed. Is this contract a fixed price or discount from some other price.
    --    Value:    The value of the contract, in the case of type discount, this would be a percentage expressed as an integer. In the case of a fixed price, it would be the price,
    -- e.g. order_0|entity_product|key_product_code|value_123|type_discount~10
    -- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    -- Hierarchy:
    -- 1. ALL Customers, Customer Group, or specific Customer
    -- 2. ALL Inventory, Inventory Group, or specific Inventory Item
    -- 3. Actual discount details (discount % or unit price)
    -- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    -- Contract: Customer discount by group (percentage)
    -- Requirements:
    --   Product.meta_item_group_code
    stuff((
        SELECT
            ',order_0|entity_product|key_item_group_code|value_' + CONVERT(VARCHAR, LTRIM(RTRIM(vd.iGroupID))) + '|type_discount~' +
            CASE WHEN getdate() >= vdln.dEffDate AND getdate() <= vdln.dExpDate
                THEN CONVERT(VARCHAR, LTRIM(RTRIM(vdlvln.fPriceDisc)))
                ELSE '0'
            END
        FROM _etblVDAR vd WITH (NOLOCK)
            INNER JOIN _etblVDLnAR vdln WITH (NOLOCK)
                ON vd.IDVD = vdln.iVDID
            INNER JOIN _etblVDLnLvlAR vdlvln WITH (NOLOCK)
                ON vdln.IDVDLn = vdlvln.iVDLnID
        WHERE vd.bARAPAll = 0                   -- discount for all
            AND vd.iARAPID = 0                  -- per accounts receivable (Client.DCLink)
            AND vdlvln.fQuantity = 1            -- volume discount
            AND vdln.iStockID = 0               -- stock item (0 means it applies to all stock)
            AND vd.iGroupID = cl.iClassID       -- customer group (0 means it applies to all groups)
            AND vdln.iStGroupID = 0             -- stock group
            AND vdln.bUseStockPrc = 0           -- fixed or percentage
        ORDER BY vdln.iStockID
        FOR XML PATH ('')), 1, 1, '')
                                                    AS 'csv_contract_product_customer_group',

    -- Contract: Customer discount by group (fixed)
    -- Requirements:
    --   Product.meta_item_group_code
    stuff((
        SELECT
            ',order_0|entity_product|key_item_group_code|value_' + CONVERT(VARCHAR, LTRIM(RTRIM(vd.iGroupID))) + '|type_fixed~' +
            CASE WHEN getdate() >= vdln.dEffDate AND getdate() <= vdln.dExpDate
                THEN CONVERT(VARCHAR, COALESCE(vdlvln.fPriceDisc, 0))
                ELSE '0'
            END
        FROM _etblVDAR vd WITH (NOLOCK)
            INNER JOIN _etblVDLnAR vdln WITH (NOLOCK)
                ON vd.IDVD = vdln.iVDID
            INNER JOIN _etblVDLnLvlAR vdlvln WITH (NOLOCK)
                ON vdln.IDVDLn = vdlvln.iVDLnID
        WHERE vd.bARAPAll = 0                   -- discount for all
            AND vd.iARAPID = 0                  -- per accounts receivable (Client.DCLink)
            AND vdlvln.fQuantity = 1            -- volume discount
            AND vdln.iStockID = 0               -- stock item (0 mean it applies to all stock)
            AND vd.iGroupID = cl.iClassID       -- customer group (0 means it applies to all groups)
            AND vdln.iStGroupID = 0             -- stock group
            AND vdln.bUseStockPrc = 1           -- fixed or percentage
        ORDER BY vdln.iStockID
        FOR XML PATH ('')), 1, 1, '')
                                                    AS 'csv_contract_product_customer_group_fixed'

    -- LEGACY CONTRACT PRICING (kept here for referencing purposes only)
--    -- Discount by customer group
--    stuff(
--        (SELECT convert(VARCHAR, vdln.iStockID) + '|' +
--                convert(VARCHAR,
--                CASE WHEN getdate() >= vdln.dEffDate AND getdate() <= vdln.dExpDate
--                  THEN vdlvln.fPriceDisc
--                  ELSE 0
--                END
--                ) + ','
--         FROM _etblVDAR vd WITH (NOLOCK)
--             INNER JOIN _etblVDLnAR vdln WITH (NOLOCK)
--                 ON vd.IDVD = vdln.iVDID
--             INNER JOIN _etblVDLnLvlAR vdlvln WITH (NOLOCK)
--                 ON vdln.IDVDLn = vdlvln.iVDLnID
--         WHERE vd.bARAPAll = 0                -- discount for all
--          AND vd.iARAPID = 0                  -- per accounts receivable
--          AND vdlvln.fQuantity = 1            -- volume discount
--          AND vdln.iStockID = 0               -- stock item (0 mean it applies to all stock)
--          AND vd.iGroupID = cl.iClassID       -- customer group
--          AND vdln.iStGroupID = 0             -- stock group
--          AND vdln.bUseStockPrc = 0           -- fixed or percentage
--         ORDER BY vdln.iStockID
--         FOR XML PATH ('')), 1, 0, '')
--                                                  AS 'csv_product_customer_group',
--
--    -- Discount by customer group fixed
--    stuff(
--        (SELECT convert(VARCHAR, vdln.iStockID) + '|' +
--                convert(VARCHAR,
--                CASE WHEN getdate() >= vdln.dEffDate AND getdate() <= vdln.dExpDate
--                  THEN vdlvln.fPriceDisc
--                  ELSE 0
--                END
--                ) + ','
--         FROM _etblVDAR vd WITH (NOLOCK)
--             INNER JOIN _etblVDLnAR vdln WITH (NOLOCK)
--                 ON vd.IDVD = vdln.iVDID
--             INNER JOIN _etblVDLnLvlAR vdlvln WITH (NOLOCK)
--                 ON vdln.IDVDLn = vdlvln.iVDLnID
--         WHERE vd.bARAPAll = 0                -- discount for all
--          AND vd.iARAPID = 0                  -- per accounts receivable
--          AND vdlvln.fQuantity = 1            -- volume discount
--          AND vdln.iStockID = 0               -- stock item (0 mean it applies to all stock)
--          AND vd.iGroupID = cl.iClassID       -- customer group
--          AND vdln.iStGroupID = 0             -- stock group
--          AND vdln.bUseStockPrc = 1           -- fixed or percentage
--         ORDER BY vdln.iStockID
--         FOR XML PATH ('')), 1, 0, '')
--                                                  AS 'csv_product_customer_group_fixed'

FROM Client cl WITH (NOLOCK)
    LEFT JOIN SalesRep sr WITH (NOLOCK)
       ON sr.idSalesRep = cl.RepID
WHERE RTRIM(ISNULL(cl.EMail, '')) <> ''
) AS ROWS
WHERE
n > '%(audit_lower_limit)n'
AND n <= '%(audit_upper_limit)n'