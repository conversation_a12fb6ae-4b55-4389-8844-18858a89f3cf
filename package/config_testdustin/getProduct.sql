select
    convert(varchar, p.ProductID)
                                               as "source_product_code",
    convert(varchar, p.ProductID)
                                               as "source_variant_code",
    p.ProductName                              as "title",
    c.Description                              as "collection",
    c.CategoryName                             as "product_type",
    s.Supplier<PERSON>                             as "vendor",
    ''                                         as "variants.option1",
    ''                                         as "variants.option2",
    ''                                         as "variants.option3",
    convert(varchar, p.ProductID)
                                               as "variants.sku",
    ''                                         as "body_html",
    0                                          as "variants.weight",
    ''                                         as "variants.barcode",
    1                                          as "variants.qty",
    cast(p.Price as float)                     as "variants.retail_price",
    'true'                                     as "variants.inventory_management",
    'true'                                     as "product_active",
    ''                                         as "tags"

from products p with (nolock)

inner join categories c on c.CategoryID = p.CategoryID
inner join suppliers s on s.SupplierID = p.SupplierID

where
    p.ProductID = '%(source_variant_code)s';