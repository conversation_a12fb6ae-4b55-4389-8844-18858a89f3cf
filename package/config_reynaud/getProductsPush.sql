SELECT *
FROM (
SELECT
  row_number() over (order by st.StockLink) as n,

  st.Code                                                              AS "source_product_code",
  st.Code                                                              AS "source_variant_code",
  st.Description_1                                                     AS "title",
  (
  select sc.cCategoryName
  FROM _etblStockCategories sc WITH (NOLOCK)
  INNER JOIN _etblStockDetails sd WITH (NOLOCK)
      on sc.idStockCategories = sd.ItemCategoryID
  WHERE sd.StockID = st.StockLink AND sd.WhseID = st.WhseItem
  )                                                                    AS "collection",
  (
  SELECT grp.StGroup
  FROM GrpTbl grp
  INNER JOIN _etblStockDetails sd
  ON sd.StockID = st.StockLink
  AND grp.idGrpTbl = sd.GroupID
  AND sd.WhseID = st.WhseItem
  )                                                                    AS "product_type",
  'n/a'                                                                AS "vendor",
  st.Code                                                              AS "variants.sku",
  ''                                                                   AS "variants.option1",
  ''                                                                   AS "variants.option2",
  ''                                                                   AS "variants.option3",
  st.Description_3                                                     AS "body_html",
  0                                                                    AS "variants.weight",
  (
    SELECT top 1 bc.Barcode
    FROM _etblBarcodes bc WITH (NOLOCK)
    WHERE bc.StockID = st.StockLink and bc.WhseID = 0
  )                                                                    AS "variants.barcode",
  'true'                                                               AS "variants.inventory_management",
  CASE st.Description_2
    WHEN 1 THEN 'true'
    ELSE 'false'
  END                                                                  AS "product_active",
  ''                                                                   AS "tags",

  'Price List 9'                                                       AS "default_price_tier",
  stuff(
    (select
      ',' + convert(varchar, price_names.cName) + '|' +
            convert(varchar, price.fExclPrice)
    from dbo._etblPriceListPrices price WITH (NOLOCK)

      inner join _etblPriceListName price_names WITH (NOLOCK)
        ON price.iPriceListNameID = price_names.IDPriceListName AND price.iStockID = st.StockLink and price.iWarehouseID = 0

    order by price_names.cName
    for xml path('')),1,1,'')
                                                                       AS "csv_price_tiers",


  CAST(ISNULL(st.Qty_On_Hand - st.QtyOnSO, 0) AS INTEGER)              AS "variants.qty",
  'Mstr'                                                               AS "default_qty_availability",
  STUFF((
    SELECT
      CONVERT(VARCHAR, whse_mst.Code) + '|' +
      CONVERT(VARCHAR, ISNULL(sq.QtyOnHand - sq.QtyOnSO, 0)) + ','
    FROM _etblStockQtys sq WITH (NOLOCK)

      LEFT JOIN WhseMst whse_mst WITH (NOLOCK)
        ON whse_mst.WhseLink = sq.WhseID
    WHERE sq.StockID = st.StockLink
    ORDER BY whse_mst.Code
    for xml path('')),1,0,'')
                                                                       AS "csv_qty_availability",

  -- List of images for this product
  -- Enables automatic pulling of new images when image is added
  ISNULL(
  STUFF(
    (SELECT ',' + convert(varchar, i.idInvImage)
    FROM _etblInvImages i WITH (NOLOCK)
    WHERE st.StockLink = i.iStockLink
    ORDER BY i.idInvImage
    FOR XML PATH ('')), 1, 1, ''), '')
                                                                       AS "meta_image_id_csv"

FROM StkItem st WITH (NOLOCK)

    -- Extra meta-data
   LEFT JOIN _etblInvSegValue seg1 WITH (NOLOCK)
     ON seg1.idInvSegValue = st.iInvSegValue1ID
--   LEFT JOIN _etblInvSegValue seg2 WITH (NOLOCK)
--     ON seg2.idInvSegValue = st.iInvSegValue2ID
--   LEFT JOIN _etblInvSegValue seg3 WITH (NOLOCK)
--     ON seg3.idInvSegValue = st.iInvSegValue3ID
--   LEFT JOIN _etblInvSegValue seg4 WITH (NOLOCK)
--     ON seg4.idInvSegValue = st.iInvSegValue4ID
--   LEFT JOIN _etblInvSegValue seg5 WITH (NOLOCK)
--     ON seg5.idInvSegValue = st.iInvSegValue5ID
--   LEFT JOIN _etblInvSegValue seg6 WITH (NOLOCK)
--     ON seg6.idInvSegValue = st.iInvSegValue6ID
--   LEFT JOIN _etblInvSegValue seg7 WITH (NOLOCK)
--     ON seg7.idInvSegValue = st.iInvSegValue7ID

WHERE st.Description_2 in (1, 2)
) AS rows
WHERE n > '%(audit_lower_limit)n' AND n <= '%(audit_upper_limit)n';