select *
from (
SELECT
    row_number() over (order by cl.DC<PERSON>ink) AS n,

    CONCAT(
      convert(varchar, cl.Account), '-',
      CASE
        WHEN ad.idDelAddress is NULL THEN '0'
        ELSE convert(varchar, ad.idDelAddress)
      END
       )
                                           AS 'source_customer_code',

    CONCAT(ad1.cdeladdresscode, convert(varchar, cl.Account))
                                           AS 'first_name',

    CASE
        WHEN ad.idDelAddress is NULL THEN cl.Name
        ELSE convert(varchar, ad.cDelContact1)
    END                                    AS 'last_name',

    CASE
        WHEN ad.cemail is NULL then cl.Email
    ELSE ad.cemail
    END                                    AS 'email',

    CASE
        WHEN cl.ulARB2BActive = 1 THEN 'true'
        ELSE 'false'
    END                                    AS 'customer_active',

    0                                      AS 'accepts_marketing',

    ad.cDelAddress1                        AS 'address.address1',
    ad.cDelAddress2                        AS 'address.address2',
    ad.cDelAddress3                        AS 'address.city',
    ad.cDelAddress4                        AS 'address.country',
    ''                                     AS 'address.country_code',
    ad.cDelAddress5                        AS 'address.province',
    ''                                     AS 'address.province_code',
    ad.cDelAddressPC                       AS 'address.zip',
    ad.cdescription                        AS 'address.company',
    ad.cDelTelephone1                      AS 'address.phone',
    ''                                     AS 'meta_show_online',
    ''                                     AS 'meta_display_name',
    CAST(cl.Credit_Limit AS DECIMAL(30,2)) AS 'meta_credit_limit',
    cl.DCBalance                           AS 'meta_balance',
    ''                                     AS 'meta_branch',
    t.cDescription                         AS 'meta_terms',
    cl.ucARSegment                         AS 'meta_buying_group',
    cl.ucARMarketStore                     AS 'meta_market_store',
    cl.Client_iBranchID                    AS 'meta_branch_code',
    convert(varchar, cl.Account)           AS 'meta_customer_code',
    cl.ucARNOD                             AS 'meta_NOD',
    cl.ucARNDD                             AS 'meta_NDD',
    CASE
        WHEN cl.ucARMarketStore = 'A' THEN 1422
        WHEN cl.ucARMarketStore = 'B' THEN 1419
        WHEN cl.ucARMarketStore = 'C' THEN 1424
        WHEN cl.ucARMarketStore = 'D' THEN 1420
        WHEN cl.ucARMarketStore = 'E' THEN 1421
        WHEN cl.ucARMarketStore = 'F' THEN 1423
        WHEN cl.ucARMarketStore = 'G' THEN 1435
        WHEN cl.ucARMarketStore = 'H' THEN 1436
        WHEN cl.ucARMarketStore = 'I' THEN 1437
        WHEN cl.ucARMarketStore = 'J' THEN 1438
        --WHEN cl.ucARMarketStore = 'K' THEN 857
        --WHEN cl.ucARMarketStore = 'L' THEN 858
        WHEN cl.ucARMarketStore = 'P' THEN 1257
        WHEN cl.ucARMarketStore = 'Q' THEN 1258
        WHEN cl.ucARMarketStore = 'R' THEN 1259
        WHEN cl.ucARMarketStore = 'S' THEN 1260
        WHEN cl.ucARMarketStore = 'T' THEN 1261
        ELSE 1262
    END                                       AS 'meta_channel_id',
    RTRIM(cl.ucARDefaultWarehouseCode)
                                              AS 'meta_warehouse',
    (
      SELECT sr.Code
      FROM SalesRep sr WITH (NOLOCK)
      WHERE sr.idSalesRep = cl.RepID
    )
                                               AS 'meta_sales_rep',
    CASE
        WHEN cl.On_Hold = 1 THEN 'Dear Customer, Please note that your account is currently on hold, please contact our Accounts team to assist: 010 824 0444 (Select option 2)'
        ELSE 'ACTIVE'
    END                                        AS 'meta_account_status',
    '<p>Balance: ' +
    ISNULL(CONVERT(VARCHAR, cl.DCBalance), 'N/A') + '</p>' +
    '<p>Credit Limit: ' +
    ISNULL(CONVERT(VARCHAR,cl.Credit_Limit), 'N/A') + '</p>' +
    '<p>NDD:' +
    ISNULL(CONVERT(VARCHAR,cl.ucARNDD), 'N/A') + '</p>' +
    '<p>NOD: ' +
    ISNULL(CONVERT(VARCHAR,cl.ucARNOD), 'N/A') + '</p>'            AS 'meta_account_display',

    (
      SELECT pn.cName
      FROM _etblPriceListName pn WITH (NOLOCK)
      WHERE pn.IDPriceListName = cl.iARPriceListNameID
    )
                                                 AS 'price_tier',
    RTRIM(cl.ucARDefaultWarehouseCode)
                                                 AS 'qty_availability'

FROM Client cl WITH (NOLOCK)
inner join _etblTerms t WITH (NOLOCK) on t.iTermID = cl.iAgeingTermID
inner join _etblDelAddress ad WITH (NOLOCK) on ad.iAccountID = cl.DCLink
left join _etblDelAddressCode ad1 WITH (NOLOCK) on ad.iDelAddressCodeID = ad1.IDDelAddressCode
WHERE RTRIM(ISNULL(ad.cemail, '')) <> '' AND cl.ulARB2BActive in (1, 2)
) as rows
where
n > '%(audit_lower_limit)n'
and n <= '%(audit_upper_limit)n'