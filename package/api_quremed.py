import json
import imp
import time
from .shared import pastel_evo as pastel
from .shared import utils
from .controllers import base_push as base

class Api(base.Api):
    _configDir = "config_quremed"

    def reload(self):
        imp.reload(base)
        base.Api.reload(self)
        response = {
            "status": True,
            "data": {
                "reload": True
            }
        }
        return json.dumps(response, indent=self._indent)

    def createOrder(self):
        t1 = time.time()

        self._setConfig()

        params = self._getParams()
        response = utils.checkRequiredParams(
            ["warehouse_code", "shipping_code"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        payload = self._payload()
        try:
            # Payload might be bytes, see connector.py
            payload = payload.decode("utf-8")
        except AttributeError:
            # Assume payload is already a decoded unicode string
            pass
        try:
            payload = json.loads(payload)
        except ValueError:
            response = {
                "status": False,
                "description": "Request body is not valid json",
                "line": utils.lineNo()
            }
            return json.dumps(response, indent=self._indent)

        order = payload["system_order"]
        source = payload["sources"][0]

        source_customer_code = None

        # Check order line items are in stock if negative stock disabled
        negative_stock_disabled = False
        if "negative_stock_disabled" in params:
            negative_stock_disabled = params["negative_stock_disabled"] == "true"
        if negative_stock_disabled:
            description = ""
            descriptionTemplate = "%s x %s ordered %s available, "
            with pastel.openConn(self._config["dsn"]) as cursor:
                for line_item in order["line_items"]:
                    sql = self._getProductPastel()
                    source_variant_code = line_item["sku"]
                    source_variant_code = source_variant_code.replace("'", "");
                    sql = utils.bindSqlParams(sql, {
                        "source_variant_code": str(source_variant_code),
                        "warehouse_code": str(params["warehouse_code"])
                    })
                    cursor.execute(sql)
                    result = utils.getRowsODBC(cursor, 1)
                    if len(result) > 0:
                        product = result[0]
                        if int(line_item["qty"]) > 0 and int(line_item["qty"]) > int(product["variants.qty"]):
                            description += descriptionTemplate % (line_item["qty"], line_item["sku"], product["variants.qty"])
                    else:
                        description += "sku %s not found, " % (line_item["sku"])

            if len(description) > 0:
                response = {
                    "status": False,
                    "code": "409",
                    "description": description,
                    "line": utils.lineNo()
                }
                return json.dumps(response, indent=self._indent)

        use_credit_note_discount = False
        if "use_credit_note_discount" in params:
            use_credit_note_discount = params["use_credit_note_discount"] == "true"

        sdk, helper = pastel.getSdk(self._config)

        # WARNING Doing a try catch around everything is bad form,
        # instead of making debugging easier it hides where the exception comes from!
        # try:

        # Try to find the order customer account
        if "source_customer_code" in source:
            source_customer_code = source["source_customer_code"]


        # If contacts (or persons) are included in the source customer code,
        # we need to strip out the contact id and only use customer code.
        # The convention of source_customer_code is:
        # {customer_code}-{contact_id}
        if "contact_source_customer" in params:
            if params["contact_source_customer"] == "true":
                source["original_source_customer_code"] = source["source_customer_code"]
                codes = source["source_customer_code"].rsplit('-', 1)
                if codes[1]:
                    source_customer_code = codes[0]
                else:
                    raise Exception("Invalid source_customer_code {}".format(source["source_customer_code"]))

        pastelCustomer = self._getPastelCustomer(sdk, params, source_customer_code)
        source_customer_code = pastelCustomer.Code

        # ==== CREDIT LIMIT =====
        # check if the customer is not over terms
        # the SDK documentation explicitly states that it does not do this check
        if "check_credit_limit" in params and params["check_credit_limit"] == "true":
            credit_limit = pastelCustomer.CreditLimit
            if credit_limit > 0:
                # subtract order total + balance from credit limit
                account_balance = pastelCustomer.AccountBalance
                order_total = 0
                for line_item in order["line_items"]:
                    order_total += (line_item["qty"] * line_item["price"])
                if (credit_limit - (account_balance + order_total)) < 0:
                    raise Exception("Credit limit reached - Credit Limit: %s, Account Balance: %s, Order Total: %s" %
                                       (credit_limit, account_balance, order_total))

        # set branch if required
        if "branch_code" in params:
            branch_id = sdk.Branch.FindByCode(params["branch_code"])
            if branch_id == -1:
                raise Exception(
                    "Branch code {} not found".format(params["branch_code"]))
            # Requires pythonnet 2.1.0
            # https://github.com/pythonnet/pythonnet/issues/211
            sdk.DatabaseContext.SetBranchContext(branch_id)

        if "order_document_type" in params and params["order_document_type"] == "Quotation":
            so = sdk.SalesOrderQuotation()
        else:
            so = sdk.SalesOrder()
        so.Customer = pastelCustomer

        # Set order date
        date_created = order["created"]
        if "time_zone" in params:
            date_created = self._getDateByTimezone(date_created, params["time_zone"])


        so.OrderDate = pastel.DateTime(pastel.iso8601ToTicks(date_created))

        # due date
        if "due_date" in params:
            so.DueDate = pastel.DateTime(pastel.iso8601ToTicks(params["due_date"]))

        # delivery date
        if "delivery_date" in params:
            so.DeliveryDate = pastel.DateTime(pastel.iso8601ToTicks(params["delivery_date"]))

        # TODO Allow an array of notes?
        notes = ""
        if "notes" in order:
            # Message field only allows 255 chars
            notes = order["notes"][:255]

        # Transform notes if neccessary
        notes1 = notes
        notes2 = self._beforeNotes2Save('', order)
        notes3 = self._beforeNotes3Save('', order)

        so.MessageLine1 = notes1
        so.MessageLine2 = notes2
        so.MessageLine3 = notes3

        use_customer_address = False

        # set param use_customer_address to true or false
        # depending if param customer_tags contains 'sage-true' or 'sage-false'
        if "customer_tags" in params:
            customer_tags = params["use_customer_address"]
            if 'sage-true' in customer_tags:
                params["use_customer_address"] = 'true'
            else:
                params["use_customer_address"] = 'false'

        if "use_customer_address" in params:
            use_customer_address = (params["use_customer_address"] == "true")
        if use_customer_address:
            response = self._getCustomerDeliveryAddress(Customer = pastelCustomer)
            delivery_address = response["address"]
            a = sdk.Address()
            a.Line1 = delivery_address["Line1"]
            a.Line2 = delivery_address["Line2"]
            a.Line3 = delivery_address["Line3"]
            a.Line4 = delivery_address["Line4"]
            a.Line5 = delivery_address["Line5"]
            a.PostalCode = delivery_address["PostalCode"]
            so.DeliverTo = self._beforeAddressSave(a)

            response = self._getCustomerAddress(Customer = pastelCustomer)
            postal_address = response["address"]
            a = sdk.Address()
            a.Line1 = postal_address["Line1"]
            a.Line2 = postal_address["Line2"]
            a.Line3 = postal_address["Line3"]
            a.Line4 = postal_address["Line4"]
            a.Line5 = postal_address["Line5"]
            a.PostalCode = postal_address["PostalCode"]
            so.InvoiceTo = self._beforeAddressSave(a)

        else:
            so.DeliverTo = self._beforeAddressSave(self._getPastelAddress(sdk, order["shipping_address"]))
            so.InvoiceTo = self._beforeAddressSave(self._getPastelAddress(sdk, order["billing_address"]))

        if "external_order_no" in params:
            so.ExternalOrderNo = params["external_order_no"]

        if "order_project_code" in params:
            so.Project = sdk.Project(params["order_project_code"])

        if "delivery_method" in params:
            so.DeliveryMethod = sdk.DeliveryMethod(params["delivery_method"])

        if "order_representative_code" in params:
            code = params["order_representative_code"]
            sales_rep_id = sdk.SalesRepresentative.FindByCode(code)
            # Requires pythonnet 2.1.0
            # https://github.com/pythonnet/pythonnet/issues/211
            so.RepresentativeID = int(sales_rep_id)
            # so.Representative = sdk.SalesRepresentative(params["order_representative_code"])

        process_invoice = False
        if "process_invoice" in params:
            if params["process_invoice"] == "true":
                process_invoice = True

        useForeignCurrency = False
        if "currency_code" in params:
            # Bug: Pastel Evolution 6 does not automatically calculate the exchange rate based on the currency so we have to do it manually
            # Below steps are required to set the exchange rate for Evolution versions below 7 (tested with 6.80.45)
            # 1. Check evo version first - if < 7.x.x then just set UnitSellingPrice
            # 2. Check if customer has currency set - run code if it's set
            # 3. What happens when a customer has no currency set?? It simply uses the default
            # 4. Should the discount cents not be converted into the same currency?? No. the discount percent is the same doesn't matter the currency
            evoVersion = sdk.ComHelper().CurrentEvolutionDatabaseVersion
            evoVersion.split('.')

            # We only compare the major version (left most number)
            if float(evoVersion[0]) < 7:
                if so.Customer.IsForeignCurrencyAccount:
                    # For some reason Evo v6 has an issue setting so.Currency using so.Currency = sdk.Currency.GetByCode(params["currency_code"])
                    # despite the value of params["currency_code"] being the same as so.Customer.Currency.Code
                    so.Currency = sdk.Currency.GetByCode(so.Customer.Currency.Code)
                    useForeignCurrency = True
                    currencyID = so.Customer.CurrencyID
                    ticksDate = pastel.DateTime(pastel.iso8601ToTicks(datetime.datetime.now().isoformat()))
                    idCurrencyHist = sdk.ExchangeRate.FindLatest(currencyID, ticksDate)
                    drCurrencyHist = sdk.DatabaseContext.ExecuteCommandSingleRow(
                        "SELECT * FROM CurrencyHist WHERE idCurrencyHist = " + str(idCurrencyHist))
                    so.ExchangeRate = float(drCurrencyHist["fBuyRate"])
            else:
                so.Currency = sdk.Currency.GetByCode(params["currency_code"])

        exclusiveTotalCents = 0
        for line_item in order["line_items"]:
            od = sdk.OrderDetail()
            od.InventoryItem = helper.GetStockItem(line_item["sku"])

            add_warehouse_code = True
            if "ignore_warehouse_code" in params:
                if params["ignore_warehouse_code"] == "true":
                    add_warehouse_code = False
            if add_warehouse_code:
                if od.InventoryItem.IsWarehouseTracked:
                    od.Warehouse = helper.GetWarehouseByCode(
                        params["warehouse_code"])

            od.Quantity = line_item["qty"]

            if process_invoice:
                od.ToProcess = line_item["qty"]

            if useForeignCurrency:
                od.UnitSellingPriceForeign = line_item["price"]
                od.UnitSellingPrice = line_item["price"] * so.ExchangeRate
            else:
                od.UnitSellingPrice = line_item["price"]

            exclusiveTotalCents += round(int(line_item["qty"]) * (line_item["price"] * 100))

            tax_line = line_item["tax_lines"][0]
            taxCode = self._getTaxCode(
                params, tax_line["code"]
            )
            od.TaxType = sdk.TaxRate(taxCode)


            so.Detail.Add(od)

        add_shipping = True
        if "ignore_shipping" in params:
            if params["ignore_shipping"] == "true":
                add_shipping = False
        if "shipping_lines" in order and add_shipping:
            for shipping_line in order["shipping_lines"]:

                # the check for shipping which is has a zero price is removed.
                # we may need to create a parameter for this to set it per client
                # # only add shipping as line item if it is greater than 0
                # if int(shipping_line["price"]) > 0:
                od = sdk.OrderDetail()
                so.Detail.Add(od)

                if "gl_shipping_code" in params:
                    od.GLAccount = helper.GetGLAccount(params["gl_shipping_code"])
                else:
                    od.InventoryItem = helper.GetStockItem(params["shipping_code"])
                    if od.InventoryItem.IsWarehouseTracked:
                        od.Warehouse = helper.GetWarehouseByCode(
                            params["warehouse_code"])

                shippingQty = 1
                od.Quantity = shippingQty
                if process_invoice:
                    od.ToProcess = shippingQty

                if useForeignCurrency:
                    od.UnitSellingPriceForeign = shipping_line["price"]
                    od.UnitSellingPrice = shipping_line["price"] * so.ExchangeRate
                else:
                    od.UnitSellingPrice = shipping_line["price"]
                exclusiveTotalCents += round(shippingQty * shipping_line["price"] * 100)

                tax_line = shipping_line["tax_lines"][0]
                taxCode = self._getTaxCode(
                    params, tax_line["code"], shipping=True
                )
                od.TaxType = sdk.TaxRate(taxCode)

        # By default we let the system assign an order code
        use_channel_order_code = False
        if "use_channel_order_code" in params:
            use_channel_order_code = (params["use_channel_order_code"] == "true")
        if use_channel_order_code:
            orderFound = sdk.SalesOrder.Find(
                "OrderNum = '%s'" % order["channel_order_code"])
            if orderFound > -1:
                # Saving an existing order will duplicate line items
                raise Exception(
                    "Order already exists: %s" % order["channel_order_code"])

            so.OrderNo = order["channel_order_code"]

        # By default the discount is applied as a percentage on the order
        if "total_discount" in order:
            # Prevent division by zero
            if exclusiveTotalCents > 0 and not use_credit_note_discount:
                totalDiscountCents = round(float(order["total_discount"]) * 100)
                so.DiscountPercent = totalDiscountCents / exclusiveTotalCents * 100

        # user defined fields
        # user_field_order_[x]
        self._setUserField(so, params, "user_field_order_")

        self._beforeOrderSave(so, payload)
        so.Save()
        self._afterOrderSave(so, payload)

        # set source order code
        if "order_document_type" in params and params["order_document_type"] == "Quotation":
            source_order_code = so.QuoteNo
        else:
            source_order_code = so.OrderNo

        if "process_invoice" in params:
            if params["process_invoice"] == "true":
                so.InvoiceDate = pastel.DateTime(pastel.iso8601ToTicks(date_created))
                so.Process()

        if "complete_invoice" in params:
            if params["complete_invoice"] == "true":
                so.InvoiceDate = pastel.DateTime(pastel.iso8601ToTicks(date_created))
                so.Complete()



        # TODO we should use SQL to fetch invoice number,
        # if "use_invoice_number" in params:
        #    if params["use_invoice_number"] == "true":
        #        source_order_code = so.InvoiceNumber


        # Create credit note for discount
        if "total_discount" in order and use_credit_note_discount:
            self._createCreditNote(
                order=order,
                source_order_code=source_order_code,
                params=params,
                pastelCustomer=pastelCustomer,
                sdk=sdk,
                helper=helper
            )

        # except Exception as exc:
        #     response = {
        #         "status": False,
        #         "code": None,
        #         "description": str(exc),
        #         "data": {
        #             "source_order_code": source_order_code,
        #             "source_customer_code": source_customer_code
        #         },
        #         "line": utils.lineNo()
        #     }
        #     return json.dumps(response, indent=self._indent)

        # if contact used in source customer code, return the original source customer code, i.e.
        # {customer_code}-{contact_id}
        if "contact_source_customer" in params:
            if params["contact_source_customer"] == "true":
                source_customer_code = source["original_source_customer_code"]

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {
                "source_order_code": source_order_code,
                "source_customer_code": source_customer_code
            },
        }
        return json.dumps(response, indent=self._indent)