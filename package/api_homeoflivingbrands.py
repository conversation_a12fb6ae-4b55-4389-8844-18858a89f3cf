import json
import imp
import datetime
import collections
from .controllers import base_push as base

class Api(base.Api):
    _configDir = "config_homeoflivingbrands"

    def reload(self):
        imp.reload(base)
        base.Api.reload(self)
        response = {
            "status": True,
            "data": {
                "reload": True
            }
        }
        return json.dumps(response, indent=self._indent)

    # Add _transformProduct here to override default
    def _afterProductTransform(self, params, result, source_product):

        # Check now price, set to default price if exists
        if "meta_now" in result and result["meta_now"] is not None:
            today = datetime.date.today()
            date = self.to_integer(today)
            if int(result["meta_now_start_date"]) <= int(date) <= int(result["meta_now_end_date"]):
                source_product["product"]["variants"]["price"] = result["meta_now"]

        # for adding cost_price in price_tiers:
        if "price_tiers" not in source_product["product"]["variants"]:
            source_product["product"]["variants"]["price_tiers"] = []

        price_tier = collections.OrderedDict()
        price_tier["tier"] = "cost"
        price_tier["price"] = float("{0:.2f}".format(float(result["variants.cost_price"])))
        source_product["product"]["variants"]["price_tiers"].append(price_tier)

        return source_product

    def to_integer(self, dt_time):
        return 10000 * dt_time.year + 100 * dt_time.month + dt_time.day
