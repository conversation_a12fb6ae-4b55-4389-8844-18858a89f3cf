SELECT
    rtrim(i.ItemCode)         AS "source_product_code",
    rtrim(i.ItemCode)         AS "source_variant_code",
    rtrim(i.Description)      AS "title",
    rtrim(i.ItemCode)         AS "variants.sku",
    rtrim(ig.Description)     AS "collection",
    ''                        AS "product_type",
    ''                        AS "vendor",
    ''                        AS "variants.option1",
    ''                        AS "variants.option2",
    ''                        AS "variants.option3",
    ''                        AS "body_html",
    0                         AS "variants.weight",
    CAST((i.NettMass / 1000) AS DEC(8,6))
                              AS "meta_weight",
    rtrim(i.Barcode)          AS "variants.barcode",

    -- Warehouse 1 qty
    (
      (
        (wh1.OpeningQty + wh1.QtyBuyLast + wh1.QtyAdjustLast - wh1.QtySellLast) +
        (wh1.QtyBuyThis01 + wh1.QtyBuyThis02 + wh1.QtyBuyThis03 + wh1.QtyBuyThis04 +
            wh1.QtyBuyThis05 + wh1.QtyBuyThis06 + wh1.QtyBuyThis07 + wh1.QtyBuyThis08 +
            wh1.QtyBuyThis09 + wh1.QtyBuyThis10 + wh1.QtyBuyThis11 + wh1.QtyBuyThis12 +
            wh1.QtyBuyThis13) +
        (wh1.QtyAdjustThis01 + wh1.QtyAdjustThis02 + wh1.QtyAdjustThis03 +
            wh1.QtyAdjustThis04 + wh1.QtyAdjustThis05 + wh1.QtyAdjustThis06 +
            wh1.QtyAdjustThis07 + wh1.QtyAdjustThis08 + wh1.QtyAdjustThis09 +
            wh1.QtyAdjustThis10 + wh1.QtyAdjustThis11 + wh1.QtyAdjustThis12 +
            wh1.QtyAdjustThis13) -
        (wh1.QtySellThis01 + wh1.QtySellThis02 + wh1.QtySellThis03 + wh1.QtySellThis04 +
            wh1.QtySellThis05 + wh1.QtySellThis06 + wh1.QtySellThis07 + wh1.QtySellThis08 +
            wh1.QtySellThis09 + wh1.QtySellThis10 + wh1.QtySellThis11 + wh1.QtySellThis12 +
            wh1.QtySellThis13)
      )
    )                         as "qty_on_hand",
    (
      (
        (wh2.OpeningQty + wh2.QtyBuyLast + wh2.QtyAdjustLast - wh2.QtySellLast) +
        (wh2.QtyBuyThis01 + wh2.QtyBuyThis02 + wh2.QtyBuyThis03 + wh2.QtyBuyThis04 +
            wh2.QtyBuyThis05 + wh2.QtyBuyThis06 + wh2.QtyBuyThis07 + wh2.QtyBuyThis08 +
            wh2.QtyBuyThis09 + wh2.QtyBuyThis10 + wh2.QtyBuyThis11 + wh2.QtyBuyThis12 +
            wh2.QtyBuyThis13) +
        (wh2.QtyAdjustThis01 + wh2.QtyAdjustThis02 + wh2.QtyAdjustThis03 +
            wh2.QtyAdjustThis04 + wh2.QtyAdjustThis05 + wh2.QtyAdjustThis06 +
            wh2.QtyAdjustThis07 + wh2.QtyAdjustThis08 + wh2.QtyAdjustThis09 +
            wh2.QtyAdjustThis10 + wh2.QtyAdjustThis11 + wh2.QtyAdjustThis12 +
            wh2.QtyAdjustThis13) -
        (wh2.QtySellThis01 + wh2.QtySellThis02 + wh2.QtySellThis03 + wh2.QtySellThis04 +
            wh2.QtySellThis05 + wh2.QtySellThis06 + wh2.QtySellThis07 + wh2.QtySellThis08 +
            wh2.QtySellThis09 + wh2.QtySellThis10 + wh2.QtySellThis11 + wh2.QtySellThis12 +
            wh2.QtySellThis13)
      )
    )                         as "qty_on_hand_020",
    (
      (
        (wh3.OpeningQty + wh3.QtyBuyLast + wh3.QtyAdjustLast - wh3.QtySellLast) +
        (wh3.QtyBuyThis01 + wh3.QtyBuyThis02 + wh3.QtyBuyThis03 + wh3.QtyBuyThis04 +
            wh3.QtyBuyThis05 + wh3.QtyBuyThis06 + wh3.QtyBuyThis07 + wh3.QtyBuyThis08 +
            wh3.QtyBuyThis09 + wh3.QtyBuyThis10 + wh3.QtyBuyThis11 + wh3.QtyBuyThis12 +
            wh3.QtyBuyThis13) +
        (wh3.QtyAdjustThis01 + wh3.QtyAdjustThis02 + wh3.QtyAdjustThis03 +
            wh3.QtyAdjustThis04 + wh3.QtyAdjustThis05 + wh3.QtyAdjustThis06 +
            wh3.QtyAdjustThis07 + wh3.QtyAdjustThis08 + wh3.QtyAdjustThis09 +
            wh3.QtyAdjustThis10 + wh3.QtyAdjustThis11 + wh3.QtyAdjustThis12 +
            wh3.QtyAdjustThis13) -
        (wh3.QtySellThis01 + wh3.QtySellThis02 + wh3.QtySellThis03 + wh3.QtySellThis04 +
            wh3.QtySellThis05 + wh3.QtySellThis06 + wh3.QtySellThis07 + wh3.QtySellThis08 +
            wh3.QtySellThis09 + wh3.QtySellThis10 + wh3.QtySellThis11 + wh3.QtySellThis12 +
            wh3.QtySellThis13)
      )
    )                         as "qty_on_hand_025",
    (
      (
        (wh4.OpeningQty + wh4.QtyBuyLast + wh4.QtyAdjustLast - wh4.QtySellLast) +
        (wh4.QtyBuyThis01 + wh4.QtyBuyThis02 + wh4.QtyBuyThis03 + wh4.QtyBuyThis04 +
            wh4.QtyBuyThis05 + wh4.QtyBuyThis06 + wh4.QtyBuyThis07 + wh4.QtyBuyThis08 +
            wh4.QtyBuyThis09 + wh4.QtyBuyThis10 + wh4.QtyBuyThis11 + wh4.QtyBuyThis12 +
            wh4.QtyBuyThis13) +
        (wh4.QtyAdjustThis01 + wh4.QtyAdjustThis02 + wh4.QtyAdjustThis03 +
            wh4.QtyAdjustThis04 + wh4.QtyAdjustThis05 + wh4.QtyAdjustThis06 +
            wh4.QtyAdjustThis07 + wh4.QtyAdjustThis08 + wh4.QtyAdjustThis09 +
            wh4.QtyAdjustThis10 + wh4.QtyAdjustThis11 + wh4.QtyAdjustThis12 +
            wh4.QtyAdjustThis13) -
        (wh4.QtySellThis01 + wh4.QtySellThis02 + wh4.QtySellThis03 + wh4.QtySellThis04 +
            wh4.QtySellThis05 + wh4.QtySellThis06 + wh4.QtySellThis07 + wh4.QtySellThis08 +
            wh4.QtySellThis09 + wh4.QtySellThis10 + wh4.QtySellThis11 + wh4.QtySellThis12 +
            wh4.QtySellThis13)
      )
    )                         as "qty_on_hand_030",
    (
      (
        (wh5.OpeningQty + wh5.QtyBuyLast + wh5.QtyAdjustLast - wh5.QtySellLast) +
        (wh5.QtyBuyThis01 + wh5.QtyBuyThis02 + wh5.QtyBuyThis03 + wh5.QtyBuyThis04 +
            wh5.QtyBuyThis05 + wh5.QtyBuyThis06 + wh5.QtyBuyThis07 + wh5.QtyBuyThis08 +
            wh5.QtyBuyThis09 + wh5.QtyBuyThis10 + wh5.QtyBuyThis11 + wh5.QtyBuyThis12 +
            wh5.QtyBuyThis13) +
        (wh5.QtyAdjustThis01 + wh5.QtyAdjustThis02 + wh5.QtyAdjustThis03 +
            wh5.QtyAdjustThis04 + wh5.QtyAdjustThis05 + wh5.QtyAdjustThis06 +
            wh5.QtyAdjustThis07 + wh5.QtyAdjustThis08 + wh5.QtyAdjustThis09 +
            wh5.QtyAdjustThis10 + wh5.QtyAdjustThis11 + wh5.QtyAdjustThis12 +
            wh5.QtyAdjustThis13) -
        (wh5.QtySellThis01 + wh5.QtySellThis02 + wh5.QtySellThis03 + wh5.QtySellThis04 +
            wh5.QtySellThis05 + wh5.QtySellThis06 + wh5.QtySellThis07 + wh5.QtySellThis08 +
            wh5.QtySellThis09 + wh5.QtySellThis10 + wh5.QtySellThis11 + wh5.QtySellThis12 +
            wh5.QtySellThis13)
      )
    )                         as "qty_on_hand_050",
    (
      (
        (wh6.OpeningQty + wh6.QtyBuyLast + wh6.QtyAdjustLast - wh6.QtySellLast) +
        (wh6.QtyBuyThis01 + wh6.QtyBuyThis02 + wh6.QtyBuyThis03 + wh6.QtyBuyThis04 +
            wh6.QtyBuyThis05 + wh6.QtyBuyThis06 + wh6.QtyBuyThis07 + wh6.QtyBuyThis08 +
            wh6.QtyBuyThis09 + wh6.QtyBuyThis10 + wh6.QtyBuyThis11 + wh6.QtyBuyThis12 +
            wh6.QtyBuyThis13) +
        (wh6.QtyAdjustThis01 + wh6.QtyAdjustThis02 + wh6.QtyAdjustThis03 +
            wh6.QtyAdjustThis04 + wh6.QtyAdjustThis05 + wh6.QtyAdjustThis06 +
            wh6.QtyAdjustThis07 + wh6.QtyAdjustThis08 + wh6.QtyAdjustThis09 +
            wh6.QtyAdjustThis10 + wh6.QtyAdjustThis11 + wh6.QtyAdjustThis12 +
            wh6.QtyAdjustThis13) -
        (wh6.QtySellThis01 + wh6.QtySellThis02 + wh6.QtySellThis03 + wh6.QtySellThis04 +
            wh6.QtySellThis05 + wh6.QtySellThis06 + wh6.QtySellThis07 + wh6.QtySellThis08 +
            wh6.QtySellThis09 + wh6.QtySellThis10 + wh6.QtySellThis11 + wh6.QtySellThis12 +
            wh6.QtySellThis13)
      )
    )                         as "qty_on_hand_099",
    (
      select
        sum(QtyLeft)

      from HistoryLines hl where
        hl.Documenttype = 102 and
        hl.ItemCode = i.ItemCode and
        hl.MultiStore = wh1.StoreCode
    )                         as "qty_on_sales_order",
    (
      select
        sum(QtyLeft)

      from HistoryLines hl where
        hl.Documenttype = 102 and
        hl.ItemCode = i.ItemCode and
        hl.MultiStore = wh2.StoreCode
    )                         as "qty_on_sales_order_020",
    (
      select
        sum(QtyLeft)

      from HistoryLines hl where
        hl.Documenttype = 102 and
        hl.ItemCode = i.ItemCode and
        hl.MultiStore = wh3.StoreCode
    )                         as "qty_on_sales_order_025",
    (
      select
        sum(QtyLeft)

      from HistoryLines hl where
        hl.Documenttype = 102 and
        hl.ItemCode = i.ItemCode and
        hl.MultiStore = wh4.StoreCode
    )                         as "qty_on_sales_order_030",
    (
      select
        sum(QtyLeft)

      from HistoryLines hl where
        hl.Documenttype = 102 and
        hl.ItemCode = i.ItemCode and
        hl.MultiStore = wh5.StoreCode
    )                         as "qty_on_sales_order_050",
    (
      select
        sum(QtyLeft)

      from HistoryLines hl where
        hl.Documenttype = 102 and
        hl.ItemCode = i.ItemCode and
        hl.MultiStore = wh5.StoreCode
    )                         as "qty_on_sales_order_099",


    -- Warehouse 1 prices
    -- Note that price can be set by warehouse (store)
    '01'                      as "default_price_tier",
    (
    '01|' + REPLACE(CAST(wh5.SellExcl01 AS VARCHAR(30)), ',', '.') + ',' +
    '02|' + REPLACE(CAST(wh5.SellExcl02 AS VARCHAR(30)), ',', '.') + ',' +
    '03|' + REPLACE(CAST(wh5.SellExcl03 AS VARCHAR(30)), ',', '.') + ',' +
    '04|' + REPLACE(CAST(wh5.SellExcl04 AS VARCHAR(30)), ',', '.') + ',' +
    '05|' + REPLACE(CAST(wh5.SellExcl05 AS VARCHAR(30)), ',', '.') + ',' +
    '06|' + REPLACE(CAST(wh5.SellExcl06 AS VARCHAR(30)), ',', '.') + ',' +
    '07|' + REPLACE(CAST(wh5.SellExcl07 AS VARCHAR(30)), ',', '.') + ',' +
    '08|' + REPLACE(CAST(wh5.SellExcl08 AS VARCHAR(30)), ',', '.') + ',' +
    '09|' + REPLACE(CAST(wh5.SellExcl09 AS VARCHAR(30)), ',', '.') + ',' +
    '10|' + REPLACE(CAST(wh5.SellExcl10 AS VARCHAR(30)), ',', '.') + ',' +
    'special|' + REPLACE(CAST(wh5.SpecialPriceExcl AS VARCHAR(30)), ',', '.'))
                              as "csv_price_tiers",
    case
        when rtrim(i.UserDefNum01) = '1' then 'true'
        else 'false'
    end                       as "product_active",
    ''                        as "tags",
    'true'                    as "variants.inventory_management",
    i.Picture                 as "picture",
    rtrim(i.UserDefNum01)     as "meta_user_def_num_1",
    rtrim(i.UserDefNum02)     as "meta_user_def_num_2",
    rtrim(i.UserDefNum03)     as "meta_user_def_num_3",
    rtrim(i.UserDefText01)    as "meta_user_def_text_1",
    rtrim(i.UserDefText02)    as "meta_user_def_text_2",
    rtrim(i.UserDefText03)    as "meta_user_def_text_3"

FROM Inventory i

    -- Warehouse 1
    left join MultiStoreTrn wh1 on
      wh1.ItemCode = i.ItemCode and
      wh1.StoreCode = '%(multi_store)s'
    left join MultiStoreTrn wh2 on
      wh2.ItemCode = i.ItemCode and
      wh2.StoreCode = '020'
    left join MultiStoreTrn wh3 on
      wh3.ItemCode = i.ItemCode and
      wh3.StoreCode = '025'
    left join MultiStoreTrn wh4 on
      wh4.ItemCode = i.ItemCode and
      wh4.StoreCode = '030'
    left join MultiStoreTrn wh5 on
      wh5.ItemCode = i.ItemCode and
      wh5.StoreCode = '050'
    left join MultiStoreTrn wh6 on
      wh6.ItemCode = i.ItemCode and
      wh6.StoreCode = '099'

    left join InventoryGroups ig on
      ig.InvGroup = wh1.InvGroup

    -- Batches
--    LEFT JOIN Unposted U on
--      i.ItemCode = U.ItemCode AND '%(multi_store)s' = U.StoreCode

WHERE rtrim(i.ItemCode) = '%(sku)s'
;