SELECT
    RTRIM(LTRIM(inv.StockCode))                       AS "source_product_code",
    RTRIM(LTRIM(inv.StockCode))                       AS "source_variant_code",
    RTRIM(LTRIM(inv.Description))                     AS "title",
    RTRIM(LTRIM(inv.ProductClass))                     AS "collection",
    ''                                                AS "product_type",
    RTRIM(LTRIM(inv.DrawOfficeNum))                   AS "vendor",
    RTRIM(LTRIM(inv.StockCode))                       AS "variants.sku",
    ISNULL(
        STUFF((
            SELECT '|' + (oemc.OEMPartNumber)
            FROM SysproCompanyRWI.dbo.OEMCrossReference oemc WITH (NOLOCK)
            WHERE oemc.StockCode = inv.StockCode
            ORDER BY LTRIM(RTRIM(oemc.OEMPartNumber))
            for xml path('')),1,1,''
        )
        + '|' +
        (
            SELECT oemem.OEMEngineName
            FROM SysproCompanyRWI.dbo.OEMEngineMaster oemem WITH (NOLOCK)
            WHERE oemc1.OEMEngine = oemem.OEMIndex
        ),
	'')
	                                                  AS "body_html",
    CAST(ISNULL(inv.Mass, 0) AS INTEGER)              AS "variants.weight",
    inv.AlternateKey1                                 AS "variants.barcode",
    CASE LOWER(inv2.WebActive)
	WHEN 'active' THEN 'true'
	ELSE 'false'
	END                                                 AS "product_active",
    'true'                                            AS "variants.inventory_management",
    ISNULL(
        STUFF((
            SELECT ',' + (oemc.OEMPartNumber)
            FROM SysproCompanyRWI.dbo.OEMCrossReference oemc WITH (NOLOCK)
            WHERE oemc.StockCode = inv.StockCode
            ORDER BY LTRIM(RTRIM(oemc.OEMPartNumber))
            for xml path('')),1,1,''
        )
        + ',' +
        (
            SELECT oemem.OEMEngineName
            FROM SysproCompanyRWI.dbo.OEMEngineMaster oemem WITH (NOLOCK)
            WHERE oemc1.OEMEngine = oemem.OEMIndex
        ),
    '')
	                                                  AS "tags",

    -- Prices
    'A'                                               AS "default_price_tier",
    STUFF((
      SELECT
        ',' + CONVERT(VARCHAR, LTRIM(RTRIM(price_tiers.PriceCode))) + '|' +
              CONVERT(VARCHAR, CAST(ISNULL(price_tiers.SellingPrice, 0) AS FLOAT))
      FROM InvPrice price_tiers WITH (NOLOCK)
      WHERE price_tiers.StockCode = inv.StockCode
      ORDER BY LTRIM(RTRIM(price_tiers.PriceCode))
    for xml path('')),1,1,'')
                                                      AS "csv_price_tiers",

    'FL'                                              AS "default_qty_availability",
    STUFF((
      SELECT
        ',' + CONVERT(VARCHAR, LTRIM(RTRIM(whse.Warehouse))) + '|' +
              CONVERT(VARCHAR, CONVERT(INTEGER, COALESCE(whse.QtyOnHand, 0) - COALESCE(whse.QtyAllocated, 0)))
      FROM InvWarehouse whse WITH (NOLOCK)
      WHERE whse.StockCode = inv.StockCode
      ORDER BY LTRIM(RTRIM(whse.Warehouse))
    for xml path('')),1,1,'')
                                                      AS "csv_qty_availability",

    -- If FixedPriceCode is set, use discount 1
    STUFF((
      SELECT
      ',' + CONVERT(VARCHAR, LTRIM(RTRIM(sor.CustomerBuyGrp))) + '|' + CONVERT(VARCHAR, LTRIM(RTRIM(sor.FixedPriceCode))) + '-' + CONVERT(VARCHAR, COALESCE(sor.Discount1, 0))
      FROM SorContractPrice sor WITH (NOLOCK)
      WHERE sor.StockCode = inv.StockCode
      AND sor.ContractType='B'
      AND sor.PriceMethod in ('P', 'C')
      AND (DATEADD(day, DATEDIFF(day,0,GETDATE()),0) <= ExpiryDate or ExpiryDate is NULL )
      AND DATEADD(day, DATEDIFF(day,0,GETDATE()),0) >= StartDate
      ORDER BY LTRIM(RTRIM(sor.CustomerBuyGrp))
    for xml path('')),1,1,'')
                                                      AS "csv_discount_groups",

    -- If FixedPriceCode is empty, uses fixedPrice
    STUFF((
      SELECT
        ',' + CONVERT(VARCHAR, LTRIM(RTRIM(sor.CustomerBuyGrp))) + '|' + CONVERT(VARCHAR, COALESCE(sor.FixedPrice, 0))
      FROM SorContractPrice sor WITH (NOLOCK)
      WHERE sor.StockCode = inv.StockCode
      AND sor.ContractType='B'
      AND sor.PriceMethod='F'
      AND (DATEADD(day, DATEDIFF(day,0,GETDATE()),0) <= ExpiryDate or ExpiryDate is NULL )
      AND DATEADD(day, DATEDIFF(day,0,GETDATE()),0) >= StartDate
      ORDER BY LTRIM(RTRIM(sor.CustomerBuyGrp))
    for xml path('')),1,1,'')
                                                      AS "csv_discount_groups_fixed",

    '0'                                               AS "meta_tax_rate",
    inv2.OemEngine                                    AS "meta_oem_engine",
    inv2.EnginePlatform                               AS "meta_engine_platform",
    STUFF((
      SELECT
        ' ' + CONVERT(VARCHAR, LTRIM(RTRIM(whse.Warehouse))) + ':' +
              CONVERT(VARCHAR, CONVERT(INTEGER, COALESCE(whse.QtyOnHand, 0) - COALESCE(whse.QtyAllocated, 0)))
      FROM InvWarehouse whse WITH (NOLOCK)
      WHERE whse.StockCode = inv.StockCode
      ORDER BY LTRIM(RTRIM(whse.Warehouse))
    for xml path('')),1,1,'')
                                                      AS "meta_warehouses",
    ISNULL(
        STUFF((
            SELECT
            '|' + (oemc.OEMPartNumber)
            FROM SysproCompanyRWI.dbo.OEMCrossReference oemc WITH (NOLOCK)
            WHERE oemc.StockCode = inv.StockCode
            ORDER BY LTRIM(RTRIM(oemc.OEMPartNumber))
            for xml path('')),1,1,''
        )
        + '|' +
        (
            SELECT oemem.OEMEngineName
            FROM SysproCompanyRWI.dbo.OEMEngineMaster oemem WITH (NOLOCK)
            WHERE oemc1.OEMEngine = oemem.OEMIndex
        ),
    '')                                               AS "meta_longdescription1"


FROM InvMaster inv WITH (NOLOCK)

    -- Base Data
    LEFT JOIN [InvMaster+] inv2 WITH (nolock)
    ON inv.StockCode = inv2.StockCode

    LEFT JOIN AdmTax atx WITH (nolock)
    ON atx.TaxCode = inv.TaxCode

    LEFT JOIN SysproCompanyRWI.dbo.OEMCrossReference oemc1 WITH (NOLOCK)
	ON oemc1.StockCode = inv.StockCode

Where inv.StockCode = '%(sku)s';