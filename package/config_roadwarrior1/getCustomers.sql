select *
from (
select
    row_number() over (order by ar.Customer) as n,

    convert(varchar, ar.Customer)            as 'source_customer_code',

    convert(varchar, ar.Customer)            as 'first_name',
    ar.Name                                  as 'last_name',
    ar2.Email                                as 'email',
    CASE LOWER(ar2.WebActive)
      WHEN 'active'
      THEN 'true'
      ELSE 'false'
    END                                      as 'customer_active',
    1                                        as 'accepts_marketing',
    ar.ShipToAddr1                           as 'address.address2',
    ar.ShipToAddr2 +
    CASE LTRIM(RTRIM(ar.ShipToAddr3Loc))
    WHEN '' THEN ''
    ELSE ', ' + ar.ShipToAddr3Loc
    END                                      as 'address.address1',
    ar.ShipToAddr3                           as 'address.city',
    ar.ShipToAddr5                           as 'address.country',
    ''                                       as 'address.country_code',
    ar.ShipToAddr4                           as 'address.province',
    ''                                       as 'address.province_code',
    ar.ShipPostalCode                        as 'address.zip',
    ar.Name                                  as 'address.company',
    ar.Telephone                             as 'address.phone',
    ar.BuyingGroup1                          as 'meta_customer_group',
    ar.ShippingInstrs                        as 'meta_shipping_instructions',
    ar.Salesperson                           as 'meta_sales_person',
    ar.CreditLimit                           as 'meta_credit_limit',
    ar.Branch                                as 'meta_branch',
    ''                                       as 'meta_terms',
    COALESCE(di.DiscountPct1, 0)             as 'meta_discount',
    ar.Area                                  as 'meta_area',
    '$' + convert(VARCHAR,convert(MONEY, arb.CurrentBalance1), 1)
                                             as 'meta_balance',
    '$' + convert(VARCHAR,convert(MONEY, arb.Val120daysInv), 1)
                                             as 'meta_balance_120',
    '$' + convert(VARCHAR,convert(MONEY, arb.Val90daysInv), 1)
                                             as 'meta_balance_90',
    '$' + convert(VARCHAR,convert(MONEY, arb.Val60daysInv), 1)
                                             as 'meta_balance_60',
    '$' + convert(VARCHAR,convert(MONEY, arb.Val30daysInv), 1)
                                             as 'meta_balance_30',
    '$' + convert(VARCHAR,convert(MONEY, arb.ValCurrentInv), 1)
                                             as 'meta_balance_current',
    '$' + convert(VARCHAR,convert(MONEY, ar.CreditLimit - arb.CurrentBalance1), 1)
                                             as 'meta_credit_available',

    ar.PriceCode                             as 'price_tier',
    ar.SalesWarehouse                        as 'qty_availability',

    STUFF((
      SELECT
      ',' + CONVERT(VARCHAR, LTRIM(RTRIM(sor.StockCode))) + '|' + CONVERT(VARCHAR, LTRIM(RTRIM(sor.FixedPriceCode))) + '-' + CONVERT(VARCHAR, COALESCE(sor.Discount1, 0))
      FROM SorContractPrice sor WITH (NOLOCK)
      WHERE sor.CustomerBuyGrp = ar.Customer
      AND sor.ContractType='C'
      AND sor.PriceMethod in ('P', 'C')
      AND (DATEADD(day, DATEDIFF(day,0,GETDATE()),0) <= ExpiryDate or ExpiryDate is NULL )
      AND DATEADD(day, DATEDIFF(day,0,GETDATE()),0) >= StartDate
      ORDER BY LTRIM(RTRIM(sor.StockCode))
    for xml path('')),1,1,'')
                                             AS "csv_special_prices",

    STUFF((
      SELECT
        ',' + CONVERT(VARCHAR, LTRIM(RTRIM(sor.StockCode))) + '|' + CONVERT(VARCHAR, COALESCE(sor.FixedPrice, 0))
      FROM SorContractPrice sor WITH (NOLOCK)
      WHERE sor.CustomerBuyGrp = ar.Customer
      AND sor.ContractType='C'
      AND sor.PriceMethod = 'F'
      AND (DATEADD(day, DATEDIFF(day,0,GETDATE()),0) <= ExpiryDate or ExpiryDate is NULL )
      AND DATEADD(day, DATEDIFF(day,0,GETDATE()),0) >= StartDate
      ORDER BY LTRIM(RTRIM(sor.StockCode))
    for xml path('')),1,1,'')
                                             AS "csv_special_prices_fixed",

    '891'                                    AS "meta_channel_id"

 -- TODO RWA is USA, RWI is Canada


FROM ArCustomer ar WITH (nolock)
 INNER JOIN ArCustomerBal arb WITH (nolock)
 ON arb.Customer = ar.Customer
 LEFT JOIN [ArCustomer+] ar2 WITH (nolock)
 ON ar2.Customer = ar.Customer
LEFT JOIN [TblSoDiscount] di WITH (nolock)
 ON di.DiscountCode = ar.LineDiscCode

  WHERE LOWER(ar2.WebActive) in ('active', 'delete') AND ISNULL(ar2.Email, '') <> ''

) as rows
where
n > '%(audit_lower_limit)n'
and n <= '%(audit_upper_limit)n'