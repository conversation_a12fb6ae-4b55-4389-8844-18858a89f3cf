select

    convert(varchar, ar.Customer)            as 'source_customer_code',

    ar.Name                                  as 'first_name',
    ''                                       as 'last_name',
    ar2.B2bEmail                             as 'email',
    CASE
      WHEN ar2.B2B='Y' and ar2.B2bEmail <> '' and not ar2.B2bEmail is null
      THEN 'true'
      ELSE 'false'
    END                                      as 'customer_active',
    1                                        as 'accepts_marketing',
    ar.ShipToAddr1                           as 'address.address1',
    ar.ShipToAddr2 +
    CASE LTRIM(RTRIM(ar.ShipToAddr3Loc))
    WHEN '' THEN ''
    ELSE ', ' + ar.ShipToAddr3Loc
    END                                      as 'address.address2',
    ar.ShipToAddr3                           as 'address.city',
    ar.ShipToAddr5                           as 'address.country',
    ''                                       as 'address.country_code',
    ar.ShipToAddr4                           as 'address.province',
    ''                                       as 'address.province_code',
    ar.ShipPostalCode                        as 'address.zip',
    ar.Name                                  as 'address.company',
    ar.Telephone                             as 'address.phone',
    ar.BuyingGroup1                          as 'meta_customer_group',
    ar.ShippingInstrs                        as 'meta_shipping_instructions',
    rtrim(ar.Salesperson)                    as 'meta_sales_person',
    rtrim(sp.Salesperson)                    as 'meta_sales_rep_code',
    rtrim(ISNULL(sp.Name,''))       as 'meta_sales_rep_name',
    rtrim(art.TermsCode)                     as 'meta_payment_terms_code',
    rtrim(art.Description)                   as 'meta_payment_terms_description',
--     case ar.TermsCode
--      when 3 then '[
--   {
-- 	"method": "MygateVirtual",
-- 	"description": "Card Payment",
-- 	"url": "https://apiv2.wirecard.co.za/product/payment/v1/initialisevirtual",
-- 	"merchant_id": "655943EB-29E6-4F5C-B396-DB8B4028BF1A",
-- 	"application_id": "25E04642-0CE1-447E-BF24-D5D05D4B0FF7",
-- 	"mode": 1
-- }]'
--      else '[{
-- 	"method": "OnAccount",
-- 	"description": "On Account"
-- }, {
-- 	"method": "MygateVirtual",
-- 	"description": "Card Payment",
-- 	"url": "https://apiv2.wirecard.co.za/product/payment/v1/initialisevirtual",
-- 	"merchant_id": "655943EB-29E6-4F5C-B396-DB8B4028BF1A",
-- 	"application_id": "25E04642-0CE1-447E-BF24-D5D05D4B0FF7",
-- 	"mode": 1
-- }]'
--      end                                      as 'meta_payment_methods',
    -- ''                                       AS 'meta_payment_methods',

    ar.CreditLimit                           as 'meta_credit_limit',
    ar.Branch                                as 'meta_branch',
    ''                                       as 'meta_terms',
    COALESCE(di.DiscountPct1, 0)             as 'meta_discount',
    ar.Area                                  as 'meta_area',
    'R' + convert(VARCHAR,convert(MONEY, arb.CurrentBalance1), 1)
                                             as 'meta_balance',
    'R' + convert(VARCHAR,convert(MONEY, arb.Val120daysInv), 1)
                                             as 'meta_balance_120',
    'R' + convert(VARCHAR,convert(MONEY, arb.Val90daysInv), 1)
                                             as 'meta_balance_90',
    'R' + convert(VARCHAR,convert(MONEY, arb.Val60daysInv), 1)
                                             as 'meta_balance_60',
    'R' + convert(VARCHAR,convert(MONEY, arb.Val30daysInv), 1)
                                             as 'meta_balance_30',
    'R' + convert(VARCHAR,convert(MONEY, arb.ValCurrentInv), 1)
                                             as 'meta_balance_current',
    'R' + convert(VARCHAR,convert(MONEY, ar.CreditLimit - arb.CurrentBalance1), 1)
                                             as 'meta_credit_available',

    ar.PriceCode                             as 'price_tier',
    ar.Area                                  as 'qty_availability',

    STUFF((
      SELECT
      ',' + CONVERT(VARCHAR, LTRIM(RTRIM(sor.StockCode))) + '|' + CONVERT(VARCHAR, LTRIM(RTRIM(sor.FixedPriceCode))) + '-' + CONVERT(VARCHAR, COALESCE(sor.Discount1, 0))
      FROM SorContractPrice sor WITH (NOLOCK)
      WHERE sor.CustomerBuyGrp = ar.Customer
      AND sor.ContractType='C'
      AND sor.PriceMethod in ('P', 'C')
      AND (DATEADD(day, DATEDIFF(day,0,GETDATE()),0) <= ExpiryDate or ExpiryDate is NULL )
      AND DATEADD(day, DATEDIFF(day,0,GETDATE()),0) >= StartDate
      ORDER BY LTRIM(RTRIM(sor.StockCode))
    for xml path('')),1,1,'')
                                             AS "csv_special_prices",

    STUFF((
      SELECT
        ',' + CONVERT(VARCHAR, LTRIM(RTRIM(sor.StockCode))) + '|' +
        CASE WHEN (DATEADD(day, DATEDIFF(day,0,GETDATE()),0) <= ExpiryDate or ExpiryDate is NULL ) AND DATEADD(day, DATEDIFF(day,0,GETDATE()),0) >= StartDate
        THEN CONVERT(VARCHAR, COALESCE(sor.FixedPrice, 0))
        ELSE (SELECT CONVERT(VARCHAR, CAST(ISNULL(price_tiers.SellingPrice, 0) AS FLOAT))
              FROM InvPrice price_tiers WITH (NOLOCK)
              WHERE price_tiers.StockCode = sor.StockCode
              AND price_tiers.PriceCode = ar.PriceCode)
        END
      FROM SorContractPrice sor WITH (NOLOCK)
      WHERE sor.CustomerBuyGrp = ar.Customer
      AND sor.ContractType='C'
      AND sor.PriceMethod = 'F'
     -- AND (DATEADD(day, DATEDIFF(day,0,GETDATE()),0) <= ExpiryDate or ExpiryDate is NULL )
     -- AND DATEADD(day, DATEDIFF(day,0,GETDATE()),0) >= StartDate
      ORDER BY LTRIM(RTRIM(sor.StockCode))
    for xml path('')),1,1,'')
                                             AS "csv_special_prices_fixed"

FROM ArCustomer ar WITH (nolock)
 INNER JOIN ArCustomerBal arb WITH (nolock)
 ON arb.Customer = ar.Customer
 LEFT JOIN [ArCustomer+] ar2 WITH (nolock)
 ON ar2.Customer = ar.Customer
LEFT JOIN [TblSoDiscount] di WITH (nolock)
 ON di.DiscountCode = ar.LineDiscCode
LEFT JOIN[TblArTerms] art WITH (nolock)
 ON art.TermsCode =  ar.TermsCode
LEFT JOIN [SalSalesperson] sp WITH (nolock)
 ON sp.Salesperson = ar.Salesperson AND ar.Branch = sp.Branch

  WHERE ar2.B2B in ('Y', 'N')

  AND convert(varchar, ar.Customer) = 'PALM001'