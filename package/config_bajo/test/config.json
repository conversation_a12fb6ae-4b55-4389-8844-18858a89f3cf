{"dbName": "local.db", "dsn": "Driver={SQL Server Native Client 11.0};Server=WIN-9QRCG8OF8MF\\SQLEXPRESS2008;Database=SBODemoZA;Uid=sa;Pwd=****;", "server": "WIN-9QRCG8OF8MF\\SQLEXPRESS2008", "companyDb": "SBODemoZA", "userName": "manager", "password": "manager", "dbServerType": "MSSQL2008", "dbUserName": "sa", "dbPassword": "****", "licenseServer": "WIN-9QRCG8OF8MF", "audit_limit": 1000, "push": {"source_id": "source_id", "limit": 100, "token": "token", "writeLog": "http://localhost:1337/writeLog", "getProducts": "http://localhost:1337/sapone/getProductsBatch", "getCustomers": "http://localhost:1337/sapone/getCustomersBatch", "countProducts": "http://localhost:1337/sapone/countProducts", "countCustomers": "http://localhost:1337/sapone/countCustomers", "getMeta": "http://localhost:1337/sapone/getMeta", "setMeta": "http://localhost:1337/sapone/setMeta", "productsQueue": "https://app.stock2shop.com/v1/products/queue", "customersQueue": "https://app.stock2shop.com/v1/customers/queue"}}