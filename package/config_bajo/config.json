{"dbName": "local.db", "dsn": "Driver={SQL Server};Server=BAJO_SERVER001;Database=SBK_BAJO;Uid=**********;Pwd=**********;", "dsnNoDb": "Driver={SQL Server};Server=BAJO_SERVER001;Uid=**********;Pwd=**********;", "server": "BAJO_SERVER001", "companyDb": "SBK_Bajo", "userName": "manager", "password": "manager", "dbServerType": "MSSQL2012", "dbUserName": "sa", "dbPassword": "cbaj001", "licenseServer": "serverName", "audit_limit": 500, "push": {"source_id": 238, "limit": 100, "token": "050fde35e0c8afc3361bdc8a630774487ce62dac", "writeLog": "http://localhost:1337/writeLog", "getProducts": "http://localhost:1337/sapone/getProductsBatch", "getCustomers": "http://localhost:1337/sapone/getCustomersBatch", "countProducts": "http://localhost:1337/sapone/countProducts", "countCustomers": "http://localhost:1337/sapone/countCustomers", "getMeta": "http://localhost:1337/sapone/getMeta", "setMeta": "http://localhost:1337/sapone/setMeta", "productsQueue": "https://app.**********.com/v1/products/queue", "customersQueue": "https://app.**********.com/v1/customers/queue"}}