select *
from (
select
    row_number() over (order by oitm.ItemCode) as n,

    -- There must only be one result row per ItemCode!
    oitm.ItemCode               as "source_product_code",
    oitm.ItemCode               as "source_variant_code",
    oitm.FrgnName               as "title",
    oitm.U_WebDept1             as "collection",

    (select [@WEB_COMMODITY].U_LongName
    from [@WEB_COMMODITY] where oitm.U_WebCommodity = [@WEB_COMMODITY].code)
                                as "product_type",

    ''                          as "vendor",
    ''                          as "variants.option1",
    ''                          as "variants.option2",
    ''                          as "variants.option3",
    ''                          as "body_html",
    oitm.U_WebWeight            as "variants.weight",
    oitm.CodeBars               as "variants.barcode",

    -- All price tiers
    '1'                         as "default_price_tier",
    stuff(
    (select
    convert(varchar, itm1.PriceList) + '|' +
    convert(varchar, itm1.Price) + ','
    from itm1
    where itm1.ItemCode = oitm.ItemCode
    order by itm1.PriceList
    for xml path('')),1,0,'')
                                as "csv_price_tiers",

    -- All qty availability
    'WHO_WCPT'                  as "default_qty_availability",
    stuff(
    (select
    convert(varchar, oitw.WhsCode) + '|' +
    convert(varchar, oitw.OnHand-oitw.IsCommited)+','
    from oitw
    where oitw.ItemCode = oitm.ItemCode
    order by oitw.WhsCode
    for xml path('')),1,0,'')
                                as "csv_qty_availability",

    case oitm.InvntItem
        when 'y' then 'true'
        when 'Y' then 'true'
        else 'false'
    end                         as "variants.inventory_management",

    case oitm.U_WEB
        when 'y' then 'true'
        when 'Y' then 'true'
        else 'false'
    end                         as "product_active",

    ''                          as "tags",

    oitb.ItmsGrpCod             as "meta_item_group_code",
    oitb.ItmsGrpNam             as "meta_item_group_name",

    (select [@WEB_COLOUR].U_LongName
    from [@WEB_COLOUR] where oitm.U_WebColour = [@WEB_COLOUR].code)
                                as "meta_colour",
    (select [@WEB_MATERIAL].U_LongName
    from [@WEB_MATERIAL] where oitm.U_WebMaterial = [@WEB_MATERIAL].code)
                                as "meta_material",
    oitm.U_WebGender            as "meta_gender",
    oitm.U_WebBestSeller        as "meta_featured",
    (select [@WEB_SWATCH].U_LongName
    from [@WEB_SWATCH] where oitm.U_WebSwatch = [@WEB_SWATCH].code)
                                as "meta_swatch",
    (select [@WEB_ACTIVITY].U_LongName
    from [@WEB_ACTIVITY] where oitm.U_WebActivity = [@WEB_ACTIVITY].code)
                                as "meta_activity",
    (select [@WEB_IWANT].U_LongName
    from [@WEB_IWANT] where oitm.U_WebIWant = [@WEB_IWANT].code)
                                as "meta_i_want",
    oitm.U_WebStockFiller       as "meta_stock_filler",
    oitm.U_WebDept1             as "meta_dept1",
    oitm.U_WebDept2             as "meta_dept2",
    oitm.U_WebDept3             as "meta_dept3",
    (select [@WEB_SUBDEPT].U_LongName
    from [@WEB_SUBDEPT] where oitm.U_WebSubDept1 = [@WEB_SUBDEPT].code)
                                as "meta_sub_dept1",
    (select [@WEB_SUBDEPT].U_LongName
    from [@WEB_SUBDEPT] where oitm.U_WebSubDept2 = [@WEB_SUBDEPT].code)
                                as "meta_sub_dept2",
    (select [@WEB_SUBDEPT].U_LongName
    from [@WEB_SUBDEPT] where oitm.U_WebSubDept3 = [@WEB_SUBDEPT].code)
                                as "meta_sub_dept3",
    (select [@WEB_SUBDEPT].U_LongName
    from [@WEB_SUBDEPT] where oitm.U_WebSubDept4 = [@WEB_SUBDEPT].code)
                                as "meta_sub_dept4",
    (select [@WEB_SUBDEPT].U_LongName
    from [@WEB_SUBDEPT] where oitm.U_WebSubDept5 = [@WEB_SUBDEPT].code)
                                as "meta_sub_dept5",
    oitm.U_WebFAB1              as "meta_fab1",
    oitm.U_WebFAB2              as "meta_fab2",
    oitm.U_WebFAB3              as "meta_fab3",
    oitm.U_WebWarning           as "meta_warning",
    oitm.U_WebInstructions      as "meta_instructions",
    oitm.U_WebSize              as "meta_size",
    oitm.U_WebCapacity          as "meta_capacity",

    -- Special prices for all periods and volumes
    stuff(
    (select
    convert(varchar, ospp.CardCode) + '|' +
    convert(varchar, ospp.Discount)+','
    from ospp
    where ospp.ItemCode = oitm.ItemCode
    and ospp.Discount > 0
    order by ospp.ItemCode
    for xml path('')),1,0,'')
                                as 'csv_special_prices'

    -- Special prices for period join ospp with spp1
    -- Special prices for volume join ospp with spp2

from oitm
join oitb on oitm.ItmsGrpCod = oitb.ItmsGrpCod
where 1 = 1
-- Only track products that have a value for U_WEB
and (
    U_WEB = 'y' or
    U_WEB = 'Y' or
    U_WEB = 'n' or
    U_WEB = 'N'
)

) as rows
where
n > 0
and n <= 10


