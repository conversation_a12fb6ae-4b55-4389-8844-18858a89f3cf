select


    RTRIM(LTRIM(st.ITMREF_0))				                                                    AS "source_product_code",
    RTRIM(LTRIM(st.ITMREF_0))				                                                    AS "source_variant_code",
    RTRIM(LTRIM(st.ITMDES1_0))			                                                        AS "title",
    RTRIM(LTRIM(st.TCLCOD_0))				                                                    AS "product_type",
    ''                                                                                          AS "collection",
    ''                                                                                          AS "vendor",
    RTRIM(LTRIM(st.ITMREF_0))				                                                    AS "variants.sku",
    ''												                                            AS "body_html",
    0                                                                                           AS "variants.weight",
    ''                                                                                          AS "variants.barcode",
    'true'                                                                                      AS "variants.inventory_management",
    CASE UPPER(st.ITMSTD_0) WHEN 'ECM' THEN
                                CASE st.ITMSTA_0 WHEN 1 THEN 'true' ELSE 'false' END
                            ELSE 'false'  END                                                   AS "product_active",
    ''                                                                                          AS "tags",
    'PHI01'                                                                                     AS "default_qty_availability",
    STUFF((
              SELECT
                      ',' + CONVERT(VARCHAR, sq.STOFCY_0) + '|' +
                      CONVERT(VARCHAR, ISNULL(sq.PHYSTO_0 - sq.PHYALL_0, 0)) + ','
              from PHLIVE.ITMMVT sq
              where sq.ITMREF_0 = st.ITMREF_0
              order by sq.STOFCY_0
              for xml path('')),1,0,'')
        AS "csv_qty_availability",
    'SPL23-0001'                                                                                AS "default_price_tier",
    STUFF((
              SELECT
                      ',' + CONVERT(VARCHAR, price.PLICRD_0) + '|' +
                      CONVERT(VARCHAR, price.PRI_0) + ','
                      + ',' + 's2s_web' + '|' +
                      CONVERT(VARCHAR, price.PRI_0) + ','
              from PHLIVE.SPRICLIST price
              where price.PLICRI1_0 = st.ITMREF_0
              order by price.PLICRD_0
              for xml path('')),1,1,'')
                                                                                                AS "csv_price_tiers"


FROM PHLIVE.ITMMASTER st
WHERE RTRIM(LTRIM(st.ITMREF_0)) = '%(sku)s'
;