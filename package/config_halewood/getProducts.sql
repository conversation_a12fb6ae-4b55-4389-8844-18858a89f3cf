select *
from (
select
    row_number() OVER (ORDER BY inv.StockCode) as n,

    RTRIM(LTRIM(inv.StockCode))                as "source_product_code",
    RTRIM(LTRIM(inv.StockCode))                as "source_variant_code",
    RTRIM(LTRIM(inv.Description))              as "title",
    ''                                         as "collection",
    ''                                         as "product_type",
    ''                                         as "vendor",
    RTRIM(LTRIM(inv.StockCode))                as "variants.sku",
    ''                                         as "body_html",
    CAST(ISNULL(inv.Mass, 0) AS INTEGER)       as "variants.weight",
    inv.AlternateKey1                          as "variants.barcode",
    CASE
        WHEN RTRIM(ISNULL(inv2.LiquordropStatus, '')) = '1' THEN 'true'
        ELSE 'false'
    END                                        as "product_active",
    'false'                                    as "variants.inventory_management",
    ''                                         as "tags",

    -- Prices
    'C'                                        as "default_price_tier",
    STUFF((
      SELECT
        ',' + CONVERT(VARCHAR, LTRIM(RTRIM(price_tiers.PriceCode))) + '|' +
              CONVERT(VARCHAR, CAST(ISNULL(price_tiers.SellingPrice, 0) AS FLOAT))
      FROM InvPrice price_tiers WITH (NOLOCK)
      WHERE price_tiers.StockCode = inv.StockCode
      ORDER BY LTRIM(RTRIM(price_tiers.PriceCode))
    for xml path('')),1,1,'')
                                                as "csv_price_tiers",

    -- Quantities
    'OC'										as "default_qty_availability",
    STUFF((
      SELECT
        ',' + CONVERT(VARCHAR, LTRIM(RTRIM(whse.Warehouse))) + '|' +
              CONVERT(VARCHAR, (COALESCE(whse.QtyOnHand, 0) - COALESCE(whse.QtyAllocated, 0)))
      FROM InvWarehouse whse WITH (NOLOCK)
      WHERE whse.StockCode = inv.StockCode
      ORDER BY LTRIM(RTRIM(whse.Warehouse))
    for xml path('')),1,1,'')
										        as "csv_qty_availability",

    -- Stock UOM csv list
    STUFF((
      SELECT ',' + 
      CONVERT(VARCHAR, LTRIM(RTRIM(inv.StockCode))) + '|' + CONVERT(VARCHAR,LTRIM(RTRIM(inv.StockUom)))
      FROM InvMaster inv3 WITH (NOLOCK)
      WHERE inv3.StockCode = inv.StockCode
      FOR XML PATH('')) ,1,1,'') 
                            AS "csv_uom",

(
      SELECT
      CONVERT(VARCHAR, (ISNULL(price_tiers.SellingPrice, 0)))
      FROM InvPrice price_tiers WITH (NOLOCK)
      WHERE price_tiers.StockCode = inv.StockCode
      AND price_tiers.PriceCode = 'C')
                                                as "meta_regular_price",

(
      SELECT
      CONVERT(VARCHAR, (ISNULL(price_tiers.SellingPrice, 0)))
      FROM InvPrice price_tiers WITH (NOLOCK)
      WHERE price_tiers.StockCode = inv.StockCode
      AND price_tiers.PriceCode = 'D')
                                                as "meta_sale_price"

FROM InvMaster inv WITH (NOLOCK)

    LEFT JOIN [InvMaster+] inv2 WITH (nolock)
    ON inv.StockCode = inv2.StockCode

WHERE LTRIM(RTRIM(inv2.LiquordropStatus)) in (1,2)

) as rows
where
n > '%(audit_lower_limit)n'
and n <= '%(audit_upper_limit)n'