import json
import imp

import win32com.client
from .controllers import base_push as base
from .shared import sap_one


class Api(base.Api):
    _configDir = "config_apico"

    def reload(self):
        imp.reload(base)
        base.Api.reload(self)
        response = {
            "status": True,
            "data": {
                "reload": True
            }
        }
        return json.dumps(response, indent=self._indent)

    # Add _transformProduct here to override default


    @staticmethod
    def _getSeriesNameByWarehouseCode(warehouse_code):
        return "Primary"

    # def _duringCreateOrder(self, payload, oOrder, params):
    #     if "card_name" in params:
    #         oOrder.CardName = params["card_name"]
    #     return payload, oOrder, params

def getCompany(config):
    company = win32com.client.Dispatch("SAPBobsCOM.Company")
    company.Server = config["server"]
    company.CompanyDB = config["companyDb"]
    company.UserName = config["userName"]
    company.Password = config["password"]
    company.DbServerType = sap_one.DB_SERVER_TYPES[config["dbServerType"]]["code"]
    # company.DbUserName = config["dbUserName"]
    # company.DbPassword = config["dbPassword"]
    company.LicenseServer = config["licenseServer"]
    return company

# Override the sap_one.getCompany method with the new method
# TODO investigate why we need to not include dbUsername and dbPassword
sap_one.getCompany = getCompany
