{"dbName": "local.db", "dsn": "Driver={SQL Server};Server=DESKTOP-CD3L66A\\TESTSQLSERVER;Database=Apifact;Uid=sa;Pwd=*************;", "audit_limit": 1000, "push": {"source_id": 999, "limit": 500, "token": "CRON TOKEN", "writeLog": "http://localhost:13337/writeLog", "getProducts": "http://localhost:13337/kyle/getProductsBatch", "getCustomers": "http://localhost:13337/kyle/getCustomersBatch", "countProducts": "http://localhost:13337/kyle/countProducts", "countCustomers": "http://localhost:13337/kyle/countCustomers", "getMeta": "http://localhost:13337/kyle/getMeta", "setMeta": "http://localhost:13337/kyle/setMeta", "productsQueue": "https://app.stock2shop.com/v1/products/queue", "customersQueue": "https://app.stock2shop.com/v1/customers/queue"}}