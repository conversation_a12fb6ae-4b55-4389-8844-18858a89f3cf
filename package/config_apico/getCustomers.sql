SELECT *
FROM (
SELECT
    CardCode                            AS 'source_customer_code',
    CardName                            AS 'card_name',
    E_Mail                              AS 'email',

    'false'                             AS "customer_active",
    0                                   AS 'accepts_marketing',
    MailAddres                          AS 'address.address1', -- OR Address?
    ''                                  AS 'address.address2',
    City                                AS 'address.city',
    Country                             AS 'address.country',
    'ZA'                                AS 'address.country_code',
    ''                                  AS 'address.province',
    ''                                  AS 'address.province_code',
    ZipCode                             AS 'address.zip',
    ''                                  AS 'address.company',
    CntctPrsn                           AS 'address.contact_person',
    Phone1                              AS 'address.phone',

    -- Discount groups
    STUFF(
    (SELECT
    CONVERT(VARCHAR, ospg.ObjKey) + '|' + -- oitb.ItmsGrpCod
    CONVERT(VARCHAR, ospg.Discount) + ','
    FROM ospg
    WHERE ospg.CardCode = ocrd.CardCode
    AND ospg.ObjType = 52 -- Discount Groups
    AND ospg.Discount > 0
    ORDER BY ospg.ObjKey
    for xml path('')),1,0,'')
                                        AS 'csv_discount_groups',
    -- CONTRACT PRICING:
    -- A contract consists of 6 parts, these ARE:-
    --    ORDER:    The hierarchy (ORDER) IN which to load the contracts. The FIRST successful contract will be used
    --    Entity:   The entity the contract will apply to. For example, a customer would have a contract WITH entity "product" to apply to the product.
    --    KEY:      The corresponding entities KEY to match on. IN the above example, this would be "category"
    --    TYPE:     Discount OR fixed. IS this contract a fixed price OR discount FROM some other price.
    --              Note subtypes - discount: applies to default price, discount_channel: channel price, discount_channel_user: customer assigned price
    --    VALUE:    The VALUE of the contract, IN the CASE of TYPE discount, this would be a percentage expressed AS an integer. IN the CASE of a fixed price, it would be the price,
    -- e.g. order_0|entity_product|key_product_code|value_123|type_discount~10
    -- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    -- Hierarchy:
    -- 1. ALL Customers, Customer GROUP, OR specific Customer
    -- 2. ALL Inventory, Inventory GROUP, OR specific Inventory Item
    -- 3. Actual discount details (discount % OR unit price)
    -- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    -- Contract: Customer discount BY GROUP (percentage)
    -- Requirements:
    --   Product.meta_item_group_code
    stuff((
      SELECT
          ',order_1|entity_product|key_item_group_code|value_' + convert(varchar, T1.[ObjKey]) + '|type_discount_channel_user~' +
          convert(varchar, T1.[Discount])
      FROM OEDG T0 WITH (NOLOCK) INNER JOIN EDG1 T1 ON T0.[AbsEntry] = T1.[AbsEntry]
      WHERE T0.[ObjCode] = convert(varchar, ocrd.CardCode)
        and T1.ObjType = 52 -- Discount Groups
        and T1.Discount > 0
      order by T1.ObjKey
      FOR XML PATH ('')), 1, 1, '')
                                                                               as 'csv_contract_product_customer_group',

    -- For customers

    ListNum                             AS 'price_tier',
    ''                                  AS 'qty_availability'

FROM ocrd
WHERE 1 = 1

) AS ROWS
WHERE
n > '%(audit_lower_limit)n'
AND n <= '%(audit_upper_limit)n'