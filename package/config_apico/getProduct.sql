SELECT
    oitm.ItemCode               AS "source_product_code",
    oitm.ItemCode               AS "source_variant_code",
    oitm.ItemName               AS "title",
    oitm.ItemCode               AS "variants.sku",
    ''                          AS "collection",
    ''                          AS "product_type",
    ''                          AS "vendor",
    ''                          AS "variants.option1",
    ''                          AS "variants.option2",
    ''                          AS "variants.option3",
    ''                          AS "body_html",
    0                           AS "variants.weight",
    ''                          AS "variants.barcode",

    -- All price tiers
    'DEFAULT'                   AS "default_price_tier",

    STUFF(
    (SELECT
    CONVERT(varchar, itm1.PriceList) + '|' +
    CONVERT(varchar, itm1.Price) + ','
    FROM itm1
    WHERE itm1.ItemCode = oitm.ItemCode
    ORDER BY itm1.PriceList
    FOR XML PATH('')),1,0,'')
                                AS "csv_price_tiers",

    -- All qty availability
    'DEFAULT'                   AS "default_qty_availability",

    STUFF(
    (SELECT
    CONVERT(varchar, oitw.WhsCode) + '|' +
    CONVERT(varchar, oitw.OnHand-oitw.IsCommited)+','
    FROM oitw
    WHERE oitw.ItemCode = oitm.ItemCode
    ORDER BY oitw.WhsCode
    FOR XML PATH('')),1,0,'')
                                AS "csv_qty_availability",

    'true'                      AS "variants.inventory_management",

    CASE lower(oitm.U_S2S_Sync)
        WHEN 1 THEN 'true'
        WHEN 0 THEN 'false'
        ELSE 'false'
    END                         AS "product_active",
    ''                          AS "tags"

FROM oitm
WHERE 
    oitm.U_S2S_Sync IN (1,2)
    AND oitm.ItemCode = '%(source_variant_code)s';