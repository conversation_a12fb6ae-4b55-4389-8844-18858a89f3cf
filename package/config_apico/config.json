{"dbName": "local.db", "dsn": "Driver={SQL Server Native Client 11.0};Server=***************;Database=apico_live;Uid=ReadOnly;Pwd=********;", "server": "apico-sap1", "companyDb": "apico_live", "userName": "josh", "password": "manc11", "dbServerType": "MSSQL2019", "dbUserName": "Read<PERSON>nly", "dbPassword": "********", "licenseServer": "apico-sap1", "audit_limit": 500, "audit_image_limit": 100, "debug": 0, "push": {"source_id": 1656, "limit": 100, "image_limit": 2, "token": "4Y6CQROA4GF2HOE6J1HDBEYSLBG4TB9GL4EKUDHQ", "writeLog": "http://localhost:13337/writeLog", "getProducts": "http://localhost:13337/sapone/getProductsBatch", "getCustomers": "http://localhost:13337/sapone/getCustomersBatch", "countProducts": "http://localhost:13337/sapone/countProducts", "countCustomers": "http://localhost:13337/sapone/countCustomers", "getMeta": "http://localhost:13337/sapone/getMeta", "setMeta": "http://localhost:13337/sapone/setMeta", "productsQueue": "https://app.stock2shop.com/v1/products/queue", "customersQueue": "https://app.stock2shop.com/v1/customers/queue"}}