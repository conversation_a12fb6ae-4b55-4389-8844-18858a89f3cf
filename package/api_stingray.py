import json
import imp
import time
import math
from .shared import utils
from .shared import sap_one
from .controllers import base_push as base

class Api(base.Api):
    _configDir = "config_stingray"

    def reload(self):
        imp.reload(base)
        base.Api.reload(self)
        response = {
            "status": True,
            "data": {
                "reload": True
            }
        }
        return json.dumps(response, indent=self._indent)

    @staticmethod
    def _getSeriesNameByWarehouseCode(warehouse_code):
        return "Primary"

    def createOrder(self):
        t1 = time.time()
        self._setConfig()

        params = self._getParams()
        response = utils.checkRequiredParams(
            ["warehouse_code"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        try:
            payload = json.loads(self._payload())
        except ValueError:
            response = {
                "status": False,
                "description": "Request body is not valid json",
                "line": utils.lineNo()
            }
            return json.dumps(response, indent=self._indent)

        # hook for custom transforms
        payload, params = self._beforeCreateOrder(payload, params)

        order = payload["system_order"]
        source = payload["sources"][0]

        if "source_customer_code" in source and source["source_customer_code"]:
            source_customer_code = source["source_customer_code"]
        else:
            if "default_customer_code" in params:
                source_customer_code = params["default_customer_code"]

        if source_customer_code is None:
            raise Exception("source_customer_code missing and no default_customer_code set")

        # If contacts are included in the source customer code,
        # we need to strip out the contact id and only use card code.
        # The format of source_customer_code is:
        # {card_code}-{contact_id}
        if "contact_source_customer" in params:
            if params["contact_source_customer"] == "true":
                source["original_source_customer_code"] = source["source_customer_code"]
                codes = source["source_customer_code"].rsplit('-', 1)
                if codes[1]:
                    source_customer_code = codes[0]
                else:
                    raise Exception("Invalid source_customer_code {}".format(source["source_customer_code"]))

        # Parse document type if it is given in the params, otherwise create invoices by default
        document_type = "oInvoices"
        if "order_document_type" in params:
            document_type = params["order_document_type"]
            if document_type not in sap_one.BO_OBJECT_TYPES:
                response = {
                    "status": False,
                    "description": "Invalid document type '{}' given".format(document_type),
                    "line": utils.lineNo()
                }
                return json.dumps(response, indent=self._indent)

        # Get company instance
        oCompany = sap_one.getCompany(self._config)

        # Try to connect
        response = sap_one.connect(oCompany)
        if not response["status"]:
            return json.dumps(response, indent=self._indent)

        object_type = sap_one.BO_OBJECT_TYPES[document_type]["code"]

        # Create order object
        oOrder = oCompany.GetBusinessObject(object_type)
        oOrder.CardCode = source_customer_code
        oOrder.DocDueDate = sap_one.getVariantDate(order["created"])

        notes = ""
        if "notes" in order:
            notes = order["notes"]
        oOrder.Comments = notes

        if "doc_due_date" in params:
            oOrder.DocDueDate = sap_one.getVariantDate(params["doc_due_date"])

        if "card_name" in params:
            oOrder.CardName = params["card_name"]

        customer_reference = ""
        if "customer_reference" in params:
            customer_reference = params["customer_reference"]
        oOrder.NumAtCard = customer_reference

        if "contact_person_code" in params:
            oOrder.ContactPersonCode = params["contact_person_code"]

        if "documents_owner" in params:
            oOrder.DocumentsOwner = params["documents_owner"]

        if "project" in params:
            oOrder.Project = params["project"]

        if "reserve_invoice" in params:
            if params["reserve_invoice"] == "true":
                oOrder.ReserveInvoice = sap_one.BO_YES_NO_ENUM["tYES"]["code"]

        if "transportation_code" in params:
            oOrder.TransportationCode = params["transportation_code"]

        if "address" in params:
            oOrder.Address = params["address"]

        if "address2" in params:
            oOrder.Address2 = params["address2"]

        if "set_branch_code" in params:
            if params["set_branch_code"] == "true":
                branch_code = self._getBranchCode(
                    params["warehouse_code"])
                oOrder.BPL_IDAssignedToInvoice = branch_code

        # Set user defined fields
        for field in params:
            if "user_field_order_" in field:
                user_field = field.replace("user_field_order_", "")
                oOrder.UserFields.Fields.Item(user_field).Value = params[field]

        # Try to set numbering series
        Series, NextNumber = self._getNumberingSeriesByName(
            params["warehouse_code"],
            object_type)
        if Series is not None and NextNumber is not None:
            oOrder.Series = Series
            # To use the HandWritten property to manually set the next number:
            # oOrder.HandWritten = sap_one.BO_YES_NO_ENUM["tYES"]["code"]
            # oOrder.DocNum = NextNumber
        else:
            raise Exception(
                "Numbering series not found for warehouse {}".format(
                    params["warehouse_code"]))

        # Add line items to order
        exclusiveTotalCents = 0
        for line_item in order["line_items"]:
            # Check if line item exists,
            # it maybe have been deleted from SAP
            # and not synced to stock2shop
            ItemCode = self._getItemStatus(line_item["sku"])
            if ItemCode is None:
                response = {
                    "status": False,
                    "description": "ItemCode {} does not exist".format(
                        line_item["sku"]),
                    "line": utils.lineNo()
                }
                return json.dumps(response, indent=self._indent)

            oOrder.Lines.ItemCode = line_item["sku"]
            oOrder.Lines.Quantity = line_item["qty"]

            # warehouse code set on line items
            if "use_product_warehouse_code" in params and params["use_product_warehouse_code"] == "true":
                oOrder.Lines.WarehouseCode = self._getProductDefaultWarehouseCode(line_item["sku"])
            else:
                oOrder.Lines.WarehouseCode = params["warehouse_code"]

            # unit price or price
            if "use_unit_price" in params and params["use_unit_price"] == "true":
                oOrder.Lines.UnitPrice = line_item["price"]
            else:
                oOrder.Lines.Price = line_item["price"]

            if "line_item_project_code" in params:
                oOrder.Lines.ProjectCode = params["line_item_project_code"]

            if "line_item_costing_code" in params:
                oOrder.Lines.CostingCode = params["line_item_costing_code"]

            # Set account codes if we have them for the client
            sales_account = self._getSalesGLAccount(line_item["sku"], source_customer_code)
            cogs_account = self._getCOGSGLAccount(line_item["sku"], source_customer_code)
            if sales_account is not None:
                oOrder.Lines.AccountCode = sales_account

            if cogs_account is not None:
                oOrder.Lines.COGSAccountCode = cogs_account

            exclusiveTotalCents += round(int(line_item["qty"]) * (line_item["price"] * 100))
            # TODO See gomedia_evolution
            # tax_line = line_item["tax_lines"][0]
            # oOrder.Lines.TaxCode = self._getTaxCode(
            #     params, tax_line["code"]
            # )
            # oOrder.Lines.TaxCode = "0"

            try:
                oOrder.Lines.Add()
            except Exception as e:
                response = {
                    "status": False,
                    "description": str(e),
                    "line": utils.lineNo()
                }
                return json.dumps(response, indent=self._indent)

        add_shipping = True

        if "ignore_shipping" in params:
            if params["ignore_shipping"] == "true":
                add_shipping = False
        if "shipping_lines" in order and "shipping_code" in params and add_shipping:
            delivery_mode = 'C'
            for shipping_line in order["shipping_lines"]:

                # Delivery codes:
                # C => Collect
                # M => MDS Collivery
                # R7, V => Specified on b2b customers
                delivery_mode = 'C'

                oOrder.Lines.ItemCode = params["shipping_code"]
                oOrder.Lines.Quantity = 1
                oOrder.Lines.UnitPrice = shipping_line["price"]
                if "shipping_project_code" in params:
                    oOrder.Lines.ProjectCode = params["shipping_project_code"]

                if "shipping_costing_code" in params:
                    oOrder.Lines.CostingCode = params["shipping_costing_code"]

                if "u_mod" in params:
                    delivery_mode = params["u_mod"]
                else:
                    if "MDS" in shipping_line["title"]:
                        delivery_mode = 'M'

                exclusiveTotalCents += round(shipping_line["price"] * 100)

                try:
                    oOrder.Lines.Add()
                except Exception as e:
                    response = {
                        "status": False,
                        "description": str(e),
                        "line": utils.lineNo()
                    }
                    return json.dumps(response, indent=self._indent)

            # oOrder.U_MOD = delivery_mode
            oOrder.UserFields.Fields.Item("U_MOD").value = delivery_mode

        # if line item count > 0 then add 'price dispute' lie item
        if len(order["line_items"]) > 0:
            oOrder.Lines.ItemCode = 'PRICEDISP'
            oOrder.Lines.UnitPrice = 0

            # Quantity = number of line items + shipping line item
            oOrder.Lines.Quantity = len(order["line_items"]) + 1

            try:
                oOrder.Lines.Add()
            except Exception as e:
                response = {
                    "status": False,
                    "description": str(e),
                    "line": utils.lineNo()
                }
                return json.dumps(response, indent=self._indent)

        # By default the discount is applied as a percentage on the order
        if "total_discount" in order:
            # Prevent division by zero
            if exclusiveTotalCents > 0:
                totalDiscountCents = round(float(order["total_discount"]) * 100)
                oOrder.DiscountPercent = (totalDiscountCents / exclusiveTotalCents) * 100

        # Hook for transforming order just before order is added
        self._duringCreateOrder(payload, oOrder, params)

        try:
            retCode = oOrder.Add()
        except Exception as e:
            response = {
                "status": False,
                "description": str(e),
                "line": utils.lineNo()
            }
            return json.dumps(response, indent=self._indent)

        if retCode != 0:
            code, message = oCompany.GetLastError(retCode)
            code = int(math.fabs(int(code)))
            if code == 5009:
                # Default message for this code is too cryptic
                message = "SKU not found"
            response = {
                "status": False,
                "description": "{} - {}".format(code, message),
                "line": utils.lineNo()
            }
            return json.dumps(response, indent=self._indent)

        else:
            # GetNewObjectCode returns DocEntry,
            # to get DocNum query the ordr table
            DocEntry = oCompany.GetNewObjectCode()
            with sap_one.openConn(self._config["dsn"]) as cursor:
                # Sales order
                if object_type == sap_one.BO_OBJECT_TYPES["oOrders"]["code"]:
                    sql = '''select ordr."DocNum" as docnum from ordr
                    where ordr."DocEntry" = '%(DocEntry)n' '''

                # Invoice
                elif object_type == sap_one.BO_OBJECT_TYPES["oInvoices"]["code"]:
                    sql = '''select oinv."DocNum" as docnum from oinv
                    where oinv."DocEntry" = '%(DocEntry)n' '''

                # Quotation
                elif object_type == sap_one.BO_OBJECT_TYPES["oQuotations"]["code"]:
                    sql = '''select oqut."DocNum" as docnum from oqut
                    where oqut."DocEntry" = '%(DocEntry)n' '''

                else:
                    response = {
                        "status": False,
                        "description": "object_type {} not implemented".format(
                            object_type),
                        "line": utils.lineNo()
                    }
                    return json.dumps(response, indent=self._indent)

                sql = utils.bindSqlParams(sql, {"DocEntry": int(DocEntry)})
                cursor.execute(sql)
                rows = utils.getRowsODBC(cursor, 1)
                if len(rows) == 0:
                    response = {
                        "status": False,
                        "description":
                            "DocNum for DocEntry {} not found".format(DocEntry),
                        "line": utils.lineNo()
                    }
                    return json.dumps(response, indent=self._indent)

                source_order_code = rows[0]["docnum"]  # Must be lowercase

        # Remember to disconnect when done
        if oCompany.Connected:
            oCompany.Disconnect()

        # hook for after order
        source_order_code, source_customer_code = self._afterCreateOrder(payload, source_order_code,
                                                                         source_customer_code)

        # if contact used in source customer code, return the original source customer code, i.e.
        # {card_code}-{contact_id}
        if "contact_source_customer" in params:
            if params["contact_source_customer"] == "true":
                source_customer_code = source["original_source_customer_code"]

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {
                "source_order_code": source_order_code,
                "source_customer_code": source_customer_code
            }
        }
        return json.dumps(response, indent=self._indent)
