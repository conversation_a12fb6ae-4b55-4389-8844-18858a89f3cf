SELECT

  st.ucIIGroupCode                                                     AS "source_product_code",
  st.Code                                                              AS "source_variant_code",
  st.ucIIWebtitle                                                      AS "title",
  st.ulIIS2SCategory                                                   AS "collection",
  'n/a'                                                                AS "product_type",
  ''                                                                   AS "vendor",
  st.Code                                                              AS "variants.sku",
  'Watts'                                                              AS "variants.option1_name",
  st.ucIIWattsVoltage                                                  AS "variants.option1_value",
  'Size'                                                               AS "variants.option2_name",
  st.ucIISize                                                          AS "variants.option2_value",
  'Selection'                                                          AS "variants.option3_name",
  st.ucIISelection                                                     AS "variants.option3_value",
  ''                                                                   AS "body_html",
  0                                                                    AS "variants.weight",
  st.Bar_code                                                          AS "variants.barcode",
  'true'                                                               AS "variants.inventory_management",
  CASE LOWER(st.ulIIStock2ShopActive)
    WHEN 'active' THEN 'true'
    ELSE 'false'
  END                                                                  AS "product_active",
  ''                                                                   AS "tags",

  'Retail'                                                             AS "default_price_tier",
  stuff(
    (select
      ',' + convert(varchar, price_names.cName) + '|' +
            convert(varchar, price.fExclPrice)
    from dbo._etblPriceListPrices price WITH (NOLOCK)

      inner join _etblPriceListName price_names WITH (NOLOCK)
        ON price.iPriceListNameID = price_names.IDPriceListName AND price.iStockID = st.StockLink and price.iWarehouseID = 0

    order by price_names.cName
    for xml path('')),1,1,'')
                                                                       AS "csv_price_tiers",


--   CAST(ISNULL(qty_wh1.WHQtyOnHand - qty_wh1.WHQtyOnSO, 0)   AS INTEGER)AS "variants.qty",
  'Mstr'                                                               AS "default_qty_availability",
  STUFF((
    SELECT
      CONVERT(VARCHAR, whse_mst.Code) + '|' +
      CONVERT(VARCHAR,
          CASE WHEN (ISNULL(whse.WHQtyOnHand - whse.WHQtyOnSO, 0)) < 0
          THEN 0
          ELSE (ISNULL(whse.WHQtyOnHand - whse.WHQtyOnSO, 0))
          END) + ','
    FROM WhseStk whse WITH (NOLOCK)

      LEFT JOIN WhseMst whse_mst WITH (NOLOCK)
        ON whse_mst.WhseLink = whse.WHWhseID

    WHERE whse.WHStockLink = st.StockLink
    ORDER BY whse_mst.Code
    for xml path('')),1,0,'')
                                                                      AS "csv_qty_availability",

  -- List of images for this product
  -- Enables automatic pulling of new images when image is added
  ISNULL(
  STUFF(
    (SELECT ',' + convert(varchar, i.idInvImage)
    FROM _etblInvImages i WITH (NOLOCK)
    WHERE st.StockLink = i.iStockLink
    ORDER BY i.idInvImage
    FOR XML PATH ('')), 1, 1, ''), '')
                                                                       AS "meta_image_id_csv",
  st.ulIIS2SCategory                                                   AS "meta_category",
  st.itemgroup                                                         AS "meta_item_group",
  grp.idGrpTbl                                                         AS "meta_item_group_code",
  st.StockLink                                                         AS "meta_item_code",


  -- Discount by customer per product
    stuff(
        (SELECT convert(VARCHAR, st.StockLink) + '-' + convert(VARCHAR, vd.iARAPID) + '|' +
                convert(VARCHAR,
                CASE WHEN getdate() >= vdln.dEffDate AND getdate() <= vdln.dExpDate
                  THEN vdlvln.fPriceDisc
                  ELSE 0
                END
                ) + ','
         FROM _etblVDAR vd
             INNER JOIN _etblVDLnAR vdln
                 ON vd.IDVD = vdln.iVDID
             INNER JOIN _etblVDLnLvlAR vdlvln
                 ON vdln.IDVDLn = vdlvln.iVDLnID
         WHERE vd.bARAPAll = 0
           AND vd.iARAPID > 0
           AND vdlvln.fQuantity = 1
           AND vdln.iStockID = st.StockLink
           AND vd.iGroupID = 0
           AND vdln.iSTGroupID = 0
           AND vdln.bUseStockPrc = 0
         ORDER BY vd.iARAPID
         FOR XML PATH ('')), 1, 0, '')
                                                                       AS 'csv_customer',
  -- Discount by customer per product fixed
    stuff(
        (SELECT convert(VARCHAR, st.StockLink) + '-' + convert(VARCHAR, vd.iARAPID) + '|' +
                convert(VARCHAR,
                CASE WHEN getdate() >= vdln.dEffDate AND getdate() <= vdln.dExpDate
                  THEN vdlvln.fPriceDisc
                  ELSE 0
                END
                ) + ','
         FROM _etblVDAR vd
             INNER JOIN _etblVDLnAR vdln
                 ON vd.IDVD = vdln.iVDID
             INNER JOIN _etblVDLnLvlAR vdlvln
                 ON vdln.IDVDLn = vdlvln.iVDLnID
         WHERE vd.bARAPAll = 0
           AND vd.iARAPID > 0
           AND vdlvln.fQuantity = 1
           AND vdln.iStockID = st.StockLink
           AND vd.iGroupID = 0
           AND vdln.iSTGroupID = 0
           AND vdln.bUseStockPrc = 1
         ORDER BY vd.iARAPID
         FOR XML PATH ('')), 1, 0, '')
                                                                       AS 'csv_customer_fixed',

  -- Discount on product that applies to all customers (e.g StockLink-0)
    stuff(
        (SELECT convert(VARCHAR, st.StockLink) + '-' + convert(VARCHAR, vd.iARAPID) + '|' +
                convert(VARCHAR,
                CASE WHEN getdate() >= vdln.dEffDate AND getdate() <= vdln.dExpDate
                  THEN vdlvln.fPriceDisc
                  ELSE 0
                END
                ) + ','
         FROM _etblVDAR vd
             INNER JOIN _etblVDLnAR vdln
                 ON vd.IDVD = vdln.iVDID
             INNER JOIN _etblVDLnLvlAR vdlvln
                 ON vdln.IDVDLn = vdlvln.iVDLnID
          WHERE vd.bARAPAll = 1
          AND vdln.iStGroupID = grp.idGrpTbl
          AND getdate() >= vdln.dEffDate
          AND getdate() <= vdln.dExpDate
         ORDER BY vdln.dExpDate DESC
         FOR XML PATH ('')), 1, 0, '')
                                                                       AS 'csv_customer_product_global',

  -- Discount by customer linked to product group
    stuff(
        (SELECT convert(VARCHAR, st.StockLink) + '-' + convert(VARCHAR, vd.iARAPID) + '|' +
                convert(VARCHAR,
                CASE WHEN getdate() >= vdln.dEffDate AND getdate() <= vdln.dExpDate
                  THEN vdlvln.fPriceDisc
                  ELSE 0
                END
                ) + ','
         FROM _etblVDAR vd
             INNER JOIN _etblVDLnAR vdln
                 ON vd.IDVD = vdln.iVDID
             INNER JOIN _etblVDLnLvlAR vdlvln
                 ON vdln.IDVDLn = vdlvln.iVDLnID
          WHERE vd.bARAPAll = 0
          AND vd.iARAPID > 0
          AND vdlvln.fQuantity = 1
          AND vdln.iStockID = 0
          AND vd.iGroupID = 0
          AND vdln.iStGroupID = grp.idGrpTbl
          AND vdln.bUseStockPrc = 0
         ORDER BY vd.iARAPID
         FOR XML PATH ('')), 1, 0, '')
                                                                       AS 'csv_customer_product_group',

    -- Discount by customer linked to product group fixed
    stuff(
        (SELECT convert(VARCHAR, st.StockLink) + '-' + convert(VARCHAR, vd.iARAPID) + '|' +
                convert(VARCHAR,
                CASE WHEN getdate() >= vdln.dEffDate AND getdate() <= vdln.dExpDate
                  THEN vdlvln.fPriceDisc
                  ELSE 0
                END
                ) + ','
         FROM _etblVDAR vd
             INNER JOIN _etblVDLnAR vdln
                 ON vd.IDVD = vdln.iVDID
             INNER JOIN _etblVDLnLvlAR vdlvln
                 ON vdln.IDVDLn = vdlvln.iVDLnID
         WHERE vd.bARAPAll = 0
          AND vd.iARAPID > 0
          AND vdlvln.fQuantity = 1
          AND vdln.iStockID = 0
          AND vd.iGroupID = 0
          AND vdln.iStGroupID = grp.idGrpTbl
          AND vdln.bUseStockPrc = 1
         ORDER BY vd.iARAPID
         FOR XML PATH ('')), 1, 0, '')
                                                                       AS 'csv_customer_product_group_fixed',

    -- Discount by customer group linked to product group
    stuff(
        (SELECT convert(VARCHAR, st.StockLink) + '-' + convert(VARCHAR, vd.iGroupID) + '|' +
                convert(VARCHAR,
                CASE WHEN getdate() >= vdln.dEffDate AND getdate() <= vdln.dExpDate
                  THEN vdlvln.fPriceDisc
                  ELSE 0
                END
                ) + ','
         FROM _etblVDAR vd
             INNER JOIN _etblVDLnAR vdln
                 ON vd.IDVD = vdln.iVDID
             INNER JOIN _etblVDLnLvlAR vdlvln
                 ON vdln.IDVDLn = vdlvln.iVDLnID
         WHERE vd.bARAPAll = 0                -- discount for all
          AND vd.iARAPID = 0                  -- per accounts receivable
          AND vdlvln.fQuantity = 1            -- volume discount
          AND vdln.iStockID = 0               -- stock item
          AND vd.iGroupID > 0                 -- customer group
          AND vdln.iStGroupID = grp.idGrpTbl  -- stock group
          AND vdln.bUseStockPrc = 0           -- fixed or percentage
         ORDER BY vd.iGroupID
         FOR XML PATH ('')), 1, 0, '')
                                                                       AS 'csv_customer_group_product_group',

       -- Discount by customer group linked to product group fixed
    stuff(
        (SELECT convert(VARCHAR, st.StockLink) + '-' + convert(VARCHAR, vd.iGroupID) + '|' +
                convert(VARCHAR,
                CASE WHEN getdate() >= vdln.dEffDate AND getdate() <= vdln.dExpDate
                  THEN vdlvln.fPriceDisc
                  ELSE 0
                END
                ) + ','
         FROM _etblVDAR vd
             INNER JOIN _etblVDLnAR vdln
                 ON vd.IDVD = vdln.iVDID
             INNER JOIN _etblVDLnLvlAR vdlvln
                 ON vdln.IDVDLn = vdlvln.iVDLnID
         WHERE vd.bARAPAll = 0                -- discount for all
          AND vd.iARAPID = 0                  -- per accounts receivable
          AND vdlvln.fQuantity = 1            -- volume discount
          AND vdln.iStockID = 0               -- stock item
          AND vd.iGroupID > 0                 -- customer group
          AND vdln.iStGroupID = grp.idGrpTbl  -- stock group
          AND vdln.bUseStockPrc = 1           -- fixed or percentage
         ORDER BY vd.iGroupID
         FOR XML PATH ('')), 1, 0, '')
                                                                       AS 'csv_customer_group_product_group_fixed'

FROM StkItem st WITH (NOLOCK)

  -- General info
  LEFT JOIN GrpTbl grp WITH (NOLOCK) ON grp.StGroup = st.ItemGroup

--   -- qty
--   LEFT JOIN dbo.WhseStk qty_wh1 WITH (NOLOCK)
--   ON qty_wh1.WHStockLink = st.StockLink AND
--     qty_wh1.WHWhseID = 1 -- Master Warehouse

WHERE  st.Code = '%(sku)s';