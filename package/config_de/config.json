{"dbName": "local.db", "dsn": "Driver={SQL Server Native Client 11.0};Server=DE-SQL;Database=Diesel_Live;Uid=sa;Pwd=**************;", "server": "DE-SQL", "companyDb": "Diesel_Live", "userName": "REPV1", "password": "de123", "dbServerType": "MSSQL2019", "dbUserName": "sa", "dbPassword": "**************", "licenseServer": "DE-SQL", "audit_limit": 10000, "audit_image_limit": 200, "push": {"source_id": 318, "limit": 500, "image_limit": 7, "token": "dabd55e51a3ff9745a34bbb7d05da367fc756a17", "writeLog": "http://localhost:13337/writeLog", "getProducts": "http://localhost:13337/sapone/getProductsBatch", "getCustomers": "http://localhost:13337/sapone/getCustomersBatch", "getImages": "http://localhost:13337/sapone/getImagesBatch", "countProducts": "http://localhost:13337/sapone/countProducts", "countCustomers": "http://localhost:13337/sapone/countCustomers", "countImages": "http://localhost:13337/sapone/countImages", "getMeta": "http://localhost:13337/sapone/getMeta", "setMeta": "http://localhost:13337/sapone/setMeta", "productsQueue": "https://app.stock2shop.com/v1/products/queue", "customersQueue": "https://app.stock2shop.com/v1/customers/queue", "imagesQueue": "https://app.stock2shop.com/v1/images/queue"}}