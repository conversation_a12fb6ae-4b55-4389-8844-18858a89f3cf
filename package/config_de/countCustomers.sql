select

(
  select count(*) as count
  from ocpr WITH (NOLOCK)
  INNER JOIN ocrd on ocrd.CardCode=ocpr.CardCode
  INNER JOIN OVTG on ovtg.Code=ocrd.ECVatGroup
  LEFT JOIN CRD1 on CRD1.CardCode = ocpr.CardCode and ocpr.Notes2 = CRD1.Address
  where 1 = 1
  and (
      ocrd.U_S2sActive = 1 or
      ocrd.U_S2sActive = 0
  )
  and ocrd.CardType = 'C'
  and ocrd.CardName not like '%(closed)'
  and datalength(ocpr.E_MailL) <> 0
  and ocpr.E_MailL is not null
  and ocpr.FirstName is not null
  and ocpr.LastName is not null
)                                               as count,

(
  select count(*) as count
  from ocpr WITH (NOLOCK)
  INNER JOIN ocrd on ocrd.CardCode=ocpr.CardCode
  INNER JOIN OVTG on ovtg.Code=ocrd.ECVatGroup
  LEFT JOIN CRD1 on CRD1.CardCode = ocpr.CardCode and ocpr.Notes2 = CRD1.Address
  where 1 = 1
  and (
      ocrd.U_S2sActive = 1 or
      ocrd.U_S2sActive = 0
  )
  and ocrd.CardType = 'C'
  and ocrd.CardName not like '%(closed)'
  and datalength(ocpr.E_MailL) <> 0
  and ocpr.E_MailL is not null
  and ocpr.FirstName is not null
  and ocpr.LastName is not null
  and ocrd.U_S2sActive = 1
  and ocpr.Pager = 'Y'
)
                                                as active,

(
  select count(*) as count
  from ocpr WITH (NOLOCK)
  INNER JOIN ocrd on ocrd.CardCode=ocpr.CardCode
  INNER JOIN OVTG on ovtg.Code=ocrd.ECVatGroup
  LEFT JOIN CRD1 on CRD1.CardCode = ocpr.CardCode and ocpr.Notes2 = CRD1.Address
  LEFT JOIN OSLP on ocrd.SlpCode = OSLP.SlpCode
  where 1 = 1
  and (
      ocrd.U_S2sActive = 1 or
      ocrd.U_S2sActive = 0
  )
  and ocrd.CardType = 'C'
  and ocrd.CardName not like '%(closed)'
  and datalength(ocpr.E_MailL) <> 0
  and ocpr.E_MailL is not null
  and ocpr.FirstName is not null
  and ocpr.LastName is not null
  and (ocrd.U_S2sActive <> 1 or ocpr.Pager <> 'Y')

)
                                                as inactive
;
