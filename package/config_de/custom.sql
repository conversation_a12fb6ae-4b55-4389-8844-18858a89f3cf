select *
from (
select
    row_number() over (order by oitm.ItemCode) as n,

    -- There must only be one result row per ItemCode!
    oitm.ItemCode               as "source_product_code",
    oitm.ItemCode               as "source_variant_code",

    -- If Brand and CategoryExt not empty, just append part number and alternative, otherwise set it as the title
    CASE
        WHEN LTRIM(RTRIM(ISNULL(convert(varchar, oitm.U_Brand) + ' ', '') + convert(varchar, oitm.U_CategoryExt))) <> ''
        THEN ISNULL(convert(varchar, oitm.U_Brand) + ' ', '') + convert(varchar, oitm.U_CategoryExt) + ' (' + ISNULL(oitm.ItemName, '') + ' - ' +
                ISNULL(stuff(
                (select
                ',' + convert(varchar, oali.AltItem)
                from oali WITH (NOLOCK)
                where oitm.ItemCode = oali.OrigItem
                order by oali.AltItem
                for xml path('')),1,1,''), '')
            + ')'
        ELSE ISNULL(oitm.ItemName, '') + ' - ' +
            ISNULL(stuff(
            (select
            ',' + convert(varchar, oali.AltItem)
            from oali WITH (NOLOCK)
            where oitm.ItemCode = oali.OrigItem
            order by oali.AltItem
            for xml path('')),1,1,''), '')
    END                         as "title",

    oitm.U_Groups               as "collection",
    oitm.U_CategoryExt          as "product_type",
    oitm.U_Brand                as "vendor",
    ''                          as "variants.option1",
    ''                          as "variants.option2",
    ''                          as "variants.option3",
    ''                          as "body_html",
    0                           as "variants.weight",
    oitm.CodeBars               as "variants.barcode",

    -- All price tiers
    '5'                         as "default_price_tier",
    stuff(
    (select
    convert(varchar, itm1.PriceList) + '|' +
    convert(varchar, itm1.Price) + ','
    from itm1 WITH (NOLOCK)
    where itm1.ItemCode = oitm.ItemCode
    order by itm1.PriceList
    for xml path('')),1,0,'')
                                as "csv_price_tiers",

    -- All qty availability
    'W_02'                      as "default_qty_availability",
    stuff(
    (select
    convert(varchar, oitw.WhsCode) + '|' +
    convert(varchar, oitw.OnHand-oitw.IsCommited)+','
    from oitw WITH (NOLOCK)
    where oitw.ItemCode = oitm.ItemCode
    order by oitw.WhsCode
    for xml path('')),1,0,'')
                                as "csv_qty_availability",

    'true'                      as "variants.inventory_management",

    case oitm.U_S2sActive
        when 1 then 'true'
        when 0 then 'false'
        else 'false'
    end                         as "product_active",

    stuff(
    (select convert(varchar, oali.AltItem) + ','
    from oali WITH (NOLOCK)
    where oitm.ItemCode = oali.OrigItem
    order by oali.AltItem
    for xml path('')),1,0,'')
                                as 'tags',

    oitb.ItmsGrpCod             as "meta_item_group_code",
    oitb.ItmsGrpNam             as "meta_item_group_name",
    oitm.U_Category             as "meta_category",
    oitm.ItemName               as "meta_part_number",
    oitm.SalUnitMsr             as "meta_qty_multiples_of",

     -- Special prices for all periods and volumes
     stuff(
     (select
     convert(varchar, ospp.CardCode) + '|' +
     convert(varchar, ospp.Discount)+','
     from ospp WITH (NOLOCK)
     where ospp.ItemCode = oitm.ItemCode
     -- and ospp.Discount > 0
     order by ospp.ItemCode
     for xml path('')),1,0,'')
                                 as 'csv_special_prices',

 	-- Special prices for all periods and volumes
     stuff(
     (select
     convert(varchar, ospp.CardCode) + '|' +
     convert(varchar, ospp.Price)+','
     from ospp WITH (NOLOCK)
     where ospp.ItemCode = oitm.ItemCode
     -- and ospp.Discount > 0
     order by ospp.ItemCode
     for xml path('')),1,0,'')
                                 as 'csv_special_prices_fixed',

--      stuff(
--      (select
--      ',<a href="/#/products?q=' + convert(varchar, oali.AltItem) + '">' + convert(varchar, oali.AltItem) + '</a>'
--      from oali WITH (NOLOCK)
--      where oitm.ItemCode = oali.OrigItem
--      order by oali.AltItem
--      for xml path('')),1,1,'')
--                                  as 'meta_Alternatives',

     -- Special prices for promotions
     stuff(
     (select
     convert(varchar, ish.U_CGcode) + '|' +
     case when DATEADD(day, DATEDIFF(day,0,GETDATE()),0) <= ish.U_To
     then convert(varchar, isl.U_Discount)+','
     else '0,'
     end
       FROM [@ICO_ISL] isl WITH (NOLOCK)
       INNER JOIN [@ICO_ISH] ish on ish.DocEntry=isl.DocEntry
        where U_Itemcode = oitm.ItemCode
 --         and isl.U_Discount > 0
 --         and ish.U_To is not null
 -- 	      and ish.U_From is not null
        order by ish.U_CGcode
     for xml path('')),1,0,'')
                                 as 'csv_promotional_discounts',

     -- Discount per customer group code
     stuff(
     (SELECT
         convert(varchar, T0.ObjCode) + '|' +
         CASE WHEN T0.ValidFor='Y' then
         convert(varchar, T1.[Discount])
         ELSE
         '0'
         END

          + ','
       FROM OEDG T0 WITH (NOLOCK) INNER JOIN EDG1 T1 ON T0.[AbsEntry] = T1.[AbsEntry]
       WHERE
       T1.[ObjKey] = oitb.ItmsGrpCod
       and T0.ObjType = 10
       and T1.ObjType = 52 -- Discount Groups
 	    and T0.Type = 'C'
       and T1.Discount > 0
       order by T0.ObjCode
       for xml path('')),1,0,'')       as 'csv_discount_customer_groups'




    -- Special prices for period join ospp with spp1
    -- Special prices for volume join ospp with spp2

from oitm WITH (NOLOCK)
join oitb on oitm.ItmsGrpCod = oitb.ItmsGrpCod
where 1 = 1
-- Only track products that have a value for U_S2sActive
and (
    U_S2sActive = 1 or
    U_S2sActive = 0
)

) as rows
where
n > 0
and n <= 2000



