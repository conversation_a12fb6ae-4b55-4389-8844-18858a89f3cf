select

    -- bug with push.exe causes source_customer_code to be converted to int even though it
    -- is a string. Concatenate it to avoid this.
    ocpr.CardCode + '-' + convert(varchar, ocpr.CntctCode)      as 'source_customer_code',
    ocpr.FirstName                                              as 'first_name',
	  ocpr.LastName                                               as 'last_name',
	  ocrd.CardName                                               as 'card_name',
    ocpr.E_MailL                                                as 'email',

    case convert(varchar, ocrd.U_S2sActive) + ocpr.Pager
      when '1Y' then 'true'
      else 'false'
    end                                                         as "customer_active",

    1                                                           as 'accepts_marketing',
    CASE WHEN CRD1.street is null then ocrd.MailAddres
      ELSE CRD1.street
    END                                                         as 'address.address1',
    ''                                                          as 'address.address2',
    CASE WHEN CRD1.city is null then ocrd.City
      ELSE CRD1.city
    END
                                                                as 'address.city',
    ocrd.Country                                                as 'address.country',
    'ZA'                                                        as 'address.country_code',
    ''                                                          as 'address.province',
    ''                                                          as 'address.province_code',
    CASE WHEN CRD1.ZipCode is null then ocrd.ZipCode
      ELSE CRD1.ZipCode
    END
                                                                as 'address.zip',
    ''                                                          as 'address.company',
    ocrd.CntctPrsn                                              as 'address.contact_person',
    ocrd.Phone1                                                 as 'address.phone',

    -- XML addresses
    (
      SELECT
          crd1.Address    as 'address_code',
          CASE crd1.AdresType
          WHEN 'B' THEN 'billing'
          WHEN 'S' THEN 'shipping'
          END
                          as 'type',
          coalesce(crd1.Street, '')     as 'address1',
          coalesce(crd1.Address2, '')   as 'address2',
          coalesce(crd1.City, '')       as 'city',
          coalesce(crd1.Country, '')    as 'country',
          coalesce(crd1.Country, '')    as 'country_code',
          coalesce(crd1.County, '')     as 'province',
          coalesce(crd1.State, '')      as 'province_code',
          coalesce(crd1.ZipCode, '')    as 'zip',
          coalesce(ocrd.Phone1, '')     as 'phone',
          coalesce(ocrd.CardName, '')   as 'company',
          coalesce(ocpr.FirstName, '')  as 'first_name',
          coalesce(ocpr.LastName, '')   as 'last_name'
       FROM crd1
       WHERE crd1.CardCode=ocpr.CardCode and crd1.AdresType in ('S','B')
       ORDER BY crd1.Address
       FOR xml PATH ('address'), root ('addresses')
    )                                                           as 'xml_addresses',

    ocrd.ShipType                                               as 'meta_ship_type',
    ocrd.GroupCode                                              as 'meta_customer_group_code',
	  ocrd.ECVatGroup                                             as 'meta_tax_group_code',
	  ovtg.Rate                                                   as 'meta_tax_rate',
	  ocpr.CardCode                                               as 'meta_source_customer_code',
	  ocrd.U_RouteC                                               as 'meta_delivery_route',
	  CASE LEFT(ocpr.CardCode, 2)
	   WHEN 'CV' THEN 'COLLECT'
	   WHEN 'CS' THEN 'COUNTER'
	   WHEN 'CB' THEN 'COLLECTB'
	  END           as 'meta_collection_route',


    -- customer group promotions
    -- SELECT T0.[CardCode], T0.[GroupCode] FROM OCRD T0  INNER JOIN OCRG T1 ON T0.[GroupCode] = ocrd.CardCode
    -- SELECT T0.[U_CGcode], T0.[U_CGName], T0.[U_From], T0.[U_To], T1.[U_Itemcode], T1.[U_Discount] FROM [dbo].[@ICO_ISH]  T0 , [dbo].[@ICO_ISL]  T1 WHERE T0.[U_CGcode] = ocrd.CardCode

    -- Discount groups per item group code
    stuff(
    (SELECT
        convert(varchar, T1.[ObjKey]) + '|' +
        CASE WHEN T0.ValidFor='Y' THEN
        convert(varchar, T1.[Discount])
        ELSE '0'
        END
         + ','
      FROM OEDG T0 WITH (NOLOCK) INNER JOIN EDG1 T1 ON T0.[AbsEntry] = T1.[AbsEntry]
      WHERE T0.[ObjCode] = ocrd.CardCode
      and T0.ObjType = 2
      and T1.ObjType = 52 -- Discount Groups
      and T1.Discount > 0
      order by T1.ObjKey
      for xml path('')),1,0,'')                                 as 'csv_discount_groups',

    -- For customers

    ListNum    as 'price_tier',

    case
    when ocrd.QryGroup4  = 'Y' then 'W_04' -- BLM
    when ocrd.QryGroup5  = 'Y' then 'W_02' -- Ver
    when ocrd.QryGroup14 = 'Y' then 'W_05' -- SPR
    end
                as 'qty_availability',
    case
    when ocrd.QryGroup4  = 'Y' then
        (SELECT T0.[U_MAILACC] FROM [dbo].[@ICO_MAILGROUP] T0
        WHERE T0.[Code] = 'W_04')
    when ocrd.QryGroup5  = 'Y' then
        (SELECT T0.[U_MAILACC] FROM [dbo].[@ICO_MAILGROUP] T0
        WHERE T0.[Code] = 'W_02')
    when ocrd.QryGroup14  = 'Y' then
        (SELECT T0.[U_MAILACC] FROM [dbo].[@ICO_MAILGROUP] T0
        WHERE T0.[Code] = 'W_05')
    end
                as 'meta_source_order_emails',

-- segment on a customer based on the attribute meta_product_division, this is a lookup segment
CASE WHEN (CONCAT(
        (CASE WHEN LTRIM(RTRIM(LOWER(ocrd.U_Div_Electrical))) = 'yes'
        THEN 'ELECTRICAL,'
        END),
        (CASE WHEN LTRIM(RTRIM(LOWER(ocrd.U_Div_Diesel))) = 'yes'
        THEN 'DIESEL,'
        END),
        (CASE WHEN LTRIM(RTRIM(LOWER(ocrd.U_Div_Commercial))) = 'yes'
        THEN 'COMMERCIAL,'
        END),
        (CASE WHEN LTRIM(RTRIM(LOWER(ocrd.U_Div_Aircon))) = 'yes'
        THEN 'AIRCON'
        END))) = '' or NULL THEN 'ALL'
    END               as 'segment|source|products|meta_product_division|lookup'


from ocpr WITH (NOLOCK)
INNER JOIN ocrd on ocrd.CardCode=ocpr.CardCode
INNER JOIN OVTG on ovtg.Code=ocrd.ECVatGroup
LEFT JOIN CRD1 on CRD1.CardCode = ocpr.CardCode and ocpr.Notes2 = CRD1.Address

where 1 = 1
and (
    ocrd.U_S2sActive = 1 or
    ocrd.U_S2sActive = 0
)
and ocrd.CardType = 'C'
and ocrd.CardName not like '%(closed)'
and datalength(ocpr.E_MailL) <> 0
and ocpr.E_MailL is not null
and ocpr.FirstName is not null
and ocpr.LastName is not null
and ocpr.CardCode + '-' + convert(varchar, ocpr.CntctCode) = 'CV5307-9972';

