select

    -- There must only be one result row per ItemCode!
    oitm.ItemCode               as "source_product_code",
    oitm.ItemCode               as "source_variant_code",

    -- If Brand and CategoryExt not empty, just append part number and alternative, otherwise set it as the title
    CASE
        WHEN LTRIM(RTRIM(ISNULL(convert(varchar, oitm.U_Brand) + ' ', '') + convert(varchar, oitm.U_CategoryExt))) <> ''
        THEN ISNULL(convert(varchar, oitm.U_Brand) + ' ', '') + convert(varchar, oitm.U_CategoryExt) + ' (' + ISNULL(oitm.ItemName, '') + ' - ' +
                ISNULL(stuff(
                (select
                ',' + convert(varchar, oali.AltItem)
                from oali WITH (NOLOCK)
                where oitm.ItemCode = oali.OrigItem
                order by oali.AltItem
                for xml path('')),1,1,''), '')
            + ')'
        ELSE ISNULL(oitm.ItemName, '') + ' - ' +
            ISNULL(stuff(
            (select
            ',' + convert(varchar, oali.AltItem)
            from oali WITH (NOLOCK)
            where oitm.ItemCode = oali.OrigItem
            order by oali.AltItem
            for xml path('')),1,1,''), '')
    END                         as "title",

    oitm.U_Groups               as "collection",
    oitm.U_CategoryExt          as "product_type",
    oitm.U_Brand                as "vendor",
    ''                          as "variants.option1",
    ''                          as "variants.option2",
    ''                          as "variants.option3",
    ''                          as "body_html",
    0                           as "variants.weight",
    oitm.CodeBars               as "variants.barcode",

    -- All price tiers
    '5'                         as "default_price_tier",
    stuff(
    (select
    convert(varchar, itm1.PriceList) + '|' +
    convert(varchar, itm1.Price) + ','
    from itm1 WITH (NOLOCK)
    where itm1.ItemCode = oitm.ItemCode
    order by itm1.PriceList
    for xml path('')),1,0,'')
                                as "csv_price_tiers",

    -- All qty availability
    'W_02'                      as "default_qty_availability",
   stuff(
        (select
        convert(varchar, oitw.WhsCode) + '|' +
         convert(varchar,
         case when (oitw.OnHand-oitw.IsCommited) < 0
          then 0.000000
          else (oitw.OnHand-oitw.IsCommited)
         end
         )+','
        from oitw with (nolock)
        where oitw.ItemCode = oitm.ItemCode
        order by oitw.WhsCode
        for xml path('')),1,0,'')

                                as "csv_qty_availability",

    'true'                      as "variants.inventory_management",

    case oitm.U_S2sActive
        when 1 then 'true'
        when 0 then 'false'
        else 'false'
    end                         as "product_active",

    stuff(
    (select convert(varchar, oali.AltItem) + ','
    from oali WITH (NOLOCK)
    where oitm.ItemCode = oali.OrigItem
    order by oali.AltItem
    for xml path('')),1,0,'')
                                as 'tags',

    oitb.ItmsGrpCod             as "meta_item_group_code",
    oitb.ItmsGrpNam             as "meta_item_group_name",
    oitm.U_Category             as "meta_category",
    oitm.U_CategoryExt2         as "meta_category_ext2",
    oitm.ItemName               as "meta_part_number",
    oitm.U_MinOrQty             as "meta_qty_multiples_of",
    '2021-11-24'                as "meta_full_sync",

    -- Do not set if division is "AIRCON"
    (
        CASE 
            WHEN LTRIM(RTRIM(UPPER(oitm.U_Division))) = 'AIRCON'
                THEN ''
            ELSE oitm.usertext      
        END
    )
                                as "meta_additional_info",

    LTRIM(RTRIM(UPPER(oitm.U_Division)))
                                as "meta_product_division",

    --All price tiers as a meta
    stuff(
    (select
    convert(varchar, itm1.PriceList) + '|' +
    convert(varchar, itm1.Price) + ','
    from itm1 WITH (NOLOCK)
    where itm1.ItemCode = oitm.ItemCode
    order by itm1.PriceList
    for xml path('')),1,0,'')
                                as "csv_price_tier",

    -- Special prices for all periods and volumes
    stuff(
    (select
    convert(varchar, ospp.CardCode) + '|' +
    convert(varchar, ospp.Discount)+','
    from ospp WITH (NOLOCK)
    where ospp.ItemCode = oitm.ItemCode
    -- and ospp.Discount > 0
    order by ospp.ItemCode
    for xml path('')),1,0,'')
                                as 'csv_special_prices',

	-- Special prices for all periods and volumes
    stuff(
    (select
    convert(varchar, ospp.CardCode) + '|' +
    convert(varchar, ospp.Price)+','
    from ospp WITH (NOLOCK)
    where ospp.ItemCode = oitm.ItemCode
    -- and ospp.Discount > 0
    order by ospp.ItemCode
    for xml path('')),1,0,'')
                                as 'csv_special_prices_fixed',

       -- TODO add this back after index created it causes performance issues
    stuff(
    (select
    ',<a href="/#/products?q=' + convert(varchar, oali.AltItem) + '">' + convert(varchar, oali.AltItem) + '</a>'
    from oali WITH (NOLOCK)
    where oitm.ItemCode = oali.OrigItem
    order by oali.AltItem
    for xml path('')),1,1,'')
                                as 'meta_Alternatives',

    -- Recommended products
    concat(
        oitm.U_Recommend1,',',
        oitm.U_Recommend2,',',
        oitm.U_Recommend3,',',
        oitm.U_Recommend4,',',
        oitm.U_Recommend5,',',
        oitm.U_Recommend6,',',
        oitm.U_Recommend7,',',
        oitm.U_Recommend8,',',
        oitm.U_Recommend9,',',
        oitm.U_Recommend10
    )                          AS "meta_recommended",
    
    -- BOM products
    concat(
        oitm.U_BOM1,'-',oitm.U_BOM1Desc,',', 
        oitm.U_BOM2,'-',oitm.U_BOM2Desc,',', 
        oitm.U_BOM3,'-',oitm.U_BOM3Desc,',', 
        oitm.U_BOM4,'-',oitm.U_BOM4Desc,',', 
        oitm.U_BOM5,'-',oitm.U_BOM5Desc,',', 
        oitm.U_BOM6,'-',oitm.U_BOM6Desc,',', 
        oitm.U_BOM7,'-',oitm.U_BOM7Desc,',', 
        oitm.U_BOM8,'-',oitm.U_BOM8Desc,',', 
        oitm.U_BOM9,'-',oitm.U_BOM9Desc,',', 
        oitm.U_BOM10,'-',oitm.U_BOM10Desc
    )                           AS "meta_bom",

    -- oenr tags
    ISNULL(oitm.U_BOM1D, '')    AS "meta_oenr_tag1",
    ISNULL(oitm.U_BOMD2, '')    AS "meta_oenr_tag2",
    ISNULL(oitm.U_BOMD3, '')    AS "meta_oenr_tag3",
    ISNULL(oitm.U_BOMD4, '')    AS "meta_oenr_tag4",
    ISNULL(oitm.U_BOMD5, '')    AS "meta_oenr_tag5",
    ISNULL(oitm.U_BOMD6, '')    AS "meta_oenr_tag6",
    ISNULL(oitm.U_BOMD7, '')    AS "meta_oenr_tag7",
    ISNULL(oitm.U_BOMD8, '')    AS "meta_oenr_tag8",
    ISNULL(oitm.U_BOMD9, '')    AS "meta_oenr_tag9",
    ISNULL(oitm.U_BOMD10, '')   AS "meta_oenr_tag10",
    ISNULL(oitm.U_BOMD11, '')   AS "meta_oenr_tag11",
    ISNULL(oitm.U_BOMD12, '')   AS "meta_oenr_tag12",

    -- Special prices for promotions
    stuff(
    (select
        convert(varchar, ish.U_CGcode) + '|' + convert(varchar, isl.U_Discount)+','
      FROM [@ICO_ISL] isl WITH (NOLOCK)
      INNER JOIN [@ICO_ISH] ish on ish.DocEntry=isl.DocEntry
        where U_Itemcode = oitm.ItemCode
          and DATEADD(day, DATEDIFF(day,0,GETDATE()),0) <= ish.U_To
--         and isl.U_Discount > 0
--         and ish.U_To is not null
-- 	      and ish.U_From is not null
       order by ish.U_CGcode
    for xml path('')),1,0,'')
                                as 'csv_promotional_discounts',

    -- Discount per customer group code
    stuff(
    (SELECT
        convert(varchar, T0.ObjCode) + '|' +
        CASE WHEN T0.ValidFor='Y' then
        convert(varchar, T1.[Discount])
        ELSE
        '0'
        END

         + ','
      FROM OEDG T0 WITH (NOLOCK) INNER JOIN EDG1 T1 ON T0.[AbsEntry] = T1.[AbsEntry]
      WHERE
      T1.[ObjKey] = oitb.ItmsGrpCod
      and T0.ObjType = 10
      and T1.ObjType = 52 -- Discount Groups
	    and T0.Type = 'C'
      and T1.Discount > 0
      order by T0.ObjCode
      for xml path('')),1,0,'')       as 'csv_discount_customer_groups'




    -- Special prices for period join ospp with spp1
    -- Special prices for volume join ospp with spp2

from oitm WITH (NOLOCK)
join oitb on oitm.ItmsGrpCod = oitb.ItmsGrpCod
where 1 = 1
-- Only track products that have a value for U_S2sActive
and (
    U_S2sActive = 1 or
    U_S2sActive = 0
)

order by oitm.ItemCode
offset '%(audit_lower_limit)n' ROWS FETCH NEXT ('%(audit_upper_limit)n' - '%(audit_lower_limit)n') ROWS ONLY



