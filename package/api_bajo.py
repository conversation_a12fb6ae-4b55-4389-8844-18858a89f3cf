import json
import imp
import collections
import math
import xml.sax.saxutils as saxutils
from .controllers import base_push as base

class Api(base.Api):
    _configDir = "config_bajo"

    def reload(self):
        imp.reload(base)
        base.Api.reload(self)
        response = {
            "status": True,
            "data": {
                "reload": True
            }
        }
        return json.dumps(response, indent=self._indent)

    # Add _transformProduct here to override default
    def _transformProduct(self, params, row, auditRow):
        """
        Given the result of getProduct we return a dictionary
        with required structure ready to be converted to JSON.
        """
        source_product = collections.OrderedDict()
        source_product["source"] = collections.OrderedDict()
        source_product["source"]["source_id"] = params["source_id"]
        source_product["source"]["product_active"] = row["product_active"]
        source_product["source"]["source_product_code"] = row["source_product_code"]
        source_product["source"]["sync_token"] = auditRow[self._productIndex["sync_token"]]

        source_product["product"] = collections.OrderedDict()
        source_product["product"]["options"] = []
        source_product["product"]["body_html"] = row["body_html"]
        source_product["product"]["collection"] = row["collection"]
        source_product["product"]["product_type"] = row["product_type"]
        source_product["product"]["tags"] = str(row["tags"]).lower()
        source_product["product"]["title"] = row["title"]
        source_product["product"]["vendor"] = row["vendor"]

        source_product["product"]["variants"] = collections.OrderedDict()
        source_product["product"]["variants"]["source_variant_code"] = row["source_variant_code"]
        source_product["product"]["variants"]["sku"] = row["source_variant_code"]
        source_product["product"]["variants"]["barcode"] = row["variants.barcode"]

        source_product["product"]["variants"]["inventory_management"] = row["variants.inventory_management"]

        source_product["product"]["meta"] = self._getProductMeta(row)
        productJson = json.loads(auditRow[self._productIndex["json"]])
        if "meta_delete" in productJson:
            source_product["product"]["meta_delete"] = productJson["meta_delete"]

        # Parse price tiers
        default_price = 0
        source_product["product"]["variants"]["price_tiers"] = []
        if row["csv_price_tiers"] is not None:
            # The stuff function used in the sql queries
            # will encode special characters as html entities,
            # for example, "&" becomes "&amp;".
            # Call html.unescape to convert back to special characters
            prices = saxutils.unescape(row["csv_price_tiers"])
            prices = prices.split(",")
            for priceStr in prices:
                priceItem = priceStr.split("|")
                if len(priceItem) == 2 and priceItem[0].strip() != "":
                    tier = priceItem[0]
                    # Throw away everything after the second decimal
                    price = float("{0:.2f}".format(float(priceItem[1])))
                    if tier == row["default_price_tier"]:
                        default_price = price
                    price_tier = collections.OrderedDict()
                    price_tier["tier"] = tier
                    price_tier["price"] = price

                    ## custom code to reset special price (price list 5) if not given
                    ## this transform should happen on the channel via the transform
                    ## since magento channel does not yet use mustache, doing this is not possible
                    if tier == "1":
                        retail = price
                    if tier == "5":
                        meta = collections.OrderedDict()
                        meta["key"] = "on_sale"
                        if price == 0:
                            price_tier["price"] = retail
                            meta["value"] = "false"
                        else:
                            meta["value"] = "true"
                        source_product["product"]["meta"].append(meta)

                        meta = collections.OrderedDict()
                        meta["key"] = "sale_price"
                        meta["value"] = str(price)
                        source_product["product"]["meta"].append(meta)

                    source_product["product"]["variants"]["price_tiers"].append(price_tier)
        source_product["product"]["variants"]["price"] = default_price

        # Parse qty availability
        default_qty = 0
        source_product["product"]["variants"]["qty_availability"] = []
        if row["csv_qty_availability"] is not None:
            quantities = saxutils.unescape(row["csv_qty_availability"])
            quantities = quantities.split(",")
            for qtyStr in quantities:
                qtyItem = qtyStr.split("|")
                if len(qtyItem) == 2 and qtyItem[0].strip() != "":
                    description = qtyItem[0]
                    # Qty must be an integer
                    qty = math.trunc(float(qtyItem[1]))
                    if description == row["default_qty_availability"]:
                        default_qty = qty
                    availability = collections.OrderedDict()
                    availability["qty"] = qty
                    availability["description"] = description
                    source_product["product"]["variants"]["qty_availability"].append(availability)

                    ## custom code for stock level display
                    ## this transform should happen on the channel via the transform
                    ## since magento channel does not yet use mustache, doing this is not possible
                    if description == "PYL_PV&A":
                        meta = collections.OrderedDict()
                        meta["key"] = "warehouse_va"
                        if qty > 0:
                            meta["value"] = "In Stock"
                        else:
                            meta["value"] = "Out of Stock"
                        source_product["product"]["meta"].append(meta)

                    if description == "PYL_PSAN":
                        meta = collections.OrderedDict()
                        meta["key"] = "warehouse_sandton"
                        if qty > 0:
                            meta["value"] = "In Stock"
                        else:
                            meta["value"] = "Out of Stock"
                        source_product["product"]["meta"].append(meta)

                    if description == "PYL_PMOA":
                        meta = collections.OrderedDict()
                        meta["key"] = "warehouse_moa"
                        if qty > 0:
                            meta["value"] = "In Stock"
                        else:
                            meta["value"] = "Out of Stock"
                        source_product["product"]["meta"].append(meta)

                    if description == "PYL_PMEN":
                        meta = collections.OrderedDict()
                        meta["key"] = "warehouse_menlyn"
                        if qty > 0:
                            meta["value"] = "In Stock"
                        else:
                            meta["value"] = "Out of Stock"
                        source_product["product"]["meta"].append(meta)

        source_product["product"]["variants"]["qty"] = default_qty



        return source_product

    def createOrder(self):
        self._setConfig()
        raise Exception("Not implemented")
