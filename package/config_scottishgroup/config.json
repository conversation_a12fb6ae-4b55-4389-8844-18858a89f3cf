{"dbName": "local.db", "dsn": "Driver={SQL Server};Server=SKGJHB-POS-01;Database=IntegritySAHO;Uid=Ecomplete;Pwd=*******;", "audit_limit": 1000, "push": {"source_id": 518, "limit": 500, "token": "133f77173fc955b9568f00bad9a03c07e5bae746", "writeLog": "http://localhost:1337/writeLog", "getProducts": "http://localhost:1337/odbc/getProductsBatch", "getCustomers": "http://localhost:1337/odbc/getCustomersBatch", "countProducts": "http://localhost:1337/odbc/countProducts", "countCustomers": "http://localhost:1337/odbc/countCustomers", "getMeta": "http://localhost:1337/odbc/getMeta", "setMeta": "http://localhost:1337/odbc/setMeta", "productsQueue": "https://app.stock2shop.com/v1/products/queue", "customersQueue": "https://app.stock2shop.com/v1/customers/queue"}}