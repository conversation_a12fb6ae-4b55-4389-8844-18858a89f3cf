import json
import imp
from .controllers import base_push as base

class Api(base.Api):
    _configDir = "config_internetexpress"

    def reload(self):
        imp.reload(base)
        base.Api.reload(self)
        response = {
            "status": True,
            "data": {
                "reload": True
            }
        }
        return json.dumps(response, indent=self._indent)

    # Add _transformProduct here to override default