SELECT
    RTRIM(HH.OrderNumber) as "OrderNumber",
    RTRIM(HH.DocumentNumber) as "DocumentNumber",
    HH.DelAddress01,
    HH.DelAddress02,
    HH.<PERSON>ddress03,
    H<PERSON><PERSON>ddress04,
    H<PERSON><PERSON>ress05,
    C<PERSON><PERSON>PostAddress01,
    C<PERSON>.PostAddress02,
    CM.PostAddress03,
    CM.PostAddress04,
    CM.PostAddress05,
    CM.CustomerDesc,
    CM.CustomerCode,
    CM.ExemptRef,
    HL.TaxType,
    HH.DiscountPercent,
    HL.ItemCode,
    HL.Qty,
    HL.Description,
    cast(HL.UnitPrice as decimal(8,2)) as UnitPrice,
    cast(HL.InclusivePrice as decimal(8,2)) as InclusivePrice,
    HL.DiscountType,
    HL.DiscountPercentage / 100 as DiscountPercentage,
    (
      SELECT top 1 email from DeliveryAddresses where CustomerCode = HH.CustomerCode
    ) as "Email",
    (
      SELECT top 1 Telephone from DeliveryAddresses where CustomerCode = HH.CustomerCode
    ) as "Telephone",
    (
      SELECT top 1 Contact from DeliveryAddresses where CustomerCode = HH.CustomerCode
    ) as "Contact"
FROM HistoryHeader HH
INNER JOIN HistoryLines HL on HL.DocumentNumber = HH.DocumentNumber
LEFT JOIN CustomerMaster CM on CM.CustomerCode = HH.CustomerCode
WHERE HH.DocumentNumber = '%(source_order_code)s';