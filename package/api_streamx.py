import json
import imp
import os
from .shared import utils
from .controllers import base_push as base

class Api(base.Api):
    _configDir = "config_streamx"

    def reload(self):
        imp.reload(base)
        base.Api.reload(self)
        response = {
            "status": True,
            "data": {
                "reload": True
            }
        }
        return json.dumps(response, indent=self._indent)

    def _checkInstall(self):
        dbPath = self._getDbPath()
        table_results = []
        index_results = []

        with utils.openSqliteConn(self._getDbPath()) as cursor:
            if os.path.isfile(dbPath):
                sql = "select * from sqlite_master where type='table';"
                cursor.execute(sql)
                table_results = cursor.fetchall()

                sql = "select * from sqlite_master where type='index';"
                cursor.execute(sql)
                index_results = cursor.fetchall()

        # Create tables
        if len(table_results) == 0:
            with utils.openSqliteConn(self._getDbPath(), commit=True) as cursor:
                # If not installed then create a new sqlite database
                cursor.execute(
                    "create table product (source_product_code, source_variant_code, hash, sync_token, modified)")
                cursor.execute(
                    "create table customer (source_customer_code, hash, sync_token, modified)")
                cursor.execute(
                    "create table image (source_variant_code, hash, modified)")
                cursor.execute("""create table meta (key, value)""")

        # Create indexes
        if len(index_results) == 0:
            with utils.openSqliteConn(self._getDbPath(), commit=True) as cursor:
                # product table indexes
                cursor.execute(
                    "CREATE INDEX idx_product_source_product_code_source_variant_code ON product(`source_product_code`,`source_product_code`)")
                cursor.execute(
                    "CREATE INDEX idx_product_sync_token ON product(`sync_token`)")

                # customer table indexes
                cursor.execute(
                    "CREATE INDEX idx_customer_source_customer_code ON customer(`source_customer_code`)")
                cursor.execute(
                    "CREATE INDEX idx_customer_sync_token ON customer(`sync_token`)")

                # image table indexes
                cursor.execute(
                    "CREATE INDEX idx_image_source_image_code ON image(`source_image_code`)")
                cursor.execute(
                    "CREATE INDEX idx_image_sync_token ON image(`sync_token`)")
