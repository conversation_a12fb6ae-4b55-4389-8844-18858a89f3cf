select
    CASE
        WHEN RTRIM(ISNULL(i.UserDefText02, '')) <> '' THEN rtrim(i.UserDefText02)
        ELSE rtrim(i.ItemCode)
    END                       as "source_product_code",
    rtrim(i.GUID)             as "source_variant_code",
    rtrim(i.Description)      as "title",
    rtrim(i.ItemCode)         as "variants.sku",
    rtrim(ic.ICDesc)          as "collection",
--     rtrim(i.UserDefText02)    as "collection",
    ''                        as "product_type",
    i.UserDefText01           as "vendor",
    'Selection'               as "variants.option1_name",
    i.UserDefText03           as "variants.option1_value",
    ''                        as "variants.option2",
    ''                        as "variants.option3",
    ''                        as "body_html",
    rtrim(i.NettMass)         as "variants.weight",
    rtrim(i.Barcode)          as "variants.barcode",

    -- Warehouse 1 qty
    (
      (
        (wh1.OpeningQty + wh1.QtyBuyLast + wh1.QtyAdjustLast - wh1.QtySellLast) +
        (wh1.QtyBuyThis01 + wh1.QtyBuyThis02 + wh1.QtyBuyThis03 + wh1.QtyBuyThis04 +
            wh1.QtyBuyThis05 + wh1.QtyBuyThis06 + wh1.QtyBuyThis07 + wh1.QtyBuyThis08 +
            wh1.QtyBuyThis09 + wh1.QtyBuyThis10 + wh1.QtyBuyThis11 + wh1.QtyBuyThis12 +
            wh1.QtyBuyThis13) +
        (wh1.QtyAdjustThis01 + wh1.QtyAdjustThis02 + wh1.QtyAdjustThis03 +
            wh1.QtyAdjustThis04 + wh1.QtyAdjustThis05 + wh1.QtyAdjustThis06 +
            wh1.QtyAdjustThis07 + wh1.QtyAdjustThis08 + wh1.QtyAdjustThis09 +
            wh1.QtyAdjustThis10 + wh1.QtyAdjustThis11 + wh1.QtyAdjustThis12 +
            wh1.QtyAdjustThis13) -
        (wh1.QtySellThis01 + wh1.QtySellThis02 + wh1.QtySellThis03 + wh1.QtySellThis04 +
            wh1.QtySellThis05 + wh1.QtySellThis06 + wh1.QtySellThis07 + wh1.QtySellThis08 +
            wh1.QtySellThis09 + wh1.QtySellThis10 + wh1.QtySellThis11 + wh1.QtySellThis12 +
            wh1.QtySellThis13)
      )
    )                         as "qty_on_hand",
    (
      select sum(QtyLeft)
      from HistoryLines where
        Documenttype = 102 and
        ItemCode = i.ItemCode and
        MultiStore = wh1.StoreCode
    )                         as "qty_on_sales_order",

    -- Warehouse 1 prices
    -- Note that price can be set by warehouse (store)
    wh1.SellIncl01            as "variants.retail_price",
    wh1.SellIncl02            as "variants.dealer_price",
    wh1.SellIncl03            as "variants.distribution_price",
    wh1.SellIncl05            as "variants.wholesale_price",
    case
        when i.UserDefNum01 = 1 then 'true'
        else 'false'
    end                       as "product_active",
    ''                        as "tags",
    rtrim(ig.Description)     as "inventory_group",
    'true'                    as "variants.inventory_management",
    i.Picture                 as "picture"

from Inventory i

    -- Warehouse 1
    left join MultiStoreTrn wh1 on
      wh1.ItemCode = i.ItemCode and
      wh1.StoreCode = '%(multi_store)s'
    left join InventoryGroups ig on
      ig.InvGroup = wh1.InvGroup

    -- Attribute info
    left join InventoryCategory ic on
      i.Category = ic.ICCode

WHERE i.UserDefNum01 IN (1, 2) AND wh1.StoreCode = '%(multi_store)s' AND RTRIM(ISNULL(i.GUID, '')) <> '';
