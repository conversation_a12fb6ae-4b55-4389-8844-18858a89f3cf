select

    RTRIM(LTRIM(inv.Description))                     AS "source_product_code",
    RTRIM(LTRIM(inv.StockCode))                       AS "source_variant_code",
    RTRIM(LTRIM(inv.Description))                     AS "title",
    RTRIM(LTRIM(inv2.ProductByApplicati))             AS "collection",
    RTRIM(LTRIM(spcd.Description))                    AS "product_type",
    ''                                                AS "vendor",
    'Option'                                          AS "variants.option1_name",
    RTRIM(LTRIM(cfcd.Description))                    AS "variants.option1_value",
    RTRIM(LTRIM(inv.StockCode))                       AS "variants.sku",
    ''                                                AS "body_html",
    CAST(ISNULL(inv.Mass, 0) AS INTEGER)              AS "variants.weight",
    ''                                                AS "variants.barcode",
    'true'                                            AS "variants.inventory_management",
    STUFF((
      SELECT
        ',' + CONVERT(VARCHAR, LTRIM(RTRIM(inv3.StockCode))) + '|' + CONVERT(VARCHAR, (inv.StockUom))
      FROM InvMaster inv3 WITH (NOLOCK)
	  WHERE inv3.StockCode = inv.StockCode
      ORDER BY LTRIM(RTRIM(inv.StockCode))
    for xml path('')),1,1,'')                         AS "csv_uom",
    CASE
       WHEN LOWER(RTRIM(ISNULL(inv2.Stock2Shop, ''))) = 'yes' THEN 'true'
        ELSE 'false'
    END                                               AS "product_active",
    ''                                                AS "tags",

    -- Prices
    '01'                                               AS "default_price_tier",
    STUFF((
      SELECT
        ',' + CONVERT(VARCHAR, LTRIM(RTRIM(price_tiers.PriceCode))) + '|' +
              CONVERT(VARCHAR, CAST(ISNULL(price_tiers.SellingPrice, 0) AS FLOAT))
      FROM InvPrice price_tiers WITH (NOLOCK)
      WHERE price_tiers.StockCode = inv.StockCode
      ORDER BY LTRIM(RTRIM(price_tiers.PriceCode))
    for xml path('')),1,1,'')
                                                      AS "csv_price_tiers",
    (SELECT
      'R' + CONVERT(VARCHAR, CAST(ISNULL(price_tiers.SellingPrice, 0) AS FLOAT))
      FROM InvPrice price_tiers WITH (NOLOCK)
      WHERE price_tiers.StockCode = inv.StockCode
      AND CONVERT(VARCHAR, LTRIM(RTRIM(price_tiers.PriceCode))) = '03')
                                                      AS "meta_list_price",

    '03'                                              AS "default_qty_availability",
    STUFF((
      SELECT
        ',' + CONVERT(VARCHAR, LTRIM(RTRIM(whse.Warehouse))) + '|' +
              CONVERT(VARCHAR, CONVERT(INTEGER, COALESCE(whse.QtyOnHand, 0) - COALESCE(whse.QtyAllocated, 0)))
      FROM InvWarehouse whse WITH (NOLOCK)
      WHERE whse.StockCode = inv.StockCode
      ORDER BY LTRIM(RTRIM(whse.Warehouse))
    for xml path('')),1,1,'')
                                                      AS "csv_qty_availability",
    -- Contract: Customer discount by product class group (percentage)
    -- Requirements:
    --   customer.meta_customer_code
    stuff((
        SELECT
            ',order_0|entity_customer|key_customer_code|value_' + CONVERT(VARCHAR, LTRIM(RTRIM(sor.Customer))) + '|type_discount_channel_user~' +
            CONVERT(VARCHAR, sor.Discount11)
        FROM SorDiscCusQty sor WITH (NOLOCK)
        INNER JOIN ArCustomer ar WITH (nolock)
                   ON ar.Customer = sor.Customer
        LEFT JOIN [ArCustomer+] ar2
                   ON ar2.Customer = ar.Customer
        WHERE sor.ProductClass = inv.ProductClass
              AND sor.DiscountType = 'V' -- Discount percent
              AND ar.Branch = '03' -- Limit by active customers
              and ar2.BuyerEMail is not null
          ORDER BY LTRIM(RTRIM(sor.Customer))
        FOR XML PATH ('')), 1, 1, '')
                                                        AS "contract_product_customer_group",
    inv.ProductClass                                    AS "meta_product_class"


FROM InvMaster inv WITH (NOLOCK)

    -- Base Data
    LEFT JOIN [InvMaster+] inv2 WITH (nolock)
    ON inv.StockCode = inv2.StockCode

    -- Product class
    LEFT JOIN SalProductClassDes spcd WITH (nolock)
    ON inv.ProductClass = spcd.ProductClass

    -- Product Colour Desc
    LEFT JOIN CustomFormColourDesc cfcd WITH (nolock)
    ON inv2.Colour = cfcd.Item

    -- Warehouse join for filter
    LEFT JOIN InvWarehouse whse WITH (NOLOCK)
    ON whse.StockCode = inv.StockCode

Where LOWER(RTRIM(inv2.Stock2Shop)) in ('yes', 'no')
and CONVERT(VARCHAR, LTRIM(RTRIM(whse.Warehouse))) = '03' -- Limit to CPT branch/whse
and inv.StockCode = '%(source_variant_code)s';
