SELECT
  st.Code                                                              AS "source_product_code",
  st.Code                                                              AS "source_variant_code",
  st.Description_1                                                     AS "title",
  (
    SELECT sc.cCategoryName
    FROM _etblStockCategories sc WITH (NOLOCK)
    INNER JOIN _etblStockDetails sd WITH (NOLOCK)
        ON sc.idStockCategories = sd.ItemCategoryID
    WHERE sd.StockID = st.StockLink AND sd.WhseID = st.WhseItem
  )                                                                    AS "collection",
  ''                                                                   AS "product_type",
  ''                                                                   AS "vendor",
  st.Code                                                              AS "variants.sku",
  ''                                                                   AS "variants.option1",
  ''                                                                   AS "variants.option2",
  ''                                                                   AS "variants.option3",
  ''                                                                   AS "body_html",
  0                                                                    AS "variants.weight",
  (
    SELECT TOP 1 bc.Barcode
    FROM _etblBarcodes bc WITH (NOLOCK)
    WHERE bc.StockID = st.StockLink AND bc.WhseID = 0
  )                                                                    AS "variants.barcode",
    CASE RTRIM(LTRIM(LOWER(st.ulIIOnlineICTrack)))
    WHEN 'yes' THEN 'false'
    ELSE 'true'
  END                                                                  AS "variants.inventory_management",
  CASE RTRIM(LTRIM(LOWER(st.UlIIOnlineActive)))
    WHEN 'active' THEN 'true'
    ELSE 'false'
  END                                                                  AS "product_active",
  ''                                                                   AS "tags",

  'Std_4'                                                              AS "default_price_tier",
  STUFF(
    (SELECT
      ',' + CONVERT(VARCHAR, price_names.cName) + '_' + CONVERT(VARCHAR, price.iWarehouseID) + '|' +
            CONVERT(VARCHAR, price.fExclPrice) + 
      -- Inclusive Price (_incl)
      ',' + CONVERT(VARCHAR, price_names.cName) + '_' + CONVERT(VARCHAR, price.iWarehouseID) + '_incl' + '|' +
            CONVERT(VARCHAR, price.fInclPrice)
    FROM dbo._etblPriceListPrices price WITH (NOLOCK)

      INNER JOIN _etblPriceListName price_names WITH (NOLOCK)
        ON price.iPriceListNameID = price_names.IDPriceListName AND price.iStockID = st.StockLink --and price.iWarehouseID in (4, 10)
        --4 = AbsoluteOrganix
        --10 NowB2B
    ORDER BY price_names.cName
    for xml path('')),1,1,'')
                                                                       AS "csv_price_tiers",

  '200-TH'                                                             AS "default_qty_availability",
  STUFF((
    SELECT
      CONVERT(VARCHAR, whse_mst.Code) + '|' +
      CONVERT(VARCHAR, ISNULL(sq.QtyOnHand - sq.QtyOnSO, 0)) + ','
    FROM _etblStockQtys sq WITH (NOLOCK)

      LEFT JOIN WhseMst whse_mst WITH (NOLOCK)
        ON whse_mst.WhseLink = sq.WhseID
    WHERE sq.StockID = st.StockLink
    ORDER BY whse_mst.Code
    for xml path('')),1,0,'')
                                                                       AS "csv_qty_availability",

  -- List of images for this product
  -- Enables automatic pulling of new images when image is added
  ISNULL(
  STUFF(
    (SELECT ',' + convert(varchar, i.idInvImage)
    FROM _etblInvImages i WITH (NOLOCK)
    WHERE st.StockLink = i.iStockLink
    ORDER BY i.idInvImage
    FOR XML PATH ('')), 1, 1, ''), '')
                                                                       AS "meta_image_id_csv",
    st.uiIIChannelSync                                                 AS "meta_ChannelSync"

FROM StkItem st WITH (NOLOCK)

WHERE st.Code = '%(sku)s'
;