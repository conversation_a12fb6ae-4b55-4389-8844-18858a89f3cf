SELECT

    st.Code                                                             AS 'source_product_code',
    st.StockLink                                                        AS 'source_variant_code',
    st.Description_1                                                    AS 'title',
    (
        SELECT sc.cCategoryName
        FROM _etblStockCategories sc WITH (NOLOCK)
        INNER JOIN _etblStockDetails sd WITH (NOLOCK)
        ON sc.idStockCategories = sd.ItemCategoryID
        WHERE sd.StockID = st.StockLink AND sd.WhseID = st.WhseItem
    )                                                                   AS 'collection',
    (
        SELECT grp.StGroup
        FROM GrpTbl grp WITH (NOLOCK)
        INNER JOIN _etblStockDetails sd WITH (NOLOCK)
        ON sd.StockID = st.StockLink
        AND grp.idGrpTbl = sd.GroupID
        AND sd.WhseID = st.WhseItem
    )                                                                   AS 'product_type',
    ''                                                                  AS 'vendor',
    st.Code                                                             AS 'variants.sku',
    ''                                                                  AS 'variants.option1',
    ''                                                                  AS 'variants.option2',
    ''                                                                  AS 'variants.option3',
    ''                                                                  AS 'body_html',
    0                                                                   AS 'variants.weight',
    (
        SELECT top 1 bc.Barcode
        FROM _etblBarcodes bc WITH (NOLOCK)
        WHERE bc.StockID = st.StockLink AND bc.WhseID = 0
    )                                                                   AS 'variants.barcode',
    'true'                                                              AS 'variants.inventory_management',
    CASE st.ItemActive
        WHEN 1 THEN 'true'
        ELSE 'false'
    END                                                                 AS 'product_active',
    ''                                                                  AS 'tags',

    '1'                                                                 AS 'default_price_tier',
    STUFF((
        SELECT
            ',' + CONVERT(VARCHAR, price_names.cName) + '|' +
            CONVERT(VARCHAR, price.fExclPrice)
        FROM dbo._etblPriceListPrices price WITH (NOLOCK)
        INNER JOIN _etblPriceListName price_names WITH (NOLOCK)
            ON price.iPriceListNameID = price_names.IDPriceListName
            AND price.iStockID = st.StockLink
            AND price.iWarehouseID = 0
        ORDER BY price_names.cName
        for xml path('')),1,1,'')
                                                                        AS 'csv_price_tiers',

    'Mstr'                                                              AS 'default_qty_availability',
  -- SELECT all warehouses AND SUM all bins
  STUFF((
    SELECT
      CONVERT(VARCHAR, whse_mst.Code) + '|' +
      CONVERT(VARCHAR, ISNULL(sq.QtyOnHand - sq.QtyOnSO - sq.QtyReserved, 0)) + ','
    FROM (
      SELECT ssq.StockID, ssq.WhseID,
        SUM(QtyOnHand) AS QtyOnHand,
        SUM(QtyOnSO) AS QtyOnSO,
        SUM(QtyReserved) AS QtyReserved
      FROM _etblStockQtys ssq
      GROUP BY ssq.StockID, ssq.WhseID
    ) sq

    LEFT JOIN WhseMst whse_mst WITH (NOLOCK)
      ON whse_mst.WhseLink = sq.WhseID
    WHERE sq.StockID = st.StockLink
    ORDER BY whse_mst.Code
    for xml path('')),1,0,'')
                                                                        AS 'csv_qty_availability',

    -- List of images for this product
    -- Enables automatic pulling of NEW images WHEN image IS added
    ISNULL(
        STUFF((
            SELECT ',' + CONVERT(VARCHAR, i.idInvImage)
            FROM _etblInvImages i WITH (NOLOCK)
            WHERE st.StockLink = i.iStockLink
            ORDER BY i.idInvImage
            FOR XML PATH ('')), 1, 1, ''),
        ''
    )
                                                                        AS 'meta_image_id_csv',
    grp.StGroup                                                         AS 'meta_item_group',
    grp.idGrpTbl                                                        AS 'meta_item_group_code',
    st.StockLink                                                        AS 'meta_item_code',

    -- CONTRACT PRICING:
    -- A contract consists of 6 parts, these ARE:-
    --    ORDER:    The hierarchy (ORDER) IN which to load the contracts. The FIRST successful contract will be used
    --    Entity:   The entity the contract will apply to. For example, a customer would have a contract WITH entity "product" to apply to the product.
    --    KEY:      The corresponding entities KEY to match on. IN the above example, this would be "category"
    --    TYPE:     Discount OR fixed. IS this contract a fixed price OR discount FROM some other price.
    --    VALUE:    The VALUE of the contract, IN the CASE of TYPE discount, this would be a percentage expressed AS an integer. IN the CASE of a fixed price, it would be the price,
    -- e.g. order_0|entity_customer|key_item_group_code|value_123|type_discount~10
    -- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    -- Hierarchy:
    -- 1. ALL Customers, Customer GROUP, OR specific Customer
    -- 2. ALL Inventory, Inventory GROUP, OR specific Inventory Item
    -- 3. Actual discount details (discount % OR unit price)
    -- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    -- Contract: Product discount applying to all customers (percent)
    -- Requirements:
    --   Customer.meta_global_discount
    STUFF((
        SELECT
            ',order_0|entity_customer|key_global_discount|value_true|type_discount_channel~' +
            CONVERT(VARCHAR, LTRIM(RTRIM(vdlvln.fPriceDisc)))
        FROM _etblVDAR vd WITH (NOLOCK)
            INNER JOIN _etblVDLnAR vdln WITH (NOLOCK)
                ON vd.IDVD = vdln.iVDID
            INNER JOIN _etblVDLnLvlAR vdlvln WITH (NOLOCK)
                ON vdln.IDVDLn = vdlvln.iVDLnID
        WHERE vd.bARAPAll = 1                   -- discount for all
            AND vdln.iStockID = st.StockLink    -- stock item (0 mean it applies to all stock)
            AND vdln.bUseStockPrc = 0           -- fixed OR percentage
            AND getdate() >= vdln.dEffDate
            AND getdate() <= vdln.dExpDate
        ORDER BY vdln.dExpDate DESC
        FOR XML PATH ('')), 1, 1, '')
                                                                       AS 'csv_contract_customer_product_global',

    -- Contract: Product discount applying to all customers (fixed)
    -- Requirements:
    --   Customer.meta_global_discount
    STUFF((
        SELECT
            ',order_0|entity_customer|key_global_discount_fixed|value_true|type_fixed~' +
            CONVERT(VARCHAR, LTRIM(RTRIM(vdlvln.fPriceDisc)))
        FROM _etblVDAR vd WITH (NOLOCK)
            INNER JOIN _etblVDLnAR vdln WITH (NOLOCK)
                ON vd.IDVD = vdln.iVDID
            INNER JOIN _etblVDLnLvlAR vdlvln WITH (NOLOCK)
                ON vdln.IDVDLn = vdlvln.iVDLnID
        WHERE vd.bARAPAll = 1                   -- discount for all
            AND vdln.iStockID = st.StockLink    -- stock item (0 mean it applies to all stock)
            AND vdln.bUseStockPrc = 1           -- fixed OR percentage
            AND getdate() >= vdln.dEffDate
            AND getdate() <= vdln.dExpDate
        ORDER BY vdln.dExpDate DESC
        FOR XML PATH ('')), 1, 1, '')
                                                                       AS 'csv_contract_customer_product_global_fixed',

    -- Contract: Discount BY customer GROUP linked to product GROUP (percent)
    -- Requirements:
    --   Customer.meta_customer_group_id
    STUFF((
        SELECT
            ',order_1|entity_customer|key_customer_group_id|value_' + CONVERT(VARCHAR, LTRIM(RTRIM(vd.iGroupID))) + '|type_discount_channel~' +
            CONVERT(VARCHAR, LTRIM(RTRIM(vdlvln.fPriceDisc)))
        FROM _etblVDAR vd WITH (NOLOCK)
            INNER JOIN _etblVDLnAR vdln WITH (NOLOCK)
                ON vd.IDVD = vdln.iVDID
            INNER JOIN _etblVDLnLvlAR vdlvln WITH (NOLOCK)
                ON vdln.IDVDLn = vdlvln.iVDLnID
        WHERE vd.bARAPAll = 0                   -- discount for all
            AND vd.iARAPID = 0                  -- per accounts receivable (Client.DCLink)
            AND vdlvln.fQuantity = 1            -- volume discount
            AND vdln.iStockID = 0               -- stock item (0 mean it applies to all stock)
            AND vd.iGroupID > 0                 -- customer GROUP (0 means it applies to all groups)
            AND vdln.iStGroupID = grp.idGrpTbl  -- stock GROUP
            AND vdln.bUseStockPrc = 0           -- fixed OR percentage
            AND getdate() >= vdln.dEffDate
            AND getdate() <= vdln.dExpDate
        ORDER BY vd.iGroupID
        FOR XML PATH ('')), 1, 1, '')
                                                                       AS 'csv_contract_customer_group_product_group',

    -- Contract: Discount BY customer GROUP linked to product GROUP (fixed)
    -- Requirements:
    --   Customer.meta_customer_group_id
    STUFF((
        SELECT
            ',order_1|entity_customer|key_customer_group_id|value_' + CONVERT(VARCHAR, LTRIM(RTRIM(vd.iGroupID))) + '|type_fixed~' +
            CONVERT(VARCHAR, LTRIM(RTRIM(vdlvln.fPriceDisc)))
        FROM _etblVDAR vd WITH (NOLOCK)
            INNER JOIN _etblVDLnAR vdln WITH (NOLOCK)
                ON vd.IDVD = vdln.iVDID
            INNER JOIN _etblVDLnLvlAR vdlvln WITH (NOLOCK)
                ON vdln.IDVDLn = vdlvln.iVDLnID
        WHERE vd.bARAPAll = 0                   -- discount for all
            AND vd.iARAPID = 0                  -- per accounts receivable (Client.DCLink)
            AND vdlvln.fQuantity = 1            -- volume discount
            AND vdln.iStockID = 0               -- stock item (0 mean it applies to all stock)
            AND vd.iGroupID > 0                 -- customer GROUP (0 means it applies to all groups)
            AND vdln.iStGroupID = grp.idGrpTbl  -- stock GROUP
            AND vdln.bUseStockPrc = 1           -- fixed OR percentage
            AND getdate() >= vdln.dEffDate
            AND getdate() <= vdln.dExpDate
        ORDER BY vd.iGroupID
        FOR XML PATH ('')), 1, 1, '')
                                                                       AS 'csv_contract_customer_group_product_group_fixed',

    -- Contract: Product discount BY customer (percent)
    -- Requirements:
    --   Customer.price_tier [discount IS applied to the assigned price tier]
    --   Customer.meta_customer_id
    STUFF((
        SELECT
            ',order_2|entity_customer|key_customer_id|value_' + CONVERT(VARCHAR, LTRIM(RTRIM(vd.iARAPID))) + '|type_discount_channel_user~' +
            CONVERT(VARCHAR, LTRIM(RTRIM(vdlvln.fPriceDisc)))
        FROM _etblVDAR vd WITH (NOLOCK)
            INNER JOIN _etblVDLnAR vdln WITH (NOLOCK)
                ON vd.IDVD = vdln.iVDID
            INNER JOIN _etblVDLnLvlAR vdlvln WITH (NOLOCK)
                ON vdln.IDVDLn = vdlvln.iVDLnID
            INNER JOIN Client cl WITH (NOLOCK)
                ON cl.DCLink = vd.iARAPID
        WHERE vd.bARAPAll = 0                   -- discount for all
            AND vd.iARAPID > 0                  -- per accounts receivable (Client.DCLink)
            AND vdlvln.fQuantity = 1            -- volume discount
            AND vdln.iStockID = st.StockLink    -- stock item (0 mean it applies to all stock)
            AND vd.iGroupID = 0                 -- customer GROUP (0 means it applies to all groups)
            AND vdln.iSTGroupID = 0             -- stock GROUP
            AND vdln.bUseStockPrc = 0           -- fixed OR percentage
            AND getdate() >= vdln.dEffDate
            AND getdate() <= vdln.dExpDate
            -- FILTER only active customers
--            AND LOWER(RTRIM(cl.ulARWebActive)) IN ('active')
        ORDER BY vd.iARAPID
        FOR XML PATH ('')), 1, 1, '')
                                                                       AS 'csv_contract_customer',

    -- Contract: Product discount BY customer (fixed)
    -- Requirements:
    --   Customer.meta_customer_id
    STUFF((
        SELECT
            ',order_2|entity_customer|key_customer_id|value_' + CONVERT(VARCHAR, LTRIM(RTRIM(vd.iARAPID))) + '|type_fixed~' +
            CONVERT(VARCHAR, LTRIM(RTRIM(vdlvln.fPriceDisc)))
        FROM _etblVDAR vd WITH (NOLOCK)
            INNER JOIN _etblVDLnAR vdln WITH (NOLOCK)
                ON vd.IDVD = vdln.iVDID
            INNER JOIN _etblVDLnLvlAR vdlvln WITH (NOLOCK)
                ON vdln.IDVDLn = vdlvln.iVDLnID
            INNER JOIN Client cl WITH (NOLOCK)
                ON cl.DCLink = vd.iARAPID
        WHERE vd.bARAPAll = 0                   -- discount for all
            AND vd.iARAPID > 0                  -- per accounts receivable (Client.DCLink)
            AND vdlvln.fQuantity = 1            -- volume discount
            AND vdln.iStockID = st.StockLink    -- stock item (0 mean it applies to all stock)
            AND vd.iGroupID = 0                 -- customer GROUP (0 means it applies to all groups)
            AND vdln.iSTGroupID = 0             -- stock GROUP
            AND vdln.bUseStockPrc = 1           -- fixed OR percentage
            AND getdate() >= vdln.dEffDate
            AND getdate() <= vdln.dExpDate
            -- FILTER only active customers
--            AND LOWER(RTRIM(cl.ulARWebActive)) IN ('active')
        ORDER BY vd.iARAPID
        FOR XML PATH ('')), 1, 1, '')
                                                                       AS 'csv_contract_customer_fixed',

    -- Contract: Discount BY customer linked to product GROUP (percent)
    -- Requirements:
    --   Customer.price_tier [discount IS applied to the assigned price tier]
    --   Customer.meta_customer_id
    STUFF((
        SELECT
            ',order_3|entity_customer|key_customer_id|value_' + CONVERT(VARCHAR, LTRIM(RTRIM(vd.iARAPID))) + '|type_discount_channel_user~' +
            CONVERT(VARCHAR, LTRIM(RTRIM(vdlvln.fPriceDisc)))
        FROM _etblVDAR vd WITH (NOLOCK)
            INNER JOIN _etblVDLnAR vdln WITH (NOLOCK)
                ON vd.IDVD = vdln.iVDID
            INNER JOIN _etblVDLnLvlAR vdlvln WITH (NOLOCK)
                ON vdln.IDVDLn = vdlvln.iVDLnID
            INNER JOIN Client cl WITH (NOLOCK)
                ON cl.DCLink = vd.iARAPID
        WHERE vd.bARAPAll = 0                   -- discount for all
            AND vd.iARAPID > 0                  -- per accounts receivable (Client.DCLink)
            AND vdlvln.fQuantity = 1            -- volume discount
            AND vdln.iStockID = 0               -- stock item (0 mean it applies to all stock)
            AND vd.iGroupID = 0                 -- customer GROUP (0 means it applies to all groups)
            AND vdln.iStGroupID = grp.idGrpTbl  -- stock GROUP
            AND vdln.bUseStockPrc = 0           -- fixed OR percentage
            AND getdate() >= vdln.dEffDate
            AND getdate() <= vdln.dExpDate
            -- FILTER only active customers
--            AND LOWER(RTRIM(cl.ulARWebActive)) IN ('active')
        ORDER BY vd.iARAPID
        FOR XML PATH ('')), 1, 1, '')
                                                                       AS 'csv_contract_customer_product_group',

    -- Contract: Discount BY customer linked to product GROUP (fixed)
    -- Requirements:
    --   Customer.meta_customer_id
    STUFF((
        SELECT
            ',order_3|entity_customer|key_customer_id|value_' + CONVERT(VARCHAR, LTRIM(RTRIM(vd.iARAPID))) + '|type_fixed~' +
            CONVERT(VARCHAR, LTRIM(RTRIM(vdlvln.fPriceDisc)))
        FROM _etblVDAR vd WITH (NOLOCK)
            INNER JOIN _etblVDLnAR vdln WITH (NOLOCK)
                ON vd.IDVD = vdln.iVDID
            INNER JOIN _etblVDLnLvlAR vdlvln WITH (NOLOCK)
                ON vdln.IDVDLn = vdlvln.iVDLnID
            INNER JOIN Client cl WITH (NOLOCK)
                ON cl.DCLink = vd.iARAPID
        WHERE vd.bARAPAll = 0                   -- discount for all
            AND vd.iARAPID > 0                  -- per accounts receivable (Client.DCLink)
            AND vdlvln.fQuantity = 1            -- volume discount
            AND vdln.iStockID = 0               -- stock item (0 mean it applies to all stock)
            AND vd.iGroupID = 0                 -- customer GROUP (0 means it applies to all groups)
            AND vdln.iStGroupID = grp.idGrpTbl  -- stock GROUP
            AND vdln.bUseStockPrc = 1           -- fixed OR percentage
            AND getdate() >= vdln.dEffDate
            AND getdate() <= vdln.dExpDate
            -- FILTER only active customers
--            AND LOWER(RTRIM(cl.ulARWebActive)) IN ('active')
        ORDER BY vd.iARAPID
        FOR XML PATH ('')), 1, 1, '')
                                                                       AS 'csv_contract_customer_product_group_fixed'


    -- LEGACY CONTRACT PRICING (kept here for referencing purposes only)
--    -- Discount BY customer per product
--    STUFF(
--        (SELECT CONVERT(VARCHAR, st.StockLink) + '-' + CONVERT(VARCHAR, vd.iARAPID) + '|' +
--                CONVERT(VARCHAR,
--                CASE WHEN getdate() >= vdln.dEffDate AND getdate() <= vdln.dExpDate
--                  THEN vdlvln.fPriceDisc
--                  ELSE 0
--                END
--                ) + ','
--         FROM _etblVDAR vd WITH (NOLOCK)
--             INNER JOIN _etblVDLnAR vdln WITH (NOLOCK)
--                 ON vd.IDVD = vdln.iVDID
--             INNER JOIN _etblVDLnLvlAR vdlvln WITH (NOLOCK)
--                 ON vdln.IDVDLn = vdlvln.iVDLnID
--         WHERE vd.bARAPAll = 0
--           AND vd.iARAPID > 0
--           AND vdlvln.fQuantity = 1
--           AND vdln.iStockID = st.StockLink
--           AND vd.iGroupID = 0
--           AND vdln.iSTGroupID = 0
--           AND vdln.bUseStockPrc = 0
--         ORDER BY vd.iARAPID
--         FOR XML PATH ('')), 1, 0, '')
--                                                                       AS 'csv_customer',
--    -- Discount BY customer per product fixed
--    STUFF(
--        (SELECT CONVERT(VARCHAR, st.StockLink) + '-' + CONVERT(VARCHAR, vd.iARAPID) + '|' +
--                CONVERT(VARCHAR,
--                CASE WHEN getdate() >= vdln.dEffDate AND getdate() <= vdln.dExpDate
--                  THEN vdlvln.fPriceDisc
--                  ELSE 0
--                END
--                ) + ','
--         FROM _etblVDAR vd WITH (NOLOCK)
--             INNER JOIN _etblVDLnAR vdln WITH (NOLOCK)
--                 ON vd.IDVD = vdln.iVDID
--             INNER JOIN _etblVDLnLvlAR vdlvln WITH (NOLOCK)
--                 ON vdln.IDVDLn = vdlvln.iVDLnID
--         WHERE vd.bARAPAll = 0
--           AND vd.iARAPID > 0
--           AND vdlvln.fQuantity = 1
--           AND vdln.iStockID = st.StockLink
--           AND vd.iGroupID = 0
--           AND vdln.iSTGroupID = 0
--           AND vdln.bUseStockPrc = 1
--         ORDER BY vd.iARAPID
--         FOR XML PATH ('')), 1, 0, '')
--                                                                       AS 'csv_customer_fixed',
--
--    -- Discount ON product that applies to all customers (e.g StockLink-0)
--    STUFF(
--        (SELECT TOP 1 CONVERT(VARCHAR, st.StockLink) + '-' + CONVERT(VARCHAR, vd.iARAPID) + '|' +
--                CONVERT(VARCHAR,
--                CASE WHEN getdate() >= vdln.dEffDate AND getdate() <= vdln.dExpDate
--                  THEN vdlvln.fPriceDisc
--                  ELSE 0
--                END
--                ) + ','
--         FROM _etblVDAR vd WITH (NOLOCK)
--             INNER JOIN _etblVDLnAR vdln WITH (NOLOCK)
--                 ON vd.IDVD = vdln.iVDID
--             INNER JOIN _etblVDLnLvlAR vdlvln WITH (NOLOCK)
--                 ON vdln.IDVDLn = vdlvln.iVDLnID
--          WHERE vd.bARAPAll = 1
--          AND vdln.iStGroupID = grp.idGrpTbl
--         ORDER BY vdln.dExpDate DESC
--         FOR XML PATH ('')), 1, 0, '')
--                                                                       AS 'csv_customer_product_global',
--
--    -- Discount BY customer linked to product GROUP
--    STUFF(
--        (SELECT CONVERT(VARCHAR, st.StockLink) + '-' + CONVERT(VARCHAR, vd.iARAPID) + '|' +
--                CONVERT(VARCHAR,
--                CASE WHEN getdate() >= vdln.dEffDate AND getdate() <= vdln.dExpDate
--                  THEN vdlvln.fPriceDisc
--                  ELSE 0
--                END
--                ) + ','
--         FROM _etblVDAR vd WITH (NOLOCK)
--             INNER JOIN _etblVDLnAR vdln WITH (NOLOCK)
--                 ON vd.IDVD = vdln.iVDID
--             INNER JOIN _etblVDLnLvlAR vdlvln WITH (NOLOCK)
--                 ON vdln.IDVDLn = vdlvln.iVDLnID
--          WHERE vd.bARAPAll = 0
--          AND vd.iARAPID > 0
--          AND vdlvln.fQuantity = 1
--          AND vdln.iStockID = 0
--          AND vd.iGroupID = 0
--          AND vdln.iStGroupID = grp.idGrpTbl
--          AND vdln.bUseStockPrc = 0
--         ORDER BY vd.iARAPID
--         FOR XML PATH ('')), 1, 0, '')
--                                                                       AS 'csv_customer_product_group',
--
--    -- Discount BY customer linked to product GROUP fixed
--    STUFF(
--        (SELECT CONVERT(VARCHAR, st.StockLink) + '-' + CONVERT(VARCHAR, vd.iARAPID) + '|' +
--                CONVERT(VARCHAR,
--                CASE WHEN getdate() >= vdln.dEffDate AND getdate() <= vdln.dExpDate
--                  THEN vdlvln.fPriceDisc
--                  ELSE 0
--                END
--                ) + ','
--         FROM _etblVDAR vd WITH (NOLOCK)
--             INNER JOIN _etblVDLnAR vdln WITH (NOLOCK)
--                 ON vd.IDVD = vdln.iVDID
--             INNER JOIN _etblVDLnLvlAR vdlvln WITH (NOLOCK)
--                 ON vdln.IDVDLn = vdlvln.iVDLnID
--         WHERE vd.bARAPAll = 0
--          AND vd.iARAPID > 0
--          AND vdlvln.fQuantity = 1
--          AND vdln.iStockID = 0
--          AND vd.iGroupID = 0
--          AND vdln.iStGroupID = grp.idGrpTbl
--          AND vdln.bUseStockPrc = 1
--         ORDER BY vd.iARAPID
--         FOR XML PATH ('')), 1, 0, '')
--                                                                       AS 'csv_customer_product_group_fixed',
--
--    -- Discount BY customer GROUP linked to product GROUP
--    STUFF(
--        (SELECT CONVERT(VARCHAR, st.StockLink) + '-' + CONVERT(VARCHAR, vd.iGroupID) + '|' +
--                CONVERT(VARCHAR,
--                CASE WHEN getdate() >= vdln.dEffDate AND getdate() <= vdln.dExpDate
--                  THEN vdlvln.fPriceDisc
--                  ELSE 0
--                END
--                ) + ','
--         FROM _etblVDAR vd WITH (NOLOCK)
--             INNER JOIN _etblVDLnAR vdln WITH (NOLOCK)
--                 ON vd.IDVD = vdln.iVDID
--             INNER JOIN _etblVDLnLvlAR vdlvln WITH (NOLOCK)
--                 ON vdln.IDVDLn = vdlvln.iVDLnID
--         WHERE vd.bARAPAll = 0                -- discount for all
--          AND vd.iARAPID = 0                  -- per accounts receivable
--          AND vdlvln.fQuantity = 1            -- volume discount
--          AND vdln.iStockID = 0               -- stock item
--          AND vd.iGroupID > 0                 -- customer GROUP
--          AND vdln.iStGroupID = grp.idGrpTbl  -- stock GROUP
--          AND vdln.bUseStockPrc = 0           -- fixed OR percentage
--         ORDER BY vd.iGroupID
--         FOR XML PATH ('')), 1, 0, '')
--                                                                       AS 'csv_customer_group_product_group',
--
--    -- Discount BY customer GROUP linked to product GROUP fixed
--    STUFF(
--        (SELECT CONVERT(VARCHAR, st.StockLink) + '-' + CONVERT(VARCHAR, vd.iGroupID) + '|' +
--                CONVERT(VARCHAR,
--                CASE WHEN getdate() >= vdln.dEffDate AND getdate() <= vdln.dExpDate
--                  THEN vdlvln.fPriceDisc
--                  ELSE 0
--                END
--                ) + ','
--         FROM _etblVDAR vd WITH (NOLOCK)
--             INNER JOIN _etblVDLnAR vdln WITH (NOLOCK)
--                 ON vd.IDVD = vdln.iVDID
--             INNER JOIN _etblVDLnLvlAR vdlvln WITH (NOLOCK)
--                 ON vdln.IDVDLn = vdlvln.iVDLnID
--         WHERE vd.bARAPAll = 0                -- discount for all
--          AND vd.iARAPID = 0                  -- per accounts receivable
--          AND vdlvln.fQuantity = 1            -- volume discount
--          AND vdln.iStockID = 0               -- stock item
--          AND vd.iGroupID > 0                 -- customer GROUP
--          AND vdln.iStGroupID = grp.idGrpTbl  -- stock GROUP
--          AND vdln.bUseStockPrc = 1           -- fixed OR percentage
--         ORDER BY vd.iGroupID
--         FOR XML PATH ('')), 1, 0, '')
--                                                                       AS 'csv_customer_group_product_group_fixed'

FROM StkItem st WITH (NOLOCK)

INNER JOIN _etblStockDetails sd WITH (NOLOCK)
	ON sd.StockID = st.StockLink
	AND sd.WhseID = st.WhseItem

LEFT JOIN GrpTbl grp WITH (NOLOCK) ON grp.idGrpTbl = sd.GroupID

WHERE 1=1
;