select *
from (
select
    row_number() over (order by ITM.ITMREF_0) as n,

    ITM.ITMREF_0                               as "source_product_code",
    ITM.ITMREF_0                               as "source_variant_code",
    ISNULL(IDS.TEXTE_0,ITM.ITMDES1_0)          as "title",
    ISNULL(IDV.TEXTE_0,ITM.TCLCOD_0)           as "collection",
    ISNULL(CAT.TEXTE_0,ITM.TSICOD_0)           as "product_type",
    ISNULL(BRD.TEXTE_0,ITM.TSICOD_2)           as "vendor",
    ITM.ITMREF_0                               as "variants.sku",
    ''                                         as "body_html",
    0                                          as "variants.weight",
    ''                                         as "variants.barcode",
    'AE011'                                    as "default_qty_availability",
    STUFF((
              SELECT
                      ',' + CONVERT(VARCHAR, sq.STOFCY_0) + '|' +
                      CONVERT(VARCHAR, ISNULL(sq.PHYSTO_0 - sq.PHYALL_0, 0)) + ','
              from SEED.ITMMVT sq
              where sq.ITMREF_0 = ITM.ITMREF_0
              order by sq.STOFCY_0
              for xml path('')),1,0,'')
                                               as "csv_qty_availability",
    'SPL12-0003'                               as "default_price_tier",
    STUFF((
              SELECT
                      ',' + CONVERT(VARCHAR, price.PLICRD_0) + '|' +
                      CONVERT(VARCHAR, price.PRI_0) + ','
              from SEED.SPRICLIST price
              where price.PLICRI1_0 = ITM.ITMREF_0
              order by price.PLICRD_0
              for xml path('')),1,1,'')
                                               as "csv_price_tiers",
    'true'                                     as "variants.inventory_management",
    CASE WHEN ITM.ITMSTA_0 = 1
        THEN 'true'
        ELSE 'false'
    END                                        as "product_active",
    ''                                         as "tags",
    ISNULL(IDV.TEXTE_0,ITM.TCLCOD_0)           as "meta_division",
    ISNULL(CAT.TEXTE_0,ITM.TSICOD_0)           as "meta_category",
    ISNULL(SCT.TEXTE_0,ITM.TSICOD_1)           as "meta_sub_category",
    ISNULL(BRD.TEXTE_0,ITM.TSICOD_2)           as "meta_brand"

from SEED.ITMMASTER ITM

LEFT JOIN   SEED.ATEXTRA    IDS ON  ITM.ITMREF_0 = IDS.IDENT1_0
                                    AND IDS.CODFIC_0 = 'ITMMASTER'
                                    AND IDS.ZONE_0   = 'DES1AXX'
                                    AND IDS.LANGUE_0 = 'ENG'
LEFT JOIN   SEED.ITMCATEG   ITG ON  ITM.TCLCOD_0 = ITG.TCLCOD_0
                                    AND ITG.STOFCY_0 = ''
LEFT JOIN   SEED.ATEXTRA    IDV ON  ITM.TCLCOD_0 = IDV.IDENT1_0
                                    AND IDV.CODFIC_0 = 'ITMCATEG'
                                    AND IDV.ZONE_0   = 'TCLAXX'
                                    AND IDV.LANGUE_0 = 'ENG'
LEFT JOIN   SEED.ATEXTRA    CAT ON  ITM.TSICOD_0 = CAT.IDENT2_0
                                    AND CAT.CODFIC_0 = 'ATABDIV'
                                    AND CAT.IDENT1_0 = '20'
                                    AND CAT.ZONE_0   = 'LNGDES'
                                    AND CAT.LANGUE_0 = 'ENG'
LEFT JOIN   SEED.ATEXTRA    SCT ON  ITM.TSICOD_1 = SCT.IDENT2_0
                                    AND SCT.CODFIC_0 = 'ATABDIV'
                                    AND SCT.IDENT1_0 = '21'
                                    AND SCT.ZONE_0   = 'LNGDES'
                                    AND SCT.LANGUE_0 = 'ENG'
LEFT JOIN   SEED.ATEXTRA    BRD ON  ITM.TSICOD_2 = BRD.IDENT2_0
                                    AND BRD.CODFIC_0 = 'ATABDIV'
                                    AND BRD.IDENT1_0 = '22'
                                    AND BRD.ZONE_0   = 'LNGDES'
                                    AND BRD.LANGUE_0 = 'ENG'
LEFT JOIN   SEED.ITMMVT     STK on ITM.ITMREF_0 = STK.ITMREF_0
                                    AND STK.STOFCY_0 = 'ROODE'
) as rows
where
n > '%(audit_lower_limit)n'
and n <= '%(audit_upper_limit)n'