{"dbName": "local.db", "dsn": "Driver={SQL Server};Server=AON-ERP;Database=SysproCompanyA;Uid=Stock2Shop;Pwd=********;", "audit_limit": 1000, "push": {"source_id": 557, "limit": 500, "token": "0fae50b92f533b8761eb91692d6ddd369586cc55", "writeLog": "http://localhost:1337/writeLog", "getProducts": "http://localhost:1337/odbc/getProductsBatch", "getCustomers": "http://localhost:1337/odbc/getCustomersBatch", "countProducts": "http://localhost:1337/odbc/countProducts", "countCustomers": "http://localhost:1337/odbc/countCustomers", "getMeta": "http://localhost:1337/odbc/getMeta", "setMeta": "http://localhost:1337/odbc/setMeta", "productsQueue": "https://app.stock2shop.com/v1/products/queue", "customersQueue": "https://app.stock2shop.com/v1/customers/queue"}}