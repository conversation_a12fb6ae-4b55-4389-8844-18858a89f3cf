select *
from (
SELECT
    row_number() OVER (ORDER BY inv.StockCode)        AS n,

    RTRIM(LTRIM(inv.StockCode))                       AS "source_product_code",
    RTRIM(LTRIM(inv.StockCode))                       AS "source_variant_code",
    RTRIM(LTRIM(inv.Description))                     AS "title",
    RTRIM(LTRIM(inv.ProductClass))                    AS "collection",
    ''                                                AS "product_type",
    ''                                                AS "vendor",
   -- 'Size'                                            AS "variants.option1_name",
   -- RTRIM(LTRIM(inv2.Size1))                          AS "variants.option1_value",
    RTRIM(LTRIM(inv.StockCode))                       AS "variants.sku",
    RTRIM(LTRIM(inv.LongDesc))                        AS "body_html",
    CAST(ISNULL(inv.Mass, 0) AS INTEGER)              AS "variants.weight",
    inv.AlternateKey1                                 AS "variants.barcode",
    'true'                                            AS "variants.inventory_management",
    CASE
        WHEN LOWER(RTRIM(ISNULL(inv2.WebActive, ''))) = 'active' THEN 'true'
        ELSE 'false'
    END                                               AS "product_active",
    ''                                                AS "tags",

    -- Prices
    'CTN'                                             AS "default_price_tier",
    STUFF((
      SELECT
        ',' + CONVERT(VARCHAR, LTRIM(RTRIM(whse.Warehouse))) + '|' +
              CONVERT(VARCHAR, CAST(ISNULL(whse.UnitCost, 0) AS FLOAT))
      FROM InvWarehouse whse WITH (NOLOCK)
      WHERE whse.StockCode = inv.StockCode
      ORDER BY LTRIM(RTRIM(whse.Warehouse))
    for xml path('')),1,1,'')
                                                      AS "csv_price_tiers",

    'CTN'                                             AS "default_qty_availability",
    STUFF((
      SELECT
        ',' + CONVERT(VARCHAR, LTRIM(RTRIM(whse.Warehouse))) + '|' +
              CONVERT(VARCHAR, CONVERT(INTEGER, COALESCE(whse.QtyOnHand, 0) - COALESCE(whse.QtyAllocated, 0)))
      FROM InvWarehouse whse WITH (NOLOCK)
      WHERE whse.StockCode = inv.StockCode
      ORDER BY LTRIM(RTRIM(whse.Warehouse))
    for xml path('')),1,1,'')
                                                      AS "csv_qty_availability",

    STUFF((
      SELECT
        ',' + CONVERT(VARCHAR, LTRIM(RTRIM(sor.CustomerBuyGrp))) + '|' +
              CASE
                WHEN DATEADD(day, DATEDIFF(day,0,GETDATE()),0) <= ExpiryDate and DATEADD(day, DATEDIFF(day,0,GETDATE()),0) >= StartDate
                 THEN CONVERT(VARCHAR, COALESCE(sor.FixedPrice, 0))
                ELSE '0'
              END
      FROM SorContractPrice sor WITH (NOLOCK)
      WHERE sor.StockCode = inv.StockCode AND sor.ContractType='C'
      ORDER BY LTRIM(RTRIM(sor.CustomerBuyGrp))
    for xml path('')),1,1,'')
                                                      AS "csv_discount_groups"


FROM InvMaster inv WITH (NOLOCK)

    -- Base Data
    LEFT JOIN [InvMaster+] inv2 WITH (nolock)
    ON inv.StockCode = inv2.StockCode

Where not inv2.WebActive is null

) as rows
where
n > '%(audit_lower_limit)n'
and n <= '%(audit_upper_limit)n'