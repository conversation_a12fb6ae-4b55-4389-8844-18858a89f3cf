import json
import locale
import os
import time
import xml.etree.ElementTree as etree
from .exceptions import ConfigError
from ..shared import utils
from ..shared import odbc
from ..vo.OrderSource import OrderSource
from ..vo.SystemOrder import SystemOrder
from collections import OrderedDict
import collections
import subprocess
import xml.sax.saxutils as saxutils
import math


class Meta(object):
    def __init__(self, meta_lookup: dict):
        self.meta_lookup = meta_lookup

    def get_by_key(self, key: str) -> str:
        m = self.meta_lookup.get(key.lower().strip(), None)
        if not m or not m.value:
            return None
        return m.value

    def get_bool(self, key: str) -> bool:
        v = self.get_by_key(key)
        return (v or "").strip().lower() == "true"


class OrdersAdd(object):
    def __init__(self, system_order: SystemOrder):
        self.order = system_order

        meta_lookup = {}
        for m in system_order.meta:
            meta_lookup[m.key.lower().strip()] = m

        self.meta = Meta(meta_lookup)


class Api:
    _indent = False

    _packageDir = None
    _configDir = None
    _config = None

    def _setConfig(self):
        params = self._getParams()
        test = False
        if "test" in params:
            test = params["test"] == "true"
        if test:
            configFile = open(
                os.path.join(self._packageDir, self._configDir, "test", "config.json"), "r")
            self._config = json.loads(configFile.read())
        else:
            configFile = open(os.path.join(self._packageDir, self._configDir, "config.json"), "r")
            self._config = json.loads(configFile.read())
        configFile.close()

    # App will override this
    @staticmethod
    def _getParams():
        return {}

    # App will override this
    @staticmethod
    def _payload():
        return {}

    def _getDbPath(self):
        return os.path.join(self._packageDir, self._configDir, self._config["dbName"])

    def _checkInstall(self):
        dbPath = self._getDbPath()
        table_results = []
        index_results = []

        with utils.openSqliteConn(self._getDbPath()) as cursor:
            if os.path.isfile(dbPath):
                sql = "select * from sqlite_master where type='table';"
                cursor.execute(sql)
                table_results = cursor.fetchall()

                sql = "select * from sqlite_master where type='index';"
                cursor.execute(sql)
                index_results = cursor.fetchall()

        # Create tables
        if len(table_results) == 0:
            self.createTables()

        # Create indexes
        if len(index_results) == 0:
            self.createIndexes()

    def createTables(self):
        self._setConfig()
        with utils.openSqliteConn(self._getDbPath(), commit=True) as cursor:
            # If not installed then create a new sqlite database
            cursor.execute(
                "create table product (source_product_code, source_variant_code, hash, sync_token, modified)")
            cursor.execute(
                "create table customer (source_customer_code, hash, sync_token, modified)")
            cursor.execute(
                "create table image (source_variant_code, hash, modified)")
            cursor.execute("""create table meta (key, value)""")

        response = {
            "status": True,
            "data": {
                "Create Tables": True
            }
        }
        return json.dumps(response, indent=self._indent)

    def createIndexes(self):
        self._setConfig()
        with utils.openSqliteConn(self._getDbPath(), commit=True) as cursor:
            # product table indexes
            cursor.execute(
                "CREATE INDEX idx_product_source_product_code_source_variant_code ON product(`source_product_code`,`source_variant_code`)")
            cursor.execute(
                "CREATE INDEX idx_product_sync_token ON product(`sync_token`)")

            # customer table indexes
            cursor.execute(
                "CREATE INDEX idx_customer_source_customer_code ON customer(`source_customer_code`)")
            cursor.execute(
                "CREATE INDEX idx_customer_sync_token ON customer(`sync_token`)")

            # image table indexes
            cursor.execute(
                "CREATE INDEX idx_image_source_variant_code ON image(`source_variant_code`)")

        response = {
            "status": True,
            "data": {
                "Indexed": True
            }
        }
        return json.dumps(response, indent=self._indent)

    def reindex(self):
        self._setConfig()
        self._dropIndexes()
        self.createIndexes()
        response = {
            "status": True,
            "data": {
                "Reindex": True
            }
        }
        return json.dumps(response, indent=self._indent)

    def _dropIndexes(self):
        with utils.openSqliteConn(self._getDbPath(), commit=True) as cursor:
            # product table indexes
            cursor.execute(
                "DROP INDEX IF EXISTS idx_product_source_product_code_source_variant_code")
            cursor.execute(
                "DROP INDEX IF EXISTS idx_product_sync_token")

            # customer table indexes
            cursor.execute(
                "DROP INDEX IF EXISTS idx_customer_source_customer_code")
            cursor.execute(
                "DROP INDEX IF EXISTS idx_customer_sync_token")

            # image table indexes
            cursor.execute(
                "DROP INDEX IF EXISTS idx_image_source_variant_code")

    def _getProductsOdbc(self):
        path = os.path.join(self._packageDir, self._configDir, "getProducts.sql")
        f = open(path, "r")
        sql = f.read()
        f.close()
        return sql

    def _getCustomersOdbc(self):
        path = os.path.join(self._packageDir, self._configDir, "getCustomers.sql")
        f = open(path, "r")
        sql = f.read()
        f.close()
        return sql

    def _countProductsOdbc(self):
        path = os.path.join(self._packageDir, self._configDir, "countProducts.sql")
        f = open(path, "r")
        sql = f.read()
        f.close()
        return sql

    def _countCustomersOdbc(self):
        path = os.path.join(self._packageDir, self._configDir, "countCustomers.sql")
        f = open(path, "r")
        sql = f.read()
        f.close()
        return sql

    def _getProductOdbc(self):
        path = os.path.join(self._packageDir, self._configDir, "getProduct.sql")
        f = open(path, "r")
        sql = f.read()
        f.close()
        return sql

    def _getProductBySKUOdbc(self):
        path = os.path.join(self._packageDir, self._configDir, "getProductBySKU.sql")
        f = open(path, "r")
        sql = f.read()
        f.close()
        return sql

    def _getCustomerOdbc(self):
        path = os.path.join(self._packageDir, self._configDir, "getCustomer.sql")
        f = open(path, "r")
        sql = f.read()
        f.close()
        return sql

    def _customOdbc(self, fileName=None):
        if not fileName:
            fileName = "custom.sql"
        path = os.path.join(self._packageDir, self._configDir, fileName)
        f = open(path, "r")
        sql = f.read()
        f.close()
        return sql

    def _getOrderOdbc(self):
        path = os.path.join(self._packageDir, self._configDir, "getOrder.sql")
        f = open(path, "r")
        sql = f.read()
        f.close()
        return sql

    _productIndex = {
        "source_product_code": 0,
        "source_variant_code": 1,
        "hash": 2,
        "sync_token": 3,
        "modified": 4
    }

    @staticmethod
    def _getProductsLocal():
        return '''
          select * from product where sync_token > ?
          order by sync_token asc limit ?
        '''

    @staticmethod
    def _getProductLocal():
        return '''
          select * from product
          where sync_token > ?
          and source_product_code = ?
          order by sync_token asc
        '''

    _customerIndex = {
        "source_customer_code": 0,
        "hash": 1,
        "sync_token": 2,
        "modified": 3
    }

    @staticmethod
    def _getCustomersLocal():
        return '''
          select * from customer where sync_token > ?
          order by sync_token asc limit ?
        '''

    @staticmethod
    def _getCustomerLocal():
        return '''
          select * from customer
          where source_customer_code = ?
        '''

    metaIndex = {
        "key": 0,
        "value": 1,
    }

    @staticmethod
    def _getMetaLocal():
        return '''
        select `key`, value from meta where `key` = ?
        '''

    @staticmethod
    def _getLastModifiedProduct(sqliteCursor):
        sql = '''
          select * from product
          order by sync_token desc limit 1
        '''
        sqliteCursor.execute(sql)
        lastModified = sqliteCursor.fetchone()
        return lastModified

    def _getProductSyncToken(self, lastModified):
        if lastModified is None:
            sync_token = 1
        else:
            sync_token = int(lastModified[self._productIndex["sync_token"]]) + 1
        return sync_token

    def _updateHash(self, row, sqliteCursor):
        params = {"source_id": None}
        auditRow = {
            self._productIndex["sync_token"]: None,
        }
        rowHash = utils.getHash(json.dumps(self._transformProduct(
            params, row, auditRow)))

        # Compare hash to existing row
        sql = "select * from product where source_product_code = ?" + \
              " and source_variant_code = ?"
        sqliteCursor.execute(
            sql, ((
                row["source_product_code"], row["source_variant_code"]
            ))
        )
        oldProduct = sqliteCursor.fetchone()  # Assuming there is only one match

        if oldProduct is None:
            lastModified = self._getLastModifiedProduct(sqliteCursor)
            sql = "insert into product values (?, ?, ?, ?, ?)"
            sqliteCursor.execute(
                sql, ((
                    row["source_product_code"],
                    row["source_variant_code"],
                    rowHash,
                    self._getProductSyncToken(lastModified),
                    utils.getTimestamp()
                ))
            )

        elif oldProduct[self._productIndex["hash"]] == rowHash:
            pass

        else:
            # Update if hash has changed
            lastModified = self._getLastModifiedProduct(sqliteCursor)
            sql = "update product set" \
                  " hash = ?," + \
                  " modified = ?," \
                  " sync_token = ?" \
                  " where" + \
                  " source_product_code = ?" + \
                  " and source_variant_code = ?"
            sqliteCursor.execute(
                sql,
                ((
                    rowHash,
                    utils.getTimestamp(),
                    self._getProductSyncToken(lastModified),
                    row["source_product_code"],
                    row["source_variant_code"],
                ))
            )

    @staticmethod
    def _getLastModifiedCustomer(sqliteCursor):
        sql = '''
          select * from customer
          order by sync_token desc limit 1
        '''
        sqliteCursor.execute(sql)
        lastModified = sqliteCursor.fetchone()
        return lastModified

    def _getCustomerSyncToken(self, lastModified):
        if lastModified is None:
            sync_token = 1
        else:
            sync_token = int(lastModified[self._customerIndex["sync_token"]]) + 1
        return sync_token

    def _updateCustomerHash(self, row, sqliteCursor):
        params = {"source_id": None}
        auditRow = {
            self._customerIndex["sync_token"]: None
        }
        rowHash = utils.getHash(json.dumps(self._transformCustomer(
            params, row, auditRow)))

        # Compare hash to existing row
        sql = "select * from customer where source_customer_code = ?"
        sqliteCursor.execute(
            sql, ((
                row["source_customer_code"],
            ))
        )
        oldCustomer = sqliteCursor.fetchone()  # Assuming there is only one match

        if oldCustomer is None:
            lastModified = self._getLastModifiedCustomer(sqliteCursor)
            sql = "insert into customer values (?, ?, ?, ?)"
            sqliteCursor.execute(
                sql, ((
                    row["source_customer_code"],
                    rowHash,
                    self._getCustomerSyncToken(lastModified),
                    utils.getTimestamp(),
                ))
            )

        elif oldCustomer[self._customerIndex["hash"]] != rowHash:
            lastModified = self._getLastModifiedCustomer(sqliteCursor)
            # Update if hash has changed
            sql = "update customer set" \
                  " hash = ?," + \
                  " modified = ?," \
                  " sync_token = ?" \
                  " where" + \
                  " source_customer_code = ?"
            sqliteCursor.execute(
                sql,
                ((
                    rowHash,
                    utils.getTimestamp(),
                    self._getCustomerSyncToken(lastModified),
                    row["source_customer_code"],
                ))
            )

    @staticmethod
    def _imageHashChanged(row, encoded, sqliteCursor):
        """
        Update image hash in local database.
        Return true if a row was inserted or updated, otherwise return false.
        """
        # Include source_variant_code in hash,
        # in case the same picture is used for multiple products.
        imageHash = utils.getHash("{} {}".format(encoded, row["source_variant_code"]))

        # Compare hash to existing row
        hashIndex = 0
        sql = "select hash from image where source_variant_code = ?"
        sqliteCursor.execute(
            sql, (row["source_variant_code"],)
        )
        oldImage = sqliteCursor.fetchone()  # Assuming there is only one match

        if oldImage is None:
            sql = "insert into image values (?, ?, ?)"
            sqliteCursor.execute(
                sql, ((
                    row["source_variant_code"],
                    imageHash,
                    utils.getTimestamp()
                ))
            )
            return True

        elif oldImage[hashIndex] != imageHash:
            # Update if hash has changed
            sql = "update image set" \
                  " hash = ?," + \
                  " modified = ?" \
                  " where" + \
                  " source_variant_code = ?"
            sqliteCursor.execute(
                sql,
                ((
                    imageHash,
                    utils.getTimestamp(),
                    row["source_variant_code"]
                ))
            )
            return True

        else:
            return False

    def _transformProduct(self, params, result, sync_token):
        """
        Given the result of getProduct we return a dictionary
        with required structure ready to be converted to JSON.
        """
        source_product = OrderedDict()
        source_product["source"] = OrderedDict()
        source_product["product"] = OrderedDict()
        source_product["product"]["variants"] = OrderedDict()

        source_product = self._beforeProductTransform(params, result, source_product)

        source_product["source"]["source_id"] = params["source_id"]
        source_product["source"]["product_active"] = result["product_active"]
        source_product["source"]["source_product_code"] = str(result["source_product_code"])
        source_product["source"]["sync_token"] = sync_token

        source_product["product"]["options"] = []
        source_product["product"]["body_html"] = result["body_html"]
        source_product["product"]["collection"] = result["collection"]
        source_product["product"]["product_type"] = result["product_type"]
        source_product["product"]["tags"] = str(result["tags"]).lower()
        source_product["product"]["title"] = result["title"]
        source_product["product"]["vendor"] = result["vendor"]
        source_product["product"]["meta"] = self._parseMetaFields(result)

        source_product["product"]["variants"]["source_variant_code"] = str(result["source_variant_code"])
        # ensure backward compatible
        if "variants.sku" in result:
            source_product["product"]["variants"]["sku"] = str(result["variants.sku"])
        else:
            source_product["product"]["variants"]["sku"] = str(result["source_variant_code"])
        source_product["product"]["variants"]["barcode"] = result["variants.barcode"]
        source_product["product"]["variants"]["inventory_management"] = result["variants.inventory_management"]

        # For the weight, check for "variants.weight" key first, then check for "variants.grams"
        # "variants.weight" seems to be standard
        weight = 0
        if "variants.weight" in result:
            weight = result["variants.weight"]
        elif "variants.grams" in result:
            weight = result["variants.grams"]
        source_product["product"]["variants"]["grams"] = weight

        source_product = self._parsePriceTiers(result, source_product)
        source_product = self._parseQtyAvailability(result, source_product)
        source_product = self._parseOptions(result, source_product)

        return self._afterProductTransform(params, result, source_product)

    def _beforeProductTransform(self, params, result, source_product):
        """
        Hook method that gets called before a product has been transformed.
        For default behavior just return the passed "source_product"
        """
        return source_product

    def _afterProductTransform(self, params, result, source_product):
        """
        Hook method that gets called after a product has been transformed.
        For default behavior just return the passed "source_product"
        """
        return source_product

    def _afterCustomerTransform(self, params, result, source_customer):
        """
        Hook method that gets called after a product has been transformed.
        For default behavior just return the passed "source_product"
        """
        return source_customer

    @staticmethod
    def _parseOptions(row, source_product):
        """
        Parses the optionX fields in the query result.
        This uses either the old format of "variants.optionX" or the newer
        version of key/value pairs in the form "variants.optionX_name" and "variants.optionX_value"
        """
        source_product_result = source_product

        # Test and add the 3 key/value pairs for options
        optionIndex = 1
        options = []
        for i in range(1, 4):
            nameKey = 'variants.option{}_name'.format(i)
            valueKey = 'variants.option{}_value'.format(i)
            # Add this option to product if both key/value exists and is not empty
            if nameKey in row and row[nameKey] is not None and str(row[nameKey]).strip() != '' and \
                    valueKey in row and row[valueKey] is not None and str(row[valueKey]).strip() != '':
                option = OrderedDict()
                option["name"] = row[nameKey]
                option["position"] = '{}'.format(optionIndex)
                option["value"] = "undefined"
                options.append(option)

                optionKey = 'option{}'.format(optionIndex)
                source_product_result["product"]["variants"][optionKey] = row[valueKey]
                optionIndex += 1
            else:
                # Fall back to older "optionX" version
                key = 'variants.option{}'.format(i)
                if key in row and row[key] is not None and str(row[key]).strip() != '':
                    source_product['product']['variants']['option{}'.format(i)] = row[key]
        source_product_result["product"]["options"] = options
        return source_product_result

    @staticmethod
    def _parsePriceTiers(row, source_product):
        """
        Parses the "csv_price_tiers" field.
        If we have a CSV list of prices use that, otherwise use the default.
        """
        source_product_result = source_product

        source_product_result["product"]["variants"]["price_tiers"] = []

        # Add retail price tier
        if "variants.retail_price" in row:
            price_tier = OrderedDict()
            price_tier["tier"] = "retail"
            price_tier["price"] = row["variants.retail_price"]
            source_product_result["product"]["variants"]["price_tiers"].append(price_tier)

        # Add wholesale price tier if it exists
        if "variants.wholesale_price" in row:
            price_tier = OrderedDict()
            price_tier["tier"] = "wholesale"
            price_tier["price"] = row["variants.wholesale_price"]
            source_product_result["product"]["variants"]["price_tiers"].append(price_tier)

        if "csv_price_tiers" in row and row["csv_price_tiers"] is not None:
            default_price = 0
            # The stuff function used in the sql queries
            # will encode special characters as html entities,
            # for example, "&" becomes "&amp;".
            # Call html.unescape to convert back to special characters
            prices = saxutils.unescape(row["csv_price_tiers"])
            prices = prices.split(",")
            for priceStr in prices:
                priceItem = priceStr.split("|")
                if len(priceItem) == 2 and priceItem[0].strip() != "":
                    tier = priceItem[0]
                    # Throw away everything after the second decimal
                    price = float("{0:.2f}".format(float(priceItem[1])))
                    if tier == row["default_price_tier"]:
                        default_price = price
                    price_tier = OrderedDict()
                    price_tier["tier"] = tier
                    price_tier["price"] = price
                    source_product_result["product"]["variants"]["price_tiers"].append(price_tier)
            source_product_result["product"]["variants"]["price"] = default_price
        else:
            if "variants.retail_price" in row:
                source_product_result["product"]["variants"]["price"] = row["variants.retail_price"]
            else:
                source_product_result["product"]["variants"]["price"] = 0

        return source_product_result

    @staticmethod
    def _parseQtyAvailability(row, source_product):
        """
        Parses the "csv_qty_availability" field.
        If we have a CSV list of warehouse quantities use that, otherwise use the default.
        """
        source_product_result = source_product

        if "csv_qty_availability" in row and row["csv_qty_availability"] is not None:
            source_product_result["product"]["variants"]["qty_availability"] = []
            default_qty = 0
            quantities = saxutils.unescape(row["csv_qty_availability"])
            quantities = quantities.split(",")
            for qtyStr in quantities:
                qtyItem = qtyStr.split("|")
                if len(qtyItem) == 2 and qtyItem[0].strip() != "":
                    description = qtyItem[0]
                    # Qty must be an integer
                    qty = math.trunc(float(qtyItem[1]))
                    if description == row["default_qty_availability"]:
                        default_qty = qty
                    availability = OrderedDict()
                    availability["qty"] = qty
                    availability["description"] = description
                    source_product_result["product"]["variants"]["qty_availability"].append(availability)
            source_product_result["product"]["variants"]["qty"] = default_qty
        else:
            if "variants.qty" in row and row["variants.qty"] is not None:
                source_product_result["product"]["variants"]["qty"] = math.trunc(float(row["variants.qty"]))
            else:
                source_product_result["product"]["variants"]["qty"] = 0

        return source_product_result

    @staticmethod
    def _parseSegments(row):
        segments = []

        # Anything that starts with segment| gets converted into a segment
        # Expecting the following fields for Segments:
        # owner|type|key|operator|value
        # The sql on query must follow segment structure
        # 'value'  as "segment|source|products|meta_rep_id|equal"
        for key in row:
            if key.startswith("segment|"):
                parts = key.split("|")
                segment = OrderedDict()
                segment["owner"] = parts[1].strip()
                segment["type"] = parts[2].strip()
                segment["key"] = parts[3].strip()
                segment["operator"] = parts[4].strip()
                segment["value"] = row[key]
                segments.append(segment)

        return segments

    @staticmethod
    def _parseMetaFields(row):
        metaFields = []

        # Anything that starts with csv gets converted into meta
        # e.g. csv_special_prices will convert to:
        # meta_special_prices_{key} = {value}
        # ignore special keywords
        for key in row:
            if key.startswith("csv_") and \
                    not key.startswith("csv_price_tiers") and \
                    not key.startswith("csv_contract_") and \
                    not key.startswith("csv_qty_availability"):
                # Value from database might be null
                if row[key] is not None:
                    title = key.replace("csv_", "")
                    values = saxutils.unescape(row[key]).split(",")
                    for value in values:
                        valueItem = value.split("|")
                        if len(valueItem) == 2 and valueItem[0].strip() != "":
                            left = valueItem[0].strip()
                            right = valueItem[1]
                            meta = OrderedDict()
                            meta["key"] = str(title + "_{}".format(left))
                            meta["value"] = str(right)
                            metaFields.append(meta)

        for key in row:
            if key.startswith("meta_"):
                # Value from database might be null
                if row[key] is not None:
                    meta = OrderedDict()
                    meta["key"] = str(key[5:])  # Remove "meta_" from key
                    meta["value"] = saxutils.unescape(str(row[key]))
                    metaFields.append(meta)

            # Contract pricing
            if key.startswith("csv_contract_"):
                # Value from database might be null
                if row[key] is not None:
                    # Example row value:
                    # order_0|entity_customer|key_item_group_code|value_{}|type_discount~10,
                    # order_0|entity_product|key_item_group_code|value_{}|type_discount~20
                    # Output:
                    # [
                    #   {
                    #       key: order_0|entity_customer|key_item_group_code|value_abc|type_discount
                    #       value: 10
                    #   },
                    #   {
                    #       key: order_0|entity_customer|key_item_group_code|value_xyz|type_discount
                    #       value: 20
                    #   }
                    # ]
                    contracts = saxutils.unescape(row[key]).split(",")
                    for contract in contracts:
                        contractItem = contract.split("~")
                        if len(contractItem) == 2 and contractItem[0].strip() != "":
                            meta_key = contractItem[0].strip()
                            meta_value = contractItem[1]  # discount / fixed price
                            meta = collections.OrderedDict()
                            meta["key"] = str(meta_key)
                            meta["value"] = str(meta_value)
                            metaFields.append(meta)

        return metaFields

    @staticmethod
    def _parseCustomerName(row):
        first_name = row["first_name"]
        last_name = row["last_name"]
        company = row["address.company"]
        if first_name is None and last_name is None:
            # Note that company could also be None
            first_name = company
        return company, first_name, last_name

    def _transformCustomer(self, params, row, sync_token):
        """
        Given the result of getCustomer we return a dictionary
        with required structure ready to be converted to JSON.
        """

        source_customer = OrderedDict()
        source_customer["source"] = OrderedDict()
        source_customer["source"]["source_id"] = params["source_id"]
        source_customer["source"]["customer_active"] = row["customer_active"]
        source_customer["source"]["source_customer_code"] = \
            row["source_customer_code"]
        source_customer["source"]["sync_token"] = sync_token

        customer = OrderedDict()
        company, first_name, last_name = self._parseCustomerName(row)
        customer["first_name"] = first_name
        customer["last_name"] = last_name
        customer["email"] = row["email"]
        if row["accepts_marketing"] == 1:
            customer["accepts_marketing"] = True
        else:
            customer["accepts_marketing"] = False
        customer["addresses"] = self._getCustomerAddresses(row, company, first_name, last_name)

        customer["price_tier"] = str(row["price_tier"])
        customer["qty_availability"] = str(row["qty_availability"])

        customer["meta"] = self._parseMetaFields(row)
        customer["segments"] = self._parseSegments(row)

        source_customer["customer"] = customer

        return self._afterCustomerTransform(params, row, source_customer)

    @staticmethod
    def _getCustomerAddresses(row, company, first_name, last_name):
        customer_addresses = []

        # Parse address xml
        if "xml_addresses" in row and row["xml_addresses"] is not None:
            root = etree.fromstring(row["xml_addresses"])
            for child in root:
                address = collections.OrderedDict()
                address["type"] = child.find("type").text
                address["address_code"] = child.find("address_code").text
                address["address1"] = child.find("address1").text
                address["address2"] = child.find("address2").text
                address["city"] = child.find("city").text
                address["country"] = child.find("country").text
                address["country_code"] = child.find("country_code").text
                address["province"] = child.find("province").text
                address["province_code"] = child.find("province_code").text
                address["zip"] = child.find("zip").text
                address["company"] = child.find("company").text
                address["first_name"] = child.find("first_name").text
                address["last_name"] = child.find("last_name").text
                address["phone"] = child.find("phone").text
                customer_addresses.append(address)
        else:
            address = collections.OrderedDict()
            address["address1"] = row["address.address1"]
            address["address2"] = row["address.address2"]
            address["city"] = row["address.city"]
            address["country"] = row["address.country"]
            address["country_code"] = row["address.country_code"]
            address["province"] = row["address.province"]
            address["province_code"] = row["address.province_code"]
            address["zip"] = row["address.zip"]
            address["company"] = company
            address["first_name"] = first_name
            address["last_name"] = last_name
            address["phone"] = row["address.phone"]
            customer_addresses.append(address)

        return customer_addresses

    @staticmethod
    def _getattrOrEmptyString(row, key):
        attr = getattr(row, key)
        if attr is None:
            return ""
        return attr

    def _transformOrder(self, params, rows):
        i = 0
        source_order = OrderedDict()

        for row in rows:
            # Read order details from the first line item
            if i == 0:
                source_order["sources"] = [OrderedDict()]
                source_order["sources"][0]["source_id"] = params["source_id"]
                source_order["sources"][0]["source_order_code"] = row.source_order_code.strip()
                source_order["sources"][0]["source_customer_code"] = row.source_client_code.strip()

                source_order["source_order"] = OrderedDict()
                source_order["source_order"]["id"] = row.source_order_code.strip()
                source_order["source_order"]["notes"] = row.notes1.strip() + "\\n" + \
                                                        row.notes2.strip() + "\\n" + \
                                                        row.notes3.strip()
                source_order["source_order"]["customer"] = OrderedDict()
                source_order["source_order"]["customer"]["first_name"] = self._getattrOrEmptyString(row,
                                                                                                    "customer.first_name").strip()
                source_order["source_order"]["customer"]["last_name"] = self._getattrOrEmptyString(row,
                                                                                                   "customer.last_name").strip()
                source_order["source_order"]["customer"]["email"] = self._getattrOrEmptyString(row,
                                                                                               "customer.email").strip()

                billing_address = OrderedDict()
                billing_address["address1"] = self._getattrOrEmptyString(row, "billing_address.address1").strip()
                billing_address["address2"] = self._getattrOrEmptyString(row, "billing_address.address2").strip()
                billing_address["city"] = self._getattrOrEmptyString(row, "billing_address.city").strip()
                billing_address["company"] = self._getattrOrEmptyString(row, "billing_address.company").strip()
                billing_address["country"] = self._getattrOrEmptyString(row, "billing_address.country").strip()
                billing_address["first_name"] = self._getattrOrEmptyString(row, "billing_address.first_name").strip()
                billing_address["last_name"] = self._getattrOrEmptyString(row, "billing_address.last_name").strip()
                billing_address["phone"] = self._getattrOrEmptyString(row, "billing_address.phone").strip()
                billing_address["province"] = self._getattrOrEmptyString(row, "billing_address.province").strip()
                billing_address["zip"] = self._getattrOrEmptyString(row, "billing_address.zip").strip()
                billing_address["country_code"] = self._getattrOrEmptyString(row,
                                                                             "billing_address.country_code").strip()
                source_order["source_order"]["customer"]["addresses"] = [billing_address]

                source_order["source_order"]["billing_address"] = billing_address

                shipping_address = OrderedDict()
                shipping_address["address1"] = self._getattrOrEmptyString(row, "shipping_address.address1").strip()
                shipping_address["address2"] = self._getattrOrEmptyString(row, "shipping_address.address2").strip()
                shipping_address["city"] = self._getattrOrEmptyString(row, "shipping_address.city").strip()
                shipping_address["company"] = self._getattrOrEmptyString(row, "shipping_address.company").strip()
                shipping_address["country"] = self._getattrOrEmptyString(row, "shipping_address.country").strip()
                shipping_address["first_name"] = self._getattrOrEmptyString(row, "shipping_address.first_name").strip()
                shipping_address["last_name"] = self._getattrOrEmptyString(row, "shipping_address.last_name").strip()
                shipping_address["phone"] = self._getattrOrEmptyString(row, "shipping_address.phone").strip()
                shipping_address["province"] = self._getattrOrEmptyString(row, "shipping_address.province").strip()
                shipping_address["zip"] = self._getattrOrEmptyString(row, "shipping_address.zip").strip()
                shipping_address["country_code"] = self._getattrOrEmptyString(row,
                                                                              "shipping_address.country_code").strip()
                source_order["source_order"]["shipping_address"] = shipping_address

                source_order["source_order"]["line_items"] = []
                source_order["source_order"]["shipping_lines"] = []

            # Each additional row contains a line item
            line_item = OrderedDict()
            line_item["source_id"] = params["source_id"]
            line_item["sku"] = self._getattrOrEmptyString(row, "line_item.sku").strip()
            line_item["title"] = self._getattrOrEmptyString(row, "line_item.title").strip()
            line_item["price"] = getattr(row, "line_item.price")
            line_item["qty"] = getattr(row, "line_item.qty")
            line_item["code"] = "item"
            tax_line = OrderedDict()
            tax_line["price"] = getattr(row, "tax_line.price")
            tax_line["rate"] = 14
            tax_line["title"] = "VAT"
            tax_line["code"] = "taxed"
            line_item["tax_lines"] = [tax_line]

            if line_item["sku"] == "SHIP001":
                source_order["source_order"]["shipping_lines"].append(line_item)
            else:
                source_order["source_order"]["line_items"].append(line_item)

            i += 1

        return source_order

    def reload(self):
        # This is not needed anymore as we get run from the cli - not as a long-running python process
        # So it doesn't do anything

        response = {
            "status": True,
            "data": {
                "reload": True
            }
        }
        return json.dumps(response, indent=self._indent)

    # Do not allow writing to sqlite database using this function,
    # i.e. do not pass commit=True to utils.openSqliteConn.
    def runLocal(self):
        self._setConfig()
        params = self._getParams()
        with utils.openSqliteConn(self._getDbPath()) as cursor:
            response = utils.runSQLite(params, cursor)
            return json.dumps(response, indent=self._indent)

    def runCustom(self):
        self._setConfig()
        params = self._getParams()
        fileName = None
        if "fileName" in params:
            fileName = params["fileName"]
        if "limit" in params:
            limit = int(params["limit"])
        else:
            limit = 500

        # use sql in params otherwise use filename
        if "sql" in params:
            sql = params["sql"]
        else:
            sql = self._customOdbc(fileName)

        if sql == "":
            return json.dumps({
                "status": False,
                "description": "Sql not given. Use query param 'fileName=xyz.sql' or 'sql=select * from...'",
                "line": utils.lineNo()
            }, indent=self._indent)

        params = {
            "sql": sql,
            "limit": limit
        }
        with odbc.openConn(self._config["dsn"]) as cursor:
            response = utils.runODBC(params, cursor)
            return json.dumps(response)

    def log(self):
        params = self._getParams()
        response = utils.log(params, self._packageDir)
        return json.dumps(response, indent=self._indent)

    def auditReset(self):
        t1 = time.time()
        self._setConfig()
        dbPath = self._getDbPath()
        if os.path.isfile(dbPath):
            os.remove(dbPath)
        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {}
        }
        return json.dumps(response, indent=self._indent)

    def auditResetCustomers(self):
        """
        Remove all customers from the local audit database
        """
        t1 = time.time()
        self._setConfig()
        with utils.openSqliteConn(self._getDbPath(), commit=True) as sqliteCursor:
            sql = "delete from customer"
            sqliteCursor.execute(sql)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {}
        }
        return json.dumps(response, indent=self._indent)

    def auditResetProducts(self):
        """
        Remove all products from the local audit database
        """
        t1 = time.time()
        self._setConfig()
        with utils.openSqliteConn(self._getDbPath(), commit=True) as sqliteCursor:
            sql = "delete from product"
            sqliteCursor.execute(sql)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {}
        }
        return json.dumps(response, indent=self._indent)

    # We assume getImages will always run after auditProducts
    def getImages(self):
        self._setConfig()
        raise Exception("Not implemented")

    def getProductBySKU(self):
        t1 = time.time()
        self._setConfig()
        params = self._getParams()

        response = utils.checkRequiredParams(["sku"], params)
        if response is not None:
            return json.dumps(response, indent=self._indent)

        rows = []
        with odbc.openConn(self._config["dsn"]) as cursor:
            sql = self._getProductBySKUOdbc()
            sql = utils.bindSqlParams(sql, {
                "sku": str(params["sku"])
            })

            response = odbc.execute(cursor, sql)
            if response is not None:
                return json.dumps(response, indent=self._indent)

            rows = utils.getRowsODBC(cursor, 1)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {"source_products": rows}
        }
        return json.dumps(response, indent=self._indent)

    def countProducts(self):
        t1 = time.time()
        self._setConfig()

        params = self._getParams()

        with odbc.openConn(self._config["dsn"]) as cursor:
            sql = self._countProductsOdbc()
            sql = utils.bindSqlParams(sql, params)

            response = odbc.execute(cursor, sql)
            if response is not None:
                return json.dumps(response, indent=self._indent)

            rows = utils.getRowsODBC(cursor, 1)
            count = rows[0]["count"]

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {
                "count": count
            }
        }
        return json.dumps(response, indent=self._indent)

    def countCustomers(self):
        t1 = time.time()
        self._setConfig()

        params = self._getParams()

        with odbc.openConn(self._config["dsn"]) as cursor:
            sql = self._countCustomersOdbc()
            sql = utils.bindSqlParams(sql, params)

            response = odbc.execute(cursor, sql)
            if response is not None:
                return json.dumps(response, indent=self._indent)

            rows = utils.getRowsODBC(cursor, 1)

        # "count" must be present and be an int,
        # it is used by push.exe
        data = OrderedDict()
        if "count" in rows[0]:
            data["count"] = int(rows[0]["count"])
        if "active" in rows[0]:
            data["active"] = int(rows[0]["active"])
        if "inactive" in rows[0]:
            data["inactive"] = int(rows[0]["inactive"])
        if "total" in rows[0]:
            data["total"] = int(rows[0]["total"])

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": data
        }
        return json.dumps(response, indent=self._indent)

    def getProduct(self):
        """
        This functions uses the grtProduct.sql to return the product.
        It also includes the raw data returned by the sql query as well as the transformed S2S product
        """
        t1 = time.time()
        self._setConfig()

        params = self._getParams()

        response = utils.checkRequiredParams(
            ["source_variant_code", "source_id"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        rows = []
        with odbc.openConn(self._config["dsn"]) as cursor:
            sql = self._getProductOdbc()

            # it could be thar source_variant_code is a number not a string
            # if an exception occurs, try parse it as int
            try:
                sql = utils.bindSqlParams(sql, {
                    "source_variant_code": params["source_variant_code"],
                })
            except Exception:
                sql = utils.bindSqlParams(sql, {
                    "source_variant_code": int(params["source_variant_code"]),
                })

            response = odbc.execute(cursor, sql)
            if response is not None:
                return json.dumps(response, indent=self._indent)

            result = utils.getRowsODBC(cursor, 1)
            if len(result) > 0:
                transformed_product = self._transformProduct(
                    params,
                    result[0],
                    "0"
                )
                transformed_product["raw_query"] = result
                rows.append(transformed_product)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {"source_products": rows}
        }
        return json.dumps(response, indent=self._indent)

    def auditProductsBatch(self):
        """
        This function kicks off a process that reads all the products
        in the SQL server database and updates the hashes in sqlite.
        If the sqlite hash differs from the time stamp the product row is updated.
        getProducts can then check the sync token against sqlite.
        """
        t1 = time.time()
        products = 0
        self._setConfig()

        self._checkInstall()
        params = self._getParams()

        response = utils.checkRequiredParams(
            ["audit_lower_limit", "audit_upper_limit"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        # Read products from SQL server
        with odbc.openConn(self._config["dsn"]) as cursor:
            sql = self._getProductsOdbc()
            sql = utils.bindSqlParams(sql, {
                "audit_lower_limit": int(params["audit_lower_limit"]),
                "audit_upper_limit": int(params["audit_upper_limit"]),
            })

            response = odbc.execute(cursor, sql)
            if response is not None:
                return json.dumps(response, indent=self._indent)

            rows = utils.getRowsODBC(cursor, 1)

            # Get local connection
            if len(rows) > 0:
                with utils.openSqliteConn(self._getDbPath(), commit=True) as sqliteCursor:
                    while len(rows) > 0:
                        products += 1
                        # Update hash for this product
                        self._updateHash(rows[0], sqliteCursor)
                        rows = utils.getRowsODBC(cursor, 1)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {
                "products": products
            }
        }
        return json.dumps(response, indent=self._indent)

    def auditCustomersBatch(self):
        """
        This function kicks off a process that reads all the customers
        in the SQL server database and updates the hashes in sqlite.
        If the sqlite hash differs from the time stamp the customer row is updated.
        getCustomers can then check the sync token against sqlite.
        """
        t1 = time.time()
        customers = 0
        self._setConfig()

        self._checkInstall()
        params = self._getParams()

        response = utils.checkRequiredParams(
            ["audit_lower_limit", "audit_upper_limit"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        # Read products from SQL server
        with odbc.openConn(self._config["dsn"]) as cursor:
            sql = self._getCustomersOdbc()
            sql = utils.bindSqlParams(sql, {
                "audit_lower_limit": int(params["audit_lower_limit"]),
                "audit_upper_limit": int(params["audit_upper_limit"]),
            })

            response = odbc.execute(cursor, sql)
            if response is not None:
                return json.dumps(response, indent=self._indent)

            rows = utils.getRowsODBC(cursor, 1)

            # Get local connection
            if len(rows) > 0:
                with utils.openSqliteConn(self._getDbPath(), commit=True) as sqliteCursor:
                    while len(rows) > 0:
                        customers += 1
                        # Update hash for this customer
                        self._updateCustomerHash(rows[0], sqliteCursor)
                        rows = utils.getRowsODBC(cursor, 1)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {
                "customers": customers
            }
        }
        return json.dumps(response, indent=self._indent)

    def _getAuditProducts(self):
        t1 = time.time()
        self._setConfig()

        params = self._getParams()

        response = utils.checkRequiredParams(
            ["sync_token", "limit"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        rows = []
        with utils.openSqliteConn(self._getDbPath(), commit=True) as sqliteCursor:
            sql = self._getProductsLocal()
            sqliteCursor.execute(sql, (
                int(params["sync_token"]), int(params["limit"])
            ))

            auditRow = sqliteCursor.fetchone()
            while auditRow is not None:
                auditEntry = OrderedDict()
                auditEntry['source_product_code'] = auditRow[self._productIndex['source_product_code']]
                auditEntry['source_variant_code'] = auditRow[self._productIndex['source_variant_code']]
                auditEntry['hash'] = auditRow[self._productIndex['hash']]
                auditEntry['sync_token'] = auditRow[self._productIndex['sync_token']]
                auditEntry['modified'] = auditRow[self._productIndex['modified']]
                rows.append(auditEntry)

                auditRow = sqliteCursor.fetchone()

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {"source_products": rows}
        }
        return json.dumps(response, indent=self._indent)

    def getProductsBatch(self):
        t1 = time.time()
        self._setConfig()

        params = self._getParams()

        response = utils.checkRequiredParams(
            ["sync_token", "limit", "source_id"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        # Test source in the s2s console does not pass in current_iteration
        audit_products = False
        if "current_iteration" not in params:
            params["current_iteration"] = "1"

        # Only audit products on the first iteration
        if params["current_iteration"] == "1":
            response = self.auditProductsBatch()
            response = json.loads(response)
            if not response["status"]:
                return json.dumps(response, indent=self._indent)
            audit_products = True

        with odbc.openConn(self._config["dsn"]) as cursor:
            with utils.openSqliteConn(self._getDbPath(), commit=True) as sqliteCursor:
                sql = self._getProductsLocal()
                sqliteCursor.execute(sql, (
                    int(params["sync_token"]), int(params["limit"])
                ))

                rows = []
                auditRow = sqliteCursor.fetchone()
                while auditRow is not None:
                    sql = self._getProductOdbc()
                    sql = utils.bindSqlParams(sql, {
                        "source_variant_code":
                            auditRow[self._productIndex["source_variant_code"]]
                    })
                    cursor.execute(sql)
                    odbcRows = utils.getRowsODBC(cursor, 1)
                    if len(odbcRows) > 0:
                        rows.append(
                            self._transformProduct(
                                params,
                                odbcRows[0],
                                auditRow[self._productIndex["sync_token"]]
                            )
                        )
                    auditRow = sqliteCursor.fetchone()

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {"source_products": rows},
            "audit_products": audit_products
        }
        return json.dumps(response, indent=self._indent)

    def getCustomersBatch(self):
        t1 = time.time()
        self._setConfig()

        params = self._getParams()

        response = utils.checkRequiredParams(
            ["sync_token", "limit"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        audit_customers = False
        if "current_iteration" not in params:
            params["current_iteration"] = "1"

        # Only audit customers on the first iteration
        if params["current_iteration"] == "1":
            response = self.auditCustomersBatch()
            response = json.loads(response)
            if not response["status"]:
                return json.dumps(response, indent=self._indent)
            audit_customers = True

        with odbc.openConn(self._config["dsn"]) as cursor:
            with utils.openSqliteConn(self._getDbPath(), commit=True) as sqliteCursor:
                sql = self._getCustomersLocal()
                sqliteCursor.execute(sql, (
                    int(params["sync_token"]), int(params["limit"])
                ))

                rows = []
                auditRow = sqliteCursor.fetchone()
                while auditRow is not None:
                    sql = self._getCustomerOdbc()
                    sql = utils.bindSqlParams(sql, {
                        "source_customer_code":
                            auditRow[self._customerIndex["source_customer_code"]]
                    })
                    cursor.execute(sql)
                    odbcRow = utils.getRowsODBC(cursor, 1)
                    if len(odbcRow) > 0:
                        rows.append(
                            self._transformCustomer(
                                params,
                                odbcRow[0],
                                auditRow[self._customerIndex["sync_token"]]
                            )
                        )
                    auditRow = sqliteCursor.fetchone()

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {"source_customers": rows},
            "audit_customers": audit_customers
        }
        return json.dumps(response, indent=self._indent)

    def getCustomer(self):
        """
        This functions uses getCustomer.sql to return the customer.
        It includes the raw data returned by the sql query
        as well as the transformed S2S customer
        """
        t1 = time.time()
        self._setConfig()

        params = self._getParams()

        response = utils.checkRequiredParams(
            ["source_customer_code", "source_id"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        rows = []
        with odbc.openConn(self._config["dsn"]) as cursor:
            sql = self._getCustomerOdbc()
            sql = utils.bindSqlParams(sql, {
                "source_customer_code": params["source_customer_code"],
            })
            cursor.execute(sql)
            result = utils.getRowsODBC(cursor, 1)
            with utils.openSqliteConn(self._getDbPath(), commit=True) as sqliteCursor:
                sql = self._getCustomerLocal()
                sqliteCursor.execute(sql, (params["source_customer_code"],))
                auditRow = sqliteCursor.fetchone()
                if auditRow is None:
                    auditRow = {
                        self._customerIndex["sync_token"]: "0"
                    }

                if len(result) > 0:
                    transformed_customer = self._transformCustomer(
                        params,
                        result[0],
                        auditRow
                    )
                    transformed_customer["raw_query"] = result
                    rows.append(transformed_customer)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {"source_customers": rows}
        }
        return json.dumps(response, indent=self._indent)

    def getOrder(self):
        t1 = time.time()
        self._setConfig()

        params = self._getParams()
        response = utils.checkRequiredParams(
            ["source_order_code", "source_id"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        with odbc.openConn(self._config["dsn"]) as cursor:
            sql = self._getOrderOdbc()
            sql = utils.bindSqlParams(sql, {
                "source_order_code": params["source_order_code"]
            })

            response = odbc.execute(cursor, sql)
            if response is not None:
                return json.dumps(response, indent=self._indent)

            rows = cursor.fetchall()
            source_order = self._transformOrder(params, rows)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": source_order
        }
        return json.dumps(response, indent=self._indent)

    def createOrder(self):
        t1 = time.time()
        self._setConfig()

        payload = self._payload()
        try:
            payload = json.loads(payload)
        except ValueError:
            response = {
                "status": False,
                "description": "Order request body is not valid json",
                "line": utils.lineNo()
            }
            return json.dumps(response, indent=self._indent)

        # Load apifact payload and params in to a SystemOrder
        order = payload["system_order"]
        order["sources"] = payload["sources"]
        order["meta"] = self.getOrderMeta()
        system_order = SystemOrder(order)

        try:
            order_source = self.createOrderV2(OrdersAdd(system_order))

            t2 = time.time()
            response = {
                "status": True,
                "timer": str(t2 - t1),
                "data": {
                    "source_order_code": order_source.source_order_code,
                    "source_customer_code": order_source.source_customer_code
                }
            }
        except ConfigError as ce:
            response = {
                "status": False,
                "code": 400,
                "description": str(ce)
            }
        except Exception as e:
            response = {
                "status": False,
                "code": 500,
                "description": str(e)
            }

        return json.dumps(response, indent=self._indent)

    def createOrderV2(self, order_add: OrdersAdd) -> OrderSource:
        raise Exception("Not implemented")

    def getHostname(self):
        """
        This function is useful when configuring the ODBC connection string
        """
        self._setConfig()
        args = ["hostname"]

        cmd = subprocess.Popen(args, stdout=subprocess.PIPE)
        response = cmd.communicate()[0]

        os_encoding = locale.getpreferredencoding()
        response = response.decode(os_encoding)

        hostname = response.replace('\n', ' ').replace('\r', '')
        response = {
            "status": True,
            "data": {
                "hostname": hostname.strip()
            }
        }
        return json.dumps(response, indent=self._indent)

    def getIpConfig(self):
        """
        This function is useful when configuring the ODBC connection string
        """
        self._setConfig()
        args = ["ipconfig"]

        cmd = subprocess.Popen(args, stdout=subprocess.PIPE)
        response = cmd.communicate()[0]

        os_encoding = locale.getpreferredencoding()
        response = response.decode(os_encoding)

        ipconfig = response.split("\r\n")
        response = {
            "status": True,
            "data": {
                "ipconfig": ipconfig
            }
        }
        return json.dumps(response, indent=self._indent)

    def getOrderMeta(self) -> list:
        params = self._getParams()
        meta = []
        # Add package config to order meta
        for key, value in self._config.items():
            meta.append({
                "key": key,
                "value": value
            })

        # Add params to order meta (overriding package config)
        for key, value in params.items():
            for item in meta:
                if item["key"] == key:
                    item["value"] = value
                    break
            else:
                meta.append({
                    "key": key,
                    "value": value
                })
        return meta

    def _setMeta(self, key, value, update=False):
        t1 = time.time()
        with utils.openSqliteConn(
                self._getDbPath(), commit=True) as sqliteCursor:
            if update:
                sql = "update meta set value = ? where `key` = ?"
            else:
                sql = "insert into meta (value, `key`) values (?, ?)"
            # Note that param order must work for both queries
            sqliteCursor.execute(sql, (value, key))

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {
                "key": key,
                "value": value
            }
        }
        return response

    def setMeta(self):
        self._setConfig()
        self._checkInstall()

        params = self._getParams()
        response = utils.checkRequiredParams(
            ["key", "value"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        response = self._getMeta(params["key"])
        if response["status"]:
            response = self._setMeta(
                params["key"], params["value"], update=True)
        else:
            response = self._setMeta(params["key"], params["value"])

        return json.dumps(response, indent=self._indent)

    def _getMeta(self, key):
        t1 = time.time()
        with utils.openSqliteConn(self._getDbPath()) as sqliteCursor:
            sql = self._getMetaLocal()
            sqliteCursor.execute(sql, (key,))
            row = sqliteCursor.fetchone()
            if row is None:
                response = {
                    "status": False,
                    "description": "Key {} not found".format(key),
                    "line": utils.lineNo()
                }
                return response

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {
                "key": row[self.metaIndex["key"]],
                "value": row[self.metaIndex["value"]]
            }
        }
        return response

    def getMeta(self):
        self._setConfig()
        self._checkInstall()

        params = self._getParams()
        response = utils.checkRequiredParams(
            ["key"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        response = self._getMeta(params["key"])
        return json.dumps(response, indent=self._indent)

    def deleteMeta(self):
        t1 = time.time()
        self._setConfig()
        self._checkInstall()

        params = self._getParams()
        response = utils.checkRequiredParams(
            ["key"],
            params
        )
        if response is not None:
            return json.dumps(response, indent=self._indent)

        response = self._getMeta(params["key"])
        if response["status"]:
            with utils.openSqliteConn(
                    self._getDbPath(), commit=True) as sqliteCursor:
                sql = "delete from meta where `key` = ?"
                sqliteCursor.execute(sql, (params["key"],))

        else:
            response = {
                "status": False,
                "description": "Key {} not found".format(params["key"]),
                "line": utils.lineNo()
            }
            return json.dumps(response, indent=self._indent)

        t2 = time.time()
        response = {
            "status": True,
            "timer": str(t2 - t1),
            "data": {
                "key": params["key"]
            }
        }
        return json.dumps(response, indent=self._indent)

    def isPush(self):
        response = {
            "status": True,
            "data": {
                "push": True
            }
        }
        return json.dumps(response, indent=self._indent)

    def exampleRequest(self):
        import requests
        r = requests.get('https://example.com/')
        status = False
        if r.status_code == 200:
            status = True
        response = {
            "status": status,
            "data": {
                "status_code": r.status_code
            }
        }
        return json.dumps(response, indent=self._indent)

    def getDefaultEncoding(self):
        os_encoding = locale.getpreferredencoding()

        response = {
            "status": True,
            "data": {
                "encoding": str(os_encoding)
            }
        }
        return json.dumps(response, indent=self._indent)
