<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|AnyCPU'">
    <StartArguments>DK12111476 4526117 "c:\\Pastel12\\_Demo" "c:\\Pastel12" ImportCustomer "AAA004|Soap Joe|Soap Joe|1 Roeland Str||Cape Town, South Africa|3440||||N|01|1|||||||N|0|N|1|Y||00|00|||||||M|1|1||joe%40example.com||||N|||||||||||||"</StartArguments>
    <EnableUnmanagedDebugging>true</EnableUnmanagedDebugging>
    <EnableSQLServerDebugging>true</EnableSQLServerDebugging>
  </PropertyGroup>
  <PropertyGroup>
    <PublishUrlHistory>publish\</PublishUrlHistory>
    <InstallUrlHistory />
    <SupportUrlHistory />
    <UpdateUrlHistory />
    <BootstrapperUrlHistory />
    <ErrorReportUrlHistory />
    <FallbackCulture>en-US</FallbackCulture>
    <VerifyUploadedFiles>false</VerifyUploadedFiles>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|AnyCPU'">
    <StartArguments>DK12111476 4526117 c:\\Pastel12\\_Demo c:\\Pastel12 GetRecord ACCMASD 0 AAA002</StartArguments>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <StartArguments>DK141111066 2530525 "c:\\Pastel14\\_Demo" "c:\\Pastel14" TestCreateOrder</StartArguments>
    <EnableUnmanagedDebugging>true</EnableUnmanagedDebugging>
    <EnableSQLServerDebugging>true</EnableSQLServerDebugging>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <StartArguments>DK12111476 4526117 c:\\Pastel12\\_Demo c:\\Pastel12 GetRecord ACCMASD 0 AAA002</StartArguments>
  </PropertyGroup>
</Project>