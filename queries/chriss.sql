use stock2shop;

# ..............................................................................
# accounts

# Clients by year
SELECT YEAR(c.created), count(*)
FROM client c
         LEFT JOIN clientmeta c3 on c.id = c3.client_id and c3.`key` = 'status'
WHERE c3.value <> 'demo'
GROUP BY YEAR(c.created)
ORDER BY YEAR(c.created)
;

# first invoice by year
SELECT YEAR(c2.value), count(*)
FROM client c
         LEFT JOIN clientmeta c2 on c.id = c2.client_id and c2.`key` = 'first_invoice_date'
         LEFT JOIN clientmeta c3 on c.id = c3.client_id and c3.`key` = 'status'
WHERE c3.value <> 'demo'
GROUP BY YEAR(c2.value)
ORDER BY YEAR(c2.value)
;

# missing first invoice
SELECT c.id, c.name, c.active, c2.`key`, c2.value, c3.`key`, c3.value
FROM client c
         LEFT JOIN clientmeta c2 on c.id = c2.client_id and c2.`key` = 'first_invoice_date'
         LEFT JOIN clientmeta c3 on c.id = c3.client_id and c3.`key` = 'status'
WHERE c3.value <> 'demo'
  AND (c2.value = '' OR c2.value is null);

# last invoice by year
SELECT YEAR(c2.value), count(*)
FROM client c
         LEFT JOIN clientmeta c2 on c.id = c2.client_id and c2.`key` = 'final_invoice_date'
         LEFT JOIN clientmeta c3 on c.id = c3.client_id and c3.`key` = 'status'
WHERE not c3.value in ('demo', 'production', 'data_discussion', 'handover', 'install')
GROUP BY YEAR(c2.value)
ORDER BY YEAR(c2.value)
;

# 2021 cancellations
SELECT c.id,c.name,c1.value,c2.value, DATEDIFF( c2.value, c1.value),c3.value
FROM client c
         LEFT JOIN clientmeta c1 on c.id = c1.client_id and c1.`key` = 'first_invoice_date'
         LEFT JOIN clientmeta c2 on c.id = c2.client_id and c2.`key` = 'final_invoice_date'
         LEFT JOIN clientmeta c3 on c.id = c3.client_id and c3.`key` = 'status'
WHERE not c3.value in ('demo', 'production', 'data_discussion', 'handover', 'install')
  and YEAR(c2.value) = 2021
;

# 2021 signups
SELECT c.id,c.name,c1.value,c2.value, DATEDIFF( c2.value, c1.value),c3.value
FROM client c
         LEFT JOIN clientmeta c1 on c.id = c1.client_id and c1.`key` = 'first_invoice_date'
         LEFT JOIN clientmeta c2 on c.id = c2.client_id and c2.`key` = 'final_invoice_date'
         LEFT JOIN clientmeta c3 on c.id = c3.client_id and c3.`key` = 'status'
WHERE not c3.value in ('demo')
  and YEAR(c1.value) = 2021
;

# all cancellations
SELECT AVG(DATEDIFF( c2.value, c1.value)) as diff
FROM client c
         LEFT JOIN clientmeta c1 on c.id = c1.client_id and c1.`key` = 'first_invoice_date'
         LEFT JOIN clientmeta c2 on c.id = c2.client_id and c2.`key` = 'final_invoice_date'
where not c2.value is null and c.active=0 and YEAR(c.created)>2016
order by diff
;

# missing final invoice
SELECT c.id, c.name, c.active, c2.`key`, c2.value, c3.`key`, c3.value
FROM client c
         LEFT JOIN clientmeta c2 on c.id = c2.client_id and c2.`key` = 'final_invoice_date'
         LEFT JOIN clientmeta c3 on c.id = c3.client_id and c3.`key` = 'status'
WHERE not c3.value in ('demo', 'production', 'data_discussion', 'handover', 'install')
  AND (c2.value = '' OR c2.value is null);


# account invoices
SELECT YEAR(a.created_at), MONTH(a.created_at), sum(a.total_value)
FROM accountinvoice a
WHERE a.status in ('Open', 'Overdue', 'Paid')
  and type = 'invoices'
GROUP BY YEAR(a.created_at), MONTH(a.created_at)
ORDER BY YEAR(a.created_at), MONTH(a.created_at)
;

# s2s sales account invoices
SELECT YEAR(a.created_at), MONTH(a.created_at), sum(ai.price * ai.qty)
FROM accountinvoice a
         INNER JOIN accountinvoiceitem ai on ai.account_invoice_code = a.account_invoice_code
WHERE a.status in ('Open', 'Overdue', 'Paid')
  AND ai.sales_category = 'S2S Sales'
GROUP BY YEAR(a.created_at), MONTH(a.created_at)
ORDER BY YEAR(a.created_at), MONTH(a.created_at)
;


# S2S sales by source by year
SELECT s.type, sum(ai.price * ai.qty)
FROM accountinvoiceitem ai
         INNER JOIN accountinvoice a on ai.account_invoice_code = a.account_invoice_code
         INNER JOIN `source` s on s.id = ai.source_id
WHERE a.status in ('Open', 'Overdue', 'Paid')
  AND ai.sales_category = 'S2S Sales'
  AND a.created_at >= '2021-03-01'
  AND a.created_at < '2022-03-01'
GROUP BY s.type
ORDER BY s.type
;

# list of statuses
select status, count(*)
from accountinvoice
where created_at >= '2022-01-01'
group by status;


# ..............................................................................
# channels
# list all channels (active or not) for non demo clients
select c.id          as "client_id",
       c.name        as "client_name",
       c.active      as "client_actve",
       s.id          as "channel_id",
       s.description as "channel_description",
       s.active      as "channel_active"
from channel s
       inner join client c on s.client_id = c.id
       inner join clientmeta c2 on c.id = c2.client_id and c2.`key` = 'status' and c2.value <> 'demo';


# ..............................................................................
# channel variants

# list all duplicate channel variant codes by channel
select count(*) as count, cv.channel_variant_code, c2.name
from channel_variant cv
         inner join channel c on cv.channel_id = c.id
         inner join client c2 on cv.client_id = c2.id
where 1 = 1
  and c.type = 'woocommerce'
  and c.active = 1
  and cv.active = 1
  and c2.active = 1
  and not cv.channel_variant_code is null
group by cv.channel_variant_code, c2.name
order by count desc
limit 50;



# ..............................................................................
# clients

Select count(*)
from client
where CONVERT_TZ(created, '+00:00', '+02:00') <= '2019-11-28'
  and active = 1;
Select count(*)
from client
where active = 1;

# clients and specific meta
SELECT c.*, c1.value as first_invoice_date, c2.value as final_invoice_date, c3.value as status, c4.value as sign_off_date
FROM client c
         LEFT JOIN clientmeta c1 on c.id = c1.client_id and c1.`key` = 'first_invoice_date'
         LEFT JOIN clientmeta c2 on c.id = c2.client_id and c2.`key` = 'final_invoice_date'
         LEFT JOIN clientmeta c3 on c.id = c3.client_id and c3.`key` = 'status'
         LEFT JOIN clientmeta c4 on c.id = c4.client_id and c4.`key` = 'sign_off_date';

# clients
SELECT c.id,
       c.name,
       c1.value as first_invoice_date,
       c2.value as final_invoice_date,
       c3.value as status,
       c4.value as sign_off_date,
       -- group list of channel types
       group_concat(distinct c5.type) as channel_types,
       group_concat(distinct s.type) as source_types,
       group_concat(distinct f.type) as fulfillment_types
FROM client c
         LEFT JOIN fulfillmentservice f on c.id = f.client_id and f.active=1
         LEFT JOIN source s on c.id = s.client_id and s.active=1
         LEFT JOIN channel c5 on c.id = c5.client_id and c5.active=1
         LEFT JOIN clientmeta c1 on c.id = c1.client_id and c1.`key` = 'first_invoice_date'
         LEFT JOIN clientmeta c2 on c.id = c2.client_id and c2.`key` = 'final_invoice_date'
         LEFT JOIN clientmeta c3 on c.id = c3.client_id and c3.`key` = 'status'
         LEFT JOIN clientmeta c4 on c.id = c4.client_id and c4.`key` = 'sign_off_date'
WHERE c.active = 1
group by c.id;


# ..............................................................................
# clientmeta

# notifications with specific email.
select c.name, cm.*
from clientmeta cm
       inner join client c on cm.client_id = c.id
where `key` = 'notifications'
  and `value` like '%<EMAIL>%'
limit 50;


# ..............................................................................
# customers

# trade customer with a specific email address
SELECT c2.name, c.*
from customer c
       inner join client c2 on c.client_id = c2.id
       inner join channel c3 on c.channel_id = c3.id
where email = '<EMAIL>'
  and c3.type = 'trade'
  and c.active = 1
  and c2.active = 1
  and c3.active = 1
limit 50
;

SELECT count(*)
FROM customer c
       INNER JOIN customer_source cs on c.id = cs.customer_id
WHERE channel_id = 651;

SELECT c.channel_customer_code, c.first_name, c.last_name, c.email, c.active, cs.source_customer_code
FROM customer c
       INNER JOIN customer_source cs on c.id = cs.customer_id
WHERE channel_id = 651
LIMIT 600;
#
# # update customers to remove from b2b store
# UPDATE customer set active=0 where client_id=?;
# UPDATE user set active=0 where client_id=? and id in (
#   select user_id from customer_user where client_id=?
# );
# delete from customer_user where client_id=?;

# ..............................................................................
# flatfile image audit

# inactive clients with orphaned rows in flatfile image audit
select count(*) as "count", client_id
from flatfileimageaudit f
       inner join client c on c.id = f.client_id
where c.active = 0
group by f.client_id
order by count desc
limit 50;

# count by client
select count(*) as "count", client_id
from flatfileimageaudit f
            inner join client c on c.id = f.client_id
where c.active = 1
group by f.client_id
order by count desc
limit 50;


# ..............................................................................
# fulfillments

# list all fulfillment services (active or not) for non demo clients
select c.id          as "client_id",
       c.name        as "client_name",
       c.active      as "client_actve",
       s.id          as "fulfillmentservice_id",
       s.description as "fulfillmentservice_description",
       s.active      as "fulfillmentservice_active"
from fulfillmentservice s
       inner join client c on s.client_id = c.id
       inner join clientmeta c2 on c.id = c2.client_id and c2.`key` = 'status' and c2.value <> 'demo';

# ..............................................................................
# images

select count(*) as "count", processed from image group by processed;
select * from image order by id desc limit 10;

-- processed and error flag does not seem to be used since we do thumbs on lambda
select * from image where error <> '' order by id desc limit 10;
select * from image where processed=1 order by id desc limit 10;



# ..............................................................................
# orders

# Count orders between 2 dates with time zone

select count(*)
from `order` o
where CONVERT_TZ(o.created, '+00:00', '+02:00') between '2018-11-30' and '2018-12-01';

# sum all orders
select sum(orderitem.price * orderitem.qty)
from `orderitem`
where client_id <> 21;

# sum / count pre black friday 2017
select count(*)
from `order` o
where client_id <> 21
  and CONVERT_TZ(o.created, '+00:00', '+02:00') <= '2017-11-23';
select sum(orderitem.price * orderitem.qty)
from `orderitem`
where client_id <> 21
  and CONVERT_TZ(created, '+00:00', '+02:00') <= '2017-11-23';

# black friday 2017 - pre black friday 2018
select count(*)
from `order` o
where client_id <> 21
  and CONVERT_TZ(o.created, '+00:00', '+02:00') between '2017-11-24' and '2018-11-22';
select sum(price * qty)
from `orderitem`
where client_id <> 21
  and CONVERT_TZ(created, '+00:00', '+02:00') between '2017-11-24' and '2018-11-22';
# black friday / cyber monday 2017 only
select count(*)
from `order` o
where client_id <> 21
  and CONVERT_TZ(o.created, '+00:00', '+02:00') between '2017-11-24' and '2017-11-28';
select sum(price * qty)
from `orderitem`
where client_id <> 21
  and CONVERT_TZ(created, '+00:00', '+02:00') between '2017-11-24' and '2017-11-28';


# black friday 2018 - pre black friday 2019
select count(*)
from `order` o
where client_id <> 21
  and CONVERT_TZ(o.created, '+00:00', '+02:00') between '2018-11-23' and '2019-11-28';
select sum(price * qty)
from `orderitem`
where client_id <> 21
  and CONVERT_TZ(created, '+00:00', '+02:00') between '2018-11-23' and '2019-11-28';
# black friday / cyber monday 2018 only
select count(*)
from `order` o
where client_id <> 21
  and CONVERT_TZ(o.created, '+00:00', '+02:00') between '2018-11-23' and '2018-11-27';
select sum(price * qty)
from `orderitem`
where client_id <> 21
  and CONVERT_TZ(created, '+00:00', '+02:00') between '2018-11-23' and '2018-11-27';

# black friday 2019 - pre black friday 2020
select count(*)
from `order` o
where client_id <> 21
  and CONVERT_TZ(o.created, '+00:00', '+02:00') between '2019-11-29' and '2020-11-26';
select sum(price * qty)
from `orderitem`
where client_id <> 21
  and CONVERT_TZ(created, '+00:00', '+02:00') between '2019-11-29' and '2020-11-26';
# black friday / cyber monday 2019 only
select count(*)
from `order` o
where client_id <> 21
  and CONVERT_TZ(o.created, '+00:00', '+02:00') between '2019-11-29' and '2019-11-29';
select sum(price * qty)
from `orderitem`
where client_id <> 21
  and CONVERT_TZ(created, '+00:00', '+02:00') between '2019-11-29' and '2019-11-29';


# black friday / cyber monday 2020 only
select count(*)
from `order` o
where client_id <> 21
  and CONVERT_TZ(o.created, '+00:00', '+02:00') between '2020-11-27' and '2020-12-01';
select sum(price * qty)
from `orderitem`
where client_id <> 21
  and CONVERT_TZ(created, '+00:00', '+02:00') between '2020-11-27' and '2020-12-01';

# black friday per client

select client.id,
       client.name,
       order_count.orders,
       sum(orderitem.price * orderitem.qty) as total
from `order`
       join client on `order`.client_id = client.id
       join orderitem on `order`.id = orderitem.order_id
       join (
  select `order`.client_id, count(*) as orders
  from `order`
  where CONVERT_TZ(`order`.created, '+00:00', '+02:00') between '2020-11-27' and '2020-12-01'
  group by `order`.client_id
) as order_count on order_count.client_id = client.id
where CONVERT_TZ(`order`.created, '+00:00', '+02:00') between '2020-11-27' and '2020-12-01'
group by client.id
order by total desc
limit 50;

# ..............................................................................
# Products / variants

# Count of variants per product
SELECT count(*) as cnt, v.product_id, v.client_id, c.active
FROM variant v
       inner join client c on v.client_id = c.id
       inner join product p on v.product_id = p.id
where v.active = 1
  and p.active = 1
  and c.active = 1
group by v.product_id, v.client_id, c.active
order by cnt desc
LIMIT 50;

# var $productTables = [
#         "option",
#         "productmeta",
#         "product_ymm",
#         "tag",
#         "imagelog"
#     ];
#
# var $variantTables = [
#         "optionitem",
#         "pricevariant",
#         "qtyvariant",
#     ];


# ..............................................................................
# Productmeta

# global count of product meta per client
select count(*) as "count", product_id, c.id from productmeta pm
inner join client c on c.id=pm.client_id
inner join product p on p.id=pm.product_id
where c.active=1 -- and not c.id in (150, 478)
and p.active=1
and pm.active=1
group by pm.product_id, c.id
order by count desc limit 100;
select * from productmeta where product_id=1018618 limit 100;


# count of product meta per client
select count(*) as "count", product_id from productmeta
where client_id=649
group by product_id
order by count desc limit 10
;

# ..............................................................................
# Roles

# routes by role
select r2.description, r.*
from route r
       INNER JOIN role_route rr on r.id = rr.route_id
       INNER JOIN role r2 on rr.role_id = r2.id
order by r2.description, r.pattern;

# routes
select *
from route
order by pattern;

# ..............................................................................
# schedules

# list all source get product schedules
select *
from sourcemeta sm
       inner join source s on sm.source_id = s.id
       inner join client c on s.client_id = c.id
where s.active = 1
  and c.active
  and sm.`key` = 'cron_get_products_schedule'
limit 200;

# ..............................................................................
# shared channel and sources

select c2.id, c2.name, sm.*
from sourcemeta sm
       inner join source s on sm.source_id = s.id
       inner join client c2 on sm.client_id = c2.id
where `key` = 'channel_product_map'
  and c2.active = 1
  and s.active = 1
limit 10;

select c2.id, c2.name, cm.*
from channelmeta cm
       inner join channel c on cm.channel_id = c.id
       inner join client c2 on cm.client_id = c2.id
where `key` = 'shared_source_ids'
  and c2.active = 1
  and c.active = 1
limit 10;

# ..............................................................................
# sources

# list all sources (active or not) for non demo clients
select c.id          as "client_id",
       c.name        as "client_name",
       c.active      as "client_actve",
       s.id          as "source_id",
       s.description as "source_description",
       s.active      as "source_active"
from source s
       inner join client c on s.client_id = c.id
       inner join clientmeta c2 on c.id = c2.client_id and c2.`key` = 'status' and c2.value <> 'demo';

# active shared sources and their channels
select c.id          as "client_id",
       c.name        as "client_name",
       c.active      as "client_actve",
       s.id          as "source_id",
       s.description as "source_description",
       s.active      as "source_active",
       sm.value      as "shared_channel_ids"
from source s
       inner join client c on s.client_id = c.id
       inner join sourcemeta sm on s.id = sm.source_id
where sm.`key` = 'shared_channel_ids'
  and s.active = 1
  and c.active = 1;


# ..............................................................................
# sourcehistory

select * from sourcehistory order by id desc limit 10;

# count of sourcehistories by source
select count(*) as count, source_id
from sourcehistory
group by source_id
order by count desc limit 50
;



select count(*) from sourcehistory sh
inner join source s on s.id=sh.source_id
inner join client c on s.client_id = c.id
where c.active=0 or s.active=0;

# ..............................................................................
# Trade store related queries


# Trade store qty_availability
select c.id, c.qty_availability
from channel c
       inner join client c2 on c.client_id = c2.id
where c.active = 1
#   and c.type = 'trade'
  and c2.active = 1
  and c.qty_availability <> ''
order by c.id;

# active shared channels
select sm.key, sm.value,s.id as source_id,''as channel_id, s.description, c.name, c.id as client_id
from sourcemeta sm
       inner join source s on sm.source_id = s.id
       inner join client c on s.client_id = c.id
where c.active = 1
  and s.active = 1
  and sm.`key` = 'shared_channel_ids' <> ''
union
select cm.key,cm.value,'' as source_id,c2.id as channel_id, c2.description, c.name, c.id as client_id
from channelmeta cm
            inner join channel c2 on cm.channel_id = c2.id
            inner join client c on c2.client_id = c.id
where c.active = 1
  and c2.active = 1
  and cm.`key` = 'shared_source_ids' <> ''


limit 100;

# select source_id, `value` from sourcemeta where  `key`='channel_product_map';
# select * from channel where id in (799,874,876,879);
# select * from source s inner join client c on s.client_id = c.id where s.id in (404);

# ..............................................................................
# users

# console users without a role
select distinct u.email, u.name, s.type
from user u
       left join role_user ru on u.id = ru.user_id
       left join source s on s.client_id = u.client_id and s.active = 1
where ru.user_id is null
  and u.created > '2020-01-01'
  and u.email is not null
  and u.name is not null
group by u.email, u.name, s.type;

